exports.id=5482,exports.ids=[5482],exports.modules={25213:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,92291,23)),Promise.resolve().then(a.bind(a,30998)),Promise.resolve().then(a.bind(a,93104)),Promise.resolve().then(a.bind(a,82718)),Promise.resolve().then(a.bind(a,81807))},69181:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,24671,23)),Promise.resolve().then(a.bind(a,22403)),Promise.resolve().then(a.bind(a,85476)),Promise.resolve().then(a.bind(a,60746)),Promise.resolve().then(a.bind(a,88155))},66706:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,13219,23)),Promise.resolve().then(a.t.bind(a,34863,23)),Promise.resolve().then(a.t.bind(a,25155,23)),Promise.resolve().then(a.t.bind(a,40802,23)),Promise.resolve().then(a.t.bind(a,9350,23)),Promise.resolve().then(a.t.bind(a,48530,23)),Promise.resolve().then(a.t.bind(a,88921,23))},13562:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,66959,23)),Promise.resolve().then(a.t.bind(a,33875,23)),Promise.resolve().then(a.t.bind(a,88903,23)),Promise.resolve().then(a.t.bind(a,57174,23)),Promise.resolve().then(a.t.bind(a,84178,23)),Promise.resolve().then(a.t.bind(a,87190,23)),Promise.resolve().then(a.t.bind(a,61365,23))},85476:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>n});var r=a(45512),o=a(60746),s=a(3371);function n({children:e}){return(0,r.jsx)(s.N,{defaultTheme:"light",attribute:"class",enableSystem:!1,forcedTheme:"light",children:(0,r.jsx)(o.SidebarProvider,{children:e})})}},60746:(e,t,a)=>{"use strict";a.d(t,{SidebarProvider:()=>l,V:()=>i});var r=a(45512),o=a(765),s=a(58009);let n=(0,s.createContext)(null);function i(){let e=(0,s.useContext)(n);if(!e)throw Error("useSidebarContext must be used within a SidebarProvider");return e}function l({children:e,defaultOpen:t=!0}){let a=(0,o.a)(),[i,l]=(0,s.useState)(a),[u,d]=(0,s.useState)(!a&&t),[c,g]=(0,s.useState)(!1);return(0,r.jsx)(n.Provider,{value:{state:u?"expanded":"collapsed",isOpen:u,setIsOpen:d,isMobile:a,toggleSidebar:function(){g(!0),setTimeout(()=>{d(e=>{let t=!e;return console.log("Sidebar manually toggled:",{newState:t,isMobile:a}),t})},50)}},children:e})}},24715:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(45512);a(58009);var o=a(21419),s=a(88752);let n=({size:e="large",fullScreen:t=!1,tip:a})=>{let n=(0,r.jsx)(s.A,{style:{fontSize:24},spin:!0});return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80",children:(0,r.jsx)(o.A,{size:e,indicator:n,tip:a})}):(0,r.jsx)("div",{className:"flex flex-col h-full w-full items-center justify-center py-8",children:(0,r.jsx)(o.A,{size:e,indicator:n,tip:a})})}},765:(e,t,a)=>{"use strict";a.d(t,{a:()=>o});var r=a(58009);function o(){let[e,t]=(0,r.useState)();return!!e}},88155:(e,t,a)=>{"use strict";a.d(t,{default:()=>m});var r=a(45512),o=a(58009),s=a.n(o),n=a(92273),i=a(24107),l=a(9919),u=a(74133),d=a(21419),c=a(88752),g=a(24715),h=a(97245),p=a(42211);let m=({children:e})=>{let[t,a]=s().useState(!1);return s().useEffect(()=>{console.log("ClientProvider mounted"),a(!0),window.__FORCE_REFRESH_USER_DATA=async()=>{console.log("\uD83D\uDD04 Global force refresh user data triggered");try{let{user:e,accessToken:t}=l.M.getState().auth;if(!e||!t){console.log("⚠️ No user or access token for refresh");return}l.M.dispatch(h.i$.util.invalidateTags(["User"])),l.M.dispatch(h.i$.util.resetApiState()),await new Promise(e=>setTimeout(e,500));let a=await l.M.dispatch(h.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in a&&a.data?.success&&a.data.data){let e=a.data.data;console.log("✅ Global refresh: Fresh user data fetched:",{id:e.id,paymentStatus:e.paymentStatus}),l.M.dispatch((0,p.gV)({user:e,accessToken:t})),console.log("✅ Global refresh: Redux state updated")}else console.log("❌ Global refresh: Failed to fetch fresh user data")}catch(e){console.error("❌ Global refresh error:",e)}},console.log("✅ Global refresh function set up")},[]),(0,r.jsx)(n.Kq,{store:l.M,children:(0,r.jsx)(i.Q,{loading:(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-white/80",children:(0,r.jsx)(d.A,{size:"large",indicator:(0,r.jsx)(c.A,{style:{fontSize:24},spin:!0})})}),persistor:l.q,onBeforeLift:()=>{console.log("PersistGate - Before lift")},children:(0,r.jsx)(u.Z,{children:t?e:(0,r.jsx)(g.A,{fullScreen:!0})})})})}},80025:(e,t,a)=>{"use strict";a.d(t,{V:()=>n});let r="https://nexapoapi.up.railway.app/api/v1",o=async(e,t,a)=>{try{let o;console.log("API Request:",{url:`${r}${t}`,method:"POST",payload:e,hasToken:!!a});let s={Accept:"application/json","Content-Type":"application/json",Authorization:`Bearer ${a}`},n={method:"POST",headers:s,body:JSON.stringify(e),credentials:"same-origin",mode:"cors"},i=await fetch(`${r}${t}`,n);try{o=await i.json()}catch(e){return console.error("Failed to parse JSON response:",e),{success:!1,message:`Failed to parse server response: ${i.statusText||"Unknown error"}`}}if(i.ok)return{success:!0,message:o.message||"Action completed successfully",data:o.data||null};return console.error("API Error Response:",{status:i.status,statusText:i.statusText,result:o}),{success:!1,message:o.message||`Request failed with status: ${i.status} ${i.statusText}`}}catch(e){return console.error("API call failed:",e),{success:!1,message:e instanceof TypeError&&e.message.includes("fetch")?"Network error: Unable to connect to the server. Please check your internet connection.":`Error: ${e.message||"Unable to complete action. An unknown error occurred!"}`}}},s=async(e,t)=>{try{let a;console.log("Login API Request:",{url:`${r}${e}`,method:"POST",payload:t});let o={method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t),credentials:"same-origin",mode:"cors"},s=await fetch(`${r}${e}`,o);try{a=await s.json()}catch(e){return console.error("Failed to parse JSON response:",e),{success:!1,message:`Failed to parse server response: ${s.statusText||"Unknown error"}`}}if(s.ok)return{success:!0,message:a.message||"Login successful",data:a.data||null};return console.error("Login API Error Response:",{status:s.status,statusText:s.statusText,result:a}),{success:!1,message:a.message||`Login failed with status: ${s.status} ${s.statusText}`}}catch(e){return console.error("Login API call failed:",e),{success:!1,message:e instanceof TypeError&&e.message.includes("fetch")?"Network error: Unable to connect to the server. Please check your internet connection.":`Error: ${e.message||"Unable to login. An unknown error occurred!"}`}}},n=async e=>{let{urlpath:t,payloaddata:a,token:r}=e;try{if("/login"===t||"/logout"===t){let e=await s(t,a);if(e.success)return{data:e};return{error:e}}if(r){console.log("Making authenticated request:",{urlpath:t,payloaddata:a,hasToken:!!r});try{if(!r.trim())return console.error("Empty token provided for authenticated request"),{error:{success:!1,message:"Authentication token is empty. Please log in again."}};let e=await o(a,t,r);if(console.log("Authenticated request result:",{success:e.success,urlpath:t}),e.success)return{data:e};return console.error("API error:",{urlpath:t,error:e.message}),{error:e}}catch(e){return console.error("API call exception:",{urlpath:t,error:e}),{error:{success:!1,message:`Error in API call: ${e.message||"Unknown error"}`}}}}console.error("Invalid API call - No token provided or invalid request structure",{urlpath:t,hasToken:!!r,payloaddata:a});let e="Authentication error";return e=r?t?"Invalid API call - Please check your request structure.":"Invalid API call - No URL path provided.":"No authentication token provided. Please log in again.",{error:{success:!1,message:e}}}catch(e){return console.error("API call failed:",e),{error:{success:!1,message:"An error occurred during the API call"}}}}},97245:(e,t,a)=>{"use strict";a.d(t,{$f:()=>d,H7:()=>g,HC:()=>h,Qg:()=>m,SV:()=>y,VL:()=>u,go:()=>c,h8:()=>i,i$:()=>n,i0:()=>l,qx:()=>p});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"userApi",baseQuery:o.V,tagTypes:["User"],endpoints:e=>({loginUser:e.mutation({query:e=>({urlpath:"/login",payloaddata:e})}),createUser:e.mutation({query:e=>{let t=s.M.getState().auth.accessToken||void 0;return{urlpath:"/users",payloaddata:{mode:"createnew",...e},token:t}},invalidatesTags:["User"]}),getAllUsers:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth.accessToken||void 0;return console.log("getAllUsers query params:",{page:e,limit:t,search:a}),{urlpath:"/users",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:r}},keepUnusedDataFor:0,providesTags:["User"]}),getUserById:e.query({query:e=>({urlpath:"/users",payloaddata:{mode:"retrieve",userId:e},token:s.M.getState().auth.accessToken||void 0}),providesTags:["User"]}),getCurrentUser:e.query({query:()=>({urlpath:"/users",payloaddata:{mode:"current"},token:s.M.getState().auth.accessToken||void 0}),providesTags:["User"]}),updateUser:e.mutation({query:({userId:e,data:t})=>{let a=s.M.getState().auth.accessToken||void 0;return{urlpath:"/users",payloaddata:{mode:"update",userId:e,...t},token:a}},invalidatesTags:["User"]}),deleteUser:e.mutation({query:e=>({urlpath:"/users",payloaddata:{mode:"delete",userId:e},token:s.M.getState().auth.accessToken||void 0}),invalidatesTags:["User"]}),bulkDeleteUsers:e.mutation({query:e=>({urlpath:"/users",payloaddata:{mode:"delete",userIds:e},token:s.M.getState().auth.accessToken||void 0}),invalidatesTags:["User"]}),logoutUser:e.mutation({query:e=>({urlpath:"/logout",method:"POST",payloaddata:e})}),changePassword:e.mutation({query:({userId:e,currentPassword:t,newPassword:a})=>({urlpath:"/users",payloaddata:{mode:"update",userId:e,currentPassword:t,newPassword:a,passwordChange:!0},token:s.M.getState().auth.accessToken||void 0}),invalidatesTags:["User"]})})}),{useLoginUserMutation:i,useCreateUserMutation:l,useGetAllUsersQuery:u,useGetUserByIdQuery:d,useGetCurrentUserQuery:c,useUpdateUserMutation:g,useDeleteUserMutation:h,useBulkDeleteUsersMutation:p,useLogoutUserMutation:m,useChangePasswordMutation:y}=n},42211:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,gV:()=>o,lM:()=>s});let r=(0,a(8979).Z0)({name:"auth",initialState:{user:null,accessToken:null},reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.accessToken=t.payload.accessToken},clearUser:e=>{e.user=null,e.accessToken=null}}}),{setUser:o,clearUser:s}=r.actions,n=r.reducer},43087:(e,t,a)=>{"use strict";a.d(t,{BP:()=>n,OU:()=>l,XY:()=>u,ec:()=>c,ez:()=>d,gH:()=>g,lg:()=>i});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"categoryApi",baseQuery:o.V,tagTypes:["Category"],endpoints:e=>({getAllCategories:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(console.log("getAllCategories query params:",{page:e,limit:t,search:a}),!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["Category"]}),getCategoryById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"retrieve",categoryId:e},token:a}},providesTags:(e,t,a)=>[{type:"Category",id:a}]}),createCategory:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(console.log("Creating category with token:",a?"Token exists (not showing for security)":"No token found"),console.log("Category data:",e),console.log("Auth state:",{hasUser:!!t?.user,hasToken:!!t?.accessToken,userRole:t?.user?.role}),!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Category"]}),updateCategory:e.mutation({query:({categoryId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"update",categoryId:e,...t},token:r}},invalidatesTags:(e,t,{categoryId:a})=>[{type:"Category",id:a},"Category"]}),deleteCategory:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"delete",categoryId:e},token:a}},invalidatesTags:["Category"]}),bulkDeleteCategories:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"delete",categoryIds:e},token:a}},invalidatesTags:["Category"]})})}),{useGetAllCategoriesQuery:i,useGetCategoryByIdQuery:l,useCreateCategoryMutation:u,useUpdateCategoryMutation:d,useDeleteCategoryMutation:c,useBulkDeleteCategoriesMutation:g}=n},42391:(e,t,a)=>{"use strict";a.d(t,{H:()=>l,P:()=>u});var r=a(32476),o=a(80025),s=a(9919),n=a(16589),i=a.n(n);let l=(0,r.xP)({reducerPath:"dashboardApi",baseQuery:o.V,tagTypes:["Dashboard"],endpoints:e=>({getDashboardStats:e.query({query:()=>{let e=s.M.getState().auth,t=e?.accessToken||"";if(!t)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");let a=i()().startOf("month").format("YYYY-MM-DD"),r=i()().endOf("month").format("YYYY-MM-DD"),o=new Date().getTime(),n=Math.random().toString(36).substring(7);return{urlpath:"/dashboard",payloaddata:{mode:"stats",timestamp:o,randomId:n,sessionId:Date.now()+Math.random(),cacheBuster:`${o}_${n}`,dateRange:{startDate:a,endDate:r}},token:t}},keepUnusedDataFor:0,providesTags:["Dashboard"]})})}),{useGetDashboardStatsQuery:u}=l},95239:(e,t,a)=>{"use strict";a.d(t,{GH:()=>c,Nx:()=>u,cY:()=>i,cp:()=>d,z9:()=>n});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"expenseApi",baseQuery:o.V,tagTypes:["Expense"],endpoints:e=>({getAllExpenses:e.query({query:({page:e=1,limit:t=10,search:a="",categoryId:r,startDate:o,endDate:n})=>{let i=s.M.getState().auth,l=i?.accessToken||"";if(!l)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"retrieve",page:e,limit:t,search:a,categoryId:r,startDate:o,endDate:n},token:l}},keepUnusedDataFor:0,providesTags:["Expense"]}),getExpenseById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"retrieve",expenseId:e},token:a}},providesTags:(e,t,a)=>[{type:"Expense",id:a}]}),createExpense:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Expense"]}),updateExpense:e.mutation({query:({expenseId:e,expenseData:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"update",expenseId:e,...t},token:r}},invalidatesTags:(e,t,{expenseId:a})=>[{type:"Expense",id:a},"Expense"]}),deleteExpense:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"delete",expenseId:e},token:a}},invalidatesTags:["Expense"]}),getExpenseStats:e.query({query:({startDate:e,endDate:t}={})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"stats",startDate:e,endDate:t},token:r}},keepUnusedDataFor:0,providesTags:["Expense"]})})}),{useGetAllExpensesQuery:i,useGetExpenseByIdQuery:l,useCreateExpenseMutation:u,useUpdateExpenseMutation:d,useDeleteExpenseMutation:c,useGetExpenseStatsQuery:g}=n},48991:(e,t,a)=>{"use strict";a.d(t,{CQ:()=>d,DY:()=>u,J0:()=>n,L:()=>i,Uc:()=>c});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"expenseCategoryApi",baseQuery:o.V,tagTypes:["ExpenseCategory"],endpoints:e=>({getAllExpenseCategories:e.query({query:({page:e=1,limit:t=50,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"retrieve",page:e,limit:t,search:a},token:o}},keepUnusedDataFor:300,providesTags:["ExpenseCategory"]}),getExpenseCategoryById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"retrieve",categoryId:e},token:a}},providesTags:(e,t,a)=>[{type:"ExpenseCategory",id:a}]}),createExpenseCategory:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["ExpenseCategory"]}),updateExpenseCategory:e.mutation({query:({categoryId:e,categoryData:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"update",categoryId:e,...t},token:r}},invalidatesTags:(e,t,{categoryId:a})=>[{type:"ExpenseCategory",id:a},"ExpenseCategory"]}),deleteExpenseCategory:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"delete",categoryId:e},token:a}},invalidatesTags:["ExpenseCategory"]})})}),{useGetAllExpenseCategoriesQuery:i,useGetExpenseCategoryByIdQuery:l,useCreateExpenseCategoryMutation:u,useUpdateExpenseCategoryMutation:d,useDeleteExpenseCategoryMutation:c}=n},9269:(e,t,a)=>{"use strict";a.d(t,{Ex:()=>d,Zf:()=>n,nm:()=>i});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"paymentApi",baseQuery:o.V,tagTypes:["Payment"],endpoints:e=>({initializePayment:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"initialize",...e},token:a}},invalidatesTags:["Payment"]}),getPaymentHistory:e.query({query:({page:e=1,limit:t=10})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"history",page:e,limit:t},token:r}},providesTags:["Payment"]}),getPaymentById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"retrieve",paymentId:e},token:a}},providesTags:["Payment"]}),verifyPaystackPayment:e.mutation({query:({reference:e})=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"verify-paystack",reference:e},token:a}},invalidatesTags:["Payment"]}),verifyPayment:e.mutation({query:({transactionId:e})=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"verify",transactionId:e},token:a}},invalidatesTags:["Payment"]}),updateUserPaymentStatus:e.mutation({query:({userId:e,paymentStatus:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/users",payloaddata:{mode:"update",userId:e,paymentStatus:t},token:r}},invalidatesTags:["Payment"]})})}),{useInitializePaymentMutation:i,useGetPaymentHistoryQuery:l,useGetPaymentByIdQuery:u,useVerifyPaystackPaymentMutation:d,useVerifyPaymentMutation:c,useUpdateUserPaymentStatusMutation:g}=n},2274:(e,t,a)=>{"use strict";a.d(t,{GQ:()=>n,IT:()=>h,Q$:()=>d,eb:()=>u,hi:()=>l,lY:()=>g,r3:()=>i,vM:()=>c});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"productApi",baseQuery:o.V,tagTypes:["Product"],endpoints:e=>({getAllProducts:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["Product"]}),getProductById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"retrieve",productId:e},token:a}},keepUnusedDataFor:0,providesTags:(e,t,a)=>[{type:"Product",id:a}]}),getProductByBarcode:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"barcode",barcode:e},token:a}},keepUnusedDataFor:0,providesTags:(e,t,a)=>[{type:"Product",id:`barcode-${a}`}]}),createProduct:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"createnew",productsData:[e]},token:a}},invalidatesTags:["Product"]}),updateProduct:e.mutation({query:({productId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"update",productId:e,...t},token:r}},invalidatesTags:["Product"]}),deleteProduct:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"delete",productId:e},token:a}},invalidatesTags:["Product"]}),bulkDeleteProducts:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"delete",productIds:e},token:a}},invalidatesTags:["Product"]})})}),{useGetAllProductsQuery:i,useGetProductByIdQuery:l,useGetProductByBarcodeQuery:u,useCreateProductMutation:d,useUpdateProductMutation:c,useDeleteProductMutation:g,useBulkDeleteProductsMutation:h}=n},65906:(e,t,a)=>{"use strict";a.d(t,{PY:()=>c,Pt:()=>l,eT:()=>n,hZ:()=>i,o3:()=>u,oq:()=>g,xc:()=>d});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"purchaseApi",baseQuery:o.V,tagTypes:["Purchase"],endpoints:e=>({getAllPurchases:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["Purchase"]}),getPurchaseById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"retrieve",purchaseId:e},token:a}},providesTags:(e,t,a)=>[{type:"Purchase",id:a}]}),createPurchase:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Purchase"]}),updatePurchase:e.mutation({query:({purchaseId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"update",purchaseId:e,...t},token:r}},invalidatesTags:(e,t,{purchaseId:a})=>[{type:"Purchase",id:a},"Purchase"]}),deletePurchase:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"delete",purchaseId:e},token:a}},invalidatesTags:["Purchase"]}),bulkDeletePurchases:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"delete",purchaseIds:e},token:a}},invalidatesTags:["Purchase"]})})}),{useGetAllPurchasesQuery:i,useGetPurchaseByIdQuery:l,useCreatePurchaseMutation:u,useUpdatePurchaseMutation:d,useDeletePurchaseMutation:c,useBulkDeletePurchasesMutation:g}=n},56335:(e,t,a)=>{"use strict";a.d(t,{Jy:()=>n,iB:()=>u,rN:()=>d,sK:()=>i});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"receiptApi",baseQuery:o.V,tagTypes:["Receipt"],endpoints:e=>({getAllReceipts:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"retrieve",page:e,limit:t,search:a},token:o}},providesTags:["Receipt"]}),getReceiptBySaleId:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"retrieveBySaleId",saleId:e},token:a}},providesTags:["Receipt"]}),deleteReceipt:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"delete",id:e},token:a}},invalidatesTags:["Receipt"]}),bulkDeleteReceipts:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"delete",receiptIds:e},token:a}},invalidatesTags:["Receipt"]})})}),{useGetAllReceiptsQuery:i,useGetReceiptBySaleIdQuery:l,useDeleteReceiptMutation:u,useBulkDeleteReceiptsMutation:d}=n},10499:(e,t,a)=>{"use strict";a.d(t,{E$:()=>u,J2:()=>n,JZ:()=>i,Kn:()=>g,Vk:()=>l,lz:()=>c});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"salesApi",baseQuery:o.V,tagTypes:["Sale"],endpoints:e=>({getAllSales:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["Sale"]}),getSaleById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"retrieve",saleId:e},token:a}},providesTags:(e,t,a)=>[{type:"Sale",id:a}]}),createSale:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"createnew",saleData:e},token:a}},invalidatesTags:["Sale"]}),updateSale:e.mutation({query:({saleId:e,updateData:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"update",saleId:e,...t},token:r}},invalidatesTags:(e,t,{saleId:a})=>[{type:"Sale",id:a}]}),deleteSale:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"delete",saleId:e},token:a}},invalidatesTags:["Sale"]}),bulkDeleteSales:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"delete",saleIds:e},token:a}},invalidatesTags:["Sale"]})})}),{useGetAllSalesQuery:i,useGetSaleByIdQuery:l,useCreateSaleMutation:u,useUpdateSaleMutation:d,useDeleteSaleMutation:c,useBulkDeleteSalesMutation:g}=n},11104:(e,t,a)=>{"use strict";a.d(t,{Wn:()=>u,sc:()=>n});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"stockAdjustmentApi",baseQuery:o.V,tagTypes:["StockAdjustment"],endpoints:e=>({getAllStockAdjustments:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["StockAdjustment"]}),getStockAdjustmentById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"retrieve",adjustmentId:e},token:a}},providesTags:(e,t,a)=>[{type:"StockAdjustment",id:a}]}),createStockAdjustment:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["StockAdjustment"]}),updateStockAdjustment:e.mutation({query:({adjustmentId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"update",adjustmentId:e,...t},token:r}},invalidatesTags:(e,t,{adjustmentId:a})=>[{type:"StockAdjustment",id:a},"StockAdjustment"]}),deleteStockAdjustment:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"delete",adjustmentId:e},token:a}},invalidatesTags:["StockAdjustment"]})})}),{useGetAllStockAdjustmentsQuery:i,useGetStockAdjustmentByIdQuery:l,useCreateStockAdjustmentMutation:u,useUpdateStockAdjustmentMutation:d,useDeleteStockAdjustmentMutation:c}=n},80852:(e,t,a)=>{"use strict";a.d(t,{HP:()=>d,ST:()=>u,Vf:()=>c,g0:()=>n,tD:()=>i,vs:()=>l,yv:()=>g});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"storeApi",baseQuery:o.V,tagTypes:["Store"],endpoints:e=>({getAllStores:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim()},token:o}},keepUnusedDataFor:0,providesTags:["Store"]}),getStoreById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"retrieve",storeId:e},token:a}},providesTags:(e,t,a)=>[{type:"Store",id:a}]}),createStore:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Store"]}),updateStore:e.mutation({query:({storeId:e,updateData:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"update",storeId:e,...t},token:r}},invalidatesTags:(e,t,{storeId:a})=>[{type:"Store",id:a}]}),deleteStore:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"delete",storeId:e},token:a}},invalidatesTags:["Store"]}),bulkDeleteStores:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"delete",storeIds:e},token:a}},invalidatesTags:["Store"]})})}),{useGetAllStoresQuery:i,useGetStoreByIdQuery:l,useCreateStoreMutation:u,useUpdateStoreMutation:d,useDeleteStoreMutation:c,useBulkDeleteStoresMutation:g}=n},63087:(e,t,a)=>{"use strict";a.d(t,{$i:()=>u,HC:()=>n,kH:()=>c,mP:()=>d,tr:()=>g,w$:()=>i,w1:()=>l});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"supplierApi",baseQuery:o.V,tagTypes:["Supplier"],endpoints:e=>({getAllSuppliers:e.query({query:({page:e=1,limit:t=10,search:a=""})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");let n=s.M.getState().auth.user?.id;return console.log("API call - User ID:",n),{urlpath:"/suppliers",payloaddata:{mode:"retrieve",page:e,limit:t,search:a.trim(),userId:n},token:o}},keepUnusedDataFor:0,providesTags:["Supplier"]}),getSupplierById:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"retrieve",supplierId:e},token:a}},providesTags:(e,t,a)=>[{type:"Supplier",id:a}]}),createSupplier:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Supplier"]}),updateSupplier:e.mutation({query:({supplierId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"update",supplierId:e,...t},token:r}},invalidatesTags:(e,t,{supplierId:a})=>[{type:"Supplier",id:a},"Supplier"]}),deleteSupplier:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"delete",supplierId:e},token:a}},invalidatesTags:["Supplier"]}),bulkDeleteSuppliers:e.mutation({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"delete",supplierIds:e},token:a}},invalidatesTags:["Supplier"]})})}),{useGetAllSuppliersQuery:i,useGetSupplierByIdQuery:l,useCreateSupplierMutation:u,useUpdateSupplierMutation:d,useDeleteSupplierMutation:c,useBulkDeleteSuppliersMutation:g}=n},46211:(e,t,a)=>{"use strict";a.d(t,{He:()=>g,PK:()=>c,Rv:()=>l,aW:()=>h,ih:()=>d,lr:()=>i,tU:()=>u,z$:()=>n});var r=a(32476),o=a(80025),s=a(9919);let n=(0,r.xP)({reducerPath:"userStoreApi",baseQuery:o.V,tagTypes:["UserStore","Store"],endpoints:e=>({getUserStores:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"getUserStores",userId:e},token:a}},keepUnusedDataFor:0,providesTags:["UserStore","Store"]}),getUserDefaultStore:e.query({query:e=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"getDefaultStore",userId:e},token:a}},providesTags:["UserStore","Store"]}),associateUserWithStore:e.mutation({query:({userId:e,storeId:t,isDefault:a=!1})=>{let r=s.M.getState().auth,o=r?.accessToken||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"associate",userId:e,storeId:t,isDefault:a},token:o}},invalidatesTags:["UserStore","Store"]}),setUserDefaultStore:e.mutation({query:({userId:e,storeId:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"setDefaultStore",userId:e,storeId:t},token:r}},invalidatesTags:["UserStore","Store"]}),removeUserFromStore:e.mutation({query:({userId:e,storeId:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"removeAssociation",userId:e,storeId:t},token:r}},invalidatesTags:["UserStore","Store"]}),createUserStore:e.mutation({query:({data:e})=>{let t=s.M.getState().auth,a=t?.accessToken||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"createStore",...e},token:a}},invalidatesTags:["UserStore","Store"]}),updateUserStore:e.mutation({query:({storeId:e,data:t})=>{let a=s.M.getState().auth,r=a?.accessToken||"";if(!r)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"updateStore",storeId:e,...t},token:r}},invalidatesTags:["UserStore","Store"]})})}),{useGetUserStoresQuery:i,useGetUserDefaultStoreQuery:l,useAssociateUserWithStoreMutation:u,useSetUserDefaultStoreMutation:d,useRemoveUserFromStoreMutation:c,useCreateUserStoreMutation:g,useUpdateUserStoreMutation:h}=n},9919:(e,t,a)=>{"use strict";a.d(t,{q:()=>w,M:()=>T});var r=a(57673),o=a(8979),s=a(39336),n=a(42211),i=a(97245),l=a(9269),u=a(43087),d=a(2274),c=a(11104),g=a(63087),h=a(65906),p=a(10499),m=a(80852),y=a(46211),k=a(56335),P=a(42391),v=a(95239),A=a(48991);a(11055);let f=(0,r.HY)({auth:n.Ay,[i.i$.reducerPath]:i.i$.reducer,[l.Zf.reducerPath]:l.Zf.reducer,[u.BP.reducerPath]:u.BP.reducer,[d.GQ.reducerPath]:d.GQ.reducer,[c.sc.reducerPath]:c.sc.reducer,[g.HC.reducerPath]:g.HC.reducer,[h.eT.reducerPath]:h.eT.reducer,[p.J2.reducerPath]:p.J2.reducer,[m.g0.reducerPath]:m.g0.reducer,[y.z$.reducerPath]:y.z$.reducer,[k.Jy.reducerPath]:k.Jy.reducer,[P.H.reducerPath]:P.H.reducer,[v.z9.reducerPath]:v.z9.reducer,[A.J0.reducerPath]:A.J0.reducer}),S=(0,s.rL)({key:"root",storage:{getItem:e=>Promise.resolve(null),setItem:(e,t)=>Promise.resolve(t),removeItem:e=>Promise.resolve()},whitelist:["auth"]},f),T=(0,o.U1)({reducer:S,middleware:e=>e({serializableCheck:!1}).concat(i.i$.middleware,l.Zf.middleware,u.BP.middleware,d.GQ.middleware,c.sc.middleware,g.HC.middleware,h.eT.middleware,p.J2.middleware,m.g0.middleware,y.z$.middleware,k.Jy.middleware,P.H.middleware,v.z9.middleware,A.J0.middleware)}),w=(0,s.GM)(T)},71354:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,metadata:()=>d});var r=a(62740);a(31944),a(78652),a(19933),a(36602);var o=a(92291),s=a.n(o),n=a(93104),i=a(81807),l=a(30998),u=a(82718);let d={title:"NEXAPO POS | Business Management System",description:"Complete business management platform with offline POS capabilities",manifest:"/manifest.json",themeColor:"#2563eb",viewport:{width:"device-width",initialScale:1,maximumScale:1,userScalable:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:"NEXAPO POS"},icons:{icon:[{url:"/favicon.ico",sizes:"any"},{url:"/images/logo.png",sizes:"192x192",type:"image/png"},{url:"/images/logo.png",sizes:"512x512",type:"image/png"}],apple:[{url:"/images/logo.png",sizes:"180x180",type:"image/png"}]},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"NEXAPO POS","application-name":"NEXAPO POS","msapplication-TileColor":"#2563eb","msapplication-tap-highlight":"no"}};function c({children:e}){return(0,r.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,className:"h-full",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("meta",{name:"theme-color",content:"#2563eb"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"NEXAPO POS"}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"/images/logo.png"})]}),(0,r.jsxs)("body",{suppressHydrationWarning:!0,className:"h-screen w-screen overflow-x-hidden",children:[(0,r.jsx)(n.Providers,{children:(0,r.jsx)(i.default,{children:(0,r.jsxs)(u.SidebarProvider,{children:[(0,r.jsx)(s(),{color:"#5750F1",showSpinner:!0}),e,(0,r.jsx)(l.Toaster,{position:"top-center",reverseOrder:!1})]})})}),(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', async function() {
                  try {
                    // Unregister any existing service workers first
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for(let registration of registrations) {
                      await registration.unregister();
                    }

                    // Register the new service worker
                    const swPath = '/sw.js';
                    const registration = await navigator.serviceWorker.register(swPath);
                    console.log('Service Worker registration successful with scope: ', registration.scope);

                    // Handle updates
                    registration.addEventListener('updatefound', () => {
                      const newWorker = registration.installing;
                      newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                          console.log('New service worker installed');
                        }
                      });
                    });
                  } catch (error) {
                    console.error('Service Worker registration failed: ', error);
                  }
                });

                // Handle offline/online events
                window.addEventListener('online', function() {
                  console.log('Back online');
                  window.location.reload();
                });

                window.addEventListener('offline', function() {
                  console.log('Gone offline');
                });
              }
            `}})]})]})}},93104:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\providers.tsx","Providers")},82718:(e,t,a)=>{"use strict";a.d(t,{SidebarProvider:()=>o});var r=a(46760);(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebarContext() from the server but useSidebarContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx","useSidebarContext");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx","SidebarProvider")},81807:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\provider\\Provider.tsx","default")},36602:()=>{},31944:()=>{},78652:()=>{}};