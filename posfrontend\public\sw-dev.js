importScripts('https://storage.googleapis.com/workbox-cdn/releases/6.4.1/workbox-sw.js');

const { registerRoute, NavigationRoute } = workbox.routing;
const { NetworkFirst, CacheFirst, StaleWhileRevalidate } = workbox.strategies;
const { CacheableResponsePlugin } = workbox.cacheableResponse;
const { ExpirationPlugin } = workbox.expiration;
const { precacheAndRoute } = workbox.precaching;

// Skip waiting and claim clients
self.skipWaiting();
workbox.core.clientsClaim();

// Precache essential resources
const resourcesToCache = [
  '/dashboard',
  '/dashboard/products',
  '/dashboard/sales',
  '/dashboard/customers',
  '/dashboard/expenses',
  '/dashboard/reports',
  '/offline.html',
  '/images/logo.png'
];

// Precache essential resources during installation
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open('app-shell').then((cache) => {
      return cache.addAll(resourcesToCache);
    })
  );
});

// Handle dashboard routes
registerRoute(
  ({ request, url }) => {
    // Match dashboard routes and their data requests
    return url.pathname.startsWith('/dashboard') || 
           url.pathname.includes('api') ||
           request.mode === 'navigate';
  },
  new NetworkFirst({
    cacheName: 'dashboard-cache',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxAgeSeconds: 24 * 60 * 60, // 24 hours
      }),
    ],
    networkTimeoutSeconds: 3, // Fallback to cache after 3 seconds
  })
);

// Cache static assets
registerRoute(
  ({ request }) => {
    return (
      request.destination === 'style' ||
      request.destination === 'script' ||
      request.destination === 'image' ||
      request.destination === 'font' ||
      request.url.includes('.css') ||
      request.url.includes('.js') ||
      request.url.includes('.png') ||
      request.url.includes('.jpg') ||
      request.url.includes('.jpeg') ||
      request.url.includes('.svg') ||
      request.url.includes('.ico')
    );
  },
  new CacheFirst({
    cacheName: 'static-assets',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      }),
    ],
  })
);

// Cache API responses
registerRoute(
  ({ url }) => url.pathname.includes('api'),
  new StaleWhileRevalidate({
    cacheName: 'api-cache',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 24 * 60 * 60, // 24 hours
      }),
    ],
  })
);

// Fallback handler for offline scenarios
self.addEventListener('fetch', (event) => {
  if (!navigator.onLine) {
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          if (response) {
            return response;
          }

          // For dashboard routes, try to serve dashboard shell
          const url = new URL(event.request.url);
          if (url.pathname.startsWith('/dashboard')) {
            return caches.match('/dashboard')
              .then((dashboardResponse) => {
                return dashboardResponse || caches.match('/offline.html');
              });
          }

          // For API requests, try to serve cached data
          if (url.pathname.includes('api')) {
            return caches.match(event.request, { ignoreSearch: true })
              .then((apiResponse) => {
                return apiResponse || new Response(JSON.stringify({ 
                  error: 'No cached data available'
                }), {
                  headers: { 'Content-Type': 'application/json' },
                  status: 503
                });
              });
          }

          // Default to offline page
          return caches.match('/offline.html');
        })
    );
  }
}); 