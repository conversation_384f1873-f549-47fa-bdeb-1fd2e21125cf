import { toast } from "react-hot-toast";

export const showMessage = (type: "success" | "error" | "warning", content: string) => {
  if (type === "success") {
    toast.success(content);
  } else if (type === "error") {
    toast.error(content);
  } else if (type === "warning") {
    toast(content, {
      icon: '⚠️',
      style: {
        background: '#FEF3C7',
        color: '#92400E',
        border: '1px solid #F59E0B',
      },
    });
  }
};

// Add convenience methods
showMessage.success = (content: string) => showMessage("success", content);
showMessage.error = (content: string) => showMessage("error", content);
showMessage.warning = (content: string) => showMessage("warning", content);

