import { ApiResponse } from "./user";

export interface Store {
  id: number;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  phone?: string;
  email?: string;
  logo?: string;
  website?: string;
  taxId?: string;
  createdBy?: number;
  createdByName?: string;
  createdAt: string;
  updatedAt: string;
  isDefault?: boolean;
}

export interface CreateStoreDto {
  name: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  phone?: string;
  email?: string;
  logo?: string;
  website?: string;
  taxId?: string;
}

export interface UpdateStoreDto {
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  phone?: string;
  email?: string;
  logo?: string;
  website?: string;
  taxId?: string;
}

export interface PaginatedStores {
  total: number;
  page: number;
  perPage: number;
  stores: Store[];
}

export interface UserStoreAssociation {
  id: number;
  userId: number;
  storeId: number;
  isDefault: boolean;
  createdBy: number;
  createdAt: string;
}

export interface StoreApiResponse<T> extends ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
}
