import createWebStorage from 'redux-persist/lib/storage/createWebStorage';

// Create a custom storage that handles the case when localStorage is not available
const createNoopStorage = () => {
  return {
    getItem(_key: string) {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: any) {
      return Promise.resolve(value);
    },
    removeItem(_key: string) {
      return Promise.resolve();
    }
  };
};

// Create storage that works in both browser and server environments
const storage = typeof window !== 'undefined' 
  ? createWebStorage('local')
  : createNoopStorage();

export default storage;
