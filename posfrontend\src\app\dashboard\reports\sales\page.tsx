"use client";

import React, { useState, useMemo } from "react";
import { Card, Button, Spin, DatePicker, Select, Row, Col, Statistic, Table, message } from "antd";
import {
  BarChartOutlined,
  LoadingOutlined,
  DownloadOutlined,
  CalendarOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGetDashboardStatsQuery } from "@/reduxRTK/services/dashboardApi";
import { useGetAllSalesQuery } from "@/reduxRTK/services/salesApi";
import { useGetAllProductsQuery } from "@/reduxRTK/services/productApi";
import dayjs from "dayjs";
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

const { RangePicker } = DatePicker;
const { Option } = Select;

const SalesReportsPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // State management
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'days'),
    dayjs()
  ]);
  const [reportType, setReportType] = useState<string>("summary");
  const [reportGenerated, setReportGenerated] = useState(true); // Auto-generate on load

  // API calls for real data
  const { data: dashboardData, isLoading: dashboardLoading } = useGetDashboardStatsQuery();
  const { data: salesData, isLoading: salesLoading, refetch: refetchSales } = useGetAllSalesQuery({
    page: 1,
    limit: 1000, // Get all sales for analysis
    search: ''
  });
  const { data: productsData, isLoading: productsLoading } = useGetAllProductsQuery({
    page: 1,
    limit: 1000, // Get all products for analysis
    search: ''
  });

  const isLoading = dashboardLoading || salesLoading || productsLoading;

  // Calculate real statistics from API data
  const salesStats = useMemo(() => {
    const sales = salesData?.data?.sales || [];
    const products = productsData?.data?.products || [];
    const dashStats = dashboardData?.data;

    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Sales Data:", salesData);
      console.log("🔍 Sales Array:", sales);
      console.log("🔍 Products Data:", productsData);
      console.log("🔍 Dashboard Data:", dashboardData);
      console.log("🔍 Report Type:", reportType);
      console.log("🔍 Report Generated:", reportGenerated);
    }

    // Filter sales by date range if specified
    const filteredSales = dateRange ? sales.filter(sale => {
      const saleDate = dayjs(sale.transactionDate);
      return saleDate.isAfter(dateRange[0].startOf('day')) && saleDate.isBefore(dateRange[1].endOf('day'));
    }) : sales;

    const totalRevenue = filteredSales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount), 0);
    const totalTransactions = filteredSales.length;
    const averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    // Calculate product performance
    const productSales: { [key: string]: { quantity: number; revenue: number; name: string } } = {};

    // Debug sales items
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Filtered Sales for Product Analysis:", filteredSales);
      filteredSales.forEach((sale, index) => {
        console.log(`🔍 Sale ${index + 1}:`, {
          id: sale.id,
          totalAmount: sale.totalAmount,
          hasItems: !!sale.items,
          itemsCount: sale.items?.length || 0,
          items: sale.items
        });
      });
    }

    filteredSales.forEach(sale => {
      if (sale.items && sale.items.length > 0) {
        sale.items.forEach(item => {
          const key = item.productId.toString();
          if (!productSales[key]) {
            productSales[key] = {
              quantity: 0,
              revenue: 0,
              name: item.productName || `Product ${item.productId}`
            };
          }
          productSales[key].quantity += item.quantity;
          productSales[key].revenue += parseFloat(item.price) * item.quantity;
        });
      }
    });

    // Debug product sales calculation
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Product Sales Calculated:", productSales);
    }

    // Find top selling product
    const topProduct = Object.entries(productSales)
      .sort(([,a], [,b]) => b.quantity - a.quantity)[0];

    // Fallback: if no product sales data, try to get from products list
    let topSellingProductName = "No sales data";
    if (topProduct) {
      topSellingProductName = topProduct[1].name;
    } else if (products.length > 0) {
      // Fallback to first product if no sales items
      topSellingProductName = `${products[0].name} (No sales yet)`;
    }

    // Calculate customer analysis (by cashier/user)
    const customerAnalysis: { [key: string]: { transactions: number; revenue: number; name: string } } = {};

    filteredSales.forEach(sale => {
      const cashierId = sale.createdBy.toString();
      const cashierName = sale.createdByName || `User ${sale.createdBy}`;

      if (!customerAnalysis[cashierId]) {
        customerAnalysis[cashierId] = {
          transactions: 0,
          revenue: 0,
          name: cashierName
        };
      }

      customerAnalysis[cashierId].transactions += 1;
      customerAnalysis[cashierId].revenue += parseFloat(sale.totalAmount);
    });

    // Calculate payment method analysis
    const paymentMethods: { [key: string]: { count: number; revenue: number } } = {};

    filteredSales.forEach(sale => {
      const method = sale.paymentMethod;
      if (!paymentMethods[method]) {
        paymentMethods[method] = { count: 0, revenue: 0 };
      }
      paymentMethods[method].count += 1;
      paymentMethods[method].revenue += parseFloat(sale.totalAmount);
    });

    // Debug customer analysis data
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Customer Analysis Raw:", customerAnalysis);
      console.log("🔍 Payment Methods Raw:", paymentMethods);
    }

    return {
      totalSales: totalRevenue,
      totalTransactions,
      averageOrderValue,
      topSellingProduct: topSellingProductName,
      productPerformance: Object.entries(productSales)
        .map(([productId, data]) => ({
          productId: parseInt(productId),
          productName: data.name,
          quantity: data.quantity,
          revenue: data.revenue
        }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10), // Top 10 products
      customerAnalysis: Object.entries(customerAnalysis)
        .map(([cashierId, data]) => ({
          cashierId: parseInt(cashierId),
          cashierName: data.name,
          transactions: data.transactions,
          revenue: data.revenue,
          averageOrderValue: data.transactions > 0 ? data.revenue / data.transactions : 0
        }))
        .sort((a, b) => b.revenue - a.revenue),
      paymentMethods: Object.entries(paymentMethods)
        .map(([method, data]) => ({
          method,
          count: data.count,
          revenue: data.revenue,
          percentage: totalTransactions > 0 ? (data.count / totalTransactions) * 100 : 0
        }))
        .sort((a, b) => b.revenue - a.revenue)
    };
  }, [salesData, productsData, dateRange, dashboardData, reportGenerated, reportType]);

  const handleGenerateReport = () => {
    setReportGenerated(true);
    refetchSales();
    message.success('Report generated successfully!');
  };

  const handleExportReport = () => {
    if (!reportGenerated) {
      message.warning('Please generate a report first');
      return;
    }

    // Create CSV content
    const csvContent = generateCSVContent();

    // Generate professional filename
    const dateRangeStr = dateRange ?
      `${dateRange[0].format('YYYY-MM-DD')}_to_${dateRange[1].format('YYYY-MM-DD')}` :
      'all-time';

    const reportTypeNames = {
      summary: 'Sales-Summary',
      products: 'Product-Performance',
      customers: 'Customer-Analysis',
      detailed: 'Detailed-Sales'
    };

    const filename = `NEXAPO_${reportTypeNames[reportType as keyof typeof reportTypeNames]}_Report_${dateRangeStr}_${dayjs().format('YYYY-MM-DD_HH-mm')}.csv`;

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('Professional report exported successfully!');
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      message.error('Please allow popups to print sales report');
      return;
    }
    const printContent = `
      <html>
        <head>
          <title>Sales Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print { .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Sales Report</h2>
            <p class="date">Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Month</th>
                <th>Amount</th>
                <th>Payment Method</th>
                <th>Cashier</th>
              </tr>
            </thead>
            <tbody>
              ${filteredSales.map(sale => `
                <tr>
                  <td>${dayjs(sale.transactionDate).format('YYYY-MM-DD HH:mm')}</td>
                  <td>${dayjs(sale.transactionDate).format('MMMM YYYY')}</td>
                  <td>₵${parseFloat(sale.totalAmount).toFixed(2)}</td>
                  <td>${sale.paymentMethod.replace('_', ' ').toUpperCase()}</td>
                  <td>${sale.createdByName || `User ${sale.createdBy}`}</td>
                </tr>
              `).join('')}
              <tr>
                <td colspan="2" style="font-weight:bold; text-align:right;">Total</td>
                <td style="font-weight:bold;">₵${totalAmount.toFixed(2)}</td>
                <td colspan="2"></td>
              </tr>
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;
    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  const handleExportPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text('Sales Report', 14, 15);
    doc.setFontSize(10);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}`, 14, 22);
    const tableData = filteredSales.map(sale => [
      dayjs(sale.transactionDate).format('YYYY-MM-DD HH:mm'),
      dayjs(sale.transactionDate).format('MMMM YYYY'),
      `₵${parseFloat(sale.totalAmount).toFixed(2)}`,
      sale.paymentMethod.replace('_', ' ').toUpperCase(),
      sale.createdByName || `User ${sale.createdBy}`
    ]);
    // Add summary row
    tableData.push([
      'Total',
      '',
      `₵${totalAmount.toFixed(2)}`,
      '',
      ''
    ]);
    autoTable(doc, {
      head: [['Date', 'Month', 'Amount', 'Payment Method', 'Cashier']],
      body: tableData,
      startY: 30,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [41, 128, 185] }
    });
    doc.save('sales-report.pdf');
  };

  const generateCSVContent = () => {
    const dateRangeStr = dateRange ?
      `${dateRange[0].format('YYYY-MM-DD')} to ${dateRange[1].format('YYYY-MM-DD')}` :
      'All time';

    const currentDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const reportTypeNames = {
      summary: 'Sales Summary Report',
      products: 'Product Performance Report',
      customers: 'Customer Analysis Report',
      detailed: 'Detailed Sales Report'
    };

    // Professional header section
    const headerSection = [
      'NEXAPO SALES REPORT',
      '',
      `Report Type:,${reportTypeNames[reportType as keyof typeof reportTypeNames]}`,
      `Date Range:,${dateRangeStr}`,
      `Generated On:,${currentDate}`,
      `Generated By:,${(currentUser as any)?.firstName || 'Admin'} ${(currentUser as any)?.lastName || 'User'}`,
      '',
      'REPORT DATA',
      ''
    ];

    if (reportType === "summary") {
      const summarySection = [
        'SALES SUMMARY',
        '',
        'Metric,Value,Description',
        `Total Revenue,GHS ${salesStats.totalSales.toFixed(2)},Total sales revenue for selected period`,
        `Total Transactions,${salesStats.totalTransactions},Number of completed sales`,
        `Average Order Value,GHS ${salesStats.averageOrderValue.toFixed(2)},Average revenue per transaction`,
        `Top Selling Product,"${salesStats.topSellingProduct}",Best performing product by quantity`
      ];
      return [...headerSection, ...summarySection].join('\n');

    } else if (reportType === "products") {
      const productSection = [
        'PRODUCT PERFORMANCE ANALYSIS',
        '',
        'Rank,Product Name,Quantity Sold,Revenue (GHS)',
        ...salesStats.productPerformance.map((product, index) => [
          (index + 1).toString(),
          `"${product.productName}"`, // Quotes for names with commas
          product.quantity.toString(),
          `GHS ${product.revenue.toFixed(2)}`
        ].join(',')),
        '',
        'SUMMARY STATISTICS',
        `Total Products Analyzed,${salesStats.productPerformance.length}`,
        `Total Revenue,GHS ${salesStats.productPerformance.reduce((sum, p) => sum + p.revenue, 0).toFixed(2)}`,
        `Total Quantity Sold,${salesStats.productPerformance.reduce((sum, p) => sum + p.quantity, 0)}`
      ];
      return [...headerSection, ...productSection].join('\n');

    } else if (reportType === "customers") {
      const customerSection = [
        'CUSTOMER ANALYSIS REPORT',
        '',
        'CASHIER PERFORMANCE',
        'Rank,Cashier Name,Transactions,Revenue (GHS),Avg Order Value (GHS)',
        ...salesStats.customerAnalysis.map((cashier, index) => [
          (index + 1).toString(),
          `"${cashier.cashierName}"`,
          cashier.transactions.toString(),
          `GHS ${cashier.revenue.toFixed(2)}`,
          `GHS ${cashier.averageOrderValue.toFixed(2)}`
        ].join(',')),
        '',
        'PAYMENT METHODS ANALYSIS',
        'Payment Method,Transactions,Revenue (GHS),Percentage (%)',
        ...salesStats.paymentMethods.map(method => [
          method.method.replace('_', ' ').toUpperCase(),
          method.count.toString(),
          `GHS ${method.revenue.toFixed(2)}`,
          `${method.percentage.toFixed(1)}%`
        ].join(',')),
        '',
        'SUMMARY STATISTICS',
        `Total Cashiers,${salesStats.customerAnalysis.length}`,
        `Total Payment Methods,${salesStats.paymentMethods.length}`,
        `Total Revenue,GHS ${salesStats.customerAnalysis.reduce((sum, c) => sum + c.revenue, 0).toFixed(2)}`
      ];
      return [...headerSection, ...customerSection].join('\n');

    } else if (reportType === "detailed") {
      const filteredSales = salesData?.data?.sales?.filter(sale => {
        if (!dateRange) return true;
        const saleDate = dayjs(sale.transactionDate);
        return saleDate.isAfter(dateRange[0].startOf('day')) && saleDate.isBefore(dateRange[1].endOf('day'));
      }) || [];

      const detailedSection = [
        'DETAILED SALES TRANSACTIONS',
        '',
        'Transaction ID,Date & Time,Month,Amount (GHS),Payment Method,Cashier',
        ...filteredSales.map(sale => [
          sale.id.toString(),
          dayjs(sale.transactionDate).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(sale.transactionDate).format('MMMM YYYY'),
          `GHS ${parseFloat(sale.totalAmount).toFixed(2)}`,
          sale.paymentMethod.replace('_', ' ').toUpperCase(),
          `"${sale.createdByName || `User ${sale.createdBy}`}"`
        ].join(',')),
        '',
        'SUMMARY STATISTICS',
        `Total Transactions,${filteredSales.length}`,
        `Total Revenue,GHS ${filteredSales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount), 0).toFixed(2)}`,
        `Average Transaction,GHS ${filteredSales.length > 0 ? (filteredSales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount), 0) / filteredSales.length).toFixed(2) : '0.00'}`,
        `Date Range,${dateRangeStr}`
      ];
      return [...headerSection, ...detailedSection].join('\n');
    }

    return '';
  };

  const filteredSales = (salesData?.data?.sales || []).filter(sale => {
    if (!dateRange) return true;
    const saleDate = dayjs(sale.transactionDate);
    return saleDate.isAfter(dateRange[0].startOf('day')) && saleDate.isBefore(dateRange[1].endOf('day'));
  });
  const totalAmount = filteredSales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount), 0);

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={
          <div className="flex items-center gap-2">
            <BarChartOutlined className="text-blue-600" />
            <span className="text-gray-800">Sales Reports</span>
          </div>
        }
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          <div className={isMobile ? "flex flex-col gap-2" : "flex flex-row gap-2 items-center"}>
            <Button
              type="primary"
              icon={<PrinterOutlined />} 
              onClick={handlePrint}
              size={isMobile ? "small" : "middle"}
              className="bg-green-600 hover:bg-green-700"
            >
              {isMobile ? "" : "Print"}
            </Button>
            <Button
              type="primary"
              icon={<DownloadOutlined />} 
              onClick={handleExportPDF}
              size={isMobile ? "small" : "middle"}
              className="bg-green-600 hover:bg-green-700"
            >
              {isMobile ? "" : "Export"}
            </Button>
          </div>
        }
      >
        <div className="w-full space-y-6">
          {/* Filters Section */}
          <Card
            title="Report Filters"
            size="small"
            className="bg-gray-50"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Date Range</label>
                  <RangePicker
                    value={dateRange}
                    onChange={(dates) => {
                      if (dates && dates[0] && dates[1]) {
                        setDateRange([dates[0], dates[1]]);
                      } else {
                        setDateRange(null);
                      }
                    }}
                    className="w-full"
                    format="YYYY-MM-DD"
                    suffixIcon={<CalendarOutlined />}
                    getPopupContainer={(trigger) => trigger.parentElement || document.body}
                    style={{ zIndex: 1000 }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Report Type</label>
                  <Select
                    value={reportType}
                    onChange={setReportType}
                    className="w-full"
                  >
                    <Option value="summary">Sales Summary</Option>
                    <Option value="detailed">Detailed Sales</Option>
                    <Option value="products">Product Performance</Option>
                    <Option value="customers">Customer Analysis</Option>
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={24} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">&nbsp;</label>
                  <Button
                    type="primary"
                    onClick={handleGenerateReport}
                    loading={isLoading}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    icon={<BarChartOutlined />}
                  >
                    Generate Report
                  </Button>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Statistics Cards */}
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Total Sales"
                  value={salesStats.totalSales}
                  precision={2}
                  prefix="₵"
                  valueStyle={{ color: '#3f8600', fontSize: '18px' }}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Transactions"
                  value={salesStats.totalTransactions}
                  valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Avg. Order Value"
                  value={salesStats.averageOrderValue}
                  precision={2}
                  prefix="₵"
                  valueStyle={{ color: '#722ed1', fontSize: '18px' }}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <div className="w-full">
                  <div className="text-sm text-gray-500 mb-1">Top Product</div>
                  <div
                    className="text-sm font-semibold text-gray-800 break-words max-w-full"
                    style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}
                    title={salesStats.topSellingProduct}
                  >
                    {salesStats.topSellingProduct}
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          {/* Report Content */}
          <Card title="Report Data" className="min-h-96">
            {isLoading ? (
              <div className="flex h-60 items-center justify-center">
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{ fontSize: 24, color: "#1890ff" }}
                      spin
                    />
                  }
                />
              </div>
            ) : reportGenerated ? (
              <div className="space-y-6">


                {/* Sales Summary Table */}
                {reportType === "summary" && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Sales Summary</h3>



                    {/* Sales Summary Table */}
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full min-w-[600px]">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Metric</th>
                              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Value</th>
                              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Description</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-gray-900">Total Revenue</td>
                              <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{salesStats.totalSales.toFixed(2)}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">Total sales revenue for selected period</td>
                            </tr>
                            <tr className="border-b hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-gray-900">Total Transactions</td>
                              <td className="px-4 py-3 text-sm font-semibold text-blue-600">{salesStats.totalTransactions}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">Number of completed sales</td>
                            </tr>
                            <tr className="border-b hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-gray-900">Average Order Value</td>
                              <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{salesStats.averageOrderValue.toFixed(2)}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">Average revenue per transaction</td>
                            </tr>
                            <tr className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-gray-900">Top Selling Product</td>
                              <td className="px-4 py-3 text-sm font-semibold text-blue-600">{salesStats.topSellingProduct}</td>
                              <td className="px-4 py-3 text-sm text-gray-600">Best performing product by quantity</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>


                  </div>
                )}

                {/* Product Performance Table */}
                {reportType === "products" && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Product Performance</h3>
                    {salesStats.productPerformance && salesStats.productPerformance.length > 0 ? (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="w-full min-w-[500px]">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Rank</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Product Name</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Quantity Sold</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Revenue</th>
                              </tr>
                            </thead>
                            <tbody>
                              {salesStats.productPerformance.map((product, index) => (
                                <tr key={index} className="border-b hover:bg-gray-50">
                                  <td className="px-4 py-3 text-sm text-gray-900">{index + 1}</td>
                                  <td className="px-4 py-3 text-sm text-gray-900">{product.productName}</td>
                                  <td className="px-4 py-3 text-sm text-gray-900">{product.quantity}</td>
                                  <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{product.revenue.toFixed(2)}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-10">
                        <p>No product performance data available.</p>
                        <p className="text-sm mt-2">This could be because there are no sales with product details in the selected period.</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Customer Analysis */}
                {reportType === "customers" && (
                  <div className="space-y-6">
                    {/* Cashier Performance */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Cashier Performance</h3>
                      {salesStats.customerAnalysis && salesStats.customerAnalysis.length > 0 ? (
                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div className="overflow-x-auto">
                            <table className="w-full min-w-[600px]">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Rank</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Cashier Name</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Transactions</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Revenue</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Avg. Order Value</th>
                                </tr>
                              </thead>
                              <tbody>
                                {salesStats.customerAnalysis.map((cashier, index) => (
                                  <tr key={index} className="border-b hover:bg-gray-50">
                                    <td className="px-4 py-3 text-sm text-gray-900">{index + 1}</td>
                                    <td className="px-4 py-3 text-sm text-gray-900">{cashier.cashierName}</td>
                                    <td className="px-4 py-3 text-sm text-gray-900">{cashier.transactions}</td>
                                    <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{cashier.revenue.toFixed(2)}</td>
                                    <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{cashier.averageOrderValue.toFixed(2)}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center text-gray-500 py-10">
                          <p>No cashier performance data available.</p>
                        </div>
                      )}
                    </div>

                    {/* Payment Methods Analysis */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Payment Methods Analysis</h3>
                      {salesStats.paymentMethods && salesStats.paymentMethods.length > 0 ? (
                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div className="overflow-x-auto">
                            <table className="w-full min-w-[500px]">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Payment Method</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Transactions</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Revenue</th>
                                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Percentage</th>
                                </tr>
                              </thead>
                              <tbody>
                                {salesStats.paymentMethods.map((method, index) => (
                                  <tr key={index} className="border-b hover:bg-gray-50">
                                    <td className="px-4 py-3 text-sm text-gray-900">{method.method.replace('_', ' ').toUpperCase()}</td>
                                    <td className="px-4 py-3 text-sm text-gray-900">{method.count}</td>
                                    <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{method.revenue.toFixed(2)}</td>
                                    <td className="px-4 py-3 text-sm font-semibold text-green-600">{method.percentage.toFixed(1)}%</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center text-gray-500 py-10">
                          <p>No payment method data available.</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Detailed Sales */}
                {reportType === "detailed" && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Detailed Sales</h3>

                    {salesData?.data?.sales && salesData.data.sales.length > 0 ? (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="w-full min-w-[600px]">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Date</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Month</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Amount</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Payment Method</th>
                                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Cashier</th>
                              </tr>
                            </thead>
                            <tbody>
                              {salesData.data.sales
                                .filter(sale => {
                                  if (!dateRange) return true;
                                  const saleDate = dayjs(sale.transactionDate);
                                  return saleDate.isAfter(dateRange[0].startOf('day')) && saleDate.isBefore(dateRange[1].endOf('day'));
                                })
                                .map((sale, index) => (
                                  <tr key={sale.id} className="border-b hover:bg-gray-50">
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {dayjs(sale.transactionDate).format('YYYY-MM-DD HH:mm')}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {dayjs(sale.transactionDate).format('MMMM YYYY')}
                                    </td>
                                    <td className="px-4 py-3 text-sm font-semibold text-blue-600">
                                      ₵{parseFloat(sale.totalAmount).toFixed(2)}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {sale.paymentMethod.replace('_', ' ').toUpperCase()}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {sale.createdByName || `User ${sale.createdBy}`}
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </div>
                        <div className="px-4 py-3 bg-gray-50 border-t text-sm text-gray-600">
                          Showing {salesData.data.sales.filter(sale => {
                            if (!dateRange) return true;
                            const saleDate = dayjs(sale.transactionDate);
                            return saleDate.isAfter(dateRange[0].startOf('day')) && saleDate.isBefore(dateRange[1].endOf('day'));
                          }).length} sales transactions
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-10">
                        <p>No sales data available for the selected period.</p>
                        <p className="text-sm mt-2">Try adjusting the date range or check if there are any sales in the system.</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-20">
                <BarChartOutlined className="text-4xl mb-4" />
                <p>Select filters and click &quot;Generate Report&quot; to view sales data</p>
                <p className="text-sm mt-2">Charts and detailed analytics will appear here</p>
              </div>
            )}
          </Card>

          {/* Coming Soon Notice */}
          <Card className="bg-blue-50 border-blue-200">
            <div className="text-center text-blue-700">
              <h3 className="font-semibold mb-2">📊 Advanced Analytics Coming Soon</h3>
              <p className="text-sm">
                Interactive charts, trend analysis, and detailed breakdowns will be available in future updates.
              </p>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default SalesReportsPage;
