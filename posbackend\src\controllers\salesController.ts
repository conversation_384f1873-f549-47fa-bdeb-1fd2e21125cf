import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createSale,
  getAllSales,
  getSaleById,
  updateSale,
  deleteSale,
  getSaleDetails,
} from "../services/salesService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleSalesRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, saleId, page, limit, saleData } = req.body; // saleData instead of bulkSalesData
  const requester = req.user as DecodedToken;

  const validModes = ["createnew", "retrieve", "update", "delete"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      case "createnew": {
        // Validate saleData for "createnew" mode
        if (
          !saleData ||
          !saleData.items ||
          !Array.isArray(saleData.items) ||
          saleData.items.length === 0
        ) {
          return sendResponse(
            res,
            400,
            false,
            "Sale data with valid items is required."
          );
        }

        // Handle sale creation
        const result = await createSale(requester, { saleData });
        return sendResponse(
          res,
          201,
          true,
          "Sale created successfully.",
          result
        );
      }

      case "retrieve": {
        if (saleId) {
          try {
            // Fetch sale with items included
            const sale = await getSaleById(requester, Number(saleId));
            console.log("Retrieved sale with items:", sale);

            // Return the sale with its items
            return sendResponse(res, 200, true, "Sale retrieved successfully.", {
              sale,
              saleItems: (sale as any).items || [], // Include items for backward compatibility
            });
          } catch (error) {
            console.error("Error retrieving sale:", error);
            return sendResponse(res, 500, false, "Error retrieving sale details");
          }
        } else {
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;
          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          // Fetch sales data with items already included
          const salesData = await getAllSales(requester, pageNum, limitNum);
          console.log("Retrieved sales with items:", salesData.sales.length);

          return sendResponse(res, 200, true, "Sales retrieved successfully.", salesData);
        }
      }

      case "update": {
        if (!saleId) {
          return sendResponse(
            res,
            400,
            false,
            "Sale ID is required for updating."
          );
        }

        const updatedSale = await updateSale(
          requester,
          Number(saleId),
          req.body
        );
        return sendResponse(
          res,
          200,
          true,
          "Sale updated successfully.",
          updatedSale
        );
      }

      case "delete": {
        // Check if we have saleIds (array) or saleId (single)
        const { saleIds } = req.body;

        if (!saleId && !saleIds) {
          return sendResponse(
            res,
            400,
            false,
            "Sale ID(s) are required for deletion."
          );
        }

        // Use saleIds if provided, otherwise use single saleId
        const idsToDelete = saleIds
          ? (Array.isArray(saleIds) ? saleIds : [saleIds])
          : [Number(saleId)];

        const result = await deleteSale(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} sales deleted successfully.`
          : "Sale deleted successfully.";

        return sendResponse(
          res,
          200,
          true,
          message,
          result
        );
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(
      res,
      500,
      false,
      error.message || "Internal Server Error"
    );
  }
};
