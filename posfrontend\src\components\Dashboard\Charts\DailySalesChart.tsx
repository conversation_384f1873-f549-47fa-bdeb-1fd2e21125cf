"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface DailySalesChartProps {
  sales: any[];
}

const DailySalesChart: React.FC<DailySalesChartProps> = ({ sales }) => {
  const chartData = useMemo(() => {
    console.log('📊 Daily Sales Chart - Sales found:', sales.length);

    // Create last 7 days array for daily sales
    const last7Days: Array<{
      dayKey: string;
      dayLabel: string;
      salesCount: number;
      revenue: number;
    }> = [];
    
    for (let i = 6; i >= 0; i--) {
      const day = dayjs().subtract(i, 'day');
      last7Days.push({
        dayKey: day.format('YYYY-MM-DD'),
        dayLabel: day.format('ddd DD'),
        salesCount: 0,
        revenue: 0
      });
    }

    // Group sales by day
    sales.forEach(sale => {
      if (sale.transactionDate) {
        const saleDay = dayjs(sale.transactionDate).format('YYYY-MM-DD');
        const dayData = last7Days.find(d => d.dayKey === saleDay);
        if (dayData) {
          dayData.salesCount++;
          dayData.revenue += parseFloat(sale.totalAmount || '0');
        }
      }
    });

    const salesData = last7Days.map(day => ({
      x: day.dayLabel,
      y: day.salesCount
    }));

    const revenueData = last7Days.map(day => ({
      x: day.dayLabel,
      y: day.revenue
    }));

    console.log('📈 Daily Sales chart data:', { salesData, revenueData });
    return { salesData, revenueData };
  }, [sales]);

  const series = [
    {
      name: 'Sales Count',
      type: 'column',
      data: chartData.salesData,
    },
    {
      name: 'Revenue (₵)',
      type: 'line',
      data: chartData.revenueData,
    },
  ];

  const options: ApexOptions = {
    chart: {
      height: 350,
      type: 'line',
      stacked: false,
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: false,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      width: [0, 4],
      curve: 'smooth',
    },
    plotOptions: {
      bar: {
        columnWidth: '50%',
      },
    },
    fill: {
      opacity: [0.85, 1],
      gradient: {
        inverseColors: false,
        shade: 'light',
        type: 'vertical',
        opacityFrom: 0.85,
        opacityTo: 0.55,
        stops: [0, 100, 100, 100],
      },
    },
    labels: chartData.salesData.map(item => item.x),
    markers: {
      size: 0,
    },
    xaxis: {
      type: 'category',
      labels: {
        style: {
          fontSize: '12px',
          fontWeight: 500,
        },
      },
    },
    yaxis: [
      {
        title: {
          text: 'Sales Count',
          style: {
            color: '#1f77b4',
            fontSize: '14px',
            fontWeight: 600,
          },
        },
        labels: {
          style: {
            colors: '#1f77b4',
          },
          formatter: (value) => Math.round(value).toString(),
        },
      },
      {
        opposite: true,
        title: {
          text: 'Revenue (₵)',
          style: {
            color: '#ff7f0e',
            fontSize: '14px',
            fontWeight: 600,
          },
        },
        labels: {
          style: {
            colors: '#ff7f0e',
          },
          formatter: (value) => `₵${value.toFixed(0)}`,
        },
      },
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: [
        {
          formatter: (value) => `${value} sales`,
        },
        {
          formatter: (value) => `₵${value.toFixed(2)}`,
        },
      ],
    },
    legend: {
      horizontalAlign: 'left',
      offsetX: 40,
    },
    colors: ['#1f77b4', '#ff7f0e'],
    grid: {
      borderColor: '#f1f1f1',
      strokeDashArray: 3,
    },
  };

  if (chartData.salesData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No daily sales data available</p>
          <p className="text-sm">Chart will appear when sales are made</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="line"
        height={250}
      />
    </div>
  );
};

export default DailySalesChart;
