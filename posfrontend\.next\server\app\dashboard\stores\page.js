(()=>{var e={};e.id=3488,e.ids=[3488],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},23818:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=n(70260),o=n(28203),l=n(25155),a=n.n(l),i=n(67292),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);n.d(t,s);let c=["",{children:["dashboard",{children:["stores",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,22367)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\stores\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\stores\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/stores/page",pathname:"/dashboard/stores",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48765:(e,t,n)=>{Promise.resolve().then(n.bind(n,22367))},92733:(e,t,n)=>{Promise.resolve().then(n.bind(n,83374))},44209:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},53180:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},23847:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},63440:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0014.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h676c17.7 0 32-14.3 32-32V535a175 175 0 0015.6-28.9c9.5-22.3 14.4-46 14.4-70.4V304c0-17-13.3-30.9-30-31.9zM214 184h596v88H214v-88zm362 656.1H448V736h128v104.1zm234 0H640V704c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v136.1H214V597.9c2.9 1.4 5.9 2.8 9 4 22.3 9.4 46 14.1 70.4 14.1s48-4.7 70.4-14.1c13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 3-1.3 6-2.6 9-4v242.2zm30-404.4c0 59.8-49 108.3-109.3 108.3-40.8 0-76.4-22.1-95.2-54.9-2.9-5-8.1-8.1-13.9-8.1h-.6c-5.7 0-11 3.1-13.9 8.1A109.24 109.24 0 01512 544c-40.7 0-76.2-22-95-54.7-3-5.1-8.4-8.3-14.3-8.3s-11.4 3.2-14.3 8.3a109.63 109.63 0 01-95.1 54.7C233 544 184 495.5 184 435.7v-91.2c0-.3.2-.5.5-.5h655c.3 0 .5.2.5.5v91.2z"}}]},name:"shop",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},37248:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(55740),o=n.n(r);function l(e,t,n,r){var l=o().unstable_batchedUpdates?function(e){o().unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,l,r),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,l,r)}}}},83374:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>lN});var r=n(45512),o=n(58009),l=n.n(o),a=n(6987),i=n(3117),s=n(21419),c=n(37287),d=n(24648),u=n(88752),m=n(48752),p=n(77067),f=n(70001),g=n(25421),h=n(63440),v=n(11855);let b={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 289.1a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1zm0-617.2c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 551c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 439c0-29.9 11.7-58 32.8-79.2C454 338.6 482.1 327 512 327c29.9 0 58 11.6 79.2 32.8C612.4 381 624 409.1 624 439c0 29.9-11.6 58-32.8 79.2z"}}]},name:"environment",theme:"outlined"};var x=n(78480),y=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:b}))}),A=n(23847),$=n(25834),w=n(99261),C=n(86977),k=n(63844),S=n(73542),E=n(80852),N=n(59022),j=n(60165);let I=({current:e,pageSize:t,total:n,onChange:o,isMobile:l=!1})=>{let a=Math.ceil(n/t);return(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,n)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:n})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>o(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(N.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,a)},(t,n)=>{let l=n+1;return(0,r.jsx)("button",{onClick:()=>o(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700"}`,children:l},l)}),(0,r.jsxs)("button",{onClick:()=>o(e+1),disabled:e>=a,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=a?"text-gray-400 cursor-not-allowed":"text-gray-700"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(j.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>o(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white"}`,children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",a]}),(0,r.jsx)("button",{onClick:()=>o(e+1),disabled:e>=a,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=a?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white"}`,children:"Next"})]})]})};var O=n(51531),z=n(49792);let P=e=>{let[t,{isLoading:n}]=(0,E.Vf)();return{deleteStore:async n=>{try{let r=await t(n).unwrap();if(!r.success)throw Error(r.message||"Failed to delete store");return(0,z.r)("success","Store deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete store error:",e),(0,z.r)("error",e.message||"Failed to delete store"),e}},isDeleting:n}};var K=n(16589),R=n.n(K);let M=({search:e,onEdit:t,onView:n,onSuccess:l,onSelect:a,onBulkDelete:s,isMobile:c=!1})=>{let u=(0,S.E)(),v=c||u,[b,x]=(0,o.useState)(1),[N,j]=(0,o.useState)(10),[z,K]=(0,o.useState)(null),[R,M]=(0,o.useState)(!1),[B,T]=(0,o.useState)(null),[D,H]=(0,o.useState)([]),[L,_]=(0,o.useState)(!1),{data:W,isLoading:F,isFetching:q,refetch:V}=(0,E.tD)({page:b,limit:N,search:e}),{deleteStore:X,isDeleting:U}=P(()=>{M(!1),l()}),G=e=>{T(e),M(!0)},Y=async()=>{B&&await X(B)},J=e=>{let t=e.target.checked;_(t),t?H(Z.map(e=>e.id)):H([])},Q=(e,t)=>{t?H(t=>[...t,e]):H(t=>t.filter(t=>t!==e))},Z=W?.data?.stores||[];return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[(0,r.jsx)(O.A,{isOpen:R,onClose:()=>{M(!1),T(null)},onConfirm:Y,title:"Delete Store",message:"Are you sure you want to delete this store? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:U,type:"danger"}),D.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[D.length," ",1===D.length?"store":"stores"," selected"]}),(0,r.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(g.A,{}),onClick:()=>{D.length>0&&s?(s(D),H([]),_(!1)):m.Ay.warning({message:"No stores selected",description:"Please select at least one store to delete."})},className:"ml-2",children:"Delete Selected"})]}),v?(0,r.jsxs)(k.jB,{columns:"50px 200px 200px 150px 150px",minWidth:"800px",children:[(0,r.jsx)(k.A0,{className:"text-center",children:(0,r.jsx)(p.A,{checked:L,onChange:J,disabled:0===Z.length})}),(0,r.jsx)(k.A0,{sticky:v?void 0:"left",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"}),"Location"]})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Contact"]})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),"Created By"]})}),(0,r.jsx)(k.A0,{sticky:v?void 0:"right",className:"text-right",children:"Actions"}),Z.map(e=>(0,r.jsxs)(k.Hj,{selected:D.includes(e.id),children:[(0,r.jsx)(k.nA,{className:"text-center",children:(0,r.jsx)(p.A,{checked:D.includes(e.id),onChange:t=>Q(e.id,t.target.checked)})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis text-gray-600",children:[e.address,e.city,e.state,e.country].filter(Boolean).join(", ")||"N/A"})}),(0,r.jsx)(k.nA,{children:e.phone||e.email?(0,r.jsxs)("div",{className:"max-w-[130px] overflow-hidden text-ellipsis",children:[e.phone&&(0,r.jsx)("div",{className:"font-mono text-sm",children:e.phone}),e.email&&(0,r.jsx)("div",{className:"text-blue-600 text-sm",children:e.email})]}):"N/A"}),(0,r.jsx)(k.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(f.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)($.A,{}),onClick:t=>{t.stopPropagation(),n(e)},type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),(0,r.jsx)(f.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(w.A,{}),onClick:n=>{n.stopPropagation(),t(e)},type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(f.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:t=>{t.stopPropagation(),G(e.id)},type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(p.A,{checked:L,onChange:J,disabled:0===Z.length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"}),"Location"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Contact"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),"Created By"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:Z.map(e=>(0,r.jsxs)("tr",{className:D.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:(0,r.jsx)(p.A,{checked:D.includes(e.id),onChange:t=>Q(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[200px] overflow-hidden text-ellipsis",children:[e.address,e.city,e.state,e.country].filter(Boolean).join(", ")||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.phone||e.email?(0,r.jsxs)("div",{className:"max-w-[150px] overflow-hidden text-ellipsis",children:[e.phone&&(0,r.jsx)("div",{children:e.phone}),e.email&&(0,r.jsx)("div",{children:e.email})]}):"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.createdByName||"N/A"}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(f.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)($.A,{}),onClick:t=>{t.stopPropagation(),n(e)},type:"text",className:"view-button text-green-500",size:"middle"})}),(0,r.jsx)(f.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(w.A,{}),onClick:n=>{n.stopPropagation(),t(e)},type:"text",className:"edit-button text-blue-500",size:"middle"})}),(0,r.jsx)(f.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:t=>{t.stopPropagation(),G(e.id)},type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})}),W?.data&&(0,r.jsx)(I,{current:b,pageSize:N,total:W.data.total,onChange:(e,t)=>{x(e),t&&j(t)},isMobile:v})]})};var B=n(37764),T=n(58733);n(86078);let D=({value:e,onChange:t,isMobile:n=!1})=>(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(B.A,{placeholder:"Search stores by name, location...",prefix:(0,r.jsx)(T.A,{className:"text-gray-500"}),value:e,onChange:e=>{let n=e.target.value;console.log("Store search input changed:",n),t(n)},className:"border-gray-300 bg-white text-gray-800 focus:border-blue-500",style:{width:n?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var H=n(41257),L=n(53180);let _={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"};var W=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:_}))});let F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136V232h752v560zM610.3 476h123.4c1.3 0 2.3-3.6 2.3-8v-48c0-4.4-1-8-2.3-8H610.3c-1.3 0-2.3 3.6-2.3 8v48c0 4.4 1 8 2.3 8zm4.8 144h185.7c3.9 0 7.1-3.6 7.1-8v-48c0-4.4-3.2-8-7.1-8H615.1c-3.9 0-7.1 3.6-7.1 8v48c0 4.4 3.2 8 7.1 8zM224 673h43.9c4.2 0 7.6-3.3 7.9-7.5 3.8-50.5 46-90.5 97.2-90.5s93.4 40 97.2 90.5c.3 4.2 3.7 7.5 7.9 7.5H522a8 8 0 008-8.4c-2.8-53.3-32-99.7-74.6-126.1a111.8 111.8 0 0029.1-75.5c0-61.9-49.9-112-111.4-112s-111.4 50.1-111.4 112c0 29.1 11 55.5 29.1 75.5a158.09 158.09 0 00-74.6 126.1c-.4 4.6 3.2 8.4 7.8 8.4zm149-262c28.5 0 51.7 23.3 51.7 52s-23.2 52-51.7 52-51.7-23.3-51.7-52 23.2-52 51.7-52z"}}]},name:"idcard",theme:"outlined"};var q=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:F}))}),V=n(98776);n(27742);let X=({isOpen:e,onClose:t,onSuccess:n,editStore:l,mode:a})=>{let[s]=H.A.useForm(),[c,{isLoading:d}]=(0,E.ST)(),[u,{isLoading:m}]=(0,E.HP)(),{refetch:p}=(0,E.tD)({page:1,limit:1e3},{skip:!0});(0,o.useEffect)(()=>{e&&"edit"===a&&l?s.setFieldsValue({name:l.name,address:l.address,city:l.city,state:l.state,country:l.country,phone:l.phone,email:l.email,website:l.website,taxId:l.taxId}):e&&"create"===a&&s.resetFields()},[e,a,l,s]);let f=async()=>{try{let e=await s.validateFields();if("create"===a){let r={name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId},o=await c(r).unwrap();o.success?((0,z.r)("success","Store created successfully"),s.resetFields(),setTimeout(()=>{t(),n&&n(),p()},500)):(0,z.r)("error",o.message||"Failed to create store")}else if("edit"===a&&l){let r={name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId},o=await u({storeId:l.id,updateData:r}).unwrap();o.success?((0,z.r)("success","Store updated successfully"),t(),n&&n()):(0,z.r)("error",o.message||"Failed to update store")}}catch(e){(0,z.r)("error",e.data?.message||"An error occurred while saving the store")}},g=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Cancel"}),(0,r.jsx)(i.Ay,{type:"primary",onClick:()=>s.submit(),loading:d||m,icon:(0,r.jsx)(h.A,{}),className:"bg-blue-600 hover:bg-blue-700 border-blue-600",children:"create"===a?"Create Store":"Update Store"})]});return(0,r.jsx)(V.A,{title:"create"===a?"Create Store":"Edit Store",isOpen:e,onClose:t,width:"500px",footer:g,children:(0,r.jsx)("div",{className:"p-6 bg-white",children:(0,r.jsxs)(H.A,{form:s,layout:"vertical",className:"store-form",onFinish:f,requiredMark:!0,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Store Information"}),(0,r.jsx)(H.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-1"})," Store Name"]}),rules:[{required:!0,message:"Please enter the store name"}],tooltip:"Name of your store or business",children:(0,r.jsx)(B.A,{placeholder:"Enter store name"})}),(0,r.jsx)(H.A.Item,{name:"address",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"})," Address"]}),tooltip:"Physical address of your store",children:(0,r.jsx)(B.A.TextArea,{rows:2,placeholder:"Enter store address"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(H.A.Item,{name:"city",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"})," City"]}),children:(0,r.jsx)(B.A,{placeholder:"Enter city"})}),(0,r.jsx)(H.A.Item,{name:"state",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"})," State/Province"]}),children:(0,r.jsx)(B.A,{placeholder:"Enter state/province"})}),(0,r.jsx)(H.A.Item,{name:"country",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"mr-1"})," Country"]}),children:(0,r.jsx)(B.A,{placeholder:"Enter country"})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Contact Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(H.A.Item,{name:"phone",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"})," Phone"]}),tooltip:"Contact phone number for the store",children:(0,r.jsx)(B.A,{placeholder:"Enter phone number"})}),(0,r.jsx)(H.A.Item,{name:"email",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(L.A,{className:"mr-1"})," Email"]}),tooltip:"Contact email for the store",rules:[{type:"email",message:"Please enter a valid email address"}],children:(0,r.jsx)(B.A,{placeholder:"Enter email address"})})]}),(0,r.jsx)(H.A.Item,{name:"website",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(W,{className:"mr-1"})," Website"]}),tooltip:"Store website URL (if available)",children:(0,r.jsx)(B.A,{placeholder:"Enter website URL"})}),(0,r.jsx)(H.A.Item,{name:"taxId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(q,{className:"mr-1"})," Tax ID / Business Registration"]}),tooltip:"Tax ID or business registration number",children:(0,r.jsx)(B.A,{placeholder:"Enter tax ID or registration number"})})]})]})})})};var U=n(12869),G=n(81045);let Y=({isOpen:e,onClose:t,onEdit:n,storeId:o})=>{let{data:l,isLoading:a}=(0,E.vs)(o||0,{skip:!o||!e}),c=l?.data,m=e=>e?R()(e).format("MMM D, YYYY"):"N/A",p=c?(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"}),(0,r.jsx)(i.Ay,{type:"primary",icon:(0,r.jsx)(w.A,{}),onClick:()=>n(c),className:"border-blue-600 bg-blue-600 hover:bg-blue-700",children:"Edit"})]}):(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"})});return(0,r.jsx)(V.A,{title:"Store Details",isOpen:e,onClose:t,width:"500px",footer:p,children:(0,r.jsx)("div",{className:"bg-white p-6s",children:a?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)(s.A,{indicator:(0,r.jsx)(u.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):c?(0,r.jsxs)("div",{className:"mx-auto max-w-2xl",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(h.A,{className:"mr-2"}),"Store: ",c.name]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"Complete store information and details"})]}),(0,r.jsxs)(U.A,{bordered:!0,column:1,className:"store-detail-light",children:[(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(h.A,{})," Store ID"]}),children:c.id}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(h.A,{})," Name"]}),children:c.name}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(y,{})," Address"]}),children:c.address||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(y,{})," City"]}),children:c.city||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(y,{})," State/Province"]}),children:c.state||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(y,{})," Country"]}),children:c.country||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(A.A,{})," Phone"]}),children:c.phone||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(L.A,{})," Email"]}),children:c.email||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(W,{})," Website"]}),children:c.website||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(q,{})," Tax ID"]}),children:c.taxId||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(d.A,{})," Created By"]}),children:c.createdByName||"N/A"}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(G.A,{})," Created At"]}),children:m(c.createdAt)}),(0,r.jsx)(U.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(G.A,{})," Last Updated"]}),children:m(c.updatedAt)})]})]}):(0,r.jsx)("div",{className:"text-center text-gray-800",children:(0,r.jsx)("p",{children:'Store not found or you don"t have permission to view it.'})})})})};var J=n(46542),Q=n(7325),Z={},ee="rc-table-internal-hook",et=n(7770),en=n(25392),er=n(55977),eo=n(56114),el=n(55740);function ea(e){var t=o.createContext(void 0);return{Context:t,Provider:function(e){var n=e.value,r=e.children,l=o.useRef(n);l.current=n;var a=o.useState(function(){return{getValue:function(){return l.current},listeners:new Set}}),i=(0,et.A)(a,1)[0];return(0,er.A)(function(){(0,el.unstable_batchedUpdates)(function(){i.listeners.forEach(function(e){e(n)})})},[n]),o.createElement(t.Provider,{value:i},r)},defaultValue:e}}function ei(e,t){var n=(0,en.A)("function"==typeof t?t:function(e){if(void 0===t)return e;if(!Array.isArray(t))return e[t];var n={};return t.forEach(function(t){n[t]=e[t]}),n}),r=o.useContext(null==e?void 0:e.Context),l=r||{},a=l.listeners,i=l.getValue,s=o.useRef();s.current=n(r?i():null==e?void 0:e.defaultValue);var c=o.useState({}),d=(0,et.A)(c,2)[1];return(0,er.A)(function(){if(r)return a.add(e),function(){a.delete(e)};function e(e){var t=n(e);(0,eo.A)(s.current,t,!0)||d({})}},[r]),s.current}var es=n(80799);function ec(){var e=o.createContext(null);function t(){return o.useContext(e)}return{makeImmutable:function(n,r){var l=(0,es.f3)(n),a=function(a,i){var s=l?{ref:i}:{},c=o.useRef(0),d=o.useRef(a);return null!==t()?o.createElement(n,(0,v.A)({},a,s)):((!r||r(d.current,a))&&(c.current+=1),d.current=a,o.createElement(e.Provider,{value:c.current},o.createElement(n,(0,v.A)({},a,s))))};return l?o.forwardRef(a):a},responseImmutable:function(e,n){var r=(0,es.f3)(e),l=function(n,l){return t(),o.createElement(e,(0,v.A)({},n,r?{ref:l}:{}))};return r?o.memo(o.forwardRef(l),n):o.memo(l,n)},useImmutableMark:t}}var ed=ec();ed.makeImmutable,ed.responseImmutable,ed.useImmutableMark;var eu=ec(),em=eu.makeImmutable,ep=eu.responseImmutable,ef=eu.useImmutableMark,eg=ea(),eh=n(97549),ev=n(12992),eb=n(65074),ex=n(56073),ey=n.n(ex),eA=n(45860),e$=n(75312),ew=n(67010),eC=o.createContext({renderWithProps:!1});function ek(e){var t=[],n={};return e.forEach(function(e){for(var r=e||{},o=r.key,l=r.dataIndex,a=o||(null==l?[]:Array.isArray(l)?l:[l]).join("-")||"RC_TABLE_KEY";n[a];)a="".concat(a,"_next");n[a]=!0,t.push(a)}),t}var eS=n(29966),eE=function(e){var t,n=e.ellipsis,r=e.rowType,l=e.children,a=!0===n?{showTitle:!0}:n;return a&&(a.showTitle||"header"===r)&&("string"==typeof l||"number"==typeof l?t=l.toString():o.isValidElement(l)&&"string"==typeof l.props.children&&(t=l.props.children)),t};let eN=o.memo(function(e){var t,n,r,l,a,i,s,c,d,u,m=e.component,p=e.children,f=e.ellipsis,g=e.scope,h=e.prefixCls,b=e.className,x=e.align,y=e.record,A=e.render,$=e.dataIndex,w=e.renderIndex,C=e.shouldCellUpdate,k=e.index,S=e.rowType,E=e.colSpan,N=e.rowSpan,j=e.fixLeft,I=e.fixRight,O=e.firstFixLeft,z=e.lastFixLeft,P=e.firstFixRight,K=e.lastFixRight,R=e.appendNode,M=e.additionalProps,B=void 0===M?{}:M,T=e.isSticky,D="".concat(h,"-cell"),H=ei(eg,["supportSticky","allColumnsFixedLeft","rowHoverable"]),L=H.supportSticky,_=H.allColumnsFixedLeft,W=H.rowHoverable,F=(t=o.useContext(eC),n=ef(),(0,eA.A)(function(){if(null!=p)return[p];var e=null==$||""===$?[]:Array.isArray($)?$:[$],n=(0,e$.A)(y,e),r=n,l=void 0;if(A){var a=A(n,y,w);!a||"object"!==(0,eh.A)(a)||Array.isArray(a)||o.isValidElement(a)?r=a:(r=a.children,l=a.props,t.renderWithProps=!0)}return[r,l]},[n,y,p,$,A,w],function(e,n){if(C){var r=(0,et.A)(e,2)[1];return C((0,et.A)(n,2)[1],r)}return!!t.renderWithProps||!(0,eo.A)(e,n,!0)})),q=(0,et.A)(F,2),V=q[0],X=q[1],U={},G="number"==typeof j&&L,Y="number"==typeof I&&L;G&&(U.position="sticky",U.left=j),Y&&(U.position="sticky",U.right=I);var J=null!==(r=null!==(l=null!==(a=null==X?void 0:X.colSpan)&&void 0!==a?a:B.colSpan)&&void 0!==l?l:E)&&void 0!==r?r:1,Q=null!==(i=null!==(s=null!==(c=null==X?void 0:X.rowSpan)&&void 0!==c?c:B.rowSpan)&&void 0!==s?s:N)&&void 0!==i?i:1,Z=ei(eg,function(e){var t,n;return[(t=Q||1,n=e.hoverStartRow,k<=e.hoverEndRow&&k+t-1>=n),e.onHover]}),ee=(0,et.A)(Z,2),en=ee[0],er=ee[1],el=(0,eS._q)(function(e){var t;y&&er(k,k+Q-1),null==B||null===(t=B.onMouseEnter)||void 0===t||t.call(B,e)}),ea=(0,eS._q)(function(e){var t;y&&er(-1,-1),null==B||null===(t=B.onMouseLeave)||void 0===t||t.call(B,e)});if(0===J||0===Q)return null;var es=null!==(d=B.title)&&void 0!==d?d:eE({rowType:S,ellipsis:f,children:V}),ec=ey()(D,b,(u={},(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)(u,"".concat(D,"-fix-left"),G&&L),"".concat(D,"-fix-left-first"),O&&L),"".concat(D,"-fix-left-last"),z&&L),"".concat(D,"-fix-left-all"),z&&_&&L),"".concat(D,"-fix-right"),Y&&L),"".concat(D,"-fix-right-first"),P&&L),"".concat(D,"-fix-right-last"),K&&L),"".concat(D,"-ellipsis"),f),"".concat(D,"-with-append"),R),"".concat(D,"-fix-sticky"),(G||Y)&&T&&L),(0,eb.A)(u,"".concat(D,"-row-hover"),!X&&en)),B.className,null==X?void 0:X.className),ed={};x&&(ed.textAlign=x);var eu=(0,ev.A)((0,ev.A)((0,ev.A)((0,ev.A)({},null==X?void 0:X.style),U),ed),B.style),em=V;return"object"!==(0,eh.A)(em)||Array.isArray(em)||o.isValidElement(em)||(em=null),f&&(z||P)&&(em=o.createElement("span",{className:"".concat(D,"-content")},em)),o.createElement(m,(0,v.A)({},X,B,{className:ec,style:eu,title:es,scope:g,onMouseEnter:W?el:void 0,onMouseLeave:W?ea:void 0,colSpan:1!==J?J:null,rowSpan:1!==Q?Q:null}),R,em)});function ej(e,t,n,r,o){var l,a,i=n[e]||{},s=n[t]||{};"left"===i.fixed?l=r.left["rtl"===o?t:e]:"right"===s.fixed&&(a=r.right["rtl"===o?e:t]);var c=!1,d=!1,u=!1,m=!1,p=n[t+1],f=n[e-1],g=p&&!p.fixed||f&&!f.fixed||n.every(function(e){return"left"===e.fixed});return"rtl"===o?void 0!==l?m=!(f&&"left"===f.fixed)&&g:void 0!==a&&(u=!(p&&"right"===p.fixed)&&g):void 0!==l?c=!(p&&"left"===p.fixed)&&g:void 0!==a&&(d=!(f&&"right"===f.fixed)&&g),{fixLeft:l,fixRight:a,lastFixLeft:c,firstFixRight:d,lastFixRight:u,firstFixLeft:m,isSticky:r.isSticky}}var eI=o.createContext({}),eO=n(49543),ez=["children"];function eP(e){return e.children}eP.Row=function(e){var t=e.children,n=(0,eO.A)(e,ez);return o.createElement("tr",n,t)},eP.Cell=function(e){var t=e.className,n=e.index,r=e.children,l=e.colSpan,a=void 0===l?1:l,i=e.rowSpan,s=e.align,c=ei(eg,["prefixCls","direction"]),d=c.prefixCls,u=c.direction,m=o.useContext(eI),p=m.scrollColumnIndex,f=m.stickyOffsets,g=m.flattenColumns,h=n+a-1+1===p?a+1:a,b=ej(n,n+h-1,g,f,u);return o.createElement(eN,(0,v.A)({className:t,index:n,component:"td",prefixCls:d,record:null,dataIndex:null,align:s,colSpan:h,rowSpan:i,render:function(){return r}},b))};let eK=ep(function(e){var t=e.children,n=e.stickyOffsets,r=e.flattenColumns,l=ei(eg,"prefixCls"),a=r.length-1,i=r[a],s=o.useMemo(function(){return{stickyOffsets:n,flattenColumns:r,scrollColumnIndex:null!=i&&i.scrollbar?a:null}},[i,r,a,n]);return o.createElement(eI.Provider,{value:s},o.createElement("tfoot",{className:"".concat(l,"-summary")},t))});var eR=n(21776),eM=n(51811),eB=n(67725),eT=n(31299),eD=n(90365);function eH(e,t,n,r){return o.useMemo(function(){if(null!=n&&n.size){for(var o=[],l=0;l<(null==e?void 0:e.length);l+=1)!function e(t,n,r,o,l,a,i){t.push({record:n,indent:r,index:i});var s=a(n),c=null==l?void 0:l.has(s);if(n&&Array.isArray(n[o])&&c)for(var d=0;d<n[o].length;d+=1)e(t,n[o][d],r+1,o,l,a,d)}(o,e[l],0,t,n,r,l);return o}return null==e?void 0:e.map(function(e,t){return{record:e,indent:0,index:t}})},[e,t,n,r])}function eL(e,t,n,r){var o,l=ei(eg,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),a=l.flattenColumns,i=l.expandableType,s=l.expandedKeys,c=l.childrenColumnName,d=l.onTriggerExpand,u=l.rowExpandable,m=l.onRow,p=l.expandRowByClick,f=l.rowClassName,g="nest"===i,h="row"===i&&(!u||u(e)),v=h||g,b=s&&s.has(t),x=c&&e&&e[c],y=(0,eS._q)(d),A=null==m?void 0:m(e,n),$=null==A?void 0:A.onClick;"string"==typeof f?o=f:"function"==typeof f&&(o=f(e,n,r));var w=ek(a);return(0,ev.A)((0,ev.A)({},l),{},{columnsKey:w,nestExpandable:g,expanded:b,hasNestChildren:x,record:e,onTriggerExpand:y,rowSupportExpand:h,expandable:v,rowProps:(0,ev.A)((0,ev.A)({},A),{},{className:ey()(o,null==A?void 0:A.className),onClick:function(t){p&&v&&d(e,t);for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null==$||$.apply(void 0,[t].concat(r))}})})}let e_=function(e){var t=e.prefixCls,n=e.children,r=e.component,l=e.cellComponent,a=e.className,i=e.expanded,s=e.colSpan,c=e.isEmpty,d=ei(eg,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),u=d.scrollbarSize,m=d.fixHeader,p=d.fixColumn,f=d.componentWidth,g=d.horizonScroll,h=n;return(c?g&&f:p)&&(h=o.createElement("div",{style:{width:f-(m&&!c?u:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},h)),o.createElement(r,{className:a,style:{display:i?null:"none"}},o.createElement(eN,{component:l,prefixCls:t,colSpan:s},h))};function eW(e){var t=e.prefixCls,n=e.record,r=e.onExpand,l=e.expanded,a=e.expandable,i="".concat(t,"-row-expand-icon");return a?o.createElement("span",{className:ey()(i,(0,eb.A)((0,eb.A)({},"".concat(t,"-row-expanded"),l),"".concat(t,"-row-collapsed"),!l)),onClick:function(e){r(n,e),e.stopPropagation()}}):o.createElement("span",{className:ey()(i,"".concat(t,"-row-spaced"))})}function eF(e,t,n,r){return"string"==typeof e?e:"function"==typeof e?e(t,n,r):""}function eq(e,t,n,r,l){var a,i,s=e.record,c=e.prefixCls,d=e.columnsKey,u=e.fixedInfoList,m=e.expandIconColumnIndex,p=e.nestExpandable,f=e.indentSize,g=e.expandIcon,h=e.expanded,v=e.hasNestChildren,b=e.onTriggerExpand,x=d[n],y=u[n];return n===(m||0)&&p&&(a=o.createElement(o.Fragment,null,o.createElement("span",{style:{paddingLeft:"".concat(f*r,"px")},className:"".concat(c,"-row-indent indent-level-").concat(r)}),g({prefixCls:c,expanded:h,expandable:v,record:s,onExpand:b}))),t.onCell&&(i=t.onCell(s,l)),{key:x,fixedInfo:y,appendCellNode:a,additionalCellProps:i||{}}}let eV=ep(function(e){var t,n=e.className,r=e.style,l=e.record,a=e.index,i=e.renderIndex,s=e.rowKey,c=e.indent,d=void 0===c?0:c,u=e.rowComponent,m=e.cellComponent,p=e.scopeCellComponent,f=eL(l,s,a,d),g=f.prefixCls,h=f.flattenColumns,b=f.expandedRowClassName,x=f.expandedRowRender,y=f.rowProps,A=f.expanded,$=f.rowSupportExpand,w=o.useRef(!1);w.current||(w.current=A);var C=eF(b,l,a,d),k=o.createElement(u,(0,v.A)({},y,{"data-row-key":s,className:ey()(n,"".concat(g,"-row"),"".concat(g,"-row-level-").concat(d),null==y?void 0:y.className,(0,eb.A)({},C,d>=1)),style:(0,ev.A)((0,ev.A)({},r),null==y?void 0:y.style)}),h.map(function(e,t){var n=e.render,r=e.dataIndex,s=e.className,c=eq(f,e,t,d,a),u=c.key,h=c.fixedInfo,b=c.appendCellNode,x=c.additionalCellProps;return o.createElement(eN,(0,v.A)({className:s,ellipsis:e.ellipsis,align:e.align,scope:e.rowScope,component:e.rowScope?p:m,prefixCls:g,key:u,record:l,index:a,renderIndex:i,dataIndex:r,render:n,shouldCellUpdate:e.shouldCellUpdate},h,{appendNode:b,additionalProps:x}))}));if($&&(w.current||A)){var S=x(l,a,d+1,A);t=o.createElement(e_,{expanded:A,className:ey()("".concat(g,"-expanded-row"),"".concat(g,"-expanded-row-level-").concat(d+1),C),prefixCls:g,component:u,cellComponent:m,colSpan:h.length,isEmpty:!1},S)}return o.createElement(o.Fragment,null,k,t)});function eX(e){var t=e.columnKey,n=e.onColumnResize,r=o.useRef();return o.useEffect(function(){r.current&&n(t,r.current.offsetWidth)},[]),o.createElement(eR.A,{data:t},o.createElement("td",{ref:r,style:{padding:0,border:0,height:0}},o.createElement("div",{style:{height:0,overflow:"hidden"}},"\xa0")))}function eU(e){var t=e.prefixCls,n=e.columnsKey,r=e.onColumnResize;return o.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},o.createElement(eR.A.Collection,{onBatchResize:function(e){e.forEach(function(e){r(e.data,e.size.offsetWidth)})}},n.map(function(e){return o.createElement(eX,{key:e,columnKey:e,onColumnResize:r})})))}let eG=ep(function(e){var t,n=e.data,r=e.measureColumnWidth,l=ei(eg,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),a=l.prefixCls,i=l.getComponent,s=l.onColumnResize,c=l.flattenColumns,d=l.getRowKey,u=l.expandedKeys,m=l.childrenColumnName,p=l.emptyNode,f=eH(n,m,u,d),g=o.useRef({renderWithProps:!1}),h=i(["body","wrapper"],"tbody"),v=i(["body","row"],"tr"),b=i(["body","cell"],"td"),x=i(["body","cell"],"th");t=n.length?f.map(function(e,t){var n=e.record,r=e.indent,l=e.index,a=d(n,t);return o.createElement(eV,{key:a,rowKey:a,record:n,index:t,renderIndex:l,rowComponent:v,cellComponent:b,scopeCellComponent:x,indent:r})}):o.createElement(e_,{expanded:!0,className:"".concat(a,"-placeholder"),prefixCls:a,component:v,cellComponent:b,colSpan:c.length,isEmpty:!0},p);var y=ek(c);return o.createElement(eC.Provider,{value:g.current},o.createElement(h,{className:"".concat(a,"-tbody")},r&&o.createElement(eU,{prefixCls:a,columnsKey:y,onColumnResize:s}),t))});var eY=["expandable"],eJ="RC_TABLE_INTERNAL_COL_DEFINE",eQ=["columnType"];let eZ=function(e){for(var t=e.colWidths,n=e.columns,r=e.columCount,l=ei(eg,["tableLayout"]).tableLayout,a=[],i=r||n.length,s=!1,c=i-1;c>=0;c-=1){var d=t[c],u=n&&n[c],m=void 0,p=void 0;if(u&&(m=u[eJ],"auto"===l&&(p=u.minWidth)),d||p||m||s){var f=m||{},g=(f.columnType,(0,eO.A)(f,eQ));a.unshift(o.createElement("col",(0,v.A)({key:c,style:{width:d,minWidth:p}},g))),s=!0}}return o.createElement("colgroup",null,a)};var e0=n(43984),e1=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"],e2=o.forwardRef(function(e,t){var n=e.className,r=e.noData,l=e.columns,a=e.flattenColumns,i=e.colWidths,s=e.columCount,c=e.stickyOffsets,d=e.direction,u=e.fixHeader,m=e.stickyTopOffset,p=e.stickyBottomOffset,f=e.stickyClassName,g=e.onScroll,h=e.maxContentScroll,v=e.children,b=(0,eO.A)(e,e1),x=ei(eg,["prefixCls","scrollbarSize","isSticky","getComponent"]),y=x.prefixCls,A=x.scrollbarSize,$=x.isSticky,w=(0,x.getComponent)(["header","table"],"table"),C=$&&!u?0:A,k=o.useRef(null),S=o.useCallback(function(e){(0,es.Xf)(t,e),(0,es.Xf)(k,e)},[]);o.useEffect(function(){var e;function t(e){var t=e.currentTarget,n=e.deltaX;n&&(g({currentTarget:t,scrollLeft:t.scrollLeft+n}),e.preventDefault())}return null===(e=k.current)||void 0===e||e.addEventListener("wheel",t,{passive:!1}),function(){var e;null===(e=k.current)||void 0===e||e.removeEventListener("wheel",t)}},[]);var E=o.useMemo(function(){return a.every(function(e){return e.width})},[a]),N=a[a.length-1],j={fixed:N?N.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(y,"-cell-scrollbar")}}},I=(0,o.useMemo)(function(){return C?[].concat((0,e0.A)(l),[j]):l},[C,l]),O=(0,o.useMemo)(function(){return C?[].concat((0,e0.A)(a),[j]):a},[C,a]),z=(0,o.useMemo)(function(){var e=c.right,t=c.left;return(0,ev.A)((0,ev.A)({},c),{},{left:"rtl"===d?[].concat((0,e0.A)(t.map(function(e){return e+C})),[0]):t,right:"rtl"===d?e:[].concat((0,e0.A)(e.map(function(e){return e+C})),[0]),isSticky:$})},[C,c,$]),P=(0,o.useMemo)(function(){for(var e=[],t=0;t<s;t+=1){var n=i[t];if(void 0===n)return null;e[t]=n}return e},[i.join("_"),s]);return o.createElement("div",{style:(0,ev.A)({overflow:"hidden"},$?{top:m,bottom:p}:{}),ref:S,className:ey()(n,(0,eb.A)({},f,!!f))},o.createElement(w,{style:{tableLayout:"fixed",visibility:r||P?null:"hidden"}},(!r||!h||E)&&o.createElement(eZ,{colWidths:P?[].concat((0,e0.A)(P),[C]):[],columCount:s+1,columns:O}),v((0,ev.A)((0,ev.A)({},b),{},{stickyOffsets:z,columns:I,flattenColumns:O}))))});let e3=o.memo(e2),e4=function(e){var t,n=e.cells,r=e.stickyOffsets,l=e.flattenColumns,a=e.rowComponent,i=e.cellComponent,s=e.onHeaderRow,c=e.index,d=ei(eg,["prefixCls","direction"]),u=d.prefixCls,m=d.direction;s&&(t=s(n.map(function(e){return e.column}),c));var p=ek(n.map(function(e){return e.column}));return o.createElement(a,t,n.map(function(e,t){var n,a=e.column,s=ej(e.colStart,e.colEnd,l,r,m);return a&&a.onHeaderCell&&(n=e.column.onHeaderCell(a)),o.createElement(eN,(0,v.A)({},e,{scope:a.title?e.colSpan>1?"colgroup":"col":null,ellipsis:a.ellipsis,align:a.align,component:i,prefixCls:u,key:p[t]},s,{additionalProps:n,rowType:"header"}))}))},e8=ep(function(e){var t=e.stickyOffsets,n=e.columns,r=e.flattenColumns,l=e.onHeaderRow,a=ei(eg,["prefixCls","getComponent"]),i=a.prefixCls,s=a.getComponent,c=o.useMemo(function(){return function(e){var t=[];!function e(n,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;t[o]=t[o]||[];var l=r;return n.filter(Boolean).map(function(n){var r={key:n.key,className:n.className||"",children:n.title,column:n,colStart:l},a=1,i=n.children;return i&&i.length>0&&(a=e(i,l,o+1).reduce(function(e,t){return e+t},0),r.hasSubColumns=!0),"colSpan"in n&&(a=n.colSpan),"rowSpan"in n&&(r.rowSpan=n.rowSpan),r.colSpan=a,r.colEnd=r.colStart+a-1,t[o].push(r),l+=a,a})}(e,0);for(var n=t.length,r=function(e){t[e].forEach(function(t){"rowSpan"in t||t.hasSubColumns||(t.rowSpan=n-e)})},o=0;o<n;o+=1)r(o);return t}(n)},[n]),d=s(["header","wrapper"],"thead"),u=s(["header","row"],"tr"),m=s(["header","cell"],"th");return o.createElement(d,{className:"".concat(i,"-thead")},c.map(function(e,n){return o.createElement(e4,{key:n,flattenColumns:r,cells:e,stickyOffsets:t,rowComponent:u,cellComponent:m,onHeaderRow:l,index:n})}))});var e5=n(86866);function e6(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"number"==typeof t?t:t.endsWith("%")?e*parseFloat(t)/100:null}var e7=["children"],e9=["fixed"];function te(e){return(0,e5.A)(e).filter(function(e){return o.isValidElement(e)}).map(function(e){var t=e.key,n=e.props,r=n.children,o=(0,eO.A)(n,e7),l=(0,ev.A)({key:t},o);return r&&(l.children=te(r)),l})}function tt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key";return e.filter(function(e){return e&&"object"===(0,eh.A)(e)}).reduce(function(e,n,r){var o=n.fixed,l=!0===o?"left":o,a="".concat(t,"-").concat(r),i=n.children;return i&&i.length>0?[].concat((0,e0.A)(e),(0,e0.A)(tt(i,a).map(function(e){return(0,ev.A)({fixed:l},e)}))):[].concat((0,e0.A)(e),[(0,ev.A)((0,ev.A)({key:a},n),{},{fixed:l})])},[])}let tn=function(e,t){var n=e.prefixCls,r=e.columns,l=e.children,a=e.expandable,i=e.expandedKeys,s=e.columnTitle,c=e.getRowKey,d=e.onTriggerExpand,u=e.expandIcon,m=e.rowExpandable,p=e.expandIconColumnIndex,f=e.direction,g=e.expandRowByClick,h=e.columnWidth,v=e.fixed,b=e.scrollWidth,x=e.clientWidth,y=o.useMemo(function(){return function e(t){return t.filter(function(e){return e&&"object"===(0,eh.A)(e)&&!e.hidden}).map(function(t){var n=t.children;return n&&n.length>0?(0,ev.A)((0,ev.A)({},t),{},{children:e(n)}):t})}((r||te(l)||[]).slice())},[r,l]),A=o.useMemo(function(){if(a){var e,t=y.slice();if(!t.includes(Z)){var r=p||0;r>=0&&(r||"left"===v||!v)&&t.splice(r,0,Z),"right"===v&&t.splice(y.length,0,Z)}var l=t.indexOf(Z);t=t.filter(function(e,t){return e!==Z||t===l});var f=y[l];e=v||(f?f.fixed:null);var b=(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)({},eJ,{className:"".concat(n,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",s),"fixed",e),"className","".concat(n,"-row-expand-icon-cell")),"width",h),"render",function(e,t,r){var l=c(t,r),a=u({prefixCls:n,expanded:i.has(l),expandable:!m||m(t),record:t,onExpand:d});return g?o.createElement("span",{onClick:function(e){return e.stopPropagation()}},a):a});return t.map(function(e){return e===Z?b:e})}return y.filter(function(e){return e!==Z})},[a,y,c,i,u,f]),$=o.useMemo(function(){var e=A;return t&&(e=t(e)),e.length||(e=[{render:function(){return null}}]),e},[t,A,f]),w=o.useMemo(function(){return"rtl"===f?tt($).map(function(e){var t=e.fixed,n=(0,eO.A)(e,e9),r=t;return"left"===t?r="right":"right"===t&&(r="left"),(0,ev.A)({fixed:r},n)}):tt($)},[$,f,b]),C=o.useMemo(function(){for(var e=-1,t=w.length-1;t>=0;t-=1){var n=w[t].fixed;if("left"===n||!0===n){e=t;break}}if(e>=0)for(var r=0;r<=e;r+=1){var o=w[r].fixed;if("left"!==o&&!0!==o)return!0}var l=w.findIndex(function(e){return"right"===e.fixed});if(l>=0){for(var a=l;a<w.length;a+=1)if("right"!==w[a].fixed)return!0}return!1},[w]),k=o.useMemo(function(){if(b&&b>0){var e=0,t=0;w.forEach(function(n){var r=e6(b,n.width);r?e+=r:t+=1});var n=Math.max(b,x),r=Math.max(n-e,t),o=t,l=r/t,a=0,i=w.map(function(e){var t=(0,ev.A)({},e),n=e6(b,t.width);if(n)t.width=n;else{var i=Math.floor(l);t.width=1===o?r:i,r-=i,o-=1}return a+=t.width,t});if(a<n){var s=n/a;r=n,i.forEach(function(e,t){var n=Math.floor(e.width*s);e.width=t===i.length-1?r:n,r-=n})}return[i,Math.max(a,n)]}return[w,b]},[w,b,x]),S=(0,et.A)(k,2);return[$,S[0],S[1],C]};function tr(e){var t=(0,o.useRef)(e),n=(0,o.useState)({}),r=(0,et.A)(n,2)[1],l=(0,o.useRef)(null),a=(0,o.useRef)([]);return(0,o.useEffect)(function(){return function(){l.current=null}},[]),[t.current,function(e){a.current.push(e);var n=Promise.resolve();l.current=n,n.then(function(){if(l.current===n){var e=a.current,o=t.current;a.current=[],e.forEach(function(e){t.current=e(t.current)}),l.current=null,o!==t.current&&r({})}})}]}var to=(0,n(7822).A)()?window:null;let tl=function(e){var t=e.className,n=e.children;return o.createElement("div",{className:t},n)};var ta=n(37248),ti=n(64267),ts=n(5704);function tc(e){var t=(0,ts.rb)(e).getBoundingClientRect(),n=document.documentElement;return{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}let td=o.forwardRef(function(e,t){var n,r,l=e.scrollBodyRef,a=e.onScroll,i=e.offsetScroll,s=e.container,c=e.direction,d=ei(eg,"prefixCls"),u=(null===(n=l.current)||void 0===n?void 0:n.scrollWidth)||0,m=(null===(r=l.current)||void 0===r?void 0:r.clientWidth)||0,p=u&&m/u*m,f=o.useRef(),g=tr({scrollLeft:0,isHiddenScrollBar:!0}),h=(0,et.A)(g,2),v=h[0],b=h[1],x=o.useRef({delta:0,x:0}),y=o.useState(!1),A=(0,et.A)(y,2),$=A[0],w=A[1],C=o.useRef(null);o.useEffect(function(){return function(){ti.A.cancel(C.current)}},[]);var k=function(){w(!1)},S=function(e){var t,n=(e||(null===(t=window)||void 0===t?void 0:t.event)).buttons;if(!$||0===n){$&&w(!1);return}var r=x.current.x+e.pageX-x.current.x-x.current.delta,o="rtl"===c;r=Math.max(o?p-m:0,Math.min(o?0:m-p,r)),(!o||Math.abs(r)+Math.abs(p)<m)&&(a({scrollLeft:r/m*(u+2)}),x.current.x=e.pageX)},E=function(){ti.A.cancel(C.current),C.current=(0,ti.A)(function(){if(l.current){var e=tc(l.current).top,t=e+l.current.offsetHeight,n=s===window?document.documentElement.scrollTop+window.innerHeight:tc(s).top+s.clientHeight;t-(0,eT.A)()<=n||e>=n-i?b(function(e){return(0,ev.A)((0,ev.A)({},e),{},{isHiddenScrollBar:!0})}):b(function(e){return(0,ev.A)((0,ev.A)({},e),{},{isHiddenScrollBar:!1})})}})},N=function(e){b(function(t){return(0,ev.A)((0,ev.A)({},t),{},{scrollLeft:e/u*m||0})})};return(o.useImperativeHandle(t,function(){return{setScrollLeft:N,checkScrollBarVisible:E}}),o.useEffect(function(){var e=(0,ta.A)(document.body,"mouseup",k,!1),t=(0,ta.A)(document.body,"mousemove",S,!1);return E(),function(){e.remove(),t.remove()}},[p,$]),o.useEffect(function(){if(l.current){for(var e=[],t=(0,ts.rb)(l.current);t;)e.push(t),t=t.parentElement;return e.forEach(function(e){return e.addEventListener("scroll",E,!1)}),window.addEventListener("resize",E,!1),window.addEventListener("scroll",E,!1),s.addEventListener("scroll",E,!1),function(){e.forEach(function(e){return e.removeEventListener("scroll",E)}),window.removeEventListener("resize",E),window.removeEventListener("scroll",E),s.removeEventListener("scroll",E)}}},[s]),o.useEffect(function(){v.isHiddenScrollBar||b(function(e){var t=l.current;return t?(0,ev.A)((0,ev.A)({},e),{},{scrollLeft:t.scrollLeft/t.scrollWidth*t.clientWidth}):e})},[v.isHiddenScrollBar]),u<=m||!p||v.isHiddenScrollBar)?null:o.createElement("div",{style:{height:(0,eT.A)(),width:m,bottom:i},className:"".concat(d,"-sticky-scroll")},o.createElement("div",{onMouseDown:function(e){e.persist(),x.current.delta=e.pageX-v.scrollLeft,x.current.x=0,w(!0),e.preventDefault()},ref:f,className:ey()("".concat(d,"-sticky-scroll-bar"),(0,eb.A)({},"".concat(d,"-sticky-scroll-bar-active"),$)),style:{width:"".concat(p,"px"),transform:"translate3d(".concat(v.scrollLeft,"px, 0, 0)")}}))});var tu="rc-table",tm=[],tp={};function tf(){return"No Data"}var tg=o.forwardRef(function(e,t){var n,r=(0,ev.A)({rowKey:"key",prefixCls:tu,emptyText:tf},e),l=r.prefixCls,a=r.className,i=r.rowClassName,s=r.style,c=r.data,d=r.rowKey,u=r.scroll,m=r.tableLayout,p=r.direction,f=r.title,g=r.footer,h=r.summary,b=r.caption,x=r.id,y=r.showHeader,A=r.components,$=r.emptyText,w=r.onRow,C=r.onHeaderRow,k=r.onScroll,S=r.internalHooks,E=r.transformColumns,N=r.internalRefs,j=r.tailor,I=r.getContainerWidth,O=r.sticky,z=r.rowHoverable,P=void 0===z||z,K=c||tm,R=!!K.length,M=S===ee,B=o.useCallback(function(e,t){return(0,e$.A)(A,e)||t},[A]),T=o.useMemo(function(){return"function"==typeof d?d:function(e){return e&&e[d]}},[d]),D=B(["body"]),H=(tU=o.useState(-1),tY=(tG=(0,et.A)(tU,2))[0],tJ=tG[1],tQ=o.useState(-1),t0=(tZ=(0,et.A)(tQ,2))[0],t1=tZ[1],[tY,t0,o.useCallback(function(e,t){tJ(e),t1(t)},[])]),L=(0,et.A)(H,3),_=L[0],W=L[1],F=L[2],q=(t8=(t3=r.expandable,t4=(0,eO.A)(r,eY),!1===(t2="expandable"in r?(0,ev.A)((0,ev.A)({},t4),t3):t4).showExpandColumn&&(t2.expandIconColumnIndex=-1),t2).expandIcon,t5=t2.expandedRowKeys,t6=t2.defaultExpandedRowKeys,t7=t2.defaultExpandAllRows,t9=t2.expandedRowRender,ne=t2.onExpand,nt=t2.onExpandedRowsChange,nn=t2.childrenColumnName||"children",nr=o.useMemo(function(){return t9?"row":!!(r.expandable&&r.internalHooks===ee&&r.expandable.__PARENT_RENDER_ICON__||K.some(function(e){return e&&"object"===(0,eh.A)(e)&&e[nn]}))&&"nest"},[!!t9,K]),no=o.useState(function(){if(t6)return t6;if(t7){var e;return e=[],function t(n){(n||[]).forEach(function(n,r){e.push(T(n,r)),t(n[nn])})}(K),e}return[]}),na=(nl=(0,et.A)(no,2))[0],ni=nl[1],ns=o.useMemo(function(){return new Set(t5||na||[])},[t5,na]),nc=o.useCallback(function(e){var t,n=T(e,K.indexOf(e)),r=ns.has(n);r?(ns.delete(n),t=(0,e0.A)(ns)):t=[].concat((0,e0.A)(ns),[n]),ni(t),ne&&ne(!r,e),nt&&nt(t)},[T,ns,K,ne,nt]),[t2,nr,ns,t8||eW,nn,nc]),V=(0,et.A)(q,6),X=V[0],U=V[1],G=V[2],Y=V[3],J=V[4],Q=V[5],Z=null==u?void 0:u.x,er=o.useState(0),el=(0,et.A)(er,2),ea=el[0],ei=el[1],es=tn((0,ev.A)((0,ev.A)((0,ev.A)({},r),X),{},{expandable:!!X.expandedRowRender,columnTitle:X.columnTitle,expandedKeys:G,getRowKey:T,onTriggerExpand:Q,expandIcon:Y,expandIconColumnIndex:X.expandIconColumnIndex,direction:p,scrollWidth:M&&j&&"number"==typeof Z?Z:null,clientWidth:ea}),M?E:null),ec=(0,et.A)(es,4),ed=ec[0],eu=ec[1],em=ec[2],ep=ec[3],ef=null!=em?em:Z,ex=o.useMemo(function(){return{columns:ed,flattenColumns:eu}},[ed,eu]),ew=o.useRef(),eC=o.useRef(),eS=o.useRef(),eE=o.useRef();o.useImperativeHandle(t,function(){return{nativeElement:ew.current,scrollTo:function(e){var t;if(eS.current instanceof HTMLElement){var n=e.index,r=e.top,o=e.key;if("number"!=typeof r||Number.isNaN(r)){var l,a,i=null!=o?o:T(K[n]);null===(a=eS.current.querySelector('[data-row-key="'.concat(i,'"]')))||void 0===a||a.scrollIntoView()}else null===(l=eS.current)||void 0===l||l.scrollTo({top:r})}else null!==(t=eS.current)&&void 0!==t&&t.scrollTo&&eS.current.scrollTo(e)}}});var eN=o.useRef(),eI=o.useState(!1),ez=(0,et.A)(eI,2),eH=ez[0],eL=ez[1],e_=o.useState(!1),eF=(0,et.A)(e_,2),eq=eF[0],eV=eF[1],eX=tr(new Map),eU=(0,et.A)(eX,2),eJ=eU[0],eQ=eU[1],e1=ek(eu).map(function(e){return eJ.get(e)}),e2=o.useMemo(function(){return e1},[e1.join("_")]),e4=(0,o.useMemo)(function(){var e=eu.length,t=function(e,t,n){for(var r=[],o=0,l=e;l!==t;l+=n)r.push(o),eu[l].fixed&&(o+=e2[l]||0);return r},n=t(0,e,1),r=t(e-1,-1,-1).reverse();return"rtl"===p?{left:r,right:n}:{left:n,right:r}},[e2,eu,p]),e5=u&&null!=u.y,e6=u&&null!=ef||!!X.fixed,e7=e6&&eu.some(function(e){return e.fixed}),e9=o.useRef(),te=(nm=void 0===(nu=(nd="object"===(0,eh.A)(O)?O:{}).offsetHeader)?0:nu,nf=void 0===(np=nd.offsetSummary)?0:np,nh=void 0===(ng=nd.offsetScroll)?0:ng,nb=(void 0===(nv=nd.getContainer)?function(){return to}:nv)()||to,nx=!!O,o.useMemo(function(){return{isSticky:nx,stickyClassName:nx?"".concat(l,"-sticky-holder"):"",offsetHeader:nm,offsetSummary:nf,offsetScroll:nh,container:nb}},[nx,nh,nm,nf,l,nb])),tt=te.isSticky,ta=te.offsetHeader,ti=te.offsetSummary,tc=te.offsetScroll,tg=te.stickyClassName,th=te.container,tv=o.useMemo(function(){return null==h?void 0:h(K)},[h,K]),tb=(e5||tt)&&o.isValidElement(tv)&&tv.type===eP&&tv.props.fixed;e5&&(nA={overflowY:R?"scroll":"auto",maxHeight:u.y}),e6&&(ny={overflowX:"auto"},e5||(nA={overflowY:"hidden"}),n$={width:!0===ef?"auto":ef,minWidth:"100%"});var tx=o.useCallback(function(e,t){(0,eM.A)(ew.current)&&eQ(function(n){if(n.get(e)!==t){var r=new Map(n);return r.set(e,t),r}return n})},[]),ty=function(e){var t=(0,o.useRef)(null),n=(0,o.useRef)();function r(){window.clearTimeout(n.current)}return(0,o.useEffect)(function(){return r},[]),[function(e){t.current=e,r(),n.current=window.setTimeout(function(){t.current=null,n.current=void 0},100)},function(){return t.current}]}(0),tA=(0,et.A)(ty,2),t$=tA[0],tw=tA[1];function tC(e,t){t&&("function"==typeof t?t(e):t.scrollLeft!==e&&(t.scrollLeft=e,t.scrollLeft!==e&&setTimeout(function(){t.scrollLeft=e},0)))}var tk=(0,en.A)(function(e){var t,n=e.currentTarget,r=e.scrollLeft,o="rtl"===p,l="number"==typeof r?r:n.scrollLeft,a=n||tp;tw()&&tw()!==a||(t$(a),tC(l,eC.current),tC(l,eS.current),tC(l,eN.current),tC(l,null===(t=e9.current)||void 0===t?void 0:t.setScrollLeft));var i=n||eC.current;if(i){var s=M&&j&&"number"==typeof ef?ef:i.scrollWidth,c=i.clientWidth;if(s===c){eL(!1),eV(!1);return}o?(eL(-l<s-c),eV(-l>0)):(eL(l>0),eV(l<s-c))}}),tS=(0,en.A)(function(e){tk(e),null==k||k(e)}),tE=function(){if(e6&&eS.current){var e;tk({currentTarget:(0,ts.rb)(eS.current),scrollLeft:null===(e=eS.current)||void 0===e?void 0:e.scrollLeft})}else eL(!1),eV(!1)},tN=o.useRef(!1);o.useEffect(function(){tN.current&&tE()},[e6,c,ed.length]),o.useEffect(function(){tN.current=!0},[]);var tj=o.useState(0),tI=(0,et.A)(tj,2),tO=tI[0],tz=tI[1],tP=o.useState(!0),tK=(0,et.A)(tP,2),tR=tK[0],tM=tK[1];o.useEffect(function(){j&&M||(eS.current instanceof Element?tz((0,eT.V)(eS.current).width):tz((0,eT.V)(eE.current).width)),tM((0,eB.F)("position","sticky"))},[]),o.useEffect(function(){M&&N&&(N.body.current=eS.current)});var tB=o.useCallback(function(e){return o.createElement(o.Fragment,null,o.createElement(e8,e),"top"===tb&&o.createElement(eK,e,tv))},[tb,tv]),tT=o.useCallback(function(e){return o.createElement(eK,e,tv)},[tv]),tD=B(["table"],"table"),tH=o.useMemo(function(){return m||(e7?"max-content"===ef?"auto":"fixed":e5||tt||eu.some(function(e){return e.ellipsis})?"fixed":"auto")},[e5,e7,eu,m,tt]),tL={colWidths:e2,columCount:eu.length,stickyOffsets:e4,onHeaderRow:C,fixHeader:e5,scroll:u},t_=o.useMemo(function(){return R?null:"function"==typeof $?$():$},[R,$]),tW=o.createElement(eG,{data:K,measureColumnWidth:e5||e6||tt}),tF=o.createElement(eZ,{colWidths:eu.map(function(e){return e.width}),columns:eu}),tq=null!=b?o.createElement("caption",{className:"".concat(l,"-caption")},b):void 0,tV=(0,eD.A)(r,{data:!0}),tX=(0,eD.A)(r,{aria:!0});if(e5||tt){"function"==typeof D?(nC=D(K,{scrollbarSize:tO,ref:eS,onScroll:tk}),tL.colWidths=eu.map(function(e,t){var n=e.width,r=t===eu.length-1?n-tO:n;return"number"!=typeof r||Number.isNaN(r)?0:r})):nC=o.createElement("div",{style:(0,ev.A)((0,ev.A)({},ny),nA),onScroll:tS,ref:eS,className:ey()("".concat(l,"-body"))},o.createElement(tD,(0,v.A)({style:(0,ev.A)((0,ev.A)({},n$),{},{tableLayout:tH})},tX),tq,tF,tW,!tb&&tv&&o.createElement(eK,{stickyOffsets:e4,flattenColumns:eu},tv)));var tU,tG,tY,tJ,tQ,tZ,t0,t1,t2,t3,t4,t8,t5,t6,t7,t9,ne,nt,nn,nr,no,nl,na,ni,ns,nc,nd,nu,nm,np,nf,ng,nh,nv,nb,nx,ny,nA,n$,nw,nC,nk=(0,ev.A)((0,ev.A)((0,ev.A)({noData:!K.length,maxContentScroll:e6&&"max-content"===ef},tL),ex),{},{direction:p,stickyClassName:tg,onScroll:tk});nw=o.createElement(o.Fragment,null,!1!==y&&o.createElement(e3,(0,v.A)({},nk,{stickyTopOffset:ta,className:"".concat(l,"-header"),ref:eC}),tB),nC,tb&&"top"!==tb&&o.createElement(e3,(0,v.A)({},nk,{stickyBottomOffset:ti,className:"".concat(l,"-summary"),ref:eN}),tT),tt&&eS.current&&eS.current instanceof Element&&o.createElement(td,{ref:e9,offsetScroll:tc,scrollBodyRef:eS,onScroll:tk,container:th,direction:p}))}else nw=o.createElement("div",{style:(0,ev.A)((0,ev.A)({},ny),nA),className:ey()("".concat(l,"-content")),onScroll:tk,ref:eS},o.createElement(tD,(0,v.A)({style:(0,ev.A)((0,ev.A)({},n$),{},{tableLayout:tH})},tX),tq,tF,!1!==y&&o.createElement(e8,(0,v.A)({},tL,ex)),tW,tv&&o.createElement(eK,{stickyOffsets:e4,flattenColumns:eu},tv)));var nS=o.createElement("div",(0,v.A)({className:ey()(l,a,(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)({},"".concat(l,"-rtl"),"rtl"===p),"".concat(l,"-ping-left"),eH),"".concat(l,"-ping-right"),eq),"".concat(l,"-layout-fixed"),"fixed"===m),"".concat(l,"-fixed-header"),e5),"".concat(l,"-fixed-column"),e7),"".concat(l,"-fixed-column-gapped"),e7&&ep),"".concat(l,"-scroll-horizontal"),e6),"".concat(l,"-has-fix-left"),eu[0]&&eu[0].fixed),"".concat(l,"-has-fix-right"),eu[eu.length-1]&&"right"===eu[eu.length-1].fixed)),style:s,id:x,ref:ew},tV),f&&o.createElement(tl,{className:"".concat(l,"-title")},f(K)),o.createElement("div",{ref:eE,className:"".concat(l,"-container")},nw),g&&o.createElement(tl,{className:"".concat(l,"-footer")},g(K)));e6&&(nS=o.createElement(eR.A,{onResize:function(e){var t,n=e.width;null===(t=e9.current)||void 0===t||t.checkScrollBarVisible();var r=ew.current?ew.current.offsetWidth:n;M&&I&&ew.current&&(r=I(ew.current,r)||r),r!==ea&&(tE(),ei(r))}},nS));var nE=(n=eu.map(function(e,t){return ej(t,t,eu,e4,p)}),(0,eA.A)(function(){return n},[n],function(e,t){return!(0,eo.A)(e,t)})),nN=o.useMemo(function(){return{scrollX:ef,prefixCls:l,getComponent:B,scrollbarSize:tO,direction:p,fixedInfoList:nE,isSticky:tt,supportSticky:tR,componentWidth:ea,fixHeader:e5,fixColumn:e7,horizonScroll:e6,tableLayout:tH,rowClassName:i,expandedRowClassName:X.expandedRowClassName,expandIcon:Y,expandableType:U,expandRowByClick:X.expandRowByClick,expandedRowRender:X.expandedRowRender,onTriggerExpand:Q,expandIconColumnIndex:X.expandIconColumnIndex,indentSize:X.indentSize,allColumnsFixedLeft:eu.every(function(e){return"left"===e.fixed}),emptyNode:t_,columns:ed,flattenColumns:eu,onColumnResize:tx,hoverStartRow:_,hoverEndRow:W,onHover:F,rowExpandable:X.rowExpandable,onRow:w,getRowKey:T,expandedKeys:G,childrenColumnName:J,rowHoverable:P}},[ef,l,B,tO,p,nE,tt,tR,ea,e5,e7,e6,tH,i,X.expandedRowClassName,Y,U,X.expandRowByClick,X.expandedRowRender,Q,X.expandIconColumnIndex,X.indentSize,t_,ed,eu,tx,_,W,F,X.rowExpandable,w,T,G,J,P]);return o.createElement(eg.Provider,{value:nN},nS)}),th=em(tg,void 0);th.EXPAND_COLUMN=Z,th.INTERNAL_HOOKS=ee,th.Column=function(e){return null},th.ColumnGroup=function(e){return null},th.Summary=eP;var tv=n(94456),tb=ea(null),tx=ea(null);let ty=function(e){var t,n=e.rowInfo,r=e.column,l=e.colIndex,a=e.indent,i=e.index,s=e.component,c=e.renderIndex,d=e.record,u=e.style,m=e.className,p=e.inverse,f=e.getHeight,g=r.render,h=r.dataIndex,b=r.className,x=r.width,y=ei(tx,["columnsOffset"]).columnsOffset,A=eq(n,r,l,a,i),$=A.key,w=A.fixedInfo,C=A.appendCellNode,k=A.additionalCellProps,S=k.style,E=k.colSpan,N=void 0===E?1:E,j=k.rowSpan,I=void 0===j?1:j,O=y[(t=l-1)+(N||1)]-(y[t]||0),z=(0,ev.A)((0,ev.A)((0,ev.A)({},S),u),{},{flex:"0 0 ".concat(O,"px"),width:"".concat(O,"px"),marginRight:N>1?x-O:0,pointerEvents:"auto"}),P=o.useMemo(function(){return p?I<=1:0===N||0===I||I>1},[I,N,p]);P?z.visibility="hidden":p&&(z.height=null==f?void 0:f(I));var K={};return(0===I||0===N)&&(K.rowSpan=1,K.colSpan=1),o.createElement(eN,(0,v.A)({className:ey()(b,m),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:s,prefixCls:n.prefixCls,key:$,record:d,index:i,renderIndex:c,dataIndex:h,render:P?function(){return null}:g,shouldCellUpdate:r.shouldCellUpdate},w,{appendNode:C,additionalProps:(0,ev.A)((0,ev.A)({},k),{},{style:z},K)}))};var tA=["data","index","className","rowKey","style","extra","getHeight"],t$=ep(o.forwardRef(function(e,t){var n,r=e.data,l=e.index,a=e.className,i=e.rowKey,s=e.style,c=e.extra,d=e.getHeight,u=(0,eO.A)(e,tA),m=r.record,p=r.indent,f=r.index,g=ei(eg,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),h=g.scrollX,b=g.flattenColumns,x=g.prefixCls,y=g.fixColumn,A=g.componentWidth,$=ei(tb,["getComponent"]).getComponent,w=eL(m,i,l,p),C=$(["body","row"],"div"),k=$(["body","cell"],"div"),S=w.rowSupportExpand,E=w.expanded,N=w.rowProps,j=w.expandedRowRender,I=w.expandedRowClassName;if(S&&E){var O=j(m,l,p+1,E),z=eF(I,m,l,p),P={};y&&(P={style:(0,eb.A)({},"--virtual-width","".concat(A,"px"))});var K="".concat(x,"-expanded-row-cell");n=o.createElement(C,{className:ey()("".concat(x,"-expanded-row"),"".concat(x,"-expanded-row-level-").concat(p+1),z)},o.createElement(eN,{component:k,prefixCls:x,className:ey()(K,(0,eb.A)({},"".concat(K,"-fixed"),y)),additionalProps:P},O))}var R=(0,ev.A)((0,ev.A)({},s),{},{width:h});c&&(R.position="absolute",R.pointerEvents="none");var M=o.createElement(C,(0,v.A)({},N,u,{"data-row-key":i,ref:S?null:t,className:ey()(a,"".concat(x,"-row"),null==N?void 0:N.className,(0,eb.A)({},"".concat(x,"-row-extra"),c)),style:(0,ev.A)((0,ev.A)({},R),null==N?void 0:N.style)}),b.map(function(e,t){return o.createElement(ty,{key:t,component:k,rowInfo:w,column:e,colIndex:t,indent:p,index:l,renderIndex:f,record:m,inverse:c,getHeight:d})}));return S?o.createElement("div",{ref:t},M,n):M})),tw=ep(o.forwardRef(function(e,t){var n=e.data,r=e.onScroll,l=ei(eg,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),a=l.flattenColumns,i=l.onColumnResize,s=l.getRowKey,c=l.expandedKeys,d=l.prefixCls,u=l.childrenColumnName,m=l.scrollX,p=l.direction,f=ei(tb),g=f.sticky,h=f.scrollY,v=f.listItemHeight,b=f.getComponent,x=f.onScroll,y=o.useRef(),A=eH(n,u,c,s),$=o.useMemo(function(){var e=0;return a.map(function(t){var n=t.width,r=t.key;return e+=n,[r,n,e]})},[a]),w=o.useMemo(function(){return $.map(function(e){return e[2]})},[$]);o.useEffect(function(){$.forEach(function(e){var t=(0,et.A)(e,2);i(t[0],t[1])})},[$]),o.useImperativeHandle(t,function(){var e,t={scrollTo:function(e){var t;null===(t=y.current)||void 0===t||t.scrollTo(e)},nativeElement:null===(e=y.current)||void 0===e?void 0:e.nativeElement};return Object.defineProperty(t,"scrollLeft",{get:function(){var e;return(null===(e=y.current)||void 0===e?void 0:e.getScrollInfo().x)||0},set:function(e){var t;null===(t=y.current)||void 0===t||t.scrollTo({left:e})}}),t});var C=function(e,t){var n=null===(o=A[t])||void 0===o?void 0:o.record,r=e.onCell;if(r){var o,l,a=r(n,t);return null!==(l=null==a?void 0:a.rowSpan)&&void 0!==l?l:1}return 1},k=o.useMemo(function(){return{columnsOffset:w}},[w]),S="".concat(d,"-tbody"),E=b(["body","wrapper"]),N={};return g&&(N.position="sticky",N.bottom=0,"object"===(0,eh.A)(g)&&g.offsetScroll&&(N.bottom=g.offsetScroll)),o.createElement(tx.Provider,{value:k},o.createElement(tv.A,{fullHeight:!1,ref:y,prefixCls:"".concat(S,"-virtual"),styles:{horizontalScrollBar:N},className:S,height:h,itemHeight:v||24,data:A,itemKey:function(e){return s(e.record)},component:E,scrollWidth:m,direction:p,onVirtualScroll:function(e){var t,n=e.x;r({currentTarget:null===(t=y.current)||void 0===t?void 0:t.nativeElement,scrollLeft:n})},onScroll:x,extraRender:function(e){var t=e.start,n=e.end,r=e.getSize,l=e.offsetY;if(n<0)return null;for(var i=a.filter(function(e){return 0===C(e,t)}),c=t,d=function(e){if(!(i=i.filter(function(t){return 0===C(t,e)})).length)return c=e,1},u=t;u>=0&&!d(u);u-=1);for(var m=a.filter(function(e){return 1!==C(e,n)}),p=n,f=function(e){if(!(m=m.filter(function(t){return 1!==C(t,e)})).length)return p=Math.max(e-1,n),1},g=n;g<A.length&&!f(g);g+=1);for(var h=[],v=function(e){if(!A[e])return 1;a.some(function(t){return C(t,e)>1})&&h.push(e)},b=c;b<=p;b+=1)if(v(b))continue;return h.map(function(e){var t=A[e],n=s(t.record,e),a=r(n);return o.createElement(t$,{key:e,data:t,rowKey:n,index:e,style:{top:-l+a.top},extra:!0,getHeight:function(t){var o=e+t-1,l=r(n,s(A[o].record,o));return l.bottom-l.top}})})}},function(e,t,n){var r=s(e.record,t);return o.createElement(t$,{data:e,rowKey:r,index:t,style:n.style})}))})),tC=function(e,t){var n=t.ref,r=t.onScroll;return o.createElement(tw,{ref:n,data:e,onScroll:r})},tk=o.forwardRef(function(e,t){var n=e.data,r=e.columns,l=e.scroll,a=e.sticky,i=e.prefixCls,s=void 0===i?tu:i,c=e.className,d=e.listItemHeight,u=e.components,m=e.onScroll,p=l||{},f=p.x,g=p.y;"number"!=typeof f&&(f=1),"number"!=typeof g&&(g=500);var h=(0,eS._q)(function(e,t){return(0,e$.A)(u,e)||t}),b=(0,eS._q)(m),x=o.useMemo(function(){return{sticky:a,scrollY:g,listItemHeight:d,getComponent:h,onScroll:b}},[a,g,d,h,b]);return o.createElement(tb.Provider,{value:x},o.createElement(th,(0,v.A)({},e,{className:ey()(c,"".concat(s,"-virtual")),scroll:(0,ev.A)((0,ev.A)({},l),{},{x:f}),components:(0,ev.A)((0,ev.A)({},u),{},{body:null!=n&&n.length?tC:void 0}),columns:r,internalHooks:ee,tailor:!0,ref:t})))});em(tk,void 0);var tS=n(77953),tE=o.createContext(null),tN=o.createContext({});let tj=o.memo(function(e){for(var t=e.prefixCls,n=e.level,r=e.isStart,l=e.isEnd,a="".concat(t,"-indent-unit"),i=[],s=0;s<n;s+=1)i.push(o.createElement("span",{key:s,className:ey()(a,(0,eb.A)((0,eb.A)({},"".concat(a,"-start"),r[s]),"".concat(a,"-end"),l[s]))}));return o.createElement("span",{"aria-hidden":"true",className:"".concat(t,"-indent")},i)});var tI=n(55681),tO=["children"];function tz(e,t){return"".concat(e,"-").concat(t)}function tP(e,t){return null!=e?e:t}function tK(e){var t=e||{},n=t.title,r=t._title,o=t.key,l=t.children,a=n||"title";return{title:a,_title:r||[a],key:o||"key",children:l||"children"}}function tR(e){return function e(t){return(0,e5.A)(t).map(function(t){if(!(t&&t.type&&t.type.isTreeNode))return(0,ew.Ay)(!t,"Tree/TreeNode can only accept TreeNode as children."),null;var n=t.key,r=t.props,o=r.children,l=(0,eO.A)(r,tO),a=(0,ev.A)({key:n},l),i=e(o);return i.length&&(a.children=i),a}).filter(function(e){return e})}(e)}function tM(e,t,n){var r=tK(n),o=r._title,l=r.key,a=r.children,i=new Set(!0===t?[]:t),s=[];return function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return n.map(function(c,d){for(var u,m=tz(r?r.pos:"0",d),p=tP(c[l],m),f=0;f<o.length;f+=1){var g=o[f];if(void 0!==c[g]){u=c[g];break}}var h=Object.assign((0,tI.A)(c,[].concat((0,e0.A)(o),[l,a])),{title:u,key:p,parent:r,pos:m,children:null,data:c,isStart:[].concat((0,e0.A)(r?r.isStart:[]),[0===d]),isEnd:[].concat((0,e0.A)(r?r.isEnd:[]),[d===n.length-1])});return s.push(h),!0===t||i.has(p)?h.children=e(c[a]||[],h):h.children=[],h})}(e),s}function tB(e){var t,n,r,o,l,a,i,s,c,d,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},m=u.initWrapper,p=u.processEntity,f=u.onProcessFinished,g=u.externalGetKey,h=u.childrenPropName,v=u.fieldNames,b=arguments.length>2?arguments[2]:void 0,x={},y={},A={posEntities:x,keyEntities:y};return m&&(A=m(A)||A),t=function(e){var t=e.node,n=e.index,r=e.pos,o=e.key,l=e.parentPos,a=e.level,i={node:t,nodes:e.nodes,index:n,key:o,pos:r,level:a},s=tP(o,r);x[r]=i,y[s]=i,i.parent=x[l],i.parent&&(i.parent.children=i.parent.children||[],i.parent.children.push(i)),p&&p(i,A)},n={externalGetKey:g||b,childrenPropName:h,fieldNames:v},l=(o=("object"===(0,eh.A)(n)?n:{externalGetKey:n})||{}).childrenPropName,a=o.externalGetKey,s=(i=tK(o.fieldNames)).key,c=i.children,d=l||c,a?"string"==typeof a?r=function(e){return e[a]}:"function"==typeof a&&(r=function(e){return a(e)}):r=function(e,t){return tP(e[s],t)},function n(o,l,a,i){var s=o?o[d]:e,c=o?tz(a.pos,l):"0",u=o?[].concat((0,e0.A)(i),[o]):[];if(o){var m=r(o,c);t({node:o,index:l,pos:c,key:m,parentPos:a.node?a.pos:null,level:a.level+1,nodes:u})}s&&s.forEach(function(e,t){n(e,t,{node:o,pos:c,level:a?a.level+1:-1},u)})}(null),f&&f(A),A}function tT(e,t){var n=t.expandedKeys,r=t.selectedKeys,o=t.loadedKeys,l=t.loadingKeys,a=t.checkedKeys,i=t.halfCheckedKeys,s=t.dragOverNodeKey,c=t.dropPosition,d=t.keyEntities[e];return{eventKey:e,expanded:-1!==n.indexOf(e),selected:-1!==r.indexOf(e),loaded:-1!==o.indexOf(e),loading:-1!==l.indexOf(e),checked:-1!==a.indexOf(e),halfChecked:-1!==i.indexOf(e),pos:String(d?d.pos:""),dragOver:s===e&&0===c,dragOverGapTop:s===e&&-1===c,dragOverGapBottom:s===e&&1===c}}function tD(e){var t=e.data,n=e.expanded,r=e.selected,o=e.checked,l=e.loaded,a=e.loading,i=e.halfChecked,s=e.dragOver,c=e.dragOverGapTop,d=e.dragOverGapBottom,u=e.pos,m=e.active,p=e.eventKey,f=(0,ev.A)((0,ev.A)({},t),{},{expanded:n,selected:r,checked:o,loaded:l,loading:a,halfChecked:i,dragOver:s,dragOverGapTop:c,dragOverGapBottom:d,pos:u,active:m,key:p});return"props"in f||Object.defineProperty(f,"props",{get:function(){return(0,ew.Ay)(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),f}var tH=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],tL="open",t_="close",tW=function(e){var t,n,r,o=e.eventKey,a=e.className,i=e.style,s=e.dragOver,c=e.dragOverGapTop,d=e.dragOverGapBottom,u=e.isLeaf,m=e.isStart,p=e.isEnd,f=e.expanded,g=e.selected,h=e.checked,b=e.halfChecked,x=e.loading,y=e.domRef,A=e.active,$=e.data,w=e.onMouseMove,C=e.selectable,k=(0,eO.A)(e,tH),S=l().useContext(tE),E=l().useContext(tN),N=l().useRef(null),j=l().useState(!1),I=(0,et.A)(j,2),O=I[0],z=I[1],P=!!(S.disabled||e.disabled||null!==(t=E.nodeDisabled)&&void 0!==t&&t.call(E,$)),K=l().useMemo(function(){return!!S.checkable&&!1!==e.checkable&&S.checkable},[S.checkable,e.checkable]),R=function(t){P||S.onNodeSelect(t,tD(e))},M=function(t){P||!K||e.disableCheckbox||S.onNodeCheck(t,tD(e),!h)},B=l().useMemo(function(){return"boolean"==typeof C?C:S.selectable},[C,S.selectable]),T=function(t){S.onNodeClick(t,tD(e)),B?R(t):M(t)},D=function(t){S.onNodeDoubleClick(t,tD(e))},H=function(t){S.onNodeMouseEnter(t,tD(e))},L=function(t){S.onNodeMouseLeave(t,tD(e))},_=function(t){S.onNodeContextMenu(t,tD(e))},W=l().useMemo(function(){return!!(S.draggable&&(!S.draggable.nodeDraggable||S.draggable.nodeDraggable($)))},[S.draggable,$]),F=function(t){x||S.onNodeExpand(t,tD(e))},q=l().useMemo(function(){return!!((S.keyEntities[o]||{}).children||[]).length},[S.keyEntities,o]),V=l().useMemo(function(){return!1!==u&&(u||!S.loadData&&!q||S.loadData&&e.loaded&&!q)},[u,S.loadData,q,e.loaded]);l().useEffect(function(){!x&&("function"!=typeof S.loadData||!f||V||e.loaded||S.onNodeLoad(tD(e)))},[x,S.loadData,S.onNodeLoad,f,V,e]);var X=l().useMemo(function(){var e;return null!==(e=S.draggable)&&void 0!==e&&e.icon?l().createElement("span",{className:"".concat(S.prefixCls,"-draggable-icon")},S.draggable.icon):null},[S.draggable]),U=function(t){var n=e.switcherIcon||S.switcherIcon;return"function"==typeof n?n((0,ev.A)((0,ev.A)({},e),{},{isLeaf:t})):n},G=l().useMemo(function(){if(!K)return null;var t="boolean"!=typeof K?K:null;return l().createElement("span",{className:ey()("".concat(S.prefixCls,"-checkbox"),(0,eb.A)((0,eb.A)((0,eb.A)({},"".concat(S.prefixCls,"-checkbox-checked"),h),"".concat(S.prefixCls,"-checkbox-indeterminate"),!h&&b),"".concat(S.prefixCls,"-checkbox-disabled"),P||e.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":b?"mixed":h,"aria-disabled":P||e.disableCheckbox,"aria-label":"Select ".concat("string"==typeof e.title?e.title:"tree node")},t)},[K,h,b,P,e.disableCheckbox,e.title]),Y=l().useMemo(function(){return V?null:f?tL:t_},[V,f]),J=l().useMemo(function(){return l().createElement("span",{className:ey()("".concat(S.prefixCls,"-iconEle"),"".concat(S.prefixCls,"-icon__").concat(Y||"docu"),(0,eb.A)({},"".concat(S.prefixCls,"-icon_loading"),x))})},[S.prefixCls,Y,x]),Q=l().useMemo(function(){var t=!!S.draggable;return!e.disabled&&t&&S.dragOverNodeKey===o?S.dropIndicatorRender({dropPosition:S.dropPosition,dropLevelOffset:S.dropLevelOffset,indent:S.indent,prefixCls:S.prefixCls,direction:S.direction}):null},[S.dropPosition,S.dropLevelOffset,S.indent,S.prefixCls,S.direction,S.draggable,S.dragOverNodeKey,S.dropIndicatorRender]),Z=l().useMemo(function(){var t,n,r=e.title,o=void 0===r?"---":r,a="".concat(S.prefixCls,"-node-content-wrapper");if(S.showIcon){var i=e.icon||S.icon;t=i?l().createElement("span",{className:ey()("".concat(S.prefixCls,"-iconEle"),"".concat(S.prefixCls,"-icon__customize"))},"function"==typeof i?i(e):i):J}else S.loadData&&x&&(t=J);return n="function"==typeof o?o($):S.titleRender?S.titleRender($):o,l().createElement("span",{ref:N,title:"string"==typeof o?o:"",className:ey()(a,"".concat(a,"-").concat(Y||"normal"),(0,eb.A)({},"".concat(S.prefixCls,"-node-selected"),!P&&(g||O))),onMouseEnter:H,onMouseLeave:L,onContextMenu:_,onClick:T,onDoubleClick:D},t,l().createElement("span",{className:"".concat(S.prefixCls,"-title")},n),Q)},[S.prefixCls,S.showIcon,e,S.icon,J,S.titleRender,$,Y,H,L,_,T,D]),ee=(0,eD.A)(k,{aria:!0,data:!0}),en=(S.keyEntities[o]||{}).level,er=p[p.length-1],eo=!P&&W,el=S.draggingNodeKey===o;return l().createElement("div",(0,v.A)({ref:y,role:"treeitem","aria-expanded":u?void 0:f,className:ey()(a,"".concat(S.prefixCls,"-treenode"),(r={},(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)(r,"".concat(S.prefixCls,"-treenode-disabled"),P),"".concat(S.prefixCls,"-treenode-switcher-").concat(f?"open":"close"),!u),"".concat(S.prefixCls,"-treenode-checkbox-checked"),h),"".concat(S.prefixCls,"-treenode-checkbox-indeterminate"),b),"".concat(S.prefixCls,"-treenode-selected"),g),"".concat(S.prefixCls,"-treenode-loading"),x),"".concat(S.prefixCls,"-treenode-active"),A),"".concat(S.prefixCls,"-treenode-leaf-last"),er),"".concat(S.prefixCls,"-treenode-draggable"),W),"dragging",el),(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)(r,"drop-target",S.dropTargetKey===o),"drop-container",S.dropContainerKey===o),"drag-over",!P&&s),"drag-over-gap-top",!P&&c),"drag-over-gap-bottom",!P&&d),"filter-node",null===(n=S.filterTreeNode)||void 0===n?void 0:n.call(S,tD(e))),"".concat(S.prefixCls,"-treenode-leaf"),V))),style:i,draggable:eo,onDragStart:eo?function(t){t.stopPropagation(),z(!0),S.onNodeDragStart(t,e);try{t.dataTransfer.setData("text/plain","")}catch(e){}}:void 0,onDragEnter:W?function(t){t.preventDefault(),t.stopPropagation(),S.onNodeDragEnter(t,e)}:void 0,onDragOver:W?function(t){t.preventDefault(),t.stopPropagation(),S.onNodeDragOver(t,e)}:void 0,onDragLeave:W?function(t){t.stopPropagation(),S.onNodeDragLeave(t,e)}:void 0,onDrop:W?function(t){t.preventDefault(),t.stopPropagation(),z(!1),S.onNodeDrop(t,e)}:void 0,onDragEnd:W?function(t){t.stopPropagation(),z(!1),S.onNodeDragEnd(t,e)}:void 0,onMouseMove:w},void 0!==C?{"aria-selected":!!C}:void 0,ee),l().createElement(tj,{prefixCls:S.prefixCls,level:en,isStart:m,isEnd:p}),X,function(){if(V){var e=U(!0);return!1!==e?l().createElement("span",{className:ey()("".concat(S.prefixCls,"-switcher"),"".concat(S.prefixCls,"-switcher-noop"))},e):null}var t=U(!1);return!1!==t?l().createElement("span",{onClick:F,className:ey()("".concat(S.prefixCls,"-switcher"),"".concat(S.prefixCls,"-switcher_").concat(f?tL:t_))},t):null}(),G,Z)};function tF(e,t){if(!e)return[];var n=e.slice(),r=n.indexOf(t);return r>=0&&n.splice(r,1),n}function tq(e,t){var n=(e||[]).slice();return -1===n.indexOf(t)&&n.push(t),n}function tV(e){return e.split("-")}function tX(e,t,n,r,o,l,a,i,s,c){var d,u,m=e.clientX,p=e.clientY,f=e.target.getBoundingClientRect(),g=f.top,h=f.height,v=(("rtl"===c?-1:1)*(((null==o?void 0:o.x)||0)-m)-12)/r,b=s.filter(function(e){var t;return null===(t=i[e])||void 0===t||null===(t=t.children)||void 0===t?void 0:t.length}),x=i[n.eventKey];if(p<g+h/2){var y=a.findIndex(function(e){return e.key===x.key});x=i[a[y<=0?0:y-1].key]}var A=x.key,$=x,w=x.key,C=0,k=0;if(!b.includes(A))for(var S=0;S<v;S+=1)if(function(e){if(e.parent){var t=tV(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}(x))x=x.parent,k+=1;else break;var E=t.data,N=x.node,j=!0;return 0===Number((d=tV(x.pos))[d.length-1])&&0===x.level&&p<g+h/2&&l({dragNode:E,dropNode:N,dropPosition:-1})&&x.key===n.eventKey?C=-1:($.children||[]).length&&b.includes(w)?l({dragNode:E,dropNode:N,dropPosition:0})?C=0:j=!1:0===k?v>-1.5?l({dragNode:E,dropNode:N,dropPosition:1})?C=1:j=!1:l({dragNode:E,dropNode:N,dropPosition:0})?C=0:l({dragNode:E,dropNode:N,dropPosition:1})?C=1:j=!1:l({dragNode:E,dropNode:N,dropPosition:1})?C=1:j=!1,{dropPosition:C,dropLevelOffset:k,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:w,dropContainerKey:0===C?null:(null===(u=x.parent)||void 0===u?void 0:u.key)||null,dropAllowed:j}}function tU(e,t){if(e)return t.multiple?e.slice():e.length?[e[0]]:e}function tG(e){var t;if(!e)return null;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else{if("object"!==(0,eh.A)(e))return(0,ew.Ay)(!1,"`checkedKeys` is not an array or an object"),null;t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0}}return t}function tY(e,t){var n=new Set;return(e||[]).forEach(function(e){!function e(r){if(!n.has(r)){var o=t[r];if(o){n.add(r);var l=o.parent;!o.node.disabled&&l&&e(l.key)}}}(e)}),(0,e0.A)(n)}function tJ(e,t){var n=new Set;return e.forEach(function(e){t.has(e)||n.add(e)}),n}function tQ(e){var t=e||{},n=t.disabled,r=t.disableCheckbox,o=t.checkable;return!!(n||r)||!1===o}function tZ(e,t,n,r){var o,l=[];o=r||tQ;var a=new Set(e.filter(function(e){var t=!!n[e];return t||l.push(e),t})),i=new Map,s=0;return Object.keys(n).forEach(function(e){var t=n[e],r=t.level,o=i.get(r);o||(o=new Set,i.set(r,o)),o.add(t),s=Math.max(s,r)}),(0,ew.Ay)(!l.length,"Tree missing follow keys: ".concat(l.slice(0,100).map(function(e){return"'".concat(e,"'")}).join(", "))),!0===t?function(e,t,n,r){for(var o=new Set(e),l=new Set,a=0;a<=n;a+=1)(t.get(a)||new Set).forEach(function(e){var t=e.key,n=e.node,l=e.children,a=void 0===l?[]:l;o.has(t)&&!r(n)&&a.filter(function(e){return!r(e.node)}).forEach(function(e){o.add(e.key)})});for(var i=new Set,s=n;s>=0;s-=1)(t.get(s)||new Set).forEach(function(e){var t=e.parent;if(!(r(e.node)||!e.parent||i.has(e.parent.key))){if(r(e.parent.node)){i.add(t.key);return}var n=!0,a=!1;(t.children||[]).filter(function(e){return!r(e.node)}).forEach(function(e){var t=e.key,r=o.has(t);n&&!r&&(n=!1),!a&&(r||l.has(t))&&(a=!0)}),n&&o.add(t.key),a&&l.add(t.key),i.add(t.key)}});return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(tJ(l,o))}}(a,i,s,o):function(e,t,n,r,o){for(var l=new Set(e),a=new Set(t),i=0;i<=r;i+=1)(n.get(i)||new Set).forEach(function(e){var t=e.key,n=e.node,r=e.children,i=void 0===r?[]:r;l.has(t)||a.has(t)||o(n)||i.filter(function(e){return!o(e.node)}).forEach(function(e){l.delete(e.key)})});a=new Set;for(var s=new Set,c=r;c>=0;c-=1)(n.get(c)||new Set).forEach(function(e){var t=e.parent;if(!(o(e.node)||!e.parent||s.has(e.parent.key))){if(o(e.parent.node)){s.add(t.key);return}var n=!0,r=!1;(t.children||[]).filter(function(e){return!o(e.node)}).forEach(function(e){var t=e.key,o=l.has(t);n&&!o&&(n=!1),!r&&(o||a.has(t))&&(r=!0)}),n||l.delete(t.key),r&&a.add(t.key),s.add(t.key)}});return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(tJ(a,l))}}(a,t.halfCheckedKeys,i,s,o)}tW.isTreeNode=1;var t0=n(61849),t1=n(22505),t2=n(6394),t3=n(78371);let t4=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var t8=n(44805),t5=n(80349),t6=n(2866),t7=n(26948),t9=n(27343),ne=n(90334),nt=n(84458);let nn=o.createContext({});var nr=n(38299),no=n(46219);let nl=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var na=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ni=e=>{let{prefixCls:t,className:n,dashed:r}=e,l=na(e,["prefixCls","className","dashed"]),{getPrefixCls:a}=o.useContext(t9.QO),i=a("menu",t),s=ey()({[`${i}-item-divider-dashed`]:!!r},n);return o.createElement(nt.cG,Object.assign({className:s},l))},ns=e=>{var t;let{className:n,children:r,icon:l,title:a,danger:i,extra:s}=e,{prefixCls:c,firstLevel:d,direction:u,disableMenuItemTitleTooltip:m,inlineCollapsed:p}=o.useContext(nl),{siderCollapsed:g}=o.useContext(nn),h=a;void 0===a?h=d?r:"":!1===a&&(h="");let v={title:h};g||p||(v.title=null,v.open=!1);let b=(0,e5.A)(r).length,x=o.createElement(nt.q7,Object.assign({},(0,tI.A)(e,["title","icon","danger"]),{className:ey()({[`${c}-item-danger`]:i,[`${c}-item-only-child`]:(l?b+1:b)===1},n),title:"string"==typeof a?a:void 0}),(0,t6.Ob)(l,{className:ey()(o.isValidElement(l)?null===(t=l.props)||void 0===t?void 0:t.className:"",`${c}-item-icon`)}),(e=>{let t=null==r?void 0:r[0],n=o.createElement("span",{className:ey()(`${c}-title-content`,{[`${c}-title-content-with-extra`]:!!s||0===s})},r);return(!l||o.isValidElement(r)&&"span"===r.type)&&r&&e&&d&&"string"==typeof t?o.createElement("div",{className:`${c}-inline-collapsed-noicon`},t.charAt(0)):n})(p));return m||(x=o.createElement(f.A,Object.assign({},v,{placement:"rtl"===u?"left":"right",classNames:{root:`${c}-inline-collapsed-tooltip`}}),x)),x};var nc=n(93629),nd=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let nu=o.createContext(null),nm=o.forwardRef((e,t)=>{let{children:n}=e,r=nd(e,["children"]),l=o.useContext(nu),a=o.useMemo(()=>Object.assign(Object.assign({},l),r),[l,r.prefixCls,r.mode,r.selectable,r.rootClassName]),i=(0,es.H3)(n),s=(0,es.xK)(t,i?(0,es.A9)(n):null);return o.createElement(nu.Provider,{value:a},o.createElement(nc.A,{space:!0},i?o.cloneElement(n,{ref:s}):n))});var np=n(1439),nf=n(43891),ng=n(47285),nh=n(19117),nv=n(36485),nb=n(66801),nx=n(13662),ny=n(10941);let nA=e=>{let{componentCls:t,motionDurationSlow:n,horizontalLineHeight:r,colorSplit:o,lineWidth:l,lineType:a,itemPaddingInline:i}=e;return{[`${t}-horizontal`]:{lineHeight:r,border:0,borderBottom:`${(0,np.zA)(l)} ${a} ${o}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:`border-color ${n},background ${n}`},[`${t}-submenu-arrow`]:{display:"none"}}}},n$=e=>{let{componentCls:t,menuArrowOffset:n,calc:r}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,
    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,np.zA)(r(n).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,np.zA)(n)})`}}}}},nw=e=>Object.assign({},(0,ng.jk)(e)),nC=(e,t)=>{let{componentCls:n,itemColor:r,itemSelectedColor:o,subMenuItemSelectedColor:l,groupTitleColor:a,itemBg:i,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:d,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:f,motionEaseOut:g,itemPaddingInline:h,motionDurationMid:v,itemHoverColor:b,lineType:x,colorSplit:y,itemDisabledColor:A,dangerItemColor:$,dangerItemHoverColor:w,dangerItemSelectedColor:C,dangerItemActiveBg:k,dangerItemSelectedBg:S,popupBg:E,itemHoverBg:N,itemActiveBg:j,menuSubMenuBg:I,horizontalItemSelectedColor:O,horizontalItemSelectedBg:z,horizontalItemBorderRadius:P,horizontalItemHoverBg:K}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:r,background:i,[`&${n}-root:focus-visible`]:Object.assign({},nw(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:a}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:l},[`${n}-item, ${n}-submenu-title`]:{color:r,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},nw(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${A} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:b}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:N},"&:active":{backgroundColor:j}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:N},"&:active":{backgroundColor:j}}},[`${n}-item-danger`]:{color:$,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:w}},[`&${n}-item:active`]:{background:k}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:o,[`&${n}-item-danger`]:{color:C},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:S}},[`&${n}-submenu > ${n}`]:{backgroundColor:I},[`&${n}-popup > ${n}`]:{backgroundColor:E},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:E},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:P,"&::after":{position:"absolute",insetInline:h,bottom:0,borderBottom:`${(0,np.zA)(d)} solid transparent`,transition:`border-color ${p} ${f}`,content:'""'},"&:hover, &-active, &-open":{background:K,"&::after":{borderBottomWidth:d,borderBottomColor:O}},"&-selected":{color:O,backgroundColor:z,"&:hover":{backgroundColor:z},"&::after":{borderBottomWidth:d,borderBottomColor:O}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${(0,np.zA)(m)} ${x} ${y}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:s},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,np.zA)(u)} solid ${o}`,transform:"scaleY(0.0001)",opacity:0,transition:`transform ${v} ${g},opacity ${v} ${g}`,content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:C}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:`transform ${v} ${f},opacity ${v} ${f}`}}}}}},nk=e=>{let{componentCls:t,itemHeight:n,itemMarginInline:r,padding:o,menuArrowSize:l,marginXS:a,itemMarginBlock:i,itemWidth:s,itemPaddingInline:c}=e,d=e.calc(l).add(o).add(a).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:(0,np.zA)(n),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:r,marginBlock:i,width:s},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:(0,np.zA)(n)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:d}}},nS=e=>{let{componentCls:t,iconCls:n,itemHeight:r,colorTextLightSolid:o,dropdownWidth:l,controlHeightLG:a,motionEaseOut:i,paddingXL:s,itemMarginInline:c,fontSizeLG:d,motionDurationFast:u,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:f,collapsedWidth:g,collapsedIconSize:h}=e,v={height:r,lineHeight:(0,np.zA)(r),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},nk(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},nk(e)),{boxShadow:f})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:l,maxHeight:`calc(100vh - ${(0,np.zA)(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:`border-color ${m},background ${m},padding ${u} ${i}`,[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:v,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:v}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:d,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,np.zA)(e.calc(h).div(2).equal())} - ${(0,np.zA)(c)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:h,lineHeight:(0,np.zA)(r),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:o}},[`${t}-item-group-title`]:Object.assign(Object.assign({},ng.L9),{paddingInline:p})}}]},nE=e=>{let{componentCls:t,motionDurationSlow:n,motionDurationMid:r,motionEaseInOut:o,motionEaseOut:l,iconCls:a,iconSize:i,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:`border-color ${n},background ${n},padding calc(${n} + 0.1s) ${o}`,[`${t}-item-icon, ${a}`]:{minWidth:i,fontSize:i,transition:`font-size ${r} ${l},margin ${n} ${o},color ${n}`,"+ span":{marginInlineStart:s,opacity:1,transition:`opacity ${n} ${o},margin ${n},color ${n}`}},[`${t}-item-icon`]:Object.assign({},(0,ng.Nk)()),[`&${t}-item-only-child`]:{[`> ${a}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},nN=e=>{let{componentCls:t,motionDurationSlow:n,motionEaseInOut:r,borderRadius:o,menuArrowSize:l,menuArrowOffset:a}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:l,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${r}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(l).mul(.6).equal(),height:e.calc(l).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:o,transition:`background ${n} ${r},transform ${n} ${r},top ${n} ${r},color ${n} ${r}`,content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,np.zA)(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,np.zA)(a)})`}}}}},nj=e=>{let{antCls:t,componentCls:n,fontSize:r,motionDurationSlow:o,motionDurationMid:l,motionEaseInOut:a,paddingXS:i,padding:s,colorSplit:c,lineWidth:d,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:f,menuArrowOffset:g,lineType:h,groupTitleLineHeight:v,groupTitleFontSize:b}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,ng.t6)()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,ng.dF)(e)),(0,ng.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:r,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${o} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${(0,np.zA)(i)} ${(0,np.zA)(s)}`,fontSize:b,lineHeight:v,transition:`all ${o}`},[`&-horizontal ${n}-submenu`]:{transition:`border-color ${o} ${a},background ${o} ${a}`},[`${n}-submenu, ${n}-submenu-inline`]:{transition:`border-color ${o} ${a},background ${o} ${a},padding ${l} ${a}`},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:`background ${o} ${a},padding ${o} ${a}`},[`${n}-title-content`]:{transition:`color ${o}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:h,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),nE(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${(0,np.zA)(e.calc(r).mul(2).equal())} ${(0,np.zA)(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},nE(e)),nN(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${o} ${a}`}})},[`
          &-placement-leftTop,
          &-placement-bottomRight,
          `]:{transformOrigin:"100% 0"},[`
          &-placement-leftBottom,
          &-placement-topRight,
          `]:{transformOrigin:"100% 100%"},[`
          &-placement-rightBottom,
          &-placement-topLeft,
          `]:{transformOrigin:"0 100%"},[`
          &-placement-bottomLeft,
          &-placement-rightTop,
          `]:{transformOrigin:"0 0"},[`
          &-placement-leftTop,
          &-placement-leftBottom
          `]:{paddingInlineEnd:e.paddingXS},[`
          &-placement-rightTop,
          &-placement-rightBottom
          `]:{paddingInlineStart:e.paddingXS},[`
          &-placement-topRight,
          &-placement-topLeft
          `]:{paddingBottom:e.paddingXS},[`
          &-placement-bottomRight,
          &-placement-bottomLeft
          `]:{paddingTop:e.paddingXS}}}),nN(e)),{[`&-inline-collapsed ${n}-submenu-arrow,
        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,np.zA)(g)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,np.zA)(e.calc(g).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${(0,np.zA)(e.calc(f).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,np.zA)(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,np.zA)(g)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},nI=e=>{var t,n,r;let{colorPrimary:o,colorError:l,colorTextDisabled:a,colorErrorBg:i,colorText:s,colorTextDescription:c,colorBgContainer:d,colorFillAlter:u,colorFillContent:m,lineWidth:p,lineWidthBold:f,controlItemBgActive:g,colorBgTextHover:h,controlHeightLG:v,lineHeight:b,colorBgElevated:x,marginXXS:y,padding:A,fontSize:$,controlHeightSM:w,fontSizeLG:C,colorTextLightSolid:k,colorErrorHover:S}=e,E=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,N=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,j=null!==(r=e.itemMarginInline)&&void 0!==r?r:e.marginXXS,I=new nf.Y(k).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:o,horizontalItemHoverColor:o,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:o,itemSelectedColor:o,subMenuItemSelectedColor:o,colorItemTextSelectedHorizontal:o,horizontalItemSelectedColor:o,colorItemBg:d,itemBg:d,colorItemBgHover:h,itemHoverBg:h,colorItemBgActive:m,itemActiveBg:g,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:E,colorActiveBarHeight:f,activeBarHeight:f,colorActiveBarBorderSize:p,activeBarBorderWidth:N,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:l,dangerItemColor:l,colorDangerItemTextHover:l,dangerItemHoverColor:l,colorDangerItemTextSelected:l,dangerItemSelectedColor:l,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:j,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:v,groupTitleLineHeight:b,collapsedWidth:2*v,popupBg:x,itemMarginBlock:y,itemPaddingInline:A,horizontalLineHeight:`${1.15*v}px`,iconSize:$,iconMarginInlineEnd:w-$,collapsedIconSize:C,groupTitleFontSize:$,darkItemDisabledColor:new nf.Y(k).setA(.25).toRgbString(),darkItemColor:I,darkDangerItemColor:l,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:k,darkItemSelectedBg:o,darkDangerItemSelectedBg:l,darkItemHoverBg:"transparent",darkGroupTitleColor:I,darkItemHoverColor:k,darkDangerItemHoverColor:S,darkDangerItemSelectedColor:k,darkDangerItemActiveBg:l,itemWidth:E?`calc(100% + ${N}px)`:`calc(100% - ${2*j}px)`}},nO=e=>{var t;let n;let{popupClassName:r,icon:l,title:a,theme:i}=e,s=o.useContext(nl),{prefixCls:c,inlineCollapsed:d,theme:u}=s,m=(0,nt.Wj)();if(l){let e=o.isValidElement(a)&&"span"===a.type;n=o.createElement(o.Fragment,null,(0,t6.Ob)(l,{className:ey()(o.isValidElement(l)?null===(t=l.props)||void 0===t?void 0:t.className:"",`${c}-item-icon`)}),e?a:o.createElement("span",{className:`${c}-title-content`},a))}else n=d&&!m.length&&a&&"string"==typeof a?o.createElement("div",{className:`${c}-inline-collapsed-noicon`},a.charAt(0)):o.createElement("span",{className:`${c}-title-content`},a);let p=o.useMemo(()=>Object.assign(Object.assign({},s),{firstLevel:!1}),[s]),[f]=(0,t3.YK)("Menu");return o.createElement(nl.Provider,{value:p},o.createElement(nt.g8,Object.assign({},(0,tI.A)(e,["icon"]),{title:n,popupClassName:ey()(c,r,`${c}-${i||u}`),popupStyle:Object.assign({zIndex:f},e.popupStyle)})))};var nz=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function nP(e){return null===e||!1===e}let nK={item:ns,submenu:nO,divider:ni},nR=(0,o.forwardRef)((e,t)=>{var n;let r=o.useContext(nu),l=r||{},{getPrefixCls:a,getPopupContainer:i,direction:s,menu:c}=o.useContext(t9.QO),d=a(),{prefixCls:u,className:m,style:p,theme:f="light",expandIcon:g,_internalDisableMenuItemTitleTooltip:h,inlineCollapsed:v,siderCollapsed:b,rootClassName:x,mode:y,selectable:A,onClick:$,overflowedIndicatorPopupClassName:w}=e,C=nz(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),k=(0,tI.A)(C,["collapsedWidth"]);null===(n=l.validator)||void 0===n||n.call(l,{mode:y});let S=(0,en.A)(function(){var e;null==$||$.apply(void 0,arguments),null===(e=l.onClick)||void 0===e||e.call(l)}),E=l.mode||y,N=null!=A?A:l.selectable,j=null!=v?v:b,I={horizontal:{motionName:`${d}-slide-up`},inline:(0,no.A)(d),other:{motionName:`${d}-zoom-big`}},O=a("menu",u||l.prefixCls),z=(0,ne.A)(O),[P,K,R]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,nx.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:n,fontSize:r,darkItemColor:o,darkDangerItemColor:l,darkItemBg:a,darkSubMenuItemBg:i,darkItemSelectedColor:s,darkItemSelectedBg:c,darkDangerItemSelectedBg:d,darkItemHoverBg:u,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:f,darkDangerItemHoverColor:g,darkDangerItemSelectedColor:h,darkDangerItemActiveBg:v,popupBg:b,darkPopupBg:x}=e,y=e.calc(r).div(7).mul(5).equal(),A=(0,ny.oX)(e,{menuArrowSize:y,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(y).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:b}),$=(0,ny.oX)(A,{itemColor:o,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:a,popupBg:x,subMenuItemBg:i,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:u,itemDisabledColor:f,dangerItemColor:l,dangerItemHoverColor:g,dangerItemSelectedColor:h,dangerItemActiveBg:v,dangerItemSelectedBg:d,menuSubMenuBg:i,horizontalItemSelectedColor:s,horizontalItemSelectedBg:c});return[nj(A),nA(A),nS(A),nC(A,"light"),nC($,"dark"),n$(A),(0,nh.A)(A),(0,nv._j)(A,"slide-up"),(0,nv._j)(A,"slide-down"),(0,nb.aB)(A,"zoom-big")]},nI,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)}(O,z,!r),M=ey()(`${O}-${f}`,null==c?void 0:c.className,m),B=o.useMemo(()=>{var e,t;if("function"==typeof g||nP(g))return g||null;if("function"==typeof l.expandIcon||nP(l.expandIcon))return l.expandIcon||null;if("function"==typeof(null==c?void 0:c.expandIcon)||nP(null==c?void 0:c.expandIcon))return(null==c?void 0:c.expandIcon)||null;let n=null!==(e=null!=g?g:null==l?void 0:l.expandIcon)&&void 0!==e?e:null==c?void 0:c.expandIcon;return(0,t6.Ob)(n,{className:ey()(`${O}-submenu-expand-icon`,o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})},[g,null==l?void 0:l.expandIcon,null==c?void 0:c.expandIcon,O]),T=o.useMemo(()=>({prefixCls:O,inlineCollapsed:j||!1,direction:s,firstLevel:!0,theme:f,mode:E,disableMenuItemTitleTooltip:h}),[O,j,s,h,f]);return P(o.createElement(nu.Provider,{value:null},o.createElement(nl.Provider,{value:T},o.createElement(nt.Ay,Object.assign({getPopupContainer:i,overflowedIndicator:o.createElement(nr.A,null),overflowedIndicatorPopupClassName:ey()(O,`${O}-${f}`,w),mode:E,selectable:N,onClick:S},k,{inlineCollapsed:j,style:Object.assign(Object.assign({},null==c?void 0:c.style),p),className:M,prefixCls:O,direction:s,defaultMotions:I,expandIcon:B,ref:t,rootClassName:ey()(x,K,l.rootClassName,R,z),_internalComponents:nK})))))}),nM=(0,o.forwardRef)((e,t)=>{let n=(0,o.useRef)(null),r=o.useContext(nn);return(0,o.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}})),o.createElement(nR,Object.assign({ref:n},e,r))});nM.Item=ns,nM.SubMenu=nO,nM.Divider=ni,nM.ItemGroup=nt.te;var nB=n(39772),nT=n(1195),nD=n(36725),nH=n(50127);let nL=e=>{let{componentCls:t,menuCls:n,colorError:r,colorTextLightSolid:o}=e,l=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:r,"&:hover":{color:o,backgroundColor:r}}}}}},n_=e=>{let{componentCls:t,menuCls:n,zIndexPopup:r,dropdownArrowDistance:o,sizePopupArrow:l,antCls:a,iconCls:i,motionDurationMid:s,paddingBlock:c,fontSize:d,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:f,colorBgElevated:g}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:r,display:"block","&::before":{position:"absolute",insetBlock:e.calc(l).div(2).sub(o).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${a}-btn`]:{[`& > ${i}-down, & > ${a}-btn-icon > ${i}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${a}-btn > ${i}-down`]:{fontSize:p},[`${i}-down::before`]:{transition:`transform ${s}`}},[`${t}-wrap-open`]:{[`${i}-down::before`]:{transform:"rotate(180deg)"}},[`
        &-hidden,
        &-menu-hidden,
        &-menu-submenu-hidden
      `]:{display:"none"},[`&${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomLeft,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomLeft,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottom,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottom,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomRight,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:nv.ox},[`&${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topLeft,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topLeft,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-top,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-top,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topRight,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topRight`]:{animationName:nv.nP},[`&${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomLeft,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottom,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:nv.vR},[`&${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topLeft,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-top,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topRight`]:{animationName:nv.YU}}},(0,nD.Ay)(e,g,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:r,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,ng.dF)(e)),{[n]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:g,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,ng.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${(0,np.zA)(c)} ${(0,np.zA)(f)}`,color:e.colorTextDescription,transition:`all ${s}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:d,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${s}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,np.zA)(c)} ${(0,np.zA)(f)}`,color:e.colorText,fontWeight:"normal",fontSize:d,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${s}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,ng.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:g,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,np.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:p,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${(0,np.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(f).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:g,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,nv._j)(e,"slide-up"),(0,nv._j)(e,"slide-down"),(0,nT.Mh)(e,"move-up"),(0,nT.Mh)(e,"move-down"),(0,nb.aB)(e,"zoom-big")]]},nW=(0,nx.OF)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:n,paddingXXS:r,componentCls:o}=e,l=(0,ny.oX)(e,{menuCls:`${o}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:r});return[n_(l),nL(l)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,nD.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,nH.n)(e)),{resetStyle:!1}),nF=e=>{var t;let{menu:n,arrow:r,prefixCls:l,children:a,trigger:i,disabled:s,dropdownRender:c,getPopupContainer:d,overlayClassName:u,rootClassName:m,overlayStyle:p,open:f,onOpenChange:g,visible:h,onVisibleChange:v,mouseEnterDelay:b=.15,mouseLeaveDelay:x=.1,autoAdjustOverflow:y=!0,placement:A="",overlay:$,transitionName:w}=e,{getPopupContainer:C,getPrefixCls:k,direction:S,dropdown:E}=o.useContext(t9.QO);(0,t1.rJ)("Dropdown");let I=o.useMemo(()=>{let e=k();return void 0!==w?w:A.includes("top")?`${e}-slide-down`:`${e}-slide-up`},[k,A,w]),O=o.useMemo(()=>A?A.includes("Center")?A.slice(0,A.indexOf("Center")):A:"rtl"===S?"bottomRight":"bottomLeft",[A,S]),z=k("dropdown",l),P=(0,ne.A)(z),[K,R,M]=nW(z,P),[,B]=(0,nB.Ay)(),T=o.Children.only(t4(a)?o.createElement("span",null,a):a),D=(0,t6.Ob)(T,{className:ey()(`${z}-trigger`,{[`${z}-rtl`]:"rtl"===S},T.props.className),disabled:null!==(t=T.props.disabled)&&void 0!==t?t:s}),H=s?[]:i,L=!!(null==H?void 0:H.includes("contextMenu")),[_,W]=(0,t0.A)(!1,{value:null!=f?f:h}),F=(0,en.A)(e=>{null==g||g(e,{source:"trigger"}),null==v||v(e),W(e)}),q=ey()(u,m,R,M,P,null==E?void 0:E.className,{[`${z}-rtl`]:"rtl"===S}),V=(0,t8.A)({arrowPointAtCenter:"object"==typeof r&&r.pointAtCenter,autoAdjustOverflow:y,offset:B.marginXXS,arrowWidth:r?B.sizePopupArrow:0,borderRadius:B.borderRadius}),X=o.useCallback(()=>{null!=n&&n.selectable&&null!=n&&n.multiple||(null==g||g(!1,{source:"menu"}),W(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[U,G]=(0,t3.YK)("Dropdown",null==p?void 0:p.zIndex),Y=o.createElement(t2.A,Object.assign({alignPoint:L},(0,tI.A)(e,["rootClassName"]),{mouseEnterDelay:b,mouseLeaveDelay:x,visible:_,builtinPlacements:V,arrow:!!r,overlayClassName:q,prefixCls:z,getPopupContainer:d||C,transitionName:I,trigger:H,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(nM,Object.assign({},n)):"function"==typeof $?$():$,c&&(e=c(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(nm,{prefixCls:`${z}-menu`,rootClassName:ey()(M,P),expandIcon:o.createElement("span",{className:`${z}-menu-submenu-arrow`},"rtl"===S?o.createElement(N.A,{className:`${z}-menu-submenu-arrow-icon`}):o.createElement(j.A,{className:`${z}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:X,validator:e=>{let{mode:t}=e}},e)},placement:O,onVisibleChange:F,overlayStyle:Object.assign(Object.assign(Object.assign({},null==E?void 0:E.style),p),{zIndex:U})}),D);return U&&(Y=o.createElement(t7.A.Provider,{value:G},Y)),K(Y)},nq=(0,t5.A)(nF,"align",void 0,"dropdown",e=>e);nF._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(nq,Object.assign({},e),o.createElement("span",null));var nV=n(87173),nX=n(66799),nU=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let nG=e=>{let{getPopupContainer:t,getPrefixCls:n,direction:r}=o.useContext(t9.QO),{prefixCls:l,type:a="default",danger:s,disabled:c,loading:d,onClick:u,htmlType:m,children:p,className:f,menu:g,arrow:h,autoFocus:v,overlay:b,trigger:x,align:y,open:A,onOpenChange:$,placement:w,getPopupContainer:C,href:k,icon:S=o.createElement(nr.A,null),title:E,buttonsRender:N=e=>e,mouseEnterDelay:j,mouseLeaveDelay:I,overlayClassName:O,overlayStyle:z,destroyPopupOnHide:P,dropdownRender:K}=e,R=nU(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),M=n("dropdown",l),B=`${M}-button`,T={menu:g,arrow:h,autoFocus:v,align:y,disabled:c,trigger:c?[]:x,onOpenChange:$,getPopupContainer:C||t,mouseEnterDelay:j,mouseLeaveDelay:I,overlayClassName:O,overlayStyle:z,destroyPopupOnHide:P,dropdownRender:K},{compactSize:D,compactItemClassnames:H}=(0,nX.RQ)(M,r),L=ey()(B,H,f);"overlay"in e&&(T.overlay=b),"open"in e&&(T.open=A),"placement"in e?T.placement=w:T.placement="rtl"===r?"bottomLeft":"bottomRight";let[_,W]=N([o.createElement(i.Ay,{type:a,danger:s,disabled:c,loading:d,onClick:u,htmlType:m,href:k,title:E},p),o.createElement(i.Ay,{type:a,danger:s,icon:S})]);return o.createElement(nV.A.Compact,Object.assign({className:L,size:D,block:!0},R),_,o.createElement(nF,Object.assign({},T),W))};nG.__ANT_BUTTON=!0,nF.Button=nG;var nY=n(58493);let nJ={},nQ="SELECT_ALL",nZ="SELECT_INVERT",n0="SELECT_NONE",n1=[],n2=(e,t)=>{let n=[];return(t||[]).forEach(t=>{n.push(t),t&&"object"==typeof t&&e in t&&(n=[].concat((0,e0.A)(n),(0,e0.A)(n2(e,t[e]))))}),n},n3=(e,t)=>{let{preserveSelectedRowKeys:n,selectedRowKeys:r,defaultSelectedRowKeys:l,getCheckboxProps:a,onChange:i,onSelect:s,onSelectAll:c,onSelectInvert:d,onSelectNone:u,onSelectMultiple:m,columnWidth:f,type:g,selections:h,fixed:v,renderCell:b,hideSelectAll:x,checkStrictly:y=!0}=t||{},{prefixCls:A,data:$,pageData:w,getRecordByKey:C,getRowKey:k,expandType:S,childrenColumnName:E,locale:N,getPopupContainer:j}=e,I=(0,t1.rJ)("Table"),[O,z]=function(e){let[t,n]=(0,o.useState)(null);return[(0,o.useCallback)((r,o,l)=>{let a=null!=t?t:r,i=Math.min(a||0,r),s=Math.max(a||0,r),c=o.slice(i,s+1).map(t=>e(t)),d=c.some(e=>!l.has(e)),u=[];return c.forEach(e=>{d?(l.has(e)||u.push(e),l.add(e)):(l.delete(e),u.push(e))}),n(d?s:null),u},[t]),e=>{n(e)}]}(e=>e),[P,K]=(0,t0.A)(r||l||n1,{value:r}),R=o.useRef(new Map),M=(0,o.useCallback)(e=>{if(n){let t=new Map;e.forEach(e=>{let n=C(e);!n&&R.current.has(e)&&(n=R.current.get(e)),t.set(e,n)}),R.current=t}},[C,n]);o.useEffect(()=>{M(P)},[P]);let B=(0,o.useMemo)(()=>n2(E,w),[E,w]),{keyEntities:T}=(0,o.useMemo)(()=>{if(y)return{keyEntities:null};let e=$;if(n){let t=new Set(B.map((e,t)=>k(e,t))),n=Array.from(R.current).reduce((e,n)=>{let[r,o]=n;return t.has(r)?e:e.concat(o)},[]);e=[].concat((0,e0.A)(e),(0,e0.A)(n))}return tB(e,{externalGetKey:k,childrenPropName:E})},[$,k,y,E,n,B]),D=(0,o.useMemo)(()=>{let e=new Map;return B.forEach((t,n)=>{let r=k(t,n),o=(a?a(t):null)||{};e.set(r,o)}),e},[B,k,a]),H=(0,o.useCallback)(e=>{let t;let n=k(e);return!!(null==(t=D.has(n)?D.get(k(e)):a?a(e):void 0)?void 0:t.disabled)},[D,k]),[L,_]=(0,o.useMemo)(()=>{if(y)return[P||[],[]];let{checkedKeys:e,halfCheckedKeys:t}=tZ(P,!0,T,H);return[e||[],t]},[P,y,T,H]),W=(0,o.useMemo)(()=>new Set("radio"===g?L.slice(0,1):L),[L,g]),F=(0,o.useMemo)(()=>"radio"===g?new Set:new Set(_),[_,g]);o.useEffect(()=>{t||K(n1)},[!!t]);let q=(0,o.useCallback)((e,t)=>{let r,o;M(e),n?(r=e,o=e.map(e=>R.current.get(e))):(r=[],o=[],e.forEach(e=>{let t=C(e);void 0!==t&&(r.push(e),o.push(t))})),K(r),null==i||i(r,o,{type:t})},[K,C,i,n]),V=(0,o.useCallback)((e,t,n,r)=>{if(s){let o=n.map(e=>C(e));s(C(e),t,o,r)}q(n,"single")},[s,C,q]),X=(0,o.useMemo)(()=>!h||x?null:(!0===h?[nQ,nZ,n0]:h).map(e=>e===nQ?{key:"all",text:N.selectionAll,onSelect(){q($.map((e,t)=>k(e,t)).filter(e=>{let t=D.get(e);return!(null==t?void 0:t.disabled)||W.has(e)}),"all")}}:e===nZ?{key:"invert",text:N.selectInvert,onSelect(){let e=new Set(W);w.forEach((t,n)=>{let r=k(t,n),o=D.get(r);(null==o?void 0:o.disabled)||(e.has(r)?e.delete(r):e.add(r))});let t=Array.from(e);d&&(I.deprecated(!1,"onSelectInvert","onChange"),d(t)),q(t,"invert")}}:e===n0?{key:"none",text:N.selectNone,onSelect(){null==u||u(),q(Array.from(W).filter(e=>{let t=D.get(e);return null==t?void 0:t.disabled}),"none")}}:e).map(e=>Object.assign(Object.assign({},e),{onSelect:function(){for(var t,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];null===(t=e.onSelect)||void 0===t||t.call.apply(t,[e].concat(r)),z(null)}})),[h,W,w,k,d,q]);return[(0,o.useCallback)(e=>{var n;let r,l,a;if(!t)return e.filter(e=>e!==nJ);let i=(0,e0.A)(e),s=new Set(W),d=B.map(k).filter(e=>!D.get(e).disabled),u=d.every(e=>s.has(e)),$=d.some(e=>s.has(e));if("radio"!==g){let e;if(X){let t={getPopupContainer:j,items:X.map((e,t)=>{let{key:n,text:r,onSelect:o}=e;return{key:null!=n?n:t,onClick:()=>{null==o||o(d)},label:r}})};e=o.createElement("div",{className:`${A}-selection-extra`},o.createElement(nF,{menu:t,getPopupContainer:j},o.createElement("span",null,o.createElement(tS.A,null))))}let t=B.map((e,t)=>{let n=k(e,t),r=D.get(n)||{};return Object.assign({checked:s.has(n)},r)}).filter(e=>{let{disabled:t}=e;return t}),n=!!t.length&&t.length===B.length,a=n&&t.every(e=>{let{checked:t}=e;return t}),i=n&&t.some(e=>{let{checked:t}=e;return t});l=o.createElement(p.A,{checked:n?a:!!B.length&&u,indeterminate:n?!a&&i:!u&&$,onChange:()=>{let e=[];u?d.forEach(t=>{s.delete(t),e.push(t)}):d.forEach(t=>{s.has(t)||(s.add(t),e.push(t))});let t=Array.from(s);null==c||c(!u,t.map(e=>C(e)),e.map(e=>C(e))),q(t,"all"),z(null)},disabled:0===B.length||n,"aria-label":e?"Custom selection":"Select all",skipGroup:!0}),r=!x&&o.createElement("div",{className:`${A}-selection`},l,e)}if(a="radio"===g?(e,t,n)=>{let r=k(t,n),l=s.has(r),a=D.get(r);return{node:o.createElement(nY.Ay,Object.assign({},a,{checked:l,onClick:e=>{var t;e.stopPropagation(),null===(t=null==a?void 0:a.onClick)||void 0===t||t.call(a,e)},onChange:e=>{var t;s.has(r)||V(r,!0,[r],e.nativeEvent),null===(t=null==a?void 0:a.onChange)||void 0===t||t.call(a,e)}})),checked:l}}:(e,t,n)=>{var r;let l;let a=k(t,n),i=s.has(a),c=F.has(a),u=D.get(a);return l="nest"===S?c:null!==(r=null==u?void 0:u.indeterminate)&&void 0!==r?r:c,{node:o.createElement(p.A,Object.assign({},u,{indeterminate:l,checked:i,skipGroup:!0,onClick:e=>{var t;e.stopPropagation(),null===(t=null==u?void 0:u.onClick)||void 0===t||t.call(u,e)},onChange:e=>{var t;let{nativeEvent:n}=e,{shiftKey:r}=n,o=d.findIndex(e=>e===a),l=L.some(e=>d.includes(e));if(r&&y&&l){let e=O(o,d,s),t=Array.from(s);null==m||m(!i,t.map(e=>C(e)),e.map(e=>C(e))),q(t,"multiple")}else if(y){let e=i?tF(L,a):tq(L,a);V(a,!i,e,n)}else{let{checkedKeys:e,halfCheckedKeys:t}=tZ([].concat((0,e0.A)(L),[a]),!0,T,H),r=e;if(i){let n=new Set(e);n.delete(a),r=tZ(Array.from(n),{checked:!1,halfCheckedKeys:t},T,H).checkedKeys}V(a,!i,r,n)}i?z(null):z(o),null===(t=null==u?void 0:u.onChange)||void 0===t||t.call(u,e)}})),checked:i}},!i.includes(nJ)){if(0===i.findIndex(e=>{var t;return(null===(t=e[eJ])||void 0===t?void 0:t.columnType)==="EXPAND_COLUMN"})){let[e,...t]=i;i=[e,nJ].concat((0,e0.A)(t))}else i=[nJ].concat((0,e0.A)(i))}let w=i.indexOf(nJ),E=(i=i.filter((e,t)=>e!==nJ||t===w))[w-1],N=i[w+1],I=v;void 0===I&&((null==N?void 0:N.fixed)!==void 0?I=N.fixed:(null==E?void 0:E.fixed)!==void 0&&(I=E.fixed)),I&&E&&(null===(n=E[eJ])||void 0===n?void 0:n.columnType)==="EXPAND_COLUMN"&&void 0===E.fixed&&(E.fixed=I);let P=ey()(`${A}-selection-col`,{[`${A}-selection-col-with-dropdown`]:h&&"checkbox"===g}),K={fixed:I,width:f,className:`${A}-selection-column`,title:(null==t?void 0:t.columnTitle)?"function"==typeof t.columnTitle?t.columnTitle(l):t.columnTitle:r,render:(e,t,n)=>{let{node:r,checked:o}=a(e,t,n);return b?b(o,t,n,r):r},onCell:t.onCell,[eJ]:{className:P}};return i.map(e=>e===nJ?K:e)},[k,B,t,L,W,F,f,X,S,D,m,V,H]),W]},n4=e=>0;var n8=n(14092),n5=n(43089),n6=n(52271),n7=n(13439);let n9={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"};var re=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:n9}))});let rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"};var rn=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:rt}))}),rr=n(73924);let ro={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};var rl=[10,20,50,100];let ra=function(e){var t=e.pageSizeOptions,n=void 0===t?rl:t,r=e.locale,o=e.changeSize,a=e.pageSize,i=e.goButton,s=e.quickGo,c=e.rootPrefixCls,d=e.disabled,u=e.buildOptionText,m=e.showSizeChanger,p=e.sizeChangerRender,f=l().useState(""),g=(0,et.A)(f,2),h=g[0],v=g[1],b=function(){return!h||Number.isNaN(h)?void 0:Number(h)},x="function"==typeof u?u:function(e){return"".concat(e," ").concat(r.items_per_page)},y=function(e){""!==h&&(e.keyCode===rr.A.ENTER||"click"===e.type)&&(v(""),null==s||s(b()))},A="".concat(c,"-options");if(!m&&!s)return null;var $=null,w=null,C=null;return m&&p&&($=p({disabled:d,size:a,onSizeChange:function(e){null==o||o(Number(e))},"aria-label":r.page_size,className:"".concat(A,"-size-changer"),options:(n.some(function(e){return e.toString()===a.toString()})?n:n.concat([a]).sort(function(e,t){return(Number.isNaN(Number(e))?0:Number(e))-(Number.isNaN(Number(t))?0:Number(t))})).map(function(e){return{label:x(e),value:e}})})),s&&(i&&(C="boolean"==typeof i?l().createElement("button",{type:"button",onClick:y,onKeyUp:y,disabled:d,className:"".concat(A,"-quick-jumper-button")},r.jump_to_confirm):l().createElement("span",{onClick:y,onKeyUp:y},i)),w=l().createElement("div",{className:"".concat(A,"-quick-jumper")},r.jump_to,l().createElement("input",{disabled:d,type:"text",value:h,onChange:function(e){v(e.target.value)},onKeyUp:y,onBlur:function(e){!i&&""!==h&&(v(""),e.relatedTarget&&(e.relatedTarget.className.indexOf("".concat(c,"-item-link"))>=0||e.relatedTarget.className.indexOf("".concat(c,"-item"))>=0)||null==s||s(b()))},"aria-label":r.page}),r.page,C)),l().createElement("li",{className:A},$,w)},ri=function(e){var t=e.rootPrefixCls,n=e.page,r=e.active,o=e.className,a=e.showTitle,i=e.onClick,s=e.onKeyPress,c=e.itemRender,d="".concat(t,"-item"),u=ey()(d,"".concat(d,"-").concat(n),(0,eb.A)((0,eb.A)({},"".concat(d,"-active"),r),"".concat(d,"-disabled"),!n),o),m=c(n,"page",l().createElement("a",{rel:"nofollow"},n));return m?l().createElement("li",{title:a?String(n):null,className:u,onClick:function(){i(n)},onKeyDown:function(e){s(e,i,n)},tabIndex:0},m):null};var rs=function(e,t,n){return n};function rc(){}function rd(e){var t=Number(e);return"number"==typeof t&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function ru(e,t,n){return Math.floor((n-1)/(void 0===e?t:e))+1}let rm=function(e){var t,n,r,a,i=e.prefixCls,s=void 0===i?"rc-pagination":i,c=e.selectPrefixCls,d=e.className,u=e.current,m=e.defaultCurrent,p=e.total,f=void 0===p?0:p,g=e.pageSize,h=e.defaultPageSize,b=e.onChange,x=void 0===b?rc:b,y=e.hideOnSinglePage,A=e.align,$=e.showPrevNextJumpers,w=e.showQuickJumper,C=e.showLessItems,k=e.showTitle,S=void 0===k||k,E=e.onShowSizeChange,N=void 0===E?rc:E,j=e.locale,I=void 0===j?ro:j,O=e.style,z=e.totalBoundaryShowSizeChanger,P=e.disabled,K=e.simple,R=e.showTotal,M=e.showSizeChanger,B=void 0===M?f>(void 0===z?50:z):M,T=e.sizeChangerRender,D=e.pageSizeOptions,H=e.itemRender,L=void 0===H?rs:H,_=e.jumpPrevIcon,W=e.jumpNextIcon,F=e.prevIcon,q=e.nextIcon,V=l().useRef(null),X=(0,t0.A)(10,{value:g,defaultValue:void 0===h?10:h}),U=(0,et.A)(X,2),G=U[0],Y=U[1],J=(0,t0.A)(1,{value:u,defaultValue:void 0===m?1:m,postState:function(e){return Math.max(1,Math.min(e,ru(void 0,G,f)))}}),Q=(0,et.A)(J,2),Z=Q[0],ee=Q[1],en=l().useState(Z),er=(0,et.A)(en,2),eo=er[0],el=er[1];(0,o.useEffect)(function(){el(Z)},[Z]);var ea=Math.max(1,Z-(C?3:5)),ei=Math.min(ru(void 0,G,f),Z+(C?3:5));function es(t,n){var r=t||l().createElement("button",{type:"button","aria-label":n,className:"".concat(s,"-item-link")});return"function"==typeof t&&(r=l().createElement(t,(0,ev.A)({},e))),r}function ec(e){var t=e.target.value,n=ru(void 0,G,f);return""===t?t:Number.isNaN(Number(t))?eo:t>=n?n:Number(t)}var ed=f>G&&w;function eu(e){var t=ec(e);switch(t!==eo&&el(t),e.keyCode){case rr.A.ENTER:em(t);break;case rr.A.UP:em(t-1);break;case rr.A.DOWN:em(t+1)}}function em(e){if(rd(e)&&e!==Z&&rd(f)&&f>0&&!P){var t=ru(void 0,G,f),n=e;return e>t?n=t:e<1&&(n=1),n!==eo&&el(n),ee(n),null==x||x(n,G),n}return Z}var ep=Z>1,ef=Z<ru(void 0,G,f);function eg(){ep&&em(Z-1)}function ex(){ef&&em(Z+1)}function eA(){em(ea)}function e$(){em(ei)}function ew(e,t){if("Enter"===e.key||e.charCode===rr.A.ENTER||e.keyCode===rr.A.ENTER){for(var n=arguments.length,r=Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];t.apply(void 0,r)}}function eC(e){("click"===e.type||e.keyCode===rr.A.ENTER)&&em(eo)}var ek=null,eS=(0,eD.A)(e,{aria:!0,data:!0}),eE=R&&l().createElement("li",{className:"".concat(s,"-total-text")},R(f,[0===f?0:(Z-1)*G+1,Z*G>f?f:Z*G])),eN=null,ej=ru(void 0,G,f);if(y&&f<=G)return null;var eI=[],eO={rootPrefixCls:s,onClick:em,onKeyPress:ew,showTitle:S,itemRender:L,page:-1},ez=Z-1>0?Z-1:0,eP=Z+1<ej?Z+1:ej,eK=w&&w.goButton,eR="object"===(0,eh.A)(K)?K.readOnly:!K,eM=eK,eB=null;K&&(eK&&(eM="boolean"==typeof eK?l().createElement("button",{type:"button",onClick:eC,onKeyUp:eC},I.jump_to_confirm):l().createElement("span",{onClick:eC,onKeyUp:eC},eK),eM=l().createElement("li",{title:S?"".concat(I.jump_to).concat(Z,"/").concat(ej):null,className:"".concat(s,"-simple-pager")},eM)),eB=l().createElement("li",{title:S?"".concat(Z,"/").concat(ej):null,className:"".concat(s,"-simple-pager")},eR?eo:l().createElement("input",{type:"text","aria-label":I.jump_to,value:eo,disabled:P,onKeyDown:function(e){(e.keyCode===rr.A.UP||e.keyCode===rr.A.DOWN)&&e.preventDefault()},onKeyUp:eu,onChange:eu,onBlur:function(e){em(ec(e))},size:3}),l().createElement("span",{className:"".concat(s,"-slash")},"/"),ej));var eT=C?1:2;if(ej<=3+2*eT){ej||eI.push(l().createElement(ri,(0,v.A)({},eO,{key:"noPager",page:1,className:"".concat(s,"-item-disabled")})));for(var eH=1;eH<=ej;eH+=1)eI.push(l().createElement(ri,(0,v.A)({},eO,{key:eH,page:eH,active:Z===eH})))}else{var eL=C?I.prev_3:I.prev_5,e_=C?I.next_3:I.next_5,eW=L(ea,"jump-prev",es(_,"prev page")),eF=L(ei,"jump-next",es(W,"next page"));(void 0===$||$)&&(ek=eW?l().createElement("li",{title:S?eL:null,key:"prev",onClick:eA,tabIndex:0,onKeyDown:function(e){ew(e,eA)},className:ey()("".concat(s,"-jump-prev"),(0,eb.A)({},"".concat(s,"-jump-prev-custom-icon"),!!_))},eW):null,eN=eF?l().createElement("li",{title:S?e_:null,key:"next",onClick:e$,tabIndex:0,onKeyDown:function(e){ew(e,e$)},className:ey()("".concat(s,"-jump-next"),(0,eb.A)({},"".concat(s,"-jump-next-custom-icon"),!!W))},eF):null);var eq=Math.max(1,Z-eT),eV=Math.min(Z+eT,ej);Z-1<=eT&&(eV=1+2*eT),ej-Z<=eT&&(eq=ej-2*eT);for(var eX=eq;eX<=eV;eX+=1)eI.push(l().createElement(ri,(0,v.A)({},eO,{key:eX,page:eX,active:Z===eX})));if(Z-1>=2*eT&&3!==Z&&(eI[0]=l().cloneElement(eI[0],{className:ey()("".concat(s,"-item-after-jump-prev"),eI[0].props.className)}),eI.unshift(ek)),ej-Z>=2*eT&&Z!==ej-2){var eU=eI[eI.length-1];eI[eI.length-1]=l().cloneElement(eU,{className:ey()("".concat(s,"-item-before-jump-next"),eU.props.className)}),eI.push(eN)}1!==eq&&eI.unshift(l().createElement(ri,(0,v.A)({},eO,{key:1,page:1}))),eV!==ej&&eI.push(l().createElement(ri,(0,v.A)({},eO,{key:ej,page:ej})))}var eG=(t=L(ez,"prev",es(F,"prev page")),l().isValidElement(t)?l().cloneElement(t,{disabled:!ep}):t);if(eG){var eY=!ep||!ej;eG=l().createElement("li",{title:S?I.prev_page:null,onClick:eg,tabIndex:eY?null:0,onKeyDown:function(e){ew(e,eg)},className:ey()("".concat(s,"-prev"),(0,eb.A)({},"".concat(s,"-disabled"),eY)),"aria-disabled":eY},eG)}var eJ=(n=L(eP,"next",es(q,"next page")),l().isValidElement(n)?l().cloneElement(n,{disabled:!ef}):n);eJ&&(K?(r=!ef,a=ep?0:null):a=(r=!ef||!ej)?null:0,eJ=l().createElement("li",{title:S?I.next_page:null,onClick:ex,tabIndex:a,onKeyDown:function(e){ew(e,ex)},className:ey()("".concat(s,"-next"),(0,eb.A)({},"".concat(s,"-disabled"),r)),"aria-disabled":r},eJ));var eQ=ey()(s,d,(0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)((0,eb.A)({},"".concat(s,"-start"),"start"===A),"".concat(s,"-center"),"center"===A),"".concat(s,"-end"),"end"===A),"".concat(s,"-simple"),K),"".concat(s,"-disabled"),P));return l().createElement("ul",(0,v.A)({className:eQ,style:O,ref:V},eS),eE,eG,K?eB:eI,eJ,l().createElement(ra,{locale:I,rootPrefixCls:s,disabled:P,selectPrefixCls:void 0===c?"rc-select":c,changeSize:function(e){var t=ru(e,G,f),n=Z>t&&0!==t?t:Z;Y(e),el(n),null==N||N(Z,e),ee(n),null==x||x(n,e)},pageSize:G,pageSizeOptions:D,quickGo:ed?em:null,goButton:eM,showSizeChanger:B,sizeChangerRender:T}))};var rp=n(52409),rf=n(76155),rg=n(90626),rh=n(20111),rv=n(26830);let rb=e=>{let{componentCls:t}=e;return{[`${t}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-item`]:{cursor:"not-allowed",backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${t}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${t}-simple-pager`]:{color:e.colorTextDisabled},[`${t}-jump-prev, ${t}-jump-next`]:{[`${t}-item-link-icon`]:{opacity:0},[`${t}-item-ellipsis`]:{opacity:1}}},[`&${t}-simple`]:{[`${t}-prev, ${t}-next`]:{[`&${t}-disabled ${t}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},rx=e=>{let{componentCls:t}=e;return{[`&${t}-mini ${t}-total-text, &${t}-mini ${t}-simple-pager`]:{height:e.itemSizeSM,lineHeight:(0,np.zA)(e.itemSizeSM)},[`&${t}-mini ${t}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:(0,np.zA)(e.calc(e.itemSizeSM).sub(2).equal())},[`&${t}-mini ${t}-prev, &${t}-mini ${t}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:(0,np.zA)(e.itemSizeSM)},[`&${t}-mini:not(${t}-disabled)`]:{[`${t}-prev, ${t}-next`]:{[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover ${t}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${t}-mini ${t}-prev ${t}-item-link,
    &${t}-mini ${t}-next ${t}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:(0,np.zA)(e.itemSizeSM)}},[`&${t}-mini ${t}-jump-prev, &${t}-mini ${t}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:(0,np.zA)(e.itemSizeSM)},[`&${t}-mini ${t}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:(0,np.zA)(e.itemSizeSM),input:Object.assign(Object.assign({},(0,rg.BZ)(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},ry=e=>{let{componentCls:t}=e;return{[`
    &${t}-simple ${t}-prev,
    &${t}-simple ${t}-next
    `]:{height:e.itemSizeSM,lineHeight:(0,np.zA)(e.itemSizeSM),verticalAlign:"top",[`${t}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:(0,np.zA)(e.itemSizeSM)}}},[`&${t}-simple ${t}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${(0,np.zA)(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${(0,np.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${(0,np.zA)(e.inputOutlineOffset)} 0 ${(0,np.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},rA=e=>{let{componentCls:t}=e;return{[`${t}-jump-prev, ${t}-jump-next`]:{outline:0,[`${t}-item-container`]:{position:"relative",[`${t}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${t}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}}},[`
    ${t}-prev,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${t}-prev,
    ${t}-next,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:(0,np.zA)(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${t}-prev, ${t}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${t}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${(0,np.zA)(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover`]:{[`${t}-item-link`]:{backgroundColor:"transparent"}}},[`${t}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${t}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:(0,np.zA)(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},(0,rg.wj)(e)),(0,rv.nI)(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},(0,rv.eT)(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},r$=e=>{let{componentCls:t}=e;return{[`${t}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:(0,np.zA)(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${(0,np.zA)(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${(0,np.zA)(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${t}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},rw=e=>{let{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,ng.dF)(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${t}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:(0,np.zA)(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),r$(e)),rA(e)),ry(e)),rx(e)),rb(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${t}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${t}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},rC=e=>{let{componentCls:t}=e;return{[`${t}:not(${t}-disabled)`]:{[`${t}-item`]:Object.assign({},(0,ng.K8)(e)),[`${t}-jump-prev, ${t}-jump-next`]:{"&:focus-visible":Object.assign({[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}},(0,ng.jk)(e))},[`${t}-prev, ${t}-next`]:{[`&:focus-visible ${t}-item-link`]:Object.assign({},(0,ng.jk)(e))}}}},rk=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},(0,rh.b)(e)),rS=e=>(0,ny.oX)(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},(0,rh.C)(e)),rE=(0,nx.OF)("Pagination",e=>{let t=rS(e);return[rw(t),rC(t)]},rk),rN=e=>{let{componentCls:t}=e;return{[`${t}${t}-bordered${t}-disabled:not(${t}-mini)`]:{"&, &:hover":{[`${t}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${t}-item-link`]:{borderColor:e.colorBorder}},[`${t}-item, ${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${t}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${t}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${t}-prev, ${t}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${t}${t}-bordered:not(${t}-mini)`]:{[`${t}-prev, ${t}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${t}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${t}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${t}-disabled`]:{[`${t}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${t}-item`]:{backgroundColor:e.itemBg,border:`${(0,np.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${t}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},rj=(0,nx.bf)(["Pagination","bordered"],e=>[rN(rS(e))],rk);function rI(e){return(0,o.useMemo)(()=>"boolean"==typeof e?[e,{}]:e&&"object"==typeof e?[!0,e]:[void 0,void 0],[e])}var rO=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let rz=e=>{let{align:t,prefixCls:n,selectPrefixCls:r,className:l,rootClassName:a,style:i,size:s,locale:c,responsive:d,showSizeChanger:u,selectComponentClass:m,pageSizeOptions:p}=e,f=rO(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:g}=(0,n6.A)(d),[,h]=(0,nB.Ay)(),{getPrefixCls:v,direction:b,showSizeChanger:x,className:y,style:A}=(0,t9.TP)("pagination"),$=v("pagination",n),[w,C,k]=rE($),S=(0,n5.A)(s),E="small"===S||!!(g&&!S&&d),[I]=(0,rf.A)("Pagination",rp.A),O=Object.assign(Object.assign({},I),c),[z,P]=rI(u),[K,R]=rI(x),M=null!=P?P:R,B=m||Q.A,T=o.useMemo(()=>p?p.map(e=>Number(e)):void 0,[p]),D=o.useMemo(()=>{let e=o.createElement("span",{className:`${$}-item-ellipsis`},"•••"),t=o.createElement("button",{className:`${$}-item-link`,type:"button",tabIndex:-1},"rtl"===b?o.createElement(j.A,null):o.createElement(N.A,null));return{prevIcon:t,nextIcon:o.createElement("button",{className:`${$}-item-link`,type:"button",tabIndex:-1},"rtl"===b?o.createElement(N.A,null):o.createElement(j.A,null)),jumpPrevIcon:o.createElement("a",{className:`${$}-item-link`},o.createElement("div",{className:`${$}-item-container`},"rtl"===b?o.createElement(rn,{className:`${$}-item-link-icon`}):o.createElement(re,{className:`${$}-item-link-icon`}),e)),jumpNextIcon:o.createElement("a",{className:`${$}-item-link`},o.createElement("div",{className:`${$}-item-container`},"rtl"===b?o.createElement(re,{className:`${$}-item-link-icon`}):o.createElement(rn,{className:`${$}-item-link-icon`}),e))}},[b,$]),H=v("select",r),L=ey()({[`${$}-${t}`]:!!t,[`${$}-mini`]:E,[`${$}-rtl`]:"rtl"===b,[`${$}-bordered`]:h.wireframe},y,l,a,C,k),_=Object.assign(Object.assign({},A),i);return w(o.createElement(o.Fragment,null,h.wireframe&&o.createElement(rj,{prefixCls:$}),o.createElement(rm,Object.assign({},D,f,{style:_,prefixCls:$,selectPrefixCls:H,className:L,locale:O,pageSizeOptions:T,showSizeChanger:null!=z?z:K,sizeChangerRender:e=>{var t;let{disabled:n,size:r,onSizeChange:l,"aria-label":a,className:i,options:s}=e,{className:c,onChange:d}=M||{},u=null===(t=s.find(e=>String(e.value)===String(r)))||void 0===t?void 0:t.value;return o.createElement(B,Object.assign({disabled:n,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:e=>e.parentNode,"aria-label":a,options:s},M,{value:u,onChange:(e,t)=>{null==l||l(e),null==d||d(e,t)},size:E?"small":"middle",className:ey()(i,c)}))}}))))},rP=(e,t)=>"key"in e&&void 0!==e.key&&null!==e.key?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function rK(e,t){return t?`${t}-${e}`:`${e}`}let rR=(e,t)=>"function"==typeof e?e(t):e,rM=(e,t)=>{let n=rR(e,t);return"[object Object]"===Object.prototype.toString.call(n)?"":n},rB={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"};var rT=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:rB}))});let rD=function(){let e=Object.assign({},arguments.length<=0?void 0:arguments[0]);for(let t=1;t<arguments.length;t++){let n=t<0||arguments.length<=t?void 0:arguments[t];n&&Object.keys(n).forEach(t=>{let r=n[t];void 0!==r&&(e[t]=r)})}return e};var rH=n(85303),rL=n(78959),r_=n(70476),rW=n(85430),rF=n(49306),rq=n(93316),rV=n(5453);function rX(e){if(null==e)throw TypeError("Cannot destructure "+e)}var rU=n(80775);let rG=function(e,t){var n=o.useState(!1),r=(0,et.A)(n,2),l=r[0],a=r[1];(0,er.A)(function(){if(l)return e(),function(){t()}},[l]),(0,er.A)(function(){return a(!0),function(){a(!1)}},[])};var rY=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],rJ=o.forwardRef(function(e,t){var n=e.className,r=e.style,l=e.motion,a=e.motionNodes,i=e.motionType,s=e.onMotionStart,c=e.onMotionEnd,d=e.active,u=e.treeNodeRequiredProps,m=(0,eO.A)(e,rY),p=o.useState(!0),f=(0,et.A)(p,2),g=f[0],h=f[1],b=o.useContext(tE).prefixCls,x=a&&"hide"!==i;(0,er.A)(function(){a&&x!==g&&h(x)},[a]);var y=o.useRef(!1),A=function(){a&&!y.current&&(y.current=!0,c())};return(rG(function(){a&&s()},A),a)?o.createElement(rU.Ay,(0,v.A)({ref:t,visible:g},l,{motionAppear:"show"===i,onVisibleChanged:function(e){x===e&&A()}}),function(e,t){var n=e.className,r=e.style;return o.createElement("div",{ref:t,className:ey()("".concat(b,"-treenode-motion"),n),style:r},a.map(function(e){var t=Object.assign({},(rX(e.data),e.data)),n=e.title,r=e.key,l=e.isStart,a=e.isEnd;delete t.children;var i=tT(r,u);return o.createElement(tW,(0,v.A)({},t,i,{title:n,active:d,data:e.data,key:r,isStart:l,isEnd:a}))}))}):o.createElement(tW,(0,v.A)({domRef:t,className:n,style:r},m,{active:d}))});function rQ(e,t,n){var r=e.findIndex(function(e){return e.key===n}),o=e[r+1],l=t.findIndex(function(e){return e.key===n});if(o){var a=t.findIndex(function(e){return e.key===o.key});return t.slice(l+1,a)}return t.slice(l+1)}var rZ=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],r0={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},r1=function(){},r2="RC_TREE_MOTION_".concat(Math.random()),r3={key:r2},r4={key:r2,level:0,index:0,pos:"0",node:r3,nodes:[r3]},r8={parent:null,children:[],pos:r4.pos,data:r3,title:null,key:r2,isStart:[],isEnd:[]};function r5(e,t,n,r){return!1!==t&&n?e.slice(0,Math.ceil(n/r)+1):e}function r6(e){return tP(e.key,e.pos)}var r7=o.forwardRef(function(e,t){var n=e.prefixCls,r=e.data,l=(e.selectable,e.checkable,e.expandedKeys),a=e.selectedKeys,i=e.checkedKeys,s=e.loadedKeys,c=e.loadingKeys,d=e.halfCheckedKeys,u=e.keyEntities,m=e.disabled,p=e.dragging,f=e.dragOverNodeKey,g=e.dropPosition,h=e.motion,b=e.height,x=e.itemHeight,y=e.virtual,A=e.scrollWidth,$=e.focusable,w=e.activeItem,C=e.focused,k=e.tabIndex,S=e.onKeyDown,E=e.onFocus,N=e.onBlur,j=e.onActiveChange,I=e.onListChangeStart,O=e.onListChangeEnd,z=(0,eO.A)(e,rZ),P=o.useRef(null),K=o.useRef(null);o.useImperativeHandle(t,function(){return{scrollTo:function(e){P.current.scrollTo(e)},getIndentWidth:function(){return K.current.offsetWidth}}});var R=o.useState(l),M=(0,et.A)(R,2),B=M[0],T=M[1],D=o.useState(r),H=(0,et.A)(D,2),L=H[0],_=H[1],W=o.useState(r),F=(0,et.A)(W,2),q=F[0],V=F[1],X=o.useState([]),U=(0,et.A)(X,2),G=U[0],Y=U[1],J=o.useState(null),Q=(0,et.A)(J,2),Z=Q[0],ee=Q[1],en=o.useRef(r);function eo(){var e=en.current;_(e),V(e),Y([]),ee(null),O()}en.current=r,(0,er.A)(function(){T(l);var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e.length,r=t.length;if(1!==Math.abs(n-r))return{add:!1,key:null};function o(e,t){var n=new Map;e.forEach(function(e){n.set(e,!0)});var r=t.filter(function(e){return!n.has(e)});return 1===r.length?r[0]:null}return n<r?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}(B,l);if(null!==e.key){if(e.add){var t=L.findIndex(function(t){return t.key===e.key}),n=r5(rQ(L,r,e.key),y,b,x),o=L.slice();o.splice(t+1,0,r8),V(o),Y(n),ee("show")}else{var a=r.findIndex(function(t){return t.key===e.key}),i=r5(rQ(r,L,e.key),y,b,x),s=r.slice();s.splice(a+1,0,r8),V(s),Y(i),ee("hide")}}else L!==r&&(_(r),V(r))},[l,r]),o.useEffect(function(){p||eo()},[p]);var el=h?q:r,ea={expandedKeys:l,selectedKeys:a,loadedKeys:s,loadingKeys:c,checkedKeys:i,halfCheckedKeys:d,dragOverNodeKey:f,dropPosition:g,keyEntities:u};return o.createElement(o.Fragment,null,C&&w&&o.createElement("span",{style:r0,"aria-live":"assertive"},function(e){for(var t=String(e.data.key),n=e;n.parent;)n=n.parent,t="".concat(n.data.key," > ").concat(t);return t}(w)),o.createElement("div",null,o.createElement("input",{style:r0,disabled:!1===$||m,tabIndex:!1!==$?k:null,onKeyDown:S,onFocus:E,onBlur:N,value:"",onChange:r1,"aria-label":"for screen reader"})),o.createElement("div",{className:"".concat(n,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},o.createElement("div",{className:"".concat(n,"-indent")},o.createElement("div",{ref:K,className:"".concat(n,"-indent-unit")}))),o.createElement(tv.A,(0,v.A)({},z,{data:el,itemKey:r6,height:b,fullHeight:!1,virtual:y,itemHeight:x,scrollWidth:A,prefixCls:"".concat(n,"-list"),ref:P,role:"tree",onVisibleChange:function(e){e.every(function(e){return r6(e)!==r2})&&eo()}}),function(e){var t=e.pos,n=Object.assign({},(rX(e.data),e.data)),r=e.title,l=e.key,a=e.isStart,i=e.isEnd,s=tP(l,t);delete n.key,delete n.children;var c=tT(s,ea);return o.createElement(rJ,(0,v.A)({},n,c,{title:r,active:!!w&&l===w.key,pos:t,data:e.data,isStart:a,isEnd:i,motion:h,motionNodes:l===r2?G:null,motionType:Z,onMotionStart:I,onMotionEnd:eo,treeNodeRequiredProps:ea,onMouseMove:function(){j(null)}}))}))}),r9=function(e){(0,rq.A)(n,e);var t=(0,rV.A)(n);function n(){var e;(0,r_.A)(this,n);for(var r=arguments.length,l=Array(r),a=0;a<r;a++)l[a]=arguments[a];return e=t.call.apply(t,[this].concat(l)),(0,eb.A)((0,rF.A)(e),"destroyed",!1),(0,eb.A)((0,rF.A)(e),"delayedDragEnterLogic",void 0),(0,eb.A)((0,rF.A)(e),"loadingRetryTimes",{}),(0,eb.A)((0,rF.A)(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:tK()}),(0,eb.A)((0,rF.A)(e),"dragStartMousePosition",null),(0,eb.A)((0,rF.A)(e),"dragNodeProps",null),(0,eb.A)((0,rF.A)(e),"currentMouseOverDroppableNodeKey",null),(0,eb.A)((0,rF.A)(e),"listRef",o.createRef()),(0,eb.A)((0,rF.A)(e),"onNodeDragStart",function(t,n){var r,o=e.state,l=o.expandedKeys,a=o.keyEntities,i=e.props.onDragStart,s=n.eventKey;e.dragNodeProps=n,e.dragStartMousePosition={x:t.clientX,y:t.clientY};var c=tF(l,s);e.setState({draggingNodeKey:s,dragChildrenKeys:(r=[],function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.forEach(function(t){var n=t.key,o=t.children;r.push(n),e(o)})}(a[s].children),r),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(c),window.addEventListener("dragend",e.onWindowDragEnd),null==i||i({event:t,node:tD(n)})}),(0,eb.A)((0,rF.A)(e),"onNodeDragEnter",function(t,n){var r=e.state,o=r.expandedKeys,l=r.keyEntities,a=r.dragChildrenKeys,i=r.flattenNodes,s=r.indent,c=e.props,d=c.onDragEnter,u=c.onExpand,m=c.allowDrop,p=c.direction,f=n.pos,g=n.eventKey;if(e.currentMouseOverDroppableNodeKey!==g&&(e.currentMouseOverDroppableNodeKey=g),!e.dragNodeProps){e.resetDragState();return}var h=tX(t,e.dragNodeProps,n,s,e.dragStartMousePosition,m,i,l,o,p),v=h.dropPosition,b=h.dropLevelOffset,x=h.dropTargetKey,y=h.dropContainerKey,A=h.dropTargetPos,$=h.dropAllowed,w=h.dragOverNodeKey;if(a.includes(x)||!$||(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(t){clearTimeout(e.delayedDragEnterLogic[t])}),e.dragNodeProps.eventKey!==n.eventKey&&(t.persist(),e.delayedDragEnterLogic[f]=window.setTimeout(function(){if(null!==e.state.draggingNodeKey){var r=(0,e0.A)(o),a=l[n.eventKey];a&&(a.children||[]).length&&(r=tq(o,n.eventKey)),e.props.hasOwnProperty("expandedKeys")||e.setExpandedKeys(r),null==u||u(r,{node:tD(n),expanded:!0,nativeEvent:t.nativeEvent})}},800)),e.dragNodeProps.eventKey===x&&0===b)){e.resetDragState();return}e.setState({dragOverNodeKey:w,dropPosition:v,dropLevelOffset:b,dropTargetKey:x,dropContainerKey:y,dropTargetPos:A,dropAllowed:$}),null==d||d({event:t,node:tD(n),expandedKeys:o})}),(0,eb.A)((0,rF.A)(e),"onNodeDragOver",function(t,n){var r=e.state,o=r.dragChildrenKeys,l=r.flattenNodes,a=r.keyEntities,i=r.expandedKeys,s=r.indent,c=e.props,d=c.onDragOver,u=c.allowDrop,m=c.direction;if(e.dragNodeProps){var p=tX(t,e.dragNodeProps,n,s,e.dragStartMousePosition,u,l,a,i,m),f=p.dropPosition,g=p.dropLevelOffset,h=p.dropTargetKey,v=p.dropContainerKey,b=p.dropTargetPos,x=p.dropAllowed,y=p.dragOverNodeKey;!o.includes(h)&&x&&(e.dragNodeProps.eventKey===h&&0===g?null===e.state.dropPosition&&null===e.state.dropLevelOffset&&null===e.state.dropTargetKey&&null===e.state.dropContainerKey&&null===e.state.dropTargetPos&&!1===e.state.dropAllowed&&null===e.state.dragOverNodeKey||e.resetDragState():f===e.state.dropPosition&&g===e.state.dropLevelOffset&&h===e.state.dropTargetKey&&v===e.state.dropContainerKey&&b===e.state.dropTargetPos&&x===e.state.dropAllowed&&y===e.state.dragOverNodeKey||e.setState({dropPosition:f,dropLevelOffset:g,dropTargetKey:h,dropContainerKey:v,dropTargetPos:b,dropAllowed:x,dragOverNodeKey:y}),null==d||d({event:t,node:tD(n)}))}}),(0,eb.A)((0,rF.A)(e),"onNodeDragLeave",function(t,n){e.currentMouseOverDroppableNodeKey!==n.eventKey||t.currentTarget.contains(t.relatedTarget)||(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var r=e.props.onDragLeave;null==r||r({event:t,node:tD(n)})}),(0,eb.A)((0,rF.A)(e),"onWindowDragEnd",function(t){e.onNodeDragEnd(t,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)}),(0,eb.A)((0,rF.A)(e),"onNodeDragEnd",function(t,n){var r=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),null==r||r({event:t,node:tD(n)}),e.dragNodeProps=null,window.removeEventListener("dragend",e.onWindowDragEnd)}),(0,eb.A)((0,rF.A)(e),"onNodeDrop",function(t,n){var r,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=e.state,a=l.dragChildrenKeys,i=l.dropPosition,s=l.dropTargetKey,c=l.dropTargetPos;if(l.dropAllowed){var d=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),null!==s){var u=(0,ev.A)((0,ev.A)({},tT(s,e.getTreeNodeRequiredProps())),{},{active:(null===(r=e.getActiveItem())||void 0===r?void 0:r.key)===s,data:e.state.keyEntities[s].node}),m=a.includes(s);(0,ew.Ay)(!m,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var p=tV(c),f={event:t,node:tD(u),dragNode:e.dragNodeProps?tD(e.dragNodeProps):null,dragNodesKeys:[e.dragNodeProps.eventKey].concat(a),dropToGap:0!==i,dropPosition:i+Number(p[p.length-1])};o||null==d||d(f),e.dragNodeProps=null}}}),(0,eb.A)((0,rF.A)(e),"cleanDragState",function(){null!==e.state.draggingNodeKey&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null}),(0,eb.A)((0,rF.A)(e),"triggerExpandActionExpand",function(t,n){var r=e.state,o=r.expandedKeys,l=r.flattenNodes,a=n.expanded,i=n.key;if(!n.isLeaf&&!t.shiftKey&&!t.metaKey&&!t.ctrlKey){var s=l.filter(function(e){return e.key===i})[0],c=tD((0,ev.A)((0,ev.A)({},tT(i,e.getTreeNodeRequiredProps())),{},{data:s.data}));e.setExpandedKeys(a?tF(o,i):tq(o,i)),e.onNodeExpand(t,c)}}),(0,eb.A)((0,rF.A)(e),"onNodeClick",function(t,n){var r=e.props,o=r.onClick;"click"===r.expandAction&&e.triggerExpandActionExpand(t,n),null==o||o(t,n)}),(0,eb.A)((0,rF.A)(e),"onNodeDoubleClick",function(t,n){var r=e.props,o=r.onDoubleClick;"doubleClick"===r.expandAction&&e.triggerExpandActionExpand(t,n),null==o||o(t,n)}),(0,eb.A)((0,rF.A)(e),"onNodeSelect",function(t,n){var r=e.state.selectedKeys,o=e.state,l=o.keyEntities,a=o.fieldNames,i=e.props,s=i.onSelect,c=i.multiple,d=n.selected,u=n[a.key],m=!d,p=(r=m?c?tq(r,u):[u]:tF(r,u)).map(function(e){var t=l[e];return t?t.node:null}).filter(Boolean);e.setUncontrolledState({selectedKeys:r}),null==s||s(r,{event:"select",selected:m,node:n,selectedNodes:p,nativeEvent:t.nativeEvent})}),(0,eb.A)((0,rF.A)(e),"onNodeCheck",function(t,n,r){var o,l=e.state,a=l.keyEntities,i=l.checkedKeys,s=l.halfCheckedKeys,c=e.props,d=c.checkStrictly,u=c.onCheck,m=n.key,p={event:"check",node:n,checked:r,nativeEvent:t.nativeEvent};if(d){var f=r?tq(i,m):tF(i,m);o={checked:f,halfChecked:tF(s,m)},p.checkedNodes=f.map(function(e){return a[e]}).filter(Boolean).map(function(e){return e.node}),e.setUncontrolledState({checkedKeys:f})}else{var g=tZ([].concat((0,e0.A)(i),[m]),!0,a),h=g.checkedKeys,v=g.halfCheckedKeys;if(!r){var b=new Set(h);b.delete(m);var x=tZ(Array.from(b),{checked:!1,halfCheckedKeys:v},a);h=x.checkedKeys,v=x.halfCheckedKeys}o=h,p.checkedNodes=[],p.checkedNodesPositions=[],p.halfCheckedKeys=v,h.forEach(function(e){var t=a[e];if(t){var n=t.node,r=t.pos;p.checkedNodes.push(n),p.checkedNodesPositions.push({node:n,pos:r})}}),e.setUncontrolledState({checkedKeys:h},!1,{halfCheckedKeys:v})}null==u||u(o,p)}),(0,eb.A)((0,rF.A)(e),"onNodeLoad",function(t){var n,r=t.key,o=e.state.keyEntities[r];if(null==o||null===(n=o.children)||void 0===n||!n.length){var l=new Promise(function(n,o){e.setState(function(l){var a=l.loadedKeys,i=l.loadingKeys,s=void 0===i?[]:i,c=e.props,d=c.loadData,u=c.onLoad;return!d||(void 0===a?[]:a).includes(r)||s.includes(r)?null:(d(t).then(function(){var o=tq(e.state.loadedKeys,r);null==u||u(o,{event:"load",node:t}),e.setUncontrolledState({loadedKeys:o}),e.setState(function(e){return{loadingKeys:tF(e.loadingKeys,r)}}),n()}).catch(function(t){if(e.setState(function(e){return{loadingKeys:tF(e.loadingKeys,r)}}),e.loadingRetryTimes[r]=(e.loadingRetryTimes[r]||0)+1,e.loadingRetryTimes[r]>=10){var l=e.state.loadedKeys;(0,ew.Ay)(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:tq(l,r)}),n()}o(t)}),{loadingKeys:tq(s,r)})})});return l.catch(function(){}),l}}),(0,eb.A)((0,rF.A)(e),"onNodeMouseEnter",function(t,n){var r=e.props.onMouseEnter;null==r||r({event:t,node:n})}),(0,eb.A)((0,rF.A)(e),"onNodeMouseLeave",function(t,n){var r=e.props.onMouseLeave;null==r||r({event:t,node:n})}),(0,eb.A)((0,rF.A)(e),"onNodeContextMenu",function(t,n){var r=e.props.onRightClick;r&&(t.preventDefault(),r({event:t,node:n}))}),(0,eb.A)((0,rF.A)(e),"onFocus",function(){var t=e.props.onFocus;e.setState({focused:!0});for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];null==t||t.apply(void 0,r)}),(0,eb.A)((0,rF.A)(e),"onBlur",function(){var t=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];null==t||t.apply(void 0,r)}),(0,eb.A)((0,rF.A)(e),"getTreeNodeRequiredProps",function(){var t=e.state;return{expandedKeys:t.expandedKeys||[],selectedKeys:t.selectedKeys||[],loadedKeys:t.loadedKeys||[],loadingKeys:t.loadingKeys||[],checkedKeys:t.checkedKeys||[],halfCheckedKeys:t.halfCheckedKeys||[],dragOverNodeKey:t.dragOverNodeKey,dropPosition:t.dropPosition,keyEntities:t.keyEntities}}),(0,eb.A)((0,rF.A)(e),"setExpandedKeys",function(t){var n=e.state,r=tM(n.treeData,t,n.fieldNames);e.setUncontrolledState({expandedKeys:t,flattenNodes:r},!0)}),(0,eb.A)((0,rF.A)(e),"onNodeExpand",function(t,n){var r=e.state.expandedKeys,o=e.state,l=o.listChanging,a=o.fieldNames,i=e.props,s=i.onExpand,c=i.loadData,d=n.expanded,u=n[a.key];if(!l){var m=r.includes(u),p=!d;if((0,ew.Ay)(d&&m||!d&&!m,"Expand state not sync with index check"),r=p?tq(r,u):tF(r,u),e.setExpandedKeys(r),null==s||s(r,{node:n,expanded:p,nativeEvent:t.nativeEvent}),p&&c){var f=e.onNodeLoad(n);f&&f.then(function(){var t=tM(e.state.treeData,r,a);e.setUncontrolledState({flattenNodes:t})}).catch(function(){var t=tF(e.state.expandedKeys,u);e.setExpandedKeys(t)})}}}),(0,eb.A)((0,rF.A)(e),"onListChangeStart",function(){e.setUncontrolledState({listChanging:!0})}),(0,eb.A)((0,rF.A)(e),"onListChangeEnd",function(){setTimeout(function(){e.setUncontrolledState({listChanging:!1})})}),(0,eb.A)((0,rF.A)(e),"onActiveChange",function(t){var n=e.state.activeKey,r=e.props,o=r.onActiveChange,l=r.itemScrollOffset;n!==t&&(e.setState({activeKey:t}),null!==t&&e.scrollTo({key:t,offset:void 0===l?0:l}),null==o||o(t))}),(0,eb.A)((0,rF.A)(e),"getActiveItem",function(){var t=e.state,n=t.activeKey,r=t.flattenNodes;return null===n?null:r.find(function(e){return e.key===n})||null}),(0,eb.A)((0,rF.A)(e),"offsetActiveKey",function(t){var n=e.state,r=n.flattenNodes,o=n.activeKey,l=r.findIndex(function(e){return e.key===o});-1===l&&t<0&&(l=r.length),l=(l+t+r.length)%r.length;var a=r[l];if(a){var i=a.key;e.onActiveChange(i)}else e.onActiveChange(null)}),(0,eb.A)((0,rF.A)(e),"onKeyDown",function(t){var n=e.state,r=n.activeKey,o=n.expandedKeys,l=n.checkedKeys,a=n.fieldNames,i=e.props,s=i.onKeyDown,c=i.checkable,d=i.selectable;switch(t.which){case rr.A.UP:e.offsetActiveKey(-1),t.preventDefault();break;case rr.A.DOWN:e.offsetActiveKey(1),t.preventDefault()}var u=e.getActiveItem();if(u&&u.data){var m=e.getTreeNodeRequiredProps(),p=!1===u.data.isLeaf||!!(u.data[a.children]||[]).length,f=tD((0,ev.A)((0,ev.A)({},tT(r,m)),{},{data:u.data,active:!0}));switch(t.which){case rr.A.LEFT:p&&o.includes(r)?e.onNodeExpand({},f):u.parent&&e.onActiveChange(u.parent.key),t.preventDefault();break;case rr.A.RIGHT:p&&!o.includes(r)?e.onNodeExpand({},f):u.children&&u.children.length&&e.onActiveChange(u.children[0].key),t.preventDefault();break;case rr.A.ENTER:case rr.A.SPACE:!c||f.disabled||!1===f.checkable||f.disableCheckbox?c||!d||f.disabled||!1===f.selectable||e.onNodeSelect({},f):e.onNodeCheck({},f,!l.includes(r))}}null==s||s(t)}),(0,eb.A)((0,rF.A)(e),"setUncontrolledState",function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e.destroyed){var o=!1,l=!0,a={};Object.keys(t).forEach(function(n){if(e.props.hasOwnProperty(n)){l=!1;return}o=!0,a[n]=t[n]}),o&&(!n||l)&&e.setState((0,ev.A)((0,ev.A)({},a),r))}}),(0,eb.A)((0,rF.A)(e),"scrollTo",function(t){e.listRef.current.scrollTo(t)}),e}return(0,rW.A)(n,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var e=this.props,t=e.activeKey,n=e.itemScrollOffset;void 0!==t&&t!==this.state.activeKey&&(this.setState({activeKey:t}),null!==t&&this.scrollTo({key:t,offset:void 0===n?0:n}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var e,t=this.state,n=t.focused,r=t.flattenNodes,l=t.keyEntities,a=t.draggingNodeKey,i=t.activeKey,s=t.dropLevelOffset,c=t.dropContainerKey,d=t.dropTargetKey,u=t.dropPosition,m=t.dragOverNodeKey,p=t.indent,f=this.props,g=f.prefixCls,h=f.className,b=f.style,x=f.showLine,y=f.focusable,A=f.tabIndex,$=f.selectable,w=f.showIcon,C=f.icon,k=f.switcherIcon,S=f.draggable,E=f.checkable,N=f.checkStrictly,j=f.disabled,I=f.motion,O=f.loadData,z=f.filterTreeNode,P=f.height,K=f.itemHeight,R=f.scrollWidth,M=f.virtual,B=f.titleRender,T=f.dropIndicatorRender,D=f.onContextMenu,H=f.onScroll,L=f.direction,_=f.rootClassName,W=f.rootStyle,F=(0,eD.A)(this.props,{aria:!0,data:!0});S&&(e="object"===(0,eh.A)(S)?S:"function"==typeof S?{nodeDraggable:S}:{});var q={prefixCls:g,selectable:$,showIcon:w,icon:C,switcherIcon:k,draggable:e,draggingNodeKey:a,checkable:E,checkStrictly:N,disabled:j,keyEntities:l,dropLevelOffset:s,dropContainerKey:c,dropTargetKey:d,dropPosition:u,dragOverNodeKey:m,indent:p,direction:L,dropIndicatorRender:T,loadData:O,filterTreeNode:z,titleRender:B,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return o.createElement(tE.Provider,{value:q},o.createElement("div",{className:ey()(g,h,_,(0,eb.A)((0,eb.A)((0,eb.A)({},"".concat(g,"-show-line"),x),"".concat(g,"-focused"),n),"".concat(g,"-active-focused"),null!==i)),style:W},o.createElement(r7,(0,v.A)({ref:this.listRef,prefixCls:g,style:b,data:r,disabled:j,selectable:$,checkable:!!E,motion:I,dragging:null!==a,height:P,itemHeight:K,virtual:M,focusable:y,focused:n,tabIndex:void 0===A?0:A,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:D,onScroll:H,scrollWidth:R},this.getTreeNodeRequiredProps(),F))))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n,r,o=t.prevProps,l={prevProps:e};function a(t){return!o&&e.hasOwnProperty(t)||o&&o[t]!==e[t]}var i=t.fieldNames;if(a("fieldNames")&&(i=tK(e.fieldNames),l.fieldNames=i),a("treeData")?n=e.treeData:a("children")&&((0,ew.Ay)(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),n=tR(e.children)),n){l.treeData=n;var s=tB(n,{fieldNames:i});l.keyEntities=(0,ev.A)((0,eb.A)({},r2,r4),s.keyEntities)}var c=l.keyEntities||t.keyEntities;if(a("expandedKeys")||o&&a("autoExpandParent"))l.expandedKeys=e.autoExpandParent||!o&&e.defaultExpandParent?tY(e.expandedKeys,c):e.expandedKeys;else if(!o&&e.defaultExpandAll){var d=(0,ev.A)({},c);delete d[r2];var u=[];Object.keys(d).forEach(function(e){var t=d[e];t.children&&t.children.length&&u.push(t.key)}),l.expandedKeys=u}else!o&&e.defaultExpandedKeys&&(l.expandedKeys=e.autoExpandParent||e.defaultExpandParent?tY(e.defaultExpandedKeys,c):e.defaultExpandedKeys);if(l.expandedKeys||delete l.expandedKeys,n||l.expandedKeys){var m=tM(n||t.treeData,l.expandedKeys||t.expandedKeys,i);l.flattenNodes=m}if(e.selectable&&(a("selectedKeys")?l.selectedKeys=tU(e.selectedKeys,e):!o&&e.defaultSelectedKeys&&(l.selectedKeys=tU(e.defaultSelectedKeys,e))),e.checkable&&(a("checkedKeys")?r=tG(e.checkedKeys)||{}:!o&&e.defaultCheckedKeys?r=tG(e.defaultCheckedKeys)||{}:n&&(r=tG(e.checkedKeys)||{checkedKeys:t.checkedKeys,halfCheckedKeys:t.halfCheckedKeys}),r)){var p=r,f=p.checkedKeys,g=void 0===f?[]:f,h=p.halfCheckedKeys,v=void 0===h?[]:h;if(!e.checkStrictly){var b=tZ(g,!0,c);g=b.checkedKeys,v=b.halfCheckedKeys}l.checkedKeys=g,l.halfCheckedKeys=v}return a("loadedKeys")&&(l.loadedKeys=e.loadedKeys),l}}]),n}(o.Component);(0,eb.A)(r9,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:function(e){var t=e.dropPosition,n=e.dropLevelOffset,r=e.indent,o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(t){case -1:o.top=0,o.left=-n*r;break;case 1:o.bottom=0,o.left=-n*r;break;case 0:o.bottom=0,o.left=r}return l().createElement("div",{style:o})},allowDrop:function(){return!0},expandAction:!1}),(0,eb.A)(r9,"TreeNode",tW);let oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};var ot=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oe}))});let on={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};var or=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:on}))});let oo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};var ol=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oo}))});let oa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"};var oi=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oa}))}),os=n(50183);let oc=e=>{let{treeCls:t,treeNodeCls:n,directoryNodeSelectedBg:r,directoryNodeSelectedColor:o,motionDurationMid:l,borderRadius:a,controlItemBgHover:i}=e;return{[`${t}${t}-directory ${n}`]:{[`${t}-node-content-wrapper`]:{position:"static",[`> *:not(${t}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${l}`,content:'""',borderRadius:a},"&:hover:before":{background:i}},[`${t}-switcher, ${t}-checkbox, ${t}-draggable-icon`]:{zIndex:1},"&-selected":{[`${t}-switcher, ${t}-draggable-icon`]:{color:o},[`${t}-node-content-wrapper`]:{color:o,background:"transparent","&:before, &:hover:before":{background:r}}}}}},od=new np.Mo("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),ou=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),om=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,np.zA)(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),op=(e,t)=>{let{treeCls:n,treeNodeCls:r,treeNodePadding:o,titleHeight:l,indentSize:a,nodeSelectedBg:i,nodeHoverBg:s,colorTextQuaternary:c,controlItemBgActiveDisabled:d}=t;return{[n]:Object.assign(Object.assign({},(0,ng.dF)(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${n}-rtl ${n}-switcher_close ${n}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${n}-active-focused)`]:Object.assign({},(0,ng.jk)(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${r}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:od,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[r]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:(0,np.zA)(l),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${n}-checkbox-disabled + ${n}-node-selected,&${r}-disabled${r}-selected ${n}-node-content-wrapper`]:{backgroundColor:d},[`${n}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${r}-disabled)`]:{[`${n}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${n}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${r}-disabled).filter-node ${n}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${n}-draggable-icon`]:{flexShrink:0,width:l,textAlign:"center",visibility:"visible",color:c},[`&${r}-disabled ${n}-draggable-icon`]:{visibility:"hidden"}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:a}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher, ${n}-checkbox`]:{marginInlineEnd:t.calc(t.calc(l).sub(t.controlInteractiveSize)).div(2).equal()},[`${n}-switcher`]:Object.assign(Object.assign({},ou(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:l,height:l,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${n}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${n}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:l,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},om(e,t)),{"&:hover":{backgroundColor:s},[`&${n}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:i},[`${n}-iconEle`]:{display:"inline-block",width:l,height:l,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${r}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${n}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${r}-leaf-last ${n}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${(0,np.zA)(t.calc(l).div(2).equal())} !important`}})}},of=function(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=`.${e}`,o=`${r}-treenode`,l=t.calc(t.paddingXS).div(2).equal(),a=(0,ny.oX)(t,{treeCls:r,treeNodeCls:o,treeNodePadding:l});return[op(e,a),n&&oc(a)].filter(Boolean)},og=e=>{let{controlHeightSM:t,controlItemBgHover:n,controlItemBgActive:r}=e;return{titleHeight:t,indentSize:t,nodeHoverBg:n,nodeHoverColor:e.colorText,nodeSelectedBg:r,nodeSelectedColor:e.colorText}},oh=(0,nx.OF)("Tree",(e,t)=>{let{prefixCls:n}=t;return[{[e.componentCls]:(0,os.gd)(`${n}-checkbox`,e)},of(n,e),(0,nh.A)(e)]},e=>{let{colorTextLightSolid:t,colorPrimary:n}=e;return Object.assign(Object.assign({},og(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:n})}),ov=function(e){let{dropPosition:t,dropLevelOffset:n,prefixCls:r,indent:o,direction:a="ltr"}=e,i="ltr"===a?"left":"right",s={[i]:-n*o+4,["ltr"===a?"right":"left"]:0};switch(t){case -1:s.top=-3;break;case 1:s.bottom=-3;break;default:s.bottom=-3,s[i]=o+4}return l().createElement("div",{style:s,className:`${r}-drop-indicator`})},ob={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};var ox=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:ob}))});let oy={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};var oA=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oy}))});let o$={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};var ow=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:o$}))});let oC=e=>{let t;let{prefixCls:n,switcherIcon:r,treeNodeProps:l,showLine:a,switcherLoadingIcon:i}=e,{isLeaf:s,expanded:c,loading:d}=l;if(d)return o.isValidElement(i)?i:o.createElement(u.A,{className:`${n}-switcher-loading-icon`});if(a&&"object"==typeof a&&(t=a.showLeafIcon),s){if(!a)return null;if("boolean"!=typeof t&&t){let e="function"==typeof t?t(l):t,r=`${n}-switcher-line-custom-icon`;return o.isValidElement(e)?(0,t6.Ob)(e,{className:ey()(e.props.className||"",r)}):e}return t?o.createElement(ot,{className:`${n}-switcher-line-icon`}):o.createElement("span",{className:`${n}-switcher-leaf-line`})}let m=`${n}-switcher-icon`,p="function"==typeof r?r(l):r;return o.isValidElement(p)?(0,t6.Ob)(p,{className:ey()(p.props.className||"",m)}):void 0!==p?p:a?c?o.createElement(oA,{className:`${n}-switcher-line-icon`}):o.createElement(ow,{className:`${n}-switcher-line-icon`}):o.createElement(ox,{className:m})},ok=l().forwardRef((e,t)=>{var n;let{getPrefixCls:r,direction:o,virtual:a,tree:i}=l().useContext(t9.QO),{prefixCls:s,className:c,showIcon:d=!1,showLine:u,switcherIcon:m,switcherLoadingIcon:p,blockNode:f=!1,children:g,checkable:h=!1,selectable:v=!0,draggable:b,motion:x,style:y}=e,A=r("tree",s),$=r(),w=null!=x?x:Object.assign(Object.assign({},(0,no.A)($)),{motionAppear:!1}),C=Object.assign(Object.assign({},e),{checkable:h,selectable:v,showIcon:d,motion:w,blockNode:f,showLine:!!u,dropIndicatorRender:ov}),[k,S,E]=oh(A),[,N]=(0,nB.Ay)(),j=N.paddingXS/2+((null===(n=N.Tree)||void 0===n?void 0:n.titleHeight)||N.controlHeightSM),I=l().useMemo(()=>{if(!b)return!1;let e={};switch(typeof b){case"function":e.nodeDraggable=b;break;case"object":e=Object.assign({},b)}return!1!==e.icon&&(e.icon=e.icon||l().createElement(oi,null)),e},[b]);return k(l().createElement(r9,Object.assign({itemHeight:j,ref:t,virtual:a},C,{style:Object.assign(Object.assign({},null==i?void 0:i.style),y),prefixCls:A,className:ey()({[`${A}-icon-hide`]:!d,[`${A}-block-node`]:f,[`${A}-unselectable`]:!v,[`${A}-rtl`]:"rtl"===o},null==i?void 0:i.className,c,S,E),direction:o,checkable:h?l().createElement("span",{className:`${A}-checkbox-inner`}):h,selectable:v,switcherIcon:e=>l().createElement(oC,{prefixCls:A,switcherIcon:m,switcherLoadingIcon:p,treeNodeProps:e,showLine:u}),draggable:I}),g))});function oS(e,t,n){let{key:r,children:o}=n;e.forEach(function(e){let l=e[r],a=e[o];!1!==t(l,e)&&oS(a||[],t,n)})}var oE=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function oN(e){let{isLeaf:t,expanded:n}=e;return t?o.createElement(ot,null):n?o.createElement(or,null):o.createElement(ol,null)}function oj(e){let{treeData:t,children:n}=e;return t||tR(n)}let oI=o.forwardRef((e,t)=>{var{defaultExpandAll:n,defaultExpandParent:r,defaultExpandedKeys:l}=e,a=oE(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);let i=o.useRef(null),s=o.useRef(null),c=()=>{let{keyEntities:e}=tB(oj(a));return n?Object.keys(e):r?tY(a.expandedKeys||l||[],e):a.expandedKeys||l||[]},[d,u]=o.useState(a.selectedKeys||a.defaultSelectedKeys||[]),[m,p]=o.useState(()=>c());o.useEffect(()=>{"selectedKeys"in a&&u(a.selectedKeys)},[a.selectedKeys]),o.useEffect(()=>{"expandedKeys"in a&&p(a.expandedKeys)},[a.expandedKeys]);let{getPrefixCls:f,direction:g}=o.useContext(t9.QO),{prefixCls:h,className:v,showIcon:b=!0,expandAction:x="click"}=a,y=oE(a,["prefixCls","className","showIcon","expandAction"]),A=f("tree",h),$=ey()(`${A}-directory`,{[`${A}-directory-rtl`]:"rtl"===g},v);return o.createElement(ok,Object.assign({icon:oN,ref:t,blockNode:!0},y,{showIcon:b,expandAction:x,prefixCls:A,className:$,expandedKeys:m,selectedKeys:d,onSelect:(e,t)=>{var n;let r;let{multiple:o,fieldNames:l}=a,{node:c,nativeEvent:d}=t,{key:p=""}=c,f=oj(a),g=Object.assign(Object.assign({},t),{selected:!0}),h=(null==d?void 0:d.ctrlKey)||(null==d?void 0:d.metaKey),v=null==d?void 0:d.shiftKey;o&&h?(r=e,i.current=p,s.current=r):o&&v?r=Array.from(new Set([].concat((0,e0.A)(s.current||[]),(0,e0.A)(function(e){let{treeData:t,expandedKeys:n,startKey:r,endKey:o,fieldNames:l}=e,a=[],i=0;return r&&r===o?[r]:r&&o?(oS(t,e=>{if(2===i)return!1;if(e===r||e===o){if(a.push(e),0===i)i=1;else if(1===i)return i=2,!1}else 1===i&&a.push(e);return n.includes(e)},tK(l)),a):[]}({treeData:f,expandedKeys:m,startKey:p,endKey:i.current,fieldNames:l}))))):(r=[p],i.current=p,s.current=r),g.selectedNodes=function(e,t,n){let r=(0,e0.A)(t),o=[];return oS(e,(e,t)=>{let n=r.indexOf(e);return -1!==n&&(o.push(t),r.splice(n,1)),!!r.length},tK(n)),o}(f,r,l),null===(n=a.onSelect)||void 0===n||n.call(a,r,g),"selectedKeys"in a||u(r)},onExpand:(e,t)=>{var n;return"expandedKeys"in a||p(e),null===(n=a.onExpand)||void 0===n?void 0:n.call(a,e,t)}}))});ok.DirectoryTree=oI,ok.TreeNode=tW;var oO=n(8124);let oz=e=>{let{value:t,filterSearch:n,tablePrefixCls:r,locale:l,onChange:a}=e;return n?o.createElement("div",{className:`${r}-filter-dropdown-search`},o.createElement(oO.A,{prefix:o.createElement(T.A,null),placeholder:l.filterSearchPlaceholder,onChange:a,value:t,htmlSize:1,className:`${r}-filter-dropdown-search-input`})):null},oP=e=>{let{keyCode:t}=e;t===rr.A.ENTER&&e.stopPropagation()},oK=o.forwardRef((e,t)=>o.createElement("div",{className:e.className,onClick:e=>e.stopPropagation(),onKeyDown:oP,ref:t},e.children));function oR(e){let t=[];return(e||[]).forEach(e=>{let{value:n,children:r}=e;t.push(n),r&&(t=[].concat((0,e0.A)(t),(0,e0.A)(oR(r))))}),t}function oM(e,t){return("string"==typeof t||"number"==typeof t)&&(null==t?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()))}let oB=e=>{var t,n,r,l;let a;let{tablePrefixCls:s,prefixCls:c,column:d,dropdownPrefixCls:u,columnKey:m,filterOnClose:f,filterMultiple:g,filterMode:h="menu",filterSearch:v=!1,filterState:b,triggerFilter:x,locale:y,children:A,getPopupContainer:$,rootClassName:w}=e,{filterResetToDefaultFilteredValue:C,defaultFilteredValue:k,filterDropdownProps:S={},filterDropdownOpen:E,filterDropdownVisible:N,onFilterDropdownVisibleChange:j,onFilterDropdownOpenChange:I}=d,[O,z]=o.useState(!1),P=!!(b&&((null===(t=b.filteredKeys)||void 0===t?void 0:t.length)||b.forceFiltered)),K=e=>{var t;z(e),null===(t=S.onOpenChange)||void 0===t||t.call(S,e),null==I||I(e),null==j||j(e)},R=null!==(l=null!==(r=null!==(n=S.open)&&void 0!==n?n:E)&&void 0!==r?r:N)&&void 0!==l?l:O,M=null==b?void 0:b.filteredKeys,[B,T]=function(e){let t=o.useRef(e),n=(0,rH.A)();return[()=>t.current,e=>{t.current=e,n()}]}(M||[]),D=e=>{let{selectedKeys:t}=e;T(t)},H=(e,t)=>{let{node:n,checked:r}=t;g?D({selectedKeys:e}):D({selectedKeys:r&&n.key?[n.key]:[]})};o.useEffect(()=>{O&&D({selectedKeys:M||[]})},[M]);let[L,_]=o.useState([]),W=e=>{_(e)},[F,q]=o.useState(""),V=e=>{let{value:t}=e.target;q(t)};o.useEffect(()=>{O||q("")},[O]);let X=e=>{let t=(null==e?void 0:e.length)?e:null;if(null===t&&(!b||!b.filteredKeys)||(0,eo.A)(t,null==b?void 0:b.filteredKeys,!0))return null;x({column:d,key:m,filteredKeys:t})},U=()=>{K(!1),X(B())},G=function(){let{confirm:e,closeDropdown:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{confirm:!1,closeDropdown:!1};e&&X([]),t&&K(!1),q(""),C?T((k||[]).map(e=>String(e))):T([])},Y=ey()({[`${u}-menu-without-submenu`]:!(d.filters||[]).some(e=>{let{children:t}=e;return t})}),J=e=>{e.target.checked?T(oR(null==d?void 0:d.filters).map(e=>String(e))):T([])},Q=e=>{let{filters:t}=e;return(t||[]).map((e,t)=>{let n=String(e.value),r={title:e.text,key:void 0!==e.value?n:String(t)};return e.children&&(r.children=Q({filters:e.children})),r})},Z=e=>{var t;return Object.assign(Object.assign({},e),{text:e.title,value:e.key,children:(null===(t=e.children)||void 0===t?void 0:t.map(e=>Z(e)))||[]})},{direction:ee,renderEmpty:et}=o.useContext(t9.QO);if("function"==typeof d.filterDropdown)a=d.filterDropdown({prefixCls:`${u}-custom`,setSelectedKeys:e=>D({selectedKeys:e}),selectedKeys:B(),confirm:function(){let{closeDropdown:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{closeDropdown:!0};e&&K(!1),X(B())},clearFilters:G,filters:d.filters,visible:R,close:()=>{K(!1)}});else if(d.filterDropdown)a=d.filterDropdown;else{let e=B()||[];a=o.createElement(o.Fragment,null,(()=>{var t,n;let r=null!==(t=null==et?void 0:et("Table.filter"))&&void 0!==t?t:o.createElement(rL.A,{image:rL.A.PRESENTED_IMAGE_SIMPLE,description:y.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if(0===(d.filters||[]).length)return r;if("tree"===h)return o.createElement(o.Fragment,null,o.createElement(oz,{filterSearch:v,value:F,onChange:V,tablePrefixCls:s,locale:y}),o.createElement("div",{className:`${s}-filter-dropdown-tree`},g?o.createElement(p.A,{checked:e.length===oR(d.filters).length,indeterminate:e.length>0&&e.length<oR(d.filters).length,className:`${s}-filter-dropdown-checkall`,onChange:J},null!==(n=null==y?void 0:y.filterCheckall)&&void 0!==n?n:null==y?void 0:y.filterCheckAll):null,o.createElement(ok,{checkable:!0,selectable:!1,blockNode:!0,multiple:g,checkStrictly:!g,className:`${u}-menu`,onCheck:H,checkedKeys:e,selectedKeys:e,showIcon:!1,treeData:Q({filters:d.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:F.trim()?e=>"function"==typeof v?v(F,Z(e)):oM(F,e.title):void 0})));let l=function e(t){let{filters:n,prefixCls:r,filteredKeys:l,filterMultiple:a,searchValue:i,filterSearch:s}=t;return n.map((t,n)=>{let c=String(t.value);if(t.children)return{key:c||n,label:t.text,popupClassName:`${r}-dropdown-submenu`,children:e({filters:t.children,prefixCls:r,filteredKeys:l,filterMultiple:a,searchValue:i,filterSearch:s})};let d=a?p.A:nY.Ay,u={key:void 0!==t.value?c:n,label:o.createElement(o.Fragment,null,o.createElement(d,{checked:l.includes(c)}),o.createElement("span",null,t.text))};return i.trim()?"function"==typeof s?s(i,t)?u:null:oM(i,t.text)?u:null:u})}({filters:d.filters||[],filterSearch:v,prefixCls:c,filteredKeys:B(),filterMultiple:g,searchValue:F}),a=l.every(e=>null===e);return o.createElement(o.Fragment,null,o.createElement(oz,{filterSearch:v,value:F,onChange:V,tablePrefixCls:s,locale:y}),a?r:o.createElement(nM,{selectable:!0,multiple:g,prefixCls:`${u}-menu`,className:Y,onSelect:D,onDeselect:D,selectedKeys:e,getPopupContainer:$,openKeys:L,onOpenChange:W,items:l}))})(),o.createElement("div",{className:`${c}-dropdown-btns`},o.createElement(i.Ay,{type:"link",size:"small",disabled:C?(0,eo.A)((k||[]).map(e=>String(e)),e,!0):0===e.length,onClick:()=>G()},y.filterReset),o.createElement(i.Ay,{type:"primary",size:"small",onClick:U},y.filterConfirm)))}d.filterDropdown&&(a=o.createElement(nm,{selectable:void 0},a)),a=o.createElement(oK,{className:`${c}-dropdown`},a);let en=rD({trigger:["click"],placement:"rtl"===ee?"bottomLeft":"bottomRight",children:(()=>{let e;return e="function"==typeof d.filterIcon?d.filterIcon(P):d.filterIcon?d.filterIcon:o.createElement(rT,null),o.createElement("span",{role:"button",tabIndex:-1,className:ey()(`${c}-trigger`,{active:P}),onClick:e=>{e.stopPropagation()}},e)})(),getPopupContainer:$},Object.assign(Object.assign({},S),{rootClassName:ey()(w,S.rootClassName),open:R,onOpenChange:(e,t)=>{"trigger"===t.source&&(e&&void 0!==M&&T(M||[]),K(e),e||d.filterDropdown||!f||U())},dropdownRender:()=>"function"==typeof(null==S?void 0:S.dropdownRender)?S.dropdownRender(a):a}));return o.createElement("div",{className:`${c}-column`},o.createElement("span",{className:`${s}-column-title`},A),o.createElement(nF,Object.assign({},en)))},oT=(e,t,n)=>{let r=[];return(e||[]).forEach((e,o)=>{var l;let a=rK(o,n);if(e.filters||"filterDropdown"in e||"onFilter"in e){if("filteredValue"in e){let t=e.filteredValue;"filterDropdown"in e||(t=null!==(l=null==t?void 0:t.map(String))&&void 0!==l?l:t),r.push({column:e,key:rP(e,a),filteredKeys:t,forceFiltered:e.filtered})}else r.push({column:e,key:rP(e,a),filteredKeys:t&&e.defaultFilteredValue?e.defaultFilteredValue:void 0,forceFiltered:e.filtered})}"children"in e&&(r=[].concat((0,e0.A)(r),(0,e0.A)(oT(e.children,t,a))))}),r},oD=e=>{let t={};return e.forEach(e=>{let{key:n,filteredKeys:r,column:o}=e,{filters:l,filterDropdown:a}=o;if(a)t[n]=r||null;else if(Array.isArray(r)){let e=oR(l);t[n]=e.filter(e=>r.includes(String(e)))}else t[n]=null}),t},oH=(e,t,n)=>t.reduce((e,r)=>{let{column:{onFilter:o,filters:l},filteredKeys:a}=r;return o&&a&&a.length?e.map(e=>Object.assign({},e)).filter(e=>a.some(r=>{let a=oR(l),i=a.findIndex(e=>String(e)===String(r)),s=-1!==i?a[i]:r;return e[n]&&(e[n]=oH(e[n],t,n)),o(s,e)})):e},e),oL=e=>e.flatMap(e=>"children"in e?[e].concat((0,e0.A)(oL(e.children||[]))):[e]),o_=e=>{let{prefixCls:t,dropdownPrefixCls:n,mergedColumns:r,onFilterChange:l,getPopupContainer:a,locale:i,rootClassName:s}=e;(0,t1.rJ)("Table");let c=o.useMemo(()=>oL(r||[]),[r]),[d,u]=o.useState(()=>oT(c,!0)),m=o.useMemo(()=>{let e=oT(c,!1);if(0===e.length)return e;let t=!0;if(e.forEach(e=>{let{filteredKeys:n}=e;void 0!==n&&(t=!1)}),t){let e=(c||[]).map((e,t)=>rP(e,rK(t)));return d.filter(t=>{let{key:n}=t;return e.includes(n)}).map(t=>{let n=c[e.findIndex(e=>e===t.key)];return Object.assign(Object.assign({},t),{column:Object.assign(Object.assign({},t.column),n),forceFiltered:n.filtered})})}return e},[c,d]),p=o.useMemo(()=>oD(m),[m]),f=e=>{let t=m.filter(t=>{let{key:n}=t;return n!==e.key});t.push(e),u(t),l(oD(t),t)};return[e=>(function e(t,n,r,l,a,i,s,c,d){return r.map((r,u)=>{let m=rK(u,c),{filterOnClose:p=!0,filterMultiple:f=!0,filterMode:g,filterSearch:h}=r,v=r;if(v.filters||v.filterDropdown){let e=rP(v,m),c=l.find(t=>{let{key:n}=t;return e===n});v=Object.assign(Object.assign({},v),{title:l=>o.createElement(oB,{tablePrefixCls:t,prefixCls:`${t}-filter`,dropdownPrefixCls:n,column:v,columnKey:e,filterState:c,filterOnClose:p,filterMultiple:f,filterMode:g,filterSearch:h,triggerFilter:i,locale:a,getPopupContainer:s,rootClassName:d},rR(r.title,l))})}return"children"in v&&(v=Object.assign(Object.assign({},v),{children:e(t,n,v.children,l,a,i,s,m,d)})),v})})(t,n,e,m,i,f,a,void 0,s),m,p]},oW=(e,t,n)=>{let r=o.useRef({});return[function(o){var l;if(!r.current||r.current.data!==e||r.current.childrenColumnName!==t||r.current.getRowKey!==n){let o=new Map;(function e(r){r.forEach((r,l)=>{let a=n(r,l);o.set(a,r),r&&"object"==typeof r&&t in r&&e(r[t]||[])})})(e),r.current={data:e,childrenColumnName:t,kvMap:o,getRowKey:n}}return null===(l=r.current.kvMap)||void 0===l?void 0:l.get(o)}]};var oF=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let oq=function(e,t,n){let r=n&&"object"==typeof n?n:{},{total:l=0}=r,a=oF(r,["total"]),[i,s]=(0,o.useState)(()=>({current:"defaultCurrent"in a?a.defaultCurrent:1,pageSize:"defaultPageSize"in a?a.defaultPageSize:10})),c=rD(i,a,{total:l>0?l:e}),d=Math.ceil((l||e)/c.pageSize);c.current>d&&(c.current=d||1);let u=(e,t)=>{s({current:null!=e?e:1,pageSize:t||c.pageSize})};return!1===n?[{},()=>{}]:[Object.assign(Object.assign({},c),{onChange:(e,r)=>{var o;n&&(null===(o=n.onChange)||void 0===o||o.call(n,e,r)),u(e,r),t(e,r||(null==c?void 0:c.pageSize))}}),u]},oV={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"};var oX=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oV}))});let oU={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"};var oG=o.forwardRef(function(e,t){return o.createElement(x.A,(0,v.A)({},e,{ref:t,icon:oU}))});let oY="ascend",oJ="descend",oQ=e=>"object"==typeof e.sorter&&"number"==typeof e.sorter.multiple&&e.sorter.multiple,oZ=e=>"function"==typeof e?e:!!e&&"object"==typeof e&&!!e.compare&&e.compare,o0=(e,t)=>t?e[e.indexOf(t)+1]:e[0],o1=(e,t,n)=>{let r=[],o=(e,t)=>{r.push({column:e,key:rP(e,t),multiplePriority:oQ(e),sortOrder:e.sortOrder})};return(e||[]).forEach((e,l)=>{let a=rK(l,n);e.children?("sortOrder"in e&&o(e,a),r=[].concat((0,e0.A)(r),(0,e0.A)(o1(e.children,t,a)))):e.sorter&&("sortOrder"in e?o(e,a):t&&e.defaultSortOrder&&r.push({column:e,key:rP(e,a),multiplePriority:oQ(e),sortOrder:e.defaultSortOrder}))}),r},o2=(e,t,n,r,l,a,i,s)=>(t||[]).map((t,c)=>{let d=rK(c,s),u=t;if(u.sorter){let s;let c=u.sortDirections||l,m=void 0===u.showSorterTooltip?i:u.showSorterTooltip,p=rP(u,d),g=n.find(e=>{let{key:t}=e;return t===p}),h=g?g.sortOrder:null,v=o0(c,h);if(t.sortIcon)s=t.sortIcon({sortOrder:h});else{let t=c.includes(oY)&&o.createElement(oG,{className:ey()(`${e}-column-sorter-up`,{active:h===oY})}),n=c.includes(oJ)&&o.createElement(oX,{className:ey()(`${e}-column-sorter-down`,{active:h===oJ})});s=o.createElement("span",{className:ey()(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(t&&n)})},o.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},t,n))}let{cancelSort:b,triggerAsc:x,triggerDesc:y}=a||{},A=b;v===oJ?A=y:v===oY&&(A=x);let $="object"==typeof m?Object.assign({title:A},m):{title:A};u=Object.assign(Object.assign({},u),{className:ey()(u.className,{[`${e}-column-sort`]:h}),title:n=>{let r=`${e}-column-sorters`,l=o.createElement("span",{className:`${e}-column-title`},rR(t.title,n)),a=o.createElement("div",{className:r},l,s);return m?"boolean"!=typeof m&&(null==m?void 0:m.target)==="sorter-icon"?o.createElement("div",{className:`${r} ${e}-column-sorters-tooltip-target-sorter`},l,o.createElement(f.A,Object.assign({},$),s)):o.createElement(f.A,Object.assign({},$),a):a},onHeaderCell:n=>{var o;let l=(null===(o=t.onHeaderCell)||void 0===o?void 0:o.call(t,n))||{},a=l.onClick,i=l.onKeyDown;l.onClick=e=>{r({column:t,key:p,sortOrder:v,multiplePriority:oQ(t)}),null==a||a(e)},l.onKeyDown=e=>{e.keyCode===rr.A.ENTER&&(r({column:t,key:p,sortOrder:v,multiplePriority:oQ(t)}),null==i||i(e))};let s=rM(t.title,{}),c=null==s?void 0:s.toString();return h&&(l["aria-sort"]="ascend"===h?"ascending":"descending"),l["aria-label"]=c||"",l.className=ey()(l.className,`${e}-column-has-sorters`),l.tabIndex=0,t.ellipsis&&(l.title=(null!=s?s:"").toString()),l}})}return"children"in u&&(u=Object.assign(Object.assign({},u),{children:o2(e,u.children,n,r,l,a,i,d)})),u}),o3=e=>{let{column:t,sortOrder:n}=e;return{column:t,order:n,field:t.dataIndex,columnKey:t.key}},o4=e=>{let t=e.filter(e=>{let{sortOrder:t}=e;return t}).map(o3);if(0===t.length&&e.length){let t=e.length-1;return Object.assign(Object.assign({},o3(e[t])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},o8=(e,t,n)=>{let r=t.slice().sort((e,t)=>t.multiplePriority-e.multiplePriority),o=e.slice(),l=r.filter(e=>{let{column:{sorter:t},sortOrder:n}=e;return oZ(t)&&n});return l.length?o.sort((e,t)=>{for(let n=0;n<l.length;n+=1){let{column:{sorter:r},sortOrder:o}=l[n],a=oZ(r);if(a&&o){let n=a(e,t,o);if(0!==n)return o===oY?n:-n}}return 0}).map(e=>{let r=e[n];return r?Object.assign(Object.assign({},e),{[n]:o8(r,t,n)}):e}):o},o5=e=>{let{prefixCls:t,mergedColumns:n,sortDirections:r,tableLocale:l,showSorterTooltip:a,onSorterChange:i}=e,[s,c]=o.useState(o1(n,!0)),d=(e,t)=>{let n=[];return e.forEach((e,r)=>{let o=rK(r,t);if(n.push(rP(e,o)),Array.isArray(e.children)){let t=d(e.children,o);n.push.apply(n,(0,e0.A)(t))}}),n},u=o.useMemo(()=>{let e=!0,t=o1(n,!1);if(!t.length){let e=d(n);return s.filter(t=>{let{key:n}=t;return e.includes(n)})}let r=[];function o(t){e?r.push(t):r.push(Object.assign(Object.assign({},t),{sortOrder:null}))}let l=null;return t.forEach(t=>{null===l?(o(t),t.sortOrder&&(!1===t.multiplePriority?e=!1:l=!0)):(l&&!1!==t.multiplePriority||(e=!1),o(t))}),r},[n,s]),m=o.useMemo(()=>{var e,t;let n=u.map(e=>{let{column:t,sortOrder:n}=e;return{column:t,order:n}});return{sortColumns:n,sortColumn:null===(e=n[0])||void 0===e?void 0:e.column,sortOrder:null===(t=n[0])||void 0===t?void 0:t.order}},[u]),p=e=>{let t;c(t=!1!==e.multiplePriority&&u.length&&!1!==u[0].multiplePriority?[].concat((0,e0.A)(u.filter(t=>{let{key:n}=t;return n!==e.key})),[e]):[e]),i(o4(t),t)};return[e=>o2(t,e,u,p,r,l,a),u,m,()=>o4(u)]},o6=(e,t)=>e.map(e=>{let n=Object.assign({},e);return n.title=rR(e.title,t),"children"in n&&(n.children=o6(n.children,t)),n}),o7=e=>[o.useCallback(t=>o6(t,e),[e])],o9=em(tg,(e,t)=>{let{_renderTimes:n}=e,{_renderTimes:r}=t;return n!==r}),le=em(tk,(e,t)=>{let{_renderTimes:n}=e,{_renderTimes:r}=t;return n!==r}),lt=e=>{let{componentCls:t,lineWidth:n,lineType:r,tableBorderColor:o,tableHeaderBg:l,tablePaddingVertical:a,tablePaddingHorizontal:i,calc:s}=e,c=`${(0,np.zA)(n)} ${r} ${o}`,d=(e,r,o)=>({[`&${t}-${e}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{[`
            > table > tbody > tr > th,
            > table > tbody > tr > td
          `]:{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,np.zA)(s(r).mul(-1).equal())}
              ${(0,np.zA)(s(s(o).add(n)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:c,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:c,borderTop:c,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{[`
                > thead > tr > th,
                > thead > tr > td,
                > tbody > tr > th,
                > tbody > tr > td,
                > tfoot > tr > th,
                > tfoot > tr > td
              `]:{borderInlineEnd:c},"> thead":{"> tr:not(:last-child) > th":{borderBottom:c},"> tr > th::before":{backgroundColor:"transparent !important"}},[`
                > thead > tr,
                > tbody > tr,
                > tfoot > tr
              `]:{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:c}},[`
                > tbody > tr > th,
                > tbody > tr > td
              `]:{[`> ${t}-expanded-row-fixed`]:{margin:`${(0,np.zA)(s(a).mul(-1).equal())} ${(0,np.zA)(s(s(i).add(n)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:n,bottom:0,borderInlineEnd:c,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},d("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),d("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:c,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${(0,np.zA)(n)} 0 ${(0,np.zA)(n)} ${l}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:c}}}},ln=e=>{let{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},ng.L9),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},lr=e=>{let{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,[`
          &:hover > th,
          &:hover > td,
        `]:{background:e.colorBgContainer}}}}},lo=e=>{let{componentCls:t,antCls:n,motionDurationSlow:r,lineWidth:o,paddingXS:l,lineType:a,tableBorderColor:i,tableExpandIconBg:s,tableExpandColumnWidth:c,borderRadius:d,tablePaddingVertical:u,tablePaddingHorizontal:m,tableExpandedRowBg:p,paddingXXS:f,expandIconMarginTop:g,expandIconSize:h,expandIconHalfInner:v,expandIconScale:b,calc:x}=e,y=`${(0,np.zA)(o)} ${a} ${i}`,A=x(f).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:c},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},(0,ng.Y1)(e)),{position:"relative",float:"left",width:h,height:h,color:"inherit",lineHeight:(0,np.zA)(h),background:s,border:y,borderRadius:d,transform:`scale(${b})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${r} ease-out`,content:'""'},"&::before":{top:v,insetInlineEnd:A,insetInlineStart:A,height:o},"&::after":{top:A,bottom:A,insetInlineStart:v,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:g,marginInlineEnd:l},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:p}},[`${n}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${(0,np.zA)(x(u).mul(-1).equal())} ${(0,np.zA)(x(m).mul(-1).equal())}`,padding:`${(0,np.zA)(u)} ${(0,np.zA)(m)}`}}}},ll=e=>{let{componentCls:t,antCls:n,iconCls:r,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:l,paddingXXS:a,paddingXS:i,colorText:s,lineWidth:c,lineType:d,tableBorderColor:u,headerIconColor:m,fontSizeSM:p,tablePaddingHorizontal:f,borderRadius:g,motionDurationSlow:h,colorTextDescription:v,colorPrimary:b,tableHeaderFilterActiveBg:x,colorTextDisabled:y,tableFilterDropdownBg:A,tableFilterDropdownHeight:$,controlItemBgHover:w,controlItemBgActive:C,boxShadowSecondary:k,filterDropdownMenuBg:S,calc:E}=e,N=`${n}-dropdown`,j=`${t}-filter-dropdown`,I=`${n}-tree`,O=`${(0,np.zA)(c)} ${d} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:E(a).mul(-1).equal(),marginInline:`${(0,np.zA)(a)} ${(0,np.zA)(E(f).div(2).mul(-1).equal())}`,padding:`0 ${(0,np.zA)(a)}`,color:m,fontSize:p,borderRadius:g,cursor:"pointer",transition:`all ${h}`,"&:hover":{color:v,background:x},"&.active":{color:b}}}},{[`${n}-dropdown`]:{[j]:Object.assign(Object.assign({},(0,ng.dF)(e)),{minWidth:o,backgroundColor:A,borderRadius:g,boxShadow:k,overflow:"hidden",[`${N}-menu`]:{maxHeight:$,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:S,"&:empty::after":{display:"block",padding:`${(0,np.zA)(i)} 0`,color:y,fontSize:p,textAlign:"center",content:'"Not Found"'}},[`${j}-tree`]:{paddingBlock:`${(0,np.zA)(i)} 0`,paddingInline:i,[I]:{padding:0},[`${I}-treenode ${I}-node-content-wrapper:hover`]:{backgroundColor:w},[`${I}-treenode-checkbox-checked ${I}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:C}}},[`${j}-search`]:{padding:i,borderBottom:O,"&-input":{input:{minWidth:l},[r]:{color:y}}},[`${j}-checkall`]:{width:"100%",marginBottom:a,marginInlineStart:a},[`${j}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${(0,np.zA)(E(i).sub(c).equal())} ${(0,np.zA)(i)}`,overflow:"hidden",borderTop:O}})}},{[`${n}-dropdown ${j}, ${j}-submenu`]:{[`${n}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:s},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},la=e=>{let{componentCls:t,lineWidth:n,colorSplit:r,motionDurationSlow:o,zIndexTableFixed:l,tableBg:a,zIndexTableSticky:i,calc:s}=e;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:l,background:a},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:s(n).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:s(n).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:s(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${r}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${r}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${r}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${r}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},li=e=>{let{componentCls:t,antCls:n,margin:r}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${n}-pagination`]:{margin:`${(0,np.zA)(r)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},ls=e=>{let{componentCls:t,tableRadius:n}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${(0,np.zA)(n)} ${(0,np.zA)(n)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:n,borderStartEndRadius:n,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:n},"> *:last-child":{borderStartEndRadius:n}}},"&-footer":{borderRadius:`0 0 ${(0,np.zA)(n)} ${(0,np.zA)(n)}`}}}}},lc=e=>{let{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},ld=e=>{let{componentCls:t,antCls:n,iconCls:r,fontSizeIcon:o,padding:l,paddingXS:a,headerIconColor:i,headerIconHoverColor:s,tableSelectionColumnWidth:c,tableSelectedRowBg:d,tableSelectedRowHoverBg:u,tableRowHoverBg:m,tablePaddingHorizontal:p,calc:f}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:c,[`&${t}-selection-col-with-dropdown`]:{width:f(c).add(o).add(f(l).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:f(c).add(f(a).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:f(c).add(o).add(f(l).div(4)).add(f(a).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${n}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:f(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:(0,np.zA)(f(p).div(4).equal()),[r]:{color:i,fontSize:o,verticalAlign:"baseline","&:hover":{color:s}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:d,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:m}}}}}},lu=e=>{let{componentCls:t,tableExpandColumnWidth:n,calc:r}=e,o=(e,o,l,a)=>({[`${t}${t}-${e}`]:{fontSize:a,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${(0,np.zA)(o)} ${(0,np.zA)(l)}`},[`${t}-filter-trigger`]:{marginInlineEnd:(0,np.zA)(r(l).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${(0,np.zA)(r(o).mul(-1).equal())} ${(0,np.zA)(r(l).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:(0,np.zA)(r(o).mul(-1).equal()),marginInline:`${(0,np.zA)(r(n).sub(l).equal())} ${(0,np.zA)(r(l).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:(0,np.zA)(r(l).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},lm=e=>{let{componentCls:t,marginXXS:n,fontSizeIcon:r,headerIconColor:o,headerIconHoverColor:l}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:n,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:r,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:l}}}},lp=e=>{let{componentCls:t,opacityLoading:n,tableScrollThumbBg:r,tableScrollThumbBgHover:o,tableScrollThumbSize:l,tableScrollBg:a,zIndexTableSticky:i,stickyScrollBarBorderRadius:s,lineWidth:c,lineType:d,tableBorderColor:u}=e,m=`${(0,np.zA)(c)} ${d} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${(0,np.zA)(l)} !important`,zIndex:i,display:"flex",alignItems:"center",background:a,borderTop:m,opacity:n,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:l,backgroundColor:r,borderRadius:s,transition:`all ${e.motionDurationSlow}, transform 0s`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},lf=e=>{let{componentCls:t,lineWidth:n,tableBorderColor:r,calc:o}=e,l=`${(0,np.zA)(n)} ${e.lineType} ${r}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:l}}},[`div${t}-summary`]:{boxShadow:`0 ${(0,np.zA)(o(n).mul(-1).equal())} 0 ${r}`}}}},lg=e=>{let{componentCls:t,motionDurationMid:n,lineWidth:r,lineType:o,tableBorderColor:l,calc:a}=e,i=`${(0,np.zA)(r)} ${o} ${l}`,s=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:i,transition:`background ${n}`},[`${t}-expanded-row`]:{[`${s}${s}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${(0,np.zA)(r)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:a(r).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}},lh=e=>{let{componentCls:t,fontWeightStrong:n,tablePaddingVertical:r,tablePaddingHorizontal:o,tableExpandColumnWidth:l,lineWidth:a,lineType:i,tableBorderColor:s,tableFontSize:c,tableBg:d,tableRadius:u,tableHeaderTextColor:m,motionDurationMid:p,tableHeaderBg:f,tableHeaderCellSplitColor:g,tableFooterTextColor:h,tableFooterBg:v,calc:b}=e,x=`${(0,np.zA)(a)} ${i} ${s}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},(0,ng.t6)()),{[t]:Object.assign(Object.assign({},(0,ng.dF)(e)),{fontSize:c,background:d,borderRadius:`${(0,np.zA)(u)} ${(0,np.zA)(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${(0,np.zA)(u)} ${(0,np.zA)(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${(0,np.zA)(r)} ${(0,np.zA)(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${(0,np.zA)(r)} ${(0,np.zA)(o)}`},[`${t}-thead`]:{[`
          > tr > th,
          > tr > td
        `]:{position:"relative",color:m,fontWeight:n,textAlign:"start",background:f,borderBottom:x,transition:`background ${p} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:g,transform:"translateY(-50%)",transition:`background-color ${p}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${p}, border-color ${p}`,borderBottom:x,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:(0,np.zA)(b(r).mul(-1).equal()),marginInline:`${(0,np.zA)(b(l).sub(o).equal())}
                ${(0,np.zA)(b(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:m,fontWeight:n,textAlign:"start",background:f,borderBottom:x,transition:`background ${p} ease`}}},[`${t}-footer`]:{padding:`${(0,np.zA)(r)} ${(0,np.zA)(o)}`,color:h,background:v}})}},lv=(0,nx.OF)("Table",e=>{let{colorTextHeading:t,colorSplit:n,colorBgContainer:r,controlInteractiveSize:o,headerBg:l,headerColor:a,headerSortActiveBg:i,headerSortHoverBg:s,bodySortBg:c,rowHoverBg:d,rowSelectedBg:u,rowSelectedHoverBg:m,rowExpandedBg:p,cellPaddingBlock:f,cellPaddingInline:g,cellPaddingBlockMD:h,cellPaddingInlineMD:v,cellPaddingBlockSM:b,cellPaddingInlineSM:x,borderColor:y,footerBg:A,footerColor:$,headerBorderRadius:w,cellFontSize:C,cellFontSizeMD:k,cellFontSizeSM:S,headerSplitColor:E,fixedHeaderSortActiveBg:N,headerFilterHoverBg:j,filterDropdownBg:I,expandIconBg:O,selectionColumnWidth:z,stickyScrollBarBg:P,calc:K}=e,R=(0,ny.oX)(e,{tableFontSize:C,tableBg:r,tableRadius:w,tablePaddingVertical:f,tablePaddingHorizontal:g,tablePaddingVerticalMiddle:h,tablePaddingHorizontalMiddle:v,tablePaddingVerticalSmall:b,tablePaddingHorizontalSmall:x,tableBorderColor:y,tableHeaderTextColor:a,tableHeaderBg:l,tableFooterTextColor:$,tableFooterBg:A,tableHeaderCellSplitColor:E,tableHeaderSortBg:i,tableHeaderSortHoverBg:s,tableBodySortBg:c,tableFixedHeaderSortActiveBg:N,tableHeaderFilterActiveBg:j,tableFilterDropdownBg:I,tableRowHoverBg:d,tableSelectedRowBg:u,tableSelectedRowHoverBg:m,zIndexTableFixed:2,zIndexTableSticky:K(2).add(1).equal({unit:!1}),tableFontSizeMiddle:k,tableFontSizeSmall:S,tableSelectionColumnWidth:z,tableExpandIconBg:O,tableExpandColumnWidth:K(o).add(K(e.padding).mul(2)).equal(),tableExpandedRowBg:p,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:P,tableScrollThumbBgHover:t,tableScrollBg:n});return[lh(R),li(R),lf(R),lm(R),ll(R),lt(R),ls(R),lo(R),lf(R),lr(R),ld(R),la(R),lp(R),ln(R),lu(R),lc(R),lg(R)]},e=>{let{colorFillAlter:t,colorBgContainer:n,colorTextHeading:r,colorFillSecondary:o,colorFillContent:l,controlItemBgActive:a,controlItemBgActiveHover:i,padding:s,paddingSM:c,paddingXS:d,colorBorderSecondary:u,borderRadiusLG:m,controlHeight:p,colorTextPlaceholder:f,fontSize:g,fontSizeSM:h,lineHeight:v,lineWidth:b,colorIcon:x,colorIconHover:y,opacityLoading:A,controlInteractiveSize:$}=e,w=new nf.Y(o).onBackground(n).toHexString(),C=new nf.Y(l).onBackground(n).toHexString(),k=new nf.Y(t).onBackground(n).toHexString(),S=new nf.Y(x),E=new nf.Y(y),N=$/2-b,j=2*N+3*b;return{headerBg:k,headerColor:r,headerSortActiveBg:w,headerSortHoverBg:C,bodySortBg:k,rowHoverBg:k,rowSelectedBg:a,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:s,cellPaddingInline:s,cellPaddingBlockMD:c,cellPaddingInlineMD:d,cellPaddingBlockSM:d,cellPaddingInlineSM:d,borderColor:u,headerBorderRadius:m,footerBg:k,footerColor:r,cellFontSize:g,cellFontSizeMD:g,cellFontSizeSM:g,headerSplitColor:u,fixedHeaderSortActiveBg:w,headerFilterHoverBg:l,filterDropdownMenuBg:n,filterDropdownBg:n,expandIconBg:n,selectionColumnWidth:p,stickyScrollBarBg:f,stickyScrollBarBorderRadius:100,expandIconMarginTop:(g*v-3*b)/2-Math.ceil((1.4*h-3*b)/2),headerIconColor:S.clone().setA(S.a*A).toRgbString(),headerIconHoverColor:E.clone().setA(E.a*A).toRgbString(),expandIconHalfInner:N,expandIconSize:j,expandIconScale:$/j}},{unitless:{expandIconScale:!0}}),lb=[],lx=o.forwardRef((e,t)=>{var n,r,l;let a,i,c;let{prefixCls:d,className:u,rootClassName:m,style:p,size:f,bordered:g,dropdownPrefixCls:h,dataSource:v,pagination:b,rowSelection:x,rowKey:y="key",rowClassName:A,columns:$,children:w,childrenColumnName:C,onChange:k,getPopupContainer:S,loading:E,expandIcon:N,expandable:j,expandedRowRender:I,expandIconColumnIndex:O,indentSize:z,scroll:P,sortDirections:K,locale:R,showSorterTooltip:M={target:"full-header"},virtual:B}=e;(0,t1.rJ)("Table");let T=o.useMemo(()=>$||te(w),[$,w]),D=o.useMemo(()=>T.some(e=>e.responsive),[T]),H=(0,n6.A)(D),L=o.useMemo(()=>{let e=new Set(Object.keys(H).filter(e=>H[e]));return T.filter(t=>!t.responsive||t.responsive.some(t=>e.has(t)))},[T,H]),_=(0,tI.A)(e,["className","style","columns"]),{locale:W=n7.A,direction:F,table:q,renderEmpty:V,getPrefixCls:X,getPopupContainer:U}=o.useContext(t9.QO),G=(0,n5.A)(f),Y=Object.assign(Object.assign({},W.Table),R),J=v||lb,Q=X("table",d),Z=X("dropdown",h),[,et]=(0,nB.Ay)(),en=(0,ne.A)(Q),[er,eo,el]=lv(Q,en),ea=Object.assign(Object.assign({childrenColumnName:C,expandIconColumnIndex:O},j),{expandIcon:null!==(n=null==j?void 0:j.expandIcon)&&void 0!==n?n:null===(r=null==q?void 0:q.expandable)||void 0===r?void 0:r.expandIcon}),{childrenColumnName:ei="children"}=ea,es=o.useMemo(()=>J.some(e=>null==e?void 0:e[ei])?"nest":I||(null==j?void 0:j.expandedRowRender)?"row":null,[J]),ec={body:o.useRef(null)},ed=o.useRef(null),eu=o.useRef(null);l=()=>Object.assign(Object.assign({},eu.current),{nativeElement:ed.current}),(0,o.useImperativeHandle)(t,()=>{let e=l(),{nativeElement:t}=e;return"undefined"!=typeof Proxy?new Proxy(t,{get:(t,n)=>e[n]?e[n]:Reflect.get(t,n)}):(t._antProxy=t._antProxy||{},Object.keys(e).forEach(n=>{if(!(n in t._antProxy)){let r=t[n];t._antProxy[n]=r,t[n]=e[n]}}),t)});let em=o.useMemo(()=>"function"==typeof y?y:e=>null==e?void 0:e[y],[y]),[ep]=oW(J,ei,em),ef={},eg=function(e,t){var n,r,o,l;let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=Object.assign(Object.assign({},ef),e);a&&(null===(n=ef.resetPagination)||void 0===n||n.call(ef),(null===(r=i.pagination)||void 0===r?void 0:r.current)&&(i.pagination.current=1),b&&(null===(o=b.onChange)||void 0===o||o.call(b,1,null===(l=i.pagination)||void 0===l?void 0:l.pageSize))),P&&!1!==P.scrollToFirstRowOnChange&&ec.body.current&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{getContainer:n=()=>window,callback:r,duration:o=450}=t,l=n(),a=n4(l),i=Date.now(),s=()=>{let t=Date.now()-i,n=function(e,t,n,r){let o=n-t;return(e/=r/2)<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}(t>o?o:t,a,e,o);null!=l&&l===l.window?l.scrollTo(window.pageXOffset,n):l instanceof Document||"HTMLDocument"===l.constructor.name?l.documentElement.scrollTop=n:l.scrollTop=n,t<o?(0,ti.A)(s):"function"==typeof r&&r()};(0,ti.A)(s)}(0,{getContainer:()=>ec.body.current}),null==k||k(i.pagination,i.filters,i.sorter,{currentDataSource:oH(o8(J,i.sorterStates,ei),i.filterStates,ei),action:t})},[eh,ev,eb,ex]=o5({prefixCls:Q,mergedColumns:L,onSorterChange:(e,t)=>{eg({sorter:e,sorterStates:t},"sort",!1)},sortDirections:K||["ascend","descend"],tableLocale:Y,showSorterTooltip:M}),eA=o.useMemo(()=>o8(J,ev,ei),[J,ev]);ef.sorter=ex(),ef.sorterStates=ev;let[e$,ew,eC]=o_({prefixCls:Q,locale:Y,dropdownPrefixCls:Z,mergedColumns:L,onFilterChange:(e,t)=>{eg({filters:e,filterStates:t},"filter",!0)},getPopupContainer:S||U,rootClassName:ey()(m,en)}),ek=oH(eA,ew,ei);ef.filters=eC,ef.filterStates=ew;let[eS]=o7(o.useMemo(()=>{let e={};return Object.keys(eC).forEach(t=>{null!==eC[t]&&(e[t]=eC[t])}),Object.assign(Object.assign({},eb),{filters:e})},[eb,eC])),[eE,eN]=oq(ek.length,(e,t)=>{eg({pagination:Object.assign(Object.assign({},ef.pagination),{current:e,pageSize:t})},"paginate")},b);ef.pagination=!1===b?{}:function(e,t){let n={current:e.current,pageSize:e.pageSize};return Object.keys(t&&"object"==typeof t?t:{}).forEach(t=>{let r=e[t];"function"!=typeof r&&(n[t]=r)}),n}(eE,b),ef.resetPagination=eN;let ej=o.useMemo(()=>{if(!1===b||!eE.pageSize)return ek;let{current:e=1,total:t,pageSize:n=10}=eE;return ek.length<t?ek.length>n?ek.slice((e-1)*n,e*n):ek:ek.slice((e-1)*n,e*n)},[!!b,ek,null==eE?void 0:eE.current,null==eE?void 0:eE.pageSize,null==eE?void 0:eE.total]),[eI,eO]=n3({prefixCls:Q,data:ek,pageData:ej,getRowKey:em,getRecordByKey:ep,expandType:es,childrenColumnName:ei,locale:Y,getPopupContainer:S||U},x);ea.__PARENT_RENDER_ICON__=ea.expandIcon,ea.expandIcon=ea.expandIcon||N||function(e){return t=>{let{prefixCls:n,onExpand:r,record:l,expanded:a,expandable:i}=t,s=`${n}-row-expand-icon`;return o.createElement("button",{type:"button",onClick:e=>{r(l,e),e.stopPropagation()},className:ey()(s,{[`${s}-spaced`]:!i,[`${s}-expanded`]:i&&a,[`${s}-collapsed`]:i&&!a}),"aria-label":a?e.collapse:e.expand,"aria-expanded":a})}}(Y),"nest"===es&&void 0===ea.expandIconColumnIndex?ea.expandIconColumnIndex=x?1:0:ea.expandIconColumnIndex>0&&x&&(ea.expandIconColumnIndex-=1),"number"!=typeof ea.indentSize&&(ea.indentSize="number"==typeof z?z:15);let ez=o.useCallback(e=>eS(eI(e$(eh(e)))),[eh,e$,eI]);if(!1!==b&&(null==eE?void 0:eE.total)){let e;e=eE.size?eE.size:"small"===G||"middle"===G?"small":void 0;let t=t=>o.createElement(rz,Object.assign({},eE,{className:ey()(`${Q}-pagination ${Q}-pagination-${t}`,eE.className),size:e})),n="rtl"===F?"left":"right",{position:r}=eE;if(null!==r&&Array.isArray(r)){let e=r.find(e=>e.includes("top")),o=r.find(e=>e.includes("bottom")),l=r.every(e=>"none"==`${e}`);e||o||l||(i=t(n)),e&&(a=t(e.toLowerCase().replace("top",""))),o&&(i=t(o.toLowerCase().replace("bottom","")))}else i=t(n)}"boolean"==typeof E?c={spinning:E}:"object"==typeof E&&(c=Object.assign({spinning:!0},E));let eP=ey()(el,en,`${Q}-wrapper`,null==q?void 0:q.className,{[`${Q}-wrapper-rtl`]:"rtl"===F},u,m,eo),eK=Object.assign(Object.assign({},null==q?void 0:q.style),p),eR=void 0!==(null==R?void 0:R.emptyText)?R.emptyText:(null==V?void 0:V("Table"))||o.createElement(n8.A,{componentName:"Table"}),eM={},eB=o.useMemo(()=>{let{fontSize:e,lineHeight:t,lineWidth:n,padding:r,paddingXS:o,paddingSM:l}=et,a=Math.floor(e*t);switch(G){case"middle":return 2*l+a+n;case"small":return 2*o+a+n;default:return 2*r+a+n}},[et,G]);return B&&(eM.listItemHeight=eB),er(o.createElement("div",{ref:ed,className:eP,style:eK},o.createElement(s.A,Object.assign({spinning:!1},c),a,o.createElement(B?le:o9,Object.assign({},eM,_,{ref:eu,columns:L,direction:F,expandable:ea,prefixCls:Q,className:ey()({[`${Q}-middle`]:"middle"===G,[`${Q}-small`]:"small"===G,[`${Q}-bordered`]:g,[`${Q}-empty`]:0===J.length},el,en,eo),data:ej,rowKey:em,rowClassName:(e,t,n)=>{let r;return r="function"==typeof A?ey()(A(e,t,n)):ey()(A),ey()({[`${Q}-row-selected`]:eO.has(em(e,t))},r)},emptyText:eR,internalHooks:ee,internalRefs:ec,transformColumns:ez,getContainerWidth:(e,t)=>{let n=e.querySelector(`.${Q}-container`),r=t;if(n){let e=getComputedStyle(n);r=t-parseInt(e.borderLeftWidth,10)-parseInt(e.borderRightWidth,10)}return r}})),i)))}),ly=o.forwardRef((e,t)=>{let n=o.useRef(0);return n.current+=1,o.createElement(lx,Object.assign({},e,{ref:t,_renderTimes:n.current}))});ly.SELECTION_COLUMN=nJ,ly.EXPAND_COLUMN=Z,ly.SELECTION_ALL=nQ,ly.SELECTION_INVERT=nZ,ly.SELECTION_NONE=n0,ly.Column=e=>null,ly.ColumnGroup=e=>null,ly.Summary=eP;var lA=n(60380),l$=n(44209),lw=n(97245),lC=n(46211);let lk=({isOpen:e,onClose:t,store:n,onSuccess:l})=>{let[a]=H.A.useForm(),[c,m]=(0,o.useState)(null),[p,f]=(0,o.useState)([]),{data:g,isLoading:v}=(0,lw.VL)({page:1,limit:100}),{data:b,isLoading:x,refetch:y}=(0,lC.lr)(c||0,{skip:!c}),[A,{isLoading:$}]=(0,lC.tU)(),[w,{isLoading:C}]=(0,lC.ih)(),[k,{isLoading:S}]=(0,lC.PK)();(0,o.useEffect)(()=>{b?.data?f(b.data):f([])},[b]);let E=async()=>{try{if(!n){(0,z.r)("error","No store selected");return}let e=await a.validateFields(),t=await A({userId:e.userId,storeId:n.id,isDefault:e.isDefault}).unwrap();t.success?((0,z.r)("success","User associated with store successfully"),y(),l&&l()):(0,z.r)("error",t.message||"Failed to associate user with store")}catch(e){(0,z.r)("error",e.data?.message||"An error occurred")}},N=async(e,t)=>{try{let n=await w({userId:e,storeId:t}).unwrap();n.success?((0,z.r)("success","Default store set successfully"),y(),l&&l()):(0,z.r)("error",n.message||"Failed to set default store")}catch(e){(0,z.r)("error",e.data?.message||"An error occurred")}},j=async(e,t)=>{try{if(window.confirm("Are you sure you want to remove this user from the store?")){let n=await k({userId:e,storeId:t}).unwrap();n.success?((0,z.r)("success","User removed from store successfully"),y(),l&&l()):(0,z.r)("error",n.message||"Failed to remove user from store")}}catch(e){(0,z.r)("error",e.data?.message||"An error occurred")}},I=[{title:"Store",dataIndex:"name",key:"name",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-2"}),(0,r.jsx)("span",{children:e})]})},{title:"Default",dataIndex:"isDefault",key:"isDefault",render:(e,t)=>(0,r.jsx)(J.A,{checked:e,onChange:()=>N(c,t.id),loading:C,checkedChildren:(0,r.jsx)(lA.A,{}),unCheckedChildren:(0,r.jsx)(l$.A,{})})},{title:"Actions",key:"actions",render:(e,t)=>(0,r.jsx)(i.Ay,{danger:!0,onClick:()=>j(c,t.id),loading:S,className:"border-red-600 bg-red-600 text-white hover:bg-red-700",children:"Remove"})}],O=(0,r.jsx)("div",{className:"flex justify-end space-x-2",children:(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"})});return(0,r.jsx)(V.A,{title:`Manage Users for ${n?.name||"Store"}`,isOpen:e,onClose:t,width:"500px",footer:O,children:(0,r.jsxs)("div",{className:"bg-white p-6 pt-20",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800",children:"Associate User with Store"}),(0,r.jsxs)(H.A,{form:a,layout:"vertical",onFinish:E,className:"store-form",children:[(0,r.jsx)(H.A.Item,{name:"userId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," User"]}),rules:[{required:!0,message:"Please select a user"}],children:(0,r.jsx)(Q.A,{placeholder:"Select a user",loading:v,onChange:e=>{m(e),a.setFieldsValue({userId:e})},children:g?.data?.users?.map(e=>r.jsx(Q.A.Option,{value:e.id,children:r.jsxs("div",{className:"flex items-center",children:[r.jsx(d.A,{className:"mr-2"}),e.name," (",e.role,")"]})},e.id))})}),(0,r.jsx)(H.A.Item,{name:"isDefault",valuePropName:"checked",initialValue:!1,children:(0,r.jsx)(J.A,{checkedChildren:"Set as default store",unCheckedChildren:"Not default"})}),(0,r.jsx)(i.Ay,{type:"primary",htmlType:"submit",loading:$,className:"border-blue-600 bg-blue-600 hover:bg-blue-700",disabled:!n||!c,children:"Associate User with Store"})]})]}),c&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800",children:'User"s Stores'}),x?(0,r.jsx)("div",{className:"flex h-40 items-center justify-center",children:(0,r.jsx)(s.A,{indicator:(0,r.jsx)(u.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):p.length>0?(0,r.jsx)(ly,{dataSource:p,columns:I,rowKey:"id",pagination:!1,className:"user-stores-table"}):(0,r.jsx)(rL.A,{description:"No stores associated with this user"})]})]})})},lS=e=>{let[t,{isLoading:n}]=(0,E.yv)();return{bulkDeleteStores:async n=>{try{console.log("Bulk deleting stores with IDs:",n);let r=await t(n).unwrap();if(!r.success)throw Error(r.message||"Failed to delete stores");return(0,z.r)("success",`${n.length} stores deleted successfully`),e&&e(),r.data}catch(e){throw console.error("Bulk delete stores error:",e),(0,z.r)("error",e.message||"Failed to delete stores"),e}},isDeleting:n}};var lE=n(765);let lN=()=>{let e=(0,lE.a)(),[t,n]=(0,o.useState)(""),[l,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),[g,h]=(0,o.useState)(!1),[v,b]=(0,o.useState)(!1),[x,y]=(0,o.useState)(null),[A,$]=(0,o.useState)(!1),[w,C]=(0,o.useState)([]),{refetch:k,isLoading:S,data:N}=(0,E.tD)({page:1,limit:e?5:10,search:t}),{bulkDeleteStores:j,isDeleting:I}=lS(()=>{$(!1),k()});(0,o.useEffect)(()=>{k()},[e,k]);let z=e=>{y(e),f(!0)},P=e=>{y(e),b(!0)},K=()=>{k()},R=async()=>{if(console.log("confirmBulkDelete called with stores:",w),w.length>0)try{await j(w)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,r.jsx)(a.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Store Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:e?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(i.Ay,{type:"primary",icon:(0,r.jsx)(c.A,{}),onClick:()=>m(!0),size:e?"small":"middle",className:"bg-blue-600",children:e?"":"Add Store"}),x&&(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(d.A,{}),onClick:()=>P(x),className:"bg-green-600 text-white",size:e?"small":"middle",children:e?"":"Manage Users"})]}),children:(0,r.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,r.jsx)(D,{value:t,onChange:n,isMobile:e}),S?(0,r.jsx)("div",{className:"flex h-60 items-center justify-center bg-gray-50",children:(0,r.jsx)(s.A,{indicator:(0,r.jsx)(u.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsx)(r.Fragment,{children:N?.data?.stores&&N.data.stores.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(M,{search:t,onEdit:z,onView:e=>{y(e),h(!0)},onSuccess:K,onSelect:y,onBulkDelete:e=>{console.log("handleBulkDelete called with storeIds:",e),C(e),$(!0)},isMobile:e})}):(0,r.jsx)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No stores found matching your search criteria."}),(0,r.jsx)(i.Ay,{type:"primary",onClick:()=>n(""),className:"mt-4 bg-blue-600",children:"Clear Search"})]}):(0,r.jsx)("p",{children:'No stores found. Click "Add Store" to create one.'})})})]})}),(0,r.jsx)(X,{isOpen:l,onClose:()=>m(!1),onSuccess:()=>{m(!1),k()},mode:"create"}),(0,r.jsx)(X,{isOpen:p,onClose:()=>f(!1),onSuccess:()=>{f(!1),k()},editStore:x,mode:"edit"}),(0,r.jsx)(Y,{isOpen:g,onClose:()=>h(!1),onEdit:z,storeId:x?.id}),(0,r.jsx)(lk,{isOpen:v,onClose:()=>b(!1),store:x,onSuccess:K}),(0,r.jsx)(O.A,{isOpen:A,onClose:()=>{$(!1),C([])},onConfirm:R,title:"Delete Multiple Stores",message:`Are you sure you want to delete ${w.length} stores? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:I,type:"danger"})]})}},51531:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(45512);n(58009);var o=n(88206),l=n(3117),a=n(75238);let i=({isOpen:e,onClose:t,onConfirm:n,title:i,message:s,confirmText:c="Confirm",cancelText:d="Cancel",isLoading:u=!1,type:m="danger"})=>(0,r.jsx)(o.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.A,{style:{color:"danger"===m?"#ff4d4f":"warning"===m?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:i})]}),open:e,onCancel:t,footer:[(0,r.jsx)(l.Ay,{onClick:t,disabled:u,children:d},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===m?"primary":"default",danger:"danger"===m,onClick:n,loading:u,children:c},"confirm")],maskClosable:!u,closable:!u,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:s})})},63844:(e,t,n)=>{"use strict";n.d(t,{A0:()=>a,Hj:()=>s,jB:()=>l,nA:()=>i});var r=n(45512);n(58009);var o=n(44195);let l=({children:e,columns:t,className:n,minWidth:l="800px"})=>(0,r.jsx)("div",{className:(0,o.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",n),children:(0,r.jsx)("div",{className:(0,o.cn)("gap-0","block"),style:{},children:e})}),a=({children:e,className:t,sticky:n})=>(0,r.jsx)("div",{className:(0,o.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",n&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[n],t),children:e}),i=({children:e,className:t,sticky:n})=>(0,r.jsx)("div",{className:(0,o.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",n&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[n],t),children:e}),s=({children:e,className:t,selected:n=!1,onClick:l})=>(0,r.jsx)("div",{className:(0,o.cn)("contents",n&&"bg-blue-50",l&&"cursor-pointer hover:bg-gray-50",t),onClick:l,children:e})},98776:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(45512),o=n(58009),l=n(3117),a=n(97071);let i=({isOpen:e,onClose:t,title:n,children:i,width:s="400px",footer:c,fullWidth:d=!1})=>{let[u,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),[g,h]=(0,o.useState)(1024);if((0,o.useEffect)(()=>{let e=()=>{h(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,o.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",n),e)f(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{m(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{m(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{f(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,n]),!p)return null;let v="Point of Sale"===n||d||"100vw"===s;return(0,r.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${v?"sales-panel-container":""}`,children:[(0,r.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${u?"opacity-50":"opacity-0"}`,onClick:t}),(0,r.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${u?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===n||d||"100vw"===s||g<640?"100vw":g<1024?"500px":"string"==typeof s&&s.includes("px")&&parseInt(s)>600?"600px":s},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:n}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(a.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:i}),c&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},73542:(e,t,n)=>{"use strict";n.d(t,{E:()=>o});var r=n(58009);let o=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},22367:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\stores\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\stores\\page.tsx","default")},27742:()=>{},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,1785,7277,8493,5482,106,4286],()=>n(23818));module.exports=r})();