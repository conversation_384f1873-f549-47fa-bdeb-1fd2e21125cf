/* Fix for sidebar back button overlay issues */

/* Hide back button on desktop */
@media (min-width: 1024px) {
  /* Hide mobile sidebar elements on desktop */
  .fixed.inset-0.z-40.bg-black\/50 {
    display: none !important;
  }

  /* Ensure desktop sidebar is always visible */
  .lg\:block {
    display: block !important;
  }

  /* Ensure proper margin for content area */
  .lg\:ml-\[290px\] {
    margin-left: 290px !important;
  }
}

/* Fix for mobile sidebar */
@media (max-width: 1023px) {
  /* Hide desktop sidebar on mobile */
  .hidden.lg\:block {
    display: none !important;
  }

  /* Ensure mobile sidebar is hidden by default */
  .fixed.left-0.top-0.bottom-0.z-50.transform.-translate-x-full {
    transform: translateX(-100%) !important;
    transition: transform 300ms ease-in-out !important;
  }

  /* Only show mobile sidebar when explicitly toggled */
  .fixed.left-0.top-0.bottom-0.z-50.transform.translate-x-0 {
    transform: translateX(0) !important;
    transition: transform 300ms ease-in-out !important;
  }

  /* Ensure the sidebar stays visible when toggled */
  .fixed.left-0.top-0.bottom-0.z-50.h-screen.w-\[290px\] {
    transition: transform 300ms ease-in-out !important;
    will-change: transform !important;
  }
}

/* Fix for sidebar transitions */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Fix for overlay */
.fixed.inset-0.z-40.bg-black\/50 {
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 300ms ease-in-out;
}

/* Fix for back button */
.absolute.left-3\/4.right-4\.5.top-1\/2.-translate-y-1\/2.text-right,
.absolute.right-0.top-1\/2.-translate-y-1\/2.text-right,
.back-button,
.mobile-back-button {
  display: none !important;
}

/* Only show back button when sidebar is open on mobile */
@media (max-width: 1023px) {
  .fixed.left-0.top-0.bottom-0.z-50.transform.translate-x-0 .absolute.left-3\/4.right-4\.5.top-1\/2.-translate-y-1\/2.text-right,
  .fixed.left-0.top-0.bottom-0.z-50.transform.translate-x-0 .absolute.right-0.top-1\/2.-translate-y-1\/2.text-right,
  .fixed.left-0.top-0.bottom-0.z-50.transform.translate-x-0 .back-button,
  .fixed.left-0.top-0.bottom-0.z-50.transform.translate-x-0 .mobile-back-button {
    display: block !important;
  }
}

/* Force hide back button on desktop regardless of sidebar state */
@media (min-width: 1024px) {
  .back-button,
  .mobile-back-button,
  .absolute.left-3\/4.right-4\.5.top-1\/2.-translate-y-1\/2.text-right,
  .absolute.right-0.top-1\/2.-translate-y-1\/2.text-right {
    display: none !important;
  }
}

/* Ensure desktop sidebar is always visible and above popups */
.lg\:block.fixed.left-0.top-0.h-full.z-20 {
  z-index: 40 !important;
}

/* Ensure mobile sidebar is always above popups */
.fixed.left-0.top-0.bottom-0.z-50.h-screen.w-\[290px\] {
  z-index: 50 !important;
}
