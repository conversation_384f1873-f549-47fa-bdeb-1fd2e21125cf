import { Dispatch } from '@reduxjs/toolkit';
import { setUser } from '@/reduxRTK/services/authSlice';
import { userApi } from '@/reduxRTK/services/authApi';
import { User, ApiResponse } from '@/types/user';

interface RefreshUserDataParams {
  dispatch: Dispatch;
  accessToken: string;
  currentUser?: User | null;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Utility function to refresh user data from backend after payment
 * This ensures the frontend has the latest payment status
 */
export const refreshUserDataAfterPayment = async ({
  dispatch,
  accessToken,
  currentUser,
  maxRetries = 3,
  retryDelay = 2000
}: RefreshUserDataParams): Promise<{ success: boolean; user?: User; error?: string }> => {
  console.log('🔄 Starting user data refresh after payment...');

  // First, invalidate ALL cache to ensure fresh data
  dispatch(userApi.util.invalidateTags(['User']));

  // Also clear any other cached data that might be stale
  dispatch(userApi.util.resetApiState());

  console.log('🧹 Cleared all RTK Query cache');

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📡 Attempt ${attempt}/${maxRetries} - Fetching fresh user data...`);

      // Wait before each attempt to allow backend processing
      if (attempt > 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      // Use the getCurrentUser endpoint to fetch fresh data
      const result = await (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, {
        forceRefetch: true
      }));

      if ('data' in result && result.data?.success && result.data.data) {
        const updatedUser = result.data.data;

        console.log('✅ Successfully fetched updated user data:', {
          id: updatedUser.id,
          paymentStatus: updatedUser.paymentStatus,
          lastPaymentDate: updatedUser.lastPaymentDate,
          nextPaymentDue: updatedUser.nextPaymentDue,
          attempt
        });

        // Update Redux state with fresh user data
        dispatch(setUser({ user: updatedUser, accessToken }));

        // Verify the payment status was actually updated
        if (updatedUser.paymentStatus === 'paid') {
          console.log('🎉 Payment status successfully updated to "paid"');
          return { success: true, user: updatedUser };
        } else {
          console.log(`⚠️ Payment status is "${updatedUser.paymentStatus}", not "paid". Retrying...`);
          if (attempt === maxRetries) {
            // On final attempt, use fallback
            console.log('⚠️ Max retries reached, using fallback update');
            const fallbackUser = {
              ...currentUser!,
              paymentStatus: 'paid' as const,
              lastPaymentDate: new Date().toISOString(),
              nextPaymentDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            };
            dispatch(setUser({ user: fallbackUser, accessToken }));
            return { success: true, user: fallbackUser };
          }
          continue; // Retry
        }
      } else {
        console.log(`⚠️ Attempt ${attempt}: Backend response not successful or no data`);
        if (attempt === maxRetries) {
          throw new Error('Failed to fetch user data from backend');
        }
        continue; // Retry
      }
    } catch (error: any) {
      console.error(`❌ Attempt ${attempt} failed:`, error);

      if (attempt === maxRetries) {
        console.log('💥 All attempts failed, using fallback update');

        if (currentUser) {
          // Fallback: Update Redux state manually
          const fallbackUser = {
            ...currentUser,
            paymentStatus: 'paid' as const,
            lastPaymentDate: new Date().toISOString(),
            nextPaymentDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          };
          dispatch(setUser({ user: fallbackUser, accessToken }));
          console.log('✅ Fallback: Updated user payment status in Redux');
          return { success: true, user: fallbackUser };
        } else {
          return { success: false, error: 'Failed to refresh user data and no current user for fallback' };
        }
      }
    }
  }

  return { success: false, error: 'Unexpected error in user data refresh' };
};

/**
 * Simple function to wait for a specified amount of time
 */
export const wait = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Function to check if user payment status is properly updated
 */
export const verifyPaymentStatusUpdate = (user: User | null): boolean => {
  if (!user) {
    console.log('❌ No user data to verify');
    return false;
  }

  const isPaymentStatusPaid = user.paymentStatus === 'paid';
  const hasRecentPaymentDate = user.lastPaymentDate &&
    new Date(user.lastPaymentDate).getTime() > Date.now() - (5 * 60 * 1000); // Within last 5 minutes

  console.log('🔍 Payment status verification:', {
    paymentStatus: user.paymentStatus,
    isPaymentStatusPaid,
    lastPaymentDate: user.lastPaymentDate,
    hasRecentPaymentDate,
    nextPaymentDue: user.nextPaymentDue
  });

  return isPaymentStatusPaid;
};

/**
 * Global function to force refresh user data
 * Can be called from anywhere in the app to refresh user payment status
 */
export const forceRefreshUserData = async (): Promise<void> => {
  console.log('🔄 Force refresh user data triggered');

  // This will be set by the app when it initializes
  const globalRefreshFunction = (window as any).__FORCE_REFRESH_USER_DATA;

  if (globalRefreshFunction && typeof globalRefreshFunction === 'function') {
    try {
      await globalRefreshFunction();
      console.log('✅ Force refresh completed');
    } catch (error) {
      console.error('❌ Force refresh failed:', error);
    }
  } else {
    console.log('⚠️ Global refresh function not available, trying page reload');
    window.location.reload();
  }
};
