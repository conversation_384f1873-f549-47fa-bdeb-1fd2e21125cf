"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface UserGrowthChartProps {
  users: any[];
}

const UserGrowthChart: React.FC<UserGrowthChartProps> = ({ users }) => {
  const chartData = useMemo(() => {
    // Group users by month and role
    const monthlyData = new Map<string, { admins: number; cashiers: number; total: number }>();
    
    users.forEach(user => {
      if (user.createdAt) {
        const monthKey = dayjs(user.createdAt).format('YYYY-MM');
        const existing = monthlyData.get(monthKey) || { admins: 0, cashiers: 0, total: 0 };
        
        if (user.role === 'admin') {
          existing.admins++;
        } else if (user.role === 'cashier') {
          existing.cashiers++;
        }
        existing.total++;
        
        monthlyData.set(monthKey, existing);
      }
    });

    // Convert to cumulative data and sort by date
    const sortedEntries = Array.from(monthlyData.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .slice(-12); // Last 12 months

    let cumulativeAdmins = 0;
    let cumulativeCashiers = 0;
    let cumulativeTotal = 0;

    const adminSeries = [];
    const cashierSeries = [];
    const totalSeries = [];

    for (const [month, data] of sortedEntries) {
      cumulativeAdmins += data.admins;
      cumulativeCashiers += data.cashiers;
      cumulativeTotal += data.total;

      const formattedMonth = dayjs(month).format('MMM YYYY');
      
      adminSeries.push({ x: formattedMonth, y: cumulativeAdmins });
      cashierSeries.push({ x: formattedMonth, y: cumulativeCashiers });
      totalSeries.push({ x: formattedMonth, y: cumulativeTotal });
    }

    return {
      adminSeries,
      cashierSeries,
      totalSeries
    };
  }, [users]);

  const options: ApexOptions = {
    chart: {
      type: 'line',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#5750F1', '#0ABEF9', '#10B981'],
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '14px',
      fontFamily: 'inherit',
      markers: {
        size: 8,
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function(value) {
          return `${value} users`;
        }
      }
    },
    xaxis: {
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '12px',
        }
      }
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          return Math.round(value).toString();
        },
        style: {
          fontSize: '12px',
        }
      }
    }
  };

  const series = [
    {
      name: 'Total Users',
      data: chartData.totalSeries
    },
    {
      name: 'Admins',
      data: chartData.adminSeries
    },
    {
      name: 'Cashiers',
      data: chartData.cashierSeries
    }
  ];

  if (chartData.totalSeries.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No user growth data available</p>
          <p className="text-sm">Chart will appear as users register over time</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="line"
        height={250}
      />
    </div>
  );
};

export default UserGrowthChart;
