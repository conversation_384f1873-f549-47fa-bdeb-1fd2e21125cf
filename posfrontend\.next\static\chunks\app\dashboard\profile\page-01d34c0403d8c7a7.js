(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[399],{20644:(e,a,s)=>{Promise.resolve().then(s.bind(s,31482))},31482:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>_});var r=s(95155),t=s(12115),l=s(43316),n=s(99907),o=s(81910),i=s(39279),d=s(76046),c=s(83414),m=s(41657),u=s(83391),h=s(7875),g=s(63065),p=s(75912),y=s(34252);let x=e=>{let{user:a,onProfileUpdated:s}=e,[n]=c.A.useForm(),[o,i]=(0,t.useState)(!1),d=(0,u.wA)(),[x]=(0,g.H7)(),f=(0,u.d4)(e=>e.auth.accessToken)||"";t.useEffect(()=>{console.log("ProfileForm - Raw user object:",a),console.log("ProfileForm - Raw user object (stringified):",JSON.stringify(a,null,2));let e=(0,y.n4)(a.phone),s=(0,y.oB)(a.phone);console.log("ProfileForm - Phone value:",{originalPhone:a.phone,phoneType:typeof a.phone,formattedPhone:e,displayPhone:s}),n.setFieldsValue({name:a.name,email:a.email,phone:e}),console.log("ProfileForm - Form values set:",{name:a.name,email:a.email,phone:e}),setTimeout(()=>{n.setFieldsValue({name:a.name,email:a.email,phone:e}),console.log("ProfileForm - Form values refreshed:",{name:a.name,email:a.email,phone:e})},500)},[n,a]);let b=async e=>{var r,t;i(!0),window.__PROFILE_UPDATE_IN_PROGRESS=!0,console.log("ProfileForm - Profile update in progress flag set");try{let t=(0,y.n4)(e.phone);console.log("ProfileForm - Updating user with values:",{userId:a.id,name:e.name,originalPhone:e.phone,formattedPhone:t,phoneType:typeof t});let l=await x({userId:a.id,data:{name:e.name,phone:t}});if(console.log("ProfileForm - Update response:",l),"data"in l){let e=l.data;if(e.success){let t=null===(r=e.data)||void 0===r?void 0:r.updatedUser;if(t){console.log("ProfileForm - Updated user data from backend:",t),console.log("ProfileForm - Updated user data (stringified):",JSON.stringify(t,null,2));let r=t.phone||a.phone||"",l=a.createdAt||t.createdAt||"",o=a.lastPaymentDate||t.lastPaymentDate||void 0,i=a.nextPaymentDue||t.nextPaymentDue||null,c=a.createdBy||t.createdBy||void 0;console.log("ProfileForm - Critical fields:",{updatedUserPhone:t.phone,userPhone:a.phone,finalPhone:r,updatedUserCreatedAt:t.createdAt,userCreatedAt:a.createdAt,finalCreatedAt:l,updatedUserLastPaymentDate:t.lastPaymentDate,userLastPaymentDate:a.lastPaymentDate,finalLastPaymentDate:o,updatedUserNextPaymentDue:t.nextPaymentDue,userNextPaymentDue:a.nextPaymentDue,finalNextPaymentDue:i,updatedUserCreatedBy:t.createdBy,userCreatedBy:a.createdBy,finalCreatedBy:c});let m={...a,name:t.name,phone:r,createdAt:l,lastPaymentDate:o,nextPaymentDue:i,createdBy:c,paymentStatus:a.paymentStatus};console.log("ProfileForm - Updating Redux store with:",m),console.log("ProfileForm - Updating Redux store with (stringified):",JSON.stringify(m,null,2)),console.log("ProfileForm - Using access token from Redux:",f?"Token exists (not showing for security)":"No token found"),d((0,h.gV)({user:m,accessToken:f})),(0,p.r)("success",e.message||"Profile updated successfully");let u=(0,y.n4)(t.phone);n.setFieldsValue({name:t.name,email:t.email,phone:u}),console.log("ProfileForm - Form values updated after save:",{name:t.name,email:t.email,originalPhone:t.phone,formattedPhone:u}),s&&s()}}else(0,p.r)("error",e.message||"Failed to update profile")}}catch(a){console.error("Error updating profile:",a);let e=(null===(t=a.data)||void 0===t?void 0:t.message)||a.message||"Failed to update profile. Please try again.";(0,p.r)("error",e)}finally{i(!1),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("ProfileForm - Profile update in progress flag cleared")},1e3)}};return(0,r.jsxs)(c.A,{form:n,layout:"vertical",onFinish:b,className:"text-gray-800",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsx)(c.A.Item,{name:"name",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Full Name"}),rules:[{required:!0,message:"Please enter your name"}],children:(0,r.jsx)(m.A,{className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"})}),(0,r.jsx)(c.A.Item,{name:"email",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Email Address"}),rules:[{required:!0,message:"Please enter your email"},{type:"email",message:"Please enter a valid email"}],children:(0,r.jsx)(m.A,{className:"bg-gray-100 border-gray-300 text-gray-600",disabled:!0,title:"Email cannot be changed"})}),(0,r.jsx)(c.A.Item,{name:"phone",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Phone Number"}),rules:[{required:!0,message:"Please enter your phone number"},{min:5,message:"Phone number must be at least 5 digits"},{max:20,message:"Phone number must be at most 20 digits"},{pattern:/^[0-9+\-\s()]*$/,message:"Phone number can only contain digits, spaces, and the characters +, -, (, )"}],tooltip:"Enter your phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)",children:(0,r.jsx)(m.A,{className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"e.g., ************"})})]}),(0,r.jsx)(c.A.Item,{className:"mt-4",children:(0,r.jsx)(l.Ay,{type:"primary",htmlType:"submit",loading:o,className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Save Changes"})})]})};var f=s(90954);let b=e=>{let{userId:a}=e,[s]=c.A.useForm(),[n,o]=(0,t.useState)(!1),[i]=(0,g.SV)(),d=async e=>{var r,t,l,n,d;if(e.newPassword!==e.confirmPassword){(0,p.r)("error","New passwords do not match");return}o(!0);try{if(console.log("ChangePasswordForm - Attempting to change password for user:",a),console.log("ChangePasswordForm - Password lengths:",{currentPasswordLength:(null===(r=e.currentPassword)||void 0===r?void 0:r.length)||0,newPasswordLength:(null===(t=e.newPassword)||void 0===t?void 0:t.length)||0,confirmPasswordLength:(null===(l=e.confirmPassword)||void 0===l?void 0:l.length)||0}),!e.currentPassword||!e.newPassword){(0,p.r)("error","All password fields are required"),o(!1);return}let d=await i({userId:a,currentPassword:e.currentPassword,newPassword:e.newPassword});if("data"in d){let e=d.data;console.log("ChangePasswordForm - Response:",e),e.success?((0,p.r)("success",e.message||"Password changed successfully"),s.resetFields()):(console.error("ChangePasswordForm - Error:",e.message),(0,p.r)("error",e.message||"Failed to change password"))}else if("error"in d){let e=d.error;console.error("ChangePasswordForm - Error response:",e),(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)?(0,p.r)("error",e.data.message):(null==e?void 0:e.message)?(0,p.r)("error",e.message):(0,p.r)("error","Failed to change password. Please try again.")}}catch(a){console.error("Error changing password:",a),console.error("ChangePasswordForm - Detailed error:",{message:a.message,data:a.data,status:a.status,stack:a.stack});let e=(null===(d=a.data)||void 0===d?void 0:d.message)||a.message||"Failed to change password. Please check your current password and try again.";(0,p.r)("error",e)}finally{o(!1)}};return(0,r.jsxs)(c.A,{form:s,layout:"vertical",onFinish:d,className:"text-gray-800",children:[(0,r.jsx)(c.A.Item,{name:"currentPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Current Password"}),rules:[{required:!0,message:"Please enter your current password"}],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(f.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Enter your current password"})}),(0,r.jsx)(c.A.Item,{name:"newPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"New Password"}),rules:[{required:!0,message:"Please enter your new password"},{min:8,message:"Password must be at least 8 characters"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,message:"Password must contain uppercase, lowercase, number and special character"}],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(f.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Enter your new password"})}),(0,r.jsx)(c.A.Item,{name:"confirmPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Confirm New Password"}),rules:[{required:!0,message:"Please confirm your new password"},e=>{let{getFieldValue:a}=e;return{validator:(e,s)=>s&&a("newPassword")!==s?Promise.reject(Error("The two passwords do not match")):Promise.resolve()}}],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(f.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Confirm your new password"})}),(0,r.jsx)(c.A.Item,{className:"mt-6",children:(0,r.jsx)(l.Ay,{type:"primary",htmlType:"submit",loading:n,className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Change Password"})}),(0,r.jsxs)("div",{className:"mt-4 text-gray-600 text-sm",children:[(0,r.jsx)("p",{children:"Password requirements:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 mt-1",children:[(0,r.jsx)("li",{children:"Minimum 8 characters"}),(0,r.jsx)("li",{children:"At least one uppercase letter"}),(0,r.jsx)("li",{children:"At least one lowercase letter"}),(0,r.jsx)("li",{children:"At least one number"}),(0,r.jsx)("li",{children:"At least one special character (@$!%*?&)"})]})]})]})};var P=s(72093),j=s(51814),w=s(33429);let A=e=>{let{user:a,onStoreUpdated:s}=e,[n]=c.A.useForm(),[o,d]=(0,t.useState)(!1),{data:u,isLoading:h,refetch:g}=(0,w.Rv)(a.id),[y]=(0,w.He)(),[x]=(0,w.aW)();(0,t.useEffect)(()=>{if(null==u?void 0:u.data){let e=u.data;n.setFieldsValue({name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId})}else n.resetFields()},[u,n]);let f=async()=>{try{d(!0);let e=await n.validateFields();if(null==u?void 0:u.data){let a=await x({storeId:u.data.id,data:e}).unwrap();a.success?((0,p.r)("success","Store information updated successfully"),s&&s(),g()):(0,p.r)("error",a.message||"Failed to update store information")}else{let r=await y({data:{...e,userId:a.id}}).unwrap();r.success?((0,p.r)("success","Store information saved successfully"),s&&s(),g()):(0,p.r)("error",r.message||"Failed to save store information")}}catch(a){var e;console.error("Error submitting store form:",a),(0,p.r)("error",(null===(e=a.data)||void 0===e?void 0:e.message)||"An error occurred while saving store information")}finally{d(!1)}};return h?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)(P.A,{})}):(0,r.jsxs)("div",{className:"text-gray-800",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-800 mb-2 flex items-center",children:[(0,r.jsx)(i.A,{className:"mr-2"}),(0,r.jsx)("span",{children:"Store Information"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"This information will appear on receipts and invoices"})]}),(0,r.jsxs)(c.A,{form:n,layout:"vertical",onFinish:f,className:"text-gray-800",children:[(0,r.jsx)(c.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"text-gray-700",children:["Store Name ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),rules:[{required:!0,message:"Please enter your store name"}],children:(0,r.jsx)(m.A,{placeholder:"Enter store name",prefix:(0,r.jsx)(i.A,{className:"site-form-item-icon"}),className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(c.A.Item,{name:"address",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Address"}),children:(0,r.jsx)(m.A.TextArea,{placeholder:"Enter store address",className:"bg-white border-gray-300 text-gray-800",rows:3})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.A.Item,{name:"city",label:(0,r.jsx)("span",{className:"text-gray-700",children:"City"}),children:(0,r.jsx)(m.A,{placeholder:"Enter city",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(c.A.Item,{name:"state",label:(0,r.jsx)("span",{className:"text-gray-700",children:"State/Region"}),children:(0,r.jsx)(m.A,{placeholder:"Enter state or region",className:"bg-white border-gray-300 text-gray-800"})})]}),(0,r.jsx)(c.A.Item,{name:"country",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Country"}),children:(0,r.jsx)(m.A,{placeholder:"Enter country",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.A.Item,{name:"phone",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Phone"}),children:(0,r.jsx)(m.A,{placeholder:"Enter store phone",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(c.A.Item,{name:"email",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Email"}),children:(0,r.jsx)(m.A,{placeholder:"Enter store email",className:"bg-white border-gray-300 text-gray-800",type:"email"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.A.Item,{name:"website",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Website"}),children:(0,r.jsx)(m.A,{placeholder:"Enter website URL",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(c.A.Item,{name:"taxId",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Tax ID"}),children:(0,r.jsx)(m.A,{placeholder:"Enter tax ID",className:"bg-white border-gray-300 text-gray-800"})})]}),(0,r.jsx)(c.A.Item,{className:"mt-6",children:(0,r.jsx)(l.Ay,{type:"primary",htmlType:"submit",loading:o,icon:(0,r.jsx)(j.A,{}),className:"bg-blue-600 hover:bg-blue-700 border-none text-white",block:!0,children:(null==u?void 0:u.data)?"Update Store Information":"Save Store Information"})})]})]})};var N=s(78444),v=s(55750),D=s(7162),F=s(15424),S=s(21455),I=s.n(S);let E=e=>{let{user:a}=e,s=e=>{if(!e)return"N/A";console.log("ProfileAvatar - Formatting date:",e);try{return I()(e).format("MMM D, YYYY")}catch(e){return console.error("Error formatting date:",e),"Invalid date"}};return console.log("ProfileAvatar - User data:",{name:a.name,email:a.email,phone:a.phone,phoneType:typeof a.phone,createdAt:a.createdAt,createdAtType:typeof a.createdAt,role:a.role,paymentStatus:a.paymentStatus}),console.log("ProfileAvatar - Complete user object:",JSON.stringify(a,null,2)),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(N.A,{size:120,icon:(0,r.jsx)(v.A,{}),className:"mb-4 bg-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-1",children:a.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-4 capitalize",children:a.role}),(0,r.jsxs)("div",{className:"mt-6 w-full",children:[(0,r.jsx)("h4",{className:"text-gray-800 font-medium mb-2",children:"Account Information"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(D.A,{className:"mr-2"}),"Email:"]}),(0,r.jsx)("span",{className:"text-gray-800",children:a.email})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(F.A,{className:"mr-2"}),"Phone:"]}),(0,r.jsx)("span",{className:"text-gray-800",children:a&&a.phone?(0,y.oB)(a.phone):"Not provided"})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Role:"}),(0,r.jsx)("span",{className:"text-gray-800 capitalize",children:a.role})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Payment Status:"}),(0,r.jsx)("span",{className:"capitalize ".concat("paid"===a.paymentStatus?"text-green-500":"pending"===a.paymentStatus?"text-yellow-500":"text-red-500"),children:a.paymentStatus})]}),a.nextPaymentDue&&(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Next Payment:"}),(0,r.jsx)("span",{className:"text-gray-800",children:s(a.nextPaymentDue)})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Member Since:"}),(0,r.jsx)("span",{className:"text-gray-800",children:a&&a.createdAt?s(a.createdAt):"N/A"})]}),a.lastPaymentDate&&(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Last Payment:"}),(0,r.jsx)("span",{className:"text-gray-800",children:s(a.lastPaymentDate)})]})]})]})]})};var C=s(36060),T=s(22882);function _(){var e;let{user:a,refreshUser:s}=(0,C.A)(),c=(0,d.useRouter)(),m=(0,d.usePathname)(),[u,h]=(0,t.useState)("1"),[g,p]=(0,t.useState)(0);console.log("ProfilePage - Complete user object:",JSON.stringify(a,null,2)),console.log("ProfilePage - Critical fields:",{phone:null==a?void 0:a.phone,phoneType:(null==a?void 0:a.phone)?typeof a.phone:"undefined/null",createdAt:null==a?void 0:a.createdAt,createdAtType:(null==a?void 0:a.createdAt)?typeof a.createdAt:"undefined/null"}),(0,t.useEffect)(()=>{console.log("ProfilePage - Current pathname:",m)},[m]);let y=async()=>{console.log("ProfilePage - Refreshing user data");try{await s(),console.log("ProfilePage - User data after refresh:",{phone:null==a?void 0:a.phone,phoneType:(null==a?void 0:a.phone)?typeof a.phone:"undefined/null",createdAt:null==a?void 0:a.createdAt,createdAtType:(null==a?void 0:a.createdAt)?typeof a.createdAt:"undefined/null"}),p(e=>e+1),console.log("ProfilePage - Refresh key updated:",g+1)}catch(e){console.error("Error updating profile:",e)}};return a?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"My Profile"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your account information and settings"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-1",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:(0,r.jsx)(E,{user:a},"avatar-".concat(g))}),"superadmin"!==a.role&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6 border border-gray-200",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-800 mb-4 flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-2"}),(0,r.jsx)("span",{children:"Subscription Details"})]}),(0,r.jsx)(T.A,{}),(0,r.jsx)("p",{className:"mb-4 text-gray-600",children:"Your subscription allows you to access all features of the POS system."}),a.nextPaymentDue&&(0,r.jsxs)("p",{className:"mb-4 text-gray-600",children:["Next payment due: ",(0,r.jsx)("span",{className:"text-gray-800",children:(e=a.nextPaymentDue)?I()(e).format("MMM D, YYYY"):"N/A"})]}),"cashier"!==a.role&&(0,r.jsx)(l.Ay,{type:"primary",block:!0,onClick:()=>c.push("/payment"),className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Manage Subscription"})]})]}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:(0,r.jsx)(n.A,{activeKey:u,onChange:e=>{h(e)},items:[{key:"1",label:"Profile Information",children:(0,r.jsx)(x,{user:a,onProfileUpdated:y},"profile-form-".concat(g))},{key:"2",label:"Change Password",children:(0,r.jsx)(b,{userId:a.id},"password-form-".concat(g))},..."admin"===a.role?[{key:"3",label:(0,r.jsxs)("span",{children:[(0,r.jsx)(i.A,{className:"mr-1"}),"Store Information"]}),children:(0,r.jsx)(A,{user:a,onStoreUpdated:y},"store-form-".concat(g))}]:[]],className:"profile-tabs"})})})]})]}):(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-500",children:"Loading profile information..."})})}s(74603)},36060:(e,a,s)=>{"use strict";s.d(a,{A:()=>o});var r=s(83391),t=s(70854),l=s(63065),n=s(7875);let o=()=>{let e=(0,r.wA)(),{user:a,accessToken:s}=(0,r.d4)(e=>e.auth),o=(0,t._)(),{refetch:i}=(0,l.$f)((null==a?void 0:a.id)||0,{skip:!(null==a?void 0:a.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!a&&!!s,role:null==a?void 0:a.role,phone:null==a?void 0:a.phone,phoneType:(null==a?void 0:a.phone)?typeof a.phone:"undefined/null",createdAt:null==a?void 0:a.createdAt,createdAtType:(null==a?void 0:a.createdAt)?typeof a.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(a,null,2));let d=!!a&&!!s,c=async()=>{if(!(null==a?void 0:a.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",a.id);let r=await i();console.log("useAuth - Refetch result:",r);let t=r.data;if((null==t?void 0:t.success)&&(null==t?void 0:t.data)){console.log("useAuth - API response data:",t.data);let r=a.paymentStatus;a.lastPaymentDate,a.nextPaymentDue;let l=t.data.phone||a.phone||"",o=t.data.createdAt||a.createdAt||"",i=t.data.lastPaymentDate||a.lastPaymentDate||void 0,d=t.data.nextPaymentDue||a.nextPaymentDue||null,c=t.data.createdBy||a.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:t.data.phone,userPhone:a.phone,finalPhone:l,apiCreatedAt:t.data.createdAt,userCreatedAt:a.createdAt,finalCreatedAt:o,apiLastPaymentDate:t.data.lastPaymentDate,userLastPaymentDate:a.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:t.data.nextPaymentDue,userNextPaymentDue:a.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:t.data.createdBy,userCreatedBy:a.createdBy,finalCreatedBy:c});let m={...t.data,phone:l,createdAt:o,lastPaymentDate:i,nextPaymentDue:d,createdBy:c,paymentStatus:r};console.log("useAuth - Updating Redux store with:",m),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:m,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==t?void 0:t.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:a,accessToken:s,isAuthenticated:d,hasRole:e=>!!a&&(Array.isArray(e)?e.includes(a.role):a.role===e),isSuperAdmin:()=>(null==a?void 0:a.role)==="superadmin",isAdmin:()=>(null==a?void 0:a.role)==="admin",isCashier:()=>(null==a?void 0:a.role)==="cashier",needsPayment:()=>!!a&&"superadmin"!==a.role&&o.needsPayment,paymentStatus:o,refreshUser:c}}},34252:(e,a,s)=>{"use strict";s.d(a,{n4:()=>r,oB:()=>t});let r=e=>{if(!e)return"";let a=e.replace(/\D/g,"");return e.startsWith("0")?e:a.startsWith("233")||e.startsWith("+233")?"0"+a.substring(3):e},t=e=>{let a=r(e);if(!a)return"";let s=a.replace(/\D/g,"");return 10===s.length&&s.startsWith("0")?"".concat(s.substring(0,3)," ").concat(s.substring(3,7)," ").concat(s.substring(7)):a}},74603:()=>{}},e=>{var a=a=>e(e.s=a);e.O(0,[3226,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,8408,7114,5984,821,2671,8441,1517,7358],()=>a(20644)),_N_E=e.O()}]);