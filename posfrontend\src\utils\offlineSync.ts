// Offline Sync Manager for POS Sales
import { offlineStorage, OfflineSale } from './offlineStorage';
import { store } from '@/reduxRTK/store/store';
import { showMessage } from './showMessage';

class OfflineSyncManager {
  private syncInProgress = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 30000; // 30 seconds
  private readonly MAX_RETRY_ATTEMPTS = 3;

  constructor() {
    this.setupNetworkListeners();
    this.startPeriodicSync();
  }

  // Network Status Management
  private setupNetworkListeners(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('online', () => {
      console.log('🌐 Network connection restored');
      showMessage('success', 'Connection restored - syncing offline sales...');
      this.syncOfflineSales();
    });

    window.addEventListener('offline', () => {
      console.log('📱 Network connection lost - switching to offline mode');
      showMessage('warning', 'Working offline - sales will sync when connection returns');
    });
  }

  // Periodic Sync
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (navigator.onLine && !this.syncInProgress) {
        this.syncOfflineSales();
      }
    }, this.SYNC_INTERVAL);
  }

  // Main Sync Function
  async syncOfflineSales(): Promise<void> {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress, skipping...');
      return;
    }

    if (!navigator.onLine) {
      console.log('📱 Offline - skipping sync');
      return;
    }

    this.syncInProgress = true;
    console.log('🔄 Starting offline sales sync...');

    try {
      const pendingSales = await offlineStorage.getOfflineSales('pending');
      const failedSales = await offlineStorage.getOfflineSales('failed');
      const salesToSync = [...pendingSales, ...failedSales.filter(s => s.syncAttempts < this.MAX_RETRY_ATTEMPTS)];

      if (salesToSync.length === 0) {
        console.log('✅ No offline sales to sync');
        return;
      }

      console.log(`🔄 Syncing ${salesToSync.length} offline sales...`);
      let successCount = 0;
      let failureCount = 0;

      for (const sale of salesToSync) {
        try {
          await this.syncSingleSale(sale);
          successCount++;
        } catch (error) {
          console.error(`❌ Failed to sync sale ${sale.id}:`, error);
          failureCount++;
          
          await offlineStorage.updateSaleStatus(
            sale.id, 
            'failed', 
            error instanceof Error ? error.message : 'Unknown error'
          );
        }
      }

      if (successCount > 0) {
        showMessage('success', `✅ Synced ${successCount} offline sales`);
      }
      
      if (failureCount > 0) {
        showMessage('warning', `⚠️ ${failureCount} sales failed to sync - will retry later`);
      }

    } catch (error) {
      console.error('❌ Sync process failed:', error);
      showMessage('error', 'Sync failed - will retry automatically');
    } finally {
      this.syncInProgress = false;
    }
  }

  // Sync Single Sale
  private async syncSingleSale(sale: OfflineSale): Promise<void> {
    const authState = store.getState().auth;
    const token = authState?.accessToken;

    if (!token) {
      throw new Error('No authentication token available');
    }

    // Prepare sale data for API
    const saleData = {
      totalAmount: sale.totalAmount,
      paymentMethod: sale.paymentMethod,
      items: sale.items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price
      })),
      receiptUrl: sale.receiptUrl || '',
      storeId: sale.storeId
    };

    // Make API call to create sale
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/sales`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        mode: 'createnew',
        saleData
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'Sale creation failed');
    }

    // Mark as synced and remove from offline storage
    await offlineStorage.updateSaleStatus(sale.id, 'synced');
    console.log(`✅ Successfully synced sale ${sale.id}`);
    
    // Optionally remove synced sales after a delay to keep some history
    setTimeout(() => {
      offlineStorage.deleteOfflineSale(sale.id).catch(console.error);
    }, 24 * 60 * 60 * 1000); // Remove after 24 hours
  }

  // Manual Sync Trigger
  async forceSyncNow(): Promise<void> {
    if (!navigator.onLine) {
      showMessage('warning', 'No internet connection - cannot sync now');
      return;
    }

    showMessage('success', 'Starting manual sync...');
    await this.syncOfflineSales();
  }

  // Get Sync Status
  async getSyncStatus(): Promise<{
    isOnline: boolean;
    syncInProgress: boolean;
    pendingSales: number;
    failedSales: number;
    lastSyncAttempt?: Date;
  }> {
    const stats = await offlineStorage.getStorageStats();
    
    return {
      isOnline: navigator.onLine,
      syncInProgress: this.syncInProgress,
      pendingSales: stats.pendingSales,
      failedSales: stats.failedSales,
      lastSyncAttempt: new Date() // Could be enhanced to track actual last sync
    };
  }

  // Cleanup
  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}

// Create singleton instance
export const offlineSync = new OfflineSyncManager();

// Utility function to check if we're in offline mode
export const isOfflineMode = (): boolean => {
  return typeof navigator !== 'undefined' && !navigator.onLine;
};

// Utility function to get network status
export const getNetworkStatus = () => {
  if (typeof navigator === 'undefined') {
    return { online: true, type: 'unknown' };
  }

  return {
    online: navigator.onLine,
    type: (navigator as any).connection?.effectiveType || 'unknown'
  };
};

export default offlineSync;
