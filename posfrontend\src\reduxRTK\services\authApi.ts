// services/userApi.ts
import { createApi, EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';  // Import custom baseQuery
import { User, UserResponse, PaginatedUsers, CreateUserDto, UpdateUserDto, ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

export const userApi = createApi({
  reducerPath: 'userApi' as const,
  baseQuery: customBaseQuery,  // Use custom baseQuery
  tagTypes: ['User'],
  endpoints: (builder): Record<string, any> => ({
    // 🔐 Login - NO token required
    loginUser: builder.mutation<UserResponse, { email: string; password: string }>({
      query: (credentials): { urlpath: string; payloaddata: any } => ({
        urlpath: '/login',  // Path for login
        payloaddata: credentials, // Payload for login
      }),
    }),

    // 🆕 Register new user
    createUser: builder.mutation<UserResponse, CreateUserDto>({
      query: (newUser) => {
        // Get token from store
        const token: string | undefined = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'createnew',
            ...newUser,
          },
          token,
        };
      },
      invalidatesTags: ['User'],
    }),

    // 📄 Get all users (paginated)
    getAllUsers: builder.query<ApiResponse<PaginatedUsers>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }) => {
        // Get token from store
        const token = store.getState().auth.accessToken || undefined;

        // Log the search parameters
        console.log("getAllUsers query params:", { page, limit, search });

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(), // Include search parameter
          },
          token,
        };
      },
      // Force refetch when search parameters change
      keepUnusedDataFor: 0,
      providesTags: ['User'],
    }),

    // 🔍 Get single user by ID
    getUserById: builder.query<ApiResponse<User>, number>({
      query: (userId) => {
        // Get token from store
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'retrieve',
            userId,
          },
          token,
        };
      },
      providesTags: ['User'],
    }),

    // 👤 Get current user data (for refreshing user state)
    getCurrentUser: builder.query<ApiResponse<User>, void>({
      query: () => {
        // Get token from store
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'current',
          },
          token,
        };
      },
      providesTags: ['User'],
    }),

    // ✏️ Update user
    updateUser: builder.mutation<UserResponse, { userId: number; data: UpdateUserDto }>({
      query: ({ userId, data }) => {
        // Get token from store
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'update',
            userId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: ['User'],
    }),

    // ❌ Delete user (single)
    deleteUser: builder.mutation<{ deletedId: number }, number>({
      query: (userId) => {
        // Get token from store and ensure it's string or undefined (not null)
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'delete',
            userId,
          },
          token,
        };
      },
      invalidatesTags: ['User'],
    }),

    // ❌ Bulk delete users
    bulkDeleteUsers: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (userIds) => {
        // Get token from store and ensure it's string or undefined (not null)
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'delete',
            userIds,
          },
          token,
        };
      },
      invalidatesTags: ['User'],
    }),

    logoutUser: builder.mutation<{ message: string }, { email: string }>({
      query: (data) => ({
        urlpath: '/logout',
        method: 'POST',
        payloaddata: data,
      }),
    }),

    // 🔑 Change password
    changePassword: builder.mutation<ApiResponse<{ success: boolean }>, {
      userId: number | string;
      currentPassword: string;
      newPassword: string
    }>({
      query: ({ userId, currentPassword, newPassword }) => {
        // Get token from store
        const token = store.getState().auth.accessToken || undefined;

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'update',
            userId,
            currentPassword,
            newPassword,
            passwordChange: true
          },
          token,
        };
      },
      invalidatesTags: ['User'],
    }),

  }),
});

export const {
  useLoginUserMutation,
  useCreateUserMutation,
  useGetAllUsersQuery,
  useGetUserByIdQuery,
  useGetCurrentUserQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useBulkDeleteUsersMutation,
  useLogoutUserMutation,
  useChangePasswordMutation
} = userApi;
