"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleUserRequest = exports.logoutController = exports.loginUserController = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const authService_1 = require("../services/authService");
const updatePaymentStatus_1 = require("../utils/updatePaymentStatus");
const modeValidator_1 = require("../utils/modeValidator"); // ✅ Import reusable validator
// ✅ Separate controller for LOGIN (No token required)
const loginUserController = async (req, res) => {
    const { email, password } = req.body;
    if (!email || !password) {
        return (0, responseHelper_1.sendResponse)(res, 400, false, "Email and password are required.");
    }
    try {
        const loggedInUser = await (0, authService_1.loginUser)(email, password);
        return (0, responseHelper_1.sendResponse)(res, 200, true, "Login successful.", loggedInUser);
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.loginUserController = loginUserController;
const logoutController = async (req, res) => {
    const { email } = req.body;
    try {
        await (0, authService_1.logoutUser)(email);
        return (0, responseHelper_1.sendResponse)(res, 200, true, "Logout successfully", null);
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.logoutController = logoutController;
// ✅ Main user controller (Requires authentication via middleware)
const handleUserRequest = async (req, res) => {
    const { mode, userId, page, limit, ...data } = req.body;
    // ✅ Define valid modes (excluding login)
    const validModes = ["createnew", "update", "delete", "retrieve", "current", "fix-payment-status"];
    // ✅ Validate mode using reusable function
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    // ✅ `req.user` is now provided by the middleware (no need to verify token here)
    const requester = req.user;
    try {
        switch (mode) {
            case "createnew": {
                const { name, email, password, role, phone } = data;
                if (!name || !email || !password || !phone) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Missing required fields: 'name', 'email', 'password', 'phone'.");
                }
                const newUser = await (0, authService_1.registerUser)(requester, {
                    name,
                    email,
                    password,
                    role,
                    phone,
                });
                return (0, responseHelper_1.sendResponse)(res, 201, true, "User created successfully.", newUser);
            }
            case "retrieve": {
                if (userId) {
                    const user = await (0, authService_1.getUserById)(requester, Number(userId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "User retrieved successfully.", user);
                }
                else {
                    // Log the request for debugging
                    const { search } = req.body;
                    console.log("getAllUsers request:", {
                        requester: { id: requester.id, role: requester.role },
                        page,
                        limit,
                        search: search || 'none'
                    });
                    // Log the raw request body for debugging
                    console.log("Raw request body:", req.body);
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    try {
                        const users = await (0, authService_1.getAllUsers)(requester, pageNum, limitNum, search);
                        console.log("getAllUsers success:", {
                            total: users.total,
                            userCount: users.users.length,
                            searchTerm: search || 'none'
                        });
                        return (0, responseHelper_1.sendResponse)(res, 200, true, "Users retrieved successfully.", users);
                    }
                    catch (error) {
                        console.error("getAllUsers error:", error);
                        return (0, responseHelper_1.sendResponse)(res, 500, false, `Error retrieving users: ${error.message}`, null);
                    }
                }
            }
            case "current": {
                // Get current user's data (for refreshing user state after payment)
                const currentUser = await (0, authService_1.getUserById)(requester, requester.id);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Current user data retrieved successfully.", currentUser);
            }
            case "update": {
                if (!userId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "User ID is required for updating.");
                }
                try {
                    // Check if this is a password change request
                    const { passwordChange } = data;
                    const result = await (0, authService_1.updateUserById)(requester, Number(userId), data);
                    // Handle password change response differently
                    if (passwordChange) {
                        return (0, responseHelper_1.sendResponse)(res, 200, true, "Password updated successfully.", { success: true });
                    }
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "User updated successfully.", result);
                }
                catch (error) {
                    console.error("User update error:", error);
                    // Handle specific database constraint errors
                    if (error.message.includes("duplicate key value violates unique constraint")) {
                        if (error.message.includes("users_email_unique")) {
                            return (0, responseHelper_1.sendResponse)(res, 400, false, "Email is already in use by another user.");
                        }
                        if (error.message.includes("users_phone_unique")) {
                            return (0, responseHelper_1.sendResponse)(res, 400, false, "Phone number is already in use by another user.");
                        }
                    }
                    // Handle custom error messages from the service
                    return (0, responseHelper_1.sendResponse)(res, 400, false, error.message || "Failed to update user.");
                }
            }
            case "delete": {
                // Check if we have userIds (array) or userId (single)
                const { userIds } = req.body;
                if (!userId && !userIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "User ID(s) are required for deletion.");
                }
                // Use userIds if provided, otherwise use single userId
                const idsToDelete = userIds
                    ? (Array.isArray(userIds) ? userIds : [userIds])
                    : [Number(userId)];
                const result = await (0, authService_1.deleteUserById)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} users deleted successfully.`
                    : "User deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            case "fix-payment-status": {
                // Only superadmins can manually fix payment status
                if (requester.role !== "superadmin") {
                    return (0, responseHelper_1.sendResponse)(res, 403, false, "Unauthorized: Only superadmins can fix payment status.");
                }
                if (!userId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "User ID is required for fixing payment status.");
                }
                const { paymentStatus } = data;
                if (!paymentStatus || !["pending", "paid", "overdue", "inactive"].includes(paymentStatus)) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Valid payment status is required (pending, paid, overdue, inactive).");
                }
                try {
                    console.log(`🔧 SuperAdmin ${requester.id} manually fixing payment status for user ${userId} to ${paymentStatus}`);
                    // Update payment status
                    await (0, updatePaymentStatus_1.updatePaymentStatus)(Number(userId), paymentStatus);
                    // Verify the update
                    const isVerified = await (0, updatePaymentStatus_1.verifyPaymentStatusUpdate)(Number(userId), paymentStatus);
                    if (isVerified) {
                        console.log(`✅ Payment status fix successful for user ${userId}`);
                        return (0, responseHelper_1.sendResponse)(res, 200, true, `Payment status successfully updated to ${paymentStatus}.`, { userId: Number(userId), paymentStatus, verified: true });
                    }
                    else {
                        console.error(`❌ Payment status fix verification failed for user ${userId}`);
                        return (0, responseHelper_1.sendResponse)(res, 500, false, "Payment status update failed verification. Please try again.", { userId: Number(userId), paymentStatus, verified: false });
                    }
                }
                catch (error) {
                    console.error(`❌ Error fixing payment status for user ${userId}:`, error);
                    return (0, responseHelper_1.sendResponse)(res, 500, false, `Failed to fix payment status: ${error.message}`, null);
                }
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handleUserRequest = handleUserRequest;
