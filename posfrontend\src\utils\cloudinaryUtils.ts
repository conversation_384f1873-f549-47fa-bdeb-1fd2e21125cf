/**
 * Utility functions for working with Cloudinary
 */

// Cloudinary configuration
const CLOUDINARY_CLOUD_NAME = 'dutmiedgk';
const CLOUDINARY_UPLOAD_PRESET = 'pos_receipts';

/**
 * Generates a receipt image using HTML content and uploads it to Cloudinary
 * @param htmlContent The HTML content of the receipt
 * @returns The URL of the uploaded image
 */
export const generateReceiptImage = async (htmlContent: string): Promise<string> => {
  try {
    // Create a temporary div to hold the receipt HTML
    const receiptDiv = document.createElement('div');
    receiptDiv.innerHTML = htmlContent;
    receiptDiv.style.width = '350px';
    receiptDiv.style.padding = '20px';
    receiptDiv.style.backgroundColor = 'white';
    receiptDiv.style.color = '#555555';
    receiptDiv.style.fontFamily = 'Arial, sans-serif';
    receiptDiv.style.position = 'absolute';
    receiptDiv.style.left = '-9999px';
    receiptDiv.style.borderLeft = '1px solid #000000';
    receiptDiv.style.borderRight = '1px solid #000000';
    receiptDiv.style.borderTop = '1px solid #000000';
    receiptDiv.style.borderBottom = '1px solid #000000';
    receiptDiv.style.boxSizing = 'border-box';
    receiptDiv.style.fontSize = '14px';
    receiptDiv.style.lineHeight = '1.5';

    // Add to document temporarily
    document.body.appendChild(receiptDiv);

    // Use html2canvas to convert the HTML to a canvas
    const html2canvas = (await import('html2canvas')).default;
    const canvas = await html2canvas(receiptDiv, {
      scale: 3, // Higher scale for better quality
      backgroundColor: 'white',
      logging: false,
      width: 350,
      height: receiptDiv.offsetHeight,
      windowWidth: 350,
      useCORS: true,
    });

    // Remove the temporary div
    document.body.removeChild(receiptDiv);

    // Convert canvas to blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob as Blob);
      }, 'image/png', 0.95);
    });

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', blob);
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);

    // Upload to Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    const data = await response.json();

    if (response.ok) {
      return data.secure_url;
    } else {
      console.error('Cloudinary upload failed:', data);
      throw new Error(data.error?.message || 'Failed to upload receipt image');
    }
  } catch (error) {
    console.error('Error generating receipt image:', error);
    throw error;
  }
};

/**
 * Generates HTML content for a receipt
 * @param saleData The sale data
 * @param storeInfo The store information
 * @returns HTML content for the receipt
 */
export const generateReceiptHTML = (
  saleData: {
    id: number;
    totalAmount: string | number;
    paymentMethod: string;
    transactionDate: string;
    items: Array<{
      productName: string;
      quantity: number;
      price: string | number;
    }>;
  },
  storeInfo: {
    name: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    phone?: string;
    email?: string;
  }
): string => {
  // Format the transaction date
  const date = new Date(saleData.transactionDate);
  const formattedDate = date.toLocaleDateString();
  const formattedTime = date.toLocaleTimeString();

  // Format payment method
  const paymentMethod = saleData.paymentMethod
    .replace('_', ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());

  // Generate modern receipt HTML to match the provided design
  return `
  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">

  <!-- Header and Title -->
  <div style="text-align: center; margin-bottom: 10px;">
    <div style="font-size: 18px; font-weight: bold;">${storeInfo.name || 'POS System'}</div>
    <div style="font-size: 16px; font-weight: bold;">#INV-${saleData.id}-${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}</div>
    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">
      ${storeInfo.address ? `<div>${storeInfo.address}</div>` : ''}
      ${storeInfo.city ? `<div>${storeInfo.city}</div>` : ''}
      ${storeInfo.country ? `<div>${storeInfo.country}</div>` : ''}
    </div>
  </div>

  <!-- Items Table -->
  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">
    <thead>
      <tr>
        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>
      </tr>
    </thead>
    <tbody>
      ${saleData.items.map((item, index) => {
        const price = typeof item.price === 'string' ? parseFloat(item.price) : item.price;
        const total = price * item.quantity;
        return `
          <tr>
            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">${index + 1}. ${item.productName}</td>
            <td style="text-align: right;">${item.quantity}</td>
            <td style="text-align: right;">${price.toFixed(2)}</td>
            <td style="text-align: right;">${total.toFixed(2)}</td>
          </tr>
        `;
      }).join('')}
    </tbody>
  </table>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Totals -->
  <div style="display: flex; justify-content: space-between; font-weight: bold;">
    <div>TOTAL</div>
    <div>GHS ${typeof saleData.totalAmount === 'string' ? parseFloat(saleData.totalAmount).toFixed(2) : saleData.totalAmount.toFixed(2)}</div>
  </div>
  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">
    <div>TAX</div>
    <div>0.00</div>
  </div>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Payment Info -->
  <div style="font-size: 11px; margin-bottom: 6px;">
    <div>Payment: ${paymentMethod.toUpperCase()}</div>
    ${paymentMethod.toLowerCase().includes('card') ? '<div>**** **** **** ****</div>' : ''}
  </div>

  <!-- Date/Time -->
  <div style="display: flex; justify-content: space-between; font-size: 11px;">
    <div><strong>Time:</strong> ${formattedTime}</div>
    <div><strong>Date:</strong> ${formattedDate}</div>
  </div>

  <!-- Barcode -->
  <div style="text-align: center; margin: 12px 0;">
    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>
    <div style="font-size: 11px; margin-top: 4px;">${saleData.id}</div>
  </div>

  <!-- Footer -->
  <div style="text-align: center; font-size: 12px; margin-top: 8px;">
    THANK YOU!
  </div>
</div>

`
    ;
};

/**
 * Uploads a product image to Cloudinary
 * @param file The image file to upload
 * @returns The URL of the uploaded image
 */
export const uploadProductImage = async (file: File): Promise<string> => {
  try {
    // Create form data for upload
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);
    formData.append('folder', 'products');

    // Upload to Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    const data = await response.json();

    if (response.ok) {
      return data.secure_url;
    } else {
      console.error('Cloudinary upload failed:', data);
      throw new Error(data.error?.message || 'Failed to upload product image');
    }
  } catch (error) {
    console.error('Error uploading product image:', error);
    throw error;
  }
};

