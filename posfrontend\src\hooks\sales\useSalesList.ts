"use client";

import { useState, useEffect } from "react";
import { useGetAllSalesQuery } from "@/reduxRTK/services/salesApi";

export const useSalesList = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  // Fetch sales data
  const {
    data: salesData,
    isLoading,
    isFetching,
    refetch,
  } = useGetAllSalesQuery({
    page,
    limit,
    search: debouncedSearchTerm,
  }, {
    // Force refetch on mount to ensure we have the latest data
    refetchOnMountOrArgChange: true
  });

  // Handle page change
  const handlePageChange = (newPage: number, newPageSize?: number) => {
    setPage(newPage);
    if (newPageSize) setLimit(newPageSize);
  };

  // Log sales data for debugging
  console.log("Sales data from API:", salesData);
  console.log("Total sales:", salesData?.data?.total || 0);
  console.log("Sales array:", salesData?.data?.sales || []);

  // Check if sales have items
  if (salesData?.data?.sales && salesData.data.sales.length > 0) {
    const firstSale = salesData.data.sales[0];
    console.log("First sale:", firstSale);
    console.log("First sale has items?", 'items' in firstSale);
    if ('items' in firstSale) {
      console.log("First sale items:", firstSale.items);
    }
  }

  return {
    sales: salesData?.data?.sales || [],
    total: salesData?.data?.total || 0,
    page,
    limit,
    isLoading: isLoading || isFetching,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
  };
};
