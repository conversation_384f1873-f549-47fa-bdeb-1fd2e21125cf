'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { notification, Button } from 'antd';
import { ReloadOutlined, CloseOutlined } from '@ant-design/icons';

const PWAUpdateNotification: React.FC = () => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  const handleUpdate = useCallback((key: string) => {
    if (registration?.waiting) {
      // Tell the waiting service worker to skip waiting and become active
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
    notification.destroy(key);
  }, [registration]);

  const handleDismiss = useCallback((key: string) => {
    setUpdateAvailable(false);
    notification.destroy(key);
  }, []);

  const showUpdateNotification = useCallback(() => {
    const key = 'pwa-update';

    notification.info({
      key,
      message: 'App Update Available',
      description: 'A new version of NEXAPO POS is available. Update now for the latest features and improvements.',
      duration: 0, // Don't auto-close
      placement: 'topRight',
      btn: (
        <div className="flex space-x-2">
          <Button
            type="primary"
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => handleUpdate(key)}
          >
            Update Now
          </Button>
          <Button
            size="small"
            icon={<CloseOutlined />}
            onClick={() => handleDismiss(key)}
          >
            Later
          </Button>
        </div>
      ),
      onClose: () => handleDismiss(key),
    });
  }, [handleDismiss, handleUpdate]);

  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      // Listen for service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // Reload the page when a new service worker takes control
        window.location.reload();
      });

      // Check for service worker registration
      navigator.serviceWorker.ready.then((reg) => {
        setRegistration(reg);

        // Listen for updates
        reg.addEventListener('updatefound', () => {
          const newWorker = reg.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available
                setUpdateAvailable(true);
                showUpdateNotification();
              }
            });
          }
        });

        // Check if there's already an update waiting
        if (reg.waiting) {
          setUpdateAvailable(true);
          showUpdateNotification();
        }
      });
    }
  }, [showUpdateNotification]);

  return null; // This component doesn't render anything visible
};

export default PWAUpdateNotification;


