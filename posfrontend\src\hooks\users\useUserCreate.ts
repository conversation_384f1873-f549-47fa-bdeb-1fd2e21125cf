"use client";

import { useCreateUserMutation } from "@/reduxRTK/services/authApi";
import { ApiResponse, CreateUserDto, User } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useUserCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a user
  const [createUser, { isLoading }] = useCreateUserMutation();

  const createNewUser = async (userData: CreateUserDto) => {
    try {
      const result = await createUser(userData).unwrap() as ApiResponse<User>;

      if (!result.success) {
        throw new Error(result.message || "Failed to create user");
      }

      showMessage("success", "User created successfully");

      if (onSuccess) {
        onSuccess();
      }

      return result.data;
    } catch (error: any) {
      console.error("Create user error:", error);
      showMessage("error", error.message || "Failed to create user");
      throw error;
    }
  };

  return {
    createUser: createNewUser,
    isCreating: isLoading
  };
};
