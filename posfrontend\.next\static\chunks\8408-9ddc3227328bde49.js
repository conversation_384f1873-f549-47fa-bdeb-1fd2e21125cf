"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8408],{20148:(t,e,o)=>{o.d(e,{A:()=>B});var n=o(12115),a=o(4951),r=o(6140),c=o(79624),i=o(51629),l=o(92984),s=o(4617),d=o.n(s),u=o(72261),m=o(97181),b=o(15231),p=o(58292),g=o(31049),f=o(67548),v=o(70695),h=o(1086);let y=(t,e,o,n,a)=>({background:t,border:"".concat((0,f.zA)(n.lineWidth)," ").concat(n.lineType," ").concat(e),["".concat(a,"-icon")]:{color:o}}),O=t=>{let{componentCls:e,motionDurationSlow:o,marginXS:n,marginSM:a,fontSize:r,fontSizeLG:c,lineHeight:i,borderRadiusLG:l,motionEaseInOutCirc:s,withDescriptionIconSize:d,colorText:u,colorTextHeading:m,withDescriptionPadding:b,defaultPadding:p}=t;return{[e]:Object.assign(Object.assign({},(0,v.dF)(t)),{position:"relative",display:"flex",alignItems:"center",padding:p,wordWrap:"break-word",borderRadius:l,["&".concat(e,"-rtl")]:{direction:"rtl"},["".concat(e,"-content")]:{flex:1,minWidth:0},["".concat(e,"-icon")]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:r,lineHeight:i},"&-message":{color:m},["&".concat(e,"-motion-leave")]:{overflow:"hidden",opacity:1,transition:"max-height ".concat(o," ").concat(s,", opacity ").concat(o," ").concat(s,",\n        padding-top ").concat(o," ").concat(s,", padding-bottom ").concat(o," ").concat(s,",\n        margin-bottom ").concat(o," ").concat(s)},["&".concat(e,"-motion-leave-active")]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),["".concat(e,"-with-description")]:{alignItems:"flex-start",padding:b,["".concat(e,"-icon")]:{marginInlineEnd:a,fontSize:d,lineHeight:0},["".concat(e,"-message")]:{display:"block",marginBottom:n,color:m,fontSize:c},["".concat(e,"-description")]:{display:"block",color:u}},["".concat(e,"-banner")]:{marginBottom:0,border:"0 !important",borderRadius:0}}},S=t=>{let{componentCls:e,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:a,colorWarning:r,colorWarningBorder:c,colorWarningBg:i,colorError:l,colorErrorBorder:s,colorErrorBg:d,colorInfo:u,colorInfoBorder:m,colorInfoBg:b}=t;return{[e]:{"&-success":y(a,n,o,t,e),"&-info":y(b,m,u,t,e),"&-warning":y(i,c,r,t,e),"&-error":Object.assign(Object.assign({},y(d,s,l,t,e)),{["".concat(e,"-description > pre")]:{margin:0,padding:0}})}}},w=t=>{let{componentCls:e,iconCls:o,motionDurationMid:n,marginXS:a,fontSizeIcon:r,colorIcon:c,colorIconHover:i}=t;return{[e]:{"&-action":{marginInlineStart:a},["".concat(e,"-close-icon")]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:r,lineHeight:(0,f.zA)(r),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",["".concat(o,"-close")]:{color:c,transition:"color ".concat(n),"&:hover":{color:i}}},"&-close-text":{color:c,transition:"color ".concat(n),"&:hover":{color:i}}}}},C=(0,h.OF)("Alert",t=>[O(t),S(t),w(t)],t=>({withDescriptionIconSize:t.fontSizeHeading3,defaultPadding:"".concat(t.paddingContentVerticalSM,"px ").concat(12,"px"),withDescriptionPadding:"".concat(t.paddingMD,"px ").concat(t.paddingContentHorizontalLG,"px")}));var x=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)0>e.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(o[n[a]]=t[n[a]]);return o};let E={success:a.A,info:l.A,error:r.A,warning:i.A},j=t=>{let{icon:e,prefixCls:o,type:a}=t,r=E[a]||null;return e?(0,p.fx)(e,n.createElement("span",{className:"".concat(o,"-icon")},e),()=>({className:d()("".concat(o,"-icon"),e.props.className)})):n.createElement(r,{className:"".concat(o,"-icon")})},N=t=>{let{isClosable:e,prefixCls:o,closeIcon:a,handleClose:r,ariaProps:i}=t,l=!0===a||void 0===a?n.createElement(c.A,null):a;return e?n.createElement("button",Object.assign({type:"button",onClick:r,className:"".concat(o,"-close-icon"),tabIndex:0},i),l):null},k=n.forwardRef((t,e)=>{let{description:o,prefixCls:a,message:r,banner:c,className:i,rootClassName:l,style:s,onMouseEnter:p,onMouseLeave:f,onClick:v,afterClose:h,showIcon:y,closable:O,closeText:S,closeIcon:w,action:E,id:k}=t,I=x(t,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[A,z]=n.useState(!1),M=n.useRef(null);n.useImperativeHandle(e,()=>({nativeElement:M.current}));let{getPrefixCls:P,direction:R,closable:T,closeIcon:B,className:H,style:D}=(0,g.TP)("alert"),F=P("alert",a),[W,L,Z]=C(F),q=e=>{var o;z(!0),null===(o=t.onClose)||void 0===o||o.call(t,e)},X=n.useMemo(()=>void 0!==t.type?t.type:c?"warning":"info",[t.type,c]),_=n.useMemo(()=>"object"==typeof O&&!!O.closeIcon||!!S||("boolean"==typeof O?O:!1!==w&&null!=w||!!T),[S,w,O,T]),Q=!!c&&void 0===y||y,V=d()(F,"".concat(F,"-").concat(X),{["".concat(F,"-with-description")]:!!o,["".concat(F,"-no-icon")]:!Q,["".concat(F,"-banner")]:!!c,["".concat(F,"-rtl")]:"rtl"===R},H,i,l,Z,L),G=(0,m.A)(I,{aria:!0,data:!0}),Y=n.useMemo(()=>"object"==typeof O&&O.closeIcon?O.closeIcon:S||(void 0!==w?w:"object"==typeof T&&T.closeIcon?T.closeIcon:B),[w,O,S,B]),K=n.useMemo(()=>{let t=null!=O?O:T;if("object"==typeof t){let{closeIcon:e}=t;return x(t,["closeIcon"])}return{}},[O,T]);return W(n.createElement(u.Ay,{visible:!A,motionName:"".concat(F,"-motion"),motionAppear:!1,motionEnter:!1,onLeaveStart:t=>({maxHeight:t.offsetHeight}),onLeaveEnd:h},(e,a)=>{let{className:c,style:i}=e;return n.createElement("div",Object.assign({id:k,ref:(0,b.K4)(M,a),"data-show":!A,className:d()(V,c),style:Object.assign(Object.assign(Object.assign({},D),s),i),onMouseEnter:p,onMouseLeave:f,onClick:v,role:"alert"},G),Q?n.createElement(j,{description:o,icon:t.icon,prefixCls:F,type:X}):null,n.createElement("div",{className:"".concat(F,"-content")},r?n.createElement("div",{className:"".concat(F,"-message")},r):null,o?n.createElement("div",{className:"".concat(F,"-description")},o):null),E?n.createElement("div",{className:"".concat(F,"-action")},E):null,n.createElement(N,{isClosable:_,prefixCls:F,closeIcon:Y,handleClose:q,ariaProps:K}))}))});var I=o(25514),A=o(98566),z=o(31701),M=o(97299),P=o(85625),R=o(52106);let T=function(t){function e(){var t,o,n;return(0,I.A)(this,e),o=e,n=arguments,o=(0,z.A)(o),(t=(0,P.A)(this,(0,M.A)()?Reflect.construct(o,n||[],(0,z.A)(this).constructor):o.apply(this,n))).state={error:void 0,info:{componentStack:""}},t}return(0,R.A)(e,t),(0,A.A)(e,[{key:"componentDidCatch",value:function(t,e){this.setState({error:t,info:e})}},{key:"render",value:function(){let{message:t,description:e,id:o,children:a}=this.props,{error:r,info:c}=this.state,i=(null==c?void 0:c.componentStack)||null,l=void 0===t?(r||"").toString():t;return r?n.createElement(k,{id:o,type:"error",message:l,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===e?i:e)}):a}}])}(n.Component);k.ErrorBoundary=T;let B=k},97838:(t,e,o)=>{o.d(e,{A:()=>P});var n=o(12115),a=o(4617),r=o.n(a),c=o(72261),i=o(28673),l=o(58292),s=o(31049),d=o(67548),u=o(70695),m=o(46258),b=o(56204),p=o(1086);let g=new d.Mo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new d.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new d.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),h=new d.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),y=new d.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),O=new d.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),S=t=>{let{componentCls:e,iconCls:o,antCls:n,badgeShadowSize:a,textFontSize:r,textFontSizeSM:c,statusSize:i,dotSize:l,textFontWeight:s,indicatorHeight:b,indicatorHeightSM:p,marginXS:S,calc:w}=t,C="".concat(n,"-scroll-number"),x=(0,m.A)(t,(t,o)=>{let{darkColor:n}=o;return{["&".concat(e," ").concat(e,"-color-").concat(t)]:{background:n,["&:not(".concat(e,"-count)")]:{color:n},"a:hover &":{background:n}}}});return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,["".concat(e,"-count")]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:b,height:b,color:t.badgeTextColor,fontWeight:s,fontSize:r,lineHeight:(0,d.zA)(b),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:w(b).div(2).equal(),boxShadow:"0 0 0 ".concat((0,d.zA)(a)," ").concat(t.badgeShadowColor),transition:"background ".concat(t.motionDurationMid),a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},["".concat(e,"-count-sm")]:{minWidth:p,height:p,fontSize:c,lineHeight:(0,d.zA)(p),borderRadius:w(p).div(2).equal()},["".concat(e,"-multiple-words")]:{padding:"0 ".concat((0,d.zA)(t.paddingXS)),bdi:{unicodeBidi:"plaintext"}},["".concat(e,"-dot")]:{zIndex:t.indicatorZIndex,width:l,minWidth:l,height:l,background:t.badgeColor,borderRadius:"100%",boxShadow:"0 0 0 ".concat((0,d.zA)(a)," ").concat(t.badgeShadowColor)},["".concat(e,"-count, ").concat(e,"-dot, ").concat(C,"-custom-component")]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",["&".concat(o,"-spin")]:{animationName:O,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},["&".concat(e,"-status")]:{lineHeight:"inherit",verticalAlign:"baseline",["".concat(e,"-status-dot")]:{position:"relative",top:-1,display:"inline-block",width:i,height:i,verticalAlign:"middle",borderRadius:"50%"},["".concat(e,"-status-success")]:{backgroundColor:t.colorSuccess},["".concat(e,"-status-processing")]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:a,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:g,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},["".concat(e,"-status-default")]:{backgroundColor:t.colorTextPlaceholder},["".concat(e,"-status-error")]:{backgroundColor:t.colorError},["".concat(e,"-status-warning")]:{backgroundColor:t.colorWarning},["".concat(e,"-status-text")]:{marginInlineStart:S,color:t.colorText,fontSize:t.fontSize}}}),x),{["".concat(e,"-zoom-appear, ").concat(e,"-zoom-enter")]:{animationName:f,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["".concat(e,"-zoom-leave")]:{animationName:v,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["&".concat(e,"-not-a-wrapper")]:{["".concat(e,"-zoom-appear, ").concat(e,"-zoom-enter")]:{animationName:h,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["".concat(e,"-zoom-leave")]:{animationName:y,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["&:not(".concat(e,"-status)")]:{verticalAlign:"middle"},["".concat(C,"-custom-component, ").concat(e,"-count")]:{transform:"none"},["".concat(C,"-custom-component, ").concat(C)]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[C]:{overflow:"hidden",transition:"all ".concat(t.motionDurationMid," ").concat(t.motionEaseOutBack),["".concat(C,"-only")]:{position:"relative",display:"inline-block",height:b,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseOutBack),WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",["> p".concat(C,"-only-unit")]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},["".concat(C,"-symbol")]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",["".concat(e,"-count, ").concat(e,"-dot, ").concat(C,"-custom-component")]:{transform:"translate(-50%, -50%)"}}})}},w=t=>{let{fontHeight:e,lineWidth:o,marginXS:n,colorBorderBg:a}=t,r=t.colorTextLightSolid,c=t.colorError,i=t.colorErrorHover;return(0,b.oX)(t,{badgeFontHeight:e,badgeShadowSize:o,badgeTextColor:r,badgeColor:c,badgeColorHover:i,badgeShadowColor:a,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},C=t=>{let{fontSize:e,lineHeight:o,fontSizeSM:n,lineWidth:a}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(e*o)-2*a,indicatorHeightSM:e,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}},x=(0,p.OF)("Badge",t=>S(w(t)),C),E=t=>{let{antCls:e,badgeFontHeight:o,marginXS:n,badgeRibbonOffset:a,calc:r}=t,c="".concat(e,"-ribbon"),i=(0,m.A)(t,(t,e)=>{let{darkColor:o}=e;return{["&".concat(c,"-color-").concat(t)]:{background:o,color:o}}});return{["".concat(e,"-ribbon-wrapper")]:{position:"relative"},[c]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"absolute",top:n,padding:"0 ".concat((0,d.zA)(t.paddingXS)),color:t.colorPrimary,lineHeight:(0,d.zA)(o),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,["".concat(c,"-text")]:{color:t.badgeTextColor},["".concat(c,"-corner")]:{position:"absolute",top:"100%",width:a,height:a,color:"currentcolor",border:"".concat((0,d.zA)(r(a).div(2).equal())," solid"),transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),i),{["&".concat(c,"-placement-end")]:{insetInlineEnd:r(a).mul(-1).equal(),borderEndEndRadius:0,["".concat(c,"-corner")]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},["&".concat(c,"-placement-start")]:{insetInlineStart:r(a).mul(-1).equal(),borderEndStartRadius:0,["".concat(c,"-corner")]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},j=(0,p.OF)(["Badge","Ribbon"],t=>E(w(t)),C),N=t=>{let e;let{prefixCls:o,value:a,current:c,offset:i=0}=t;return i&&(e={position:"absolute",top:"".concat(i,"00%"),left:0}),n.createElement("span",{style:e,className:r()("".concat(o,"-only-unit"),{current:c})},a)},k=t=>{let e,o;let{prefixCls:a,count:r,value:c}=t,i=Number(c),l=Math.abs(r),[s,d]=n.useState(i),[u,m]=n.useState(l),b=()=>{d(i),m(l)};if(n.useEffect(()=>{let t=setTimeout(b,1e3);return()=>clearTimeout(t)},[i]),s===i||Number.isNaN(i)||Number.isNaN(s))e=[n.createElement(N,Object.assign({},t,{key:i,current:!0}))],o={transition:"none"};else{e=[];let a=i+10,r=[];for(let t=i;t<=a;t+=1)r.push(t);let c=u<l?1:-1,d=r.findIndex(t=>t%10===s);e=(c<0?r.slice(0,d+1):r.slice(d)).map((e,o)=>n.createElement(N,Object.assign({},t,{key:e,value:e%10,offset:c<0?o-d:o,current:o===d}))),o={transform:"translateY(".concat(-function(t,e,o){let n=t,a=0;for(;(n+10)%10!==e;)n+=o,a+=o;return a}(s,i,c),"00%)")}}return n.createElement("span",{className:"".concat(a,"-only"),style:o,onTransitionEnd:b},e)};var I=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)0>e.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(o[n[a]]=t[n[a]]);return o};let A=n.forwardRef((t,e)=>{let{prefixCls:o,count:a,className:c,motionClassName:i,style:d,title:u,show:m,component:b="sup",children:p}=t,g=I(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=n.useContext(s.QO),v=f("scroll-number",o),h=Object.assign(Object.assign({},g),{"data-show":m,style:d,className:r()(v,c,i),title:u}),y=a;if(a&&Number(a)%1==0){let t=String(a).split("");y=n.createElement("bdi",null,t.map((e,o)=>n.createElement(k,{prefixCls:v,count:Number(a),value:e,key:t.length-o})))}return((null==d?void 0:d.borderColor)&&(h.style=Object.assign(Object.assign({},d),{boxShadow:"0 0 0 1px ".concat(d.borderColor," inset")})),p)?(0,l.Ob)(p,t=>({className:r()("".concat(v,"-custom-component"),null==t?void 0:t.className,i)})):n.createElement(b,Object.assign({},h,{ref:e}),y)});var z=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)0>e.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(o[n[a]]=t[n[a]]);return o};let M=n.forwardRef((t,e)=>{var o,a,d,u,m;let{prefixCls:b,scrollNumberPrefixCls:p,children:g,status:f,text:v,color:h,count:y=null,overflowCount:O=99,dot:S=!1,size:w="default",title:C,offset:E,style:j,className:N,rootClassName:k,classNames:I,styles:M,showZero:P=!1}=t,R=z(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:T,direction:B,badge:H}=n.useContext(s.QO),D=T("badge",b),[F,W,L]=x(D),Z=y>O?"".concat(O,"+"):y,q="0"===Z||0===Z,X=(null!=f||null!=h)&&(null===y||q&&!P),_=S&&!q,Q=_?"":Z,V=(0,n.useMemo)(()=>(null==Q||""===Q||q&&!P)&&!_,[Q,q,P,_]),G=(0,n.useRef)(y);V||(G.current=y);let Y=G.current,K=(0,n.useRef)(Q);V||(K.current=Q);let J=K.current,U=(0,n.useRef)(_);V||(U.current=_);let $=(0,n.useMemo)(()=>{if(!E)return Object.assign(Object.assign({},null==H?void 0:H.style),j);let t={marginTop:E[1]};return"rtl"===B?t.left=parseInt(E[0],10):t.right=-parseInt(E[0],10),Object.assign(Object.assign(Object.assign({},t),null==H?void 0:H.style),j)},[B,E,j,null==H?void 0:H.style]),tt=null!=C?C:"string"==typeof Y||"number"==typeof Y?Y:void 0,te=V||!v?null:n.createElement("span",{className:"".concat(D,"-status-text")},v),to=Y&&"object"==typeof Y?(0,l.Ob)(Y,t=>({style:Object.assign(Object.assign({},$),t.style)})):void 0,tn=(0,i.nP)(h,!1),ta=r()(null==I?void 0:I.indicator,null===(o=null==H?void 0:H.classNames)||void 0===o?void 0:o.indicator,{["".concat(D,"-status-dot")]:X,["".concat(D,"-status-").concat(f)]:!!f,["".concat(D,"-color-").concat(h)]:tn}),tr={};h&&!tn&&(tr.color=h,tr.background=h);let tc=r()(D,{["".concat(D,"-status")]:X,["".concat(D,"-not-a-wrapper")]:!g,["".concat(D,"-rtl")]:"rtl"===B},N,k,null==H?void 0:H.className,null===(a=null==H?void 0:H.classNames)||void 0===a?void 0:a.root,null==I?void 0:I.root,W,L);if(!g&&X){let t=$.color;return F(n.createElement("span",Object.assign({},R,{className:tc,style:Object.assign(Object.assign(Object.assign({},null==M?void 0:M.root),null===(d=null==H?void 0:H.styles)||void 0===d?void 0:d.root),$)}),n.createElement("span",{className:ta,style:Object.assign(Object.assign(Object.assign({},null==M?void 0:M.indicator),null===(u=null==H?void 0:H.styles)||void 0===u?void 0:u.indicator),tr)}),v&&n.createElement("span",{style:{color:t},className:"".concat(D,"-status-text")},v)))}return F(n.createElement("span",Object.assign({ref:e},R,{className:tc,style:Object.assign(Object.assign({},null===(m=null==H?void 0:H.styles)||void 0===m?void 0:m.root),null==M?void 0:M.root)}),g,n.createElement(c.Ay,{visible:!V,motionName:"".concat(D,"-zoom"),motionAppear:!1,motionDeadline:1e3},t=>{var e,o;let{className:a}=t,c=T("scroll-number",p),i=U.current,l=r()(null==I?void 0:I.indicator,null===(e=null==H?void 0:H.classNames)||void 0===e?void 0:e.indicator,{["".concat(D,"-dot")]:i,["".concat(D,"-count")]:!i,["".concat(D,"-count-sm")]:"small"===w,["".concat(D,"-multiple-words")]:!i&&J&&J.toString().length>1,["".concat(D,"-status-").concat(f)]:!!f,["".concat(D,"-color-").concat(h)]:tn}),s=Object.assign(Object.assign(Object.assign({},null==M?void 0:M.indicator),null===(o=null==H?void 0:H.styles)||void 0===o?void 0:o.indicator),$);return h&&!tn&&((s=s||{}).background=h),n.createElement(A,{prefixCls:c,show:!V,motionClassName:a,className:l,count:J,title:tt,style:s,key:"scrollNumber"},to)}),te))});M.Ribbon=t=>{let{className:e,prefixCls:o,style:a,color:c,children:l,text:d,placement:u="end",rootClassName:m}=t,{getPrefixCls:b,direction:p}=n.useContext(s.QO),g=b("ribbon",o),f="".concat(g,"-wrapper"),[v,h,y]=j(g,f),O=(0,i.nP)(c,!1),S=r()(g,"".concat(g,"-placement-").concat(u),{["".concat(g,"-rtl")]:"rtl"===p,["".concat(g,"-color-").concat(c)]:O},e),w={},C={};return c&&!O&&(w.background=c,C.color=c),v(n.createElement("div",{className:r()(f,m,h,y)},l,n.createElement("div",{className:r()(S,h),style:Object.assign(Object.assign({},w),a)},n.createElement("span",{className:"".concat(g,"-text")},d),n.createElement("div",{className:"".concat(g,"-corner"),style:C}))))};let P=M},76046:(t,e,o)=>{var n=o(66658);o.o(n,"usePathname")&&o.d(e,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(e,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(e,{useSearchParams:function(){return n.useSearchParams}}),o.o(n,"useServerInsertedHTML")&&o.d(e,{useServerInsertedHTML:function(){return n.useServerInsertedHTML}})}}]);