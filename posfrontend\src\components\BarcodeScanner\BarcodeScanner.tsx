"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import { CameraOutlined, CloseOutlined, ScanOutlined } from '@ant-design/icons';
import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';

interface BarcodeScannerProps {
  isOpen: boolean;
  onClose: () => void;
  onScan: (barcode: string) => void;
  title?: string;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  isOpen,
  onClose,
  onScan,
  title = "Scan Product Barcode"
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const codeReaderRef = useRef<BrowserMultiFormatReader | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startScanning = useCallback(async () => {
    if (!videoRef.current || !codeReaderRef.current) return;

    try {
      setIsScanning(true);
      setError(null);

      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      streamRef.current = stream;
      setHasPermission(true);

      // Set video source
      videoRef.current.srcObject = stream;

      // Start decoding
      codeReaderRef.current.decodeFromVideoDevice(
        null, // Use default video device
        videoRef.current,
        (result, error) => {
          if (result) {
            const barcode = result.getText();
            console.log('Barcode scanned:', barcode);
            onScan(barcode);
            stopScanning();
            onClose();
          }
          
          if (error && !(error instanceof NotFoundException)) {
            console.error('Barcode scanning error:', error);
          }
        }
      );

    } catch (err) {
      console.error('Camera access error:', err);
      setHasPermission(false);
      setError('Camera access denied. Please allow camera permissions and try again.');
      setIsScanning(false);
    }
  }, [onScan, onClose]);

  // Initialize barcode reader
  useEffect(() => {
    if (isOpen) {
      codeReaderRef.current = new BrowserMultiFormatReader();
      startScanning();
    }

    return () => {
      stopScanning();
    };
  }, [isOpen, startScanning]);

  const stopScanning = () => {
    // Stop the video stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Reset the code reader
    if (codeReaderRef.current) {
      codeReaderRef.current.reset();
    }

    setIsScanning(false);
  };

  const handleClose = () => {
    stopScanning();
    onClose();
  };

  const handleRetry = () => {
    setError(null);
    setHasPermission(null);
    startScanning();
  };

  return (
    <Modal
      title={
        <div className="flex items-center text-gray-800">
          <ScanOutlined className="mr-2 text-blue-600" />
          <span>{title}</span>
        </div>
      }
      open={isOpen}
      onCancel={handleClose}
      footer={[
        <Button key="close" onClick={handleClose}>
          Close
        </Button>,
        ...(error ? [
          <Button key="retry" type="primary" onClick={handleRetry}>
            Retry
          </Button>
        ] : [])
      ]}
      width={600}
      centered
      className="barcode-scanner-modal"
    >
      <div className="flex flex-col items-center">
        {error ? (
          <div className="w-full mb-4">
            <Alert
              message="Camera Error"
              description={error}
              type="error"
              showIcon
              className="mb-4"
            />
            <div className="text-center">
              <CameraOutlined className="text-6xl text-gray-400 mb-4" />
              <p className="text-gray-600">
                Please ensure your device has a camera and you&apos;ve granted camera permissions.
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <div className="relative bg-black rounded-lg overflow-hidden mb-4">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-80 object-cover"
                style={{ maxWidth: '100%' }}
              />
              
              {/* Scanning overlay */}
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="border-2 border-blue-500 bg-transparent rounded-lg w-64 h-32 relative">
                  <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                  <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                  <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                  <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>
                  
                  {/* Scanning line animation */}
                  <div className="absolute inset-x-0 top-1/2 h-0.5 bg-blue-500 animate-pulse"></div>
                </div>
              </div>

              {/* Loading indicator */}
              {isScanning && hasPermission === null && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                  <Spin size="large" />
                </div>
              )}
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <ScanOutlined className="text-2xl text-blue-600 mr-2" />
                <span className="text-lg font-medium text-gray-800">
                  Position barcode within the frame
                </span>
              </div>
              <p className="text-gray-600 text-sm">
                Hold your device steady and ensure the barcode is clearly visible
              </p>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default BarcodeScanner;
