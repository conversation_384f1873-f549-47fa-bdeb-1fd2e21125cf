// services/purchaseApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Purchase types
export interface Purchase {
  id: number;
  supplier: string;
  product: string;
  quantity: number;
  costPrice: string;
  totalCost: string;
  purchaseDate: string;
  purchasedBy: string;
}

export interface PaginatedPurchases {
  total: number;
  page: number;
  perPage: number;
  purchases: Purchase[];
}

export interface CreatePurchaseDto {
  supplierId?: number;
  productId: number;
  quantity: number;
  costPrice: string;
  totalCost: string;
}

export interface UpdatePurchaseDto {
  quantity?: number;
  costPrice?: string;
  totalCost?: string;
}

export const purchaseApi = createApi({
  reducerPath: 'purchaseApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Purchase'] as const,
  endpoints: (builder) => ({
    // Get all purchases (paginated)
    getAllPurchases: builder.query<ApiResponse<PaginatedPurchases>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Purchase'],
    }),

    // Get purchase by ID
    getPurchaseById: builder.query<ApiResponse<Purchase>, number>({
      query: (purchaseId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'retrieve',
            purchaseId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'Purchase', id }],
    }),

    // Create new purchase
    createPurchase: builder.mutation<ApiResponse<Purchase>, CreatePurchaseDto>({
      query: (purchaseData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'createnew',
            ...purchaseData,
          },
          token,
        };
      },
      invalidatesTags: ['Purchase'],
    }),

    // Update purchase
    updatePurchase: builder.mutation<ApiResponse<Purchase>, { purchaseId: number; data: UpdatePurchaseDto }>({
      query: ({ purchaseId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'update',
            purchaseId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { purchaseId }) => [
        { type: 'Purchase', id: purchaseId },
        'Purchase',
      ],
    }),

    // Delete purchase (single)
    deletePurchase: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (purchaseId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'delete',
            purchaseId,
          },
          token,
        };
      },
      invalidatesTags: ['Purchase'],
    }),

    // Bulk delete purchases
    bulkDeletePurchases: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (purchaseIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/purchases',
          payloaddata: {
            mode: 'delete',
            purchaseIds,
          },
          token,
        };
      },
      invalidatesTags: ['Purchase'],
    }),
  }),
});

export const {
  useGetAllPurchasesQuery,
  useGetPurchaseByIdQuery,
  useCreatePurchaseMutation,
  useUpdatePurchaseMutation,
  useDeletePurchaseMutation,
  useBulkDeletePurchasesMutation,
} = purchaseApi;
