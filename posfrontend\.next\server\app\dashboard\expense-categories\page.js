(()=>{var e={};e.id=137,e.ids=[137],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},67446:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(70260),a=r(28203),l=r(25155),o=r.n(l),i=r(67292),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);r.d(t,s);let c=["",{children:["dashboard",{children:["expense-categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93402)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expense-categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expense-categories\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/expense-categories/page",pathname:"/dashboard/expense-categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},39266:(e,t,r)=>{Promise.resolve().then(r.bind(r,93402))},57418:(e,t,r)=>{Promise.resolve().then(r.bind(r,17304))},97464:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"};var o=r(78480);let i=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},4472:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var o=r(78480);let i=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},99730:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};var o=r(78480);let i=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},12325:(e,t,r)=>{"use strict";r.d(t,{A:()=>O});var n=r(58009),a=r(56073),l=r.n(a),o=r(61849),i=r(73924);let s=e=>e?"function"==typeof e?e():e:null;var c=r(46219),d=r(2866),u=r(70001),m=r(60495),g=r(27343),p=r(47285),f=r(66801),h=r(36725),b=r(50127),v=r(85094),x=r(13662),y=r(10941);let C=e=>{let{componentCls:t,popoverColor:r,titleMinWidth:n,fontWeightStrong:a,innerPadding:l,boxShadowSecondary:o,colorTextHeading:i,borderRadiusLG:s,zIndexPopup:c,titleMarginBottom:d,colorBgElevated:u,popoverBg:m,titleBorderBottom:g,innerContentPadding:f,titlePadding:b}=e;return[{[t]:Object.assign(Object.assign({},(0,p.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:c,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":u,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:s,boxShadow:o,padding:l},[`${t}-title`]:{minWidth:n,marginBottom:d,color:i,fontWeight:a,borderBottom:g,padding:b},[`${t}-inner-content`]:{color:r,padding:f}})},(0,h.Ay)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},A=e=>{let{componentCls:t}=e;return{[t]:v.s.map(r=>{let n=e[`${r}6`];return{[`&${t}-${r}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}})}},j=(0,x.OF)("Popover",e=>{let{colorBgElevated:t,colorText:r}=e,n=(0,y.oX)(e,{popoverBg:t,popoverColor:r});return[C(n),A(n),(0,f.aB)(n,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:r,fontHeight:n,padding:a,wireframe:l,zIndexPopupBase:o,borderRadiusLG:i,marginXS:s,lineType:c,colorSplit:d,paddingSM:u}=e,m=r-n;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:o+30},(0,b.n)(e)),(0,h.Ke)({contentRadius:i,limitVerticalRadius:!0})),{innerPadding:l?0:12,titleMarginBottom:l?0:s,titlePadding:l?`${m/2}px ${a}px ${m/2-t}px`:0,titleBorderBottom:l?`${t}px ${c} ${d}`:"none",innerContentPadding:l?`${u}px ${a}px`:0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var $=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let w=e=>{let{title:t,content:r,prefixCls:a}=e;return t||r?n.createElement(n.Fragment,null,t&&n.createElement("div",{className:`${a}-title`},t),r&&n.createElement("div",{className:`${a}-inner-content`},r)):null},k=e=>{let{hashId:t,prefixCls:r,className:a,style:o,placement:i="top",title:c,content:d,children:u}=e,g=s(c),p=s(d),f=l()(t,r,`${r}-pure`,`${r}-placement-${i}`,a);return n.createElement("div",{className:f,style:o},n.createElement("div",{className:`${r}-arrow`}),n.createElement(m.z,Object.assign({},e,{className:t,prefixCls:r}),u||n.createElement(w,{prefixCls:r,title:g,content:p})))};var E=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let S=n.forwardRef((e,t)=>{var r,a;let{prefixCls:m,title:p,content:f,overlayClassName:h,placement:b="top",trigger:v="hover",children:x,mouseEnterDelay:y=.1,mouseLeaveDelay:C=.1,onOpenChange:A,overlayStyle:$={},styles:k,classNames:S}=e,O=E(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:N,className:M,style:P,classNames:D,styles:z}=(0,g.TP)("popover"),R=N("popover",m),[H,I,B]=j(R),F=N(),q=l()(h,I,B,M,D.root,null==S?void 0:S.root),L=l()(D.body,null==S?void 0:S.body),[T,W]=(0,o.A)(!1,{value:null!==(r=e.open)&&void 0!==r?r:e.visible,defaultValue:null!==(a=e.defaultOpen)&&void 0!==a?a:e.defaultVisible}),V=(e,t)=>{W(e,!0),null==A||A(e,t)},G=e=>{e.keyCode===i.A.ESC&&V(!1,e)},_=s(p),X=s(f);return H(n.createElement(u.A,Object.assign({placement:b,trigger:v,mouseEnterDelay:y,mouseLeaveDelay:C},O,{prefixCls:R,classNames:{root:q,body:L},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},z.root),P),$),null==k?void 0:k.root),body:Object.assign(Object.assign({},z.body),null==k?void 0:k.body)},ref:t,open:T,onOpenChange:e=>{V(e)},overlay:_||X?n.createElement(w,{prefixCls:R,title:_,content:X}):null,transitionName:(0,c.b)(F,"zoom-big",O.transitionName),"data-popover-inject":!0}),(0,d.Ob)(x,{onKeyDown:e=>{var t,r;n.isValidElement(x)&&(null===(r=null==x?void 0:(t=x.props).onKeyDown)||void 0===r||r.call(t,e)),G(e)}})))});S._InternalPanelDoNotUseOrYouWillBeFired=e=>{let{prefixCls:t,className:r}=e,a=$(e,["prefixCls","className"]),{getPrefixCls:o}=n.useContext(g.QO),i=o("popover",t),[s,c,d]=j(i);return s(n.createElement(k,Object.assign({},a,{prefixCls:i,hashId:c,className:l()(r,d)})))};let O=S},17304:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tK});var n=r(45512),a=r(58009),l=r.n(a),o=r(6987),i=r(3117),s=r(37764),c=r(21419),d=r(56403),u=r(37287),m=r(58733),g=r(88752),p=r(99730),f=r(60636),h=r(765),b=r(48991),v=r(77067),x=r(31111),y=r(70001),C=r(25421),A=r(11855);let j={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var $=r(78480),w=a.forwardRef(function(e,t){return a.createElement($.A,(0,A.A)({},e,{ref:t,icon:j}))}),k=r(99261),E=r(86977),S=r(63844),O=r(73542),N=r(92273);let M=({categories:e,loading:t=!1,onEdit:r,onDelete:a,onBulkDelete:l,selectedCategories:o=[],onSelectionChange:s,isMobile:c=!1})=>{let d=(0,O.E)(),u=c||d,m=(0,N.d4)(e=>e.auth.user),g=m?.role,f="admin"===g||"superadmin"===g,h="admin"===g||"superadmin"===g,b=(e,t)=>{s&&s(t?[...o,e]:o.filter(t=>t!==e))},A=t=>{s&&(t.target.checked?s(e.filter(e=>!e.isDefault).map(e=>e.id)):s([]))},j=e.filter(e=>!e.isDefault),$=j.length>0&&o.length===j.length,M=o.length>0&&o.length<j.length;return(0,n.jsxs)("div",{children:[o.length>0&&h&&l&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"text-red-700",children:[o.length," category(s) selected"]}),(0,n.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,n.jsx)(C.A,{}),onClick:()=>l(o),size:u?"small":"middle",children:"Delete Selected"})]})}),u?(0,n.jsxs)(S.jB,{columns:s?"50px 200px 120px 100px 150px":"200px 120px 100px 150px",minWidth:s?"700px":"650px",children:[s&&(0,n.jsx)(S.A0,{className:"text-center",children:(0,n.jsx)(v.A,{indeterminate:M,checked:$,onChange:A})}),(0,n.jsx)(S.A0,{children:(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(p.A,{className:"mr-1"}),"Category"]})}),(0,n.jsx)(S.A0,{children:"Color"}),(0,n.jsx)(S.A0,{children:"Type"}),(0,n.jsx)(S.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,n.jsxs)(S.Hj,{selected:o.includes(e.id),children:[s&&(0,n.jsx)(S.nA,{className:"text-center",children:!e.isDefault&&(0,n.jsx)(v.A,{checked:o.includes(e.id),onChange:t=>b(e.id,t.target.checked)})}),(0,n.jsx)(S.nA,{children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full mr-2",style:{backgroundColor:e.color}}),(0,n.jsxs)("div",{className:"max-w-[150px] overflow-hidden text-ellipsis font-medium",children:[e.name,e.isDefault&&(0,n.jsx)(w,{className:"ml-1 text-yellow-500",title:"System Default"})]})]})}),(0,n.jsx)(S.nA,{children:(0,n.jsx)(x.A,{color:e.color,className:"text-xs",children:e.color})}),(0,n.jsx)(S.nA,{children:e.isDefault?(0,n.jsx)(x.A,{color:"gold",icon:(0,n.jsx)(w,{}),className:"text-xs",children:"Default"}):(0,n.jsx)(x.A,{color:"blue",className:"text-xs",children:"Custom"})}),(0,n.jsx)(S.nA,{className:"text-right",children:(0,n.jsxs)("div",{className:"flex justify-end space-x-1",children:[f&&r&&!e.isDefault&&(0,n.jsx)(y.A,{title:"Edit",children:(0,n.jsx)(i.Ay,{type:"text",size:"small",icon:(0,n.jsx)(k.A,{}),onClick:()=>r(e),className:"text-blue-500 hover:text-blue-400"})}),h&&a&&!e.isDefault&&(0,n.jsx)(y.A,{title:"Delete",children:(0,n.jsx)(i.Ay,{type:"text",size:"small",danger:!0,icon:(0,n.jsx)(E.A,{}),onClick:()=>a(e.id),className:"text-red-500 hover:text-red-400"})})]})})]},e.id))]}):(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsxs)("tr",{children:[s&&(0,n.jsx)("th",{className:"px-4 py-3 text-left",children:(0,n.jsx)(v.A,{indeterminate:M,checked:$,onChange:A})}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Color"}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,n.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,n.jsxs)("tr",{className:"hover:bg-gray-50",children:[s&&(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:!e.isDefault&&(0,n.jsx)(v.A,{checked:o.includes(e.id),onChange:t=>b(e.id,t.target.checked)})}),(0,n.jsx)("td",{className:"px-4 py-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"text-sm font-medium text-gray-900 flex items-center",children:[e.name,e.isDefault&&(0,n.jsx)(w,{className:"ml-2 text-yellow-500",title:"System Default"})]})})]})}),(0,n.jsx)("td",{className:"px-4 py-4",children:(0,n.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",children:e.description||"-"})}),(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,n.jsx)(x.A,{color:e.color,className:"text-xs",children:e.color})}),(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:e.isDefault?(0,n.jsx)(x.A,{color:"gold",icon:(0,n.jsx)(w,{}),children:"System Default"}):(0,n.jsx)(x.A,{color:"blue",children:"Custom"})}),(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,n.jsxs)("div",{className:"flex justify-end space-x-2",children:[f&&r&&!e.isDefault&&(0,n.jsx)(y.A,{title:"Edit",children:(0,n.jsx)(i.Ay,{type:"text",size:"small",icon:(0,n.jsx)(k.A,{}),onClick:()=>r(e),className:"text-blue-500"})}),h&&a&&!e.isDefault&&(0,n.jsx)(y.A,{title:"Delete",children:(0,n.jsx)(i.Ay,{type:"text",size:"small",danger:!0,icon:(0,n.jsx)(E.A,{}),onClick:()=>a(e.id),className:"text-red-500"})})]})})]},e.id))})]})})]})};var P=r(85187),D=r(41257),z=r(56073),R=r.n(z),H=r(61849),I=r(93629),B=r(80349),F=r(92534),q=r(27343),L=r(87375),T=r(90334),W=r(43089),V=r(53421),G=r(12325),_=r(66799),X=r(7959),K=r(9334),U=r(43984),Y=r(75702),Z=r(55977),J=r(7770),Q=r(49543),ee=r(65074),et=r(12992),er=r(97549),en=r(55681),ea=r(80799),el=r(80775),eo=function(e,t){if(!e)return null;var r={left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth,top:e.offsetTop,bottom:e.parentElement.clientHeight-e.clientHeight-e.offsetTop,height:e.clientHeight};return t?{left:0,right:0,width:0,top:r.top,bottom:r.bottom,height:r.height}:{left:r.left,right:r.right,width:r.width,top:0,bottom:0,height:0}},ei=function(e){return void 0!==e?"".concat(e,"px"):void 0};function es(e){var t=e.prefixCls,r=e.containerRef,n=e.value,l=e.getValueIndex,o=e.motionName,i=e.onMotionStart,s=e.onMotionEnd,c=e.direction,d=e.vertical,u=void 0!==d&&d,m=a.useRef(null),g=a.useState(n),p=(0,J.A)(g,2),f=p[0],h=p[1],b=function(e){var n,a=l(e),o=null===(n=r.current)||void 0===n?void 0:n.querySelectorAll(".".concat(t,"-item"))[a];return(null==o?void 0:o.offsetParent)&&o},v=a.useState(null),x=(0,J.A)(v,2),y=x[0],C=x[1],A=a.useState(null),j=(0,J.A)(A,2),$=j[0],w=j[1];(0,Z.A)(function(){if(f!==n){var e=b(f),t=b(n),r=eo(e,u),a=eo(t,u);h(n),C(r),w(a),e&&t?i():s()}},[n]);var k=a.useMemo(function(){if(u){var e;return ei(null!==(e=null==y?void 0:y.top)&&void 0!==e?e:0)}return"rtl"===c?ei(-(null==y?void 0:y.right)):ei(null==y?void 0:y.left)},[u,c,y]),E=a.useMemo(function(){if(u){var e;return ei(null!==(e=null==$?void 0:$.top)&&void 0!==e?e:0)}return"rtl"===c?ei(-(null==$?void 0:$.right)):ei(null==$?void 0:$.left)},[u,c,$]);return y&&$?a.createElement(el.Ay,{visible:!0,motionName:o,motionAppear:!0,onAppearStart:function(){return u?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},onAppearActive:function(){return u?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},onVisibleChanged:function(){C(null),w(null),s()}},function(e,r){var n=e.className,l=e.style,o=(0,et.A)((0,et.A)({},l),{},{"--thumb-start-left":k,"--thumb-start-width":ei(null==y?void 0:y.width),"--thumb-active-left":E,"--thumb-active-width":ei(null==$?void 0:$.width),"--thumb-start-top":k,"--thumb-start-height":ei(null==y?void 0:y.height),"--thumb-active-top":E,"--thumb-active-height":ei(null==$?void 0:$.height)}),i={ref:(0,ea.K4)(m,r),style:o,className:R()("".concat(t,"-thumb"),n)};return a.createElement("div",i)}):null}var ec=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"],ed=function(e){var t=e.prefixCls,r=e.className,n=e.disabled,l=e.checked,o=e.label,i=e.title,s=e.value,c=e.name,d=e.onChange,u=e.onFocus,m=e.onBlur,g=e.onKeyDown,p=e.onKeyUp,f=e.onMouseDown;return a.createElement("label",{className:R()(r,(0,ee.A)({},"".concat(t,"-item-disabled"),n)),onMouseDown:f},a.createElement("input",{name:c,className:"".concat(t,"-item-input"),type:"radio",disabled:n,checked:l,onChange:function(e){n||d(e,s)},onFocus:u,onBlur:m,onKeyDown:g,onKeyUp:p}),a.createElement("div",{className:"".concat(t,"-item-label"),title:i,"aria-selected":l},o))},eu=a.forwardRef(function(e,t){var r,n,l=e.prefixCls,o=void 0===l?"rc-segmented":l,i=e.direction,s=e.vertical,c=e.options,d=void 0===c?[]:c,u=e.disabled,m=e.defaultValue,g=e.value,p=e.name,f=e.onChange,h=e.className,b=e.motionName,v=(0,Q.A)(e,ec),x=a.useRef(null),y=a.useMemo(function(){return(0,ea.K4)(x,t)},[x,t]),C=a.useMemo(function(){return d.map(function(e){if("object"===(0,er.A)(e)&&null!==e){var t=function(e){if(void 0!==e.title)return e.title;if("object"!==(0,er.A)(e.label)){var t;return null===(t=e.label)||void 0===t?void 0:t.toString()}}(e);return(0,et.A)((0,et.A)({},e),{},{title:t})}return{label:null==e?void 0:e.toString(),title:null==e?void 0:e.toString(),value:e}})},[d]),j=(0,H.A)(null===(r=C[0])||void 0===r?void 0:r.value,{value:g,defaultValue:m}),$=(0,J.A)(j,2),w=$[0],k=$[1],E=a.useState(!1),S=(0,J.A)(E,2),O=S[0],N=S[1],M=function(e,t){k(t),null==f||f(t)},P=(0,en.A)(v,["children"]),D=a.useState(!1),z=(0,J.A)(D,2),I=z[0],B=z[1],F=a.useState(!1),q=(0,J.A)(F,2),L=q[0],T=q[1],W=function(){T(!0)},V=function(){T(!1)},G=function(){B(!1)},_=function(e){"Tab"===e.key&&B(!0)},X=function(e){var t=C.findIndex(function(e){return e.value===w}),r=C.length,n=C[(t+e+r)%r];n&&(k(n.value),null==f||f(n.value))},K=function(e){switch(e.key){case"ArrowLeft":case"ArrowUp":X(-1);break;case"ArrowRight":case"ArrowDown":X(1)}};return a.createElement("div",(0,A.A)({role:"radiogroup","aria-label":"segmented control",tabIndex:u?void 0:0},P,{className:R()(o,(n={},(0,ee.A)(n,"".concat(o,"-rtl"),"rtl"===i),(0,ee.A)(n,"".concat(o,"-disabled"),u),(0,ee.A)(n,"".concat(o,"-vertical"),s),n),void 0===h?"":h),ref:y}),a.createElement("div",{className:"".concat(o,"-group")},a.createElement(es,{vertical:s,prefixCls:o,value:w,containerRef:x,motionName:"".concat(o,"-").concat(void 0===b?"thumb-motion":b),direction:i,getValueIndex:function(e){return C.findIndex(function(t){return t.value===e})},onMotionStart:function(){N(!0)},onMotionEnd:function(){N(!1)}}),C.map(function(e){var t;return a.createElement(ed,(0,A.A)({},e,{name:p,key:e.value,prefixCls:o,className:R()(e.className,"".concat(o,"-item"),(t={},(0,ee.A)(t,"".concat(o,"-item-selected"),e.value===w&&!O),(0,ee.A)(t,"".concat(o,"-item-focused"),L&&I&&e.value===w),t)),checked:e.value===w,onChange:M,onFocus:W,onBlur:V,onKeyDown:K,onKeyUp:_,onMouseDown:G,disabled:!!u||!!e.disabled}))})))}),em=r(68855),eg=r(1439),ep=r(47285),ef=r(13662),eh=r(10941);function eb(e,t){return{[`${e}, ${e}:hover, ${e}:focus`]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function ev(e){return{backgroundColor:e.itemSelectedBg,boxShadow:e.boxShadowTertiary}}let ex=Object.assign({overflow:"hidden"},ep.L9),ey=e=>{let{componentCls:t}=e,r=e.calc(e.controlHeight).sub(e.calc(e.trackPadding).mul(2)).equal(),n=e.calc(e.controlHeightLG).sub(e.calc(e.trackPadding).mul(2)).equal(),a=e.calc(e.controlHeightSM).sub(e.calc(e.trackPadding).mul(2)).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,ep.dF)(e)),{display:"inline-block",padding:e.trackPadding,color:e.itemColor,background:e.trackBg,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`}),(0,ep.K8)(e)),{[`${t}-group`]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",flexDirection:"row",width:"100%"},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-vertical`]:{[`${t}-group`]:{flexDirection:"column"},[`${t}-thumb`]:{width:"100%",height:0,padding:`0 ${(0,eg.zA)(e.paddingXXS)}`}},[`&${t}-block`]:{display:"flex"},[`&${t}-block ${t}-item`]:{flex:1,minWidth:0},[`${t}-item`]:{position:"relative",textAlign:"center",cursor:"pointer",transition:`color ${e.motionDurationMid} ${e.motionEaseInOut}`,borderRadius:e.borderRadiusSM,transform:"translateZ(0)","&-selected":Object.assign(Object.assign({},ev(e)),{color:e.itemSelectedColor}),"&-focused":Object.assign({},(0,ep.jk)(e)),"&::after":{content:'""',position:"absolute",zIndex:-1,width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",opacity:0,transition:`opacity ${e.motionDurationMid}`,pointerEvents:"none"},[`&:hover:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemHoverBg}},[`&:active:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemActiveBg}},"&-label":Object.assign({minHeight:r,lineHeight:(0,eg.zA)(r),padding:`0 ${(0,eg.zA)(e.segmentedPaddingHorizontal)}`},ex),"&-icon + *":{marginInlineStart:e.calc(e.marginSM).div(2).equal()},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},[`${t}-thumb`]:Object.assign(Object.assign({},ev(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:`${(0,eg.zA)(e.paddingXXS)} 0`,borderRadius:e.borderRadiusSM,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, height ${e.motionDurationSlow} ${e.motionEaseInOut}`,[`& ~ ${t}-item:not(${t}-item-selected):not(${t}-item-disabled)::after`]:{backgroundColor:"transparent"}}),[`&${t}-lg`]:{borderRadius:e.borderRadiusLG,[`${t}-item-label`]:{minHeight:n,lineHeight:(0,eg.zA)(n),padding:`0 ${(0,eg.zA)(e.segmentedPaddingHorizontal)}`,fontSize:e.fontSizeLG},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadius}},[`&${t}-sm`]:{borderRadius:e.borderRadiusSM,[`${t}-item-label`]:{minHeight:a,lineHeight:(0,eg.zA)(a),padding:`0 ${(0,eg.zA)(e.segmentedPaddingHorizontalSM)}`},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadiusXS}}}),eb(`&-disabled ${t}-item`,e)),eb(`${t}-item-disabled`,e)),{[`${t}-thumb-motion-appear-active`]:{transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, width ${e.motionDurationSlow} ${e.motionEaseInOut}`,willChange:"transform, width"},[`&${t}-shape-round`]:{borderRadius:9999,[`${t}-item, ${t}-thumb`]:{borderRadius:9999}}})}},eC=(0,ef.OF)("Segmented",e=>{let{lineWidth:t,calc:r}=e;return[ey((0,eh.oX)(e,{segmentedPaddingHorizontal:r(e.controlPaddingHorizontal).sub(t).equal(),segmentedPaddingHorizontalSM:r(e.controlPaddingHorizontalSM).sub(t).equal()}))]},e=>{let{colorTextLabel:t,colorText:r,colorFillSecondary:n,colorBgElevated:a,colorFill:l,lineWidthBold:o,colorBgLayout:i}=e;return{trackPadding:o,trackBg:i,itemColor:t,itemHoverColor:r,itemHoverBg:n,itemSelectedBg:a,itemActiveBg:l,itemSelectedColor:r}});var eA=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let ej=a.forwardRef((e,t)=>{let r=(0,em.A)(),{prefixCls:n,className:l,rootClassName:o,block:i,options:s=[],size:c="middle",style:d,vertical:u,shape:m="default",name:g=r}=e,p=eA(e,["prefixCls","className","rootClassName","block","options","size","style","vertical","shape","name"]),{getPrefixCls:f,direction:h,className:b,style:v}=(0,q.TP)("segmented"),x=f("segmented",n),[y,C,A]=eC(x),j=(0,W.A)(c),$=a.useMemo(()=>s.map(e=>{if(function(e){return"object"==typeof e&&!!(null==e?void 0:e.icon)}(e)){let{icon:t,label:r}=e;return Object.assign(Object.assign({},eA(e,["icon","label"])),{label:a.createElement(a.Fragment,null,a.createElement("span",{className:`${x}-item-icon`},t),r&&a.createElement("span",null,r))})}return e}),[s,x]),w=R()(l,o,b,{[`${x}-block`]:i,[`${x}-sm`]:"small"===j,[`${x}-lg`]:"large"===j,[`${x}-vertical`]:u,[`${x}-shape-${m}`]:"round"===m},C,A),k=Object.assign(Object.assign({},v),d);return y(a.createElement(eu,Object.assign({},p,{name:g,className:w,style:k,options:$,ref:t,prefixCls:x,direction:h,vertical:u})))}),e$=l().createContext({}),ew=l().createContext({});var ek=r(53586);let eE=e=>{let{prefixCls:t,value:r,onChange:n}=e;return l().createElement("div",{className:`${t}-clear`,onClick:()=>{if(n&&r&&!r.cleared){let e=r.toHsb();e.a=0;let t=(0,ek.Z6)(e);t.cleared=!0,n(t)}}})};var eS=r(7325),eO=r(88472);let eN=e=>{let{prefixCls:t,min:r=0,max:n=100,value:o,onChange:i,className:s,formatter:c}=e,d=`${t}-steppers`,[u,m]=(0,a.useState)(o);return(0,a.useEffect)(()=>{Number.isNaN(o)||m(o)},[o]),l().createElement(eO.A,{className:R()(d,s),min:r,max:n,value:u,formatter:c,size:"small",onChange:e=>{o||m(e||0),null==i||i(e)}})},eM=e=>{let{prefixCls:t,value:r,onChange:n}=e,o=`${t}-alpha-input`,[i,s]=(0,a.useState)((0,ek.Z6)(r||"#000"));return(0,a.useEffect)(()=>{r&&s(r)},[r]),l().createElement(eN,{value:(0,ek.Gp)(i),prefixCls:t,formatter:e=>`${e}%`,className:o,onChange:e=>{let t=i.toHsb();t.a=(e||0)/100;let a=(0,ek.Z6)(t);r||s(a),null==n||n(a)}})};var eP=r(8124);let eD=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,ez=e=>eD.test(`#${e}`),eR=e=>{let{prefixCls:t,value:r,onChange:n}=e,o=`${t}-hex-input`,[i,s]=(0,a.useState)(()=>r?(0,X.Ol)(r.toHexString()):void 0);return(0,a.useEffect)(()=>{r&&s((0,X.Ol)(r.toHexString()))},[r]),l().createElement(eP.A,{className:o,value:i,prefix:"#",onChange:e=>{let t=e.target.value;s((0,X.Ol)(t)),ez((0,X.Ol)(t,!0))&&(null==n||n((0,ek.Z6)(t)))},size:"small"})},eH=e=>{let{prefixCls:t,value:r,onChange:n}=e,o=`${t}-hsb-input`,[i,s]=(0,a.useState)((0,ek.Z6)(r||"#000"));(0,a.useEffect)(()=>{r&&s(r)},[r]);let c=(e,t)=>{let a=i.toHsb();a[t]="h"===t?e:(e||0)/100;let l=(0,ek.Z6)(a);r||s(l),null==n||n(l)};return l().createElement("div",{className:o},l().createElement(eN,{max:360,min:0,value:Number(i.toHsb().h),prefixCls:t,className:o,formatter:e=>(0,ek.W)(e||0).toString(),onChange:e=>c(Number(e),"h")}),l().createElement(eN,{max:100,min:0,value:100*Number(i.toHsb().s),prefixCls:t,className:o,formatter:e=>`${(0,ek.W)(e||0)}%`,onChange:e=>c(Number(e),"s")}),l().createElement(eN,{max:100,min:0,value:100*Number(i.toHsb().b),prefixCls:t,className:o,formatter:e=>`${(0,ek.W)(e||0)}%`,onChange:e=>c(Number(e),"b")}))},eI=e=>{let{prefixCls:t,value:r,onChange:n}=e,o=`${t}-rgb-input`,[i,s]=(0,a.useState)((0,ek.Z6)(r||"#000"));(0,a.useEffect)(()=>{r&&s(r)},[r]);let c=(e,t)=>{let a=i.toRgb();a[t]=e||0;let l=(0,ek.Z6)(a);r||s(l),null==n||n(l)};return l().createElement("div",{className:o},l().createElement(eN,{max:255,min:0,value:Number(i.toRgb().r),prefixCls:t,className:o,onChange:e=>c(Number(e),"r")}),l().createElement(eN,{max:255,min:0,value:Number(i.toRgb().g),prefixCls:t,className:o,onChange:e=>c(Number(e),"g")}),l().createElement(eN,{max:255,min:0,value:Number(i.toRgb().b),prefixCls:t,className:o,onChange:e=>c(Number(e),"b")}))},eB=["hex","hsb","rgb"].map(e=>({value:e,label:e.toUpperCase()})),eF=e=>{let{prefixCls:t,format:r,value:n,disabledAlpha:o,onFormatChange:i,onChange:s,disabledFormat:c}=e,[d,u]=(0,H.A)("hex",{value:r,onChange:i}),m=`${t}-input`,g=(0,a.useMemo)(()=>{let e={value:n,prefixCls:t,onChange:s};switch(d){case"hsb":return l().createElement(eH,Object.assign({},e));case"rgb":return l().createElement(eI,Object.assign({},e));default:return l().createElement(eR,Object.assign({},e))}},[d,t,n,s]);return l().createElement("div",{className:`${m}-container`},!c&&l().createElement(eS.A,{value:d,variant:"borderless",getPopupContainer:e=>e,popupMatchSelectWidth:68,placement:"bottomRight",onChange:e=>{u(e)},className:`${t}-format-select`,size:"small",options:eB}),l().createElement("div",{className:m},g),!o&&l().createElement(eM,{prefixCls:t,value:n,onChange:s}))};var eq=r(25392),eL=r(56114),eT=r(67010),eW=r(55740);function eV(e,t,r,n){var a=(t-r)/(n-r),l={};switch(e){case"rtl":l.right="".concat(100*a,"%"),l.transform="translateX(50%)";break;case"btt":l.bottom="".concat(100*a,"%"),l.transform="translateY(50%)";break;case"ttb":l.top="".concat(100*a,"%"),l.transform="translateY(-50%)";break;default:l.left="".concat(100*a,"%"),l.transform="translateX(-50%)"}return l}function eG(e,t){return Array.isArray(e)?e[t]:e}var e_=r(73924),eX=a.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),eK=a.createContext({}),eU=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],eY=a.forwardRef(function(e,t){var r,n=e.prefixCls,l=e.value,o=e.valueIndex,i=e.onStartMove,s=e.onDelete,c=e.style,d=e.render,u=e.dragging,m=e.draggingDelete,g=e.onOffsetChange,p=e.onChangeComplete,f=e.onFocus,h=e.onMouseEnter,b=(0,Q.A)(e,eU),v=a.useContext(eX),x=v.min,y=v.max,C=v.direction,j=v.disabled,$=v.keyboard,w=v.range,k=v.tabIndex,E=v.ariaLabelForHandle,S=v.ariaLabelledByForHandle,O=v.ariaRequired,N=v.ariaValueTextFormatterForHandle,M=v.styles,P=v.classNames,D="".concat(n,"-handle"),z=function(e){j||i(e,o)},H=eV(C,l,x,y),I={};null!==o&&(I={tabIndex:j?null:eG(k,o),role:"slider","aria-valuemin":x,"aria-valuemax":y,"aria-valuenow":l,"aria-disabled":j,"aria-label":eG(E,o),"aria-labelledby":eG(S,o),"aria-required":eG(O,o),"aria-valuetext":null===(r=eG(N,o))||void 0===r?void 0:r(l),"aria-orientation":"ltr"===C||"rtl"===C?"horizontal":"vertical",onMouseDown:z,onTouchStart:z,onFocus:function(e){null==f||f(e,o)},onMouseEnter:function(e){h(e,o)},onKeyDown:function(e){if(!j&&$){var t=null;switch(e.which||e.keyCode){case e_.A.LEFT:t="ltr"===C||"btt"===C?-1:1;break;case e_.A.RIGHT:t="ltr"===C||"btt"===C?1:-1;break;case e_.A.UP:t="ttb"!==C?1:-1;break;case e_.A.DOWN:t="ttb"!==C?-1:1;break;case e_.A.HOME:t="min";break;case e_.A.END:t="max";break;case e_.A.PAGE_UP:t=2;break;case e_.A.PAGE_DOWN:t=-2;break;case e_.A.BACKSPACE:case e_.A.DELETE:s(o)}null!==t&&(e.preventDefault(),g(t,o))}},onKeyUp:function(e){switch(e.which||e.keyCode){case e_.A.LEFT:case e_.A.RIGHT:case e_.A.UP:case e_.A.DOWN:case e_.A.HOME:case e_.A.END:case e_.A.PAGE_UP:case e_.A.PAGE_DOWN:null==p||p()}}});var B=a.createElement("div",(0,A.A)({ref:t,className:R()(D,(0,ee.A)((0,ee.A)((0,ee.A)({},"".concat(D,"-").concat(o+1),null!==o&&w),"".concat(D,"-dragging"),u),"".concat(D,"-dragging-delete"),m),P.handle),style:(0,et.A)((0,et.A)((0,et.A)({},H),c),M.handle)},I,b));return d&&(B=d(B,{index:o,prefixCls:n,value:l,dragging:u,draggingDelete:m})),B}),eZ=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],eJ=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.style,l=e.onStartMove,o=e.onOffsetChange,i=e.values,s=e.handleRender,c=e.activeHandleRender,d=e.draggingIndex,u=e.draggingDelete,m=e.onFocus,g=(0,Q.A)(e,eZ),p=a.useRef({}),f=a.useState(!1),h=(0,J.A)(f,2),b=h[0],v=h[1],x=a.useState(-1),y=(0,J.A)(x,2),C=y[0],j=y[1],$=function(e){j(e),v(!0)};a.useImperativeHandle(t,function(){return{focus:function(e){var t;null===(t=p.current[e])||void 0===t||t.focus()},hideHelp:function(){(0,eW.flushSync)(function(){v(!1)})}}});var w=(0,et.A)({prefixCls:r,onStartMove:l,onOffsetChange:o,render:s,onFocus:function(e,t){$(t),null==m||m(e)},onMouseEnter:function(e,t){$(t)}},g);return a.createElement(a.Fragment,null,i.map(function(e,t){var r=d===t;return a.createElement(eY,(0,A.A)({ref:function(e){e?p.current[t]=e:delete p.current[t]},dragging:r,draggingDelete:r&&u,style:eG(n,t),key:t,value:e,valueIndex:t},w))}),c&&b&&a.createElement(eY,(0,A.A)({key:"a11y"},w,{value:i[C],valueIndex:null,dragging:-1!==d,draggingDelete:u,render:c,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))});let eQ=function(e){var t=e.prefixCls,r=e.style,n=e.children,l=e.value,o=e.onClick,i=a.useContext(eX),s=i.min,c=i.max,d=i.direction,u=i.includedStart,m=i.includedEnd,g=i.included,p="".concat(t,"-text"),f=eV(d,l,s,c);return a.createElement("span",{className:R()(p,(0,ee.A)({},"".concat(p,"-active"),g&&u<=l&&l<=m)),style:(0,et.A)((0,et.A)({},f),r),onMouseDown:function(e){e.stopPropagation()},onClick:function(){o(l)}},n)},e0=function(e){var t=e.prefixCls,r=e.marks,n=e.onClick,l="".concat(t,"-mark");return r.length?a.createElement("div",{className:l},r.map(function(e){var t=e.value,r=e.style,o=e.label;return a.createElement(eQ,{key:t,prefixCls:l,style:r,value:t,onClick:n},o)})):null},e1=function(e){var t=e.prefixCls,r=e.value,n=e.style,l=e.activeStyle,o=a.useContext(eX),i=o.min,s=o.max,c=o.direction,d=o.included,u=o.includedStart,m=o.includedEnd,g="".concat(t,"-dot"),p=d&&u<=r&&r<=m,f=(0,et.A)((0,et.A)({},eV(c,r,i,s)),"function"==typeof n?n(r):n);return p&&(f=(0,et.A)((0,et.A)({},f),"function"==typeof l?l(r):l)),a.createElement("span",{className:R()(g,(0,ee.A)({},"".concat(g,"-active"),p)),style:f})},e2=function(e){var t=e.prefixCls,r=e.marks,n=e.dots,l=e.style,o=e.activeStyle,i=a.useContext(eX),s=i.min,c=i.max,d=i.step,u=a.useMemo(function(){var e=new Set;if(r.forEach(function(t){e.add(t.value)}),n&&null!==d)for(var t=s;t<=c;)e.add(t),t+=d;return Array.from(e)},[s,c,d,n,r]);return a.createElement("div",{className:"".concat(t,"-step")},u.map(function(e){return a.createElement(e1,{prefixCls:t,key:e,value:e,style:l,activeStyle:o})}))},e4=function(e){var t=e.prefixCls,r=e.style,n=e.start,l=e.end,o=e.index,i=e.onStartMove,s=e.replaceCls,c=a.useContext(eX),d=c.direction,u=c.min,m=c.max,g=c.disabled,p=c.range,f=c.classNames,h="".concat(t,"-track"),b=(n-u)/(m-u),v=(l-u)/(m-u),x=function(e){!g&&i&&i(e,-1)},y={};switch(d){case"rtl":y.right="".concat(100*b,"%"),y.width="".concat(100*v-100*b,"%");break;case"btt":y.bottom="".concat(100*b,"%"),y.height="".concat(100*v-100*b,"%");break;case"ttb":y.top="".concat(100*b,"%"),y.height="".concat(100*v-100*b,"%");break;default:y.left="".concat(100*b,"%"),y.width="".concat(100*v-100*b,"%")}var C=s||R()(h,(0,ee.A)((0,ee.A)({},"".concat(h,"-").concat(o+1),null!==o&&p),"".concat(t,"-track-draggable"),i),f.track);return a.createElement("div",{className:C,style:(0,et.A)((0,et.A)({},y),r),onMouseDown:x,onTouchStart:x})},e3=function(e){var t=e.prefixCls,r=e.style,n=e.values,l=e.startPoint,o=e.onStartMove,i=a.useContext(eX),s=i.included,c=i.range,d=i.min,u=i.styles,m=i.classNames,g=a.useMemo(function(){if(!c){if(0===n.length)return[];var e=null!=l?l:d,t=n[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var r=[],a=0;a<n.length-1;a+=1)r.push({start:n[a],end:n[a+1]});return r},[n,c,l,d]);if(!s)return null;var p=null!=g&&g.length&&(m.tracks||u.tracks)?a.createElement(e4,{index:null,prefixCls:t,start:g[0].start,end:g[g.length-1].end,replaceCls:R()(m.tracks,"".concat(t,"-tracks")),style:u.tracks}):null;return a.createElement(a.Fragment,null,p,g.map(function(e,n){var l=e.start,i=e.end;return a.createElement(e4,{index:n,prefixCls:t,style:(0,et.A)((0,et.A)({},eG(r,n)),u.track),start:l,end:i,key:n,onStartMove:o})}))};function e5(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}let e8=function(e,t,r,n,l,o,i,s,c,d,u){var m=a.useState(null),g=(0,J.A)(m,2),p=g[0],f=g[1],h=a.useState(-1),b=(0,J.A)(h,2),v=b[0],x=b[1],y=a.useState(!1),C=(0,J.A)(y,2),A=C[0],j=C[1],$=a.useState(r),w=(0,J.A)($,2),k=w[0],E=w[1],S=a.useState(r),O=(0,J.A)(S,2),N=O[0],M=O[1],P=a.useRef(null),D=a.useRef(null),z=a.useRef(null),R=a.useContext(eK),H=R.onDragStart,I=R.onDragChange;(0,Z.A)(function(){-1===v&&E(r)},[r,v]),a.useEffect(function(){return function(){document.removeEventListener("mousemove",P.current),document.removeEventListener("mouseup",D.current),z.current&&(z.current.removeEventListener("touchmove",P.current),z.current.removeEventListener("touchend",D.current))}},[]);var B=function(e,t,r){void 0!==t&&f(t),E(e);var n=e;r&&(n=e.filter(function(e,t){return t!==v})),i(n),I&&I({rawValues:e,deleteIndex:r?v:-1,draggingIndex:v,draggingValue:t})},F=(0,eq.A)(function(e,t,r){if(-1===e){var a=N[0],i=N[N.length-1],s=t*(l-n);s=Math.min(s=Math.max(s,n-a),l-i),s=o(a+s)-a,B(N.map(function(e){return e+s}))}else{var d=(0,U.A)(k);d[e]=N[e];var u=c(d,(l-n)*t,e,"dist");B(u.values,u.value,r)}});return[v,p,A,a.useMemo(function(){var e=(0,U.A)(r).sort(function(e,t){return e-t}),t=(0,U.A)(k).sort(function(e,t){return e-t}),n={};t.forEach(function(e){n[e]=(n[e]||0)+1}),e.forEach(function(e){n[e]=(n[e]||0)-1});var a=d?1:0;return Object.values(n).reduce(function(e,t){return e+Math.abs(t)},0)<=a?k:r},[r,k,d]),function(n,a,l){n.stopPropagation();var o=l||r,i=o[a];x(a),f(i),M(o),E(o),j(!1);var c=e5(n),m=c.pageX,g=c.pageY,p=!1;H&&H({rawValues:o,draggingIndex:a,draggingValue:i});var h=function(r){r.preventDefault();var n,l,o=e5(r),i=o.pageX,s=o.pageY,c=i-m,f=s-g,h=e.current.getBoundingClientRect(),b=h.width,v=h.height;switch(t){case"btt":n=-f/v,l=c;break;case"ttb":n=f/v,l=c;break;case"rtl":n=-c/b,l=f;break;default:n=c/b,l=f}j(p=!!d&&Math.abs(l)>130&&u<k.length),F(a,n,p)},b=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",h),z.current&&(z.current.removeEventListener("touchmove",P.current),z.current.removeEventListener("touchend",D.current)),P.current=null,D.current=null,z.current=null,s(p),x(-1),j(!1)};document.addEventListener("mouseup",b),document.addEventListener("mousemove",h),n.currentTarget.addEventListener("touchend",b),n.currentTarget.addEventListener("touchmove",h),P.current=h,D.current=b,z.current=n.currentTarget}]};var e6=a.forwardRef(function(e,t){var r,n,l,o,i,s,c,d=e.prefixCls,u=void 0===d?"rc-slider":d,m=e.className,g=e.style,p=e.classNames,f=e.styles,h=e.id,b=e.disabled,v=void 0!==b&&b,x=e.keyboard,y=void 0===x||x,C=e.autoFocus,A=e.onFocus,j=e.onBlur,$=e.min,w=void 0===$?0:$,k=e.max,E=void 0===k?100:k,S=e.step,O=void 0===S?1:S,N=e.value,M=e.defaultValue,P=e.range,D=e.count,z=e.onChange,I=e.onBeforeChange,B=e.onAfterChange,F=e.onChangeComplete,q=e.allowCross,L=e.pushable,T=void 0!==L&&L,W=e.reverse,V=e.vertical,G=e.included,_=void 0===G||G,X=e.startPoint,K=e.trackStyle,Y=e.handleStyle,Z=e.railStyle,Q=e.dotStyle,en=e.activeDotStyle,ea=e.marks,el=e.dots,eo=e.handleRender,ei=e.activeHandleRender,es=e.track,ec=e.tabIndex,ed=void 0===ec?0:ec,eu=e.ariaLabelForHandle,em=e.ariaLabelledByForHandle,eg=e.ariaRequired,ep=e.ariaValueTextFormatterForHandle,ef=a.useRef(null),eh=a.useRef(null),eb=a.useMemo(function(){return V?W?"ttb":"btt":W?"rtl":"ltr"},[W,V]),ev=(0,a.useMemo)(function(){if(!0===P||!P)return[!!P,!1,!1,0];var e=P.editable,t=P.draggableTrack;return[!0,e,!e&&t,P.minCount||0,P.maxCount]},[P]),ex=(0,J.A)(ev,5),ey=ex[0],eC=ex[1],eA=ex[2],ej=ex[3],e$=ex[4],ew=a.useMemo(function(){return isFinite(w)?w:0},[w]),ek=a.useMemo(function(){return isFinite(E)?E:100},[E]),eE=a.useMemo(function(){return null!==O&&O<=0?1:O},[O]),eS=a.useMemo(function(){return"boolean"==typeof T?!!T&&eE:T>=0&&T},[T,eE]),eO=a.useMemo(function(){return Object.keys(ea||{}).map(function(e){var t=ea[e],r={value:Number(e)};return t&&"object"===(0,er.A)(t)&&!a.isValidElement(t)&&("label"in t||"style"in t)?(r.style=t.style,r.label=t.label):r.label=t,r}).filter(function(e){var t=e.label;return t||"number"==typeof t}).sort(function(e,t){return e.value-t.value})},[ea]),eN=(r=void 0===q||q,n=a.useCallback(function(e){return Math.max(ew,Math.min(ek,e))},[ew,ek]),l=a.useCallback(function(e){if(null!==eE){var t=ew+Math.round((n(e)-ew)/eE)*eE,r=function(e){return(String(e).split(".")[1]||"").length},a=Math.max(r(eE),r(ek),r(ew)),l=Number(t.toFixed(a));return ew<=l&&l<=ek?l:null}return null},[eE,ew,ek,n]),o=a.useCallback(function(e){var t=n(e),r=eO.map(function(e){return e.value});null!==eE&&r.push(l(e)),r.push(ew,ek);var a=r[0],o=ek-ew;return r.forEach(function(e){var r=Math.abs(t-e);r<=o&&(a=e,o=r)}),a},[ew,ek,eO,eE,n,l]),i=function e(t,r,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof r){var o,i=t[n],s=i+r,c=[];eO.forEach(function(e){c.push(e.value)}),c.push(ew,ek),c.push(l(i));var d=r>0?1:-1;"unit"===a?c.push(l(i+d*eE)):c.push(l(s)),c=c.filter(function(e){return null!==e}).filter(function(e){return r<0?e<=i:e>=i}),"unit"===a&&(c=c.filter(function(e){return e!==i}));var u="unit"===a?i:s,m=Math.abs((o=c[0])-u);if(c.forEach(function(e){var t=Math.abs(e-u);t<m&&(o=e,m=t)}),void 0===o)return r<0?ew:ek;if("dist"===a)return o;if(Math.abs(r)>1){var g=(0,U.A)(t);return g[n]=o,e(g,r-d,n,a)}return o}return"min"===r?ew:"max"===r?ek:void 0},s=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[r],l=i(e,t,r,n);return{value:l,changed:l!==a}},c=function(e){return null===eS&&0===e||"number"==typeof eS&&e<eS},[o,function(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",l=e.map(o),d=l[n],u=i(l,t,n,a);if(l[n]=u,!1===r){var m=eS||0;n>0&&l[n-1]!==d&&(l[n]=Math.max(l[n],l[n-1]+m)),n<l.length-1&&l[n+1]!==d&&(l[n]=Math.min(l[n],l[n+1]-m))}else if("number"==typeof eS||null===eS){for(var g=n+1;g<l.length;g+=1)for(var p=!0;c(l[g]-l[g-1])&&p;){var f=s(l,1,g);l[g]=f.value,p=f.changed}for(var h=n;h>0;h-=1)for(var b=!0;c(l[h]-l[h-1])&&b;){var v=s(l,-1,h-1);l[h-1]=v.value,b=v.changed}for(var x=l.length-1;x>0;x-=1)for(var y=!0;c(l[x]-l[x-1])&&y;){var C=s(l,-1,x-1);l[x-1]=C.value,y=C.changed}for(var A=0;A<l.length-1;A+=1)for(var j=!0;c(l[A+1]-l[A])&&j;){var $=s(l,1,A+1);l[A+1]=$.value,j=$.changed}}return{value:l[n],values:l}}]),eM=(0,J.A)(eN,2),eP=eM[0],eD=eM[1],ez=(0,H.A)(M,{value:N}),eR=(0,J.A)(ez,2),eH=eR[0],eI=eR[1],eB=a.useMemo(function(){var e=null==eH?[]:Array.isArray(eH)?eH:[eH],t=(0,J.A)(e,1)[0],r=void 0===t?ew:t,n=null===eH?[]:[r];if(ey){if(n=(0,U.A)(e),D||void 0===eH){var a,l=D>=0?D+1:2;for(n=n.slice(0,l);n.length<l;)n.push(null!==(a=n[n.length-1])&&void 0!==a?a:ew)}n.sort(function(e,t){return e-t})}return n.forEach(function(e,t){n[t]=eP(e)}),n},[eH,ey,ew,D,eP]),eF=function(e){return ey?e:e[0]},eW=(0,eq.A)(function(e){var t=(0,U.A)(e).sort(function(e,t){return e-t});z&&!(0,eL.A)(t,eB,!0)&&z(eF(t)),eI(t)}),eV=(0,eq.A)(function(e){e&&ef.current.hideHelp();var t=eF(eB);null==B||B(t),(0,eT.Ay)(!B,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==F||F(t)}),eG=e8(eh,eb,eB,ew,ek,eP,eW,eV,eD,eC,ej),e_=(0,J.A)(eG,5),eK=e_[0],eU=e_[1],eY=e_[2],eZ=e_[3],eQ=e_[4],e1=function(e,t){if(!v){var r,n,a=(0,U.A)(eB),l=0,o=0,i=ek-ew;eB.forEach(function(t,r){var n=Math.abs(e-t);n<=i&&(i=n,l=r),t<e&&(o=r)});var s=l;eC&&0!==i&&(!e$||eB.length<e$)?(a.splice(o+1,0,e),s=o+1):a[l]=e,ey&&!eB.length&&void 0===D&&a.push(e);var c=eF(a);null==I||I(c),eW(a),t?(null===(r=document.activeElement)||void 0===r||null===(n=r.blur)||void 0===n||n.call(r),ef.current.focus(s),eQ(t,s,a)):(null==B||B(c),(0,eT.Ay)(!B,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==F||F(c))}},e4=a.useState(null),e5=(0,J.A)(e4,2),e6=e5[0],e7=e5[1];a.useEffect(function(){if(null!==e6){var e=eB.indexOf(e6);e>=0&&ef.current.focus(e)}e7(null)},[e6]);var e9=a.useMemo(function(){return(!eA||null!==eE)&&eA},[eA,eE]),te=(0,eq.A)(function(e,t){eQ(e,t),null==I||I(eF(eB))}),tt=-1!==eK;a.useEffect(function(){if(!tt){var e=eB.lastIndexOf(eU);ef.current.focus(e)}},[tt]);var tr=a.useMemo(function(){return(0,U.A)(eZ).sort(function(e,t){return e-t})},[eZ]),tn=a.useMemo(function(){return ey?[tr[0],tr[tr.length-1]]:[ew,tr[0]]},[tr,ey,ew]),ta=(0,J.A)(tn,2),tl=ta[0],to=ta[1];a.useImperativeHandle(t,function(){return{focus:function(){ef.current.focus(0)},blur:function(){var e,t=document.activeElement;null!==(e=eh.current)&&void 0!==e&&e.contains(t)&&(null==t||t.blur())}}}),a.useEffect(function(){C&&ef.current.focus(0)},[]);var ti=a.useMemo(function(){return{min:ew,max:ek,direction:eb,disabled:v,keyboard:y,step:eE,included:_,includedStart:tl,includedEnd:to,range:ey,tabIndex:ed,ariaLabelForHandle:eu,ariaLabelledByForHandle:em,ariaRequired:eg,ariaValueTextFormatterForHandle:ep,styles:f||{},classNames:p||{}}},[ew,ek,eb,v,y,eE,_,tl,to,ey,ed,eu,em,eg,ep,f,p]);return a.createElement(eX.Provider,{value:ti},a.createElement("div",{ref:eh,className:R()(u,m,(0,ee.A)((0,ee.A)((0,ee.A)((0,ee.A)({},"".concat(u,"-disabled"),v),"".concat(u,"-vertical"),V),"".concat(u,"-horizontal"),!V),"".concat(u,"-with-marks"),eO.length)),style:g,onMouseDown:function(e){e.preventDefault();var t,r=eh.current.getBoundingClientRect(),n=r.width,a=r.height,l=r.left,o=r.top,i=r.bottom,s=r.right,c=e.clientX,d=e.clientY;switch(eb){case"btt":t=(i-d)/a;break;case"ttb":t=(d-o)/a;break;case"rtl":t=(s-c)/n;break;default:t=(c-l)/n}e1(eP(ew+t*(ek-ew)),e)},id:h},a.createElement("div",{className:R()("".concat(u,"-rail"),null==p?void 0:p.rail),style:(0,et.A)((0,et.A)({},Z),null==f?void 0:f.rail)}),!1!==es&&a.createElement(e3,{prefixCls:u,style:K,values:eB,startPoint:X,onStartMove:e9?te:void 0}),a.createElement(e2,{prefixCls:u,marks:eO,dots:el,style:Q,activeStyle:en}),a.createElement(eJ,{ref:ef,prefixCls:u,style:Y,values:eZ,draggingIndex:eK,draggingDelete:eY,onStartMove:te,onOffsetChange:function(e,t){if(!v){var r=eD(eB,e,t);null==I||I(eF(eB)),eW(r.values),e7(r.value)}},onFocus:A,onBlur:j,handleRender:eo,activeHandleRender:ei,onChangeComplete:eV,onDelete:eC?function(e){if(!v&&eC&&!(eB.length<=ej)){var t=(0,U.A)(eB);t.splice(e,1),null==I||I(eF(t)),eW(t);var r=Math.max(0,e-1);ef.current.hideHelp(),ef.current.focus(r)}}:void 0}),a.createElement(e0,{prefixCls:u,marks:eO,onClick:e1})))}),e7=r(64267);let e9=(0,a.createContext)({}),te=a.forwardRef((e,t)=>{let{open:r,draggingDelete:n}=e,l=(0,a.useRef)(null),o=r&&!n,i=(0,a.useRef)(null);function s(){e7.A.cancel(i.current),i.current=null}return a.useEffect(()=>(o?i.current=(0,e7.A)(()=>{var e;null===(e=l.current)||void 0===e||e.forceAlign(),i.current=null}):s(),s),[o,e.title]),a.createElement(y.A,Object.assign({ref:(0,ea.K4)(l,t)},e,{open:o}))});var tt=r(43891);let tr=e=>{let{componentCls:t,antCls:r,controlSize:n,dotSize:a,marginFull:l,marginPart:o,colorFillContentHover:i,handleColorDisabled:s,calc:c,handleSize:d,handleSizeHover:u,handleActiveColor:m,handleActiveOutlineColor:g,handleLineWidth:p,handleLineWidthHover:f,motionDurationMid:h}=e;return{[t]:Object.assign(Object.assign({},(0,ep.dF)(e)),{position:"relative",height:n,margin:`${(0,eg.zA)(o)} ${(0,eg.zA)(l)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${(0,eg.zA)(l)} ${(0,eg.zA)(o)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${h}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${h}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:i},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${(0,eg.zA)(p)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:d,height:d,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:c(p).mul(-1).equal(),insetBlockStart:c(p).mul(-1).equal(),width:c(d).add(c(p).mul(2)).equal(),height:c(d).add(c(p).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:d,height:d,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${(0,eg.zA)(p)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${h},
            inset-block-start ${h},
            width ${h},
            height ${h},
            box-shadow ${h},
            outline ${h}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:c(u).sub(d).div(2).add(f).mul(-1).equal(),insetBlockStart:c(u).sub(d).div(2).add(f).mul(-1).equal(),width:c(u).add(c(f).mul(2)).equal(),height:c(u).add(c(f).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${(0,eg.zA)(f)} ${m}`,outline:`6px solid ${g}`,width:u,height:u,insetInlineStart:e.calc(d).sub(u).div(2).equal(),insetBlockStart:e.calc(d).sub(u).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:`${(0,eg.zA)(p)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`
          ${t}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:d,height:d,boxShadow:`0 0 0 ${(0,eg.zA)(p)} ${s}`,insetInlineStart:0,insetBlockStart:0},[`
          ${t}-mark-text,
          ${t}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${r}-tooltip-inner`]:{minWidth:"unset"}})}},tn=(e,t)=>{let{componentCls:r,railSize:n,handleSize:a,dotSize:l,marginFull:o,calc:i}=e,s=t?"width":"height",c=t?"height":"width",d=t?"insetBlockStart":"insetInlineStart",u=t?"top":"insetInlineStart",m=i(n).mul(3).sub(a).div(2).equal(),g=i(a).sub(n).div(2).equal(),p=t?{borderWidth:`${(0,eg.zA)(g)} 0`,transform:`translateY(${(0,eg.zA)(i(g).mul(-1).equal())})`}:{borderWidth:`0 ${(0,eg.zA)(g)}`,transform:`translateX(${(0,eg.zA)(e.calc(g).mul(-1).equal())})`};return{[t?"paddingBlock":"paddingInline"]:n,[c]:i(n).mul(3).equal(),[`${r}-rail`]:{[s]:"100%",[c]:n},[`${r}-track,${r}-tracks`]:{[c]:n},[`${r}-track-draggable`]:Object.assign({},p),[`${r}-handle`]:{[d]:m},[`${r}-mark`]:{insetInlineStart:0,top:0,[u]:i(n).mul(3).add(t?0:o).equal(),[s]:"100%"},[`${r}-step`]:{insetInlineStart:0,top:0,[u]:n,[s]:"100%",[c]:n},[`${r}-dot`]:{position:"absolute",[d]:i(n).sub(l).div(2).equal()}}},ta=e=>{let{componentCls:t,marginPartWithMark:r}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},tn(e,!0)),{[`&${t}-with-marks`]:{marginBottom:r}})}},tl=e=>{let{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},tn(e,!1)),{height:"100%"})}},to=(0,ef.OF)("Slider",e=>{let t=(0,eh.oX)(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[tr(t),ta(t),tl(t)]},e=>{let t=e.controlHeightLG/4,r=e.controlHeightSM/2,n=e.lineWidth+1,a=e.lineWidth+1.5,l=e.colorPrimary,o=new tt.Y(l).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:r,dotSize:8,handleLineWidth:n,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:l,handleActiveOutlineColor:o,handleColorDisabled:new tt.Y(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}});function ti(){let[e,t]=a.useState(!1),r=a.useRef(null),n=()=>{e7.A.cancel(r.current)};return a.useEffect(()=>n,[]),[e,e=>{n(),e?t(e):r.current=(0,e7.A)(()=>{t(e)})}]}var ts=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let tc=l().forwardRef((e,t)=>{let{prefixCls:r,range:n,className:a,rootClassName:o,style:i,disabled:s,tooltipPrefixCls:c,tipFormatter:d,tooltipVisible:u,getTooltipPopupContainer:m,tooltipPlacement:g,tooltip:p={},onChangeComplete:f,classNames:h,styles:b}=e,v=ts(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:x}=e,{getPrefixCls:y,direction:C,className:A,style:j,classNames:$,styles:w,getPopupContainer:k}=(0,q.TP)("slider"),E=l().useContext(L.A),{handleRender:S,direction:O}=l().useContext(e9),N="rtl"===(O||C),[M,P]=ti(),[D,z]=ti(),H=Object.assign({},p),{open:I,placement:B,getPopupContainer:F,prefixCls:T,formatter:W}=H,V=null!=I?I:u,G=(M||D)&&!1!==V,_=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"==typeof e?e.toString():""}(W,d),[X,K]=ti(),U=(e,t)=>e||(t?N?"left":"right":"top"),Y=y("slider",r),[Z,J,Q]=to(Y),ee=R()(a,A,$.root,null==h?void 0:h.root,o,{[`${Y}-rtl`]:N,[`${Y}-lock`]:X},J,Q);N&&!v.vertical&&(v.reverse=!v.reverse),l().useEffect(()=>{let e=()=>{(0,e7.A)(()=>{z(!1)},1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}},[]);let et=n&&!V,er=S||((e,t)=>{let{index:r}=t,n=e.props;function a(e,t,r){var a,l;r&&(null===(a=v[e])||void 0===a||a.call(v,t)),null===(l=n[e])||void 0===l||l.call(n,t)}let o=Object.assign(Object.assign({},n),{onMouseEnter:e=>{P(!0),a("onMouseEnter",e)},onMouseLeave:e=>{P(!1),a("onMouseLeave",e)},onMouseDown:e=>{z(!0),K(!0),a("onMouseDown",e)},onFocus:e=>{var t;z(!0),null===(t=v.onFocus)||void 0===t||t.call(v,e),a("onFocus",e,!0)},onBlur:e=>{var t;z(!1),null===(t=v.onBlur)||void 0===t||t.call(v,e),a("onBlur",e,!0)}}),i=l().cloneElement(e,o),s=(!!V||G)&&null!==_;return et?i:l().createElement(te,Object.assign({},H,{prefixCls:y("tooltip",null!=T?T:c),title:_?_(t.value):"",open:s,placement:U(null!=B?B:g,x),key:r,classNames:{root:`${Y}-tooltip`},getPopupContainer:F||m||k}),i)}),en=et?(e,t)=>{let r=l().cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return l().createElement(te,Object.assign({},H,{prefixCls:y("tooltip",null!=T?T:c),title:_?_(t.value):"",open:null!==_&&G,placement:U(null!=B?B:g,x),key:"tooltip",classNames:{root:`${Y}-tooltip`},getPopupContainer:F||m||k,draggingDelete:t.draggingDelete}),r)}:void 0,ea=Object.assign(Object.assign(Object.assign(Object.assign({},w.root),j),null==b?void 0:b.root),i),el=Object.assign(Object.assign({},w.tracks),null==b?void 0:b.tracks),eo=R()($.tracks,null==h?void 0:h.tracks);return Z(l().createElement(e6,Object.assign({},v,{classNames:Object.assign({handle:R()($.handle,null==h?void 0:h.handle),rail:R()($.rail,null==h?void 0:h.rail),track:R()($.track,null==h?void 0:h.track)},eo?{tracks:eo}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},w.handle),null==b?void 0:b.handle),rail:Object.assign(Object.assign({},w.rail),null==b?void 0:b.rail),track:Object.assign(Object.assign({},w.track),null==b?void 0:b.track)},Object.keys(el).length?{tracks:el}:{}),step:v.step,range:n,className:ee,style:ea,disabled:null!=s?s:E,ref:t,prefixCls:Y,handleRender:er,activeHandleRender:en,onChangeComplete:e=>{null==f||f(e),K(!1)}})))});var td=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let tu=e=>{let{prefixCls:t,colors:r,type:n,color:l,range:o=!1,className:i,activeIndex:s,onActive:c,onDragStart:d,onDragChange:u,onKeyDelete:m}=e,g=Object.assign(Object.assign({},td(e,["prefixCls","colors","type","color","range","className","activeIndex","onActive","onDragStart","onDragChange","onKeyDelete"])),{track:!1}),p=a.useMemo(()=>{let e=r.map(e=>`${e.color} ${e.percent}%`).join(", ");return`linear-gradient(90deg, ${e})`},[r]),f=a.useMemo(()=>l&&n?"alpha"===n?l.toRgbString():`hsl(${l.toHsb().h}, 100%, 50%)`:null,[l,n]),h=(0,eq.A)(d),b=(0,eq.A)(u),v=a.useMemo(()=>({onDragStart:h,onDragChange:b}),[]),x=(0,eq.A)((e,l)=>{let{onFocus:o,style:i,className:d,onKeyDown:u}=e.props,g=Object.assign({},i);return"gradient"===n&&(g.background=(0,ek.PU)(r,l.value)),a.cloneElement(e,{onFocus:e=>{null==c||c(l.index),null==o||o(e)},style:g,className:R()(d,{[`${t}-slider-handle-active`]:s===l.index}),onKeyDown:e=>{("Delete"===e.key||"Backspace"===e.key)&&m&&m(l.index),null==u||u(e)}})}),y=a.useMemo(()=>({direction:"ltr",handleRender:x}),[]);return a.createElement(e9.Provider,{value:y},a.createElement(eK.Provider,{value:v},a.createElement(tc,Object.assign({},g,{className:R()(i,`${t}-slider`),tooltip:{open:!1},range:{editable:o,minCount:2},styles:{rail:{background:p},handle:f?{background:f}:{}},classNames:{rail:`${t}-slider-rail`,handle:`${t}-slider-handle`}}))))};function tm(e){return(0,U.A)(e).sort((e,t)=>e.percent-t.percent)}let tg=a.memo(e=>{let{prefixCls:t,mode:r,onChange:n,onChangeComplete:l,onActive:o,activeIndex:i,onGradientDragging:s,colors:c}=e,d=a.useMemo(()=>c.map(e=>({percent:e.percent,color:e.color.toRgbString()})),[c]),u=a.useMemo(()=>d.map(e=>e.percent),[d]),m=a.useRef(d);return"gradient"!==r?null:a.createElement(tu,{min:0,max:100,prefixCls:t,className:`${t}-gradient-slider`,colors:d,color:null,value:u,range:!0,onChangeComplete:e=>{l(new X.kf(d)),i>=e.length&&o(e.length-1),s(!1)},disabled:!1,type:"gradient",activeIndex:i,onActive:o,onDragStart:e=>{let{rawValues:t,draggingIndex:r,draggingValue:a}=e;if(t.length>d.length){let e=(0,ek.PU)(d,a),t=(0,U.A)(d);t.splice(r,0,{percent:a,color:e}),m.current=t}else m.current=d;s(!0),n(new X.kf(tm(m.current)),!0)},onDragChange:e=>{let{deleteIndex:t,draggingIndex:r,draggingValue:a}=e,l=(0,U.A)(m.current);-1!==t?l.splice(t,1):(l[r]=Object.assign(Object.assign({},l[r]),{percent:a}),l=tm(l)),n(new X.kf(l),!0)},onKeyDelete:e=>{let t=(0,U.A)(d);t.splice(e,1);let r=new X.kf(t);n(r),l(r)}})});var tp=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let tf={slider:e=>{let{value:t,onChange:r,onChangeComplete:n}=e;return a.createElement(tu,Object.assign({},e,{value:[t],onChange:e=>r(e[0]),onChangeComplete:e=>n(e[0])}))}},th=()=>{let e=(0,a.useContext)(e$),{mode:t,onModeChange:r,modeOptions:n,prefixCls:o,allowClear:i,value:s,disabledAlpha:c,onChange:d,onClear:u,onChangeComplete:m,activeIndex:g,gradientDragging:p}=e,f=tp(e,["mode","onModeChange","modeOptions","prefixCls","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete","activeIndex","gradientDragging"]),h=l().useMemo(()=>s.cleared?[{percent:0,color:new X.kf("")},{percent:100,color:new X.kf("")}]:s.getColors(),[s]),b=!s.isGradient(),[v,x]=l().useState(s);(0,Z.A)(()=>{var e;b||x(null===(e=h[g])||void 0===e?void 0:e.color)},[p,g]);let y=l().useMemo(()=>{var e;return b?s:p?v:null===(e=h[g])||void 0===e?void 0:e.color},[s,g,b,v,p]),[C,A]=l().useState(y),[j,$]=l().useState(0),w=(null==C?void 0:C.equals(y))?y:C;(0,Z.A)(()=>{A(y)},[j,null==y?void 0:y.toHexString()]);let k=(e,r)=>{let n=(0,ek.Z6)(e);if(s.cleared){let e=n.toRgb();if(e.r||e.g||e.b||!r)n=(0,ek.E)(n);else{let{type:e,value:t=0}=r;n=new X.kf({h:"hue"===e?t:0,s:1,b:1,a:"alpha"===e?t/100:1})}}if("single"===t)return n;let a=(0,U.A)(h);return a[g]=Object.assign(Object.assign({},a[g]),{color:n}),new X.kf(a)},E=(e,t,r)=>{let n=k(e,r);A(n.isGradient()?n.getColors()[g].color:n),d(n,t)},S=(e,t)=>{m(k(e,t)),$(e=>e+1)},O=null,N=n.length>1;return(i||N)&&(O=l().createElement("div",{className:`${o}-operation`},N&&l().createElement(ej,{size:"small",options:n,value:t,onChange:r}),l().createElement(eE,Object.assign({prefixCls:o,value:s,onChange:e=>{d(e),null==u||u()}},f)))),l().createElement(l().Fragment,null,O,l().createElement(tg,Object.assign({},e,{colors:h})),l().createElement(Y.Ay,{prefixCls:o,value:null==w?void 0:w.toHsb(),disabledAlpha:c,onChange:(e,t)=>{E(e,!0,t)},onChangeComplete:(e,t)=>{S(e,t)},components:tf}),l().createElement(eF,Object.assign({value:y,onChange:e=>{d(k(e))},prefixCls:o,disabledAlpha:c},f)))};var tb=r(52651);let tv=()=>{let{prefixCls:e,value:t,presets:r,onChange:n}=(0,a.useContext)(ew);return Array.isArray(r)?l().createElement(tb.A,{value:t,presets:r,prefixCls:e,onChange:n}):null},tx=e=>{let{prefixCls:t,presets:r,panelRender:n,value:a,onChange:o,onClear:i,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:m,onChangeComplete:g,activeIndex:p,onActive:f,format:h,onFormatChange:b,gradientDragging:v,onGradientDragging:x,disabledFormat:y}=e,C=`${t}-inner`,A=l().useMemo(()=>({prefixCls:t,value:a,onChange:o,onClear:i,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:m,onChangeComplete:g,activeIndex:p,onActive:f,format:h,onFormatChange:b,gradientDragging:v,onGradientDragging:x,disabledFormat:y}),[t,a,o,i,s,c,d,u,m,g,p,f,h,b,v,x,y]),j=l().useMemo(()=>({prefixCls:t,value:a,presets:r,onChange:o}),[t,a,r,o]),$=l().createElement("div",{className:`${C}-content`},l().createElement(th,null),Array.isArray(r)&&l().createElement(K.A,null),l().createElement(tv,null));return l().createElement(e$.Provider,{value:A},l().createElement(ew.Provider,{value:j},l().createElement("div",{className:C},"function"==typeof n?n($,{components:{Picker:th,Presets:tv}}):$)))};var ty=r(90365),tC=r(76155),tA=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let tj=(0,a.forwardRef)((e,t)=>{let{color:r,prefixCls:n,open:o,disabled:i,format:s,className:c,showText:d,activeIndex:u}=e,m=tA(e,["color","prefixCls","open","disabled","format","className","showText","activeIndex"]),g=`${n}-trigger`,p=`${g}-text`,f=`${p}-cell`,[h]=(0,tC.A)("ColorPicker"),b=l().useMemo(()=>{if(!d)return"";if("function"==typeof d)return d(r);if(r.cleared)return h.transparent;if(r.isGradient())return r.getColors().map((e,t)=>{let r=-1!==u&&u!==t;return l().createElement("span",{key:t,className:R()(f,r&&`${f}-inactive`)},e.color.toRgbString()," ",e.percent,"%")});let e=r.toHexString().toUpperCase(),t=(0,ek.Gp)(r);switch(s){case"rgb":return r.toRgbString();case"hsb":return r.toHsbString();default:return t<100?`${e.slice(0,7)},${t}%`:e}},[r,s,d,u]),v=(0,a.useMemo)(()=>r.cleared?l().createElement(eE,{prefixCls:n}):l().createElement(Y.ZC,{prefixCls:n,color:r.toCssString()}),[r,n]);return l().createElement("div",Object.assign({ref:t,className:R()(g,c,{[`${g}-active`]:o,[`${g}-disabled`]:i})},(0,ty.A)(m)),v,d&&l().createElement("div",{className:p},b))});var t$=r(22974);let tw=(e,t)=>({backgroundImage:`conic-gradient(${t} 25%, transparent 25% 50%, ${t} 50% 75%, transparent 75% 100%)`,backgroundSize:`${e} ${e}`}),tk=(e,t)=>{let{componentCls:r,borderRadiusSM:n,colorPickerInsetShadow:a,lineWidth:l,colorFillSecondary:o}=e;return{[`${r}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:n,width:t,height:t,boxShadow:a,flex:"none"},tw("50%",e.colorFillSecondary)),{[`${r}-color-block-inner`]:{width:"100%",height:"100%",boxShadow:`inset 0 0 0 ${(0,eg.zA)(l)} ${o}`,borderRadius:"inherit"}})}},tE=e=>{let{componentCls:t,antCls:r,fontSizeSM:n,lineHeightSM:a,colorPickerAlphaInputWidth:l,marginXXS:o,paddingXXS:i,controlHeightSM:s,marginXS:c,fontSizeIcon:d,paddingXS:u,colorTextPlaceholder:m,colorPickerInputNumberHandleWidth:g,lineWidth:p}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${r}-input-number`]:{fontSize:n,lineHeight:a,[`${r}-input-number-input`]:{paddingInlineStart:i,paddingInlineEnd:0},[`${r}-input-number-handler-wrap`]:{width:g}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${(0,eg.zA)(l)}`,marginInlineStart:o},[`${t}-format-select${r}-select`]:{marginInlineEnd:c,width:"auto","&-single":{[`${r}-select-selector`]:{padding:0,border:0},[`${r}-select-arrow`]:{insetInlineEnd:0},[`${r}-select-selection-item`]:{paddingInlineEnd:e.calc(d).add(o).equal(),fontSize:n,lineHeight:(0,eg.zA)(s)},[`${r}-select-item-option-content`]:{fontSize:n,lineHeight:a},[`${r}-select-dropdown`]:{[`${r}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:o,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:o,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${r}-input-affix-wrapper`]:{flex:1,padding:`0 ${(0,eg.zA)(u)}`,[`${r}-input`]:{fontSize:n,textTransform:"uppercase",lineHeight:(0,eg.zA)(e.calc(s).sub(e.calc(p).mul(2)).equal())},[`${r}-input-prefix`]:{color:m}}}}}},tS=e=>{let{componentCls:t,controlHeightLG:r,borderRadiusSM:n,colorPickerInsetShadow:a,marginSM:l,colorBgElevated:o,colorFillSecondary:i,lineWidthBold:s,colorPickerHandlerSize:c}=e;return{userSelect:"none",[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(r).mul(4).equal(),overflow:"hidden",borderRadius:n},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:a,inset:0},marginBottom:l},[`${t}-handler`]:{width:c,height:c,border:`${(0,eg.zA)(s)} solid ${o}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${a}, 0 0 0 1px ${i}`}}},tO=e=>{let{componentCls:t,antCls:r,colorTextQuaternary:n,paddingXXS:a,colorPickerPresetColorSize:l,fontSizeSM:o,colorText:i,lineHeightSM:s,lineWidth:c,borderRadius:d,colorFill:u,colorWhite:m,marginXXS:g,paddingXS:p,fontHeightSM:f}=e;return{[`${t}-presets`]:{[`${r}-collapse-item > ${r}-collapse-header`]:{padding:0,[`${r}-collapse-expand-icon`]:{height:f,color:n,paddingInlineEnd:a}},[`${r}-collapse`]:{display:"flex",flexDirection:"column",gap:g},[`${r}-collapse-item > ${r}-collapse-content > ${r}-collapse-content-box`]:{padding:`${(0,eg.zA)(p)} 0`},"&-label":{fontSize:o,color:i,lineHeight:s},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(g).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:l,height:l,"&::before":{content:'""',pointerEvents:"none",width:e.calc(l).add(e.calc(c).mul(4)).equal(),height:e.calc(l).add(e.calc(c).mul(4)).equal(),position:"absolute",top:e.calc(c).mul(-2).equal(),insetInlineStart:e.calc(c).mul(-2).equal(),borderRadius:d,border:`${(0,eg.zA)(c)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:u},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(l).div(13).mul(5).equal(),height:e.calc(l).div(13).mul(8).equal(),border:`${(0,eg.zA)(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:m,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:o,color:n}}}},tN=e=>{let{componentCls:t,colorPickerInsetShadow:r,colorBgElevated:n,colorFillSecondary:a,lineWidthBold:l,colorPickerHandlerSizeSM:o,colorPickerSliderHeight:i,marginSM:s,marginXS:c}=e,d=e.calc(o).sub(e.calc(l).mul(2).equal()).equal(),u=e.calc(o).add(e.calc(l).mul(2).equal()).equal(),m={"&:after":{transform:"scale(1)",boxShadow:`${r}, 0 0 0 1px ${e.colorPrimaryActive}`}};return{[`${t}-slider`]:[tw((0,eg.zA)(i),e.colorFillSecondary),{margin:0,padding:0,height:i,borderRadius:e.calc(i).div(2).equal(),"&-rail":{height:i,borderRadius:e.calc(i).div(2).equal(),boxShadow:r},[`& ${t}-slider-handle`]:{width:d,height:d,top:0,borderRadius:"100%","&:before":{display:"block",position:"absolute",background:"transparent",left:{_skip_check_:!0,value:"50%"},top:"50%",transform:"translate(-50%, -50%)",width:u,height:u,borderRadius:"100%"},"&:after":{width:o,height:o,border:`${(0,eg.zA)(l)} solid ${n}`,boxShadow:`${r}, 0 0 0 1px ${a}`,outline:"none",insetInlineStart:e.calc(l).mul(-1).equal(),top:e.calc(l).mul(-1).equal(),background:"transparent",transition:"none"},"&:focus":m}}],[`${t}-slider-container`]:{display:"flex",gap:s,marginBottom:s,[`${t}-slider-group`]:{flex:1,flexDirection:"column",justifyContent:"space-between",display:"flex","&-disabled-alpha":{justifyContent:"center"}}},[`${t}-gradient-slider`]:{marginBottom:c,[`& ${t}-slider-handle`]:{"&:after":{transform:"scale(0.8)"},"&-active, &:focus":m}}}},tM=(e,t,r)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${(0,eg.zA)(e.controlOutlineWidth)} ${r}`,outline:0}),tP=e=>{let{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},tD=(e,t,r)=>{let{componentCls:n,borderRadiusSM:a,lineWidth:l,colorSplit:o,colorBorder:i,red6:s}=e;return{[`${n}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:a,border:`${(0,eg.zA)(l)} solid ${o}`,position:"relative",overflow:"hidden",cursor:"inherit",transition:`all ${e.motionDurationFast}`},r),{"&::after":{content:'""',position:"absolute",insetInlineEnd:e.calc(l).mul(-1).equal(),top:e.calc(l).mul(-1).equal(),display:"block",width:40,height:2,transformOrigin:"calc(100% - 1px) 1px",transform:"rotate(-45deg)",backgroundColor:s},"&:hover":{borderColor:i}})}},tz=e=>{let{componentCls:t,colorError:r,colorWarning:n,colorErrorHover:a,colorWarningHover:l,colorErrorOutline:o,colorWarningOutline:i}=e;return{[`&${t}-status-error`]:{borderColor:r,"&:hover":{borderColor:a},[`&${t}-trigger-active`]:Object.assign({},tM(e,r,o))},[`&${t}-status-warning`]:{borderColor:n,"&:hover":{borderColor:l},[`&${t}-trigger-active`]:Object.assign({},tM(e,n,i))}}},tR=e=>{let{componentCls:t,controlHeightLG:r,controlHeightSM:n,controlHeight:a,controlHeightXS:l,borderRadius:o,borderRadiusSM:i,borderRadiusXS:s,borderRadiusLG:c,fontSizeLG:d}=e;return{[`&${t}-lg`]:{minWidth:r,minHeight:r,borderRadius:c,[`${t}-color-block, ${t}-clear`]:{width:a,height:a,borderRadius:o},[`${t}-trigger-text`]:{fontSize:d}},[`&${t}-sm`]:{minWidth:n,minHeight:n,borderRadius:i,[`${t}-color-block, ${t}-clear`]:{width:l,height:l,borderRadius:s},[`${t}-trigger-text`]:{lineHeight:(0,eg.zA)(l)}}}},tH=e=>{let{antCls:t,componentCls:r,colorPickerWidth:n,colorPrimary:a,motionDurationMid:l,colorBgElevated:o,colorTextDisabled:i,colorText:s,colorBgContainerDisabled:c,borderRadius:d,marginXS:u,marginSM:m,controlHeight:g,controlHeightSM:p,colorBgTextActive:f,colorPickerPresetColorSize:h,colorPickerPreviewSize:b,lineWidth:v,colorBorder:x,paddingXXS:y,fontSize:C,colorPrimaryHover:A,controlOutline:j}=e;return[{[r]:Object.assign({[`${r}-inner`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({"&-content":{display:"flex",flexDirection:"column",width:n,[`& > ${t}-divider`]:{margin:`${(0,eg.zA)(m)} 0 ${(0,eg.zA)(u)}`}},[`${r}-panel`]:Object.assign({},tS(e))},tN(e)),tk(e,b)),tE(e)),tO(e)),tD(e,h,{marginInlineStart:"auto"})),{[`${r}-operation`]:{display:"flex",justifyContent:"space-between",marginBottom:u}}),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:g,minHeight:g,borderRadius:d,border:`${(0,eg.zA)(v)} solid ${x}`,cursor:"pointer",display:"inline-flex",alignItems:"flex-start",justifyContent:"center",transition:`all ${l}`,background:o,padding:e.calc(y).sub(v).equal(),[`${r}-trigger-text`]:{marginInlineStart:u,marginInlineEnd:e.calc(u).sub(e.calc(y).sub(v)).equal(),fontSize:C,color:s,alignSelf:"center","&-cell":{"&:not(:last-child):after":{content:'", "'},"&-inactive":{color:i}}},"&:hover":{borderColor:A},[`&${r}-trigger-active`]:Object.assign({},tM(e,a,j)),"&-disabled":{color:i,background:c,cursor:"not-allowed","&:hover":{borderColor:f},[`${r}-trigger-text`]:{color:i}}},tD(e,p)),tk(e,p)),tz(e)),tR(e))},tP(e))},(0,t$.G)(e,{focusElCls:`${r}-trigger-active`})]},tI=(0,ef.OF)("ColorPicker",e=>{let{colorTextQuaternary:t,marginSM:r}=e;return[tH((0,eh.oX)(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:24,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:8,colorPickerPreviewSize:e.calc(8).mul(2).add(r).equal()}))]});var tB=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let tF=e=>{let{mode:t,value:r,defaultValue:n,format:o,defaultFormat:i,allowClear:s=!1,presets:c,children:d,trigger:u="click",open:m,disabled:g,placement:p="bottomLeft",arrow:f=!0,panelRender:h,showText:b,style:v,className:x,size:y,rootClassName:C,prefixCls:A,styles:j,disabledAlpha:$=!1,onFormatChange:w,onChange:k,onClear:E,onOpenChange:S,onChangeComplete:O,getPopupContainer:N,autoAdjustOverflow:M=!0,destroyTooltipOnHide:P,disabledFormat:D}=e,z=tB(e,["mode","value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide","disabledFormat"]),{getPrefixCls:B,direction:K,colorPicker:U}=(0,a.useContext)(q.QO),Y=(0,a.useContext)(L.A),Z=null!=g?g:Y,[J,Q]=(0,H.A)(!1,{value:m,postState:e=>!Z&&e,onChange:S}),[ee,et]=(0,H.A)(o,{value:o,defaultValue:i,onChange:w}),er=B("color-picker",A),[en,ea,el,eo,ei]=function(e,t,r){let[n]=(0,tC.A)("ColorPicker"),[l,o]=(0,H.A)(e,{value:t}),[i,s]=a.useState("single"),[c,d]=a.useMemo(()=>{let e=(Array.isArray(r)?r:[r]).filter(e=>e);e.length||e.push("single");let t=new Set(e),a=[],l=(e,r)=>{t.has(e)&&a.push({label:r,value:e})};return l("single",n.singleColor),l("gradient",n.gradientColor),[a,t]},[r]),[u,m]=a.useState(null),g=(0,eq.A)(e=>{m(e),o(e)}),p=a.useMemo(()=>{let e=(0,ek.Z6)(l||"");return e.equals(u)?u:e},[l,u]),f=a.useMemo(()=>{var e;return d.has(i)?i:null===(e=c[0])||void 0===e?void 0:e.value},[d,i,c]);return a.useEffect(()=>{s(p.isGradient()?"gradient":"single")},[p]),[p,g,f,s,c]}(n,r,t),es=(0,a.useMemo)(()=>100>(0,ek.Gp)(en),[en]),[ec,ed]=l().useState(null),eu=e=>{if(O){let t=(0,ek.Z6)(e);$&&es&&(t=(0,ek.E)(e)),O(t)}},em=(e,t)=>{let r=(0,ek.Z6)(e);$&&es&&(r=(0,ek.E)(r)),ea(r),ed(null),k&&k(r,r.toCssString()),t||eu(r)},[eg,ep]=l().useState(0),[ef,eh]=l().useState(!1),{status:eb}=l().useContext(V.$W),{compactSize:ev,compactItemClassnames:ex}=(0,_.RQ)(er,K),ey=(0,W.A)(e=>{var t;return null!==(t=null!=y?y:ev)&&void 0!==t?t:e}),eC=(0,T.A)(er),[eA,ej,e$]=tI(er,eC),ew={[`${er}-rtl`]:K},eE=R()(C,e$,eC,ew),eS=R()((0,F.L)(er,eb),{[`${er}-sm`]:"small"===ey,[`${er}-lg`]:"large"===ey},ex,null==U?void 0:U.className,eE,x,ej),eO=R()(er,eE),eN=Object.assign(Object.assign({},null==U?void 0:U.style),v);return eA(l().createElement(G.A,Object.assign({style:null==j?void 0:j.popup,styles:{body:null==j?void 0:j.popupOverlayInner},onOpenChange:e=>{e&&Z||Q(e)},content:l().createElement(I.A,{form:!0},l().createElement(tx,{mode:el,onModeChange:e=>{if(eo(e),"single"===e&&en.isGradient())ep(0),em(new X.kf(en.getColors()[0].color)),ed(en);else if("gradient"===e&&!en.isGradient()){let e=es?(0,ek.E)(en):en;em(new X.kf(ec||[{percent:0,color:e},{percent:100,color:e}]))}},modeOptions:ei,prefixCls:er,value:en,allowClear:s,disabled:Z,disabledAlpha:$,presets:c,panelRender:h,format:ee,onFormatChange:et,onChange:em,onChangeComplete:eu,onClear:E,activeIndex:eg,onActive:ep,gradientDragging:ef,onGradientDragging:eh,disabledFormat:D})),classNames:{root:eO}},{open:J,trigger:u,placement:p,arrow:f,rootClassName:C,getPopupContainer:N,autoAdjustOverflow:M,destroyTooltipOnHide:P}),d||l().createElement(tj,Object.assign({activeIndex:J?eg:-1,open:J,className:eS,style:eN,prefixCls:er,disabled:Z,showText:b,format:ee},z,{color:en}))))},tq=(0,B.A)(tF,void 0,e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1}),"color-picker",e=>e);tF._InternalPanelDoNotUseOrYouWillBeFired=tq;var tL=r(98776),tT=r(4472),tW=r(97464),tV=r(49792);let{TextArea:tG}=s.A,t_=({isOpen:e,onClose:t,onSuccess:r,category:l})=>{let[o]=D.A.useForm(),c=!!l,[d,{isLoading:u}]=(0,b.DY)(),[m,{isLoading:f}]=(0,b.CQ)(),h=u||f,v=(0,a.useMemo)(()=>["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#6366F1","#14B8A6","#F97316","#6B7280"],[]);(0,a.useEffect)(()=>{e&&(c&&l?o.setFieldsValue({name:l.name,description:l.description,color:l.color,isDefault:l.isDefault}):(o.resetFields(),o.setFieldsValue({color:v[Math.floor(Math.random()*v.length)],isDefault:!1})))},[e,c,l,o,v]);let x=async e=>{try{let n;let a={name:e.name,description:e.description,color:"string"==typeof e.color?e.color:e.color?.toHexString?.()||"#6B7280",isDefault:e.isDefault||!1};(n=c&&l?await m({categoryId:l.id,categoryData:a}).unwrap():await d(a).unwrap()).success?(tV.r.success(c?"Category updated successfully!":"Category created successfully!"),o.resetFields(),r?.(),t()):tV.r.error(n.message||"Failed to save category")}catch(e){console.error("Error saving category:",e),tV.r.error(e?.data?.message||e?.message||`Failed to ${c?"update":"create"} category`)}},y=()=>{o.resetFields(),t()},C=c?"Edit Expense Category":"Add New Expense Category",A=(0,n.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,n.jsx)(i.Ay,{onClick:y,disabled:h,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,n.jsxs)(i.Ay,{type:"primary",onClick:()=>o.submit(),loading:h,icon:h?(0,n.jsx)(g.A,{}):(0,n.jsx)(tT.A,{}),children:[c?"Update":"Create"," Category"]})]});return(0,n.jsxs)(tL.A,{isOpen:e,onClose:y,title:C,width:"500px",footer:A,children:[(0,n.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4 pt-10",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Category: ",l?.name]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p.A,{className:"mr-2"}),"Add New Expense Category"]})}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:c?"Update category information":"Create a new expense category for better organization"})]}),(0,n.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,n.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,n.jsxs)(D.A,{form:o,layout:"vertical",onFinish:x,className:"expense-category-form",requiredMark:!0,children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Category Information"}),(0,n.jsx)(D.A.Item,{name:"name",label:(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(p.A,{className:"mr-1"})," Category Name ",(0,n.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter category name"},{max:100,message:"Category name must be at most 100 characters"}],tooltip:"A descriptive name for this expense category",children:(0,n.jsx)(s.A,{placeholder:"e.g., Office Supplies, Utilities, Marketing",maxLength:100})}),(0,n.jsx)(D.A.Item,{name:"description",label:(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(tW.A,{className:"mr-1"})," Description"]}),tooltip:"Optional description to help identify this category",children:(0,n.jsx)(tG,{placeholder:"Brief description of what expenses belong to this category...",rows:3,maxLength:500})})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Appearance"}),(0,n.jsx)(D.A.Item,{name:"color",label:"Category Color",tooltip:"Choose a color to help identify this category visually",children:(0,n.jsx)(tF,{showText:!0,size:"large",presets:[{label:"Recommended",colors:v}]})}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Quick Colors:"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:v.map((e,t)=>(0,n.jsx)("button",{type:"button",className:"w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-500 transition-colors",style:{backgroundColor:e},onClick:()=>o.setFieldValue("color",e),title:`Set color to ${e}`},t))})]})]}),c&&l?.isDefault&&(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("div",{className:"text-yellow-600 font-medium",children:"System Default Category"})}),(0,n.jsx)("div",{className:"text-yellow-700 text-sm mt-1",children:"This is a system default category. Some properties cannot be modified."})]})})]})]})};var tX=r(51531);let tK=()=>{let{user:e}=(0,f.A)(),t=(0,h.a)(),r=e?.role,[l,v]=(0,a.useState)(1),[x]=(0,a.useState)(20),[y,C]=(0,a.useState)(""),[A,j]=(0,a.useState)([]),[$,w]=(0,a.useState)(!1),[k,E]=(0,a.useState)(null),[S,O]=(0,a.useState)(!1),[N,D]=(0,a.useState)(!1),[z,R]=(0,a.useState)(null),[H,I]=(0,a.useState)([]),B="admin"===r||"superadmin"===r,F="admin"===r||"superadmin"===r,q={page:l,limit:x,search:y.trim()},{data:L,isLoading:T,error:W,refetch:V}=(0,b.L)(q),[G,{isLoading:_}]=(0,b.Uc)(),X=L?.data?.categories||[],K=L?.data?.total||0,U=e=>{C(e),v(1)},Y=async()=>{if(z)try{let e=await G(z).unwrap();e.success?(tV.r.success("Category deleted successfully!"),j(e=>e.filter(e=>e!==z)),O(!1),R(null)):tV.r.error(e.message||"Failed to delete category")}catch(e){console.error("Error deleting category:",e),tV.r.error(e?.data?.message||"Failed to delete category")}},Z=async()=>{try{let e=H.map(e=>G(e).unwrap());await Promise.all(e),tV.r.success(`${H.length} category(s) deleted successfully!`),j([]),D(!1),I([])}catch(e){console.error("Error deleting categories:",e),tV.r.error("Failed to delete some categories")}};return W?(0,n.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,n.jsx)(o.A,{className:"w-full",children:(0,n.jsxs)("div",{className:"text-center text-red-500 p-8",children:[(0,n.jsx)("p",{children:"Error loading expense categories. Please try again."}),(0,n.jsx)(i.Ay,{type:"primary",icon:(0,n.jsx)(d.A,{}),onClick:()=>V(),className:"mt-4",children:"Retry"})]})})}):(0,n.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,n.jsxs)(o.A,{title:(0,n.jsx)("span",{className:"text-gray-800",children:"Expense Categories"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:B&&(0,n.jsx)(i.Ay,{type:"primary",icon:(0,n.jsx)(u.A,{}),onClick:()=>{if(!B){tV.r.error("You don't have permission to add categories");return}E(null),w(!0)},size:t?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:t?"":"Add Category"}),children:[(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(s.A,{placeholder:"Search categories...",prefix:(0,n.jsx)(m.A,{}),value:y,onChange:e=>U(e.target.value),className:"max-w-md",allowClear:!0})}),(0,n.jsx)("div",{className:"min-h-96",children:T?(0,n.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,n.jsx)(c.A,{indicator:(0,n.jsx)(g.A,{style:{fontSize:48},spin:!0})})}):(0,n.jsxs)(n.Fragment,{children:[X.length>0?(0,n.jsx)(M,{categories:X,loading:T,onEdit:e=>{if(e.isDefault){tV.r.warning("System default categories cannot be edited");return}E(e),w(!0)},onDelete:e=>{if(!F){tV.r.error("You don't have permission to delete categories");return}let t=X.find(t=>t.id===e);if(t?.isDefault){tV.r.warning("System default categories cannot be deleted");return}R(e),O(!0)},onBulkDelete:F?e=>{if(!F){tV.r.error("You don't have permission to delete categories");return}let t=e.filter(e=>{let t=X.find(t=>t.id===e);return t&&!t.isDefault});if(0===t.length){tV.r.warning("No deletable categories selected");return}I(t),D(!0)}:void 0,selectedCategories:A,onSelectionChange:j,isMobile:t}):(0,n.jsxs)("div",{className:"text-center text-gray-500 py-12",children:[(0,n.jsx)(p.A,{className:"text-4xl mb-4"}),y?(0,n.jsx)("p",{children:"No categories found matching your search."}):(0,n.jsxs)("p",{children:["No expense categories found. ",B&&"Click 'Add Category' to create one."]})]}),X.length>0&&(0,n.jsx)(P.A,{current:l,pageSize:x,total:K,onChange:e=>{v(e)},isMobile:t})]})})]}),(0,n.jsx)(t_,{isOpen:$,onClose:()=>{w(!1),E(null)},onSuccess:()=>{V(),j([])},category:k}),(0,n.jsx)(tX.A,{isOpen:S,onClose:()=>{O(!1),R(null)},onConfirm:Y,title:"Delete Category",message:"Are you sure you want to delete this category? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:_,type:"danger"}),(0,n.jsx)(tX.A,{isOpen:N,onClose:()=>{D(!1),I([])},onConfirm:Z,title:"Delete Multiple Categories",message:`Are you sure you want to delete ${H.length} categories? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:_,type:"danger"})]})}},85187:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(45512);r(58009);var a=r(59022),l=r(60165);let o=({current:e,pageSize:t,total:r,onChange:o,isMobile:i=!1})=>{let s=Math.ceil(r/t);return 0===r?null:(0,n.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,n.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,n.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,n.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,r)})," of"," ",(0,n.jsx)("span",{className:"font-medium text-gray-900",children:r})," results"]})}),(0,n.jsx)("div",{children:(0,n.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,n.jsxs)("button",{onClick:()=>o(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,n.jsx)("span",{className:"sr-only",children:"Previous"}),(0,n.jsx)(a.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,s)},(t,r)=>{let a=r+1;return(0,n.jsx)("button",{onClick:()=>o(a),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:a},a)}),(0,n.jsxs)("button",{onClick:()=>o(e+1),disabled:e>=s,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=s?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,n.jsx)("span",{className:"sr-only",children:"Next"}),(0,n.jsx)(l.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,n.jsx)("button",{onClick:()=>o(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,n.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",s]}),(0,n.jsx)("button",{onClick:()=>o(e+1),disabled:e>=s,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=s?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})}},93402:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\expense-categories\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expense-categories\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,8472,5482,106,4286,6165],()=>r(67446));module.exports=n})();