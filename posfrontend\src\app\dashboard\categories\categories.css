/* Custom styles for the categories table */
.category-table .ant-table-container {
  overflow-x: auto;
  background-color: #ffffff;
}

.category-table .ant-table-body {
  overflow-x: auto !important;
}

/* Ensure all table cells have light background */
.category-table .ant-table {
  background-color: #ffffff;
}

.category-table .ant-table-thead > tr > th,
.category-table .ant-table-tbody > tr > td {
  background-color: #ffffff;
  color: #333333;
}

/* Ensure fixed columns work properly */
.category-table .ant-table-cell.ant-table-cell-fix-left,
.category-table .ant-table-cell.ant-table-cell-fix-right {
  z-index: 2;
  background-color: #ffffff !important;
}

/* Fix for sticky header */
.category-table .ant-table-header {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.category-table .ant-table-cell-fix-left-last::after,
.category-table .ant-table-cell-fix-right-first::after {
  background-color: #ffffff !important;
}

/* Improve tag display on mobile */
@media (max-width: 640px) {
  .category-table .ant-tag {
    margin-right: 0;
    padding: 0 4px;
    font-size: 11px;
  }

  .category-table .ant-table-thead > tr > th,
  .category-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    background-color: #ffffff !important;
    color: #333333;
  }

  .category-table .ant-table-filter-trigger {
    margin-right: -4px;
  }

  /* Ensure action buttons are properly spaced */
  .category-table .ant-space-item {
    margin-right: 0 !important;
  }

  .category-table .ant-btn {
    padding: 0 4px;
  }

  /* Additional fixes for mobile */
  .category-table .ant-table-container::before,
  .category-table .ant-table-container::after {
    background-color: #ffffff !important;
  }

  .category-table .ant-table-cell-scrollbar {
    box-shadow: none;
    background-color: #ffffff !important;
  }

  /* Fix for row hover effect */
  .category-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #f5f5f5 !important;
  }
}

/* Ensure the table doesn't affect the layout */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  background-color: #ffffff;
}

/* Fix for any remaining transparent areas */
.category-table .ant-table-wrapper,
.category-table .ant-spin-nested-loading,
.category-table .ant-spin-container,
.category-table .ant-table,
.category-table .ant-table-content,
.category-table .ant-table-scroll,
.category-table .ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-cell-fix-left-first,
.category-table .ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-cell-fix-right-last {
  background-color: #ffffff !important;
}

/* Fix for striped rows if enabled */
.category-table .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: #ffffff !important;
}

.category-table .ant-table-tbody > tr.ant-table-row:nth-child(even) > td {
  background-color: #f9f9f9 !important;
}

/* Fix for sticky header and scrollbar */
.category-table .ant-table-sticky-scroll {
  background-color: #ffffff !important;
}

.category-table .ant-table-sticky-scroll-bar {
  background-color: #e6e6e6 !important;
}

/* Fix for any shadow effects */
.category-table .ant-table-cell-fix-left-last::after,
.category-table .ant-table-cell-fix-right-first::after {
  box-shadow: none !important;
  background-color: #ffffff !important;
}

/* Fix for any remaining transparent areas */
.category-table .ant-table-container::before,
.category-table .ant-table-container::after,
.category-table .ant-table-header::before,
.category-table .ant-table-header::after,
.category-table .ant-table-body::before,
.category-table .ant-table-body::after {
  display: none !important;
  background-color: #ffffff !important;
}

/* Custom styles for action buttons */
.view-button .anticon-eye {
  color: #3b82f6 !important; /* Blue color for view icon */
  font-size: 18px !important;
}

.edit-button .anticon-edit {
  color: #10b981 !important; /* Green color for edit icon */
  font-size: 18px !important;
}

.delete-button .anticon-delete {
  color: #ef4444 !important; /* Red color for delete icon */
  font-size: 18px !important;
}

/* Hover effects for action buttons */
.view-button:hover {
  background-color: rgba(59, 130, 246, 0.2) !important; /* Blue hover */
}

.edit-button:hover {
  background-color: rgba(16, 185, 129, 0.2) !important; /* Green hover */
}

.delete-button:hover {
  background-color: rgba(239, 68, 68, 0.2) !important; /* Red hover */
}

/* Button background */
.view-button, .edit-button, .delete-button {
  background-color: #f5f5f5 !important;
  border: none !important;
}

/* Pagination styles */
.bg-white {
  background-color: #ffffff !important;
}

.border-gray-200, .border-gray-300 {
  border-color: #e8e8e8 !important;
}

.text-gray-500, .text-gray-700 {
  color: #595959 !important;
}

.bg-blue-50 {
  background-color: #e6f7ff !important;
}

.border-blue-500 {
  border-color: #1890ff !important;
}

.text-blue-600 {
  color: #1890ff !important;
}

.hover\:bg-gray-50:hover {
  background-color: #f5f5f5 !important;
}
