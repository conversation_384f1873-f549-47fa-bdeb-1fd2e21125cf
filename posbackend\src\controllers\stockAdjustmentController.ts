import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createStockAdjustment,
  getAllStockAdjustments,
  getStockAdjustmentById,
  updateStockAdjustmentById,
  deleteStockAdjustmentById,
} from "../services/stockAdjustmentService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleStockAdjustmentRequest = async (req: Request, res: Response): Promise<void> => {
  const { mode, adjustmentId, page, limit, ...data } = req.body;
  const requester = req.user as DecodedToken; // ✅ Extract requester from middleware

  // ✅ Validate mode
  const validModes = ["createnew", "update", "delete", "retrieve"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      // ✅ Create New Stock Adjustment
      case "createnew": {
        if (!data.productId || !data.quantityChange || !data.reason) {
          return sendResponse(res, 400, false, "All stock adjustment details are required.");
        }

        console.log(`Processing stock adjustment: Product ID ${data.productId}, Change: ${data.quantityChange}, Reason: ${data.reason}`);

        try {
          const newAdjustment = await createStockAdjustment(requester, {
            productId: data.productId,
            quantityChange: data.quantityChange,
            reason: data.reason,
          });

          console.log(`Stock adjustment successful: ${JSON.stringify(newAdjustment)}`);
          return sendResponse(res, 201, true, "Stock adjustment created successfully.", newAdjustment);
        } catch (error: any) {
          console.error(`Stock adjustment failed: ${error.message}`);
          return sendResponse(res, 500, false, `Stock adjustment failed: ${error.message}`);
        }
      }

      // ✅ Retrieve Stock Adjustments (Single or All)
      case "retrieve": {
        if (adjustmentId) {
          // Fetch a single stock adjustment by ID
          const adjustment = await getStockAdjustmentById(requester, Number(adjustmentId));
          return sendResponse(res, 200, true, "Stock adjustment retrieved successfully.", adjustment);
        } else {
          // Fetch all stock adjustments with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const adjustments = await getAllStockAdjustments(requester, pageNum, limitNum);
          return sendResponse(res, 200, true, "Stock adjustments retrieved successfully.", adjustments);
        }
      }

      // ✅ Update Stock Adjustment
      case "update": {
        if (!adjustmentId) {
          return sendResponse(res, 400, false, "Stock adjustment ID is required for updating.");
        }

        const updatedAdjustment = await updateStockAdjustmentById(requester, Number(adjustmentId), data);
        return sendResponse(res, 200, true, "Stock adjustment updated successfully.", updatedAdjustment);
      }

      // ✅ Delete Stock Adjustment
      case "delete": {
        if (!adjustmentId) {
          return sendResponse(res, 400, false, "Stock adjustment ID is required for deletion.");
        }

        const result = await deleteStockAdjustmentById(requester, Number(adjustmentId));
        return sendResponse(res, 200, true, "Stock adjustment deleted successfully.", result);
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(res, 500, false, error.message || "Internal Server Error");
  }
};
