'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Navbar, <PERSON><PERSON>, ScrollToTop } from '../components'

export default function Home() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar currentPage="home" />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 overflow-hidden pt-24">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-white animate-fade-in-up">
              <div className="mb-4 mt-8">
                <span className="inline-block bg-yellow-500 text-blue-900 px-4 py-2 rounded-full text-sm font-semibold mb-4 animate-bounce">
                  🎉 New in Ghana - Complete Business Management System
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Meet NEXAPO
                <span className="block text-yellow-400">Complete Business Management</span>
              </h1>

              <p className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">
                Ghana's most comprehensive business management system. POS, inventory management,
                expenses tracking, and powerful analytics - all in one platform.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  href="/contact"
                  className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 font-bold text-lg px-8 py-4 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg"
                >
                  🚀 Request Demo
                </Link>
                <button
                  onClick={() => scrollToSection('how-it-works')}
                  className="bg-transparent hover:bg-white/10 text-white font-semibold text-lg px-8 py-4 rounded-lg border-2 border-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  📺 Watch Demo
                </button>
              </div>
            </div>

            {/* Right Content - Hero Image */}
            <div className="relative animate-fade-in-right">
              <div className="relative bg-white rounded-2xl shadow-2xl p-4 transform rotate-2 hover:rotate-0 transition-transform duration-500">
                <Image
                  src="/images/admin.png"
                  alt="NEXAPO POS System Dashboard - Real-time Sales Analytics"
                  width={700}
                  height={500}
                  className="rounded-lg shadow-lg w-full h-auto"
                  priority
                />
                <div className="absolute -top-3 -right-3 bg-yellow-500 text-blue-900 px-3 py-1 rounded-full font-bold text-xs shadow-lg animate-pulse">
                  ✨ Live System
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-6 -left-6 bg-white rounded-xl shadow-xl p-4 border border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 text-lg">📊</span>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600">Total Revenue</div>
                    <div className="text-lg font-bold text-green-600">₵4,330.00</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-4 border border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 text-lg">🛍️</span>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600">Total Sales</div>
                    <div className="text-lg font-bold text-blue-600">2</div>
                  </div>
                </div>
              </div>

              <div className="absolute top-1/2 -left-4 bg-white rounded-xl shadow-xl p-3 border border-gray-100">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-sm">📦</span>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600">Products</div>
                    <div className="text-sm font-bold text-purple-600">4</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <p className="text-lg text-gray-600 mb-8">
              🇬🇭 Proudly Ghanaian - Built for Ghanaian Businesses
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="text-4xl font-bold text-blue-600 mb-2">NEW</div>
                <div className="text-gray-600 font-medium">Revolutionary System</div>
                <div className="text-sm text-gray-500 mt-1">First of its kind in Ghana</div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="text-4xl font-bold text-green-600 mb-2">₵40</div>
                <div className="text-gray-600 font-medium">Starting Price</div>
                <div className="text-sm text-gray-500 mt-1">Per month, all features</div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="text-4xl font-bold text-purple-600 mb-2">24/7</div>
                <div className="text-gray-600 font-medium">Ghana Support</div>
                <div className="text-sm text-gray-500 mt-1">Local team ready to help</div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="text-4xl font-bold text-orange-600 mb-2">3</div>
                <div className="text-gray-600 font-medium">Months Free Trial</div>
                <div className="text-sm text-gray-500 mt-1">No credit card required</div>
              </div>
            </div>
          </div>

          {/* Early Adopter Benefits */}
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-4">
              🎉 Be Among the First in Ghana!
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              As a new system, early adopters get exclusive benefits: 3-month free trial,
              priority support, feature requests, and special pricing for life!
            </p>
            <button
              onClick={() => scrollToSection('demo')}
              className="bg-white text-orange-600 font-bold px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors"
            >
              Join Early Adopters Program
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Complete Business Management
              <span className="block text-blue-600">Beyond Just Point of Sale</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              NEXAPO is more than a POS system - it's a complete business management platform
              with inventory control, expenses tracking, and powerful analytics.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {[
              {
                icon: "💰",
                title: "Smart Sales Management",
                description: "Process transactions instantly with our intuitive interface. Accept cash, mobile money, and card payments.",
                image: "/images/sales.png",
                status: "available"
              },
              {
                icon: "📊",
                title: "Professional Reports & Analytics",
                description: "Generate comprehensive sales and inventory reports with real-time data. Export professional CSV reports for business analysis.",
                image: "/images/admin.png",
                status: "available",
                highlight: "NEW"
              },
              {
                icon: "👥",
                title: "User Management",
                description: "Add staff members, set permissions, and track individual performance with role-based access.",
                image: "/images/user.png",
                status: "available"
              },
              {
                icon: "📦",
                title: "Advanced Inventory Management",
                description: "Complete inventory control with products, categories, stock levels, and suppliers. Get low-stock alerts and track inventory value in real-time.",
                image: "/images/product.png",
                status: "available",
                highlight: "ENHANCED"
              },
              {
                icon: "💸",
                title: "Expenses Tracking & Management",
                description: "Track all business expenses by category, monitor spending patterns, and generate expense reports for better financial control.",
                image: "/images/expenses.png",
                status: "available",
                highlight: "NEW"
              },
              {
                icon: "🏪",
                title: "Supplier & Purchase Management",
                description: "Manage suppliers, track purchase orders, and maintain vendor relationships. Complete procurement workflow with purchase history.",
                image: "/images/suppliers.png",
                status: "available"
              },
              {
                icon: "📈",
                title: "Business Intelligence & Analytics",
                description: "Comprehensive business insights with sales analytics, expense tracking, profit/loss calculations, and professional reporting with CSV exports.",
                image: "/images/purchases.png",
                status: "available",
                highlight: "ENHANCED"
              }
            ].map((feature, index) => (
              <div key={index} className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300 hover:scale-105 animate-fade-in-up relative" style={{animationDelay: `${index * 0.1}s`}}>
                {feature.status === "available" && !feature.highlight && (
                  <div className="absolute -top-2 -right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    ✅ Available
                  </div>
                )}
                {feature.highlight === "NEW" && (
                  <div className="absolute -top-2 -right-2 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                    🆕 NEW
                  </div>
                )}
                {feature.highlight === "ENHANCED" && (
                  <div className="absolute -top-2 -right-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    ⚡ ENHANCED
                  </div>
                )}
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <div className="bg-white rounded-lg p-2 shadow-sm">
                  <Image
                    src={feature.image}
                    alt={feature.title}
                    width={300}
                    height={200}
                    className="rounded w-full h-32 object-cover"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* New Reports Highlight Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white text-center">
            <div className="max-w-4xl mx-auto">
              <div className="inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4 animate-pulse">
                🆕 NEW FEATURE ALERT
              </div>
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                Complete Business Intelligence & Reporting
              </h3>
              <p className="text-xl text-blue-100 mb-6 leading-relaxed">
                Comprehensive business analytics covering sales, inventory, and expenses.
                Generate professional reports with real-time data and export CSV files for analysis.
              </p>
              <div className="grid md:grid-cols-4 gap-6 mb-8">
                <div className="bg-white/10 rounded-xl p-4">
                  <div className="text-2xl mb-2">📊</div>
                  <h4 className="font-semibold mb-2">Sales Analytics</h4>
                  <p className="text-sm text-blue-100">Track revenue, transactions, top products, and cashier performance</p>
                </div>
                <div className="bg-white/10 rounded-xl p-4">
                  <div className="text-2xl mb-2">📦</div>
                  <h4 className="font-semibold mb-2">Inventory Reports</h4>
                  <p className="text-sm text-blue-100">Monitor stock levels, low-stock alerts, and inventory valuation</p>
                </div>
                <div className="bg-white/10 rounded-xl p-4">
                  <div className="text-2xl mb-2">💸</div>
                  <h4 className="font-semibold mb-2">Expenses Tracking</h4>
                  <p className="text-sm text-blue-100">Track business expenses by category and monitor spending patterns</p>
                </div>
                <div className="bg-white/10 rounded-xl p-4">
                  <div className="text-2xl mb-2">📈</div>
                  <h4 className="font-semibold mb-2">Professional Exports</h4>
                  <p className="text-sm text-blue-100">Export all data in professional CSV format with proper headers</p>
                </div>
              </div>
              <div className="text-sm text-blue-100">
                ✨ Available now in all NEXAPO plans • Real-time data • Professional formatting • Business intelligence
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              See NEXAPO in Action
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Watch how easy it is to manage your entire business with NEXAPO.
              From sales to inventory, everything is just a few clicks away.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div className="animate-fade-in-left">
              <div className="space-y-8">
                <div className="flex items-start gap-4 animate-fade-in-up" style={{animationDelay: '0.1s'}}>
                  <div className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold text-lg">
                    1
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Set Up Your Business</h3>
                    <p className="text-gray-600">Add your products, set prices, and configure your business settings in minutes.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
                  <div className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold text-lg">
                    2
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Start Selling</h3>
                    <p className="text-gray-600">Process sales instantly, print receipts, and accept multiple payment methods.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4 animate-fade-in-up" style={{animationDelay: '0.3s'}}>
                  <div className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold text-lg">
                    3
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Track & Grow</h3>
                    <p className="text-gray-600">Generate professional reports, analyze trends, and export business data to make informed decisions for growth.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative animate-fade-in-left">
              <div className="bg-white rounded-2xl shadow-2xl p-6">
                <Image
                  src="/images/purchases.png"
                  alt="NEXAPO Purchase Management Interface"
                  width={600}
                  height={400}
                  className="rounded-lg w-full h-auto"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <button className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:bg-blue-700 transition-colors hover:scale-110">
                    <span className="text-2xl">▶️</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section id="industries" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Perfect for Every Business Type
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Whether you run a small shop or a large enterprise, NEXAPO adapts to your business needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: "Retail Stores",
                icon: "🏪",
                features: [
                  { name: "Inventory tracking", available: true },
                  { name: "Professional reports", available: true },
                  { name: "Sales management", available: true },
                  { name: "Barcode scanning ready", available: true }
                ],
                color: "blue",
                status: "available"
              },
              {
                title: "General Business",
                icon: "🏢",
                features: [
                  { name: "Sales processing", available: true },
                  { name: "User management", available: true },
                  { name: "Business intelligence", available: true },
                  { name: "Professional exports", available: true }
                ],
                color: "green",
                status: "available"
              },
              {
                title: "Restaurants",
                icon: "🍽️",
                features: [
                  { name: "Basic POS", available: true },
                  { name: "Menu management", available: false },
                  { name: "Kitchen display", available: false },
                  { name: "Table management", available: false }
                ],
                color: "orange",
                status: "coming-soon"
              },
              {
                title: "Pharmacies",
                icon: "💊",
                features: [
                  { name: "Basic POS", available: true },
                  { name: "Prescription tracking", available: false },
                  { name: "Expiry alerts", available: false },
                  { name: "Batch management", available: false }
                ],
                color: "purple",
                status: "coming-soon"
              }
            ].map((industry, index) => (
              <div key={index} className={`bg-${industry.color}-50 rounded-2xl p-6 border border-${industry.color}-100 relative`}>
                {industry.status === "coming-soon" && (
                  <div className="absolute -top-2 -right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    🔄 Coming Soon
                  </div>
                )}
                {industry.status === "available" && (
                  <div className="absolute -top-2 -right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    ✅ Available
                  </div>
                )}
                <div className="text-4xl mb-4">{industry.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{industry.title}</h3>
                <ul className="space-y-2">
                  {industry.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-2 text-gray-600">
                      <span className={`${feature.available ? 'text-green-500' : 'text-orange-500'}`}>
                        {feature.available ? '✓' : '🔄'}
                      </span>
                      <span className={`text-sm ${feature.available ? '' : 'text-gray-400'}`}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              One powerful system, flexible payment options. All plans include full features and 24/7 support.
            </p>

            {/* Pricing Breakdown */}
            <div className="bg-blue-50 rounded-2xl p-6 max-w-4xl mx-auto">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">💰 Smart Savings Breakdown</h3>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white rounded-lg p-4">
                  <div className="font-semibold text-gray-900">Monthly</div>
                  <div className="text-blue-600 font-bold">₵40/month</div>
                  <div className="text-gray-500">Standard rate</div>
                </div>
                <div className="bg-white rounded-lg p-4 border-2 border-green-200">
                  <div className="font-semibold text-gray-900">Quarterly</div>
                  <div className="text-green-600 font-bold">₵36/month</div>
                  <div className="text-gray-500">Save ₵4/month</div>
                </div>
                <div className="bg-white rounded-lg p-4 border-2 border-purple-200">
                  <div className="font-semibold text-gray-900">Annual</div>
                  <div className="text-purple-600 font-bold">₵30/month</div>
                  <div className="text-gray-500">Save ₵10/month</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: "Monthly",
                price: "₵40",
                period: "per month",
                description: "Perfect for getting started",
                originalPrice: null,
                savings: null,
                discount: null,
                features: [
                  "Complete POS system",
                  "Inventory management",
                  "Expenses tracking",
                  "Professional reports & analytics",
                  "Multi-user support",
                  "Payment integration",
                  "24/7 support",
                  "CSV export functionality"
                ],
                popular: false
              },
              {
                name: "Quarterly",
                price: "₵108",
                period: "every 3 months",
                description: "Save with quarterly billing",
                originalPrice: "₵120",
                savings: "₵12",
                discount: "10%",
                features: [
                  "Complete business management suite",
                  "POS + Inventory + Expenses tracking",
                  "10% discount applied",
                  "Priority support",
                  "Advanced business intelligence",
                  "Professional CSV exports",
                  "Free training session",
                  "Quarterly business review"
                ],
                popular: true
              },
              {
                name: "Annual",
                price: "₵360",
                period: "per year",
                description: "Best value - Maximum savings!",
                originalPrice: "₵480",
                savings: "₵120",
                discount: "25%",
                features: [
                  "Complete business management platform",
                  "POS + Inventory + Expenses + Analytics",
                  "25% discount applied",
                  "Maximum savings",
                  "VIP support",
                  "Custom training",
                  "Comprehensive business intelligence",
                  "Early feature access"
                ],
                popular: false
              }
            ].map((plan, index) => (
              <div key={index} className={`bg-white rounded-2xl p-8 shadow-lg relative transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in-up ${plan.popular ? 'ring-2 ring-blue-600' : ''}`} style={{animationDelay: `${index * 0.1}s`}}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}
                {plan.discount && (
                  <div className="absolute -top-2 -right-2 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                    {plan.discount} OFF
                  </div>
                )}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  {plan.originalPrice && (
                    <div className="text-lg text-gray-400 line-through mb-1">{plan.originalPrice}</div>
                  )}
                  <div className="text-4xl font-bold text-blue-600 mb-2">{plan.price}</div>
                  <div className="text-gray-600">{plan.period}</div>
                  {plan.savings && (
                    <div className="text-green-600 font-semibold text-sm mt-2">
                      💰 Save {plan.savings}
                    </div>
                  )}
                  <p className="text-gray-600 mt-4">{plan.description}</p>
                </div>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-3">
                      <span className="text-green-500">✓</span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors text-center block ${
                    plan.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                >
                  Request Demo
                </Link>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              🎉 <strong>Save More with Longer Commitments:</strong> 10% off quarterly • 25% off annually!
            </p>
            <div className="bg-green-50 border border-green-200 rounded-xl p-6 max-w-2xl mx-auto mb-6">
              <div className="flex items-center justify-center gap-3 mb-2">
                <span className="text-2xl">🎉</span>
                <h3 className="text-lg font-bold text-green-800">3-Month Free Trial</h3>
              </div>
              <p className="text-green-700 text-sm">
                Try NEXAPO completely free for 3 months! Experience all features with no commitment.
                Start your business transformation today - no credit card required.
              </p>
            </div>
            <p className="text-sm text-gray-500">
              All plans include complete POS features • 24/7 support • No setup fees • Professional training
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="demo" className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-white">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ready to Transform Your Business?
              </h2>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                Join the revolution! Be among the first businesses in Ghana to experience
                the power of NEXAPO. Contact us for a personalized demo.
              </p>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <span className="text-yellow-400">✓</span>
                  <span>Personalized demo session</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-yellow-400">✓</span>
                  <span>Free consultation and setup guidance</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-yellow-400">✓</span>
                  <span>Training and onboarding support</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-yellow-400">✓</span>
                  <span>Early adopter benefits and priority support</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Get Your Personalized Demo
              </h3>

              <p className="text-gray-600 mb-8">
                Ready to see NEXAPO in action? Our team will provide you with a personalized demo
                tailored to your business needs.
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3 text-left">
                  <span className="text-blue-600">📞</span>
                  <span className="text-gray-700">Schedule a live demo call</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <span className="text-blue-600">💼</span>
                  <span className="text-gray-700">Business consultation included</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <span className="text-blue-600">🎯</span>
                  <span className="text-gray-700">Customized to your industry</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <span className="text-blue-600">⚡</span>
                  <span className="text-gray-700">Quick 30-minute session</span>
                </div>
              </div>

              <Link
                href="/contact"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold text-lg py-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                📅 Schedule Demo
              </Link>

              <p className="text-sm text-gray-500 mt-4">
                No commitment required • Free consultation • Professional guidance
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      <ScrollToTop />
    </div>
  )
}
