"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const constant_1 = require("../constant/constant");
const errorHandler_1 = require("./errorHandler");
const authMiddleware = (req, res, next) => {
    const authHeader = req.headers.authorization; // ✅ Extract token from headers
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        next(new errorHandler_1.AuthenticationError("Unauthorized: No token provided"));
        return;
    }
    const token = authHeader.split(" ")[1]; // ✅ Extract actual token
    try {
        const decoded = jsonwebtoken_1.default.verify(token, constant_1.JWT_SECRET);
        req.user = decoded; // ✅ Attach decoded user info to request
        next();
    }
    catch (error) {
        // Check for specific JWT errors
        if (error.name === 'TokenExpiredError') {
            next(new errorHandler_1.AuthorizationError("Your session has expired. Please log in again."));
        }
        else if (error.name === 'JsonWebTokenError') {
            next(new errorHandler_1.AuthorizationError("Invalid token. Please log in again."));
        }
        else {
            next(new errorHandler_1.AuthorizationError("Forbidden: Invalid or expired token"));
        }
    }
};
exports.authMiddleware = authMiddleware;
