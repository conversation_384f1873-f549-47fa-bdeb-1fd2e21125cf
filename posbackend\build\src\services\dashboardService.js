"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCashierStats = exports.getAdminStats = exports.getSuperAdminStats = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const dayjs_1 = __importDefault(require("dayjs"));
/**
 * Get dashboard statistics for superadmin
 */
const getSuperAdminStats = async (requester) => {
    // Ensure user is authorized
    if (requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only superadmins can access this data");
    }
    // Get total stores count
    const storesCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.stores);
    // Get total admins count
    const adminsCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.eq)(schema_1.users.role, "admin"));
    // Get total users count
    const usersCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.users);
    // Calculate real revenue from admin subscriptions
    // SuperAdmin revenue comes from admin subscription payments (64 cedis per month per admin)
    const revenueResult = await db_1.postgresDb
        .select({
        totalRevenue: (0, drizzle_orm_1.sql) `COALESCE(SUM(CAST(${schema_1.payments.amount} AS DECIMAL)), 0)`
    })
        .from(schema_1.payments)
        .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.payments.userId, schema_1.users.id))
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.status, "successful"), (0, drizzle_orm_1.eq)(schema_1.users.role, "admin") // Only count payments from admin users
    ));
    const totalRevenue = parseFloat(revenueResult[0]?.totalRevenue || "0");
    console.log("SuperAdmin dashboard - Real revenue from admin subscriptions:", totalRevenue);
    // Calculate growth rates based on recent activity
    // For stores, check if any new stores were added in the last 30 days
    const thirtyDaysAgo = (0, dayjs_1.default)().subtract(30, 'days').toDate();
    const recentStores = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.stores)
        .where((0, drizzle_orm_1.gte)(schema_1.stores.createdAt, thirtyDaysAgo));
    const recentAdmins = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.role, "admin"), (0, drizzle_orm_1.gte)(schema_1.users.createdAt, thirtyDaysAgo)));
    const recentUsers = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.gte)(schema_1.users.createdAt, thirtyDaysAgo));
    // Since this is the first month of operation, we don't need to calculate
    // month-to-month growth rates as we did above
    // Calculate growth rates
    const storesGrowthRate = storesCount[0]?.count > 0
        ? (recentStores[0]?.count / storesCount[0]?.count)
        : 0;
    const adminsGrowthRate = adminsCount[0]?.count > 0
        ? (recentAdmins[0]?.count / adminsCount[0]?.count)
        : 0;
    const usersGrowthRate = usersCount[0]?.count > 0
        ? (recentUsers[0]?.count / usersCount[0]?.count)
        : 0;
    // For the first month, we'll set a more reasonable growth rate
    // Since this is the first month, we'll use a small positive value
    const revenueGrowthRate = 0.01; // This will display as 1.0% with our formatter
    return {
        stores: {
            value: storesCount[0]?.count || 0,
            growthRate: storesGrowthRate,
        },
        revenue: {
            value: totalRevenue,
            growthRate: revenueGrowthRate,
        },
        admins: {
            value: adminsCount[0]?.count || 0,
            growthRate: adminsGrowthRate,
        },
        users: {
            value: usersCount[0]?.count || 0,
            growthRate: usersGrowthRate,
        },
    };
};
exports.getSuperAdminStats = getSuperAdminStats;
/**
 * Get dashboard statistics for admin
 */
const getAdminStats = async (requester) => {
    // Ensure user is authorized
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins can access this data");
    }
    // Get team members (users created by this admin)
    const teamMembersResult = await db_1.postgresDb
        .select({ memberId: schema_1.users.id })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
    const teamMemberIds = teamMembersResult.map((m) => m.memberId);
    // Include the admin's ID in the team IDs
    const teamIds = [requester.id, ...teamMemberIds];
    // Get total sales count for this admin and their team
    const salesCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds));
    // Get total revenue for this admin and their team (including cashiers)
    const totalRevenue = await db_1.postgresDb
        .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.totalAmount})` })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds));
    // Get total products count for this admin
    const productsCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.products)
        .where((0, drizzle_orm_1.eq)(schema_1.products.createdBy, requester.id));
    // Get total cashiers count for this admin
    const cashiersCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.users.role, "cashier")));
    // Get revenue growth rate (comparing current month to previous month)
    const currentMonth = (0, dayjs_1.default)().startOf('month').toDate();
    const previousMonth = (0, dayjs_1.default)().subtract(1, 'month').startOf('month').toDate();
    const currentMonthRevenue = await db_1.postgresDb
        .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.totalAmount})` })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds), // Include admin and team sales
    (0, drizzle_orm_1.gte)(schema_1.sales.transactionDate, currentMonth)));
    const previousMonthRevenue = await db_1.postgresDb
        .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.totalAmount})` })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds), // Include admin and team sales
    (0, drizzle_orm_1.gte)(schema_1.sales.transactionDate, previousMonth), (0, drizzle_orm_1.lt)(schema_1.sales.transactionDate, currentMonth)));
    // Calculate growth rates
    const revenueGrowthRate = calculateGrowthRate(parseFloat(previousMonthRevenue[0]?.total || "0"), parseFloat(currentMonthRevenue[0]?.total || "0"));
    const salesGrowthRate = 0; // This would require historical data
    const productsGrowthRate = 0; // This would require historical data
    const cashiersGrowthRate = 0; // This would require historical data
    // Calculate total expenses for this admin and their team
    const totalExpenses = await db_1.postgresDb
        .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.expenses.amount})` })
        .from(schema_1.expenses)
        .where((0, drizzle_orm_1.inArray)(schema_1.expenses.createdBy, teamIds));
    // Calculate Cost of Goods Sold (COGS) from sales items for admin and team
    const cogsResult = await db_1.postgresDb
        .select({
        totalCogs: (0, drizzle_orm_1.sql) `
        COALESCE(SUM(
          CAST(${schema_1.salesItems.quantity} AS DECIMAL) *
          CAST(${schema_1.products.cost} AS DECIMAL)
        ), 0)
      `
    })
        .from(schema_1.salesItems)
        .innerJoin(schema_1.sales, (0, drizzle_orm_1.eq)(schema_1.salesItems.saleId, schema_1.sales.id))
        .innerJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.salesItems.productId, schema_1.products.id))
        .where((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds));
    // Debug: Get detailed COGS breakdown
    const cogsBreakdown = await db_1.postgresDb
        .select({
        productName: schema_1.products.name,
        productCost: schema_1.products.cost,
        quantity: schema_1.salesItems.quantity,
        itemCogs: (0, drizzle_orm_1.sql) `CAST(${schema_1.salesItems.quantity} AS DECIMAL) * CAST(${schema_1.products.cost} AS DECIMAL)`
    })
        .from(schema_1.salesItems)
        .innerJoin(schema_1.sales, (0, drizzle_orm_1.eq)(schema_1.salesItems.saleId, schema_1.sales.id))
        .innerJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.salesItems.productId, schema_1.products.id))
        .where((0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamIds))
        .limit(10); // Limit to first 10 for debugging
    console.log(`🔍 COGS Breakdown (first 10 items):`, cogsBreakdown);
    const totalCogs = parseFloat(cogsResult[0]?.totalCogs || "0");
    const totalExpensesAmount = parseFloat(totalExpenses[0]?.total || "0");
    const revenueAmount = parseFloat(totalRevenue[0]?.total || "0");
    // Debug logging for profit calculation
    console.log(`📊 Admin Dashboard Profit Calculation for Admin ID ${requester.id}:`);
    console.log(`   Revenue: ₵${revenueAmount.toFixed(2)}`);
    console.log(`   COGS: ₵${totalCogs.toFixed(2)}`);
    console.log(`   Expenses: ₵${totalExpensesAmount.toFixed(2)}`);
    // Calculate profit: Revenue - COGS - Expenses
    const totalProfit = revenueAmount - totalCogs - totalExpensesAmount;
    console.log(`   Calculated Profit: ₵${totalProfit.toFixed(2)} (Revenue - COGS - Expenses)`);
    // Calculate profit margin percentage
    const profitMargin = revenueAmount > 0 ? (totalProfit / revenueAmount) * 100 : 0;
    console.log(`   Profit Margin: ${profitMargin.toFixed(1)}%`);
    return {
        sales: {
            value: salesCount[0]?.count || 0,
            growthRate: salesGrowthRate,
        },
        revenue: {
            value: revenueAmount,
            growthRate: revenueGrowthRate,
        },
        products: {
            value: productsCount[0]?.count || 0,
            growthRate: productsGrowthRate,
        },
        cashiers: {
            value: cashiersCount[0]?.count || 0,
            growthRate: cashiersGrowthRate,
        },
        // New profit/loss metrics
        profit: {
            value: totalProfit,
            growthRate: 0, // Would need historical data for accurate calculation
        },
        expenses: {
            value: totalExpensesAmount,
            growthRate: 0, // Would need historical data for accurate calculation
        },
        cogs: {
            value: totalCogs,
            growthRate: 0, // Would need historical data for accurate calculation
        },
        profitMargin: {
            value: profitMargin,
            growthRate: 0, // Would need historical data for accurate calculation
        },
    };
};
exports.getAdminStats = getAdminStats;
/**
 * Get dashboard statistics for cashier
 */
const getCashierStats = async (requester) => {
    // Ensure user is authorized
    if (requester.role !== "cashier" && requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized access");
    }
    // Get today's date range
    const today = (0, dayjs_1.default)().startOf('day').toDate();
    const tomorrow = (0, dayjs_1.default)().add(1, 'day').startOf('day').toDate();
    // Get today's sales count for this cashier
    const todaySalesCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.gte)(schema_1.sales.transactionDate, today), (0, drizzle_orm_1.lt)(schema_1.sales.transactionDate, tomorrow)));
    // Get today's revenue for this cashier
    const todayRevenue = await db_1.postgresDb
        .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.totalAmount})` })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.gte)(schema_1.sales.transactionDate, today), (0, drizzle_orm_1.lt)(schema_1.sales.transactionDate, tomorrow)));
    // Get total products count
    const productsCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.products);
    // Get total sales count for this cashier
    const totalSalesCount = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.sales)
        .where((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
    // Calculate growth rates (would need historical data for accurate calculation)
    const todaySalesGrowthRate = 0;
    const todayRevenueGrowthRate = 0;
    const productsGrowthRate = 0;
    const totalSalesGrowthRate = 0;
    return {
        todaySales: {
            value: todaySalesCount[0]?.count || 0,
            growthRate: todaySalesGrowthRate,
        },
        todayRevenue: {
            value: parseFloat(todayRevenue[0]?.total || "0"),
            growthRate: todayRevenueGrowthRate,
        },
        totalProducts: {
            value: productsCount[0]?.count || 0,
            growthRate: productsGrowthRate,
        },
        totalSales: {
            value: totalSalesCount[0]?.count || 0,
            growthRate: totalSalesGrowthRate,
        },
    };
};
exports.getCashierStats = getCashierStats;
/**
 * Helper function to calculate growth rate
 * Returns a decimal value (e.g., 0.05 for 5% growth)
 */
function calculateGrowthRate(previous, current) {
    if (previous === 0) {
        // If previous is 0, cap the growth rate to avoid extreme values
        return current > 0 ? 1.0 : 0;
    }
    // Calculate growth rate as a decimal
    const growthRate = (current - previous) / previous;
    // Cap extreme values to provide more reasonable display
    if (growthRate > 10)
        return 10;
    if (growthRate < -10)
        return -10;
    return growthRate;
}
