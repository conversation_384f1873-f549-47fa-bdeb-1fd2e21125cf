// Comprehensive Offline Storage for ALL NEXAPO POS Features
import { openDB, DBSchema, IDBPDatabase } from 'idb';

// Database configuration
const DB_NAME = 'NEXAPO_COMPREHENSIVE_DB';
const DB_VERSION = 1;

// Define comprehensive database schema
interface ComprehensiveOfflineDB extends DBSchema {
  // Core entities
  products: {
    key: number;
    value: OfflineProduct;
    indexes: { 'by-name': string; 'by-barcode': string; 'by-category': number; };
  };
  categories: {
    key: number;
    value: OfflineCategory;
    indexes: { 'by-name': string; };
  };
  suppliers: {
    key: number;
    value: OfflineSupplier;
    indexes: { 'by-name': string; };
  };
  customers: {
    key: number;
    value: OfflineCustomer;
    indexes: { 'by-name': string; 'by-phone': string; };
  };
  users: {
    key: number;
    value: OfflineUser;
    indexes: { 'by-email': string; 'by-role': string; };
  };
  stores: {
    key: number;
    value: OfflineStore;
    indexes: { 'by-name': string; };
  };
  
  // Transaction entities
  sales: {
    key: string;
    value: OfflineSale;
    indexes: { 'by-timestamp': Date; 'by-status': string; 'by-user': number; };
  };
  purchases: {
    key: string;
    value: OfflinePurchase;
    indexes: { 'by-timestamp': Date; 'by-status': string; 'by-supplier': number; };
  };
  expenses: {
    key: string;
    value: OfflineExpense;
    indexes: { 'by-timestamp': Date; 'by-category': number; 'by-user': number; };
  };
  expenseCategories: {
    key: number;
    value: OfflineExpenseCategory;
    indexes: { 'by-name': string; };
  };
  stockAdjustments: {
    key: string;
    value: OfflineStockAdjustment;
    indexes: { 'by-timestamp': Date; 'by-product': number; 'by-user': number; };
  };
  
  // System entities
  receipts: {
    key: string;
    value: OfflineReceipt;
    indexes: { 'by-sale': string; 'by-timestamp': Date; };
  };
  settings: {
    key: string;
    value: OfflineSetting;
  };
  
  // Sync management
  offlineActions: {
    key: string;
    value: OfflineAction;
    indexes: { 'by-type': string; 'by-status': string; 'by-priority': number; };
  };
}

// Type definitions for all entities
export interface OfflineProduct {
  id: number;
  name: string;
  price: string;
  stockQuantity: number;
  sku?: string;
  barcode?: string;
  imageUrl?: string;
  categoryId?: number;
  supplierId?: number;
  description?: string;
  costPrice?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineCategory {
  id: number;
  name: string;
  description?: string;
  color?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineSupplier {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineCustomer {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  isActive: boolean;
  lastUpdated: Date;
}

export interface OfflineStore {
  id: number;
  name: string;
  address: string;
  city: string;
  country: string;
  phone?: string;
  email?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineSale {
  id: string;
  totalAmount: number;
  paymentMethod: string;
  items: OfflineSaleItem[];
  customerId?: number;
  storeId?: number;
  createdBy: number;
  timestamp: Date;
  status: 'pending' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
  receiptUrl?: string;
}

export interface OfflineSaleItem {
  productId: number;
  productName: string;
  quantity: number;
  price: number;
}

export interface OfflinePurchase {
  id: string;
  supplierId: number;
  totalAmount: number;
  items: OfflinePurchaseItem[];
  storeId?: number;
  createdBy: number;
  timestamp: Date;
  status: 'pending' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

export interface OfflinePurchaseItem {
  productId: number;
  productName: string;
  quantity: number;
  costPrice: number;
}

export interface OfflineExpense {
  id: string;
  categoryId: number;
  amount: number;
  description: string;
  date: Date;
  createdBy: number;
  timestamp: Date;
  status: 'pending' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

export interface OfflineExpenseCategory {
  id: number;
  name: string;
  description?: string;
  color?: string;
  lastUpdated: Date;
  isActive: boolean;
}

export interface OfflineStockAdjustment {
  id: string;
  productId: number;
  adjustmentType: 'increase' | 'decrease';
  quantity: number;
  reason: string;
  createdBy: number;
  timestamp: Date;
  status: 'pending' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

export interface OfflineReceipt {
  id: string;
  saleId: string;
  htmlContent: string;
  timestamp: Date;
}

export interface OfflineSetting {
  key: string;
  value: any;
  lastUpdated: Date;
}

export interface OfflineAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: string; // 'product', 'sale', 'expense', etc.
  entityId: string | number;
  data: any;
  priority: number; // 1 = highest, 10 = lowest
  timestamp: Date;
  status: 'pending' | 'syncing' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

class ComprehensiveOfflineStorage {
  private db: IDBPDatabase<ComprehensiveOfflineDB> | null = null;

  async init(): Promise<void> {
    try {
      this.db = await openDB<ComprehensiveOfflineDB>(DB_NAME, DB_VERSION, {
        upgrade(db) {
          // Products store
          const productsStore = db.createObjectStore('products', { keyPath: 'id' });
          productsStore.createIndex('by-name', 'name');
          productsStore.createIndex('by-barcode', 'barcode');
          productsStore.createIndex('by-category', 'categoryId');

          // Categories store
          const categoriesStore = db.createObjectStore('categories', { keyPath: 'id' });
          categoriesStore.createIndex('by-name', 'name');

          // Suppliers store
          const suppliersStore = db.createObjectStore('suppliers', { keyPath: 'id' });
          suppliersStore.createIndex('by-name', 'name');

          // Customers store
          const customersStore = db.createObjectStore('customers', { keyPath: 'id' });
          customersStore.createIndex('by-name', 'name');
          customersStore.createIndex('by-phone', 'phone');

          // Users store
          const usersStore = db.createObjectStore('users', { keyPath: 'id' });
          usersStore.createIndex('by-email', 'email');
          usersStore.createIndex('by-role', 'role');

          // Stores store
          const storesStore = db.createObjectStore('stores', { keyPath: 'id' });
          storesStore.createIndex('by-name', 'name');

          // Sales store
          const salesStore = db.createObjectStore('sales', { keyPath: 'id' });
          salesStore.createIndex('by-timestamp', 'timestamp');
          salesStore.createIndex('by-status', 'status');
          salesStore.createIndex('by-user', 'createdBy');

          // Purchases store
          const purchasesStore = db.createObjectStore('purchases', { keyPath: 'id' });
          purchasesStore.createIndex('by-timestamp', 'timestamp');
          purchasesStore.createIndex('by-status', 'status');
          purchasesStore.createIndex('by-supplier', 'supplierId');

          // Expenses store
          const expensesStore = db.createObjectStore('expenses', { keyPath: 'id' });
          expensesStore.createIndex('by-timestamp', 'timestamp');
          expensesStore.createIndex('by-category', 'categoryId');
          expensesStore.createIndex('by-user', 'createdBy');

          // Expense Categories store
          const expenseCategoriesStore = db.createObjectStore('expenseCategories', { keyPath: 'id' });
          expenseCategoriesStore.createIndex('by-name', 'name');

          // Stock Adjustments store
          const stockAdjustmentsStore = db.createObjectStore('stockAdjustments', { keyPath: 'id' });
          stockAdjustmentsStore.createIndex('by-timestamp', 'timestamp');
          stockAdjustmentsStore.createIndex('by-product', 'productId');
          stockAdjustmentsStore.createIndex('by-user', 'createdBy');

          // Receipts store
          const receiptsStore = db.createObjectStore('receipts', { keyPath: 'id' });
          receiptsStore.createIndex('by-sale', 'saleId');
          receiptsStore.createIndex('by-timestamp', 'timestamp');

          // Settings store
          db.createObjectStore('settings', { keyPath: 'key' });

          // Offline Actions store
          const actionsStore = db.createObjectStore('offlineActions', { keyPath: 'id' });
          actionsStore.createIndex('by-type', 'type');
          actionsStore.createIndex('by-status', 'status');
          actionsStore.createIndex('by-priority', 'priority');
        }
      });
      console.log('✅ Comprehensive offline storage initialized');
    } catch (error) {
      console.error('❌ Failed to initialize comprehensive offline storage:', error);
      throw error;
    }
  }

  // Generic CRUD operations for any entity
  async saveEntity<T>(storeName: keyof ComprehensiveOfflineDB, entity: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.put(storeName as any, entity);
  }

  async getEntity<T>(storeName: keyof ComprehensiveOfflineDB, id: string | number): Promise<T | undefined> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.get(storeName as any, id);
  }

  async getAllEntities<T>(storeName: keyof ComprehensiveOfflineDB): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAll(storeName as any);
  }

  async deleteEntity(storeName: keyof ComprehensiveOfflineDB, id: string | number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.delete(storeName as any, id);
  }

  // Offline action management
  async addOfflineAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'syncAttempts'>): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');
    
    const actionId = `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const offlineAction: OfflineAction = {
      ...action,
      id: actionId,
      timestamp: new Date(),
      syncAttempts: 0
    };

    await this.db.add('offlineActions', offlineAction);
    console.log(`📝 Offline action added: ${action.type} ${action.entity}`, actionId);
    return actionId;
  }

  async getPendingActions(): Promise<OfflineAction[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAllFromIndex('offlineActions', 'by-status', 'pending');
  }

  async updateActionStatus(actionId: string, status: OfflineAction['status'], errorMessage?: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const action = await this.db.get('offlineActions', actionId);
    if (!action) throw new Error('Action not found');

    action.status = status;
    action.syncAttempts += 1;
    action.lastSyncAttempt = new Date();
    if (errorMessage) action.errorMessage = errorMessage;

    await this.db.put('offlineActions', action);
  }

  // Bulk operations for initial data sync
  async bulkSaveEntities<T>(storeName: keyof ComprehensiveOfflineDB, entities: T[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const tx = this.db.transaction(storeName as any, 'readwrite');
    const store = tx.objectStore(storeName as any);
    
    await store.clear(); // Clear existing data
    for (const entity of entities) {
      await store.add(entity);
    }
    await tx.done;
    console.log(`💾 Bulk saved ${entities.length} ${storeName} entities`);
  }

  // Get comprehensive storage statistics
  async getStorageStats(): Promise<{
    [key: string]: number;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    const stores = ['products', 'categories', 'suppliers', 'customers', 'users', 'stores', 
                   'sales', 'purchases', 'expenses', 'expenseCategories', 'stockAdjustments', 
                   'receipts', 'settings', 'offlineActions'] as const;

    const stats: { [key: string]: number } = {};
    
    for (const store of stores) {
      const count = await this.db.count(store);
      stats[store] = count;
    }

    // Get pending actions count
    const pendingActions = await this.db.getAllFromIndex('offlineActions', 'by-status', 'pending');
    stats.pendingActions = pendingActions.length;

    return stats;
  }
}

// Create singleton instance
export const comprehensiveOfflineStorage = new ComprehensiveOfflineStorage();

// Initialize on module load
if (typeof window !== 'undefined') {
  comprehensiveOfflineStorage.init().catch(console.error);
}

export default comprehensiveOfflineStorage;
