"use client";

import React from "react";
import { <PERSON><PERSON>, Spin, Descriptions } from "antd";
import {
  EditOutlined,
  LoadingOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useGetSupplierByIdQuery } from "@/reduxRTK/services/supplierApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import "./supplier-panels.css";

interface SupplierDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
  supplierId: number | null;
  onEdit: (supplierId: number) => void;
}

const SupplierDetailPanel: React.FC<SupplierDetailPanelProps> = ({
  isOpen,
  onClose,
  supplierId,
  onEdit,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Fetch supplier details when panel is open and supplierId is available
  const {
    data: response,
    isLoading,
    error,
  } = useGetSupplierByIdQuery(supplierId || 0, {
    skip: !isOpen || !supplierId,
  });

  const supplier = response?.data;

  // The backend already filters suppliers based on permissions
  // If we can fetch the supplier, we can view it
  const canViewSupplier = !!supplier;

  console.log("Supplier detail - User ID:", user?.id);
  console.log("Supplier detail - Supplier:", supplier);
  console.log("Supplier detail - Can view supplier:", canViewSupplier);

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Check if user can edit (admin can edit all suppliers they can see)
  // The backend already filters suppliers based on permissions
  const canEdit = userRole === "admin" && !!supplier;

  // Panel footer with close button and edit button (if user can edit)
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: "#d9d9d9", background: "#f5f5f5" }}
      >
        Close
      </Button>
      {onEdit && supplier && canEdit && (
        <Button type="primary" onClick={() => onEdit(supplier.id)}>
          Edit
        </Button>
      )}
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title="Supplier Details"
      width="500px"
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <Spin
            indicator={
              <LoadingOutlined
                style={{ fontSize: 24, color: "#1890ff" }}
                spin
              />
            }
          />
        </div>
      ) : supplier && canViewSupplier ? (
        <>
          {/* Supplier detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="flex items-center text-xl font-bold text-gray-800">
              <UserOutlined className="mr-2" />
              Supplier: {supplier.name}
            </h2>
            <p className="mt-1 flex items-center text-gray-600">
              Complete supplier information and details
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="supplier-detail-light"
            labelStyle={{ color: "#333", backgroundColor: "#f5f5f5" }}
            contentStyle={{ color: "#333", backgroundColor: "#ffffff" }}
          >
            <Descriptions.Item
              label={
                <span>
                  <UserOutlined /> Supplier ID
                </span>
              }
            >
              {supplier.id}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <UserOutlined /> Name
                </span>
              }
            >
              {supplier.name}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <MailOutlined /> Email
                </span>
              }
            >
              {supplier.email || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <PhoneOutlined /> Phone
                </span>
              }
            >
              {supplier.phone || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <UserOutlined /> Contact Person
                </span>
              }
            >
              {supplier.contactPerson || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <HomeOutlined /> Address
                </span>
              }
            >
              {supplier.address || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <CalendarOutlined /> Created At
                </span>
              }
            >
              {formatDate(supplier.createdAt)}
            </Descriptions.Item>
          </Descriptions>
        </>
      ) : supplier && !canViewSupplier ? (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <p className="text-red-500">
            You don&quot;t have permission to view this supplier.
          </p>
        </div>
      ) : (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <p className="text-gray-800">No supplier data available</p>
        </div>
      )}
    </SlidingPanel>
  );
};

export default SupplierDetailPanel;
