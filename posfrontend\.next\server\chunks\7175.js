"use strict";exports.id=7175,exports.ids=[7175],exports.modules={38636:(e,n,t)=>{t.d(n,{B:()=>r});var o=t(58009);let r=t.n(o)().createContext({})},62312:(e,n,t)=>{t.d(n,{$T:()=>A,ph:()=>g,hN:()=>S});var o=t(43984),r=t(7770),c=t(49543),i=t(58009),a=t.n(i),l=t(12992),s=t(55740),u=t(11855),f=t(65074),v=t(56073),d=t.n(v),m=t(80775),y=t(97549),p=t(73924),h=t(90365);let A=i.forwardRef(function(e,n){var t=e.prefixCls,o=e.style,c=e.className,a=e.duration,l=void 0===a?4.5:a,s=e.showProgress,v=e.pauseOnHover,m=void 0===v||v,A=e.eventKey,k=e.content,g=e.closable,N=e.closeIcon,E=void 0===N?"x":N,C=e.props,x=e.onClick,b=e.onNoticeClose,R=e.times,w=e.hovering,S=i.useState(!1),M=(0,r.A)(S,2),I=M[0],j=M[1],F=i.useState(0),H=(0,r.A)(F,2),L=H[0],O=H[1],W=i.useState(0),D=(0,r.A)(W,2),P=D[0],T=D[1],K=w||I,q=l>0&&s,B=function(){b(A)};i.useEffect(function(){if(!K&&l>0){var e=Date.now()-P,n=setTimeout(function(){B()},1e3*l-P);return function(){m&&clearTimeout(n),T(Date.now()-e)}}},[l,K,R]),i.useEffect(function(){if(!K&&q&&(m||0===P)){var e,n=performance.now();return function t(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var o=Math.min((e+P-n)/(1e3*l),1);O(100*o),o<1&&t()})}(),function(){m&&cancelAnimationFrame(e)}}},[l,P,K,q,R]);var X=i.useMemo(function(){return"object"===(0,y.A)(g)&&null!==g?g:g?{closeIcon:E}:{}},[g,E]),$=(0,h.A)(X,!0),z=100-(!L||L<0?0:L>100?100:L),G="".concat(t,"-notice");return i.createElement("div",(0,u.A)({},C,{ref:n,className:d()(G,c,(0,f.A)({},"".concat(G,"-closable"),g)),style:o,onMouseEnter:function(e){var n;j(!0),null==C||null===(n=C.onMouseEnter)||void 0===n||n.call(C,e)},onMouseLeave:function(e){var n;j(!1),null==C||null===(n=C.onMouseLeave)||void 0===n||n.call(C,e)},onClick:x}),i.createElement("div",{className:"".concat(G,"-content")},k),g&&i.createElement("a",(0,u.A)({tabIndex:0,className:"".concat(G,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===p.A.ENTER)&&B()},"aria-label":"Close"},$,{onClick:function(e){e.preventDefault(),e.stopPropagation(),B()}}),X.closeIcon),q&&i.createElement("progress",{className:"".concat(G,"-progress"),max:"100",value:z},z+"%"))});var k=a().createContext({});let g=function(e){var n=e.children,t=e.classNames;return a().createElement(k.Provider,{value:{classNames:t}},n)},N=function(e){var n,t,o,r={offset:8,threshold:3,gap:16};return e&&"object"===(0,y.A)(e)&&(r.offset=null!==(n=e.offset)&&void 0!==n?n:8,r.threshold=null!==(t=e.threshold)&&void 0!==t?t:3,r.gap=null!==(o=e.gap)&&void 0!==o?o:16),[!!e,r]};var E=["className","style","classNames","styles"];let C=function(e){var n=e.configList,t=e.placement,s=e.prefixCls,v=e.className,y=e.style,p=e.motion,h=e.onAllNoticeRemoved,g=e.onNoticeClose,C=e.stack,x=(0,i.useContext)(k).classNames,b=(0,i.useRef)({}),R=(0,i.useState)(null),w=(0,r.A)(R,2),S=w[0],M=w[1],I=(0,i.useState)([]),j=(0,r.A)(I,2),F=j[0],H=j[1],L=n.map(function(e){return{config:e,key:String(e.key)}}),O=N(C),W=(0,r.A)(O,2),D=W[0],P=W[1],T=P.offset,K=P.threshold,q=P.gap,B=D&&(F.length>0||L.length<=K),X="function"==typeof p?p(t):p;return(0,i.useEffect)(function(){D&&F.length>1&&H(function(e){return e.filter(function(e){return L.some(function(n){return e===n.key})})})},[F,L,D]),(0,i.useEffect)(function(){var e,n;D&&b.current[null===(e=L[L.length-1])||void 0===e?void 0:e.key]&&M(b.current[null===(n=L[L.length-1])||void 0===n?void 0:n.key])},[L,D]),a().createElement(m.aF,(0,u.A)({key:t,className:d()(s,"".concat(s,"-").concat(t),null==x?void 0:x.list,v,(0,f.A)((0,f.A)({},"".concat(s,"-stack"),!!D),"".concat(s,"-stack-expanded"),B)),style:y,keys:L,motionAppear:!0},X,{onAllRemoved:function(){h(t)}}),function(e,n){var r=e.config,i=e.className,f=e.style,v=e.index,m=r.key,y=r.times,p=String(m),h=r.className,k=r.style,N=r.classNames,C=r.styles,R=(0,c.A)(r,E),w=L.findIndex(function(e){return e.key===p}),M={};if(D){var I=L.length-1-(w>-1?w:v-1),j="top"===t||"bottom"===t?"-50%":"0";if(I>0){M.height=B?null===(O=b.current[p])||void 0===O?void 0:O.offsetHeight:null==S?void 0:S.offsetHeight;for(var O,W,P,K,X=0,$=0;$<I;$++)X+=(null===(K=b.current[L[L.length-1-$].key])||void 0===K?void 0:K.offsetHeight)+q;var z=(B?X:I*T)*(t.startsWith("top")?1:-1),G=!B&&null!=S&&S.offsetWidth&&null!==(W=b.current[p])&&void 0!==W&&W.offsetWidth?((null==S?void 0:S.offsetWidth)-2*T*(I<3?I:3))/(null===(P=b.current[p])||void 0===P?void 0:P.offsetWidth):1;M.transform="translate3d(".concat(j,", ").concat(z,"px, 0) scaleX(").concat(G,")")}else M.transform="translate3d(".concat(j,", 0, 0)")}return a().createElement("div",{ref:n,className:d()("".concat(s,"-notice-wrapper"),i,null==N?void 0:N.wrapper),style:(0,l.A)((0,l.A)((0,l.A)({},f),M),null==C?void 0:C.wrapper),onMouseEnter:function(){return H(function(e){return e.includes(p)?e:[].concat((0,o.A)(e),[p])})},onMouseLeave:function(){return H(function(e){return e.filter(function(e){return e!==p})})}},a().createElement(A,(0,u.A)({},R,{ref:function(e){w>-1?b.current[p]=e:delete b.current[p]},prefixCls:s,classNames:N,styles:C,className:d()(h,null==x?void 0:x.notice),style:k,times:y,key:m,eventKey:m,onNoticeClose:g,hovering:D&&F.length>0})))})};var x=i.forwardRef(function(e,n){var t=e.prefixCls,c=void 0===t?"rc-notification":t,a=e.container,u=e.motion,f=e.maxCount,v=e.className,d=e.style,m=e.onAllRemoved,y=e.stack,p=e.renderNotifications,h=i.useState([]),A=(0,r.A)(h,2),k=A[0],g=A[1],N=function(e){var n,t=k.find(function(n){return n.key===e});null==t||null===(n=t.onClose)||void 0===n||n.call(t),g(function(n){return n.filter(function(n){return n.key!==e})})};i.useImperativeHandle(n,function(){return{open:function(e){g(function(n){var t,r=(0,o.A)(n),c=r.findIndex(function(n){return n.key===e.key}),i=(0,l.A)({},e);return c>=0?(i.times=((null===(t=n[c])||void 0===t?void 0:t.times)||0)+1,r[c]=i):(i.times=0,r.push(i)),f>0&&r.length>f&&(r=r.slice(-f)),r})},close:function(e){N(e)},destroy:function(){g([])}}});var E=i.useState({}),x=(0,r.A)(E,2),b=x[0],R=x[1];i.useEffect(function(){var e={};k.forEach(function(n){var t=n.placement,o=void 0===t?"topRight":t;o&&(e[o]=e[o]||[],e[o].push(n))}),Object.keys(b).forEach(function(n){e[n]=e[n]||[]}),R(e)},[k]);var w=function(e){R(function(n){var t=(0,l.A)({},n);return(t[e]||[]).length||delete t[e],t})},S=i.useRef(!1);if(i.useEffect(function(){Object.keys(b).length>0?S.current=!0:S.current&&(null==m||m(),S.current=!1)},[b]),!a)return null;var M=Object.keys(b);return(0,s.createPortal)(i.createElement(i.Fragment,null,M.map(function(e){var n=b[e],t=i.createElement(C,{key:e,configList:n,placement:e,prefixCls:c,className:null==v?void 0:v(e),style:null==d?void 0:d(e),motion:u,onNoticeClose:N,onAllNoticeRemoved:w,stack:y});return p?p(t,{prefixCls:c,key:e}):t})),a)}),b=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],R=function(){return document.body},w=0;function S(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.getContainer,t=void 0===n?R:n,a=e.motion,l=e.prefixCls,s=e.maxCount,u=e.className,f=e.style,v=e.onAllRemoved,d=e.stack,m=e.renderNotifications,y=(0,c.A)(e,b),p=i.useState(),h=(0,r.A)(p,2),A=h[0],k=h[1],g=i.useRef(),N=i.createElement(x,{container:A,ref:g,prefixCls:l,motion:a,maxCount:s,className:u,style:f,onAllRemoved:v,stack:d,renderNotifications:m}),E=i.useState([]),C=(0,r.A)(E,2),S=C[0],M=C[1],I=i.useMemo(function(){return{open:function(e){var n=function(){for(var e={},n=arguments.length,t=Array(n),o=0;o<n;o++)t[o]=arguments[o];return t.forEach(function(n){n&&Object.keys(n).forEach(function(t){var o=n[t];void 0!==o&&(e[t]=o)})}),e}(y,e);(null===n.key||void 0===n.key)&&(n.key="rc-notification-".concat(w),w+=1),M(function(e){return[].concat((0,o.A)(e),[{type:"open",config:n}])})},close:function(e){M(function(n){return[].concat((0,o.A)(n),[{type:"close",key:e}])})},destroy:function(){M(function(e){return[].concat((0,o.A)(e),[{type:"destroy"}])})}}},[]);return i.useEffect(function(){k(t())}),i.useEffect(function(){if(g.current&&S.length){var e,n;S.forEach(function(e){switch(e.type){case"open":g.current.open(e.config);break;case"close":g.current.close(e.key);break;case"destroy":g.current.destroy()}}),M(function(t){return e===t&&n||(e=t,n=t.filter(function(e){return!S.includes(e)})),n})}},[S]),[I,N]}}};