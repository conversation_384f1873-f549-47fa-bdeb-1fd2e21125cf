"use client";

import { useState, useEffect } from "react";
import { useGetAllStockAdjustmentsQuery, StockAdjustment } from "@/reduxRTK/services/stockAdjustmentApi";
import { useDebounce } from "@/hooks/useDebounce";

export const useStockAdjustmentList = (initialPage = 1, initialLimit = 10) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [searchTerm, setSearchTerm] = useState('');

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // Fetch stock adjustments with pagination and search
  const {
    data,
    error,
    isLoading,
    refetch
  } = useGetAllStockAdjustmentsQuery({
    page,
    limit,
    search: debouncedSearchTerm
  });

  // Extract stock adjustments and pagination info from the response
  const stockAdjustments: StockAdjustment[] = data?.data?.stockAdjustments || [];
  const total: number = data?.data?.total || 0;

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to page 1 when changing limit
  };

  return {
    stockAdjustments,
    total,
    page,
    limit,
    isLoading,
    error,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
    handleLimitChange
  };
};
