"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteStore = exports.updateStore = exports.getStoreById = exports.getAllStores = exports.createStore = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
/**
 * Create a new store
 * @param requester The user making the request
 * @param storeData The store data
 * @returns The created store
 */
const createStore = async (requester, storeData) => {
    // Only admins and superadmins can create stores
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins and superadmins can create stores.");
    }
    // Start a transaction to ensure atomicity
    return await db_1.postgresDb.transaction(async (trx) => {
        // Insert the store
        const [newStore] = await trx
            .insert(schema_1.stores)
            .values({
            ...storeData,
            createdBy: requester.id,
        })
            .returning();
        if (!newStore) {
            throw new Error("Store creation failed.");
        }
        // Associate the store with the creator
        await trx
            .insert(schema_1.userStores)
            .values({
            userId: requester.id,
            storeId: newStore.id,
            isDefault: true, // Make this the default store for the creator
            createdBy: requester.id,
        });
        return newStore;
    });
};
exports.createStore = createStore;
/**
 * Get all stores (paginated)
 * @param requester The user making the request
 * @param page The page number
 * @param limit The number of items per page
 * @returns Paginated list of stores
 */
const getAllStores = async (requester, page = 1, limit = 10) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "stores");
    const offset = (page - 1) * limit;
    const isSuperadmin = requester.role === "superadmin";
    // Different filter logic based on role
    let storesFilter;
    if (isSuperadmin) {
        // Superadmins can see all stores
        storesFilter = undefined;
    }
    else {
        // Get all stores associated with the user
        const userStoreIds = await db_1.postgresDb
            .select({ storeId: schema_1.userStores.storeId })
            .from(schema_1.userStores)
            .where((0, drizzle_orm_1.eq)(schema_1.userStores.userId, requester.id));
        const storeIds = userStoreIds.map((s) => s.storeId);
        if (storeIds.length > 0) {
            // User can see stores they're associated with
            storesFilter = (0, drizzle_orm_1.inArray)(schema_1.stores.id, storeIds);
        }
        else {
            // If no stores found, return empty result
            return {
                total: 0,
                page,
                perPage: limit,
                stores: [],
            };
        }
    }
    // Get stores with creator information
    const [storesData, totalStores] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.stores.id,
            name: schema_1.stores.name,
            address: schema_1.stores.address,
            city: schema_1.stores.city,
            state: schema_1.stores.state,
            country: schema_1.stores.country,
            phone: schema_1.stores.phone,
            email: schema_1.stores.email,
            logo: schema_1.stores.logo,
            website: schema_1.stores.website,
            taxId: schema_1.stores.taxId,
            createdBy: schema_1.stores.createdBy,
            createdByName: schema_1.users.name,
            createdAt: schema_1.stores.createdAt,
            updatedAt: schema_1.stores.updatedAt,
        })
            .from(schema_1.stores)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.stores.createdBy, schema_1.users.id))
            .where(storesFilter)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.stores.createdAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stores)
            .where(storesFilter),
    ]);
    return {
        total: totalStores[0]?.count ?? 0,
        page,
        perPage: limit,
        stores: storesData,
    };
};
exports.getAllStores = getAllStores;
/**
 * Get store by ID
 * @param requester The user making the request
 * @param storeId The store ID
 * @returns The store
 */
const getStoreById = async (requester, storeId) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "stores", storeId);
    const isSuperadmin = requester.role === "superadmin";
    // Different filter logic based on role
    let storeFilter;
    if (isSuperadmin) {
        // Superadmins can see any store
        storeFilter = (0, drizzle_orm_1.eq)(schema_1.stores.id, storeId);
    }
    else {
        // Check if user is associated with the store
        const userStoreAssociation = await db_1.postgresDb
            .select()
            .from(schema_1.userStores)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, requester.id), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
            .limit(1);
        if (userStoreAssociation.length === 0) {
            throw new Error("Store not found or unauthorized.");
        }
        storeFilter = (0, drizzle_orm_1.eq)(schema_1.stores.id, storeId);
    }
    // Get store with creator information
    const store = await db_1.postgresDb
        .select({
        id: schema_1.stores.id,
        name: schema_1.stores.name,
        address: schema_1.stores.address,
        city: schema_1.stores.city,
        state: schema_1.stores.state,
        country: schema_1.stores.country,
        phone: schema_1.stores.phone,
        email: schema_1.stores.email,
        logo: schema_1.stores.logo,
        website: schema_1.stores.website,
        taxId: schema_1.stores.taxId,
        createdBy: schema_1.stores.createdBy,
        createdByName: schema_1.users.name,
        createdAt: schema_1.stores.createdAt,
        updatedAt: schema_1.stores.updatedAt,
    })
        .from(schema_1.stores)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.stores.createdBy, schema_1.users.id))
        .where(storeFilter)
        .limit(1);
    if (store.length === 0) {
        throw new Error("Store not found or unauthorized.");
    }
    return store[0];
};
exports.getStoreById = getStoreById;
/**
 * Update store
 * @param requester The user making the request
 * @param storeId The store ID
 * @param updateData The update data
 * @returns The updated store
 */
const updateStore = async (requester, storeId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "stores", storeId);
    // Only admins and superadmins can update stores
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins and superadmins can update stores.");
    }
    // Check if user is associated with the store (unless superadmin)
    if (requester.role !== "superadmin") {
        const userStoreAssociation = await db_1.postgresDb
            .select()
            .from(schema_1.userStores)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, requester.id), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
            .limit(1);
        if (userStoreAssociation.length === 0) {
            throw new Error("Store not found or unauthorized.");
        }
    }
    // Update the store
    const updatedStore = await db_1.postgresDb
        .update(schema_1.stores)
        .set({
        ...updateData,
        updatedAt: new Date(),
    })
        .where((0, drizzle_orm_1.eq)(schema_1.stores.id, storeId))
        .returning();
    if (!updatedStore || updatedStore.length === 0) {
        throw new Error("Update failed: Store not found.");
    }
    return updatedStore[0];
};
exports.updateStore = updateStore;
/**
 * Delete stores (Single or Multiple)
 * @param requester The user making the request
 * @param ids The store ID or array of store IDs
 * @returns The deleted store IDs
 */
const deleteStore = async (requester, ids) => {
    const storeIds = Array.isArray(ids) ? ids : [ids];
    // Check authorization for each store
    for (const storeId of storeIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "stores", storeId);
        // Only admins and superadmins can delete stores
        if (requester.role !== "admin" && requester.role !== "superadmin") {
            throw new Error("Unauthorized: Only admins and superadmins can delete stores.");
        }
        // Check if user is associated with the store (unless superadmin)
        if (requester.role !== "superadmin") {
            const userStoreAssociation = await db_1.postgresDb
                .select()
                .from(schema_1.userStores)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, requester.id), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
                .limit(1);
            if (userStoreAssociation.length === 0) {
                throw new Error(`Store with ID ${storeId} not found or unauthorized.`);
            }
        }
    }
    // Delete the stores
    const deletedStores = await db_1.postgresDb
        .delete(schema_1.stores)
        .where((0, drizzle_orm_1.inArray)(schema_1.stores.id, storeIds))
        .returning({ deletedId: schema_1.stores.id });
    if (!deletedStores || deletedStores.length === 0) {
        throw new Error("Delete failed: Stores not found.");
    }
    return {
        deletedIds: deletedStores.map(store => store.deletedId)
    };
};
exports.deleteStore = deleteStore;
