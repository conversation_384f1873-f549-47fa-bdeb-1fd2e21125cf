"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notifyUserPaymentStatus = notifyUserPaymentStatus;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const helmet_1 = __importDefault(require("helmet"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const compression_1 = __importDefault(require("compression"));
const route_1 = __importDefault(require("./src/routes/route"));
const cronJobs_1 = require("./src/cron/cronJobs");
const errorHandler_1 = require("./src/middleware/errorHandler");
const swagger_1 = require("./src/config/swagger");
const events_1 = __importDefault(require("events"));
const http_1 = __importDefault(require("http"));
const ws_1 = __importStar(require("ws"));
dotenv_1.default.config();
// Increase the maximum number of listeners to prevent memory leak warnings
events_1.default.defaultMaxListeners = 20;
// Initialize Express app
const app = (0, express_1.default)();
const port = process.env.PORT || 5005;
// Enable trust proxy for proper IP detection behind reverse proxies
app.set('trust proxy', 1);
// 🔥 Optimize JSON parsing with size limits to prevent large payload attacks
app.use(express_1.default.json({
    limit: '1mb',
    strict: true
}));
// 🔥 Optimize URL-encoded parsing
app.use(express_1.default.urlencoded({
    extended: true,
    limit: '1mb'
}));
// 🔥 Enhanced compression for better performance
app.use((0, compression_1.default)({
    level: 6, // Balanced compression level (0-9, where 9 is max compression but slower)
    threshold: 1024, // Only compress responses larger than 1KB
    filter: (req, res) => {
        // Don't compress responses with this header
        if (req.headers['x-no-compression']) {
            return false;
        }
        // Use compression filter function from the middleware
        return compression_1.default.filter(req, res);
    }
}));
// 🔥 Optimized rate limiter with higher limits for API endpoints
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // Increased limit for better performance under load
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    message: 'Too many requests, please try again later.',
    skip: (req) => {
        // Skip rate limiting for health check endpoint
        return req.path === '/health';
    }
});
// CORS configuration
const corsOptions = {
    origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        'https://nexapo.vercel.app'
    ],
    methods: ['GET', 'POST', 'OPTIONS'], // Added OPTIONS for preflight requests
    allowedHeaders: [
        'Authorization',
        'X-User-Role',
        'Content-Type',
        'Accept',
    ],
    credentials: true, // Allow credentials (cookies, authorization headers)
    maxAge: 86400, // Cache preflight response for 24 hours
};
// Apply middleware in optimal order (performance-sensitive first)
app.use((0, cors_1.default)(corsOptions));
app.options('*', (0, cors_1.default)(corsOptions)); // Handle OPTIONS preflight requests
app.use((0, cookie_parser_1.default)());
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin resource sharing
}));
app.use(limiter);
// API routes
app.use('/api/v1', route_1.default);
// Setup Swagger API documentation
(0, swagger_1.setupSwagger)(app);
// Health check endpoint for monitoring
app.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        message: 'Backend service is running'
    });
});
// Root endpoint
app.get('/', (_req, res) => {
    res.status(200).send('OK');
});
// 404 handler - must be before the error handler
app.use((req, res, _next) => {
    res.status(404).json({
        success: false,
        message: `Route not found: ${req.method} ${req.originalUrl}`
    });
});
// Global error handler - must be the last middleware
app.use(errorHandler_1.errorHandler);
// Process-level error handling
process.on('uncaughtException', (error) => {
    console.error('UNCAUGHT EXCEPTION:', error);
    // Log to monitoring service if available
});
process.on('unhandledRejection', (reason, _promise) => {
    console.error('UNHANDLED REJECTION:', reason);
    // Log to monitoring service if available
});
// Start cron jobs
(0, cronJobs_1.startCronJobs)();
// Start the server with error handling
const server = http_1.default.createServer(app);
server.listen(port, () => {
    console.log(`Server is running at https://nexapoapi.up.railway.app`);
});
// --- WebSocket Server Setup ---
const wss = new ws_1.WebSocketServer({ server });
const userSockets = new Map(); // userId -> ws
wss.on('connection', (ws, req) => {
    let authedUserId = null;
    ws.on('message', (msg) => {
        try {
            const data = JSON.parse(msg.toString());
            if (data.type === 'auth' && typeof data.userId === 'number') {
                authedUserId = data.userId;
                userSockets.set(data.userId, ws);
                console.log(`[WebSocket] User ${data.userId} authenticated and connected.`);
            }
        }
        catch (e) {
            console.warn('[WebSocket] Failed to parse message:', msg);
        }
    });
    ws.on('close', () => {
        if (typeof authedUserId === 'number') {
            const userIdNum = authedUserId;
            userSockets.delete(userIdNum);
            console.log(`[WebSocket] User ${userIdNum} disconnected.`);
        }
    });
});
function notifyUserPaymentStatus(userId, newStatus) {
    const ws = userSockets.get(userId);
    if (ws && ws.readyState === ws_1.default.OPEN) {
        ws.send(JSON.stringify({ type: 'paymentStatus', status: newStatus }));
        console.log(`[WebSocket] Sent paymentStatus '${newStatus}' to user ${userId}`);
    }
}
exports.default = app;
