import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define expense category types
export interface ExpenseCategory {
  id: number;
  name: string;
  description?: string;
  color: string;
  isDefault: boolean;
  createdBy?: number;
  createdAt: string;
}

export interface PaginatedExpenseCategories {
  categories: ExpenseCategory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateExpenseCategoryDto {
  name: string;
  description?: string;
  color?: string;
  isDefault?: boolean;
}

export interface UpdateExpenseCategoryDto {
  name?: string;
  description?: string;
  color?: string;
  isDefault?: boolean;
}

export const expenseCategoryApi = createApi({
  reducerPath: 'expenseCategoryApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['ExpenseCategory'] as const,
  endpoints: (builder) => ({
    // Get all expense categories (paginated)
    getAllExpenseCategories: builder.query<ApiResponse<PaginatedExpenseCategories>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 50, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expense-categories',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search,
          },
          token,
        };
      },
      keepUnusedDataFor: 300, // Keep categories in cache for 5 minutes
      providesTags: ['ExpenseCategory'],
    }),

    // Get expense category by ID
    getExpenseCategoryById: builder.query<ApiResponse<ExpenseCategory>, number>({
      query: (categoryId): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expense-categories',
          payloaddata: {
            mode: 'retrieve',
            categoryId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'ExpenseCategory', id }],
    }),

    // Create new expense category
    createExpenseCategory: builder.mutation<ApiResponse<ExpenseCategory>, CreateExpenseCategoryDto>({
      query: (categoryData): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expense-categories',
          payloaddata: {
            mode: 'createnew',
            ...categoryData,
          },
          token,
        };
      },
      invalidatesTags: ['ExpenseCategory'],
    }),

    // Update expense category
    updateExpenseCategory: builder.mutation<ApiResponse<ExpenseCategory>, { categoryId: number; categoryData: UpdateExpenseCategoryDto }>({
      query: ({ categoryId, categoryData }): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expense-categories',
          payloaddata: {
            mode: 'update',
            categoryId,
            ...categoryData,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { categoryId }) => [{ type: 'ExpenseCategory', id: categoryId }, 'ExpenseCategory'],
    }),

    // Delete expense category
    deleteExpenseCategory: builder.mutation<ApiResponse<void>, number>({
      query: (categoryId): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expense-categories',
          payloaddata: {
            mode: 'delete',
            categoryId,
          },
          token,
        };
      },
      invalidatesTags: ['ExpenseCategory'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetAllExpenseCategoriesQuery,
  useGetExpenseCategoryByIdQuery,
  useCreateExpenseCategoryMutation,
  useUpdateExpenseCategoryMutation,
  useDeleteExpenseCategoryMutation,
} = expenseCategoryApi;
