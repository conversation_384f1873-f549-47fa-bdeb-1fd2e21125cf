import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/reduxRTK/store/store';
import { indexedDBService } from '@/utils/indexedDB';
import { useGetUserDefaultStoreQuery } from '@/reduxRTK/services/userStoreApi';
import { useGetAllProductsQuery } from '@/reduxRTK/services/productApi';
import { useGetAllSalesQuery } from '@/reduxRTK/services/salesApi';

export const useOfflineData = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  const { data: storeData } = useGetUserDefaultStoreQuery(user?.id || 0, { skip: !user?.id });
  const { data: productsData } = useGetAllProductsQuery({}, { skip: !user?.id });
  const { data: salesData } = useGetAllSalesQuery({}, { skip: !user?.id });

  useEffect(() => {
    const cacheData = async () => {
      try {
        // Cache user data
        if (user) {
          await indexedDBService.cacheUserData(user);
        }

        // Cache store data
        if (storeData?.data) {
          await indexedDBService.cacheStoreData(storeData.data);
        }

        // Cache products
        if (productsData?.data?.products) {
          await indexedDBService.cacheProducts(productsData.data.products);
        }

        // Cache sales
        if (salesData?.data?.sales) {
          await indexedDBService.cacheSales(salesData.data.sales);
        }
      } catch (error) {
        console.error('Error caching data:', error);
      }
    };

    cacheData();
  }, [user, storeData, productsData, salesData]);

  const getOfflineData = async () => {
    try {
      const [cachedUser, cachedStore, cachedProducts, cachedSales] = await Promise.all([
        indexedDBService.getCachedUserData(),
        indexedDBService.getCachedStoreData(),
        indexedDBService.getCachedProducts(),
        indexedDBService.getCachedSales(),
      ]);

      return {
        user: cachedUser,
        store: cachedStore,
        products: cachedProducts,
        sales: cachedSales,
      };
    } catch (error) {
      console.error('Error retrieving offline data:', error);
      return {
        user: null,
        store: null,
        products: [],
        sales: [],
      };
    }
  };

  const clearOfflineData = async () => {
    try {
      await indexedDBService.clearCache();
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  };

  return {
    getOfflineData,
    clearOfflineData,
  };
}; 