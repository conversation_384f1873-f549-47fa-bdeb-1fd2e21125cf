"use client";

import React from "react";
import { Button, Spin, Descriptions } from "antd";
import {
  EditOutlined,
  ShopOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  IdcardOutlined,
  UserOutlined,
  CalendarOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { Store } from "@/types/store";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useGetStoreByIdQuery } from "@/reduxRTK/services/storeApi";
import dayjs from "dayjs";
import "./store-panels.css";

interface StoreDetailsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (store: Store) => void;
  storeId?: number;
}

const StoreDetailsPanel: React.FC<StoreDetailsPanelProps> = ({
  isOpen,
  onClose,
  onEdit,
  storeId,
}) => {
  // Fetch store details
  const { data: storeData, isLoading } = useGetStoreByIdQuery(storeId || 0, {
    skip: !storeId || !isOpen,
  });

  const store = storeData?.data;

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Panel footer with close button and edit button
  const panelFooter = store ? (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ background: "#f5f5f5", borderColor: "#d9d9d9" }}
      >
        Close
      </Button>
      <Button
        type="primary"
        icon={<EditOutlined />}
        onClick={() => onEdit(store)}
        className="border-blue-600 bg-blue-600 hover:bg-blue-700"
      >
        Edit
      </Button>
    </div>
  ) : (
    <div className="flex justify-end">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ background: "#f5f5f5", borderColor: "#d9d9d9" }}
      >
        Close
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      title="Store Details"
      isOpen={isOpen}
      onClose={onClose}
      width="500px"
      footer={panelFooter}
    >
      <div className="bg-white p-6s">
        {isLoading ? (
          <div className="flex h-full min-h-[300px] items-center justify-center">
            <Spin
              indicator={
                <LoadingOutlined
                  style={{ fontSize: 24, color: "#1890ff" }}
                  spin
                />
              }
            />
          </div>
        ) : store ? (
          <div className="mx-auto max-w-2xl">
            {/* Store detail heading with icon */}
            <div className="mb-6 border-b border-gray-200 pb-4">
              <h2 className="flex items-center text-xl font-bold text-gray-800">
                <ShopOutlined className="mr-2" />
                Store: {store.name}
              </h2>
              <p className="mt-1 flex items-center text-gray-600">
                Complete store information and details
              </p>
            </div>

            <Descriptions bordered column={1} className="store-detail-light">
              <Descriptions.Item
                label={
                  <span>
                    <ShopOutlined /> Store ID
                  </span>
                }
              >
                {store.id}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <ShopOutlined /> Name
                  </span>
                }
              >
                {store.name}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <EnvironmentOutlined /> Address
                  </span>
                }
              >
                {store.address || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <EnvironmentOutlined /> City
                  </span>
                }
              >
                {store.city || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <EnvironmentOutlined /> State/Province
                  </span>
                }
              >
                {store.state || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <EnvironmentOutlined /> Country
                  </span>
                }
              >
                {store.country || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <PhoneOutlined /> Phone
                  </span>
                }
              >
                {store.phone || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <MailOutlined /> Email
                  </span>
                }
              >
                {store.email || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <GlobalOutlined /> Website
                  </span>
                }
              >
                {store.website || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <IdcardOutlined /> Tax ID
                  </span>
                }
              >
                {store.taxId || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <UserOutlined /> Created By
                  </span>
                }
              >
                {store.createdByName || "N/A"}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <CalendarOutlined /> Created At
                  </span>
                }
              >
                {formatDate(store.createdAt)}
              </Descriptions.Item>

              <Descriptions.Item
                label={
                  <span>
                    <CalendarOutlined /> Last Updated
                  </span>
                }
              >
                {formatDate(store.updatedAt)}
              </Descriptions.Item>
            </Descriptions>
          </div>
        ) : (
          <div className="text-center text-gray-800">
            <p>Store not found or you don&quot;t have permission to view it.</p>
          </div>
        )}
      </div>
    </SlidingPanel>
  );
};

export default StoreDetailsPanel;
