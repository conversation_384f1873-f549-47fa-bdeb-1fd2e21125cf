(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},93380:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(70260),a=t(28203),n=t(25155),o=t.n(n),l=t(67292),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11189)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58406:(e,r,t)=>{Promise.resolve().then(t.bind(t,14752))},28142:(e,r,t)=>{Promise.resolve().then(t.bind(t,29490))},2961:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(11855),a=t(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var o=t(78480);let l=a.forwardRef(function(e,r){return a.createElement(o.A,(0,s.A)({},e,{ref:r,icon:n}))})},53180:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(11855),a=t(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var o=t(78480);let l=a.forwardRef(function(e,r){return a.createElement(o.A,(0,s.A)({},e,{ref:r,icon:n}))})},29490:(e,r,t)=>{"use strict";t.d(r,{default:()=>b});var s=t(45512),a=t(58009),n=t(45103),o=t(41257),l=t(37764),i=t(3117),d=t(53180),c=t(2961),u=t(88752),m=t(92273),p=t(42211),g=t(97245);let h=()=>{let e=(0,m.wA)(),[r,t]=(0,a.useState)({loading:!1,error:"",data:null}),[s]=(0,g.h8)();return{login:async(r,a)=>{t(e=>({...e,loading:!0,error:""}));try{let n=await s({email:r,password:a}).unwrap();if(n.success&&n.data?.user&&n.data?.accessToken){let{user:r,accessToken:s}=n.data;console.log("useLoginUser - User data from login response:",r),console.log("useLoginUser - User data (stringified):",JSON.stringify(r,null,2)),console.log("useLoginUser - Critical fields:",{phone:r.phone,phoneType:typeof r.phone,createdAt:r.createdAt,createdAtType:typeof r.createdAt,lastPaymentDate:r.lastPaymentDate,nextPaymentDue:r.nextPaymentDue});let a={...r,phone:r.phone||"",createdAt:r.createdAt||"",lastPaymentDate:r.lastPaymentDate||null,nextPaymentDue:r.nextPaymentDue||null,createdBy:r.createdBy||null};return console.log("useLoginUser - Updated user data:",a),e((0,p.gV)({user:a,accessToken:s})),t({loading:!1,error:"",data:{user:a,accessToken:s}}),{success:!0,message:n.message,data:{user:a,accessToken:s}}}{let e=n.message||"Login failed";return t({loading:!1,error:e,data:null}),{success:!1,message:e,data:null}}}catch(r){let e=r?.data?.message||r.message||"Something went wrong";return t({loading:!1,error:e,data:null}),{success:!1,message:e,data:null}}},...r}};var f=t(49792),x=t(79334);function y(){let[e,r]=(0,a.useState)(!1),{login:t}=h(),m=(0,x.useRouter)(),p=async e=>{r(!0);let s=await t(e.email,e.password);s?.success?(r(!1),(0,f.r)("success",s?.message||"Login successful"),m.replace("/dashboard")):(r(!1),s?.message==="getaddrinfo ENOTFOUND ep-icy-resonance-a5p24f0y-pooler.us-east-2.aws.neon.tech"?(0,f.r)("error","Error in network connection. Check your connection"):(0,f.r)("error",s?.message))};return(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"mb-8 flex justify-center",children:(0,s.jsx)(n.default,{src:"/images/logo.png",alt:"Logo",width:150,height:64,priority:!0})}),(0,s.jsx)("h2",{className:"text-gray-800 text-2xl font-bold mb-6 text-center",children:"Sign In"}),(0,s.jsxs)(o.A,{name:"signin",onFinish:p,layout:"vertical",className:"text-gray-800",validateMessages:{required:"${label} is required"},children:[(0,s.jsx)(o.A.Item,{name:"email",label:(0,s.jsx)("span",{className:"text-gray-700",children:"Email Address"}),rules:[{required:!0,message:"Please enter your email"}],children:(0,s.jsx)(l.A,{size:"large",prefix:(0,s.jsx)(d.A,{className:"text-gray-500"}),placeholder:"Email Address",className:"rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"})}),(0,s.jsx)(o.A.Item,{name:"password",label:(0,s.jsx)("span",{className:"text-gray-700",children:"Password"}),rules:[{required:!0,message:"Please enter your password"}],children:(0,s.jsx)(l.A.Password,{size:"large",prefix:(0,s.jsx)(c.A,{className:"text-gray-500"}),placeholder:"Password",className:"rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"})}),(0,s.jsx)(o.A.Item,{className:"mb-4",children:(0,s.jsx)(i.Ay,{type:"primary",htmlType:"submit",size:"large",className:"w-full rounded-md bg-blue-500 text-white hover:bg-blue-600 border border-blue-500",children:e?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(u.A,{style:{color:"white",marginRight:"8px"},spin:!0}),(0,s.jsx)("span",{children:"Logging in..."})]}):"Log in"})})]})]})}t(83897);let v=({children:e,redirectTo:r="/dashboard"})=>{let{user:t,accessToken:n}=(0,m.d4)(e=>e.auth),o=(0,x.useRouter)();return(0,a.useEffect)(()=>{if(t&&n){let e=sessionStorage.getItem("redirectUrl");e?(sessionStorage.removeItem("redirectUrl"),o.push(e)):o.push(r)}},[t,n,o,r]),(0,s.jsx)(s.Fragment,{children:e})},b=()=>(0,s.jsx)(v,{children:(0,s.jsx)("div",{className:"w-screen h-screen flex items-center justify-center bg-white px-4",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row w-full max-w-5xl h-auto md:h-[90vh] rounded-[20px] shadow-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center bg-white p-6 md:p-10 md:border-r border-gray-200",children:(0,s.jsx)(y,{})}),(0,s.jsx)("div",{className:"hidden md:flex w-full md:w-1/2 items-center justify-center bg-[#f7f9fc] p-10",children:(0,s.jsx)(n.default,{src:"/images/signin.webp",alt:"Login Illustration",width:800,height:800,className:"object-contain w-full h-full"})})]})})})},49792:(e,r,t)=>{"use strict";t.d(r,{r:()=>a});var s=t(22403);let a=(e,r)=>{"success"===e?s.oR.success(r):"error"===e?s.oR.error(r):"warning"===e&&(0,s.oR)(r,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)},11189:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>n});var s=t(62740),a=t(14752);let n={title:"Sign in | POS System",description:"Sign in to your POS System account",icons:{icon:"/images/logo/logo-small.png"}};function o(){return(0,s.jsx)(a.default,{})}},14752:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Auth\\\\LoginPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\LoginPage.tsx","default")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},83897:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,3391,4877,3999,1184,7764,1257,5482],()=>t(93380));module.exports=s})();