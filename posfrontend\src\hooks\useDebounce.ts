"use client";

import { useState, useEffect } from 'react';

/**
 * A hook that debounces a value
 * @param value The value to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Log the value being debounced for debugging
    console.log("Debouncing value:", value);

    // Set a timeout to update the debounced value after the specified delay
    const timer = setTimeout(() => {
      console.log("Debounce timer completed, setting value:", value);
      setDebouncedValue(value);
    }, delay);

    // Clean up the timeout if the value changes before the delay has passed
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}
