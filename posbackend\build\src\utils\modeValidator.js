"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateModeWithError = exports.validateMode = void 0;
const responseHelper_1 = require("./responseHelper");
const errorHandler_1 = require("../middleware/errorHandler");
/**
 * Validates the request mode.
 * @param res - Express Response object
 * @param mode - The mode from request body
 * @param validModes - Array of allowed modes
 * @returns {boolean} - Returns true if the mode is valid, otherwise sends a response and returns false.
 */
const validateMode = (res, mode, validModes) => {
    if (!validModes.includes(mode)) {
        // For backward compatibility, still use sendResponse directly
        // This allows existing code to work without changes
        (0, responseHelper_1.sendResponse)(res, 400, false, `Invalid mode. Allowed modes: ${validModes.join(", ")}.`);
        return false;
    }
    return true;
};
exports.validateMode = validateMode;
/**
 * Validates the request mode and throws a ValidationError if invalid.
 * This version is meant to be used with the asyncHandler and global error handler.
 *
 * @param mode - The mode from request body
 * @param validModes - Array of allowed modes
 * @throws {ValidationError} - If the mode is invalid
 */
const validateModeWithError = (mode, validModes) => {
    if (!validModes.includes(mode)) {
        throw new errorHandler_1.ValidationError(`Invalid mode. Allowed modes: ${validModes.join(", ")}.`);
    }
};
exports.validateModeWithError = validateModeWithError;
