"use client";

import { useDeleteProductMutation } from "@/reduxRTK/services/productApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useProductDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a product
  const [deleteProduct, { isLoading }] = useDeleteProductMutation();

  const deleteProductById = async (productId: number) => {
    try {
      const result = await deleteProduct(productId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete product");
      }

      showMessage("success", "Product deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete product error:", error);
      showMessage("error", error.message || "Failed to delete product");
      throw error;
    }
  };

  return {
    deleteProduct: deleteProductById,
    isDeleting: isLoading
  };
};
