// services/storeApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';
import { CreateStoreDto, PaginatedStores, Store, UpdateStoreDto } from '@/types/store';

export const storeApi = createApi({
  reducerPath: 'storeApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Store'] as const,
  endpoints: (builder) => ({
    // Get all stores (paginated)
    getAllStores: builder.query<ApiResponse<PaginatedStores>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Store'],
    }),

    // Get store by ID
    getStoreById: builder.query<ApiResponse<Store>, number>({
      query: (storeId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'retrieve',
            storeId,
          },
          token,
        };
      },
      providesTags: (result, error, id) => [{ type: 'Store', id }],
    }),

    // Create new store
    createStore: builder.mutation<ApiResponse<Store>, CreateStoreDto>({
      query: (storeData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'createnew',
            ...storeData,
          },
          token,
        };
      },
      invalidatesTags: ['Store'],
    }),

    // Update store
    updateStore: builder.mutation<ApiResponse<Store>, { storeId: number; updateData: UpdateStoreDto }>({
      query: ({ storeId, updateData }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'update',
            storeId,
            ...updateData,
          },
          token,
        };
      },
      invalidatesTags: (result, error, { storeId }) => [{ type: 'Store', id: storeId }],
    }),

    // Delete store
    deleteStore: builder.mutation<ApiResponse<{ deletedId: number }>, number>({
      query: (storeId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'delete',
            storeId,
          },
          token,
        };
      },
      invalidatesTags: ['Store'],
    }),

    // Bulk delete stores
    bulkDeleteStores: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (storeIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stores',
          payloaddata: {
            mode: 'delete',
            storeIds,
          },
          token,
        };
      },
      invalidatesTags: ['Store'],
    }),
  }),
});

export const {
  useGetAllStoresQuery,
  useGetStoreByIdQuery,
  useCreateStoreMutation,
  useUpdateStoreMutation,
  useDeleteStoreMutation,
  useBulkDeleteStoresMutation,
} = storeApi;
