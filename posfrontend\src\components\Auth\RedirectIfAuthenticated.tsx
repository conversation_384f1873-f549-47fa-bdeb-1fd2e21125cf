"use client";

import { useEffect, ReactNode } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter } from "next/navigation";

interface RedirectIfAuthenticatedProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * RedirectIfAuthenticated component to redirect authenticated users away from public pages
 * For example, redirect from login page to dashboard if already logged in
 */
const RedirectIfAuthenticated = ({
  children,
  redirectTo = "/dashboard",
}: RedirectIfAuthenticatedProps) => {
  const { user, accessToken } = useSelector((state: RootState) => state.auth);
  const router = useRouter();

  useEffect(() => {
    // If user is authenticated, redirect to the specified path
    if (user && accessToken) {
      // Check if there's a stored redirect URL from a previous attempt to access a protected page
      const storedRedirectUrl = sessionStorage.getItem("redirectUrl");
      if (storedRedirectUrl) {
        sessionStorage.removeItem("redirectUrl");
        router.push(storedRedirectUrl);
      } else {
        router.push(redirectTo);
      }
    }
  }, [user, accessToken, router, redirectTo]);

  // If user is not authenticated, render children (e.g., login form)
  return <>{children}</>;
};

export default RedirectIfAuthenticated;
