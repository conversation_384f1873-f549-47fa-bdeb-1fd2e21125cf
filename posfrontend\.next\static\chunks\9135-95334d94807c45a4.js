"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9135],{94974:(e,t,n)=>{n.d(t,{A:()=>g});var o=n(59912),r=n(12115),i=n(47650),a=n(30306);n(30754);var u=n(15231),c=r.createContext(null),s=n(39014),l=n(66105),f=[],d=n(12211),h=n(77001),p="rc-util-locker-".concat(Date.now()),v=0,m=function(e){return!1!==e&&((0,a.A)()&&e?"string"==typeof e?document.querySelector(e):"function"==typeof e?e():e:null)};let g=r.forwardRef(function(e,t){var n,g,b,y=e.open,w=e.autoLock,A=e.getContainer,E=(e.debug,e.autoDestroy),_=void 0===E||E,x=e.children,M=r.useState(y),k=(0,o.A)(M,2),C=k[0],P=k[1],R=C||y;r.useEffect(function(){(_||y)&&P(y)},[y,_]);var O=r.useState(function(){return m(A)}),S=(0,o.A)(O,2),D=S[0],N=S[1];r.useEffect(function(){var e=m(A);N(null!=e?e:null)});var L=function(e,t){var n=r.useState(function(){return(0,a.A)()?document.createElement("div"):null}),i=(0,o.A)(n,1)[0],u=r.useRef(!1),d=r.useContext(c),h=r.useState(f),p=(0,o.A)(h,2),v=p[0],m=p[1],g=d||(u.current?void 0:function(e){m(function(t){return[e].concat((0,s.A)(t))})});function b(){i.parentElement||document.body.appendChild(i),u.current=!0}function y(){var e;null===(e=i.parentElement)||void 0===e||e.removeChild(i),u.current=!1}return(0,l.A)(function(){return e?d?d(b):b():y(),y},[e]),(0,l.A)(function(){v.length&&(v.forEach(function(e){return e()}),m(f))},[v]),[i,g]}(R&&!D,0),z=(0,o.A)(L,2),T=z[0],H=z[1],W=null!=D?D:T;n=!!(w&&y&&(0,a.A)()&&(W===T||W===document.body)),g=r.useState(function(){return v+=1,"".concat(p,"_").concat(v)}),b=(0,o.A)(g,1)[0],(0,l.A)(function(){if(n){var e=(0,h.V)(document.body).width,t=document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth;(0,d.BD)("\nhtml body {\n  overflow-y: hidden;\n  ".concat(t?"width: calc(100% - ".concat(e,"px);"):"","\n}"),b)}else(0,d.m6)(b);return function(){(0,d.m6)(b)}},[n,b]);var j=null;x&&(0,u.f3)(x)&&t&&(j=x.ref);var B=(0,u.xK)(j,t);if(!R||!(0,a.A)()||void 0===D)return null;var V=!1===W,F=x;return t&&(F=r.cloneElement(x,{ref:B})),r.createElement(c.Provider,{value:H},V?F:(0,i.createPortal)(F,W))})},99121:(e,t,n)=>{n.d(t,{A:()=>V});var o=n(85268),r=n(59912),i=n(64406),a=n(94974),u=n(4617),c=n.n(u),s=n(30377),l=n(68264),f=n(46191),d=n(97262),h=n(51335),p=n(66105),v=n(8324),m=n(12115),g=n(85407),b=n(72261),y=n(15231);function w(e){var t=e.prefixCls,n=e.align,o=e.arrow,r=e.arrowPos,i=o||{},a=i.className,u=i.content,s=r.x,l=r.y,f=m.useRef();if(!n||!n.points)return null;var d={position:"absolute"};if(!1!==n.autoArrow){var h=n.points[0],p=n.points[1],v=h[0],g=h[1],b=p[0],y=p[1];v!==b&&["t","b"].includes(v)?"t"===v?d.top=0:d.bottom=0:d.top=void 0===l?0:l,g!==y&&["l","r"].includes(g)?"l"===g?d.left=0:d.right=0:d.left=void 0===s?0:s}return m.createElement("div",{ref:f,className:c()("".concat(t,"-arrow"),a),style:d},u)}function A(e){var t=e.prefixCls,n=e.open,o=e.zIndex,r=e.mask,i=e.motion;return r?m.createElement(b.Ay,(0,g.A)({},i,{motionAppear:!0,visible:n,removeOnLeave:!0}),function(e){var n=e.className;return m.createElement("div",{style:{zIndex:o},className:c()("".concat(t,"-mask"),n)})}):null}var E=m.memo(function(e){return e.children},function(e,t){return t.cache}),_=m.forwardRef(function(e,t){var n=e.popup,i=e.className,a=e.prefixCls,u=e.style,l=e.target,f=e.onVisibleChanged,d=e.open,h=e.keepDom,v=e.fresh,_=e.onClick,x=e.mask,M=e.arrow,k=e.arrowPos,C=e.align,P=e.motion,R=e.maskMotion,O=e.forceRender,S=e.getPopupContainer,D=e.autoDestroy,N=e.portal,L=e.zIndex,z=e.onMouseEnter,T=e.onMouseLeave,H=e.onPointerEnter,W=e.onPointerDownCapture,j=e.ready,B=e.offsetX,V=e.offsetY,F=e.offsetR,I=e.offsetB,X=e.onAlign,Y=e.onPrepare,q=e.stretch,G=e.targetWidth,K=e.targetHeight,J="function"==typeof n?n():n,$=d||h,Q=(null==S?void 0:S.length)>0,U=m.useState(!S||!Q),Z=(0,r.A)(U,2),ee=Z[0],et=Z[1];if((0,p.A)(function(){!ee&&Q&&l&&et(!0)},[ee,Q,l]),!ee)return null;var en="auto",eo={left:"-1000vw",top:"-1000vh",right:en,bottom:en};if(j||!d){var er,ei=C.points,ea=C.dynamicInset||(null===(er=C._experimental)||void 0===er?void 0:er.dynamicInset),eu=ea&&"r"===ei[0][1],ec=ea&&"b"===ei[0][0];eu?(eo.right=F,eo.left=en):(eo.left=B,eo.right=en),ec?(eo.bottom=I,eo.top=en):(eo.top=V,eo.bottom=en)}var es={};return q&&(q.includes("height")&&K?es.height=K:q.includes("minHeight")&&K&&(es.minHeight=K),q.includes("width")&&G?es.width=G:q.includes("minWidth")&&G&&(es.minWidth=G)),d||(es.pointerEvents="none"),m.createElement(N,{open:O||$,getContainer:S&&function(){return S(l)},autoDestroy:D},m.createElement(A,{prefixCls:a,open:d,zIndex:L,mask:x,motion:R}),m.createElement(s.A,{onResize:X,disabled:!d},function(e){return m.createElement(b.Ay,(0,g.A)({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:O,leavedClassName:"".concat(a,"-hidden")},P,{onAppearPrepare:Y,onEnterPrepare:Y,visible:d,onVisibleChanged:function(e){var t;null==P||null===(t=P.onVisibleChanged)||void 0===t||t.call(P,e),f(e)}}),function(n,r){var s=n.className,l=n.style,f=c()(a,s,i);return m.createElement("div",{ref:(0,y.K4)(e,t,r),className:f,style:(0,o.A)((0,o.A)((0,o.A)((0,o.A)({"--arrow-x":"".concat(k.x||0,"px"),"--arrow-y":"".concat(k.y||0,"px")},eo),es),l),{},{boxSizing:"border-box",zIndex:L},u),onMouseEnter:z,onMouseLeave:T,onPointerEnter:H,onClick:_,onPointerDownCapture:W},M&&m.createElement(w,{prefixCls:a,arrow:M,arrowPos:k,align:C}),m.createElement(E,{cache:!d&&!v},J))})}))}),x=m.forwardRef(function(e,t){var n=e.children,o=e.getTriggerDOMNode,r=(0,y.f3)(n),i=m.useCallback(function(e){(0,y.Xf)(t,o?o(e):e)},[o]),a=(0,y.xK)(i,(0,y.A9)(n));return r?m.cloneElement(n,{ref:a}):n}),M=m.createContext(null);function k(e){return e?Array.isArray(e)?e:[e]:[]}var C=n(87543);function P(e,t,n,o){return t||(n?{motionName:"".concat(e,"-").concat(n)}:o?{motionName:o}:null)}function R(e){return e.ownerDocument.defaultView}function O(e){for(var t=[],n=null==e?void 0:e.parentElement,o=["hidden","scroll","clip","auto"];n;){var r=R(n).getComputedStyle(n);[r.overflowX,r.overflowY,r.overflow].some(function(e){return o.includes(e)})&&t.push(n),n=n.parentElement}return t}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function D(e){return S(parseFloat(e),0)}function N(e,t){var n=(0,o.A)({},e);return(t||[]).forEach(function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=R(e).getComputedStyle(e),o=t.overflow,r=t.overflowClipMargin,i=t.borderTopWidth,a=t.borderBottomWidth,u=t.borderLeftWidth,c=t.borderRightWidth,s=e.getBoundingClientRect(),l=e.offsetHeight,f=e.clientHeight,d=e.offsetWidth,h=e.clientWidth,p=D(i),v=D(a),m=D(u),g=D(c),b=S(Math.round(s.width/d*1e3)/1e3),y=S(Math.round(s.height/l*1e3)/1e3),w=p*y,A=m*b,E=0,_=0;if("clip"===o){var x=D(r);E=x*b,_=x*y}var M=s.x+A-E,k=s.y+w-_,C=M+s.width+2*E-A-g*b-(d-h-m-g)*b,P=k+s.height+2*_-w-v*y-(l-f-p-v)*y;n.left=Math.max(n.left,M),n.top=Math.max(n.top,k),n.right=Math.min(n.right,C),n.bottom=Math.min(n.bottom,P)}}),n}function L(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="".concat(t),o=n.match(/^(.*)\%$/);return o?e*(parseFloat(o[1])/100):parseFloat(n)}function z(e,t){var n=(0,r.A)(t||[],2),o=n[0],i=n[1];return[L(e.width,o),L(e.height,i)]}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function H(e,t){var n,o=t[0],r=t[1];return n="t"===o?e.y:"b"===o?e.y+e.height:e.y+e.height/2,{x:"l"===r?e.x:"r"===r?e.x+e.width:e.x+e.width/2,y:n}}function W(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map(function(e,o){return o===t?n[e]||"c":e}).join("")}var j=n(39014);n(30754);var B=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];let V=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.A;return m.forwardRef(function(t,n){var a,u,g,b,y,w,A,E,D,L,V,F,I,X,Y,q,G,K=t.prefixCls,J=void 0===K?"rc-trigger-popup":K,$=t.children,Q=t.action,U=t.showAction,Z=t.hideAction,ee=t.popupVisible,et=t.defaultPopupVisible,en=t.onPopupVisibleChange,eo=t.afterPopupVisibleChange,er=t.mouseEnterDelay,ei=t.mouseLeaveDelay,ea=void 0===ei?.1:ei,eu=t.focusDelay,ec=t.blurDelay,es=t.mask,el=t.maskClosable,ef=t.getPopupContainer,ed=t.forceRender,eh=t.autoDestroy,ep=t.destroyPopupOnHide,ev=t.popup,em=t.popupClassName,eg=t.popupStyle,eb=t.popupPlacement,ey=t.builtinPlacements,ew=void 0===ey?{}:ey,eA=t.popupAlign,eE=t.zIndex,e_=t.stretch,ex=t.getPopupClassNameFromAlign,eM=t.fresh,ek=t.alignPoint,eC=t.onPopupClick,eP=t.onPopupAlign,eR=t.arrow,eO=t.popupMotion,eS=t.maskMotion,eD=t.popupTransitionName,eN=t.popupAnimation,eL=t.maskTransitionName,ez=t.maskAnimation,eT=t.className,eH=t.getTriggerDOMNode,eW=(0,i.A)(t,B),ej=m.useState(!1),eB=(0,r.A)(ej,2),eV=eB[0],eF=eB[1];(0,p.A)(function(){eF((0,v.A)())},[]);var eI=m.useRef({}),eX=m.useContext(M),eY=m.useMemo(function(){return{registerSubPopup:function(e,t){eI.current[e]=t,null==eX||eX.registerSubPopup(e,t)}}},[eX]),eq=(0,h.A)(),eG=m.useState(null),eK=(0,r.A)(eG,2),eJ=eK[0],e$=eK[1],eQ=m.useRef(null),eU=(0,d.A)(function(e){eQ.current=e,(0,l.fk)(e)&&eJ!==e&&e$(e),null==eX||eX.registerSubPopup(eq,e)}),eZ=m.useState(null),e0=(0,r.A)(eZ,2),e1=e0[0],e2=e0[1],e5=m.useRef(null),e3=(0,d.A)(function(e){(0,l.fk)(e)&&e1!==e&&(e2(e),e5.current=e)}),e6=m.Children.only($),e4=(null==e6?void 0:e6.props)||{},e7={},e8=(0,d.A)(function(e){var t,n;return(null==e1?void 0:e1.contains(e))||(null===(t=(0,f.j)(e1))||void 0===t?void 0:t.host)===e||e===e1||(null==eJ?void 0:eJ.contains(e))||(null===(n=(0,f.j)(eJ))||void 0===n?void 0:n.host)===e||e===eJ||Object.values(eI.current).some(function(t){return(null==t?void 0:t.contains(e))||e===t})}),e9=P(J,eO,eN,eD),te=P(J,eS,ez,eL),tt=m.useState(et||!1),tn=(0,r.A)(tt,2),to=tn[0],tr=tn[1],ti=null!=ee?ee:to,ta=(0,d.A)(function(e){void 0===ee&&tr(e)});(0,p.A)(function(){tr(ee||!1)},[ee]);var tu=m.useRef(ti);tu.current=ti;var tc=m.useRef([]);tc.current=[];var ts=(0,d.A)(function(e){var t;ta(e),(null!==(t=tc.current[tc.current.length-1])&&void 0!==t?t:ti)!==e&&(tc.current.push(e),null==en||en(e))}),tl=m.useRef(),tf=function(){clearTimeout(tl.current)},td=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;tf(),0===t?ts(e):tl.current=setTimeout(function(){ts(e)},1e3*t)};m.useEffect(function(){return tf},[]);var th=m.useState(!1),tp=(0,r.A)(th,2),tv=tp[0],tm=tp[1];(0,p.A)(function(e){(!e||ti)&&tm(!0)},[ti]);var tg=m.useState(null),tb=(0,r.A)(tg,2),ty=tb[0],tw=tb[1],tA=m.useState(null),tE=(0,r.A)(tA,2),t_=tE[0],tx=tE[1],tM=function(e){tx([e.clientX,e.clientY])},tk=(a=ek&&null!==t_?t_:e1,u=m.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:ew[eb]||{}}),b=(g=(0,r.A)(u,2))[0],y=g[1],w=m.useRef(0),A=m.useMemo(function(){return eJ?O(eJ):[]},[eJ]),E=m.useRef({}),ti||(E.current={}),D=(0,d.A)(function(){if(eJ&&a&&ti){var e=eJ.ownerDocument,t=R(eJ).getComputedStyle(eJ),n=t.width,i=t.height,u=t.position,c=eJ.style.left,s=eJ.style.top,f=eJ.style.right,d=eJ.style.bottom,h=eJ.style.overflow,p=(0,o.A)((0,o.A)({},ew[eb]),eA),v=e.createElement("div");if(null===(_=eJ.parentElement)||void 0===_||_.appendChild(v),v.style.left="".concat(eJ.offsetLeft,"px"),v.style.top="".concat(eJ.offsetTop,"px"),v.style.position=u,v.style.height="".concat(eJ.offsetHeight,"px"),v.style.width="".concat(eJ.offsetWidth,"px"),eJ.style.left="0",eJ.style.top="0",eJ.style.right="auto",eJ.style.bottom="auto",eJ.style.overflow="hidden",Array.isArray(a))P={x:a[0],y:a[1],width:0,height:0};else{var m,g,b,w,_,x,M,k,P,O,D,L=a.getBoundingClientRect();L.x=null!==(O=L.x)&&void 0!==O?O:L.left,L.y=null!==(D=L.y)&&void 0!==D?D:L.top,P={x:L.x,y:L.y,width:L.width,height:L.height}}var j=eJ.getBoundingClientRect();j.x=null!==(x=j.x)&&void 0!==x?x:j.left,j.y=null!==(M=j.y)&&void 0!==M?M:j.top;var B=e.documentElement,V=B.clientWidth,F=B.clientHeight,I=B.scrollWidth,X=B.scrollHeight,Y=B.scrollTop,q=B.scrollLeft,G=j.height,K=j.width,J=P.height,$=P.width,Q=p.htmlRegion,U="visible",Z="visibleFirst";"scroll"!==Q&&Q!==Z&&(Q=U);var ee=Q===Z,et=N({left:-q,top:-Y,right:I-q,bottom:X-Y},A),en=N({left:0,top:0,right:V,bottom:F},A),eo=Q===U?en:et,er=ee?en:eo;eJ.style.left="auto",eJ.style.top="auto",eJ.style.right="0",eJ.style.bottom="0";var ei=eJ.getBoundingClientRect();eJ.style.left=c,eJ.style.top=s,eJ.style.right=f,eJ.style.bottom=d,eJ.style.overflow=h,null===(k=eJ.parentElement)||void 0===k||k.removeChild(v);var ea=S(Math.round(K/parseFloat(n)*1e3)/1e3),eu=S(Math.round(G/parseFloat(i)*1e3)/1e3);if(!(0===ea||0===eu||(0,l.fk)(a)&&!(0,C.A)(a))){var ec=p.offset,es=p.targetOffset,el=z(j,ec),ef=(0,r.A)(el,2),ed=ef[0],eh=ef[1],ep=z(P,es),ev=(0,r.A)(ep,2),em=ev[0],eg=ev[1];P.x-=em,P.y-=eg;var ey=p.points||[],eE=(0,r.A)(ey,2),e_=eE[0],ex=T(eE[1]),eM=T(e_),ek=H(P,ex),eC=H(j,eM),eR=(0,o.A)({},p),eO=ek.x-eC.x+ed,eS=ek.y-eC.y+eh,eD=tl(eO,eS),eN=tl(eO,eS,en),eL=H(P,["t","l"]),ez=H(j,["t","l"]),eT=H(P,["b","r"]),eH=H(j,["b","r"]),eW=p.overflow||{},ej=eW.adjustX,eB=eW.adjustY,eV=eW.shiftX,eF=eW.shiftY,eI=function(e){return"boolean"==typeof e?e:e>=0};tf();var eX=eI(eB),eY=eM[0]===ex[0];if(eX&&"t"===eM[0]&&(g>er.bottom||E.current.bt)){var eq=eS;eY?eq-=G-J:eq=eL.y-eH.y-eh;var eG=tl(eO,eq),eK=tl(eO,eq,en);eG>eD||eG===eD&&(!ee||eK>=eN)?(E.current.bt=!0,eS=eq,eh=-eh,eR.points=[W(eM,0),W(ex,0)]):E.current.bt=!1}if(eX&&"b"===eM[0]&&(m<er.top||E.current.tb)){var e$=eS;eY?e$+=G-J:e$=eT.y-ez.y-eh;var eQ=tl(eO,e$),eU=tl(eO,e$,en);eQ>eD||eQ===eD&&(!ee||eU>=eN)?(E.current.tb=!0,eS=e$,eh=-eh,eR.points=[W(eM,0),W(ex,0)]):E.current.tb=!1}var eZ=eI(ej),e0=eM[1]===ex[1];if(eZ&&"l"===eM[1]&&(w>er.right||E.current.rl)){var e1=eO;e0?e1-=K-$:e1=eL.x-eH.x-ed;var e2=tl(e1,eS),e5=tl(e1,eS,en);e2>eD||e2===eD&&(!ee||e5>=eN)?(E.current.rl=!0,eO=e1,ed=-ed,eR.points=[W(eM,1),W(ex,1)]):E.current.rl=!1}if(eZ&&"r"===eM[1]&&(b<er.left||E.current.lr)){var e3=eO;e0?e3+=K-$:e3=eT.x-ez.x-ed;var e6=tl(e3,eS),e4=tl(e3,eS,en);e6>eD||e6===eD&&(!ee||e4>=eN)?(E.current.lr=!0,eO=e3,ed=-ed,eR.points=[W(eM,1),W(ex,1)]):E.current.lr=!1}tf();var e7=!0===eV?0:eV;"number"==typeof e7&&(b<en.left&&(eO-=b-en.left-ed,P.x+$<en.left+e7&&(eO+=P.x-en.left+$-e7)),w>en.right&&(eO-=w-en.right-ed,P.x>en.right-e7&&(eO+=P.x-en.right+e7)));var e8=!0===eF?0:eF;"number"==typeof e8&&(m<en.top&&(eS-=m-en.top-eh,P.y+J<en.top+e8&&(eS+=P.y-en.top+J-e8)),g>en.bottom&&(eS-=g-en.bottom-eh,P.y>en.bottom-e8&&(eS+=P.y-en.bottom+e8)));var e9=j.x+eO,te=j.y+eS,tt=P.x,tn=P.y,to=Math.max(e9,tt),tr=Math.min(e9+K,tt+$),ta=Math.max(te,tn),tu=Math.min(te+G,tn+J);null==eP||eP(eJ,eR);var tc=ei.right-j.x-(eO+j.width),ts=ei.bottom-j.y-(eS+j.height);1===ea&&(eO=Math.round(eO),tc=Math.round(tc)),1===eu&&(eS=Math.round(eS),ts=Math.round(ts)),y({ready:!0,offsetX:eO/ea,offsetY:eS/eu,offsetR:tc/ea,offsetB:ts/eu,arrowX:((to+tr)/2-e9)/ea,arrowY:((ta+tu)/2-te)/eu,scaleX:ea,scaleY:eu,align:eR})}function tl(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:eo,o=j.x+e,r=j.y+t,i=Math.max(o,n.left),a=Math.max(r,n.top);return Math.max(0,(Math.min(o+K,n.right)-i)*(Math.min(r+G,n.bottom)-a))}function tf(){g=(m=j.y+eS)+G,w=(b=j.x+eO)+K}}}),L=function(){y(function(e){return(0,o.A)((0,o.A)({},e),{},{ready:!1})})},(0,p.A)(L,[eb]),(0,p.A)(function(){ti||L()},[ti]),[b.ready,b.offsetX,b.offsetY,b.offsetR,b.offsetB,b.arrowX,b.arrowY,b.scaleX,b.scaleY,b.align,function(){w.current+=1;var e=w.current;Promise.resolve().then(function(){w.current===e&&D()})}]),tC=(0,r.A)(tk,11),tP=tC[0],tR=tC[1],tO=tC[2],tS=tC[3],tD=tC[4],tN=tC[5],tL=tC[6],tz=tC[7],tT=tC[8],tH=tC[9],tW=tC[10],tj=(V=void 0===Q?"hover":Q,m.useMemo(function(){var e=k(null!=U?U:V),t=k(null!=Z?Z:V),n=new Set(e),o=new Set(t);return eV&&(n.has("hover")&&(n.delete("hover"),n.add("click")),o.has("hover")&&(o.delete("hover"),o.add("click"))),[n,o]},[eV,V,U,Z])),tB=(0,r.A)(tj,2),tV=tB[0],tF=tB[1],tI=tV.has("click"),tX=tF.has("click")||tF.has("contextMenu"),tY=(0,d.A)(function(){tv||tW()});F=function(){tu.current&&ek&&tX&&td(!1)},(0,p.A)(function(){if(ti&&e1&&eJ){var e=O(e1),t=O(eJ),n=R(eJ),o=new Set([n].concat((0,j.A)(e),(0,j.A)(t)));function r(){tY(),F()}return o.forEach(function(e){e.addEventListener("scroll",r,{passive:!0})}),n.addEventListener("resize",r,{passive:!0}),tY(),function(){o.forEach(function(e){e.removeEventListener("scroll",r),n.removeEventListener("resize",r)})}}},[ti,e1,eJ]),(0,p.A)(function(){tY()},[t_,eb]),(0,p.A)(function(){ti&&!(null!=ew&&ew[eb])&&tY()},[JSON.stringify(eA)]);var tq=m.useMemo(function(){var e=function(e,t,n,o){for(var r=n.points,i=Object.keys(e),a=0;a<i.length;a+=1){var u,c=i[a];if(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0;return n?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}(null===(u=e[c])||void 0===u?void 0:u.points,r,o))return"".concat(t,"-placement-").concat(c)}return""}(ew,J,tH,ek);return c()(e,null==ex?void 0:ex(tH))},[tH,ex,ew,J,ek]);m.useImperativeHandle(n,function(){return{nativeElement:e5.current,popupElement:eQ.current,forceAlign:tY}});var tG=m.useState(0),tK=(0,r.A)(tG,2),tJ=tK[0],t$=tK[1],tQ=m.useState(0),tU=(0,r.A)(tQ,2),tZ=tU[0],t0=tU[1],t1=function(){if(e_&&e1){var e=e1.getBoundingClientRect();t$(e.width),t0(e.height)}};function t2(e,t,n,o){e7[e]=function(r){var i;null==o||o(r),td(t,n);for(var a=arguments.length,u=Array(a>1?a-1:0),c=1;c<a;c++)u[c-1]=arguments[c];null===(i=e4[e])||void 0===i||i.call.apply(i,[e4,r].concat(u))}}(0,p.A)(function(){ty&&(tW(),ty(),tw(null))},[ty]),(tI||tX)&&(e7.onClick=function(e){var t;tu.current&&tX?td(!1):!tu.current&&tI&&(tM(e),td(!0));for(var n=arguments.length,o=Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null===(t=e4.onClick)||void 0===t||t.call.apply(t,[e4,e].concat(o))});var t5=(I=void 0===el||el,(X=m.useRef(ti)).current=ti,Y=m.useRef(!1),m.useEffect(function(){if(tX&&eJ&&(!es||I)){var e=function(){Y.current=!1},t=function(e){var t;!X.current||e8((null===(t=e.composedPath)||void 0===t||null===(t=t.call(e))||void 0===t?void 0:t[0])||e.target)||Y.current||td(!1)},n=R(eJ);n.addEventListener("pointerdown",e,!0),n.addEventListener("mousedown",t,!0),n.addEventListener("contextmenu",t,!0);var o=(0,f.j)(e1);return o&&(o.addEventListener("mousedown",t,!0),o.addEventListener("contextmenu",t,!0)),function(){n.removeEventListener("pointerdown",e,!0),n.removeEventListener("mousedown",t,!0),n.removeEventListener("contextmenu",t,!0),o&&(o.removeEventListener("mousedown",t,!0),o.removeEventListener("contextmenu",t,!0))}}},[tX,e1,eJ,es,I]),function(){Y.current=!0}),t3=tV.has("hover"),t6=tF.has("hover");t3&&(t2("onMouseEnter",!0,er,function(e){tM(e)}),t2("onPointerEnter",!0,er,function(e){tM(e)}),q=function(e){(ti||tv)&&null!=eJ&&eJ.contains(e.target)&&td(!0,er)},ek&&(e7.onMouseMove=function(e){var t;null===(t=e4.onMouseMove)||void 0===t||t.call(e4,e)})),t6&&(t2("onMouseLeave",!1,ea),t2("onPointerLeave",!1,ea),G=function(){td(!1,ea)}),tV.has("focus")&&t2("onFocus",!0,eu),tF.has("focus")&&t2("onBlur",!1,ec),tV.has("contextMenu")&&(e7.onContextMenu=function(e){var t;tu.current&&tF.has("contextMenu")?td(!1):(tM(e),td(!0)),e.preventDefault();for(var n=arguments.length,o=Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null===(t=e4.onContextMenu)||void 0===t||t.call.apply(t,[e4,e].concat(o))}),eT&&(e7.className=c()(e4.className,eT));var t4=(0,o.A)((0,o.A)({},e4),e7),t7={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach(function(e){eW[e]&&(t7[e]=function(){for(var t,n=arguments.length,o=Array(n),r=0;r<n;r++)o[r]=arguments[r];null===(t=t4[e])||void 0===t||t.call.apply(t,[t4].concat(o)),eW[e].apply(eW,o)})});var t8=m.cloneElement(e6,(0,o.A)((0,o.A)({},t4),t7)),t9=eR?(0,o.A)({},!0!==eR?eR:{}):null;return m.createElement(m.Fragment,null,m.createElement(s.A,{disabled:!ti,ref:e3,onResize:function(){t1(),tY()}},m.createElement(x,{getTriggerDOMNode:eH},t8)),m.createElement(M.Provider,{value:eY},m.createElement(_,{portal:e,ref:eU,prefixCls:J,popup:ev,className:c()(em,tq),style:eg,target:e1,onMouseEnter:q,onMouseLeave:G,onPointerEnter:q,zIndex:eE,open:ti,keepDom:tv,fresh:eM,onClick:eC,onPointerDownCapture:t5,mask:es,motion:e9,maskMotion:te,onVisibleChanged:function(e){tm(!1),tW(),null==eo||eo(e)},onPrepare:function(){return new Promise(function(e){t1(),tw(function(){return e})})},forceRender:ed,autoDestroy:eh||ep||!1,getPopupContainer:ef,align:tH,arrow:t9,arrowPos:{x:tN,y:tL},ready:tP,offsetX:tR,offsetY:tO,offsetR:tS,offsetB:tD,onAlign:tY,stretch:e_,targetWidth:tJ/tz,targetHeight:tZ/tT})))})}(a.A)},7926:(e,t,n)=>{n.d(t,{A:()=>r});var o=n(68711);let r=e=>{let[,,,,t]=(0,o.Ay)();return t?"".concat(e,"-css-var"):""}},49698:(e,t,n)=>{n.d(t,{b:()=>i});let o=e=>({animationDuration:e,animationFillMode:"both"}),r=e=>({animationDuration:e,animationFillMode:"both"}),i=function(e,t,n,i){let a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],u=a?"&":"";return{["\n      ".concat(u).concat(e,"-enter,\n      ").concat(u).concat(e,"-appear\n    ")]:Object.assign(Object.assign({},o(i)),{animationPlayState:"paused"}),["".concat(u).concat(e,"-leave")]:Object.assign(Object.assign({},r(i)),{animationPlayState:"paused"}),["\n      ".concat(u).concat(e,"-enter").concat(e,"-enter-active,\n      ").concat(u).concat(e,"-appear").concat(e,"-appear-active\n    ")]:{animationName:t,animationPlayState:"running"},["".concat(u).concat(e,"-leave").concat(e,"-leave-active")]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}}},30377:(e,t,n)=>{n.d(t,{A:()=>B});var o=n(85407),r=n(12115),i=n(63588);n(30754);var a=n(85268),u=n(21855),c=n(68264),s=n(15231),l=r.createContext(null),f=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some(function(e,o){return e[0]===t&&(n=o,!0)}),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),o=this.__entries__[n];return o&&o[1]},t.prototype.set=function(t,n){var o=e(this.__entries__,t);~o?this.__entries__[o][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,o=e(n,t);~o&&n.splice(o,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,o=this.__entries__;n<o.length;n++){var r=o[n];e.call(t,r[1],r[0])}},t}()}(),d="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,h=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),p="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(h):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)},v=["top","right","bottom","left","width","height","size","weight"],m="undefined"!=typeof MutationObserver,g=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,o=!1,r=0;function i(){n&&(n=!1,e()),o&&u()}function a(){p(i)}function u(){var e=Date.now();if(n){if(e-r<2)return;o=!0}else n=!0,o=!1,setTimeout(a,20);r=e}return u}(this.refresh.bind(this),0)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){d&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),m?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){d&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;v.some(function(e){return!!~n.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),b=function(e,t){for(var n=0,o=Object.keys(t);n<o.length;n++){var r=o[n];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e},y=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||h},w=x(0,0,0,0);function A(e){return parseFloat(e)||0}function E(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return t+A(e["border-"+n+"-width"])},0)}var _="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof y(e).SVGGraphicsElement}:function(e){return e instanceof y(e).SVGElement&&"function"==typeof e.getBBox};function x(e,t,n,o){return{x:e,y:t,width:n,height:o}}var M=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=x(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){if(!d)return w;if(_(e)){var t;return x(0,0,(t=e.getBBox()).width,t.height)}return function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return w;var o=y(e).getComputedStyle(e),r=function(e){for(var t={},n=0,o=["top","right","bottom","left"];n<o.length;n++){var r=o[n],i=e["padding-"+r];t[r]=A(i)}return t}(o),i=r.left+r.right,a=r.top+r.bottom,u=A(o.width),c=A(o.height);if("border-box"===o.boxSizing&&(Math.round(u+i)!==t&&(u-=E(o,"left","right")+i),Math.round(c+a)!==n&&(c-=E(o,"top","bottom")+a)),e!==y(e).document.documentElement){var s=Math.round(u+i)-t,l=Math.round(c+a)-n;1!==Math.abs(s)&&(u-=s),1!==Math.abs(l)&&(c-=l)}return x(r.left,r.top,u,c)}(e)}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),k=function(e,t){var n,o,r,i,a,u=(n=t.x,o=t.y,r=t.width,i=t.height,b(a=Object.create(("undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object).prototype),{x:n,y:o,width:r,height:i,top:o,right:n+r,bottom:i+o,left:n}),a);b(this,{target:e,contentRect:u})},C=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new f,"function"!=typeof e)throw TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof y(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new M(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof y(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new k(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),P="undefined"!=typeof WeakMap?new WeakMap:new f,R=function e(t){if(!(this instanceof e))throw TypeError("Cannot call a class as a function.");if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");var n=new C(t,g.getInstance(),this);P.set(this,n)};["observe","unobserve","disconnect"].forEach(function(e){R.prototype[e]=function(){var t;return(t=P.get(this))[e].apply(t,arguments)}});var O=void 0!==h.ResizeObserver?h.ResizeObserver:R,S=new Map,D=new O(function(e){e.forEach(function(e){var t,n=e.target;null===(t=S.get(n))||void 0===t||t.forEach(function(e){return e(n)})})}),N=n(25514),L=n(98566),z=n(52106),T=n(61361),H=function(e){(0,z.A)(n,e);var t=(0,T.A)(n);function n(){return(0,N.A)(this,n),t.apply(this,arguments)}return(0,L.A)(n,[{key:"render",value:function(){return this.props.children}}]),n}(r.Component),W=r.forwardRef(function(e,t){var n=e.children,o=e.disabled,i=r.useRef(null),f=r.useRef(null),d=r.useContext(l),h="function"==typeof n,p=h?n(i):n,v=r.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),m=!h&&r.isValidElement(p)&&(0,s.f3)(p),g=m?(0,s.A9)(p):null,b=(0,s.xK)(g,i),y=function(){var e;return(0,c.Ay)(i.current)||(i.current&&"object"===(0,u.A)(i.current)?(0,c.Ay)(null===(e=i.current)||void 0===e?void 0:e.nativeElement):null)||(0,c.Ay)(f.current)};r.useImperativeHandle(t,function(){return y()});var w=r.useRef(e);w.current=e;var A=r.useCallback(function(e){var t=w.current,n=t.onResize,o=t.data,r=e.getBoundingClientRect(),i=r.width,u=r.height,c=e.offsetWidth,s=e.offsetHeight,l=Math.floor(i),f=Math.floor(u);if(v.current.width!==l||v.current.height!==f||v.current.offsetWidth!==c||v.current.offsetHeight!==s){var h={width:l,height:f,offsetWidth:c,offsetHeight:s};v.current=h;var p=c===Math.round(i)?i:c,m=s===Math.round(u)?u:s,g=(0,a.A)((0,a.A)({},h),{},{offsetWidth:p,offsetHeight:m});null==d||d(g,e,o),n&&Promise.resolve().then(function(){n(g,e)})}},[]);return r.useEffect(function(){var e=y();return e&&!o&&(S.has(e)||(S.set(e,new Set),D.observe(e)),S.get(e).add(A)),function(){S.has(e)&&(S.get(e).delete(A),S.get(e).size||(D.unobserve(e),S.delete(e)))}},[i.current,o]),r.createElement(H,{ref:f},m?r.cloneElement(p,{ref:b}):p)}),j=r.forwardRef(function(e,t){var n=e.children;return("function"==typeof n?[n]:(0,i.A)(n)).map(function(n,i){var a=(null==n?void 0:n.key)||"".concat("rc-observer-key","-").concat(i);return r.createElement(W,(0,o.A)({},e,{key:a,ref:0===i?t:void 0}),n)})});j.Collection=function(e){var t=e.children,n=e.onBatchResize,o=r.useRef(0),i=r.useRef([]),a=r.useContext(l),u=r.useCallback(function(e,t,r){o.current+=1;var u=o.current;i.current.push({size:e,element:t,data:r}),Promise.resolve().then(function(){u===o.current&&(null==n||n(i.current),i.current=[])}),null==a||a(e,t,r)},[n,a]);return r.createElement(l.Provider,{value:u},t)};let B=j},77001:(e,t,n)=>{n.d(t,{A:()=>a,V:()=>u});var o,r=n(12211);function i(e){var t,n,o="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),i=document.createElement("div");i.id=o;var a=i.style;if(a.position="absolute",a.left="0",a.top="0",a.width="100px",a.height="100px",a.overflow="scroll",e){var u=getComputedStyle(e);a.scrollbarColor=u.scrollbarColor,a.scrollbarWidth=u.scrollbarWidth;var c=getComputedStyle(e,"::-webkit-scrollbar"),s=parseInt(c.width,10),l=parseInt(c.height,10);try{var f=s?"width: ".concat(c.width,";"):"",d=l?"height: ".concat(c.height,";"):"";(0,r.BD)("\n#".concat(o,"::-webkit-scrollbar {\n").concat(f,"\n").concat(d,"\n}"),o)}catch(e){console.error(e),t=s,n=l}}document.body.appendChild(i);var h=e&&t&&!isNaN(t)?t:i.offsetWidth-i.clientWidth,p=e&&n&&!isNaN(n)?n:i.offsetHeight-i.clientHeight;return document.body.removeChild(i),(0,r.m6)(o),{width:h,height:p}}function a(e){return"undefined"==typeof document?0:((e||void 0===o)&&(o=i()),o.width)}function u(e){return"undefined"!=typeof document&&e&&e instanceof Element?i(e):{width:0,height:0}}},51335:(e,t,n)=>{n.d(t,{A:()=>s});var o,r=n(59912),i=n(85268),a=n(12115),u=0,c=(0,i.A)({},o||(o=n.t(a,2))).useId;let s=c?function(e){var t=c();return e||t}:function(e){var t=a.useState("ssr-id"),n=(0,r.A)(t,2),o=n[0],i=n[1];return(a.useEffect(function(){var e=u;u+=1,i("rc_unique_".concat(e))},[]),e)?e:o}},8324:(e,t,n)=>{n.d(t,{A:()=>o});let o=function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))}}}]);