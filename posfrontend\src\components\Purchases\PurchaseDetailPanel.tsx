"use client";

import React from "react";
import { <PERSON><PERSON>, Spin, Descriptions } from "antd";
import {
  EditOutlined,
  LoadingOutlined,
  ShoppingOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useGetPurchaseByIdQuery } from "@/reduxRTK/services/purchaseApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import "./purchase-panels.css";

interface PurchaseDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
  purchaseId: number | null;
  onEdit: (purchaseId: number) => void;
}

const PurchaseDetailPanel: React.FC<PurchaseDetailPanelProps> = ({
  isOpen,
  onClose,
  purchaseId,
  onEdit,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Fetch purchase details when panel is open and purchaseId is available
  const {
    data: response,
    isLoading,
    error,
  } = useGetPurchaseByIdQuery(purchaseId || 0, {
    skip: !isOpen || !purchaseId,
  });

  const purchase = response?.data;

  // The backend already filters purchases based on permissions
  // If we can fetch the purchase, we can view it
  const canViewPurchase = !!purchase;

  console.log("Purchase detail - User ID:", user?.id);
  console.log("Purchase detail - Purchase:", purchase);
  console.log("Purchase detail - Can view purchase:", canViewPurchase);

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format currency for display
  const formatCurrency = (amount?: string) => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-GH", {
      style: "currency",
      currency: "GHS",
      minimumFractionDigits: 2,
    }).format(Number(amount));
  };

  // Check if user can edit (admin can edit all purchases they can see)
  // The backend already filters purchases based on permissions
  const canEdit = userRole === "admin" && !!purchase;

  // Panel footer with close button and edit button (if user can edit)
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: "#d9d9d9", background: "#f5f5f5" }}
      >
        Close
      </Button>
      {onEdit && purchase && canEdit && (
        <Button type="primary" onClick={() => onEdit(purchase.id)}>
          Edit
        </Button>
      )}
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title="Purchase Details"
      width="500px"
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <Spin
            indicator={
              <LoadingOutlined
                style={{ fontSize: 24, color: "#1890ff" }}
                spin
              />
            }
          />
        </div>
      ) : purchase && canViewPurchase ? (
        <>
          {/* Purchase detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="flex items-center text-xl font-bold text-gray-800">
              <ShoppingOutlined className="mr-2" />
              Purchase Details
            </h2>
            <p className="mt-1 flex items-center text-gray-600">
              Complete purchase information and details
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="purchase-detail-light"
            labelStyle={{ color: "#333", backgroundColor: "#f5f5f5" }}
            contentStyle={{ color: "#333", backgroundColor: "#ffffff" }}
          >
            <Descriptions.Item
              label={
                <span>
                  <ShoppingOutlined /> Purchase ID
                </span>
              }
            >
              {purchase.id}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <ShoppingOutlined /> Product
                </span>
              }
            >
              {purchase.product}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <UserOutlined /> Supplier
                </span>
              }
            >
              {purchase.supplier || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <ShoppingOutlined /> Quantity
                </span>
              }
            >
              {purchase.quantity}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <DollarOutlined /> Cost Price
                </span>
              }
            >
              {formatCurrency(purchase.costPrice)}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <DollarOutlined /> Total Cost
                </span>
              }
            >
              {formatCurrency(purchase.totalCost)}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <CalendarOutlined /> Purchase Date
                </span>
              }
            >
              {formatDate(purchase.purchaseDate)}
            </Descriptions.Item>

            <Descriptions.Item
              label={
                <span>
                  <UserOutlined /> Purchased By
                </span>
              }
            >
              {purchase.purchasedBy || "N/A"}
            </Descriptions.Item>
          </Descriptions>
        </>
      ) : purchase && !canViewPurchase ? (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <p className="text-red-500">
            You don&quot;t have permission to view this purchase.
          </p>
        </div>
      ) : (
        <div className="flex h-full min-h-[300px] items-center justify-center">
          <p className="text-gray-800">No purchase data available</p>
        </div>
      )}
    </SlidingPanel>
  );
};

export default PurchaseDetailPanel;
