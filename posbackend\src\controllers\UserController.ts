import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  registerUser,
  loginUser,
  getUserById,
  deleteUserById,
  getAllUsers,
  updateUserById,
  logoutUser,
} from "../services/authService";
import { updatePaymentStatus, verifyPaymentStatusUpdate } from "../utils/updatePaymentStatus";
import { validateMode } from "../utils/modeValidator"; // ✅ Import reusable validator
import { DecodedToken } from "../types/type"; // ✅ Ensure correct typing

// ✅ Separate controller for LOGIN (No token required)
export const loginUserController = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { email, password } = req.body;

  if (!email || !password) {
    return sendResponse(res, 400, false, "Email and password are required.");
  }

  try {
    const loggedInUser = await loginUser(email, password);
    return sendResponse(res, 200, true, "Login successful.", loggedInUser);
  } catch (error: any) {
    return sendResponse(
      res,
      500,
      false,
      error.message || "Internal Server Error"
    );
  }
};

export const logoutController = async (req: Request, res: Response) => {
  const { email } = req.body;
  try {
    await logoutUser(email);
    return sendResponse(res, 200, true, "Logout successfully", null);
  } catch (error: any) {
    return sendResponse(
      res,
      500,
      false,
      error.message || "Internal Server Error"
    );
  }
};

// ✅ Main user controller (Requires authentication via middleware)
export const handleUserRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, userId, page, limit, ...data } = req.body;

  // ✅ Define valid modes (excluding login)
  const validModes = ["createnew", "update", "delete", "retrieve", "current", "fix-payment-status"];

  // ✅ Validate mode using reusable function
  if (!validateMode(res, mode, validModes)) return;

  // ✅ `req.user` is now provided by the middleware (no need to verify token here)
  const requester = req.user as DecodedToken;

  try {
    switch (mode) {
      case "createnew": {
        const { name, email, password, role, phone } = data;
        if (!name || !email || !password || !phone) {
          return sendResponse(
            res,
            400,
            false,
            "Missing required fields: 'name', 'email', 'password', 'phone'."
          );
        }

        const newUser = await registerUser(requester, {
          name,
          email,
          password,
          role,
          phone,
        });
        return sendResponse(
          res,
          201,
          true,
          "User created successfully.",
          newUser
        );
      }

      case "retrieve": {
        if (userId) {
          const user = await getUserById(requester, Number(userId));
          return sendResponse(
            res,
            200,
            true,
            "User retrieved successfully.",
            user
          );
        } else {
          // Log the request for debugging
          const { search } = req.body;
          console.log("getAllUsers request:", {
            requester: { id: requester.id, role: requester.role },
            page,
            limit,
            search: search || 'none'
          });

          // Log the raw request body for debugging
          console.log("Raw request body:", req.body);

          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;
          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          try {
            const users = await getAllUsers(requester, pageNum, limitNum, search);
            console.log("getAllUsers success:", {
              total: users.total,
              userCount: users.users.length,
              searchTerm: search || 'none'
            });
            return sendResponse(
              res,
              200,
              true,
              "Users retrieved successfully.",
              users
            );
          } catch (error: any) {
            console.error("getAllUsers error:", error);
            return sendResponse(
              res,
              500,
              false,
              `Error retrieving users: ${error.message}`,
              null
            );
          }
        }
      }

      case "current": {
        // Get current user's data (for refreshing user state after payment)
        const currentUser = await getUserById(requester, requester.id);
        return sendResponse(
          res,
          200,
          true,
          "Current user data retrieved successfully.",
          currentUser
        );
      }

      case "update": {
        if (!userId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID is required for updating."
          );
        }

        try {
          // Check if this is a password change request
          const { passwordChange } = data;

          const result = await updateUserById(
            requester,
            Number(userId),
            data
          );

          // Handle password change response differently
          if (passwordChange) {
            return sendResponse(
              res,
              200,
              true,
              "Password updated successfully.",
              { success: true }
            );
          }

          return sendResponse(
            res,
            200,
            true,
            "User updated successfully.",
            result
          );
        } catch (error: any) {
          console.error("User update error:", error);

          // Handle specific database constraint errors
          if (error.message.includes("duplicate key value violates unique constraint")) {
            if (error.message.includes("users_email_unique")) {
              return sendResponse(
                res,
                400,
                false,
                "Email is already in use by another user."
              );
            }
            if (error.message.includes("users_phone_unique")) {
              return sendResponse(
                res,
                400,
                false,
                "Phone number is already in use by another user."
              );
            }
          }

          // Handle custom error messages from the service
          return sendResponse(
            res,
            400,
            false,
            error.message || "Failed to update user."
          );
        }
      }

      case "delete": {
        // Check if we have userIds (array) or userId (single)
        const { userIds } = req.body;

        if (!userId && !userIds) {
          return sendResponse(
            res,
            400,
            false,
            "User ID(s) are required for deletion."
          );
        }

        // Use userIds if provided, otherwise use single userId
        const idsToDelete = userIds
          ? (Array.isArray(userIds) ? userIds : [userIds])
          : [Number(userId)];

        const result = await deleteUserById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} users deleted successfully.`
          : "User deleted successfully.";

        return sendResponse(
          res,
          200,
          true,
          message,
          result
        );
      }

      case "fix-payment-status": {
        // Only superadmins can manually fix payment status
        if (requester.role !== "superadmin") {
          return sendResponse(
            res,
            403,
            false,
            "Unauthorized: Only superadmins can fix payment status."
          );
        }

        if (!userId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID is required for fixing payment status."
          );
        }

        const { paymentStatus } = data;
        if (!paymentStatus || !["pending", "paid", "overdue", "inactive"].includes(paymentStatus)) {
          return sendResponse(
            res,
            400,
            false,
            "Valid payment status is required (pending, paid, overdue, inactive)."
          );
        }

        try {
          console.log(`🔧 SuperAdmin ${requester.id} manually fixing payment status for user ${userId} to ${paymentStatus}`);

          // Update payment status
          await updatePaymentStatus(Number(userId), paymentStatus);

          // Verify the update
          const isVerified = await verifyPaymentStatusUpdate(Number(userId), paymentStatus);

          if (isVerified) {
            console.log(`✅ Payment status fix successful for user ${userId}`);
            return sendResponse(
              res,
              200,
              true,
              `Payment status successfully updated to ${paymentStatus}.`,
              { userId: Number(userId), paymentStatus, verified: true }
            );
          } else {
            console.error(`❌ Payment status fix verification failed for user ${userId}`);
            return sendResponse(
              res,
              500,
              false,
              "Payment status update failed verification. Please try again.",
              { userId: Number(userId), paymentStatus, verified: false }
            );
          }
        } catch (error: any) {
          console.error(`❌ Error fixing payment status for user ${userId}:`, error);
          return sendResponse(
            res,
            500,
            false,
            `Failed to fix payment status: ${error.message}`,
            null
          );
        }
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(
      res,
      500,
      false,
      error.message || "Internal Server Error"
    );
  }
};
