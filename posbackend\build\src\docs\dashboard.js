"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     DashboardStats:
 *       type: object
 *       properties:
 *         totalSales:
 *           type: integer
 *           example: 150
 *           description: Total number of sales
 *         totalRevenue:
 *           type: string
 *           example: "12500.50"
 *           description: Total revenue in GHS
 *         totalProducts:
 *           type: integer
 *           example: 250
 *           description: Total number of products
 *         totalUsers:
 *           type: integer
 *           example: 15
 *           description: Total number of users
 *         lowStockProducts:
 *           type: integer
 *           example: 5
 *           description: Number of products with low stock
 *         recentSales:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Sale'
 *           description: Recent sales (last 10)
 *         topProducts:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               productId:
 *                 type: integer
 *                 example: 1
 *               productName:
 *                 type: string
 *                 example: Coca Cola 500ml
 *               totalSold:
 *                 type: integer
 *                 example: 50
 *               revenue:
 *                 type: string
 *                 example: "275.00"
 *           description: Top selling products
 *         salesByDay:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               sales:
 *                 type: integer
 *                 example: 25
 *               revenue:
 *                 type: string
 *                 example: "1250.50"
 *           description: Sales data by day (last 7 days)
 *         paymentMethodBreakdown:
 *           type: object
 *           properties:
 *             cash:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   example: 80
 *                 amount:
 *                   type: string
 *                   example: "4500.00"
 *             card:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   example: 50
 *                 amount:
 *                   type: string
 *                   example: "3200.50"
 *             paystack:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   example: 20
 *                 amount:
 *                   type: string
 *                   example: "4800.00"
 *           description: Breakdown of sales by payment method
 */
/**
 * @swagger
 * /dashboard:
 *   post:
 *     summary: Get dashboard statistics
 *     description: Retrieve comprehensive dashboard data including sales, revenue, and analytics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mode:
 *                 type: string
 *                 enum: [stats]
 *                 example: stats
 *                 description: Mode to get dashboard statistics
 *               dateRange:
 *                 type: object
 *                 properties:
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     example: "2024-01-01"
 *                     description: Start date for analytics (optional)
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     example: "2024-01-31"
 *                     description: End date for analytics (optional)
 *                 description: Date range for filtering analytics data
 *               storeId:
 *                 type: integer
 *                 example: 1
 *                 description: Filter data by specific store (optional)
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Dashboard statistics retrieved successfully
 *                 data:
 *                   $ref: '#/components/schemas/DashboardStats'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
