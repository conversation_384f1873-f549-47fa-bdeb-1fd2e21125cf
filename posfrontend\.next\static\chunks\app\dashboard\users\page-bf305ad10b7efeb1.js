(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9242],{97229:(e,t,r)=>{Promise.resolve().then(r.bind(r,68585))},1227:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},99315:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},86260:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},7162:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},15424:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},98623:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},55750:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},45556:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},45100:(e,t,r)=>{"use strict";r.d(t,{A:()=>z});var a=r(12115),s=r(4617),l=r.n(s),n=r(70527),i=r(28673),o=r(64766),c=r(58292),d=r(71054),u=r(31049),m=r(67548),h=r(10815),x=r(70695),g=r(56204),p=r(1086);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:a,componentCls:s,calc:l}=e,n=l(a).sub(r).equal(),i=l(t).sub(r).equal();return{[s]:Object.assign(Object.assign({},(0,x.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:n,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,m.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(s,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(s,"-close-icon")]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(s,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(s,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:n}}),["".concat(s,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:r,calc:a}=e,s=e.fontSizeSM;return(0,g.oX)(e,{tagFontSize:s,tagLineHeight:(0,m.zA)(a(e.lineHeightSM).mul(s).equal()),tagIconSize:a(r).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new h.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),v=(0,p.OF)("Tag",e=>f(y(e)),b);var j=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)0>t.indexOf(a[s])&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(r[a[s]]=e[a[s]]);return r};let w=a.forwardRef((e,t)=>{let{prefixCls:r,style:s,className:n,checked:i,onChange:o,onClick:c}=e,d=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:h}=a.useContext(u.QO),x=m("tag",r),[g,p,f]=v(x),y=l()(x,"".concat(x,"-checkable"),{["".concat(x,"-checkable-checked")]:i},null==h?void 0:h.className,n,p,f);return g(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},s),null==h?void 0:h.style),className:y,onClick:e=>{null==o||o(!i),null==c||c(e)}})))});var A=r(46258);let N=e=>(0,A.A)(e,(t,r)=>{let{textColor:a,lightBorderColor:s,lightColor:l,darkColor:n}=r;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:a,background:l,borderColor:s,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),C=(0,p.bf)(["Tag","preset"],e=>N(y(e)),b),k=(e,t,r)=>{let a=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(r)],background:e["color".concat(a,"Bg")],borderColor:e["color".concat(a,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},S=(0,p.bf)(["Tag","status"],e=>{let t=y(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},b);var P=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)0>t.indexOf(a[s])&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(r[a[s]]=e[a[s]]);return r};let E=a.forwardRef((e,t)=>{let{prefixCls:r,className:s,rootClassName:m,style:h,children:x,icon:g,color:p,onClose:f,bordered:y=!0,visible:b}=e,j=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:A,tag:N}=a.useContext(u.QO),[k,E]=a.useState(!0),z=(0,n.A)(j,["closeIcon","closable"]);a.useEffect(()=>{void 0!==b&&E(b)},[b]);let D=(0,i.nP)(p),O=(0,i.ZZ)(p),M=D||O,B=Object.assign(Object.assign({backgroundColor:p&&!M?p:void 0},null==N?void 0:N.style),h),I=w("tag",r),[L,R,U]=v(I),T=l()(I,null==N?void 0:N.className,{["".concat(I,"-").concat(p)]:M,["".concat(I,"-has-color")]:p&&!M,["".concat(I,"-hidden")]:!k,["".concat(I,"-rtl")]:"rtl"===A,["".concat(I,"-borderless")]:!y},s,m,R,U),F=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||E(!1)},[,H]=(0,o.A)((0,o.d)(e),(0,o.d)(N),{closable:!1,closeIconRender:e=>{let t=a.createElement("span",{className:"".concat(I,"-close-icon"),onClick:F},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),F(t)},className:l()(null==e?void 0:e.className,"".concat(I,"-close-icon"))}))}}),_="function"==typeof j.onClick||x&&"a"===x.type,W=g||null,V=W?a.createElement(a.Fragment,null,W,x&&a.createElement("span",null,x)):x,q=a.createElement("span",Object.assign({},z,{ref:t,className:T,style:B}),V,H,D&&a.createElement(C,{key:"preset",prefixCls:I}),O&&a.createElement(S,{key:"status",prefixCls:I}));return L(_?a.createElement(d.A,{component:"Tag"},q):q)});E.CheckableTag=w;let z=E},68585:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>en});var a=r(95155),s=r(12115),l=r(71349),n=r(72093),i=r(43316);r(58604),r(36243);var o=r(16419),c=r(85407);let d={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};var u=r(84021),m=s.forwardRef(function(e,t){return s.createElement(u.A,(0,c.A)({},e,{ref:t,icon:d}))}),h=r(36060),x=r(63065),g=r(9273);let p=()=>{var e,t,r,a,l;let[n,i]=(0,s.useState)(1),[o,c]=(0,s.useState)(10),[d,u]=(0,s.useState)(""),m=(0,g.d)(d,500);(0,s.useEffect)(()=>{i(1)},[m]);let{data:h,isLoading:p,isFetching:f,refetch:y}=(0,x.VL)({page:n,limit:o,search:m},{refetchOnMountOrArgChange:!0});return console.log("User search state:",{searchTerm:d,debouncedSearchTerm:m,resultsCount:(null==h?void 0:null===(t=h.data)||void 0===t?void 0:null===(e=t.users)||void 0===e?void 0:e.length)||0,totalResults:(null==h?void 0:null===(r=h.data)||void 0===r?void 0:r.total)||0}),{users:(null==h?void 0:null===(a=h.data)||void 0===a?void 0:a.users)||[],total:(null==h?void 0:null===(l=h.data)||void 0===l?void 0:l.total)||0,isLoading:p,isFetching:f,refetch:y,searchTerm:d,setSearchTerm:u,pagination:{page:n,pageSize:o,setPage:i,setPageSize:c}}};var f=r(75912);let y=e=>{let[t,r]=(0,s.useState)(!1),[a,l]=(0,s.useState)(null),[n,{isLoading:i}]=(0,x.HC)();return{confirmDelete:e=>{l(e),r(!0)},cancelDelete:()=>{r(!1),l(null)},executeDelete:async()=>{if(a)try{await n(a).unwrap(),(0,f.r)("success","User deleted successfully"),e&&e(),r(!1),l(null)}catch(e){(0,f.r)("error",e.message||"Failed to delete user"),console.error("Delete error:",e)}},isDeleting:i,isConfirmOpen:t,userToDelete:a}},b=e=>{let[t,{isLoading:r}]=(0,x.qx)();return{bulkDeleteUsers:async r=>{try{console.log("Bulk deleting users with IDs:",r);let a=await t(r).unwrap();if(!a.success)throw Error(a.message||"Failed to delete users");return(0,f.r)("success","".concat(r.length," users deleted successfully")),e&&e(),a.data}catch(e){throw console.error("Bulk delete users error:",e),(0,f.r)("error",e.message||"Failed to delete users"),e}},isDeleting:r}};var v=r(80766),j=r(45100),w=r(92895),A=r(6457),N=r(1227),C=r(99315),k=r(45556),S=r(98623),P=r(17084),E=r(80519),z=r(86260),D=r(27656),O=r(60102),M=r(91256),B=r(21455),I=r.n(B),L=r(34252);let R=e=>{let{users:t,isMobile:r,onView:l,onEdit:n,onDelete:o,onBulkDelete:c,canManageUser:d}=e,u=(0,M.E)(),m=r||u,[h,x]=(0,s.useState)([]),[g,p]=(0,s.useState)(!1),f=e=>{let r=e.target.checked;p(r),r?x(t.filter(e=>d(e.role)).map(e=>e.id)):x([])},y=(e,t)=>{t?x(t=>[...t,e]):x(t=>t.filter(t=>t!==e))},b=e=>{switch(e){case"paid":return(0,a.jsx)(j.A,{className:"status-tag status-paid",icon:(0,a.jsx)(N.A,{}),color:"success",children:"Paid"});case"pending":return(0,a.jsx)(j.A,{className:"status-tag status-pending",icon:(0,a.jsx)(C.A,{}),color:"warning",children:"Pending"});case"overdue":return(0,a.jsx)(j.A,{className:"status-tag status-overdue",icon:(0,a.jsx)(k.A,{}),color:"error",children:"Overdue"});case"inactive":return(0,a.jsx)(j.A,{className:"status-tag status-inactive",icon:(0,a.jsx)(S.A,{}),color:"default",children:"Inactive"});default:return(0,a.jsx)(j.A,{className:"status-tag",color:"default",children:e})}},B=e=>{switch(e){case"superadmin":return(0,a.jsx)(j.A,{color:"purple",children:"Super Admin"});case"admin":return(0,a.jsx)(j.A,{color:"blue",children:"Admin"});case"cashier":return(0,a.jsx)(j.A,{color:"green",children:"Cashier"});default:return(0,a.jsx)(j.A,{color:"default",children:e})}};return(0,a.jsxs)("div",{className:"overflow-hidden bg-white",children:[h.length>0&&(0,a.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[h.length," ",1===h.length?"user":"users"," selected"]}),(0,a.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,a.jsx)(P.A,{}),onClick:()=>{h.length>0&&c?(c(h),x([]),p(!1)):v.Ay.warning({message:"No users selected",description:"Please select at least one user to delete."})},className:"ml-2",children:"Delete Selected"})]}),m?(0,a.jsxs)(O.jB,{columns:"50px 200px 100px 100px 150px",minWidth:"700px",children:[(0,a.jsx)(O.A0,{className:"text-center",children:(0,a.jsx)(w.A,{checked:g,onChange:f,disabled:0===t.filter(e=>d(e.role)).length})}),(0,a.jsx)(O.A0,{sticky:m?void 0:"left",children:"Name"}),!m&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O.A0,{children:"Email"}),(0,a.jsx)(O.A0,{children:"Phone"}),(0,a.jsx)(O.A0,{children:"Created At"})]}),(0,a.jsx)(O.A0,{children:"Role"}),(0,a.jsx)(O.A0,{children:"Status"}),(0,a.jsx)(O.A0,{sticky:m?void 0:"right",className:"text-right",children:"Actions"}),t.map(e=>(0,a.jsxs)(O.Hj,{selected:h.includes(e.id),children:[(0,a.jsx)(O.nA,{className:"text-center",children:d(e.role)&&(0,a.jsx)(w.A,{checked:h.includes(e.id),onChange:t=>y(e.id,t.target.checked)})}),(0,a.jsx)(O.nA,{children:(0,a.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,a.jsx)(O.nA,{children:B(e.role)}),(0,a.jsx)(O.nA,{children:b(e.paymentStatus)}),(0,a.jsx)(O.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(A.A,{title:"View",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(E.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),d(e.role)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{title:"Edit",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(z.A,{}),onClick:()=>n(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,a.jsx)(A.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(D.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,a.jsx)("div",{className:"overflow-x-auto w-full",children:(0,a.jsxs)("table",{className:"min-w-[900px] w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,a.jsx)(w.A,{checked:g,onChange:f,disabled:0===t.filter(e=>d(e.role)).length})}),(0,a.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-white shadow-md px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell",children:"Email"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell",children:"Phone"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden xl:table-cell",children:"Created At"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-white shadow-md px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:h.includes(e.id)?"bg-blue-50":"",children:[(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:d(e.role)&&(0,a.jsx)(w.A,{checked:h.includes(e.id),onChange:t=>y(e.id,t.target.checked)})}),(0,a.jsx)("td",{className:"sticky left-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-gray-800",children:(0,a.jsx)("div",{className:"max-w-[120px] truncate overflow-hidden text-ellipsis",children:e.name})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell",children:(0,a.jsx)("div",{className:"max-w-[200px] truncate overflow-hidden text-ellipsis",children:e.email})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell",children:(0,a.jsx)("div",{className:"max-w-[140px] truncate overflow-hidden text-ellipsis",children:(0,L.oB)(e.phone)})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden xl:table-cell",children:(0,a.jsx)("div",{className:"max-w-[130px] truncate overflow-hidden text-ellipsis",children:I()(e.createdAt).format("MMM D, YYYY")})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:B(e.role)}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:b(e.paymentStatus)}),(0,a.jsx)("td",{className:"sticky right-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(A.A,{title:"View",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(E.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-green-700",size:"middle"})}),d(e.role)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{title:"Edit",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(z.A,{}),onClick:()=>n(e),type:"text",className:"edit-button",size:"middle"})}),(0,a.jsx)(A.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(D.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]})]})})]},e.id))})]})})]})};var U=r(33621),T=r(44549);let F=e=>{let{page:t,pageSize:r,total:s,setPage:l,isMobile:n}=e,i=Math.ceil(s/r);return(0,a.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*r+1})," to"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*r,s)})," of"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)(U.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,i)},(e,r)=>{let s=r+1;return(0,a.jsx)("button",{onClick:()=>l(s),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===s?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:s},s)}),(0,a.jsxs)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=i?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)(T.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",i]}),(0,a.jsx)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=i?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})};var H=r(21614),_=r(83414),W=r(41657),V=r(24988);let q=e=>{let[t,{isLoading:r}]=(0,x.i0)();return{createUser:async r=>{try{let a=await t(r).unwrap();if(!a.success)throw Error(a.message||"Failed to create user");return(0,f.r)("success","User created successfully"),e&&e(),a.data}catch(e){throw console.error("Create user error:",e),(0,f.r)("error",e.message||"Failed to create user"),e}},isCreating:r}},Y=e=>{let[t,{isLoading:r}]=(0,x.H7)();return{updateUser:async(r,a)=>{try{let s=await t({userId:r,data:a}).unwrap();if(!s.success)throw Error(s.message||"Failed to update user");return(0,f.r)("success","User updated successfully"),e&&e(),s.data}catch(e){throw console.error("Update user error:",e),(0,f.r)("error",e.message||"Failed to update user"),e}},isUpdating:r}};r(97598);let{Option:Q}=H.A,$=e=>{let{isOpen:t,onClose:r,onSuccess:l,user:n,currentUser:o}=e,[c]=_.A.useForm(),d=!!n,{createUser:u,isCreating:m}=q(l),{updateUser:h,isUpdating:x}=Y(l),g=m||x;(0,s.useEffect)(()=>{if(t){if(n){let e=(0,L.n4)(n.phone);c.setFieldsValue({name:n.name,email:n.email,phone:e,role:n.role,paymentStatus:n.paymentStatus})}else c.resetFields(),(null==o?void 0:o.role)==="admin"&&c.setFieldsValue({role:"cashier",paymentStatus:"paid"})}},[t,n,c,o]);let p=async()=>{try{let e=await c.validateFields();if(d&&n){let t=(0,L.n4)(e.phone),r={name:e.name,email:e.email,phone:t};((null==o?void 0:o.role)==="superadmin"||(null==o?void 0:o.role)==="admin"&&"cashier"===e.role)&&(r.role=e.role),(null==o?void 0:o.role)==="superadmin"&&(r.paymentStatus=e.paymentStatus),await h(n.id,r)}else{let t=(0,L.n4)(e.phone),r={name:e.name,email:e.email,password:e.password,phone:t};(null==o?void 0:o.role)==="superadmin"?r.role=e.role:(null==o?void 0:o.role)==="admin"&&(r.role="cashier",r.paymentStatus="paid"),await u(r)}r()}catch(e){console.error("Form submission error:",e)}},f=(null==o?void 0:o.role)==="superadmin"?["superadmin","admin","cashier"]:(null==o?void 0:o.role)==="admin"?["cashier"]:[],y=(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(i.Ay,{onClick:r,disabled:g,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,a.jsx)(i.Ay,{type:"primary",loading:g,onClick:p,children:d?"Update":"Create"})]});return(0,a.jsxs)(V.A,{isOpen:t,onClose:r,title:d?"Edit User":"Add New User",width:"450px",footer:y,children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing User: ",null==n?void 0:n.name]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),"Create New User"]})}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:d?"Update user information using the form below":"Fill in the details to create a new user account"})]}),(0,a.jsxs)(_.A,{form:c,layout:"vertical",initialValues:{role:"cashier",paymentStatus:(null==o?void 0:o.role)==="admin"?"paid":"pending"},className:"text-gray-800 w-full",style:{color:"#333"},children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,a.jsx)(_.A.Item,{name:"name",label:"Name",rules:[{required:!0,message:"Please enter name"}],children:(0,a.jsx)(W.A,{placeholder:"Enter full name"})}),(0,a.jsx)(_.A.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Please enter email"},{type:"email",message:"Please enter a valid email"}],children:(0,a.jsx)(W.A,{placeholder:"Enter email address"})}),(0,a.jsx)(_.A.Item,{name:"phone",label:"Phone",rules:[{required:!0,message:"Please enter phone number"},{min:5,message:"Phone number must be at least 5 characters"},{pattern:/^[0-9+\-\s()]*$/,message:"Phone number can only contain digits, spaces, and the characters +, -, (, )"}],tooltip:"Enter phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)",children:(0,a.jsx)(W.A,{placeholder:"e.g., ************"})})]}),!d&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Authentication"}),(0,a.jsx)(_.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter password"},{min:6,message:"Password must be at least 6 characters"}],children:(0,a.jsx)(W.A.Password,{placeholder:"Enter password"})})]}),f.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Access Control"}),(0,a.jsx)(_.A.Item,{name:"role",label:"Role",rules:[{required:!0,message:"Please select role"}],children:(0,a.jsx)(H.A,{placeholder:"Select role",children:f.map(e=>(0,a.jsx)(Q,{value:e,children:"superadmin"===e?"Super Admin":"admin"===e?"Admin":"Cashier"},e))})})]}),d&&(null==o?void 0:o.role)==="superadmin"&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Payment Information"}),(0,a.jsx)(_.A.Item,{name:"paymentStatus",label:"Payment Status",rules:[{required:!0,message:"Please select payment status"}],children:(0,a.jsxs)(H.A,{placeholder:"Select payment status",children:[(0,a.jsx)(Q,{value:"pending",children:"Pending"}),(0,a.jsx)(Q,{value:"paid",children:"Paid"}),(0,a.jsx)(Q,{value:"overdue",children:"Overdue"}),(0,a.jsx)(Q,{value:"inactive",children:"Inactive"})]})})]})]})]})};var G=r(67649),X=r(55750),Z=r(7162),J=r(15424),K=r(87181);let ee=e=>{let{data:t,isLoading:r}=(0,x.$f)(e||0,{skip:!e});return{user:null==t?void 0:t.data,isLoading:r}},et=e=>{let{isOpen:t,onClose:r,userId:s}=e,{user:l,isLoading:c}=ee(s),d=e=>{if(!e)return"N/A";try{return I()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},u=(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(i.Ay,{onClick:r,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"})});return(0,a.jsx)(V.A,{isOpen:t,onClose:r,title:"User Details",width:"500px",footer:u,children:c?(0,a.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"User Profile: ",l.name,(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-1 rounded-full ".concat("superadmin"===l.role?"bg-purple-100 text-purple-800":"admin"===l.role?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"),children:"superadmin"===l.role?"Super Admin":"admin"===l.role?"Admin":"Cashier"})]}),(0,a.jsxs)("p",{className:"text-gray-600 mt-1 flex items-center",children:["Complete user information and account details",(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-1 rounded-full ".concat("paid"===l.paymentStatus?"bg-green-100 text-green-800":"pending"===l.paymentStatus?"bg-yellow-100 text-yellow-800":"overdue"===l.paymentStatus?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:"paid"===l.paymentStatus?"Paid":"pending"===l.paymentStatus?"Pending":"overdue"===l.paymentStatus?"Overdue":"Inactive"})]})]}),(0,a.jsxs)(G.A,{bordered:!0,column:1,className:"user-detail-dark",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,a.jsx)(G.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(X.A,{})," Name"]}),children:l.name}),(0,a.jsx)(G.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(Z.A,{})," Email"]}),children:l.email}),(0,a.jsx)(G.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(J.A,{})," Phone"]}),children:(0,L.oB)(l.phone)}),(0,a.jsx)(G.A.Item,{label:"Role",children:(e=>{switch(e){case"superadmin":return(0,a.jsx)(j.A,{color:"purple",children:"Super Admin"});case"admin":return(0,a.jsx)(j.A,{color:"blue",children:"Admin"});case"cashier":return(0,a.jsx)(j.A,{color:"green",children:"Cashier"});default:return(0,a.jsx)(j.A,{color:"default",children:e})}})(l.role)}),(0,a.jsx)(G.A.Item,{label:"Payment Status",children:(e=>{switch(e){case"paid":return(0,a.jsx)(j.A,{icon:(0,a.jsx)(N.A,{}),color:"success",children:"Paid"});case"pending":return(0,a.jsx)(j.A,{icon:(0,a.jsx)(C.A,{}),color:"warning",children:"Pending"});case"overdue":return(0,a.jsx)(j.A,{icon:(0,a.jsx)(k.A,{}),color:"error",children:"Overdue"});case"inactive":return(0,a.jsx)(j.A,{icon:(0,a.jsx)(S.A,{}),color:"default",children:"Inactive"});default:return(0,a.jsx)(j.A,{color:"default",children:e})}})(l.paymentStatus)}),(0,a.jsx)(G.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(K.A,{})," Created At"]}),children:d(l.createdAt)}),(0,a.jsx)(G.A.Item,{label:"Last Payment Date",children:d(l.lastPaymentDate)}),(0,a.jsx)(G.A.Item,{label:"Next Payment Due",children:d(l.nextPaymentDue)})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-gray-800",children:"User not found"})})};var er=r(5413);r(66202);let ea=e=>{let{searchTerm:t,setSearchTerm:r,isMobile:s=!1}=e;return(0,a.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,a.jsx)(W.A,{placeholder:"Search by name, email, or phone...",prefix:(0,a.jsx)(er.A,{className:"text-gray-400"}),value:t,onChange:e=>{let t=e.target.value;console.log("Search input changed:",t),r(t)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-400 focus:border-blue-400",style:{width:s?"100%":"300px",height:"36px",backgroundColor:"#ffffff",color:"#333333"},allowClear:{clearIcon:(0,a.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),t&&(0,a.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',t,'"']})]})};var es=r(12467);let el=()=>{let[e,t]=(0,s.useState)({width:window.innerWidth,height:window.innerHeight});return(0,s.useEffect)(()=>{function e(){t({width:window.innerWidth,height:window.innerHeight})}return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[]),e},en=()=>{let{user:e}=(0,h.A)(),{width:t}=el(),r=t<640,[c,d]=(0,s.useState)(!1),[u,x]=(0,s.useState)(!1),[g,f]=(0,s.useState)(!1),[v,j]=(0,s.useState)(null),[w,A]=(0,s.useState)(null),{users:N,total:C,isLoading:k,isFetching:S,refetch:P,searchTerm:E,setSearchTerm:z,pagination:{page:D,pageSize:O,setPage:M}}=p(),{confirmDelete:B,cancelDelete:I,executeDelete:L,isDeleting:U,isConfirmOpen:T,userToDelete:H}=y(P),{bulkDeleteUsers:_,isDeleting:W}=b(()=>{q(!1),P()}),[V,q]=(0,s.useState)(!1),[Y,Q]=(0,s.useState)([]),G=(null==e?void 0:e.role)==="superadmin"||(null==e?void 0:e.role)==="admin",X=async()=>{if(console.log("confirmBulkDelete called with users:",Y),Y.length>0)try{await _(Y)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return((0,s.useEffect)(()=>{console.log("Users page - isAddPanelOpen changed:",c)},[c]),k)?(0,a.jsx)("div",{className:"p-2 sm:p-4 w-full",children:(0,a.jsx)(l.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"User Management"}),styles:{body:{padding:"16px",minHeight:"200px",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,a.jsx)("div",{className:"flex justify-center items-center h-60",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})})})}):(0,a.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,a.jsx)(l.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"User Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:G&&(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(m,{}),onClick:()=>{console.log("Add user button clicked"),j(null),setTimeout(()=>{d(!0)},0)},size:r?"small":"middle",children:r?"":"Add User"}),children:(0,a.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden",children:[(0,a.jsx)(ea,{searchTerm:E,setSearchTerm:z,isMobile:r}),S?(0,a.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,a.jsxs)(a.Fragment,{children:[N.length>0?(0,a.jsx)(R,{users:N,isMobile:r,onView:e=>{A(e),f(!0)},onEdit:e=>{j(e),x(!0)},onDelete:B,onBulkDelete:e=>{console.log("handleBulkDelete called with userIds:",e),Q(e),q(!0)},canManageUser:t=>(null==e?void 0:e.role)==="superadmin"||(null==e?void 0:e.role)==="admin"&&"cashier"===t}):(0,a.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-700",children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{children:"No users found matching your search criteria."}),(0,a.jsx)(i.Ay,{type:"primary",onClick:()=>z(""),className:"mt-4 bg-blue-500 hover:bg-blue-600",children:"Clear Search"})]}):(0,a.jsxs)("p",{children:["No users found. ",G&&"Click 'Add User' to create one."]})}),N.length>0&&(0,a.jsx)(F,{page:D,pageSize:O,total:C,setPage:M,isMobile:r})]})]})}),(0,a.jsx)($,{isOpen:c,onClose:()=>d(!1),onSuccess:()=>{d(!1),P()},currentUser:e}),(0,a.jsx)($,{isOpen:u,onClose:()=>x(!1),onSuccess:()=>{x(!1),P()},user:v,currentUser:e}),(0,a.jsx)(et,{isOpen:g,onClose:()=>{f(!1),A(null)},userId:w}),(0,a.jsx)(es.A,{isOpen:T,onClose:I,onConfirm:L,title:"Delete User",message:"Are you sure you want to delete this user? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:U,type:"danger"}),(0,a.jsx)(es.A,{isOpen:V,onClose:()=>{q(!1),Q([])},onConfirm:X,title:"Delete Multiple Users",message:"Are you sure you want to delete ".concat(Y.length," users? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:W,type:"danger"})]})}},12467:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155);r(12115);var s=r(46102),l=r(43316),n=r(75218);let i=e=>{let{isOpen:t,onClose:r,onConfirm:i,title:o,message:c,confirmText:d="Confirm",cancelText:u="Cancel",isLoading:m=!1,type:h="danger"}=e;return(0,a.jsx)(s.A,{title:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{style:{color:"danger"===h?"#ff4d4f":"warning"===h?"#faad14":"#1890ff",marginRight:8}}),(0,a.jsx)("span",{children:o})]}),open:t,onCancel:r,footer:[(0,a.jsx)(l.Ay,{onClick:r,disabled:m,children:u},"cancel"),(0,a.jsx)(l.Ay,{type:"danger"===h?"primary":"default",danger:"danger"===h,onClick:i,loading:m,children:d},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,a.jsx)("p",{className:"my-4",children:c})})}},60102:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,Hj:()=>o,jB:()=>l,nA:()=>i});var a=r(95155);r(12115);var s=r(21567);let l=e=>{let{children:t,columns:r,className:l,minWidth:n="800px"}=e,i=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,a.jsx)("div",{className:(0,s.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:r,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:r,sticky:l}=e,n=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],r),children:t})},i=e=>{let{children:t,className:r,sticky:l}=e,n=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],r),children:t})},o=e=>{let{children:t,className:r,selected:l=!1,onClick:n}=e;return(0,a.jsx)("div",{className:(0,s.cn)("contents",l&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",r),onClick:n,children:t})}},24988:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(12115),l=r(43316),n=r(79624);let i=e=>{let{isOpen:t,onClose:r,title:i,children:o,width:c="400px",footer:d,fullWidth:u=!1}=e,[m,h]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),[p,f]=(0,s.useState)(window.innerWidth);if((0,s.useEffect)(()=>{let e=()=>{f(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,s.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)g(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{h(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{h(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{g(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!x)return null;let y="Point of Sale"===i||u||"100vw"===c;return(0,a.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(y?"sales-panel-container":""),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:r}),(0,a.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||u||"100vw"===c||p<640?"100vw":p<1024?"500px":"string"==typeof c&&c.includes("px")&&parseInt(c)>600?"600px":c},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,a.jsx)(l.Ay,{type:"text",icon:(0,a.jsx)(n.A,{style:{color:"#333"}}),onClick:r,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:o}),d&&(0,a.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:d})]})]})}},36060:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(83391),s=r(70854),l=r(63065),n=r(7875);let i=()=>{let e=(0,a.wA)(),{user:t,accessToken:r}=(0,a.d4)(e=>e.auth),i=(0,s._)(),{refetch:o}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!r,d=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await o();console.log("useAuth - Refetch result:",a);let s=a.data;if((null==s?void 0:s.success)&&(null==s?void 0:s.data)){console.log("useAuth - API response data:",s.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=s.data.phone||t.phone||"",i=s.data.createdAt||t.createdAt||"",o=s.data.lastPaymentDate||t.lastPaymentDate||void 0,c=s.data.nextPaymentDue||t.nextPaymentDue||null,d=s.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:s.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:s.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:s.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:s.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:s.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...s.data,phone:l,createdAt:i,lastPaymentDate:o,nextPaymentDue:c,createdBy:d,paymentStatus:a};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:u,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==s?void 0:s.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:d}}},70854:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var a=r(12115),s=r(83391),l=r(21455),n=r.n(l);let i=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,r]=(0,a.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,a.useEffect)(()=>{if(!e){r({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,a=!1,s=!0,l="inactive";if("superadmin"===e.role){r({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){a=!0,s=!1,l="active";let r=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),i=n()();if(t=l.diff(i,"day"),r){let r=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:r,daysRemaining:t,trialDaysUsed:r,trialDaysRemaining:t,isActive:a,needsPayment:s})}}}else"pending"===e.paymentStatus?(a=!1,s=!0,l="pending"):"overdue"===e.paymentStatus?(a=!1,s=!0,l="overdue"):(a=!1,s=!0,l="inactive");r({isActive:a,daysRemaining:t,status:l,needsPayment:s})},[e]),t}},9273:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var a=r(12115);function s(e,t){let[r,s]=(0,a.useState)(e);return(0,a.useEffect)(()=>{console.log("Debouncing value:",e);let r=setTimeout(()=>{console.log("Debounce timer completed, setting value:",e),s(e)},t);return()=>{clearTimeout(r)}},[e,t]),r}},91256:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var a=r(12115);let s=()=>{let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(43463),s=r(69795);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},34252:(e,t,r)=>{"use strict";r.d(t,{n4:()=>a,oB:()=>s});let a=e=>{if(!e)return"";let t=e.replace(/\D/g,"");return e.startsWith("0")?e:t.startsWith("233")||e.startsWith("+233")?"0"+t.substring(3):e},s=e=>{let t=a(e);if(!t)return"";let r=t.replace(/\D/g,"");return 10===r.length&&r.startsWith("0")?"".concat(r.substring(0,3)," ").concat(r.substring(3,7)," ").concat(r.substring(7)):t}},75912:(e,t,r)=>{"use strict";r.d(t,{r:()=>s});var a=r(55037);let s=(e,t)=>{"success"===e?a.oR.success(t):"error"===e?a.oR.error(t):"warning"===e&&(0,a.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},58604:()=>{},36243:()=>{},97598:()=>{},66202:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[610,8059,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,5211,821,8441,1517,7358],()=>t(97229)),_N_E=e.O()}]);