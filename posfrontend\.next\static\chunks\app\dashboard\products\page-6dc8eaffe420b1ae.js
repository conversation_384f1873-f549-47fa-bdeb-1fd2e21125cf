(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9926],{53745:(e,t,s)=>{Promise.resolve().then(s.bind(s,36567))},36567:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eg});var r=s(95155),a=s(12115),l=s(71349),n=s(43316),i=s(80766),o=s(72093),c=s(72278),d=s(34802),u=s(16419),m=s(36060),x=s(30555),p=s(93968),h=s(9273);let g=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,[l,n]=(0,a.useState)(s),[i,o]=(0,a.useState)(r),[c,d]=(0,a.useState)(""),u=(0,h.d)(c,500);(0,a.useEffect)(()=>{n(1)},[u]);let{data:m,error:x,isLoading:g,refetch:y}=(0,p.r3)({page:l,limit:i,search:u},{refetchOnMountOrArgChange:!0});return{products:(null==m?void 0:null===(e=m.data)||void 0===e?void 0:e.products)||[],total:(null==m?void 0:null===(t=m.data)||void 0===t?void 0:t.total)||0,page:l,limit:i,isLoading:g,error:x,refetch:y,forceRefresh:()=>{console.log("Forcing product list refresh"),y()},searchTerm:c,setSearchTerm:d,handlePageChange:e=>{n(e)},handleLimitChange:e=>{o(e),n(1)}}};var y=s(75912);let f=e=>{let[t,{isLoading:s}]=(0,p.lY)();return{deleteProduct:async s=>{try{let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete product");return(0,y.r)("success","Product deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete product error:",e),(0,y.r)("error",e.message||"Failed to delete product"),e}},isDeleting:s}},j=e=>{let[t,{isLoading:s}]=(0,p.IT)();return{bulkDeleteProducts:async s=>{try{console.log("Bulk deleting products with IDs:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete products");return(0,y.r)("success","".concat(s.length," products deleted successfully")),e&&e(),r.data}catch(e){throw console.error("Bulk delete products error:",e),(0,y.r)("error",e.message||"Failed to delete products"),e}},isDeleting:s}};var b=s(5565),v=s(92895),w=s(45100),A=s(6457),N=s(95872),k=s(17084),S=s(40794),C=s(80519),P=s(86260),D=s(27656),F=s(95693),I=s(60102),E=s(91256),T=s(10927),z=s(83391);let U=e=>{var t;let{products:s,loading:l,onView:o,onEdit:c,onDelete:u,onBulkDelete:m,onAdjustStock:x,isMobile:p=!1}=e,h=(0,E.E)(),g=(0,z.d4)(e=>e.auth.user),y=null==g?void 0:g.role,[f,j]=(0,a.useState)([]),[U,L]=(0,a.useState)(!1),{data:M}=(0,T.lg)({page:1,limit:100}),R=(null==M?void 0:null===(t=M.data)||void 0===t?void 0:t.categories)||[],B=e=>{let t=R.find(t=>t.id===e);return t?t.name:"Uncategorized"},_=e=>{let t=e.target.checked;L(t),t?j(s.filter(e=>q(e)).map(e=>e.id)):j([])},O=(e,t)=>{t?j(t=>[...t,e]):j(t=>t.filter(t=>t!==e))},q=e=>"superadmin"===y||"admin"===y&&(null==g?void 0:g.id)===e.createdBy,V="admin"===y,H=e=>e.imageUrl?(0,r.jsx)(b.default,{src:e.imageUrl,alt:e.name,width:48,height:48,className:"object-cover rounded-md"}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"text-2xl text-gray-400"})});return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[f.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[f.length," ",1===f.length?"product":"products"," selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(k.A,{}),onClick:()=>{f.length>0&&m?(m(f),j([]),L(!1)):i.Ay.warning({message:"No products selected",description:"Please select at least one product to delete."})},className:"ml-2",children:"Delete Selected"})]}),p||h?(0,r.jsxs)(I.jB,{columns:"50px 200px 120px 150px",minWidth:"700px",children:[(0,r.jsx)(I.A0,{className:"text-center",children:(0,r.jsx)(v.A,{checked:U,onChange:_,disabled:0===s.filter(e=>q(e)).length})}),(0,r.jsx)(I.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)(I.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Price"]})}),(0,r.jsx)(I.A0,{className:"text-right",children:"Actions"}),s.map(e=>(0,r.jsxs)(I.Hj,{selected:f.includes(e.id),children:[(0,r.jsx)(I.nA,{className:"text-center",children:q(e)&&(0,r.jsx)(v.A,{checked:f.includes(e.id),onChange:t=>O(e.id,t.target.checked)})}),(0,r.jsx)(I.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[H(e),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name}),(0,r.jsx)(w.A,{color:"blue",className:"mt-1",children:B(e.categoryId)})]})]})}),(0,r.jsxs)(I.nA,{children:[(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,r.jsx)(S.A,{className:"mr-1"}),parseFloat(e.price).toFixed(2)]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:[(0,r.jsx)(d.A,{className:"mr-1"}),e.stockQuantity]})]}),(0,r.jsx)(I.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>o(e.id),type:"text",className:"view-button text-blue-600",size:"small"})}),q(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>c(e),type:"text",className:"edit-button",size:"small"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(D.A,{}),onClick:()=>u(e.id),type:"text",className:"delete-button",danger:!0,size:"small"})})]}),V&&(0,r.jsx)(A.A,{title:"Adjust Stock",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(F.A,{}),onClick:()=>x(e),type:"text",className:"adjust-stock-button",size:"small"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(v.A,{checked:U,onChange:_,disabled:0===s.filter(e=>q(e)).length})}),(0,r.jsx)("th",{scope:"col",className:"w-12 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Image"}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"SKU"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Barcode"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Stock"}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,r.jsxs)("tr",{className:f.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:q(e)&&(0,r.jsx)(v.A,{checked:f.includes(e.id),onChange:t=>O(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap",children:H(e)}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)(w.A,{color:"blue",children:B(e.categoryId)})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.sku||"-"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.barcode||"-"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,r.jsx)(S.A,{className:"mr-1"}),parseFloat(e.price).toFixed(2)]})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),e.stockQuantity]})}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>o(e.id),type:"text",className:"view-button text-blue-600",size:"middle"})}),q(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>c(e),type:"text",className:"edit-button",size:"middle"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(D.A,{}),onClick:()=>u(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]}),V&&(0,r.jsx)(A.A,{title:"Adjust Stock",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(F.A,{}),onClick:()=>x(e),type:"text",className:"adjust-stock-button",size:"middle"})})]})})]},e.id))})]})})]})};var L=s(33621),M=s(44549);let R=e=>{let{current:t,pageSize:s,total:a,onChange:l,isMobile:n=!1}=e,i=Math.ceil(a/s);return(0,r.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*s+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*s,a)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:a})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(L.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,i)},(e,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>l(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:a},a)}),(0,r.jsxs)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=i?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(M.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",i]}),(0,r.jsx)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=i?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})};var B=s(21614),_=s(83414),O=s(27114),q=s(41657),V=s(18198),H=s(51554),G=s(24988);let W=e=>{let[t,{isLoading:s}]=(0,p.Q$)();return{createProduct:async s=>{try{var r,a;console.log("useProductCreate - Starting product creation with data:",s);let l=await t(s).unwrap();if(console.log("useProductCreate - API response:",l),!l.success)throw console.error("useProductCreate - API returned error:",l.message),Error(l.message||"Failed to create product");return(0,y.r)("success","Product created successfully"),e&&(console.log("Calling onSuccess callback for product creation"),e(),setTimeout(()=>{console.log("Calling delayed onSuccess to ensure table refresh after product creation"),e()},300)),(null===(a=l.data)||void 0===a?void 0:null===(r=a.products)||void 0===r?void 0:r[0])||l.data}catch(e){throw console.error("Create product error:",e),(0,y.r)("error",e.message||"Failed to create product"),e}},isCreating:s}},Q=e=>{let[t,{isLoading:s}]=(0,p.vM)();return{updateProduct:async(s,r)=>{try{console.log("Updating product:",s,r);let a=await t({productId:s,data:r}).unwrap();if(!a.success)throw Error(a.message||"Failed to update product");return(0,y.r)("success","Product updated successfully"),e&&(console.log("Calling onSuccess callback for product update"),e(),setTimeout(()=>{console.log("Calling delayed onSuccess to ensure table refresh after product update"),e()},300)),a.data}catch(e){throw console.error("Update product error:",e),(0,y.r)("error",e.message||"Failed to update product"),e}},isUpdating:s}};var Y=s(63285),K=s(87181),$=s(21455),J=s.n($),X=s(7045);s(41308);var Z=s(46702);let ee=e=>{let{imageUrl:t,setImageUrl:s,form:l}=e,[i,o]=a.useState(!1),[c,d]=a.useState(!1),{getRootProps:m,getInputProps:x,isDragActive:p}=(0,Z.VB)({accept:{"image/*":[".jpeg",".jpg",".png",".gif",".webp"]},maxFiles:1,onDrop:async e=>{if(e.length>0)try{o(!0),d(!1);let t=await (0,X.iG)(e[0]);s(t),l.setFieldsValue({imageUrl:t}),O.Ay.success("Image uploaded successfully")}catch(e){console.error("Upload error:",e),O.Ay.error("Failed to upload image"),s(null),l.setFieldsValue({imageUrl:null})}finally{o(!1)}}});return(0,r.jsxs)("div",{className:"flex flex-col items-start",children:[(0,r.jsxs)("div",{...m(),className:"border-2 border-dashed rounded-lg p-6 w-full text-center cursor-pointer transition-all duration-200 ".concat(p?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"),children:[(0,r.jsx)("input",{...x()}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"text-2xl text-gray-400"})}),p?(0,r.jsx)("p",{className:"text-blue-500 font-medium",children:"Drop the image here..."}):(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-gray-600 font-medium",children:"Drag & drop an image here, or click to select"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: JPG, PNG, GIF, WEBP"})]})]})]}),t&&!c&&(0,r.jsxs)("div",{className:"mt-4 relative group",children:[(0,r.jsx)(b.default,{src:t,alt:"Product",width:200,height:200,className:"object-cover border border-gray-200 rounded-lg shadow-sm",onError:()=>{console.error("Image failed to load:",t),d(!0),s(null),l.setFieldsValue({imageUrl:null}),O.Ay.error("Failed to load image preview")}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,r.jsx)(n.Ay,{type:"text",danger:!0,icon:(0,r.jsx)(D.A,{}),onClick:e=>{e.stopPropagation(),s(null),l.setFieldsValue({imageUrl:null})},className:"text-white hover:text-red-500",children:"Remove"})})]}),i&&(0,r.jsxs)("div",{className:"mt-2 text-blue-500 flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-2"}),"Uploading..."]})]})},{Option:et}=B.A,es=e=>{var t;let{isOpen:s,onClose:l,onSuccess:i,product:o,currentUser:c}=e,[u]=_.A.useForm(),m=!!o,[x,p]=(0,a.useState)(null),[h,g]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),j=(null==c?void 0:c.role)||"cashier",{data:b,isLoading:v}=(0,T.lg)({page:1,limit:100}),w=(null==b?void 0:null===(t=b.data)||void 0===t?void 0:t.categories)||[],{createProduct:A,isCreating:k}=W(i),{updateProduct:C,isUpdating:P}=Q(i),D=k||P;(0,a.useEffect)(()=>{s&&(m&&o?(u.setFieldsValue({...o,expiryDate:o.expiryDate?J()(o.expiryDate):void 0,price:parseFloat(o.price),cost:parseFloat(o.cost),imageUrl:o.imageUrl||null}),p(o.imageUrl||null)):(u.resetFields(),p(null)))},[s,o,u,m]);let{getRootProps:F,getInputProps:I,isDragActive:E}=(0,Z.VB)({accept:{"image/*":[".jpeg",".jpg",".png",".gif",".webp"]},maxFiles:1,onDrop:async e=>{e.length>0&&await z(e[0])}}),z=async e=>{try{g(!0);let t=await (0,X.iG)(e);return p(t),u.setFieldsValue({imageUrl:t}),O.Ay.success("Image uploaded successfully"),!1}catch(e){return O.Ay.error("Failed to upload image"),!1}finally{g(!1)}},U=async e=>{try{let t={...e,price:Number(e.price),cost:Number(e.cost),imageUrl:x||null,expiryDate:e.expiryDate?e.expiryDate.format("YYYY-MM-DD"):null};m&&o?await C(o.id,t):await A(t),l()}catch(e){console.error("Error submitting product:",e),O.Ay.error("Failed to save product")}},L=m?"Edit Product".concat("admin"===j?" (Admin)":""):"Add New Product".concat("admin"===j?" (Admin)":""),M=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:l,disabled:D,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(n.Ay,{type:"primary",loading:D,onClick:()=>u.submit(),children:m?"Update":"Create"})]});return(0,r.jsx)(G.A,{isOpen:s,onClose:l,title:L,width:"500px",footer:M,children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Product: ",null==o?void 0:o.name]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),"Create New Product"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:m?"Update product information using the form below":"Fill in the details to create a new product"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(_.A,{form:u,layout:"vertical",onFinish:U,className:"product-form",requiredMark:!0,onValuesChange:(e,t)=>{if("name"in e&&!y){let t=e.name||"";if(t){let e=t.replace(/[^A-Za-z0-9]/g,"").toUpperCase().slice(0,4),s=Math.floor(1e3+9e3*Math.random()),r="".concat(e,"-").concat(s);u.setFieldsValue({sku:r})}else u.setFieldsValue({sku:""})}},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,r.jsx)(_.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," Product Name"]}),rules:[{required:!0,message:"Please enter product name"}],tooltip:"The name of the product as it will appear to customers",children:(0,r.jsx)(q.A,{placeholder:"Enter product name"})}),(0,r.jsx)(_.A.Item,{name:"categoryId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," Category"]}),rules:[{required:!0,message:"Please select a category"}],tooltip:"The category this product belongs to",children:(0,r.jsx)(B.A,{placeholder:"Select category",loading:v,disabled:v,children:w.map(e=>(0,r.jsx)(et,{value:e.id,children:e.name},e.id))})}),(0,r.jsx)(_.A.Item,{name:"imageUrl",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"mr-1"})," Product Image"]}),tooltip:"Upload a product image (optional)",children:(0,r.jsx)(ee,{imageUrl:x,setImageUrl:p,form:u})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Pricing Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(_.A.Item,{name:"price",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Selling Price (GHS)"]}),rules:[{required:!0,message:"Please enter selling price"}],tooltip:"The price at which you sell the product to customers",children:(0,r.jsx)(V.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00"})}),(0,r.jsx)(_.A.Item,{name:"cost",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Purchase Cost (GHS)"]}),rules:[{required:!0,message:"Please enter purchase cost"}],tooltip:"The cost price you pay to acquire the product",children:(0,r.jsx)(V.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00"})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Inventory Management"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(_.A.Item,{name:"stockQuantity",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," Stock Quantity"]}),initialValue:0,tooltip:"Current quantity in stock",children:(0,r.jsx)(V.A,{min:0,style:{width:"100%"},placeholder:"0"})}),(0,r.jsx)(_.A.Item,{name:"minStockLevel",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," Min Stock Level"]}),initialValue:5,tooltip:"Minimum quantity before restock alert",children:(0,r.jsx)(V.A,{min:0,style:{width:"100%"},placeholder:"5"})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Product Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(_.A.Item,{name:"sku",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Y.A,{className:"mr-1"})," SKU (Optional)"]}),tooltip:"Stock Keeping Unit - unique identifier for inventory management",children:(0,r.jsx)(q.A,{placeholder:"Enter SKU",onChange:()=>f(!0)})}),(0,r.jsx)(_.A.Item,{name:"barcode",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Y.A,{className:"mr-1"})," Barcode (Optional)"]}),tooltip:"Product barcode for scanning",children:(0,r.jsx)(q.A,{placeholder:"Enter barcode"})})]}),(0,r.jsx)(_.A.Item,{name:"expiryDate",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(K.A,{className:"mr-1"})," Expiry Date (Optional)"]}),tooltip:"Date when the product expires",children:(0,r.jsx)(H.A,{style:{width:"100%"}})})]})]})]})})};var er=s(67649);let ea=e=>{let{data:t,error:s,isLoading:r,refetch:a}=(0,p.hi)(e||0,{skip:!e});return{product:(null==t?void 0:t.data)||null,isLoading:r,error:s,refetch:a}},el=e=>{let{isOpen:t,onClose:s,productId:a,onEdit:l}=e,{product:i,isLoading:c}=ea(a),m=e=>{if(!e)return"N/A";try{return J()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},x=e=>{if(!e)return"N/A";try{return new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS"}).format(parseFloat(e))}catch(e){return"Invalid amount"}},p=(0,z.d4)(e=>e.auth.user),h="admin"===(null==p?void 0:p.role)&&(null==p?void 0:p.id)===(null==i?void 0:i.createdBy),g=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,className:"text-white hover:text-white",style:{borderColor:"#6B7280",background:"transparent"},children:"Close"}),l&&i&&h&&(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>l(i.id),children:"Edit"})]});return(0,r.jsx)(G.A,{isOpen:t,onClose:s,title:"Product Details",width:"500px",footer:g,children:c?(0,r.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,r.jsx)(o.A,{indicator:(0,r.jsx)(u.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-2"}),"Product: ",i.name]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete product information and details"})]}),(0,r.jsxs)(er.A,{bordered:!0,column:1,className:"product-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(d.A,{})," Product ID"]}),children:i.id}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(d.A,{})," Name"]}),children:i.name}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(Y.A,{})," SKU"]}),children:i.sku||"N/A"}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(Y.A,{})," Barcode"]}),children:i.barcode||"N/A"}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Price"]}),children:x(i.price)}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Cost"]}),children:x(i.cost)}),(0,r.jsx)(er.A.Item,{label:"Stock Quantity",children:(0,r.jsx)(w.A,{color:i.stockQuantity<=0?"red":i.stockQuantity<=(i.minStockLevel||5)?"orange":"green",children:i.stockQuantity})}),(0,r.jsx)(er.A.Item,{label:"Min Stock Level",children:i.minStockLevel||"N/A"}),(0,r.jsx)(er.A.Item,{label:"Expiry Date",children:m(i.expiryDate)}),(0,r.jsx)(er.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(K.A,{})," Created At"]}),children:m(i.createdAt)})]})]}):(0,r.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,r.jsx)("p",{className:"text-gray-800",children:"No product data available"})})})};var en=s(5413);s(66202);let ei=e=>{let{searchTerm:t,setSearchTerm:s,isMobile:a=!1}=e;return(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(q.A,{placeholder:"Search by name, SKU, or barcode...",prefix:(0,r.jsx)(en.A,{className:"text-gray-500"}),value:t,onChange:e=>{let t=e.target.value;console.log("Product search input changed:",t),s(t)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:a?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),t&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',t,'"']})]})};var eo=s(12467),ec=s(89351),ed=s(34366);let eu=e=>{let[t,{isLoading:s}]=(0,ed.Wn)(),{refetch:r}=(0,p.r3)({page:1,limit:10},{skip:!1});return{createStockAdjustment:async e=>{try{console.log("useStockAdjustmentCreate - Starting stock adjustment creation with data:",e);let s=await t(e).unwrap();if(console.log("useStockAdjustmentCreate - API response:",s),!s.success)throw console.error("useStockAdjustmentCreate - API returned error:",s.message),Error(s.message||"Failed to create stock adjustment");return(0,y.r)("success","Stock adjustment created successfully"),s.data}catch(e){throw console.error("Create stock adjustment error:",e),(0,y.r)("error",e.message||"Failed to create stock adjustment"),e}},isSubmitting:s}};var em=s(96030),ex=s(35153);let{TextArea:ep}=q.A,eh=e=>{let{isOpen:t,onClose:s,onSuccess:l,product:i}=e,[o]=_.A.useForm(),{createStockAdjustment:c,isSubmitting:u}=eu(l),{data:m,refetch:x}=(0,p.hi)((null==i?void 0:i.id)||0,{skip:!(null==i?void 0:i.id)||!t}),h=(null==m?void 0:m.data)||i;(0,a.useEffect)(()=>{t&&i&&(i.id&&x(),o.resetFields(),o.setFieldsValue({adjustmentType:"add",quantityChange:1,reason:""}))},[o,t,i,x]);let g=async e=>{if(h)try{let t="add"===e.adjustmentType?Math.abs(e.quantityChange):-Math.abs(e.quantityChange),s={productId:h.id,quantityChange:t,reason:e.reason};console.log("Submitting stock adjustment:",s);let r=await c(s);console.log("Stock adjustment result:",r),x(),setTimeout(()=>{console.log("Delayed product refetch after stock adjustment"),x()},500),l&&(console.log("Calling onSuccess to refresh the product table"),l())}catch(e){console.error("Failed to create stock adjustment:",e),alert("Failed to adjust stock. Please try again.")}},y=h?"Adjust Stock: ".concat(h.name):"Adjust Stock",f=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,disabled:u,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(n.Ay,{type:"primary",loading:u,onClick:()=>o.submit(),children:"Adjust Stock"})]});return(0,r.jsx)(G.A,{isOpen:t,onClose:s,title:y,width:"500px",footer:f,children:(0,r.jsxs)("div",{className:"p-6 bg-white",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-2"}),"Stock Adjustment"]}),(0,r.jsxs)("p",{className:"text-gray-600 mt-1",children:["Adjust the stock quantity for ",null==h?void 0:h.name]})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(_.A,{form:o,layout:"vertical",onFinish:g,className:"product-form",requiredMark:!0,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Current Stock Information"}),(0,r.jsxs)("div",{className:"bg-gray-100 p-3 rounded mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Current Stock:"}),(0,r.jsx)("span",{className:"text-gray-800 font-medium",children:(null==h?void 0:h.stockQuantity)||0})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-1",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Minimum Stock Level:"}),(0,r.jsx)("span",{className:"text-gray-800 font-medium",children:(null==h?void 0:h.minStockLevel)||0})]})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Adjustment Details"}),(0,r.jsx)(_.A.Item,{name:"adjustmentType",label:(0,r.jsx)("span",{className:"flex items-center text-gray-800",children:"Adjustment Type"}),rules:[{required:!0,message:"Please select adjustment type"}],initialValue:"add",children:(0,r.jsxs)(ec.Ay.Group,{children:[(0,r.jsx)(ec.Ay,{value:"add",className:"text-gray-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(em.A,{className:"mr-1 text-green-500"})," Add Stock"]})}),(0,r.jsx)(ec.Ay,{value:"remove",className:"text-gray-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(ex.A,{className:"mr-1 text-red-500"})," Remove Stock"]})})]})}),(0,r.jsx)(_.A.Item,{name:"quantityChange",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(P.A,{className:"mr-1"})," Quantity"]}),rules:[{required:!0,message:"Please enter quantity"},{type:"number",min:1,message:"Quantity must be at least 1"}],initialValue:1,tooltip:"The quantity to add or remove",children:(0,r.jsx)(V.A,{min:1,style:{width:"100%"},placeholder:"Enter quantity"})}),(0,r.jsx)(_.A.Item,{name:"reason",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(P.A,{className:"mr-1"})," Reason"]}),rules:[{required:!0,message:"Please enter reason for adjustment"}],tooltip:"Why you are adjusting the stock",children:(0,r.jsx)(ep,{rows:4,placeholder:"Enter reason for adjustment"})})]})]})]})})},eg=()=>{let{user:e}=(0,m.A)(),t=(0,x.a)(),[s,p]=(0,a.useState)(!1),[h,y]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),[w,A]=(0,a.useState)(!1),[N,k]=(0,a.useState)(null),[S,C]=(0,a.useState)(null),[P,D]=(0,a.useState)(!1),[F,I]=(0,a.useState)(0),{products:E,total:T,page:z,limit:L,isLoading:M,refetch:B,forceRefresh:_,searchTerm:O,setSearchTerm:q,handlePageChange:V}=g(),{deleteProduct:H,isDeleting:G}=f(()=>{D(!1),_()}),{bulkDeleteProducts:W,isDeleting:Q}=j(()=>{K(!1),_()}),[Y,K]=(0,a.useState)(!1),[$,J]=(0,a.useState)([]),X=(null==e?void 0:e.role)==="admin",Z=async()=>{S&&await H(S)},ee=async()=>{if(console.log("confirmBulkDelete called with products:",$),$.length>0)try{await W($)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,a.useEffect)(()=>{F>0&&(console.log("Refreshing products due to counter change: ".concat(F)),B())},[F,B]),(0,r.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Product Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(c.A,{}),onClick:()=>{i.Ay.info({message:"Refreshing Products",description:"Getting the latest product data...",icon:(0,r.jsx)(c.A,{style:{color:"#108ee9"}}),duration:2}),_()},size:t?"small":"middle",children:t?"":"Refresh"}),X&&(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(d.A,{}),onClick:()=>{p(!0)},size:t?"small":"middle",children:t?"":"Add Product"})]}),children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,r.jsx)(ei,{searchTerm:O,setSearchTerm:q,isMobile:t}),M?(0,r.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,r.jsx)(o.A,{indicator:(0,r.jsx)(u.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[E.length>0?(0,r.jsx)(U,{products:E,loading:!1,onView:e=>{C(e),v(!0)},onEdit:e=>{k(e),y(!0)},onDelete:e=>{C(e),D(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with productIds:",e),J(e),K(!0)},onAdjustStock:e=>{k(e),A(!0)},isMobile:t}):(0,r.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No products found matching your search criteria."}),(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>q(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,r.jsxs)("p",{children:["No products found. ",X&&"Click 'Add Product' to create one."]})}),E.length>0&&(0,r.jsx)(R,{current:z,pageSize:L,total:T,onChange:V,isMobile:t})]})]})}),(0,r.jsx)(es,{isOpen:s,onClose:()=>p(!1),onSuccess:()=>{p(!1),i.Ay.success({message:"Product Added Successfully",description:"Refreshing product data...",icon:(0,r.jsx)(c.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Product creation completed, refreshing product data"),_()},currentUser:e}),(0,r.jsx)(es,{isOpen:h,onClose:()=>y(!1),onSuccess:()=>{y(!1),i.Ay.success({message:"Product Updated Successfully",description:"Refreshing product data...",icon:(0,r.jsx)(c.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Product update completed, refreshing product data"),_()},product:N,currentUser:e}),(0,r.jsx)(el,{isOpen:b,onClose:()=>{v(!1),C(null)},productId:S,onEdit:e=>{let t=E.find(t=>t.id===e)||null;t&&(k(t),v(!1),y(!0))}}),(0,r.jsx)(eo.A,{isOpen:P,onClose:()=>{D(!1),C(null)},onConfirm:Z,title:"Delete Product",message:"Are you sure you want to delete this product? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:G,type:"danger"}),(0,r.jsx)(eo.A,{isOpen:Y,onClose:()=>{K(!1),J([])},onConfirm:ee,title:"Delete Multiple Products",message:"Are you sure you want to delete ".concat($.length," products? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:Q,type:"danger"}),(0,r.jsx)(eh,{isOpen:w,onClose:()=>A(!1),onSuccess:()=>{A(!1),i.Ay.success({message:"Stock Adjustment Successful",description:"Refreshing product data...",icon:(0,r.jsx)(c.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Stock adjustment completed, refreshing product data"),_()},product:N})]})}},12467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155);s(12115);var a=s(46102),l=s(43316),n=s(75218);let i=e=>{let{isOpen:t,onClose:s,onConfirm:i,title:o,message:c,confirmText:d="Confirm",cancelText:u="Cancel",isLoading:m=!1,type:x="danger"}=e;return(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{style:{color:"danger"===x?"#ff4d4f":"warning"===x?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:o})]}),open:t,onCancel:s,footer:[(0,r.jsx)(l.Ay,{onClick:s,disabled:m,children:u},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===x?"primary":"default",danger:"danger"===x,onClick:i,loading:m,children:d},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:c})})}},60102:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,Hj:()=>o,jB:()=>l,nA:()=>i});var r=s(95155);s(12115);var a=s(21567);let l=e=>{let{children:t,columns:s,className:l,minWidth:n="800px"}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:s,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],s),children:t})},i=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],s),children:t})},o=e=>{let{children:t,className:s,selected:l=!1,onClick:n}=e;return(0,r.jsx)("div",{className:(0,a.cn)("contents",l&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",s),onClick:n,children:t})}},24988:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155),a=s(12115),l=s(43316),n=s(79624);let i=e=>{let{isOpen:t,onClose:s,title:i,children:o,width:c="400px",footer:d,fullWidth:u=!1}=e,[m,x]=(0,a.useState)(!1),[p,h]=(0,a.useState)(!1),[g,y]=(0,a.useState)(window.innerWidth);if((0,a.useEffect)(()=>{let e=()=>{y(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)h(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{x(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{x(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{h(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!p)return null;let f="Point of Sale"===i||u||"100vw"===c;return(0,r.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(f?"sales-panel-container":""),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:s}),(0,r.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||u||"100vw"===c||g<640?"100vw":g<1024?"500px":"string"==typeof c&&c.includes("px")&&parseInt(c)>600?"600px":c},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(n.A,{style:{color:"#333"}}),onClick:s,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:o}),d&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:d})]})]})}},30555:(e,t,s)=>{"use strict";s.d(t,{a:()=>a});var r=s(12115);function a(){let[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},36060:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(83391),a=s(70854),l=s(63065),n=s(7875);let i=()=>{let e=(0,r.wA)(),{user:t,accessToken:s}=(0,r.d4)(e=>e.auth),i=(0,a._)(),{refetch:o}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!s,d=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let r=await o();console.log("useAuth - Refetch result:",r);let a=r.data;if((null==a?void 0:a.success)&&(null==a?void 0:a.data)){console.log("useAuth - API response data:",a.data);let r=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=a.data.phone||t.phone||"",i=a.data.createdAt||t.createdAt||"",o=a.data.lastPaymentDate||t.lastPaymentDate||void 0,c=a.data.nextPaymentDue||t.nextPaymentDue||null,d=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...a.data,phone:l,createdAt:i,lastPaymentDate:o,nextPaymentDue:c,createdBy:d,paymentStatus:r};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:u,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==a?void 0:a.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:d}}},70854:(e,t,s)=>{"use strict";s.d(t,{_:()=>i});var r=s(12115),a=s(83391),l=s(21455),n=s.n(l);let i=()=>{let e=(0,a.d4)(e=>e.auth.user),[t,s]=(0,r.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,r.useEffect)(()=>{if(!e){s({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,r=!1,a=!0,l="inactive";if("superadmin"===e.role){s({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){r=!0,a=!1,l="active";let s=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),i=n()();if(t=l.diff(i,"day"),s){let s=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:s,daysRemaining:t,trialDaysUsed:s,trialDaysRemaining:t,isActive:r,needsPayment:a})}}}else"pending"===e.paymentStatus?(r=!1,a=!0,l="pending"):"overdue"===e.paymentStatus?(r=!1,a=!0,l="overdue"):(r=!1,a=!0,l="inactive");s({isActive:r,daysRemaining:t,status:l,needsPayment:a})},[e]),t}},9273:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(12115);function a(e,t){let[s,a]=(0,r.useState)(e);return(0,r.useEffect)(()=>{console.log("Debouncing value:",e);let s=setTimeout(()=>{console.log("Debounce timer completed, setting value:",e),a(e)},t);return()=>{clearTimeout(s)}},[e,t]),s}},91256:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(12115);let a=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(43463),a=s(69795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},7045:(e,t,s)=>{"use strict";s.d(t,{fS:()=>l,iG:()=>i,jY:()=>n});let r="dutmiedgk",a="pos_receipts",l=async e=>{try{var t;let l=document.createElement("div");l.innerHTML=e,l.style.width="350px",l.style.padding="20px",l.style.backgroundColor="white",l.style.color="#555555",l.style.fontFamily="Arial, sans-serif",l.style.position="absolute",l.style.left="-9999px",l.style.borderLeft="1px solid #000000",l.style.borderRight="1px solid #000000",l.style.borderTop="1px solid #000000",l.style.borderBottom="1px solid #000000",l.style.boxSizing="border-box",l.style.fontSize="14px",l.style.lineHeight="1.5",document.body.appendChild(l);let n=(await s.e(4316).then(s.t.bind(s,40078,23))).default,i=await n(l,{scale:3,backgroundColor:"white",logging:!1,width:350,height:l.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(l);let o=await new Promise(e=>{i.toBlob(t=>{e(t)},"image/png",.95)}),c=new FormData;c.append("file",o),c.append("upload_preset",a);let d=await fetch("https://api.cloudinary.com/v1_1/".concat(r,"/image/upload"),{method:"POST",body:c}),u=await d.json();if(d.ok)return u.secure_url;throw console.error("Cloudinary upload failed:",u),Error((null===(t=u.error)||void 0===t?void 0:t.message)||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},n=(e,t)=>{let s=new Date(e.transactionDate),r=s.toLocaleDateString(),a=s.toLocaleTimeString(),l=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return'\n  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">\n\n  \x3c!-- Header and Title --\x3e\n  <div style="text-align: center; margin-bottom: 10px;">\n    <div style="font-size: 18px; font-weight: bold;">'.concat(t.name||"POS System",'</div>\n    <div style="font-size: 16px; font-weight: bold;">#INV-').concat(e.id,"-").concat(new Date().getFullYear()).concat((new Date().getMonth()+1).toString().padStart(2,"0")).concat(new Date().getDate().toString().padStart(2,"0"),'</div>\n    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">\n      ').concat(t.address?"<div>".concat(t.address,"</div>"):"","\n      ").concat(t.city?"<div>".concat(t.city,"</div>"):"","\n      ").concat(t.country?"<div>".concat(t.country,"</div>"):"",'\n    </div>\n  </div>\n\n  \x3c!-- Items Table --\x3e\n  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">\n    <thead>\n      <tr>\n        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>\n      </tr>\n    </thead>\n    <tbody>\n      ').concat(e.items.map((e,t)=>{let s="string"==typeof e.price?parseFloat(e.price):e.price,r=s*e.quantity;return'\n          <tr>\n            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">'.concat(t+1,". ").concat(e.productName,'</td>\n            <td style="text-align: right;">').concat(e.quantity,'</td>\n            <td style="text-align: right;">').concat(s.toFixed(2),'</td>\n            <td style="text-align: right;">').concat(r.toFixed(2),"</td>\n          </tr>\n        ")}).join(""),'\n    </tbody>\n  </table>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Totals --\x3e\n  <div style="display: flex; justify-content: space-between; font-weight: bold;">\n    <div>TOTAL</div>\n    <div>GHS ').concat("string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2),'</div>\n  </div>\n  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">\n    <div>TAX</div>\n    <div>0.00</div>\n  </div>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Payment Info --\x3e\n  <div style="font-size: 11px; margin-bottom: 6px;">\n    <div>Payment: ').concat(l.toUpperCase(),"</div>\n    ").concat(l.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":"",'\n  </div>\n\n  \x3c!-- Date/Time --\x3e\n  <div style="display: flex; justify-content: space-between; font-size: 11px;">\n    <div><strong>Time:</strong> ').concat(a,"</div>\n    <div><strong>Date:</strong> ").concat(r,'</div>\n  </div>\n\n  \x3c!-- Barcode --\x3e\n  <div style="text-align: center; margin: 12px 0;">\n    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>\n    <div style="font-size: 11px; margin-top: 4px;">').concat(e.id,'</div>\n  </div>\n\n  \x3c!-- Footer --\x3e\n  <div style="text-align: center; font-size: 12px; margin-top: 8px;">\n    THANK YOU!\n  </div>\n</div>\n\n')},i=async e=>{try{var t;let s=new FormData;s.append("file",e),s.append("upload_preset",a),s.append("folder","products");let l=await fetch("https://api.cloudinary.com/v1_1/".concat(r,"/image/upload"),{method:"POST",body:s}),n=await l.json();if(l.ok)return n.secure_url;throw console.error("Cloudinary upload failed:",n),Error((null===(t=n.error)||void 0===t?void 0:t.message)||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},75912:(e,t,s)=>{"use strict";s.d(t,{r:()=>a});var r=s(55037);let a=(e,t)=>{"success"===e?r.oR.success(t):"error"===e?r.oR.error(t):"warning"===e&&(0,r.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)},41308:()=>{},66202:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[9141,8059,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,5565,5211,1637,1554,5632,821,8441,1517,7358],()=>t(53745)),_N_E=e.O()}]);