import { Dispatch } from '@reduxjs/toolkit';
import { setUser } from '@/reduxRTK/services/authSlice';
import { userApi } from '@/reduxRTK/services/authApi';

/**
 * Direct user refresh that bypasses all caching and gets fresh data from backend
 * This is the most aggressive approach to ensure we get the latest user data
 */
export const directUserRefresh = async (
  dispatch: Dispatch,
  accessToken: string,
  userId: number
): Promise<{ success: boolean; user?: any; error?: string }> => {
  try {
    console.log('🔄 DIRECT USER REFRESH - Starting aggressive refresh for user:', userId);

    // Step 1: Clear only RTK Query cache, keep auth data to prevent logout
    console.log('🧹 Step 1: Clearing RTK Query cache (keeping auth data)');
    try {
      // Clear only non-auth cache
      localStorage.removeItem('user_cache');
      localStorage.removeItem('payment_status_cache');
      console.log('✅ Non-auth cache cleared');
    } catch (e) {
      console.log('⚠️ Error clearing cache:', e);
    }

    // Step 2: Clear ALL RTK Query cache
    console.log('🧹 Step 2: Clearing ALL RTK Query cache');
    dispatch(userApi.util.resetApiState());

    // Step 3: Wait for cache clearing
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 4: Make direct API call to backend
    console.log('📡 Step 4: Making direct API call to backend');
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        mode: 'retrieve',
        userId: userId
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📡 Direct API response:', data);

    if (data.success && data.data) {
      const freshUser = data.data;

      console.log('✅ Fresh user data from direct API call:', {
        id: freshUser.id,
        paymentStatus: freshUser.paymentStatus,
        lastPaymentDate: freshUser.lastPaymentDate,
        nextPaymentDue: freshUser.nextPaymentDue
      });

      // Step 5: Update Redux store with fresh data
      console.log('💾 Step 5: Updating Redux store with fresh data');
      dispatch(setUser({ user: freshUser, accessToken }));

      // Step 6: Verify the update
      console.log('✅ Step 6: Direct refresh completed successfully');

      return { success: true, user: freshUser };
    } else {
      throw new Error(data.message || 'Failed to fetch user data');
    }
  } catch (error: any) {
    console.error('❌ Direct user refresh failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Emergency user refresh - tries multiple methods
 */
export const emergencyUserRefresh = async (
  dispatch: Dispatch,
  accessToken: string,
  userId: number
): Promise<{ success: boolean; user?: any; error?: string }> => {
  console.log('🚨 EMERGENCY USER REFRESH - Trying all methods');

  // Method 1: Direct API call
  console.log('🔄 Method 1: Direct API call');
  const directResult = await directUserRefresh(dispatch, accessToken, userId);
  if (directResult.success) {
    console.log('✅ Method 1 succeeded');
    return directResult;
  }

  // Method 2: RTK Query with force refetch
  console.log('🔄 Method 2: RTK Query with force refetch');
  try {
    const result = await (dispatch as any)(userApi.endpoints.getUserById.initiate(userId, {
      forceRefetch: true
    }));

    if ('data' in result && result.data?.success && result.data.data) {
      const freshUser = result.data.data;
      console.log('✅ Method 2 succeeded');
      dispatch(setUser({ user: freshUser, accessToken }));
      return { success: true, user: freshUser };
    }
  } catch (error: any) {
    console.error('❌ Method 2 failed:', error);
  }

  // Method 3: getCurrentUser endpoint
  console.log('🔄 Method 3: getCurrentUser endpoint');
  try {
    const result = await (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, {
      forceRefetch: true
    }));

    if ('data' in result && result.data?.success && result.data.data) {
      const freshUser = result.data.data;
      console.log('✅ Method 3 succeeded');
      dispatch(setUser({ user: freshUser, accessToken }));
      return { success: true, user: freshUser };
    }
  } catch (error: any) {
    console.error('❌ Method 3 failed:', error);
  }

  console.log('❌ All emergency refresh methods failed');
  return { success: false, error: 'All refresh methods failed' };
};
