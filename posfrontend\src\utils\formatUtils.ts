/**
 * Format a number as a compact string (e.g., 1000 -> 1K)
 * @param value The number to format
 * @returns The formatted string
 */
export const compactFormat = (value: number): string => {
  if (value === 0) return "0";

  const formatter = new Intl.NumberFormat("en", {
    notation: "compact",
    maximumFractionDigits: 1,
  });

  return formatter.format(value);
};

/**
 * Format a number as currency (Cedis)
 * @param value The number to format
 * @returns The formatted string with currency symbol
 */
export const formatCurrency = (value: number): string => {
  if (value === 0) return "GH₵0.00";

  try {
    // Format the number with commas and 2 decimal places
    const formattedNumber = new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);

    // Add the GH₵ symbol manually to ensure it displays correctly
    return `GH₵${formattedNumber}`;
  } catch (error) {
    console.error("Error formatting currency:", error);
    // Fallback formatting
    return `GH₵${value.toFixed(2)}`;
  }
};

/**
 * Format a growth rate with more meaningful indicators
 * @param value The growth rate as a decimal (e.g., 0.05 for 5%)
 * @returns A meaningful growth indicator without percentage symbols
 */
export const formatGrowthRate = (value: number): string => {
  // For zero growth, show nothing
  if (value === 0) {
    return "";
  }

  // For very small positive growth (like first month)
  if (value > 0 && value < 0.02) {
    return "New";
  }

  // For significant growth, show "Growing" instead of percentages
  if (value > 0.1) {
    return "Growing";
  }

  // For moderate growth
  if (value > 0) {
    return "Stable";
  }

  // For decline
  if (value < 0) {
    return "Declining";
  }

  // Default case - empty string
  return "";
};
