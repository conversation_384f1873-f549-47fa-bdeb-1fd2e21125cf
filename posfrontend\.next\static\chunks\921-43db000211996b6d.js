(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[921],{9124:(e,t,a)=>{"use strict";a.d(t,{A:()=>C});var n=a(95155),s=a(12115),l=a(5565),i=a(83414),o=a(41657),r=a(21614),d=a(43316),c=a(46102),u=a(7974),m=a(72093),p=a(5413),g=a(39279),x=a(5099),h=a(27656),y=a(53452),v=a(21633),f=a(93968),b=a(33429),w=a(10927),j=a(24988),N=a(75912);let A=e=>{let{onProductFound:t,onProductNotFound:a}=e,[n,l]=(0,s.useState)(!1),[i,o]=(0,s.useState)(""),{data:r,isLoading:d,refetch:c}=(0,f.eb)(i,{skip:!i,refetchOnMountOrArgChange:!0});return{isOpen:n,openScanner:(0,s.useCallback)(()=>{l(!0)},[]),closeScanner:(0,s.useCallback)(()=>{l(!1),o("")},[]),handleBarcodeScanned:(0,s.useCallback)(async e=>{if(console.log("Camera barcode scanned:",e),!e||""===e.trim()){(0,N.r)("error","Invalid barcode scanned");return}let n=e.trim();console.log("Searching for barcode:",n),o(n),setTimeout(async()=>{try{var e;let s=await c();if(console.log("Barcode search result:",s),(null===(e=s.data)||void 0===e?void 0:e.success)&&s.data.data){let e=s.data.data;console.log("Product found via camera:",e),(0,N.r)("success","Product found: ".concat(e.name)),t(e)}else console.log("No product found for barcode:",n),console.log("API response:",s.data),(0,N.r)("warning","No product found for barcode: ".concat(n)),a&&a(n)}catch(e){console.error("Error searching for product:",e),(0,N.r)("error","Error searching for product")}},100)},[c,t,a]),isLoading:d}};var S=a(7045);a(23411),a(42397);let C=e=>{var t,a,C,k,P,D,F,E;let{isOpen:T,onClose:I,onSuccess:O}=e,[L]=i.A.useForm(),[R]=i.A.useForm(),[_,Q]=(0,s.useState)([]),[q,z]=(0,s.useState)(null),[U,B]=(0,s.useState)(1),[W,M]=(0,s.useState)(0),[V,H]=(0,s.useState)(""),[Y,G]=(0,s.useState)(null),[X,$]=(0,s.useState)(!1),[J,K]=(0,s.useState)(!1),[Z,ee]=(0,s.useState)(null),[et,ea]=(0,s.useState)(!1),[en,es]=(0,s.useState)(""),[el,ei]=(0,s.useState)("Walk-in Customer"),[eo,er]=(0,s.useState)(!1),[ed,ec]=(0,s.useState)([]),[eu,em]=(0,s.useState)(!1),[ep,eg]=(0,s.useState)(null),[ex,eh]=(0,s.useState)(null),{isOpen:ey,openScanner:ev,closeScanner:ef,handleBarcodeScanned:eb,isLoading:ew}=A({onProductFound:e=>{z(e),B(1),R&&R.setFieldsValue({productId:e.id,quantity:1}),setTimeout(()=>{eQ()},100)},onProductNotFound:e=>{console.log("Product not found for barcode:",e)}}),ej=async e=>{if(es(e),e&&e.length>=8)try{var t;console.log("Searching for product with barcode:",e);let a=(null==eN?void 0:null===(t=eN.data)||void 0===t?void 0:t.products)||[],n=a.find(t=>t.barcode===e.trim());if(n||(n=a.find(t=>t.sku===e.trim())),n){if(console.log("Product found via hardware scanner:",n),n.stockQuantity<=0){(0,N.r)("error","".concat(n.name," is out of stock")),es("");return}let e={productId:n.id,productName:n.name,price:n.price,quantity:1},t=_.findIndex(e=>e.productId===n.id);if(t>=0&&_[t].quantity+1>n.stockQuantity){(0,N.r)("error","Only ".concat(n.stockQuantity," units available in stock")),es("");return}Q(t=>[...t,{...e,price:Number(e.price)}]),es(""),(0,N.r)("success","Product added: ".concat(n.name))}else console.log("No product found for barcode:",e),(0,N.r)("warning","No product found for barcode: ".concat(e))}catch(e){console.error("Error searching for product:",e),(0,N.r)("error","Error searching for product")}};(0,s.useEffect)(()=>{console.log("Items state changed:",_)},[_]);let{data:eN,isLoading:eA,refetch:eS}=(0,f.r3)({page:1,limit:1e3,search:V},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),{data:eC,isLoading:ek}=(0,w.lg)({page:1,limit:100,search:""});(0,s.useEffect)(()=>{if(eN){var e,t,a;console.log("\uD83D\uDED2 Products loaded:",{total:(null===(e=eN.data)||void 0===e?void 0:e.total)||0,productsCount:(null===(a=eN.data)||void 0===a?void 0:null===(t=a.products)||void 0===t?void 0:t.length)||0,isLoading:eA})}},[eN,eA]);let eP=()=>{{var e,t;let a=window.__REDUX_STATE;return(null==a?void 0:null===(t=a.auth)||void 0===t?void 0:null===(e=t.user)||void 0===e?void 0:e.id)||0}},{data:eD}=(0,b.lr)(eP()),{data:eF}=(0,b.Rv)(eP()),eE={firstName:"Admin"},eT=e=>{let t=e.toLowerCase();return t.includes("electronic")||t.includes("tech")||t.includes("phone")||t.includes("computer")?"\uD83D\uDCF1":t.includes("clothing")||t.includes("fashion")||t.includes("apparel")||t.includes("wear")?"\uD83D\uDC55":t.includes("food")||t.includes("grocery")||t.includes("snack")||t.includes("drink")?"\uD83C\uDF55":t.includes("book")||t.includes("education")||t.includes("stationery")||t.includes("office")?"\uD83D\uDCDA":t.includes("toy")||t.includes("game")||t.includes("play")||t.includes("kid")?"\uD83E\uDDF8":t.includes("health")||t.includes("medical")||t.includes("pharmacy")||t.includes("care")?"\uD83D\uDC8A":t.includes("beauty")||t.includes("cosmetic")||t.includes("makeup")||t.includes("skincare")?"\uD83D\uDC84":t.includes("sport")||t.includes("fitness")||t.includes("gym")||t.includes("exercise")?"⚽":t.includes("home")||t.includes("furniture")||t.includes("decor")||t.includes("kitchen")?"\uD83C\uDFE0":t.includes("auto")||t.includes("car")||t.includes("vehicle")||t.includes("motor")?"\uD83D\uDE97":"\uD83C\uDFF7️"},eI=()=>{em(!0)},eO=e=>{Q(e.items),ei(e.customer),M(e.totalAmount),eg(e.id),em(!1),(0,N.r)("success","Loaded saved sale: ₵".concat(e.totalAmount.toFixed(2)))},eL=e=>{ec(t=>t.filter(t=>t.id!==e)),(0,N.r)("success","Saved sale deleted")};(0,s.useEffect)(()=>{(null==eF?void 0:eF.data)?(G(eF.data),L.setFieldsValue({storeId:eF.data.id})):(null==eD?void 0:eD.data)&&eD.data.length>0&&(G(eD.data[0]),L.setFieldsValue({storeId:eD.data[0].id}))},[eF,eD,L]);let[eR,{isLoading:e_}]=(0,v.E$)();(0,s.useEffect)(()=>{if(_&&_.length>0){let e=_.reduce((e,t)=>e+t.price*t.quantity,0);M(e),L&&L.setFieldsValue({totalAmount:e}),console.log("Current items in useEffect:",_)}else M(0),L&&L.setFieldsValue({totalAmount:0})},[_,L]),(0,s.useEffect)(()=>{T?(console.log("\uD83D\uDED2 Sales panel opened - fetching fresh product data"),eS()):(L&&L.resetFields(),R&&R.resetFields(),Q([]),z(null),B(1),M(0),ee(null),K(!1),ea(!1))},[T,L,R,eS]);let eQ=()=>{if(!q){(0,N.r)("error","Please select a product");return}if(U<=0){(0,N.r)("error","Quantity must be greater than 0");return}if(q.stockQuantity<=0){(0,N.r)("error","".concat(q.name," is out of stock"));return}let e=_.findIndex(e=>e.productId===q.id);if(e>=0){let t=_[e].quantity+U;t>q.stockQuantity&&(t=q.stockQuantity,(0,N.r)("error","Cannot add more than ".concat(q.stockQuantity," units. Quantity set to maximum available.")));let a=[..._];a[e]={...a[e],quantity:t},Q(a),(0,N.r)("success","Updated quantity of ".concat(q.name," to ").concat(t))}else{let e=U;e>q.stockQuantity&&(e=q.stockQuantity,(0,N.r)("error","Cannot add more than ".concat(q.stockQuantity," units in stock. Quantity set to maximum available."))),Q([..._,{productId:q.id,productName:q.name,quantity:e,price:"string"==typeof q.price?parseFloat(q.price):q.price}]),(0,N.r)("success","Added ".concat(e," ").concat(q.name," to sale"))}z(null),B(1),R&&R.setFieldsValue({productId:void 0,quantity:1})},eq=e=>{let t=[..._];t.splice(e,1),Q(t),(0,N.r)("success","Item removed from sale")},ez=(e,t)=>{var a;if(t<=0){eq(e);return}let n=_[e],s=null==eN?void 0:null===(a=eN.data)||void 0===a?void 0:a.products.find(e=>e.id===n.productId);if(s&&t>s.stockQuantity){(0,N.r)("error","Only ".concat(s.stockQuantity," units available in stock"));return}let l=[..._];l[e]={...n,quantity:t},Q(l),(0,N.r)("success","Quantity updated")},eU=(0,s.useCallback)(()=>{if(!Z||et){console.log("Skipping print: ",Z?"Already printed":"No receipt URL");return}console.log("Printing receipt:",Z),ea(!0);let e=document.createElement("iframe");e.style.display="none",document.body.appendChild(e),e.onload=()=>{e.contentWindow&&(e.contentWindow.document.write('\n          <!DOCTYPE html>\n          <html>\n            <head>\n              <title>Print Receipt</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 0;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                }\n                @media print {\n                  body {\n                    margin: 0;\n                    padding: 0;\n                  }\n                  img {\n                    width: 100%;\n                    height: auto;\n                  }\n                }\n              </style>\n            </head>\n            <body>\n              <img src="'.concat(Z,'" alt="Receipt" />\n            </body>\n          </html>\n        ')),e.contentWindow.document.close(),setTimeout(()=>{if(e.contentWindow){try{e.contentWindow.focus(),e.contentWindow.print()}catch(e){console.error("Error printing receipt:",e)}setTimeout(()=>{document.body.removeChild(e)},1e3)}},500))},e.src="about:blank"},[Z,et]);(0,s.useEffect)(()=>{if(J&&Z&&!et){let e=setTimeout(()=>{eU()},800);return()=>clearTimeout(e)}},[J,Z,et,eU]);let eB=async()=>{var e,t;try{if(0===_.length){(0,N.r)("error","Please add at least one item to the sale");return}let t=await L.validateFields();if(!Y){(0,N.r)("error","No store information available. Please set up your store in your profile settings.");return}$(!0);let a=Y||(null==eD?void 0:null===(e=eD.data)||void 0===e?void 0:e.find(e=>e.id===t.storeId))||{name:"POS System"},n=(0,S.jY)({id:Date.now(),totalAmount:W,paymentMethod:t.paymentMethod,transactionDate:new Date().toISOString(),items:_.map(e=>({productName:e.productName,quantity:e.quantity,price:e.price}))},a),s="https://receipt.example.com/placeholder";try{s=await (0,S.fS)(n)}catch(e){console.error("Failed to generate receipt image:",e)}let l={totalAmount:W,paymentMethod:t.paymentMethod,items:_.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:s,storeId:null==Y?void 0:Y.id},i=await eR(l).unwrap();i.success?((0,N.r)("success","Sale created successfully"),ep&&(ec(e=>e.filter(e=>e.id!==ep)),eg(null),(0,N.r)("success","Saved sale completed and removed from pending list")),ee(s),K(!0),eS(),setTimeout(()=>{O&&eS()},300)):(0,N.r)("error",i.message||"Failed to create sale")}catch(e){(0,N.r)("error",(null===(t=e.data)||void 0===t?void 0:t.message)||"An error occurred while creating the sale")}finally{$(!1)}};return console.log("Rendering with items:",_),(0,n.jsxs)(j.A,{title:"Point of Sale",isOpen:T,onClose:I,width:"100vw",fullWidth:!0,children:[(0,n.jsxs)("div",{className:"pos-panel h-screen bg-gray-100 flex flex-col",children:[(0,n.jsxs)("div",{className:"bg-gray-800 text-white px-4 py-2 flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("span",{className:"text-lg font-bold",children:"NEXAPO"}),(0,n.jsx)("span",{className:"text-sm",children:"POS System - v1.0.0"})]}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsxs)("span",{className:"text-sm",children:["User: ",(null==eE?void 0:eE.firstName)||"Admin"]})})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col lg:flex-row",children:[(0,n.jsxs)("div",{className:"flex-1 p-2 lg:p-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 mb-3",children:[(0,n.jsx)(o.A,{placeholder:"Search products...",value:V,onChange:e=>H(e.target.value),className:"h-10 rounded-md border w-full",suffix:(0,n.jsx)(p.A,{})}),(0,n.jsx)(o.A,{placeholder:"Barcode scanner...",value:en,onChange:e=>ej(e.target.value),className:"h-10 rounded-md border w-full",style:{borderColor:"#10b981",backgroundColor:"#f0fdf4"}}),(0,n.jsxs)(r.A,{showSearch:!0,allowClear:!0,placeholder:"Filter by category",value:null!=ex?ex:void 0,onChange:e=>eh(null!=e?e:null),optionFilterProp:"label",filterOption:(e,t)=>{var a;return String(null!==(a=null==t?void 0:t.label)&&void 0!==a?a:"").toLowerCase().includes(e.toLowerCase())},className:"h-10 rounded-md border w-full",dropdownStyle:{fontSize:14},loading:ek,bordered:!0,children:[(0,n.jsx)(r.A.Option,{value:null,label:"All",children:"✨ All"}),null==eC?void 0:null===(a=eC.data)||void 0===a?void 0:null===(t=a.categories)||void 0===t?void 0:t.map(e=>(0,n.jsxs)(r.A.Option,{value:e.id,label:"".concat(eT(e.name)," ").concat(e.name),children:[eT(e.name)," ",e.name]},e.id))]})]}),(null==eN?void 0:null===(k=eN.data)||void 0===k?void 0:null===(C=k.products)||void 0===C?void 0:C.length)===0?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center py-10 text-gray-500",children:[(0,n.jsx)(g.A,{className:"text-4xl mb-4"}),(0,n.jsx)("p",{children:"No products found. Please add products to start selling."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-3 mb-3 lg:mb-4",children:null==eN?void 0:null===(E=eN.data)||void 0===E?void 0:null===(F=E.products)||void 0===F?void 0:null===(D=F.filter(e=>{var t,a;let n=!V||e.name.toLowerCase().includes(V.toLowerCase())||(null===(t=e.barcode)||void 0===t?void 0:t.toLowerCase().includes(V.toLowerCase()))||(null===(a=e.sku)||void 0===a?void 0:a.toLowerCase().includes(V.toLowerCase())),s=null===ex||e.categoryId===ex;return n&&s}))||void 0===D?void 0:null===(P=D.slice(0,36))||void 0===P?void 0:P.map(e=>(0,n.jsxs)("div",{className:"aspect-square rounded-lg p-2 lg:p-3 cursor-pointer transition-all duration-200 flex flex-col items-center justify-center text-center shadow-md ".concat(e.stockQuantity<=0?"bg-gray-200 opacity-50 cursor-not-allowed":"bg-blue-400 hover:bg-blue-500 text-white"),onClick:()=>{if(e.stockQuantity>0){let t,a="",n="success",s=_.findIndex(t=>t.productId===e.id);if(s>=0){let l=_[s].quantity+1;l>e.stockQuantity?(l=e.stockQuantity,a="Cannot add more than ".concat(e.stockQuantity," units. Quantity set to maximum available."),n="error"):(a="Updated quantity of ".concat(e.name," to ").concat(l),n="success"),(t=[..._])[s]={...t[s],quantity:l}}else a="".concat(e.name," added to cart"),n="success",t=[..._,{productId:e.id,productName:e.name,price:Number(e.price),quantity:1}];Q(t),a&&(0,N.r)(n,a)}},children:[(0,n.jsxs)("div",{className:"w-12 h-12 lg:w-16 lg:h-16 bg-white/20 rounded-lg mb-1 lg:mb-2 flex items-center justify-center overflow-hidden",children:[e.imageUrl?(0,n.jsx)(l.default,{src:e.imageUrl,alt:e.name,width:64,height:64,className:"object-cover rounded-lg",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextElementSibling;t&&(t.style.display="flex")}}):null,(0,n.jsx)("span",{className:"text-2xl ".concat(e.imageUrl?"hidden":"block"),children:"\uD83D\uDCE6"})]}),(0,n.jsx)("div",{className:"text-xs font-medium mb-1 line-clamp-2 leading-tight",children:e.name.length>15?"".concat(e.name.substring(0,15),"..."):e.name}),(0,n.jsxs)("div",{className:"text-xs font-bold",children:["₵",Number(e.price).toFixed(2)]}),(0,n.jsx)("div",{className:"text-[11px] mt-1 ".concat(e.stockQuantity>0?"text-green-700":"text-red-500"),style:{fontWeight:500},children:e.stockQuantity>0?"In Stock: ".concat(e.stockQuantity):"Out of Stock"})]},e.id))}),(0,n.jsxs)("div",{className:"grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3",children:[(0,n.jsx)("div",{className:"bg-green-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ".concat(0===_.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-green-600"),onClick:()=>{if(0===_.length)return;let e={id:Date.now(),items:[..._],totalAmount:W,customer:el,timestamp:new Date().toISOString()};ec(t=>[...t,e]),(0,N.r)("success","Sale saved! Total: ₵".concat(W.toFixed(2))),Q([]),z(null),B(1),M(0),eg(null),L.resetFields(),R.resetFields()},children:(0,n.jsx)("div",{className:"text-xs font-medium",children:"Save Sale"})}),(0,n.jsx)("div",{className:"bg-indigo-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ".concat(0===ed.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-indigo-600"),onClick:()=>{0!==ed.length&&eI()},children:(0,n.jsxs)("div",{className:"text-xs font-medium",children:["Load Saved",ed.length>0&&(0,n.jsxs)("div",{className:"text-xs opacity-80",children:["(",ed.length,")"]})]})}),(0,n.jsx)("div",{className:"bg-orange-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ".concat(0===_.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-orange-600"),onClick:()=>{0!==_.length&&(Q([]),z(null),B(1),M(0),eg(null),L.resetFields(),R.resetFields(),(0,N.r)("success","Cart cleared"))},children:(0,n.jsx)("div",{className:"text-xs font-medium",children:"Clear Cart"})}),(0,n.jsx)("div",{className:"bg-red-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-red-600 transition-colors shadow-md",onClick:()=>{I()},children:(0,n.jsx)("div",{className:"text-xs font-medium",children:"Close POS"})}),(0,n.jsx)("div",{className:"bg-gray-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-gray-600 transition-colors shadow-md",onClick:()=>{(0,N.r)("success","Help: Click products to add to cart, use barcode scanner, adjust quantities, then click PAY to complete sale")},children:(0,n.jsx)("div",{className:"text-xs font-medium",children:"Help"})})]})]})]}),(0,n.jsx)("div",{className:"w-full lg:w-80 bg-white border-t lg:border-t-0 lg:border-l border-gray-200 flex flex-col",children:(0,n.jsxs)("div",{className:"flex-1 p-2 lg:p-4",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-3 lg:p-4 mb-3 lg:mb-4",children:[(0,n.jsx)("div",{className:"text-sm font-medium mb-2",children:"Cart Items"}),(0,n.jsx)("div",{className:"space-y-2 max-h-40 lg:max-h-60 overflow-y-auto",children:0===_.length?(0,n.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,n.jsx)(x.A,{className:"text-2xl mb-2"}),(0,n.jsx)("div",{children:"No items in cart"})]}):_.map((e,t)=>(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between py-2 border-b border-gray-100 space-y-2 sm:space-y-0",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"text-sm font-medium",children:e.productName}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:["₵",Number(e.price).toFixed(2)," each"]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,n.jsx)(d.Ay,{size:"small",type:"text",icon:(0,n.jsx)("span",{className:"text-xs",children:"−"}),onClick:()=>ez(t,e.quantity-1),className:"w-6 h-6 flex items-center justify-center"}),(0,n.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,n.jsx)(d.Ay,{size:"small",type:"text",icon:(0,n.jsx)("span",{className:"text-xs",children:"+"}),onClick:()=>ez(t,e.quantity+1),className:"w-6 h-6 flex items-center justify-center"}),(0,n.jsx)(d.Ay,{size:"small",type:"text",icon:(0,n.jsx)(h.A,{}),onClick:()=>eq(t),className:"w-6 h-6 flex items-center justify-center text-red-500"})]})]},"".concat(e.productId,"-").concat(t)))})]}),(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsx)("span",{children:"TOTAL"}),(0,n.jsxs)("span",{className:"font-bold",children:["₵",W.toFixed(2)]})]}),(0,n.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,n.jsx)("span",{children:"TAX"}),(0,n.jsx)("span",{children:"₵0.00"})]}),(0,n.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,n.jsx)("span",{children:"NET"}),(0,n.jsxs)("span",{children:["₵",W.toFixed(2)]})]})]})}),(0,n.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 mb-4",children:[(0,n.jsx)("div",{className:"text-xs text-gray-600 mb-1",children:"Customer"}),(0,n.jsx)("div",{className:"font-medium text-sm",children:el}),ed.length>0&&(0,n.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:[ed.length," saved sale",ed.length>1?"s":""]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(i.A,{form:L,layout:"vertical",children:(0,n.jsx)(i.A.Item,{name:"paymentMethod",initialValue:"cash",className:"mb-4",children:(0,n.jsxs)(r.A,{className:"w-full",size:"large",disabled:eo,children:[(0,n.jsx)(r.A.Option,{value:"cash",children:"\uD83D\uDCB5 Cash"}),(0,n.jsx)(r.A.Option,{value:"card",children:"\uD83D\uDCB3 Card"}),(0,n.jsx)(r.A.Option,{value:"mobile_money",children:"\uD83D\uDCF1 Mobile Money"})]})})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-3 lg:mb-4",children:[(0,n.jsxs)(d.Ay,{className:"bg-orange-400 text-white border-0 hover:bg-orange-500",onClick:()=>{let e="Walk-in Customer"===el?"Custom Customer":"Walk-in Customer";ei(e),(0,N.r)("success","Customer set to: ".concat(e))},children:["\uD83D\uDC65 ","Walk-in Customer"===el?"Walk-in":"Custom"]}),(0,n.jsx)(d.Ay,{className:"text-white border-0 ".concat(eo?"bg-red-500 hover:bg-red-600":"bg-orange-400 hover:bg-orange-500"),onClick:()=>{er(!eo),(0,N.r)("success",eo?"POS Unlocked":"POS Locked")},children:eo?"\uD83D\uDD13 Unlock":"\uD83D\uDD12 Lock"})]}),(0,n.jsx)(d.Ay,{className:"w-full h-10 lg:h-12 bg-red-500 text-white border-0 hover:bg-red-600 text-sm lg:text-lg font-bold mb-2",onClick:()=>{if(0===_.length){(0,N.r)("error","Please add items to cart");return}let e={id:Date.now(),items:[..._],totalAmount:W,customer:el,timestamp:new Date().toISOString(),status:"pending"};ec(t=>[...t,e]),(0,N.r)("success","Sale saved as pending! Total: ₵".concat(W.toFixed(2))),Q([]),z(null),B(1),M(0),eg(null),L.resetFields(),R.resetFields()},children:"\uD83D\uDDD1️ SALE (Save Pending)"}),(0,n.jsx)(d.Ay,{type:"primary",className:"w-full h-12 lg:h-16 bg-green-500 border-0 hover:bg-green-600 text-lg lg:text-xl font-bold",onClick:eB,loading:e_||X,children:"\uD83D\uDCB0 PAY (Complete Sale)"})]})]})})]})]}),(0,n.jsx)(c.A,{title:(0,n.jsxs)("div",{className:"flex items-center text-gray-800",children:[(0,n.jsx)(y.A,{className:"mr-2"}),(0,n.jsx)("span",{children:"Receipt Preview"})]}),open:J,onCancel:()=>{K(!1),ee(null),ea(!1),L.resetFields(),R.resetFields(),Q([]),z(null),B(1),M(0)},width:500,centered:!0,className:"receipt-preview-modal",footer:[(0,n.jsx)(d.Ay,{onClick:()=>{K(!1),ee(null),ea(!1),L.resetFields(),R.resetFields(),Q([]),z(null),B(1),M(0)},className:"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200",children:"Close & New Sale"},"close"),(0,n.jsx)(d.Ay,{type:"primary",icon:(0,n.jsx)(y.A,{}),onClick:()=>{et&&ea(!1),eU()},className:"bg-blue-600 hover:bg-blue-700",children:et?"Print Again":"Print Receipt"},"print")],children:(0,n.jsx)("div",{className:"flex flex-col items-center",children:Z?(0,n.jsx)("div",{className:"receipt-image-container",children:(0,n.jsx)(u.A,{src:Z,alt:"Receipt",className:"receipt-image",style:{maxWidth:"100%"}})}):(0,n.jsx)("div",{className:"flex h-64 items-center justify-center",children:(0,n.jsx)(m.A,{size:"large"})})})}),(0,n.jsx)(c.A,{title:"Saved Sales",open:eu,onCancel:()=>em(!1),width:600,centered:!0,footer:[(0,n.jsx)(d.Ay,{onClick:()=>em(!1),className:"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200",children:"Close"},"close")],children:(0,n.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===ed.length?(0,n.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,n.jsx)(x.A,{className:"text-4xl mb-4"}),(0,n.jsx)("p",{children:"No saved sales available"})]}):(0,n.jsx)("div",{className:"space-y-3",children:ed.map(e=>(0,n.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"font-medium text-gray-800",children:e.customer}),(0,n.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₵",e.totalAmount.toFixed(2)]})]}),(0,n.jsx)("div",{className:"text-sm text-gray-500 mb-2",children:new Date(e.timestamp).toLocaleString()}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[e.items.length," item",e.items.length>1?"s":"",": "," ",e.items.map(e=>e.productName).join(", ")]})]}),(0,n.jsxs)("div",{className:"ml-4 flex flex-col space-y-2",children:[(0,n.jsx)(d.Ay,{type:"primary",size:"small",onClick:()=>eO(e),className:"bg-blue-600 hover:bg-blue-700",children:"Load Sale"}),(0,n.jsx)(d.Ay,{danger:!0,size:"small",onClick:()=>eL(e.id),children:"Delete"})]})]})},e.id))})})})]})}},24988:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(95155),s=a(12115),l=a(43316),i=a(79624);let o=e=>{let{isOpen:t,onClose:a,title:o,children:r,width:d="400px",footer:c,fullWidth:u=!1}=e,[m,p]=(0,s.useState)(!1),[g,x]=(0,s.useState)(!1),[h,y]=(0,s.useState)(window.innerWidth);if((0,s.useEffect)(()=>{let e=()=>{y(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,s.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",o),t)x(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{p(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{p(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{x(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,o]),!g)return null;let v="Point of Sale"===o||u||"100vw"===d;return(0,n.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(v?"sales-panel-container":""),children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:a}),(0,n.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===o||u||"100vw"===d||h<640?"100vw":h<1024?"500px":"string"==typeof d&&d.includes("px")&&parseInt(d)>600?"600px":d},children:[(0,n.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:o}),(0,n.jsx)(l.Ay,{type:"text",icon:(0,n.jsx)(i.A,{style:{color:"#333"}}),onClick:a,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:r}),c&&(0,n.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},36060:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(83391),s=a(70854),l=a(63065),i=a(7875);let o=()=>{let e=(0,n.wA)(),{user:t,accessToken:a}=(0,n.d4)(e=>e.auth),o=(0,s._)(),{refetch:r}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!a,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let n=await r();console.log("useAuth - Refetch result:",n);let s=n.data;if((null==s?void 0:s.success)&&(null==s?void 0:s.data)){console.log("useAuth - API response data:",s.data);let n=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=s.data.phone||t.phone||"",o=s.data.createdAt||t.createdAt||"",r=s.data.lastPaymentDate||t.lastPaymentDate||void 0,d=s.data.nextPaymentDue||t.nextPaymentDue||null,c=s.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:s.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:s.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:s.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:r,apiNextPaymentDue:s.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:s.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let u={...s.data,phone:l,createdAt:o,lastPaymentDate:r,nextPaymentDue:d,createdBy:c,paymentStatus:n};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,i.gV)({user:u,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==s?void 0:s.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:c}}},70854:(e,t,a)=>{"use strict";a.d(t,{_:()=>o});var n=a(12115),s=a(83391),l=a(21455),i=a.n(l);let o=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,a]=(0,n.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,n.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,n=!1,s=!0,l="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){n=!0,s=!1,l="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let l=i()(e.nextPaymentDue),o=i()();if(t=l.diff(o,"day"),a){let a=i()().diff(i()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:n,needsPayment:s})}}}else"pending"===e.paymentStatus?(n=!1,s=!0,l="pending"):"overdue"===e.paymentStatus?(n=!1,s=!0,l="overdue"):(n=!1,s=!0,l="inactive");a({isActive:n,daysRemaining:t,status:l,needsPayment:s})},[e]),t}},7045:(e,t,a)=>{"use strict";a.d(t,{fS:()=>l,iG:()=>o,jY:()=>i});let n="dutmiedgk",s="pos_receipts",l=async e=>{try{var t;let l=document.createElement("div");l.innerHTML=e,l.style.width="350px",l.style.padding="20px",l.style.backgroundColor="white",l.style.color="#555555",l.style.fontFamily="Arial, sans-serif",l.style.position="absolute",l.style.left="-9999px",l.style.borderLeft="1px solid #000000",l.style.borderRight="1px solid #000000",l.style.borderTop="1px solid #000000",l.style.borderBottom="1px solid #000000",l.style.boxSizing="border-box",l.style.fontSize="14px",l.style.lineHeight="1.5",document.body.appendChild(l);let i=(await a.e(4316).then(a.t.bind(a,40078,23))).default,o=await i(l,{scale:3,backgroundColor:"white",logging:!1,width:350,height:l.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(l);let r=await new Promise(e=>{o.toBlob(t=>{e(t)},"image/png",.95)}),d=new FormData;d.append("file",r),d.append("upload_preset",s);let c=await fetch("https://api.cloudinary.com/v1_1/".concat(n,"/image/upload"),{method:"POST",body:d}),u=await c.json();if(c.ok)return u.secure_url;throw console.error("Cloudinary upload failed:",u),Error((null===(t=u.error)||void 0===t?void 0:t.message)||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},i=(e,t)=>{let a=new Date(e.transactionDate),n=a.toLocaleDateString(),s=a.toLocaleTimeString(),l=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return'\n  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">\n\n  \x3c!-- Header and Title --\x3e\n  <div style="text-align: center; margin-bottom: 10px;">\n    <div style="font-size: 18px; font-weight: bold;">'.concat(t.name||"POS System",'</div>\n    <div style="font-size: 16px; font-weight: bold;">#INV-').concat(e.id,"-").concat(new Date().getFullYear()).concat((new Date().getMonth()+1).toString().padStart(2,"0")).concat(new Date().getDate().toString().padStart(2,"0"),'</div>\n    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">\n      ').concat(t.address?"<div>".concat(t.address,"</div>"):"","\n      ").concat(t.city?"<div>".concat(t.city,"</div>"):"","\n      ").concat(t.country?"<div>".concat(t.country,"</div>"):"",'\n    </div>\n  </div>\n\n  \x3c!-- Items Table --\x3e\n  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">\n    <thead>\n      <tr>\n        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>\n      </tr>\n    </thead>\n    <tbody>\n      ').concat(e.items.map((e,t)=>{let a="string"==typeof e.price?parseFloat(e.price):e.price,n=a*e.quantity;return'\n          <tr>\n            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">'.concat(t+1,". ").concat(e.productName,'</td>\n            <td style="text-align: right;">').concat(e.quantity,'</td>\n            <td style="text-align: right;">').concat(a.toFixed(2),'</td>\n            <td style="text-align: right;">').concat(n.toFixed(2),"</td>\n          </tr>\n        ")}).join(""),'\n    </tbody>\n  </table>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Totals --\x3e\n  <div style="display: flex; justify-content: space-between; font-weight: bold;">\n    <div>TOTAL</div>\n    <div>GHS ').concat("string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2),'</div>\n  </div>\n  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">\n    <div>TAX</div>\n    <div>0.00</div>\n  </div>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Payment Info --\x3e\n  <div style="font-size: 11px; margin-bottom: 6px;">\n    <div>Payment: ').concat(l.toUpperCase(),"</div>\n    ").concat(l.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":"",'\n  </div>\n\n  \x3c!-- Date/Time --\x3e\n  <div style="display: flex; justify-content: space-between; font-size: 11px;">\n    <div><strong>Time:</strong> ').concat(s,"</div>\n    <div><strong>Date:</strong> ").concat(n,'</div>\n  </div>\n\n  \x3c!-- Barcode --\x3e\n  <div style="text-align: center; margin: 12px 0;">\n    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>\n    <div style="font-size: 11px; margin-top: 4px;">').concat(e.id,'</div>\n  </div>\n\n  \x3c!-- Footer --\x3e\n  <div style="text-align: center; font-size: 12px; margin-top: 8px;">\n    THANK YOU!\n  </div>\n</div>\n\n')},o=async e=>{try{var t;let a=new FormData;a.append("file",e),a.append("upload_preset",s),a.append("folder","products");let l=await fetch("https://api.cloudinary.com/v1_1/".concat(n,"/image/upload"),{method:"POST",body:a}),i=await l.json();if(l.ok)return i.secure_url;throw console.error("Cloudinary upload failed:",i),Error((null===(t=i.error)||void 0===t?void 0:t.message)||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},75912:(e,t,a)=>{"use strict";a.d(t,{r:()=>s});var n=a(55037);let s=(e,t)=>{"success"===e?n.oR.success(t):"error"===e?n.oR.error(t):"warning"===e&&(0,n.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},42397:()=>{},23411:()=>{}}]);