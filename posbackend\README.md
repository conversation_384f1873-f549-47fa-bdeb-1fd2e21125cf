# 🏪 POS System Backend API

A comprehensive **Point of Sale (POS) System** backend built with **Node.js**, **Express**, **TypeScript**, and **PostgreSQL**. This robust API powers a multi-tenant POS system with advanced features including payment processing, inventory management, sales tracking, and role-based access control.

## 🚀 Features

### 🔐 **Authentication & Authorization**
- **JWT-based authentication** with secure token management
- **Role-based access control** (SuperAdmin, Admin, Cashier)
- **Multi-tenant architecture** with user isolation
- **Payment status verification** middleware

### 💰 **Payment Integration**
- **Paystack payment gateway** integration
- **Subscription management** (1, 3, 12-month plans)
- **Automated payment status tracking**
- **Payment verification and webhooks**

### 📦 **Inventory Management**
- **Product management** with categories and suppliers
- **Stock tracking** with automatic adjustments
- **Low stock alerts** and notifications
- **Barcode and SKU support**

### 🛒 **Sales Management**
- **Complete sales workflow** with receipt generation
- **Multiple payment methods** (Cash, Card, Paystack)
- **Sales analytics** and reporting
- **Transaction history** tracking

### 📊 **Dashboard & Analytics**
- **Role-based dashboards** with real-time data
- **Sales analytics** and revenue tracking
- **Business insights** and performance metrics
- **Customizable date ranges**

### 🏢 **Multi-Store Support**
- **Store management** with location tracking
- **User-store associations**
- **Store-specific analytics**

## 🛠️ Tech Stack

| Technology | Purpose | Version |
|------------|---------|---------|
| **Node.js** | Runtime Environment | 18+ |
| **Express.js** | Web Framework | ^4.21.2 |
| **TypeScript** | Type Safety | ^5.7.3 |
| **PostgreSQL** | Database | Latest |
| **Drizzle ORM** | Database ORM | ^0.40.0 |
| **JWT** | Authentication | ^9.0.2 |
| **Zod** | Schema Validation | ^3.24.2 |
| **Paystack** | Payment Processing | ^2.0.1 |
| **Swagger** | API Documentation | ^5.0.1 |

## 📋 Prerequisites

Before running this application, ensure you have:

- **Node.js** (v18 or higher)
- **PostgreSQL** database
- **npm** or **yarn** package manager
- **Paystack account** (for payment processing)

## ⚡ Quick Start

### 1. **Clone & Install**
```bash
# Clone the repository
git clone <repository-url>
cd posbackend

# Install dependencies
npm install
```

### 2. **Environment Setup**
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 3. **Configure Environment Variables**
```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
DB_SSL=true

# Server Configuration
PORT=5000

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
```

### 4. **Database Setup**
```bash
# Generate database migrations
npm run db:generate

# Apply migrations
npm run db:push
```

### 5. **Start Development Server**
```bash
# Start in development mode
npm run dev

# Or build and start in production
npm run build
npm start
```

## 📚 API Documentation

### **Interactive Documentation**
Once the server is running, access the comprehensive API documentation:

- **Swagger UI**: `http://localhost:5000/api-docs`
- **OpenAPI Spec**: `http://localhost:5000/api-docs.json`

### **Base URL**
```
http://localhost:5000/api/v1
```

### **Authentication**
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build TypeScript to JavaScript |
| `npm start` | Start production server |
| `npm run db:generate` | Generate database migrations |
| `npm run db:push` | Apply migrations to database |
| `npm run db:pull` | Pull schema from database |

## 🏗️ Project Structure

```
posbackend/
├── src/
│   ├── config/           # Configuration files
│   │   ├── swagger.ts    # API documentation setup
│   │   └── lruCache.ts   # Cache configuration
│   ├── controllers/      # Request handlers
│   │   ├── UserController.ts
│   │   ├── productController.ts
│   │   ├── salesController.ts
│   │   └── ...
│   ├── services/         # Business logic
│   │   ├── authService.ts
│   │   ├── productService.ts
│   │   └── ...
│   ├── middleware/       # Express middleware
│   │   ├── authMiddleware.ts
│   │   ├── errorHandler.ts
│   │   └── checkPaymentStatus.ts
│   ├── routes/           # API routes
│   │   └── route.ts
│   ├── db/              # Database configuration
│   │   ├── db.ts        # Database connection
│   │   └── schema/      # Database schemas
│   ├── docs/            # API documentation
│   │   ├── auth.ts
│   │   ├── products.ts
│   │   └── ...
│   ├── utils/           # Utility functions
│   ├── types/           # TypeScript type definitions
│   └── validation/      # Input validation schemas
├── drizzle/             # Database migrations
├── package.json
├── tsconfig.json
└── README.md
```

## 🔐 API Endpoints Overview

### **Authentication**
- `POST /login` - User authentication
- `POST /logout` - User logout

### **User Management**
- `POST /users` - CRUD operations for users
  - Modes: `createnew`, `retrieve`, `update`, `delete`, `current`, `fix-payment-status`

### **Product Management**
- `POST /products` - CRUD operations for products
  - Modes: `createnew`, `retrieve`, `update`, `delete`

### **Sales Management**
- `POST /sales` - CRUD operations for sales
  - Modes: `createnew`, `retrieve`, `update`, `delete`
- `POST /receipt` - Generate sales receipts

### **Inventory Management**
- `POST /categories` - Category management
- `POST /suppliers` - Supplier management
- `POST /purchases` - Purchase tracking
- `POST /stockadjustment` - Stock adjustments

### **Payment Processing**
- `POST /payment` - Payment operations
  - Modes: `initialize`, `verify`, `verify-paystack`, `retrieve`, `history`

### **Analytics**
- `POST /dashboard` - Dashboard statistics
  - Mode: `stats`

## 💳 Payment Integration

### **Supported Payment Methods**
- **Cash** - Traditional cash payments
- **Card** - Credit/Debit card payments
- **Paystack** - Online payment processing

### **Subscription Plans**
- **Monthly**: 40 GHS/month
- **Quarterly**: 108 GHS (3 months, 10% discount - ₵36/month)
- **Annual**: 360 GHS (12 months, 25% discount - ₵30/month)
- **Free Trial**: 3 months free trial for all new users

## 🔒 Security Features

- **JWT Authentication** with secure token management
- **Password hashing** using bcrypt
- **Rate limiting** to prevent abuse
- **CORS protection** for cross-origin requests
- **Helmet.js** for security headers
- **Input validation** using Zod schemas
- **SQL injection protection** via Drizzle ORM

## 🌍 Environment Support

- **Development** - Local development with hot reload
- **Production** - Optimized build for deployment
- **SSL Support** - Database SSL connections
- **Multi-environment** configuration

## 📈 Monitoring & Health

### **Health Check**
```
GET /health
```
Returns server status and timestamp.

### **Logging**
- Request/response logging
- Error tracking and reporting
- Performance monitoring

## 🚀 Deployment

### **Production Deployment**
```bash
# Build the application
npm run build

# Start production server
npm start
```

### **Environment Variables for Production**
```env
NODE_ENV=production
PORT=5000
DATABASE_URL=your_production_database_url
JWT_SECRET=your_production_jwt_secret
PAYSTACK_SECRET_KEY=sk_live_your_live_paystack_key
PAYSTACK_PUBLIC_KEY=pk_live_your_live_paystack_key
```

### **Database Migration in Production**
```bash
# Apply migrations
npm run db:push

# Verify migration status
npm run db:pull
```

## 🧪 Testing

### **Manual Testing**
Use the Swagger UI at `/api-docs` to test all endpoints interactively.

### **Testing with cURL**
```bash
# Login to get JWT token
curl -X POST http://localhost:5000/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Use token for authenticated requests
curl -X POST http://localhost:5000/api/v1/products \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mode":"retrieve","page":1,"limit":10}'
```

## 🔧 Troubleshooting

### **Common Issues**

#### **Database Connection Issues**
```bash
# Check PostgreSQL service
sudo systemctl status postgresql

# Test database connection
psql -h localhost -U your_user -d your_database
```

#### **Port Already in Use**
```bash
# Find process using port 5000
lsof -i :5000

# Kill the process
kill -9 <PID>
```

#### **JWT Token Issues**
- Ensure `JWT_SECRET` is set in environment variables
- Check token expiration (tokens expire after 24 hours)
- Verify token format: `Bearer <token>`

#### **Paystack Integration Issues**
- Verify API keys are correct (test vs live)
- Check network connectivity to Paystack servers
- Ensure webhook URLs are properly configured

### **Debug Mode**
```bash
# Start with debug logging
DEBUG=* npm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### **Development Guidelines**
- Follow TypeScript best practices
- Use proper error handling
- Add appropriate comments
- Update API documentation
- Test your changes thoroughly

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: `https://nexapoapi.up.railway.app//api-docs`
- 🐛 Issues: Create an issue in the repository

---

**Built with ❤️ for modern businesses**
