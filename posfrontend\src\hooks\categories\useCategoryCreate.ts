"use client";

import { useCreateCategoryMutation, CreateCategoryDto } from "@/reduxRTK/services/categoryApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useCategoryCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a category
  const [createCategory, { isLoading }] = useCreateCategoryMutation();

  const createNewCategory = async (categoryData: CreateCategoryDto) => {
    try {
      console.log("useCategoryCreate - Starting category creation with data:", categoryData);

      // Get the current auth state from Redux store
      const authState = (window as any).__REDUX_STATE?.auth;
      console.log("useCategoryCreate - Current auth state:", authState ? {
        hasUser: !!authState.user,
        hasToken: !!authState.accessToken,
        userRole: authState.user?.role
      } : "Not available");

      const result = await createCategory(categoryData).unwrap() as ApiResponse<any>;
      console.log("useCategoryCreate - API response:", result);

      if (!result.success) {
        console.error("useCategoryCreate - API returned error:", result.message);
        throw new Error(result.message || "Failed to create category");
      }

      showMessage("success", "Category created successfully");

      if (onSuccess) {
        onSuccess();
      }

      return result.data;
    } catch (error: any) {
      console.error("Create category error:", error);
      showMessage("error", error.message || "Failed to create category");
      throw error;
    }
  };

  return {
    createCategory: createNewCategory,
    isCreating: isLoading
  };
};
