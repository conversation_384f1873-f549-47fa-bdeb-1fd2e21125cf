"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleProductRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const productService_1 = require("../services/productService");
const modeValidator_1 = require("../utils/modeValidator");
const errorHandler_1 = require("../middleware/errorHandler");
exports.handleProductRequest = (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const { mode, productId, page, limit, productsData, barcode } = req.body;
    const requester = req.user;
    const validModes = ["createnew", "update", "delete", "retrieve", "barcode"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    switch (mode) {
        case "createnew": {
            if (!Array.isArray(productsData) || productsData.length === 0) {
                throw new errorHandler_1.ValidationError("Provide at least one product.");
            }
            const newProducts = await (0, productService_1.createProduct)(requester, productsData);
            return (0, responseHelper_1.sendResponse)(res, 201, true, "Products created successfully.", newProducts);
        }
        case "retrieve": {
            if (productId) {
                const product = await (0, productService_1.getProductById)(requester, Number(productId));
                if (!product) {
                    throw new errorHandler_1.NotFoundError("Product not found.");
                }
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Product retrieved successfully.", product);
            }
            else {
                const pageNum = Number(page) || 1;
                const limitNum = Number(limit) || 10;
                const { search } = req.body;
                if (pageNum < 1 || limitNum < 1) {
                    throw new errorHandler_1.ValidationError("Invalid pagination values.");
                }
                console.log("getAllProducts request:", {
                    requester: { id: requester.id, role: requester.role },
                    page: pageNum,
                    limit: limitNum,
                    search: search || 'none'
                });
                const products = await (0, productService_1.getAllProducts)(requester, pageNum, limitNum, search);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Products retrieved successfully.", products);
            }
        }
        case "update": {
            if (!productId) {
                throw new errorHandler_1.ValidationError("Product ID is required for updating.");
            }
            const updatedProduct = await (0, productService_1.updateProductById)(requester, Number(productId), req.body);
            return (0, responseHelper_1.sendResponse)(res, 200, true, "Product updated successfully.", updatedProduct);
        }
        case "delete": {
            // Check if we have productIds (array) or productId (single)
            const { productIds } = req.body;
            if (!productId && !productIds) {
                throw new errorHandler_1.ValidationError("Product ID(s) are required for deletion.");
            }
            // Use productIds if provided, otherwise use single productId
            const idsToDelete = productIds
                ? (Array.isArray(productIds) ? productIds : [productIds])
                : [Number(productId)];
            const result = await (0, productService_1.deleteProductById)(requester, idsToDelete);
            const message = idsToDelete.length > 1
                ? `${idsToDelete.length} products deleted successfully.`
                : "Product deleted successfully.";
            return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
        }
        case "barcode": {
            if (!barcode || typeof barcode !== 'string') {
                throw new errorHandler_1.ValidationError("Barcode is required for barcode search.");
            }
            const product = await (0, productService_1.getProductByBarcode)(requester, barcode.trim());
            if (!product) {
                return (0, responseHelper_1.sendResponse)(res, 404, false, "Product not found for the given barcode.", null);
            }
            return (0, responseHelper_1.sendResponse)(res, 200, true, "Product found by barcode.", product);
        }
        default:
            throw new errorHandler_1.ValidationError("Unexpected error occurred.");
    }
});
