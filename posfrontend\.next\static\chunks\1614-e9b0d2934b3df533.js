"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1614],{4768:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(85407),r=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};var a=n(84021);let l=r.forwardRef(function(e,t){return r.createElement(a.A,(0,o.A)({},e,{ref:t,icon:i}))})},10593:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(85407),r=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};var a=n(84021);let l=r.forwardRef(function(e,t){return r.createElement(a.A,(0,o.A)({},e,{ref:t,icon:i}))})},28744:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(12115),r=n(31049),i=n(53096);let a=e=>{let{componentName:t}=e,{getPrefixCls:n}=(0,o.useContext)(r.QO),a=n("empty");switch(t){case"Table":case"List":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE,className:"".concat(a,"-small")});case"Table.filter":return null;default:return o.createElement(i.A,null)}}},53096:(e,t,n)=>{n.d(t,{A:()=>b});var o=n(12115),r=n(4617),i=n.n(r),a=n(55315),l=n(10815),c=n(68711),u=n(1086),s=n(56204);let d=e=>{let{componentCls:t,margin:n,marginXS:o,marginXL:r,fontSize:i,lineHeight:a}=e;return{[t]:{marginInline:o,fontSize:i,lineHeight:a,textAlign:"center",["".concat(t,"-image")]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},["".concat(t,"-description")]:{color:e.colorTextDescription},["".concat(t,"-footer")]:{marginTop:n},"&-normal":{marginBlock:r,color:e.colorTextDescription,["".concat(t,"-description")]:{color:e.colorTextDescription},["".concat(t,"-image")]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,["".concat(t,"-image")]:{height:e.emptyImgHeightSM}}}}},f=(0,u.OF)("Empty",e=>{let{componentCls:t,controlHeightLG:n,calc:o}=e;return[d((0,s.oX)(e,{emptyImgCls:"".concat(t,"-img"),emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()}))]});var p=n(31049),m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let v=o.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,a.A)("Empty"),n=new l.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return o.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{fill:"none",fillRule:"evenodd"},o.createElement("g",{transform:"translate(24 31.67)"},o.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),o.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),o.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),o.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),o.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),o.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),o.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},o.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),o.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},null),g=o.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,a.A)("Empty"),{colorFill:n,colorFillTertiary:r,colorFillQuaternary:i,colorBgContainer:u}=e,{borderColor:s,shadowColor:d,contentColor:f}=(0,o.useMemo)(()=>({borderColor:new l.Y(n).onBackground(u).toHexString(),shadowColor:new l.Y(r).onBackground(u).toHexString(),contentColor:new l.Y(i).onBackground(u).toHexString()}),[n,r,i,u]);return o.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},o.createElement("ellipse",{fill:d,cx:"32",cy:"33",rx:"32",ry:"7"}),o.createElement("g",{fillRule:"nonzero",stroke:s},o.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),o.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:f}))))},null),h=e=>{let{className:t,rootClassName:n,prefixCls:r,image:l=v,description:c,children:u,imageStyle:s,style:d,classNames:h,styles:b}=e,A=m(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:y,direction:w,className:E,style:S,classNames:C,styles:x}=(0,p.TP)("empty"),I=y("empty",r),[O,M,R]=f(I),[z]=(0,a.A)("Empty"),D=void 0!==c?c:null==z?void 0:z.description,H=null;return H="string"==typeof l?o.createElement("img",{alt:"string"==typeof D?D:"empty",src:l}):l,O(o.createElement("div",Object.assign({className:i()(M,R,I,E,{["".concat(I,"-normal")]:l===g,["".concat(I,"-rtl")]:"rtl"===w},t,n,C.root,null==h?void 0:h.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},x.root),S),null==b?void 0:b.root),d)},A),o.createElement("div",{className:i()("".concat(I,"-image"),C.image,null==h?void 0:h.image),style:Object.assign(Object.assign(Object.assign({},s),x.image),null==b?void 0:b.image)},H),D&&o.createElement("div",{className:i()("".concat(I,"-description"),C.description,null==h?void 0:h.description),style:Object.assign(Object.assign({},x.description),null==b?void 0:b.description)},D),u&&o.createElement("div",{className:i()("".concat(I,"-footer"),C.footer,null==h?void 0:h.footer),style:Object.assign(Object.assign({},x.footer),null==b?void 0:b.footer)},u)))};h.PRESENTED_IMAGE_DEFAULT=v,h.PRESENTED_IMAGE_SIMPLE=g;let b=h},21614:(e,t,n)=>{n.d(t,{A:()=>e8});var o=n(12115),r=n(4617),i=n.n(r),a=n(85407),l=n(39014),c=n(1568),u=n(85268),s=n(59912),d=n(64406),f=n(21855),p=n(35015),m=n(30754),v=n(66105),g=n(8324),h=n(15231);let b=function(e){var t=e.className,n=e.customizeIcon,r=e.customizeIconProps,a=e.children,l=e.onMouseDown,c=e.onClick,u="function"==typeof n?n(r):n;return o.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null==l||l(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},void 0!==u?u:o.createElement("span",{className:i()(t.split(/\s+/).map(function(e){return"".concat(e,"-icon")}))},a))};var A=function(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,u=o.useMemo(function(){return"object"===(0,f.A)(r)?r.clearIcon:i||void 0},[r,i]);return{allowClear:o.useMemo(function(){return!a&&!!r&&(!!n.length||!!l)&&!("combobox"===c&&""===l)},[r,a,n.length,l,c]),clearIcon:o.createElement(b,{className:"".concat(e,"-clear"),onMouseDown:t,customizeIcon:u},"\xd7")}},y=o.createContext(null);function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,t=o.useRef(null),n=o.useRef(null);return o.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]),[function(){return t.current},function(o){(o||null===t.current)&&(t.current=o),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}]}var E=n(23672),S=n(97181),C=n(89585),x=o.forwardRef(function(e,t){var n,r=e.prefixCls,a=e.id,l=e.inputElement,c=e.disabled,s=e.tabIndex,d=e.autoFocus,f=e.autoComplete,p=e.editable,v=e.activeDescendantId,g=e.value,b=e.maxLength,A=e.onKeyDown,y=e.onMouseDown,w=e.onChange,E=e.onPaste,S=e.onCompositionStart,C=e.onCompositionEnd,x=e.onBlur,I=e.open,O=e.attrs,M=l||o.createElement("input",null),R=M,z=R.ref,D=R.props,H=D.onKeyDown,N=D.onChange,T=D.onMouseDown,B=D.onCompositionStart,P=D.onCompositionEnd,k=D.onBlur,L=D.style;return(0,m.$e)(!("maxLength"in M.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),M=o.cloneElement(M,(0,u.A)((0,u.A)((0,u.A)({type:"search"},D),{},{id:a,ref:(0,h.K4)(t,z),disabled:c,tabIndex:s,autoComplete:f||"off",autoFocus:d,className:i()("".concat(r,"-selection-search-input"),null===(n=M)||void 0===n||null===(n=n.props)||void 0===n?void 0:n.className),role:"combobox","aria-expanded":I||!1,"aria-haspopup":"listbox","aria-owns":"".concat(a,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(a,"_list"),"aria-activedescendant":I?v:void 0},O),{},{value:p?g:"",maxLength:b,readOnly:!p,unselectable:p?null:"on",style:(0,u.A)((0,u.A)({},L),{},{opacity:p?null:0}),onKeyDown:function(e){A(e),H&&H(e)},onMouseDown:function(e){y(e),T&&T(e)},onChange:function(e){w(e),N&&N(e)},onCompositionStart:function(e){S(e),B&&B(e)},onCompositionEnd:function(e){C(e),P&&P(e)},onPaste:E,onBlur:function(e){x(e),k&&k(e)}}))});function I(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var O="undefined"!=typeof window&&window.document&&window.document.documentElement;function M(e){return["string","number"].includes((0,f.A)(e))}function R(e){var t=void 0;return e&&(M(e.title)?t=e.title.toString():M(e.label)&&(t=e.label.toString())),t}function z(e){var t;return null!==(t=e.key)&&void 0!==t?t:e.value}var D=function(e){e.preventDefault(),e.stopPropagation()};let H=function(e){var t,n,r=e.id,a=e.prefixCls,l=e.values,u=e.open,d=e.searchValue,f=e.autoClearSearchValue,p=e.inputRef,m=e.placeholder,v=e.disabled,g=e.mode,h=e.showSearch,A=e.autoFocus,y=e.autoComplete,w=e.activeDescendantId,E=e.tabIndex,I=e.removeIcon,M=e.maxTagCount,H=e.maxTagTextLength,N=e.maxTagPlaceholder,T=void 0===N?function(e){return"+ ".concat(e.length," ...")}:N,B=e.tagRender,P=e.onToggleOpen,k=e.onRemove,L=e.onInputChange,j=e.onInputPaste,W=e.onInputKeyDown,F=e.onInputMouseDown,V=e.onInputCompositionStart,_=e.onInputCompositionEnd,K=e.onInputBlur,Y=o.useRef(null),X=(0,o.useState)(0),q=(0,s.A)(X,2),G=q[0],U=q[1],Q=(0,o.useState)(!1),$=(0,s.A)(Q,2),J=$[0],Z=$[1],ee="".concat(a,"-selection"),et=u||"multiple"===g&&!1===f||"tags"===g?d:"",en="tags"===g||"multiple"===g&&!1===f||h&&(u||J);t=function(){U(Y.current.scrollWidth)},n=[et],O?o.useLayoutEffect(t,n):o.useEffect(t,n);var eo=function(e,t,n,r,a){return o.createElement("span",{title:R(e),className:i()("".concat(ee,"-item"),(0,c.A)({},"".concat(ee,"-item-disabled"),n))},o.createElement("span",{className:"".concat(ee,"-item-content")},t),r&&o.createElement(b,{className:"".concat(ee,"-item-remove"),onMouseDown:D,onClick:a,customizeIcon:I},"\xd7"))},er=function(e,t,n,r,i,a){return o.createElement("span",{onMouseDown:function(e){D(e),P(!u)}},B({label:t,value:e,disabled:n,closable:r,onClose:i,isMaxTag:!!a}))},ei=o.createElement("div",{className:"".concat(ee,"-search"),style:{width:G},onFocus:function(){Z(!0)},onBlur:function(){Z(!1)}},o.createElement(x,{ref:p,open:u,prefixCls:a,id:r,inputElement:null,disabled:v,autoFocus:A,autoComplete:y,editable:en,activeDescendantId:w,value:et,onKeyDown:W,onMouseDown:F,onChange:L,onPaste:j,onCompositionStart:V,onCompositionEnd:_,onBlur:K,tabIndex:E,attrs:(0,S.A)(e,!0)}),o.createElement("span",{ref:Y,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},et,"\xa0")),ea=o.createElement(C.A,{prefixCls:"".concat(ee,"-overflow"),data:l,renderItem:function(e){var t=e.disabled,n=e.label,o=e.value,r=!v&&!t,i=n;if("number"==typeof H&&("string"==typeof n||"number"==typeof n)){var a=String(i);a.length>H&&(i="".concat(a.slice(0,H),"..."))}var l=function(t){t&&t.stopPropagation(),k(e)};return"function"==typeof B?er(o,i,t,r,l):eo(e,i,t,r,l)},renderRest:function(e){if(!l.length)return null;var t="function"==typeof T?T(e):T;return"function"==typeof B?er(void 0,t,!1,!1,void 0,!0):eo({title:t},t,!1)},suffix:ei,itemKey:z,maxCount:M});return o.createElement("span",{className:"".concat(ee,"-wrap")},ea,!l.length&&!et&&o.createElement("span",{className:"".concat(ee,"-placeholder")},m))},N=function(e){var t=e.inputElement,n=e.prefixCls,r=e.id,i=e.inputRef,a=e.disabled,l=e.autoFocus,c=e.autoComplete,u=e.activeDescendantId,d=e.mode,f=e.open,p=e.values,m=e.placeholder,v=e.tabIndex,g=e.showSearch,h=e.searchValue,b=e.activeValue,A=e.maxLength,y=e.onInputKeyDown,w=e.onInputMouseDown,E=e.onInputChange,C=e.onInputPaste,I=e.onInputCompositionStart,O=e.onInputCompositionEnd,M=e.onInputBlur,z=e.title,D=o.useState(!1),H=(0,s.A)(D,2),N=H[0],T=H[1],B="combobox"===d,P=B||g,k=p[0],L=h||"";B&&b&&!N&&(L=b),o.useEffect(function(){B&&T(!1)},[B,b]);var j=("combobox"===d||!!f||!!g)&&!!L,W=void 0===z?R(k):z,F=o.useMemo(function(){return k?null:o.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:j?{visibility:"hidden"}:void 0},m)},[k,j,m,n]);return o.createElement("span",{className:"".concat(n,"-selection-wrap")},o.createElement("span",{className:"".concat(n,"-selection-search")},o.createElement(x,{ref:i,prefixCls:n,id:r,open:f,inputElement:t,disabled:a,autoFocus:l,autoComplete:c,editable:P,activeDescendantId:u,value:L,onKeyDown:y,onMouseDown:w,onChange:function(e){T(!0),E(e)},onPaste:C,onCompositionStart:I,onCompositionEnd:O,onBlur:M,tabIndex:v,attrs:(0,S.A)(e,!0),maxLength:B?A:void 0})),!B&&k?o.createElement("span",{className:"".concat(n,"-selection-item"),title:W,style:j?{visibility:"hidden"}:void 0},k.label):null,F)};var T=o.forwardRef(function(e,t){var n=(0,o.useRef)(null),r=(0,o.useRef)(!1),i=e.prefixCls,l=e.open,c=e.mode,u=e.showSearch,d=e.tokenWithEnter,f=e.disabled,p=e.prefix,m=e.autoClearSearchValue,v=e.onSearch,g=e.onSearchSubmit,h=e.onToggleOpen,b=e.onInputKeyDown,A=e.onInputBlur,y=e.domRef;o.useImperativeHandle(t,function(){return{focus:function(e){n.current.focus(e)},blur:function(){n.current.blur()}}});var S=w(0),C=(0,s.A)(S,2),x=C[0],I=C[1],O=(0,o.useRef)(null),M=function(e){!1!==v(e,!0,r.current)&&h(!0)},R={inputRef:n,onInputKeyDown:function(e){var t=e.which,o=n.current instanceof HTMLTextAreaElement;!o&&l&&(t===E.A.UP||t===E.A.DOWN)&&e.preventDefault(),b&&b(e),t!==E.A.ENTER||"tags"!==c||r.current||l||null==g||g(e.target.value),o&&!l&&~[E.A.UP,E.A.DOWN,E.A.LEFT,E.A.RIGHT].indexOf(t)||!t||[E.A.ESC,E.A.SHIFT,E.A.BACKSPACE,E.A.TAB,E.A.WIN_KEY,E.A.ALT,E.A.META,E.A.WIN_KEY_RIGHT,E.A.CTRL,E.A.SEMICOLON,E.A.EQUALS,E.A.CAPS_LOCK,E.A.CONTEXT_MENU,E.A.F1,E.A.F2,E.A.F3,E.A.F4,E.A.F5,E.A.F6,E.A.F7,E.A.F8,E.A.F9,E.A.F10,E.A.F11,E.A.F12].includes(t)||h(!0)},onInputMouseDown:function(){I(!0)},onInputChange:function(e){var t=e.target.value;if(d&&O.current&&/[\r\n]/.test(O.current)){var n=O.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");t=t.replace(n,O.current)}O.current=null,M(t)},onInputPaste:function(e){var t=e.clipboardData,n=null==t?void 0:t.getData("text");O.current=n||""},onInputCompositionStart:function(){r.current=!0},onInputCompositionEnd:function(e){r.current=!1,"combobox"!==c&&M(e.target.value)},onInputBlur:A},z="multiple"===c||"tags"===c?o.createElement(H,(0,a.A)({},e,R)):o.createElement(N,(0,a.A)({},e,R));return o.createElement("div",{ref:y,className:"".concat(i,"-selector"),onClick:function(e){e.target!==n.current&&(void 0!==document.body.style.msTouchAction?setTimeout(function(){n.current.focus()}):n.current.focus())},onMouseDown:function(e){var t=x();e.target===n.current||t||"combobox"===c&&f||e.preventDefault(),("combobox"===c||u&&t)&&l||(l&&!1!==m&&v("",!0,!1),h())}},p&&o.createElement("div",{className:"".concat(i,"-prefix")},p),z)}),B=n(99121),P=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],k=function(e){var t=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"}}},L=o.forwardRef(function(e,t){var n=e.prefixCls,r=(e.disabled,e.visible),l=e.children,s=e.popupElement,f=e.animation,p=e.transitionName,m=e.dropdownStyle,v=e.dropdownClassName,g=e.direction,h=e.placement,b=e.builtinPlacements,A=e.dropdownMatchSelectWidth,y=e.dropdownRender,w=e.dropdownAlign,E=e.getPopupContainer,S=e.empty,C=e.getTriggerDOMNode,x=e.onPopupVisibleChange,I=e.onPopupMouseEnter,O=(0,d.A)(e,P),M="".concat(n,"-dropdown"),R=s;y&&(R=y(s));var z=o.useMemo(function(){return b||k(A)},[b,A]),D=f?"".concat(M,"-").concat(f):p,H="number"==typeof A,N=o.useMemo(function(){return H?null:!1===A?"minWidth":"width"},[A,H]),T=m;H&&(T=(0,u.A)((0,u.A)({},T),{},{width:A}));var L=o.useRef(null);return o.useImperativeHandle(t,function(){return{getPopupElement:function(){var e;return null===(e=L.current)||void 0===e?void 0:e.popupElement}}}),o.createElement(B.A,(0,a.A)({},O,{showAction:x?["click"]:[],hideAction:x?["click"]:[],popupPlacement:h||("rtl"===(void 0===g?"ltr":g)?"bottomRight":"bottomLeft"),builtinPlacements:z,prefixCls:M,popupTransitionName:D,popup:o.createElement("div",{onMouseEnter:I},R),ref:L,stretch:N,popupAlign:w,popupVisible:r,getPopupContainer:E,popupClassName:i()(v,(0,c.A)({},"".concat(M,"-empty"),S)),popupStyle:T,getTriggerDOMNode:C,onPopupVisibleChange:x}),l)}),j=n(80520);function W(e,t){var n,o=e.key;return("value"in e&&(n=e.value),null!=o)?o:void 0!==n?n:"rc-index-key-".concat(t)}function F(e){return void 0!==e&&!Number.isNaN(e)}function V(e,t){var n=e||{},o=n.label,r=n.value,i=n.options,a=n.groupLabel,l=o||(t?"children":"label");return{label:l,value:r||"value",options:i||"options",groupLabel:a||l}}function _(e){var t=(0,u.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,m.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var K=function(e,t,n){if(!t||!t.length)return null;var o=!1,r=function e(t,n){var r=(0,j.A)(n),i=r[0],a=r.slice(1);if(!i)return[t];var c=t.split(i);return o=o||c.length>1,c.reduce(function(t,n){return[].concat((0,l.A)(t),(0,l.A)(e(n,a)))},[]).filter(Boolean)}(e,t);return o?void 0!==n?r.slice(0,n):r:null},Y=o.createContext(null);function X(e){var t=e.visible,n=e.values;return t?o.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,50).map(function(e){var t=e.label,n=e.value;return["number","string"].includes((0,f.A)(t))?t:n}).join(", ")),n.length>50?", ...":null):null}var q=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],G=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],U=function(e){return"tags"===e||"multiple"===e},Q=o.forwardRef(function(e,t){var n,r,f,m,E,S,C,x=e.id,I=e.prefixCls,O=e.className,M=e.showSearch,R=e.tagRender,z=e.direction,D=e.omitDomProps,H=e.displayValues,N=e.onDisplayValuesChange,B=e.emptyOptions,P=e.notFoundContent,k=void 0===P?"Not Found":P,j=e.onClear,W=e.mode,V=e.disabled,_=e.loading,Q=e.getInputElement,$=e.getRawInputElement,J=e.open,Z=e.defaultOpen,ee=e.onDropdownVisibleChange,et=e.activeValue,en=e.onActiveValueChange,eo=e.activeDescendantId,er=e.searchValue,ei=e.autoClearSearchValue,ea=e.onSearch,el=e.onSearchSplit,ec=e.tokenSeparators,eu=e.allowClear,es=e.prefix,ed=e.suffixIcon,ef=e.clearIcon,ep=e.OptionList,em=e.animation,ev=e.transitionName,eg=e.dropdownStyle,eh=e.dropdownClassName,eb=e.dropdownMatchSelectWidth,eA=e.dropdownRender,ey=e.dropdownAlign,ew=e.placement,eE=e.builtinPlacements,eS=e.getPopupContainer,eC=e.showAction,ex=void 0===eC?[]:eC,eI=e.onFocus,eO=e.onBlur,eM=e.onKeyUp,eR=e.onKeyDown,ez=e.onMouseDown,eD=(0,d.A)(e,q),eH=U(W),eN=(void 0!==M?M:eH)||"combobox"===W,eT=(0,u.A)({},eD);G.forEach(function(e){delete eT[e]}),null==D||D.forEach(function(e){delete eT[e]});var eB=o.useState(!1),eP=(0,s.A)(eB,2),ek=eP[0],eL=eP[1];o.useEffect(function(){eL((0,g.A)())},[]);var ej=o.useRef(null),eW=o.useRef(null),eF=o.useRef(null),eV=o.useRef(null),e_=o.useRef(null),eK=o.useRef(!1),eY=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=o.useState(!1),n=(0,s.A)(t,2),r=n[0],i=n[1],a=o.useRef(null),l=function(){window.clearTimeout(a.current)};return o.useEffect(function(){return l},[]),[r,function(t,n){l(),a.current=window.setTimeout(function(){i(t),n&&n()},e)},l]}(),eX=(0,s.A)(eY,3),eq=eX[0],eG=eX[1],eU=eX[2];o.useImperativeHandle(t,function(){var e,t;return{focus:null===(e=eV.current)||void 0===e?void 0:e.focus,blur:null===(t=eV.current)||void 0===t?void 0:t.blur,scrollTo:function(e){var t;return null===(t=e_.current)||void 0===t?void 0:t.scrollTo(e)},nativeElement:ej.current||eW.current}});var eQ=o.useMemo(function(){if("combobox"!==W)return er;var e,t=null===(e=H[0])||void 0===e?void 0:e.value;return"string"==typeof t||"number"==typeof t?String(t):""},[er,W,H]),e$="combobox"===W&&"function"==typeof Q&&Q()||null,eJ="function"==typeof $&&$(),eZ=(0,h.xK)(eW,null==eJ||null===(m=eJ.props)||void 0===m?void 0:m.ref),e0=o.useState(!1),e1=(0,s.A)(e0,2),e2=e1[0],e4=e1[1];(0,v.A)(function(){e4(!0)},[]);var e5=(0,p.A)(!1,{defaultValue:Z,value:J}),e6=(0,s.A)(e5,2),e3=e6[0],e7=e6[1],e9=!!e2&&e3,e8=!k&&B;(V||e8&&e9&&"combobox"===W)&&(e9=!1);var te=!e8&&e9,tt=o.useCallback(function(e){var t=void 0!==e?e:!e9;V||(e7(t),e9!==t&&(null==ee||ee(t)))},[V,e9,e7,ee]),tn=o.useMemo(function(){return(ec||[]).some(function(e){return["\n","\r\n"].includes(e)})},[ec]),to=o.useContext(Y)||{},tr=to.maxCount,ti=to.rawValues,ta=function(e,t,n){if(!(eH&&F(tr))||!((null==ti?void 0:ti.size)>=tr)){var o=!0,r=e;null==en||en(null);var i=K(e,ec,F(tr)?tr-ti.size:void 0),a=n?null:i;return"combobox"!==W&&a&&(r="",null==el||el(a),tt(!1),o=!1),ea&&eQ!==r&&ea(r,{source:t?"typing":"effect"}),o}};o.useEffect(function(){e9||eH||"combobox"===W||ta("",!1,!1)},[e9]),o.useEffect(function(){e3&&V&&e7(!1),V&&!eK.current&&eG(!1)},[V]);var tl=w(),tc=(0,s.A)(tl,2),tu=tc[0],ts=tc[1],td=o.useRef(!1),tf=o.useRef(!1),tp=[];o.useEffect(function(){return function(){tp.forEach(function(e){return clearTimeout(e)}),tp.splice(0,tp.length)}},[]);var tm=o.useState({}),tv=(0,s.A)(tm,2)[1];eJ&&(E=function(e){tt(e)}),n=function(){var e;return[ej.current,null===(e=eF.current)||void 0===e?void 0:e.getPopupElement()]},r=!!eJ,(f=o.useRef(null)).current={open:te,triggerOpen:tt,customizedTrigger:r},o.useEffect(function(){function e(e){if(null===(t=f.current)||void 0===t||!t.customizedTrigger){var t,o=e.target;o.shadowRoot&&e.composed&&(o=e.composedPath()[0]||o),f.current.open&&n().filter(function(e){return e}).every(function(e){return!e.contains(o)&&e!==o})&&f.current.triggerOpen(!1)}}return window.addEventListener("mousedown",e),function(){return window.removeEventListener("mousedown",e)}},[]);var tg=o.useMemo(function(){return(0,u.A)((0,u.A)({},e),{},{notFoundContent:k,open:e9,triggerOpen:te,id:x,showSearch:eN,multiple:eH,toggleOpen:tt})},[e,k,te,e9,x,eN,eH,tt]),th=!!ed||_;th&&(S=o.createElement(b,{className:i()("".concat(I,"-arrow"),(0,c.A)({},"".concat(I,"-arrow-loading"),_)),customizeIcon:ed,customizeIconProps:{loading:_,searchValue:eQ,open:e9,focused:eq,showSearch:eN}}));var tb=A(I,function(){var e;null==j||j(),null===(e=eV.current)||void 0===e||e.focus(),N([],{type:"clear",values:H}),ta("",!1,!1)},H,eu,ef,V,eQ,W),tA=tb.allowClear,ty=tb.clearIcon,tw=o.createElement(ep,{ref:e_}),tE=i()(I,O,(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(I,"-focused"),eq),"".concat(I,"-multiple"),eH),"".concat(I,"-single"),!eH),"".concat(I,"-allow-clear"),eu),"".concat(I,"-show-arrow"),th),"".concat(I,"-disabled"),V),"".concat(I,"-loading"),_),"".concat(I,"-open"),e9),"".concat(I,"-customize-input"),e$),"".concat(I,"-show-search"),eN)),tS=o.createElement(L,{ref:eF,disabled:V,prefixCls:I,visible:te,popupElement:tw,animation:em,transitionName:ev,dropdownStyle:eg,dropdownClassName:eh,direction:z,dropdownMatchSelectWidth:eb,dropdownRender:eA,dropdownAlign:ey,placement:ew,builtinPlacements:eE,getPopupContainer:eS,empty:B,getTriggerDOMNode:function(e){return eW.current||e},onPopupVisibleChange:E,onPopupMouseEnter:function(){tv({})}},eJ?o.cloneElement(eJ,{ref:eZ}):o.createElement(T,(0,a.A)({},e,{domRef:eW,prefixCls:I,inputElement:e$,ref:eV,id:x,prefix:es,showSearch:eN,autoClearSearchValue:ei,mode:W,activeDescendantId:eo,tagRender:R,values:H,open:e9,onToggleOpen:tt,activeValue:et,searchValue:eQ,onSearch:ta,onSearchSubmit:function(e){e&&e.trim()&&ea(e,{source:"submit"})},onRemove:function(e){N(H.filter(function(t){return t!==e}),{type:"remove",values:[e]})},tokenWithEnter:tn,onInputBlur:function(){td.current=!1}})));return C=eJ?tS:o.createElement("div",(0,a.A)({className:tE},eT,{ref:ej,onMouseDown:function(e){var t,n=e.target,o=null===(t=eF.current)||void 0===t?void 0:t.getPopupElement();if(o&&o.contains(n)){var r=setTimeout(function(){var e,t=tp.indexOf(r);-1!==t&&tp.splice(t,1),eU(),ek||o.contains(document.activeElement)||null===(e=eV.current)||void 0===e||e.focus()});tp.push(r)}for(var i=arguments.length,a=Array(i>1?i-1:0),l=1;l<i;l++)a[l-1]=arguments[l];null==ez||ez.apply(void 0,[e].concat(a))},onKeyDown:function(e){var t,n=tu(),o=e.key,r="Enter"===o;if(r&&("combobox"!==W&&e.preventDefault(),e9||tt(!0)),ts(!!eQ),"Backspace"===o&&!n&&eH&&!eQ&&H.length){for(var i=(0,l.A)(H),a=null,c=i.length-1;c>=0;c-=1){var u=i[c];if(!u.disabled){i.splice(c,1),a=u;break}}a&&N(i,{type:"remove",values:[a]})}for(var s=arguments.length,d=Array(s>1?s-1:0),f=1;f<s;f++)d[f-1]=arguments[f];!e9||r&&td.current||(r&&(td.current=!0),null===(t=e_.current)||void 0===t||t.onKeyDown.apply(t,[e].concat(d))),null==eR||eR.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var t,n=arguments.length,o=Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];e9&&(null===(t=e_.current)||void 0===t||t.onKeyUp.apply(t,[e].concat(o))),"Enter"===e.key&&(td.current=!1),null==eM||eM.apply(void 0,[e].concat(o))},onFocus:function(){eG(!0),!V&&(eI&&!tf.current&&eI.apply(void 0,arguments),ex.includes("focus")&&tt(!0)),tf.current=!0},onBlur:function(){eK.current=!0,eG(!1,function(){tf.current=!1,eK.current=!1,tt(!1)}),!V&&(eQ&&("tags"===W?ea(eQ,{source:"submit"}):"multiple"===W&&ea("",{source:"blur"})),eO&&eO.apply(void 0,arguments))}}),o.createElement(X,{visible:eq&&!e9,values:H}),tS,S,tA&&ty),o.createElement(y.Provider,{value:tg},C)}),$=function(){return null};$.isSelectOptGroup=!0;var J=function(){return null};J.isSelectOption=!0;var Z=n(58676),ee=n(70527),et=n(3487),en=["disabled","title","children","style","className"];function eo(e){return"string"==typeof e||"number"==typeof e}var er=o.forwardRef(function(e,t){var n=o.useContext(y),r=n.prefixCls,u=n.id,f=n.open,p=n.multiple,m=n.mode,v=n.searchValue,g=n.toggleOpen,h=n.notFoundContent,A=n.onPopupScroll,w=o.useContext(Y),C=w.maxCount,x=w.flattenOptions,I=w.onActiveValue,O=w.defaultActiveFirstOption,M=w.onSelect,R=w.menuItemSelectedIcon,z=w.rawValues,D=w.fieldNames,H=w.virtual,N=w.direction,T=w.listHeight,B=w.listItemHeight,P=w.optionRender,k="".concat(r,"-item"),L=(0,Z.A)(function(){return x},[f,x],function(e,t){return t[0]&&e[1]!==t[1]}),j=o.useRef(null),W=o.useMemo(function(){return p&&F(C)&&(null==z?void 0:z.size)>=C},[p,C,null==z?void 0:z.size]),V=function(e){e.preventDefault()},_=function(e){var t;null===(t=j.current)||void 0===t||t.scrollTo("number"==typeof e?{index:e}:e)},K=o.useCallback(function(e){return"combobox"!==m&&z.has(e)},[m,(0,l.A)(z).toString(),z.size]),X=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=L.length,o=0;o<n;o+=1){var r=(e+o*t+n)%n,i=L[r]||{},a=i.group,l=i.data;if(!a&&!(null!=l&&l.disabled)&&(K(l.value)||!W))return r}return -1},q=o.useState(function(){return X(0)}),G=(0,s.A)(q,2),U=G[0],Q=G[1],$=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Q(e);var n={source:t?"keyboard":"mouse"},o=L[e];if(!o){I(null,-1,n);return}I(o.value,e,n)};(0,o.useEffect)(function(){$(!1!==O?X(0):-1)},[L.length,v]);var J=o.useCallback(function(e){return"combobox"===m?String(e).toLowerCase()===v.toLowerCase():z.has(e)},[m,v,(0,l.A)(z).toString(),z.size]);(0,o.useEffect)(function(){var e,t=setTimeout(function(){if(!p&&f&&1===z.size){var e=Array.from(z)[0],t=L.findIndex(function(t){return t.data.value===e});-1!==t&&($(t),_(t))}});return f&&(null===(e=j.current)||void 0===e||e.scrollTo(void 0)),function(){return clearTimeout(t)}},[f,v]);var er=function(e){void 0!==e&&M(e,{selected:!z.has(e)}),p||g(!1)};if(o.useImperativeHandle(t,function(){return{onKeyDown:function(e){var t=e.which,n=e.ctrlKey;switch(t){case E.A.N:case E.A.P:case E.A.UP:case E.A.DOWN:var o=0;if(t===E.A.UP?o=-1:t===E.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&n&&(t===E.A.N?o=1:t===E.A.P&&(o=-1)),0!==o){var r=X(U+o,o);_(r),$(r,!0)}break;case E.A.TAB:case E.A.ENTER:var i,a=L[U];!a||null!=a&&null!==(i=a.data)&&void 0!==i&&i.disabled||W?er(void 0):er(a.value),f&&e.preventDefault();break;case E.A.ESC:g(!1),f&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){_(e)}}}),0===L.length)return o.createElement("div",{role:"listbox",id:"".concat(u,"_list"),className:"".concat(k,"-empty"),onMouseDown:V},h);var ei=Object.keys(D).map(function(e){return D[e]}),ea=function(e){return e.label};function el(e,t){return{role:e.group?"presentation":"option",id:"".concat(u,"_list_").concat(t)}}var ec=function(e){var t=L[e];if(!t)return null;var n=t.data||{},r=n.value,i=t.group,l=(0,S.A)(n,!0),c=ea(t);return t?o.createElement("div",(0,a.A)({"aria-label":"string"!=typeof c||i?null:c},l,{key:e},el(t,e),{"aria-selected":J(r)}),r):null},eu={role:"listbox",id:"".concat(u,"_list")};return o.createElement(o.Fragment,null,H&&o.createElement("div",(0,a.A)({},eu,{style:{height:0,width:0,overflow:"hidden"}}),ec(U-1),ec(U),ec(U+1)),o.createElement(et.A,{itemKey:"key",ref:j,data:L,height:T,itemHeight:B,fullHeight:!1,onMouseDown:V,onScroll:A,virtual:H,direction:N,innerProps:H?null:eu},function(e,t){var n=e.group,r=e.groupOption,l=e.data,u=e.label,s=e.value,f=l.key;if(n){var p,m=null!==(p=l.title)&&void 0!==p?p:eo(u)?u.toString():void 0;return o.createElement("div",{className:i()(k,"".concat(k,"-group"),l.className),title:m},void 0!==u?u:f)}var v=l.disabled,g=l.title,h=(l.children,l.style),A=l.className,y=(0,d.A)(l,en),w=(0,ee.A)(y,ei),E=K(s),C=v||!E&&W,x="".concat(k,"-option"),I=i()(k,x,A,(0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(x,"-grouped"),r),"".concat(x,"-active"),U===t&&!C),"".concat(x,"-disabled"),C),"".concat(x,"-selected"),E)),O=ea(e),M=!R||"function"==typeof R||E,z="number"==typeof O?O:O||s,D=eo(z)?z.toString():void 0;return void 0!==g&&(D=g),o.createElement("div",(0,a.A)({},(0,S.A)(w),H?{}:el(e,t),{"aria-selected":J(s),className:I,title:D,onMouseMove:function(){U===t||C||$(t)},onClick:function(){C||er(s)},style:h}),o.createElement("div",{className:"".concat(x,"-content")},"function"==typeof P?P(e,{index:t}):z),o.isValidElement(R)||E,M&&o.createElement(b,{className:"".concat(k,"-option-state"),customizeIcon:R,customizeIconProps:{value:s,disabled:C,isSelected:E}},E?"✓":null))}))});let ei=function(e,t){var n=o.useRef({values:new Map,options:new Map});return[o.useMemo(function(){var o=n.current,r=o.values,i=o.options,a=e.map(function(e){if(void 0===e.label){var t;return(0,u.A)((0,u.A)({},e),{},{label:null===(t=r.get(e.value))||void 0===t?void 0:t.label})}return e}),l=new Map,c=new Map;return a.forEach(function(e){l.set(e.value,e),c.set(e.value,t.get(e.value)||i.get(e.value))}),n.current.values=l,n.current.options=c,a},[e,t]),o.useCallback(function(e){return t.get(e)||n.current.options.get(e)},[t])]};function ea(e,t){return I(e).join("").toUpperCase().includes(t)}var el=n(30306),ec=0,eu=(0,el.A)(),es=n(63588),ed=["children","value"],ef=["children"];function ep(e){var t=o.useRef();return t.current=e,o.useCallback(function(){return t.current.apply(t,arguments)},[])}var em=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],ev=["inputValue"],eg=o.forwardRef(function(e,t){var n,r,i,m,v,g=e.id,h=e.mode,b=e.prefixCls,A=e.backfill,y=e.fieldNames,w=e.inputValue,E=e.searchValue,S=e.onSearch,C=e.autoClearSearchValue,x=void 0===C||C,O=e.onSelect,M=e.onDeselect,R=e.dropdownMatchSelectWidth,z=void 0===R||R,D=e.filterOption,H=e.filterSort,N=e.optionFilterProp,T=e.optionLabelProp,B=e.options,P=e.optionRender,k=e.children,L=e.defaultActiveFirstOption,j=e.menuItemSelectedIcon,F=e.virtual,K=e.direction,X=e.listHeight,q=void 0===X?200:X,G=e.listItemHeight,$=void 0===G?20:G,J=e.labelRender,Z=e.value,ee=e.defaultValue,et=e.labelInValue,en=e.onChange,eo=e.maxCount,el=(0,d.A)(e,em),eg=(n=o.useState(),i=(r=(0,s.A)(n,2))[0],m=r[1],o.useEffect(function(){var e;m("rc_select_".concat((eu?(e=ec,ec+=1):e="TEST_OR_SSR",e)))},[]),g||i),eh=U(h),eb=!!(!B&&k),eA=o.useMemo(function(){return(void 0!==D||"combobox"!==h)&&D},[D,h]),ey=o.useMemo(function(){return V(y,eb)},[JSON.stringify(y),eb]),ew=(0,p.A)("",{value:void 0!==E?E:w,postState:function(e){return e||""}}),eE=(0,s.A)(ew,2),eS=eE[0],eC=eE[1],ex=o.useMemo(function(){var e=B;B||(e=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,es.A)(t).map(function(t,r){if(!o.isValidElement(t)||!t.type)return null;var i,a,l,c,s,f=t.type.isSelectOptGroup,p=t.key,m=t.props,v=m.children,g=(0,d.A)(m,ef);return n||!f?(i=t.key,l=(a=t.props).children,c=a.value,s=(0,d.A)(a,ed),(0,u.A)({key:i,value:void 0!==c?c:i,children:l},s)):(0,u.A)((0,u.A)({key:"__RC_SELECT_GRP__".concat(null===p?r:p,"__"),label:p},g),{},{options:e(v)})}).filter(function(e){return e})}(k));var t=new Map,n=new Map,r=function(e,t,n){n&&"string"==typeof n&&e.set(t[n],t)};return function e(o){for(var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=0;a<o.length;a+=1){var l=o[a];!l[ey.options]||i?(t.set(l[ey.value],l),r(n,l,ey.label),r(n,l,N),r(n,l,T)):e(l[ey.options],!0)}}(e),{options:e,valueOptions:t,labelOptions:n}},[B,k,ey,N,T]),eI=ex.valueOptions,eO=ex.labelOptions,eM=ex.options,eR=o.useCallback(function(e){return I(e).map(function(e){e&&"object"===(0,f.A)(e)?(o=e.key,n=e.label,t=null!==(a=e.value)&&void 0!==a?a:o):t=e;var t,n,o,r,i,a,l,c=eI.get(t);return c&&(void 0===n&&(n=null==c?void 0:c[T||ey.label]),void 0===o&&(o=null!==(l=null==c?void 0:c.key)&&void 0!==l?l:t),r=null==c?void 0:c.disabled,i=null==c?void 0:c.title),{label:n,value:t,key:o,disabled:r,title:i}})},[ey,T,eI]),ez=(0,p.A)(ee,{value:Z}),eD=(0,s.A)(ez,2),eH=eD[0],eN=eD[1],eT=ei(o.useMemo(function(){var e,t,n=eR(eh&&null===eH?[]:eH);return"combobox"!==h||(t=null===(e=n[0])||void 0===e?void 0:e.value)||0===t?n:[]},[eH,eR,h,eh]),eI),eB=(0,s.A)(eT,2),eP=eB[0],ek=eB[1],eL=o.useMemo(function(){if(!h&&1===eP.length){var e=eP[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return eP.map(function(e){var t;return(0,u.A)((0,u.A)({},e),{},{label:null!==(t="function"==typeof J?J(e):e.label)&&void 0!==t?t:e.value})})},[h,eP,J]),ej=o.useMemo(function(){return new Set(eP.map(function(e){return e.value}))},[eP]);o.useEffect(function(){if("combobox"===h){var e,t=null===(e=eP[0])||void 0===e?void 0:e.value;eC(null!=t?String(t):"")}},[eP]);var eW=ep(function(e,t){var n=null!=t?t:e;return(0,c.A)((0,c.A)({},ey.value,e),ey.label,n)}),eF=(v=o.useMemo(function(){if("tags"!==h)return eM;var e=(0,l.A)(eM);return(0,l.A)(eP).sort(function(e,t){return e.value<t.value?-1:1}).forEach(function(t){var n=t.value;eI.has(n)||e.push(eW(n,t.label))}),e},[eW,eM,eI,eP,h]),o.useMemo(function(){if(!eS||!1===eA)return v;var e=ey.options,t=ey.label,n=ey.value,o=[],r="function"==typeof eA,i=eS.toUpperCase(),a=r?eA:function(o,r){return N?ea(r[N],i):r[e]?ea(r["children"!==t?t:"label"],i):ea(r[n],i)},l=r?function(e){return _(e)}:function(e){return e};return v.forEach(function(t){if(t[e]){if(a(eS,l(t)))o.push(t);else{var n=t[e].filter(function(e){return a(eS,l(e))});n.length&&o.push((0,u.A)((0,u.A)({},t),{},(0,c.A)({},e,n)))}return}a(eS,l(t))&&o.push(t)}),o},[v,eA,N,eS,ey])),eV=o.useMemo(function(){return"tags"!==h||!eS||eF.some(function(e){return e[N||"value"]===eS})||eF.some(function(e){return e[ey.value]===eS})?eF:[eW(eS)].concat((0,l.A)(eF))},[eW,N,h,eF,eS,ey]),e_=o.useMemo(function(){return H?function e(t){return(0,l.A)(t).sort(function(e,t){return H(e,t,{searchValue:eS})}).map(function(t){return Array.isArray(t.options)?(0,u.A)((0,u.A)({},t),{},{options:t.options.length>0?e(t.options):t.options}):t})}(eV):eV},[eV,H,eS]),eK=o.useMemo(function(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],i=V(n,!1),a=i.label,l=i.value,c=i.options,u=i.groupLabel;return!function e(t,n){Array.isArray(t)&&t.forEach(function(t){if(!n&&c in t){var i=t[u];void 0===i&&o&&(i=t.label),r.push({key:W(t,r.length),group:!0,data:t,label:i}),e(t[c],!0)}else{var s=t[l];r.push({key:W(t,r.length),groupOption:n,data:t,label:t[a],value:s})}})}(e,!1),r}(e_,{fieldNames:ey,childrenAsData:eb})},[e_,ey,eb]),eY=function(e){var t=eR(e);if(eN(t),en&&(t.length!==eP.length||t.some(function(e,t){var n;return(null===(n=eP[t])||void 0===n?void 0:n.value)!==(null==e?void 0:e.value)}))){var n=et?t:t.map(function(e){return e.value}),o=t.map(function(e){return _(ek(e.value))});en(eh?n:n[0],eh?o:o[0])}},eX=o.useState(null),eq=(0,s.A)(eX,2),eG=eq[0],eU=eq[1],eQ=o.useState(0),e$=(0,s.A)(eQ,2),eJ=e$[0],eZ=e$[1],e0=void 0!==L?L:"combobox"!==h,e1=o.useCallback(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.source;eZ(t),A&&"combobox"===h&&null!==e&&"keyboard"===(void 0===o?"keyboard":o)&&eU(String(e))},[A,h]),e2=function(e,t,n){var o=function(){var t,n=ek(e);return[et?{label:null==n?void 0:n[ey.label],value:e,key:null!==(t=null==n?void 0:n.key)&&void 0!==t?t:e}:e,_(n)]};if(t&&O){var r=o(),i=(0,s.A)(r,2);O(i[0],i[1])}else if(!t&&M&&"clear"!==n){var a=o(),l=(0,s.A)(a,2);M(l[0],l[1])}},e4=ep(function(e,t){var n=!eh||t.selected;eY(n?eh?[].concat((0,l.A)(eP),[e]):[e]:eP.filter(function(t){return t.value!==e})),e2(e,n),"combobox"===h?eU(""):(!U||x)&&(eC(""),eU(""))}),e5=o.useMemo(function(){var e=!1!==F&&!1!==z;return(0,u.A)((0,u.A)({},ex),{},{flattenOptions:eK,onActiveValue:e1,defaultActiveFirstOption:e0,onSelect:e4,menuItemSelectedIcon:j,rawValues:ej,fieldNames:ey,virtual:e,direction:K,listHeight:q,listItemHeight:$,childrenAsData:eb,maxCount:eo,optionRender:P})},[eo,ex,eK,e1,e0,e4,j,ej,ey,F,z,K,q,$,eb,P]);return o.createElement(Y.Provider,{value:e5},o.createElement(Q,(0,a.A)({},el,{id:eg,prefixCls:void 0===b?"rc-select":b,ref:t,omitDomProps:ev,mode:h,displayValues:eL,onDisplayValuesChange:function(e,t){eY(e);var n=t.type,o=t.values;("remove"===n||"clear"===n)&&o.forEach(function(e){e2(e.value,!1,n)})},direction:K,searchValue:eS,onSearch:function(e,t){if(eC(e),eU(null),"submit"===t.source){var n=(e||"").trim();n&&(eY(Array.from(new Set([].concat((0,l.A)(ej),[n])))),e2(n,!0),eC(""));return}"blur"!==t.source&&("combobox"===h&&eY(e),null==S||S(e))},autoClearSearchValue:x,onSearchSplit:function(e){var t=e;"tags"!==h&&(t=e.map(function(e){var t=eO.get(e);return null==t?void 0:t.value}).filter(function(e){return void 0!==e}));var n=Array.from(new Set([].concat((0,l.A)(ej),(0,l.A)(t))));eY(n),n.forEach(function(e){e2(e,!0)})},dropdownMatchSelectWidth:z,OptionList:er,emptyOptions:!eK.length,activeValue:eG,activeDescendantId:"".concat(eg,"_list_").concat(eJ)})))});eg.Option=J,eg.OptGroup=$;var eh=n(78877),eb=n(19635),eA=n(11679),ey=n(55504),ew=n(31049),eE=n(28744),eS=n(30033),eC=n(7926),ex=n(27651),eI=n(30149),eO=n(51388),eM=n(78741),eR=n(68711);let ez=e=>{let t={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},t),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},t),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},t),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},t),{points:["br","tr"],offset:[0,-4]})}};var eD=n(70695),eH=n(98246),eN=n(1086),eT=n(56204),eB=n(46777),eP=n(96513);let ek=e=>{let{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:t,padding:r,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},eL=e=>{let{antCls:t,componentCls:n}=e,o="".concat(n,"-item"),r="&".concat(t,"-slide-up-enter").concat(t,"-slide-up-enter-active"),i="&".concat(t,"-slide-up-appear").concat(t,"-slide-up-appear-active"),a="&".concat(t,"-slide-up-leave").concat(t,"-slide-up-leave-active"),l="".concat(n,"-dropdown-placement-"),c="".concat(o,"-option-selected");return[{["".concat(n,"-dropdown")]:Object.assign(Object.assign({},(0,eD.dF)(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,["\n          ".concat(r).concat(l,"bottomLeft,\n          ").concat(i).concat(l,"bottomLeft\n        ")]:{animationName:eB.ox},["\n          ".concat(r).concat(l,"topLeft,\n          ").concat(i).concat(l,"topLeft,\n          ").concat(r).concat(l,"topRight,\n          ").concat(i).concat(l,"topRight\n        ")]:{animationName:eB.nP},["".concat(a).concat(l,"bottomLeft")]:{animationName:eB.vR},["\n          ".concat(a).concat(l,"topLeft,\n          ").concat(a).concat(l,"topRight\n        ")]:{animationName:eB.YU},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},ek(e)),{cursor:"pointer",transition:"background ".concat(e.motionDurationSlow," ease"),borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},eD.L9),"&-state":{flex:"none",display:"flex",alignItems:"center"},["&-active:not(".concat(o,"-option-disabled)")]:{backgroundColor:e.optionActiveBg},["&-selected:not(".concat(o,"-option-disabled)")]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,["".concat(o,"-option-state")]:{color:e.colorPrimary}},"&-disabled":{["&".concat(o,"-option-selected")]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},ek(e)),{color:e.colorTextDisabled})}),["".concat(c,":has(+ ").concat(c,")")]:{borderEndStartRadius:0,borderEndEndRadius:0,["& + ".concat(c)]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},(0,eB._j)(e,"slide-up"),(0,eB._j)(e,"slide-down"),(0,eP.Mh)(e,"move-up"),(0,eP.Mh)(e,"move-down")]};var ej=n(68522),eW=n(67548);function eF(e,t){let{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:r}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),a=t?"".concat(n,"-").concat(t):"";return{["".concat(n,"-single").concat(a)]:{fontSize:e.fontSize,height:e.controlHeight,["".concat(n,"-selector")]:Object.assign(Object.assign({},(0,eD.dF)(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",["".concat(n,"-selection-wrap:after")]:{lineHeight:(0,eW.zA)(i)},["".concat(n,"-selection-search")]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},["\n          ".concat(n,"-selection-item,\n          ").concat(n,"-selection-placeholder\n        ")]:{display:"block",padding:0,lineHeight:(0,eW.zA)(i),transition:"all ".concat(e.motionDurationSlow,", visibility 0s"),alignSelf:"center"},["".concat(n,"-selection-placeholder")]:{transition:"none",pointerEvents:"none"},[["&:after","".concat(n,"-selection-item:empty:after"),"".concat(n,"-selection-placeholder:empty:after")].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),["\n        &".concat(n,"-show-arrow ").concat(n,"-selection-item,\n        &").concat(n,"-show-arrow ").concat(n,"-selection-search,\n        &").concat(n,"-show-arrow ").concat(n,"-selection-placeholder\n      ")]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},["&".concat(n,"-open ").concat(n,"-selection-item")]:{color:e.colorTextPlaceholder},["&:not(".concat(n,"-customize-input)")]:{["".concat(n,"-selector")]:{width:"100%",height:"100%",alignItems:"center",padding:"0 ".concat((0,eW.zA)(o)),["".concat(n,"-selection-search-input")]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:(0,eW.zA)(i)}}},["&".concat(n,"-customize-input")]:{["".concat(n,"-selector")]:{"&:after":{display:"none"},["".concat(n,"-selection-search")]:{position:"static",width:"100%"},["".concat(n,"-selection-placeholder")]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:"0 ".concat((0,eW.zA)(o)),"&:after":{display:"none"}}}}}}}let eV=(e,t)=>{let{componentCls:n,antCls:o,controlOutlineWidth:r}=e;return{["&:not(".concat(n,"-customize-input) ").concat(n,"-selector")]:{border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(t.borderColor),background:e.selectorBg},["&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(o,"-pagination-size-changer)")]:{["&:hover ".concat(n,"-selector")]:{borderColor:t.hoverBorderHover},["".concat(n,"-focused& ").concat(n,"-selector")]:{borderColor:t.activeBorderColor,boxShadow:"0 0 0 ".concat((0,eW.zA)(r)," ").concat(t.activeOutlineColor),outline:0},["".concat(n,"-prefix")]:{color:t.color}}}},e_=(e,t)=>({["&".concat(e.componentCls,"-status-").concat(t.status)]:Object.assign({},eV(e,t))}),eK=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},eV(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),e_(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),e_(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{["&".concat(e.componentCls,"-disabled")]:{["&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector")]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},["&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item")]:{background:e.multipleItemBg,border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)}})}),eY=(e,t)=>{let{componentCls:n,antCls:o}=e;return{["&:not(".concat(n,"-customize-input) ").concat(n,"-selector")]:{background:t.bg,border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," transparent"),color:t.color},["&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(o,"-pagination-size-changer)")]:{["&:hover ".concat(n,"-selector")]:{background:t.hoverBg},["".concat(n,"-focused& ").concat(n,"-selector")]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},eX=(e,t)=>({["&".concat(e.componentCls,"-status-").concat(t.status)]:Object.assign({},eY(e,t))}),eq=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},eY(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),eX(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),eX(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{["&".concat(e.componentCls,"-disabled")]:{["&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector")]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},["&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item")]:{background:e.colorBgContainer,border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit)}})}),eG=e=>({"&-borderless":{["".concat(e.componentCls,"-selector")]:{background:"transparent",border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," transparent")},["&".concat(e.componentCls,"-disabled")]:{["&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector")]:{color:e.colorTextDisabled}},["&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item")]:{background:e.multipleItemBg,border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)},["&".concat(e.componentCls,"-status-error")]:{["".concat(e.componentCls,"-prefix, ").concat(e.componentCls,"-selection-item")]:{color:e.colorError}},["&".concat(e.componentCls,"-status-warning")]:{["".concat(e.componentCls,"-prefix, ").concat(e.componentCls,"-selection-item")]:{color:e.colorWarning}}}}),eU=(e,t)=>{let{componentCls:n,antCls:o}=e;return{["&:not(".concat(n,"-customize-input) ").concat(n,"-selector")]:{borderWidth:"0 0 ".concat((0,eW.zA)(e.lineWidth)," 0"),borderStyle:"none none ".concat(e.lineType," none"),borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},["&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(o,"-pagination-size-changer)")]:{["&:hover ".concat(n,"-selector")]:{borderColor:t.hoverBorderHover},["".concat(n,"-focused& ").concat(n,"-selector")]:{borderColor:t.activeBorderColor,outline:0},["".concat(n,"-prefix")]:{color:t.color}}}},eQ=(e,t)=>({["&".concat(e.componentCls,"-status-").concat(t.status)]:Object.assign({},eU(e,t))}),e$=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},eU(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),eQ(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),eQ(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{["&".concat(e.componentCls,"-disabled")]:{["&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector")]:{color:e.colorTextDisabled}},["&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item")]:{background:e.multipleItemBg,border:"".concat((0,eW.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)}})}),eJ=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},eK(e)),eq(e)),eG(e)),e$(e))}),eZ=e=>{let{componentCls:t}=e;return{position:"relative",transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut),input:{cursor:"pointer"},["".concat(t,"-show-search&")]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},["".concat(t,"-disabled&")]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},e0=e=>{let{componentCls:t}=e;return{["".concat(t,"-selection-search-input")]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},e1=e=>{let{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:r}=e;return{[n]:Object.assign(Object.assign({},(0,eD.dF)(e)),{position:"relative",display:"inline-flex",cursor:"pointer",["&:not(".concat(n,"-customize-input) ").concat(n,"-selector")]:Object.assign(Object.assign({},eZ(e)),e0(e)),["".concat(n,"-selection-item")]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},eD.L9),{["> ".concat(t,"-typography")]:{display:"inline"}}),["".concat(n,"-selection-placeholder")]:Object.assign(Object.assign({},eD.L9),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),["".concat(n,"-arrow")]:Object.assign(Object.assign({},(0,eD.Nk)()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:"opacity ".concat(e.motionDurationSlow," ease"),[r]:{verticalAlign:"top",transition:"transform ".concat(e.motionDurationSlow),"> svg":{verticalAlign:"top"},["&:not(".concat(n,"-suffix)")]:{pointerEvents:"auto"}},["".concat(n,"-disabled &")]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),["".concat(n,"-selection-wrap")]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},["".concat(n,"-prefix")]:{flex:"none",marginInlineEnd:e.selectAffixPadding},["".concat(n,"-clear")]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:"color ".concat(e.motionDurationMid," ease, opacity ").concat(e.motionDurationSlow," ease"),textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},["&:hover ".concat(n,"-clear")]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}}),["".concat(n,"-status")]:{"&-error, &-warning, &-success, &-validating":{["&".concat(n,"-has-feedback")]:{["".concat(n,"-clear")]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},e2=e=>{let{componentCls:t}=e;return[{[t]:{["&".concat(t,"-in-form-item")]:{width:"100%"}}},e1(e),function(e){let{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[eF(e),eF((0,eT.oX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{["".concat(t,"-single").concat(t,"-sm")]:{["&:not(".concat(t,"-customize-input)")]:{["".concat(t,"-selector")]:{padding:"0 ".concat((0,eW.zA)(n))},["&".concat(t,"-show-arrow ").concat(t,"-selection-search")]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},["\n            &".concat(t,"-show-arrow ").concat(t,"-selection-item,\n            &").concat(t,"-show-arrow ").concat(t,"-selection-placeholder\n          ")]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},eF((0,eT.oX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}(e),(0,ej.Ay)(e),eL(e),{["".concat(t,"-rtl")]:{direction:"rtl"}},(0,eH.G)(e,{borderElCls:"".concat(t,"-selector"),focusElCls:"".concat(t,"-focused")})]},e4=(0,eN.OF)("Select",(e,t)=>{let{rootPrefixCls:n}=t,o=(0,eT.oX)(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[e2(o),eJ(o)]},e=>{let{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:r,controlHeightSM:i,controlHeightLG:a,paddingXXS:l,controlPaddingHorizontal:c,zIndexPopupBase:u,colorText:s,fontWeightStrong:d,controlItemBgActive:f,controlItemBgHover:p,colorBgContainer:m,colorFillSecondary:v,colorBgContainerDisabled:g,colorTextDisabled:h,colorPrimaryHover:b,colorPrimary:A,controlOutline:y}=e,w=2*l,E=2*o,S=Math.min(r-w,r-E),C=Math.min(i-w,i-E),x=Math.min(a-w,a-E);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(l/2),zIndexPopup:u+50,optionSelectedColor:s,optionSelectedFontWeight:d,optionSelectedBg:f,optionActiveBg:p,optionPadding:"".concat((r-t*n)/2,"px ").concat(c,"px"),optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:m,clearBg:m,singleItemHeightLG:a,multipleItemBg:v,multipleItemBorderColor:"transparent",multipleItemHeight:S,multipleItemHeightSM:C,multipleItemHeightLG:x,multipleSelectorBgDisabled:g,multipleItemColorDisabled:h,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:A,activeOutlineColor:y,selectAffixPadding:l}},{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var e5=n(15867),e6=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let e3="SECRET_COMBOBOX_MODE_DO_NOT_USE",e7=o.forwardRef((e,t)=>{var n;let r;let{prefixCls:a,bordered:l,className:c,rootClassName:u,getPopupContainer:s,popupClassName:d,dropdownClassName:f,listHeight:p=256,placement:m,listItemHeight:v,size:g,disabled:h,notFoundContent:b,status:A,builtinPlacements:y,dropdownMatchSelectWidth:w,popupMatchSelectWidth:E,direction:S,style:C,allowClear:x,variant:I,dropdownStyle:O,transitionName:M,tagRender:R,maxCount:z,prefix:D}=e,H=e6(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix"]),{getPopupContainer:N,getPrefixCls:T,renderEmpty:B,direction:P,virtual:k,popupMatchSelectWidth:L,popupOverflow:j}=o.useContext(ew.QO),W=(0,ew.TP)("select"),[,F]=(0,eR.Ay)(),V=null!=v?v:null==F?void 0:F.controlHeight,_=T("select",a),K=T(),Y=null!=S?S:P,{compactSize:X,compactItemClassnames:q}=(0,eM.RQ)(_,Y),[G,U]=(0,eO.A)("select",I,l),Q=(0,eC.A)(_),[$,J,Z]=e4(_,Q),et=o.useMemo(()=>{let{mode:t}=e;return"combobox"===t?void 0:t===e3?"combobox":t},[e.mode]),en="multiple"===et||"tags"===et,eo=function(e,t){return void 0!==t?t:null!==e}(e.suffixIcon,e.showArrow),er=null!==(n=null!=E?E:w)&&void 0!==n?n:L,{status:ei,hasFeedback:ea,isFormItemInput:el,feedbackIcon:ec}=o.useContext(eI.$W),eu=(0,ey.v)(ei,A);r=void 0!==b?b:"combobox"===et?null:(null==B?void 0:B("Select"))||o.createElement(eE.A,{componentName:"Select"});let{suffixIcon:es,itemIcon:ed,removeIcon:ef,clearIcon:ep}=(0,e5.A)(Object.assign(Object.assign({},H),{multiple:en,hasFeedback:ea,feedbackIcon:ec,showSuffixIcon:eo,prefixCls:_,componentName:"Select"})),em=(0,ee.A)(H,["suffixIcon","itemIcon"]),ev=i()(d||f,{["".concat(_,"-dropdown-").concat(Y)]:"rtl"===Y},u,Z,Q,J),eA=(0,ex.A)(e=>{var t;return null!==(t=null!=g?g:X)&&void 0!==t?t:e}),eD=o.useContext(eS.A),eH=i()({["".concat(_,"-lg")]:"large"===eA,["".concat(_,"-sm")]:"small"===eA,["".concat(_,"-rtl")]:"rtl"===Y,["".concat(_,"-").concat(G)]:U,["".concat(_,"-in-form-item")]:el},(0,ey.L)(_,eu,ea),q,W.className,c,u,Z,Q,J),eN=o.useMemo(()=>void 0!==m?m:"rtl"===Y?"bottomRight":"bottomLeft",[m,Y]),[eT]=(0,eh.YK)("SelectLike",null==O?void 0:O.zIndex);return $(o.createElement(eg,Object.assign({ref:t,virtual:k,showSearch:W.showSearch},em,{style:Object.assign(Object.assign({},W.style),C),dropdownMatchSelectWidth:er,transitionName:(0,eb.b)(K,"slide-up",M),builtinPlacements:y||ez(j),listHeight:p,listItemHeight:V,mode:et,prefixCls:_,placement:eN,direction:Y,prefix:D,suffixIcon:es,menuItemSelectedIcon:ed,removeIcon:ef,allowClear:!0===x?{clearIcon:ep}:x,notFoundContent:r,className:eH,getPopupContainer:s||N,dropdownClassName:ev,disabled:null!=h?h:eD,dropdownStyle:Object.assign(Object.assign({},O),{zIndex:eT}),maxCount:en?z:void 0,tagRender:en?R:void 0})))}),e9=(0,eA.A)(e7,"dropdownAlign");e7.SECRET_COMBOBOX_MODE_DO_NOT_USE=e3,e7.Option=J,e7.OptGroup=$,e7._InternalPanelDoNotUseOrYouWillBeFired=e9;let e8=e7},68522:(e,t,n)=>{n.d(t,{Ay:()=>d,Q3:()=>c,_8:()=>a});var o=n(67548),r=n(70695),i=n(56204);let a=e=>{let{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:r,INTERNAL_FIXED_ITEM_MARGIN:i}=e,a=e.max(e.calc(n).sub(r).equal(),0),l=e.max(e.calc(a).sub(i).equal(),0);return{basePadding:a,containerPadding:l,itemHeight:(0,o.zA)(t),itemLineHeight:(0,o.zA)(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},l=e=>{let{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()},c=e=>{let{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:i,paddingXS:a,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:c,colorIcon:u,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:d}=e;return{["".concat(t,"-selection-overflow")]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},["".concat(t,"-selection-item")]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:d,borderRadius:o,cursor:"default",transition:"font-size ".concat(i,", line-height ").concat(i,", height ").concat(i),marginInlineEnd:e.calc(d).mul(2).equal(),paddingInlineStart:a,paddingInlineEnd:e.calc(a).div(2).equal(),["".concat(t,"-disabled&")]:{color:l,borderColor:c,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(a).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},(0,r.Nk)()),{display:"inline-flex",alignItems:"center",color:u,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",["> ".concat(n)]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},u=(e,t)=>{let{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:r}=e,i="".concat(n,"-selection-overflow"),u=e.multipleSelectItemHeight,s=l(e),d=t?"".concat(n,"-").concat(t):"",f=a(e);return{["".concat(n,"-multiple").concat(d)]:Object.assign(Object.assign({},c(e)),{["".concat(n,"-selector")]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:f.basePadding,paddingBlock:f.containerPadding,borderRadius:e.borderRadius,["".concat(n,"-disabled&")]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:"".concat((0,o.zA)(r)," 0"),lineHeight:(0,o.zA)(u),visibility:"hidden",content:'"\\a0"'}},["".concat(n,"-selection-item")]:{height:f.itemHeight,lineHeight:(0,o.zA)(f.itemLineHeight)},["".concat(n,"-selection-wrap")]:{alignSelf:"flex-start","&:after":{lineHeight:(0,o.zA)(u),marginBlock:r}},["".concat(n,"-prefix")]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal()},["".concat(i,"-item + ").concat(i,"-item,\n        ").concat(n,"-prefix + ").concat(n,"-selection-wrap\n      ")]:{["".concat(n,"-selection-search")]:{marginInlineStart:0},["".concat(n,"-selection-placeholder")]:{insetInlineStart:0}},["".concat(i,"-item-suffix")]:{minHeight:f.itemHeight,marginBlock:r},["".concat(n,"-selection-search")]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s).equal(),"\n          &-input,\n          &-mirror\n        ":{height:u,fontFamily:e.fontFamily,lineHeight:(0,o.zA)(u),transition:"all ".concat(e.motionDurationSlow)},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},["".concat(n,"-selection-placeholder")]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:"all ".concat(e.motionDurationSlow)}})}};function s(e,t){let{componentCls:n}=e,o=t?"".concat(n,"-").concat(t):"",r={["".concat(n,"-multiple").concat(o)]:{fontSize:e.fontSize,["".concat(n,"-selector")]:{["".concat(n,"-show-search&")]:{cursor:"text"}},["\n        &".concat(n,"-show-arrow ").concat(n,"-selector,\n        &").concat(n,"-allow-clear ").concat(n,"-selector\n      ")]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[u(e,t),r]}let d=e=>{let{componentCls:t}=e,n=(0,i.oX)(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=(0,i.oX)(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[s(e),s(n,"sm"),{["".concat(t,"-multiple").concat(t,"-sm")]:{["".concat(t,"-selection-placeholder")]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},["".concat(t,"-selection-search")]:{marginInlineStart:2}}},s(o,"lg")]}},15867:(e,t,n)=>{n.d(t,{A:()=>s});var o=n(12115),r=n(4768),i=n(6140),a=n(79624),l=n(10593),c=n(16419),u=n(5413);function s(e){let{suffixIcon:t,clearIcon:n,menuItemSelectedIcon:s,removeIcon:d,loading:f,multiple:p,hasFeedback:m,prefixCls:v,showSuffixIcon:g,feedbackIcon:h,showArrow:b,componentName:A}=e,y=null!=n?n:o.createElement(i.A,null),w=e=>null!==t||m||b?o.createElement(o.Fragment,null,!1!==g&&e,m&&h):null,E=null;if(void 0!==t)E=w(t);else if(f)E=w(o.createElement(c.A,{spin:!0}));else{let e="".concat(v,"-suffix");E=t=>{let{open:n,showSearch:r}=t;return n&&r?w(o.createElement(u.A,{className:e})):w(o.createElement(l.A,{className:e}))}}let S=null;return S=void 0!==s?s:p?o.createElement(r.A,null):null,{clearIcon:y,suffixIcon:E,itemIcon:S,removeIcon:void 0!==d?d:o.createElement(a.A,null)}}},96513:(e,t,n)=>{n.d(t,{Mh:()=>f});var o=n(67548),r=n(49698);let i=new o.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),a=new o.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),l=new o.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new o.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u=new o.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new o.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d={"move-up":{inKeyframes:new o.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:a},"move-left":{inKeyframes:l,outKeyframes:c},"move-right":{inKeyframes:u,outKeyframes:s}},f=(e,t)=>{let{antCls:n}=e,o="".concat(n,"-").concat(t),{inKeyframes:i,outKeyframes:a}=d[t];return[(0,r.b)(o,i,a,e.motionDurationMid),{["\n        ".concat(o,"-enter,\n        ").concat(o,"-appear\n      ")]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},["".concat(o,"-leave")]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},3487:(e,t,n)=>{n.d(t,{A:()=>T});var o=n(85407),r=n(21855),i=n(85268),a=n(1568),l=n(59912),c=n(64406),u=n(4617),s=n.n(u),d=n(30377),f=n(73042),p=n(66105),m=n(12115),v=n(47650),g=m.forwardRef(function(e,t){var n=e.height,r=e.offsetY,l=e.offsetX,c=e.children,u=e.prefixCls,f=e.onInnerResize,p=e.innerProps,v=e.rtl,g=e.extra,h={},b={display:"flex",flexDirection:"column"};return void 0!==r&&(h={height:n,position:"relative",overflow:"hidden"},b=(0,i.A)((0,i.A)({},b),{},(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({transform:"translateY(".concat(r,"px)")},v?"marginRight":"marginLeft",-l),"position","absolute"),"left",0),"right",0),"top",0))),m.createElement("div",{style:h},m.createElement(d.A,{onResize:function(e){e.offsetHeight&&f&&f()}},m.createElement("div",(0,o.A)({style:b,className:s()((0,a.A)({},"".concat(u,"-holder-inner"),u)),ref:t},p),c,g)))});function h(e){var t=e.children,n=e.setRef,o=m.useCallback(function(e){n(e)},[]);return m.cloneElement(t,{ref:o})}g.displayName="Filler";var b=n(13379),A=("undefined"==typeof navigator?"undefined":(0,r.A)(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);let y=function(e,t,n,o){var r=(0,m.useRef)(!1),i=(0,m.useRef)(null),a=(0,m.useRef)({top:e,bottom:t,left:n,right:o});return a.current.top=e,a.current.bottom=t,a.current.left=n,a.current.right=o,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e?t<0&&a.current.left||t>0&&a.current.right:t<0&&a.current.top||t>0&&a.current.bottom;return n&&o?(clearTimeout(i.current),r.current=!1):(!o||r.current)&&(clearTimeout(i.current),r.current=!0,i.current=setTimeout(function(){r.current=!1},50)),!r.current&&o}};var w=n(25514),E=n(98566),S=function(){function e(){(0,w.A)(this,e),(0,a.A)(this,"maps",void 0),(0,a.A)(this,"id",0),(0,a.A)(this,"diffKeys",new Set),this.maps=Object.create(null)}return(0,E.A)(e,[{key:"set",value:function(e,t){this.maps[e]=t,this.id+=1,this.diffKeys.add(e)}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffKeys.clear()}},{key:"getRecord",value:function(){return this.diffKeys}}]),e}();function C(e){var t=parseFloat(e);return isNaN(t)?0:t}var x=14/15;function I(e){return Math.floor(Math.pow(e,.5))}function O(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var M=m.forwardRef(function(e,t){var n=e.prefixCls,o=e.rtl,r=e.scrollOffset,c=e.scrollRange,u=e.onStartMove,d=e.onStopMove,f=e.onScroll,p=e.horizontal,v=e.spinSize,g=e.containerSize,h=e.style,A=e.thumbStyle,y=e.showScrollBar,w=m.useState(!1),E=(0,l.A)(w,2),S=E[0],C=E[1],x=m.useState(null),I=(0,l.A)(x,2),M=I[0],R=I[1],z=m.useState(null),D=(0,l.A)(z,2),H=D[0],N=D[1],T=!o,B=m.useRef(),P=m.useRef(),k=m.useState(y),L=(0,l.A)(k,2),j=L[0],W=L[1],F=m.useRef(),V=function(){!0!==y&&!1!==y&&(clearTimeout(F.current),W(!0),F.current=setTimeout(function(){W(!1)},3e3))},_=c-g||0,K=g-v||0,Y=m.useMemo(function(){return 0===r||0===_?0:r/_*K},[r,_,K]),X=m.useRef({top:Y,dragging:S,pageY:M,startTop:H});X.current={top:Y,dragging:S,pageY:M,startTop:H};var q=function(e){C(!0),R(O(e,p)),N(X.current.top),u(),e.stopPropagation(),e.preventDefault()};m.useEffect(function(){var e=function(e){e.preventDefault()},t=B.current,n=P.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",q,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",q)}},[]);var G=m.useRef();G.current=_;var U=m.useRef();U.current=K,m.useEffect(function(){if(S){var e,t=function(t){var n=X.current,o=n.dragging,r=n.pageY,i=n.startTop;b.A.cancel(e);var a=B.current.getBoundingClientRect(),l=g/(p?a.width:a.height);if(o){var c=(O(t,p)-r)*l,u=i;!T&&p?u-=c:u+=c;var s=G.current,d=U.current,m=Math.ceil((d?u/d:0)*s);m=Math.min(m=Math.max(m,0),s),e=(0,b.A)(function(){f(m,p)})}},n=function(){C(!1),d()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),b.A.cancel(e)}}},[S]),m.useEffect(function(){return V(),function(){clearTimeout(F.current)}},[r]),m.useImperativeHandle(t,function(){return{delayHidden:V}});var Q="".concat(n,"-scrollbar"),$={position:"absolute",visibility:j?null:"hidden"},J={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return p?($.height=8,$.left=0,$.right=0,$.bottom=0,J.height="100%",J.width=v,T?J.left=Y:J.right=Y):($.width=8,$.top=0,$.bottom=0,T?$.right=0:$.left=0,J.width="100%",J.height=v,J.top=Y),m.createElement("div",{ref:B,className:s()(Q,(0,a.A)((0,a.A)((0,a.A)({},"".concat(Q,"-horizontal"),p),"".concat(Q,"-vertical"),!p),"".concat(Q,"-visible"),j)),style:(0,i.A)((0,i.A)({},$),h),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:V},m.createElement("div",{ref:P,className:s()("".concat(Q,"-thumb"),(0,a.A)({},"".concat(Q,"-thumb-moving"),S)),style:(0,i.A)((0,i.A)({},J),A),onMouseDown:q}))});function R(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),Math.floor(n=Math.max(n,20))}var z=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],D=[],H={overflowY:"auto",overflowAnchor:"none"},N=m.forwardRef(function(e,t){var n,u,w,E,N,T,B,P,k,L,j,W,F,V,_,K,Y,X,q,G,U,Q,$,J,Z,ee,et,en,eo,er,ei,ea,el,ec,eu,es,ed,ef=e.prefixCls,ep=void 0===ef?"rc-virtual-list":ef,em=e.className,ev=e.height,eg=e.itemHeight,eh=e.fullHeight,eb=e.style,eA=e.data,ey=e.children,ew=e.itemKey,eE=e.virtual,eS=e.direction,eC=e.scrollWidth,ex=e.component,eI=e.onScroll,eO=e.onVirtualScroll,eM=e.onVisibleChange,eR=e.innerProps,ez=e.extraRender,eD=e.styles,eH=e.showScrollBar,eN=void 0===eH?"optional":eH,eT=(0,c.A)(e,z),eB=m.useCallback(function(e){return"function"==typeof ew?ew(e):null==e?void 0:e[ew]},[ew]),eP=function(e,t,n){var o=m.useState(0),r=(0,l.A)(o,2),i=r[0],a=r[1],c=(0,m.useRef)(new Map),u=(0,m.useRef)(new S),s=(0,m.useRef)(0);function d(){s.current+=1}function f(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];d();var t=function(){var e=!1;c.current.forEach(function(t,n){if(t&&t.offsetParent){var o=t.offsetHeight,r=getComputedStyle(t),i=r.marginTop,a=r.marginBottom,l=o+C(i)+C(a);u.current.get(n)!==l&&(u.current.set(n,l),e=!0)}}),e&&a(function(e){return e+1})};if(e)t();else{s.current+=1;var n=s.current;Promise.resolve().then(function(){n===s.current&&t()})}}return(0,m.useEffect)(function(){return d},[]),[function(o,r){var i=e(o),a=c.current.get(i);r?(c.current.set(i,r),f()):c.current.delete(i),!a!=!r&&(r?null==t||t(o):null==n||n(o))},f,u.current,i]}(eB,null,null),ek=(0,l.A)(eP,4),eL=ek[0],ej=ek[1],eW=ek[2],eF=ek[3],eV=!!(!1!==eE&&ev&&eg),e_=m.useMemo(function(){return Object.values(eW.maps).reduce(function(e,t){return e+t},0)},[eW.id,eW.maps]),eK=eV&&eA&&(Math.max(eg*eA.length,e_)>ev||!!eC),eY="rtl"===eS,eX=s()(ep,(0,a.A)({},"".concat(ep,"-rtl"),eY),em),eq=eA||D,eG=(0,m.useRef)(),eU=(0,m.useRef)(),eQ=(0,m.useRef)(),e$=(0,m.useState)(0),eJ=(0,l.A)(e$,2),eZ=eJ[0],e0=eJ[1],e1=(0,m.useState)(0),e2=(0,l.A)(e1,2),e4=e2[0],e5=e2[1],e6=(0,m.useState)(!1),e3=(0,l.A)(e6,2),e7=e3[0],e9=e3[1],e8=function(){e9(!0)},te=function(){e9(!1)};function tt(e){e0(function(t){var n,o=(n="function"==typeof e?e(t):e,Number.isNaN(ty.current)||(n=Math.min(n,ty.current)),n=Math.max(n,0));return eG.current.scrollTop=o,o})}var tn=(0,m.useRef)({start:0,end:eq.length}),to=(0,m.useRef)(),tr=(n=m.useState(eq),w=(u=(0,l.A)(n,2))[0],E=u[1],N=m.useState(null),B=(T=(0,l.A)(N,2))[0],P=T[1],m.useEffect(function(){var e=function(e,t,n){var o,r,i=e.length,a=t.length;if(0===i&&0===a)return null;i<a?(o=e,r=t):(o=t,r=e);var l={__EMPTY_ITEM__:!0};function c(e){return void 0!==e?n(e):l}for(var u=null,s=1!==Math.abs(i-a),d=0;d<r.length;d+=1){var f=c(o[d]);if(f!==c(r[d])){u=d,s=s||f!==c(r[d+1]);break}}return null===u?null:{index:u,multiple:s}}(w||[],eq||[],eB);(null==e?void 0:e.index)!==void 0&&P(eq[e.index]),E(eq)},[eq]),[B]),ti=(0,l.A)(tr,1)[0];to.current=ti;var ta=m.useMemo(function(){if(!eV)return{scrollHeight:void 0,start:0,end:eq.length-1,offset:void 0};if(!eK)return{scrollHeight:(null===(e=eU.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:eq.length-1,offset:void 0};for(var e,t,n,o,r=0,i=eq.length,a=0;a<i;a+=1){var l=eB(eq[a]),c=eW.get(l),u=r+(void 0===c?eg:c);u>=eZ&&void 0===t&&(t=a,n=r),u>eZ+ev&&void 0===o&&(o=a),r=u}return void 0===t&&(t=0,n=0,o=Math.ceil(ev/eg)),void 0===o&&(o=eq.length-1),{scrollHeight:r,start:t,end:o=Math.min(o+1,eq.length-1),offset:n}},[eK,eV,eZ,eq,eF,ev]),tl=ta.scrollHeight,tc=ta.start,tu=ta.end,ts=ta.offset;tn.current.start=tc,tn.current.end=tu,m.useLayoutEffect(function(){var e=eW.getRecord();if(1===e.size){var t=Array.from(e)[0],n=eq[tc];if(n&&eB(n)===t){var o=eW.get(t)-eg;tt(function(e){return e+o})}}eW.resetRecord()},[tl]);var td=m.useState({width:0,height:ev}),tf=(0,l.A)(td,2),tp=tf[0],tm=tf[1],tv=(0,m.useRef)(),tg=(0,m.useRef)(),th=m.useMemo(function(){return R(tp.width,eC)},[tp.width,eC]),tb=m.useMemo(function(){return R(tp.height,tl)},[tp.height,tl]),tA=tl-ev,ty=(0,m.useRef)(tA);ty.current=tA;var tw=eZ<=0,tE=eZ>=tA,tS=e4<=0,tC=e4>=eC,tx=y(tw,tE,tS,tC),tI=function(){return{x:eY?-e4:e4,y:eZ}},tO=(0,m.useRef)(tI()),tM=(0,f._q)(function(e){if(eO){var t=(0,i.A)((0,i.A)({},tI()),e);(tO.current.x!==t.x||tO.current.y!==t.y)&&(eO(t),tO.current=t)}});function tR(e,t){t?((0,v.flushSync)(function(){e5(e)}),tM()):tt(e)}var tz=function(e){var t=e,n=eC?eC-tp.width:0;return Math.min(t=Math.max(t,0),n)},tD=(0,f._q)(function(e,t){t?((0,v.flushSync)(function(){e5(function(t){return tz(t+(eY?-e:e))})}),tM()):tt(function(t){return t+e})}),tH=(k=!!eC,L=(0,m.useRef)(0),j=(0,m.useRef)(null),W=(0,m.useRef)(null),F=(0,m.useRef)(!1),V=y(tw,tE,tS,tC),_=(0,m.useRef)(null),K=(0,m.useRef)(null),[function(e){if(eV){b.A.cancel(K.current),K.current=(0,b.A)(function(){_.current=null},2);var t,n=e.deltaX,o=e.deltaY,r=e.shiftKey,i=n,a=o;("sx"===_.current||!_.current&&r&&o&&!n)&&(i=o,a=0,_.current="sx");var l=Math.abs(i),c=Math.abs(a);(null===_.current&&(_.current=k&&l>c?"x":"y"),"y"===_.current)?(t=a,b.A.cancel(j.current),V(!1,t)||e._virtualHandled||(e._virtualHandled=!0,L.current+=t,W.current=t,A||e.preventDefault(),j.current=(0,b.A)(function(){var e=F.current?10:1;tD(L.current*e,!1),L.current=0}))):(tD(i,!0),A||e.preventDefault())}},function(e){eV&&(F.current=e.detail===W.current)}]),tN=(0,l.A)(tH,2),tT=tN[0],tB=tN[1];Y=function(e,t,n,o){return!tx(e,t,n)&&(!o||!o._virtualHandled)&&(o&&(o._virtualHandled=!0),tT({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0)},q=(0,m.useRef)(!1),G=(0,m.useRef)(0),U=(0,m.useRef)(0),Q=(0,m.useRef)(null),$=(0,m.useRef)(null),J=function(e){if(q.current){var t=Math.ceil(e.touches[0].pageX),n=Math.ceil(e.touches[0].pageY),o=G.current-t,r=U.current-n,i=Math.abs(o)>Math.abs(r);i?G.current=t:U.current=n;var a=Y(i,i?o:r,!1,e);a&&e.preventDefault(),clearInterval($.current),a&&($.current=setInterval(function(){i?o*=x:r*=x;var e=Math.floor(i?o:r);(!Y(i,e,!0)||.1>=Math.abs(e))&&clearInterval($.current)},16))}},Z=function(){q.current=!1,X()},ee=function(e){X(),1!==e.touches.length||q.current||(q.current=!0,G.current=Math.ceil(e.touches[0].pageX),U.current=Math.ceil(e.touches[0].pageY),Q.current=e.target,Q.current.addEventListener("touchmove",J,{passive:!1}),Q.current.addEventListener("touchend",Z,{passive:!0}))},X=function(){Q.current&&(Q.current.removeEventListener("touchmove",J),Q.current.removeEventListener("touchend",Z))},(0,p.A)(function(){return eV&&eG.current.addEventListener("touchstart",ee,{passive:!0}),function(){var e;null===(e=eG.current)||void 0===e||e.removeEventListener("touchstart",ee),X(),clearInterval($.current)}},[eV]),et=function(e){tt(function(t){return t+e})},m.useEffect(function(){var e=eG.current;if(eK&&e){var t,n,o=!1,r=function(){b.A.cancel(t)},i=function e(){r(),t=(0,b.A)(function(){et(n),e()})},a=function(e){!e.target.draggable&&0===e.button&&(e._virtualHandled||(e._virtualHandled=!0,o=!0))},l=function(){o=!1,r()},c=function(t){if(o){var a=O(t,!1),l=e.getBoundingClientRect(),c=l.top,u=l.bottom;a<=c?(n=-I(c-a),i()):a>=u?(n=I(a-u),i()):r()}};return e.addEventListener("mousedown",a),e.ownerDocument.addEventListener("mouseup",l),e.ownerDocument.addEventListener("mousemove",c),function(){e.removeEventListener("mousedown",a),e.ownerDocument.removeEventListener("mouseup",l),e.ownerDocument.removeEventListener("mousemove",c),r()}}},[eK]),(0,p.A)(function(){function e(e){var t=tw&&e.detail<0,n=tE&&e.detail>0;!eV||t||n||e.preventDefault()}var t=eG.current;return t.addEventListener("wheel",tT,{passive:!1}),t.addEventListener("DOMMouseScroll",tB,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",tT),t.removeEventListener("DOMMouseScroll",tB),t.removeEventListener("MozMousePixelScroll",e)}},[eV,tw,tE]),(0,p.A)(function(){if(eC){var e=tz(e4);e5(e),tM({x:e})}},[tp.width,eC]);var tP=function(){var e,t;null===(e=tv.current)||void 0===e||e.delayHidden(),null===(t=tg.current)||void 0===t||t.delayHidden()},tk=(en=function(){return ej(!0)},eo=m.useRef(),er=m.useState(null),ea=(ei=(0,l.A)(er,2))[0],el=ei[1],(0,p.A)(function(){if(ea&&ea.times<10){if(!eG.current){el(function(e){return(0,i.A)({},e)});return}en();var e=ea.targetAlign,t=ea.originAlign,n=ea.index,o=ea.offset,r=eG.current.clientHeight,a=!1,l=e,c=null;if(r){for(var u=e||t,s=0,d=0,f=0,p=Math.min(eq.length-1,n),m=0;m<=p;m+=1){var v=eB(eq[m]);d=s;var g=eW.get(v);s=f=d+(void 0===g?eg:g)}for(var h="top"===u?o:r-o,b=p;b>=0;b-=1){var A=eB(eq[b]),y=eW.get(A);if(void 0===y){a=!0;break}if((h-=y)<=0)break}switch(u){case"top":c=d-o;break;case"bottom":c=f-r+o;break;default:var w=eG.current.scrollTop;d<w?l="top":f>w+r&&(l="bottom")}null!==c&&tt(c),c!==ea.lastTop&&(a=!0)}a&&el((0,i.A)((0,i.A)({},ea),{},{times:ea.times+1,targetAlign:l,lastTop:c}))}},[ea,eG.current]),function(e){if(null==e){tP();return}if(b.A.cancel(eo.current),"number"==typeof e)tt(e);else if(e&&"object"===(0,r.A)(e)){var t,n=e.align;t="index"in e?e.index:eq.findIndex(function(t){return eB(t)===e.key});var o=e.offset;el({times:0,index:t,offset:void 0===o?0:o,originAlign:n})}});m.useImperativeHandle(t,function(){return{nativeElement:eQ.current,getScrollInfo:tI,scrollTo:function(e){e&&"object"===(0,r.A)(e)&&("left"in e||"top"in e)?(void 0!==e.left&&e5(tz(e.left)),tk(e.top)):tk(e)}}}),(0,p.A)(function(){eM&&eM(eq.slice(tc,tu+1),eq)},[tc,tu,eq]);var tL=(ec=m.useMemo(function(){return[new Map,[]]},[eq,eW.id,eg]),es=(eu=(0,l.A)(ec,2))[0],ed=eu[1],function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=es.get(e),o=es.get(t);if(void 0===n||void 0===o)for(var r=eq.length,i=ed.length;i<r;i+=1){var a,l=eB(eq[i]);es.set(l,i);var c=null!==(a=eW.get(l))&&void 0!==a?a:eg;if(ed[i]=(ed[i-1]||0)+c,l===e&&(n=i),l===t&&(o=i),void 0!==n&&void 0!==o)break}return{top:ed[n-1]||0,bottom:ed[o]}}),tj=null==ez?void 0:ez({start:tc,end:tu,virtual:eK,offsetX:e4,offsetY:ts,rtl:eY,getSize:tL}),tW=eq.slice(tc,tu+1).map(function(e,t){var n=ey(e,tc+t,{style:{width:eC},offsetX:e4}),o=eB(e);return m.createElement(h,{key:o,setRef:function(t){return eL(e,t)}},n)}),tF=null;ev&&(tF=(0,i.A)((0,a.A)({},void 0===eh||eh?"height":"maxHeight",ev),H),eV&&(tF.overflowY="hidden",eC&&(tF.overflowX="hidden"),e7&&(tF.pointerEvents="none")));var tV={};return eY&&(tV.dir="rtl"),m.createElement("div",(0,o.A)({ref:eQ,style:(0,i.A)((0,i.A)({},eb),{},{position:"relative"}),className:eX},tV,eT),m.createElement(d.A,{onResize:function(e){tm({width:e.offsetWidth,height:e.offsetHeight})}},m.createElement(void 0===ex?"div":ex,{className:"".concat(ep,"-holder"),style:tF,ref:eG,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==eZ&&tt(t),null==eI||eI(e),tM()},onMouseEnter:tP},m.createElement(g,{prefixCls:ep,height:tl,offsetX:e4,offsetY:ts,scrollWidth:eC,onInnerResize:ej,ref:eU,innerProps:eR,rtl:eY,extra:tj},tW))),eK&&tl>ev&&m.createElement(M,{ref:tv,prefixCls:ep,scrollOffset:eZ,scrollRange:tl,rtl:eY,onScroll:tR,onStartMove:e8,onStopMove:te,spinSize:tb,containerSize:tp.height,style:null==eD?void 0:eD.verticalScrollBar,thumbStyle:null==eD?void 0:eD.verticalScrollBarThumb,showScrollBar:eN}),eK&&eC>tp.width&&m.createElement(M,{ref:tg,prefixCls:ep,scrollOffset:e4,scrollRange:eC,rtl:eY,onScroll:tR,onStartMove:e8,onStopMove:te,spinSize:th,containerSize:tp.width,horizontal:!0,style:null==eD?void 0:eD.horizontalScrollBar,thumbStyle:null==eD?void 0:eD.horizontalScrollBarThumb,showScrollBar:eN}))});N.displayName="List";let T=N}}]);