"use strict";
// DISABLED: All caching removed to ensure fresh data
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCacheStats = exports.clearCache = exports.deleteCached = exports.setCached = exports.getCached = void 0;
// Cache statistics for monitoring
const cacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0
};
/**
 * DISABLED: Get an item from cache - always returns undefined for fresh data
 * @param key Cache key
 * @returns Always undefined to force fresh database queries
 */
const getCached = (key) => {
    // DISABLED: Always return undefined to force fresh data
    console.log(`🚫 Cache DISABLED - getCached(${key}) returning undefined to force fresh data`);
    cacheStats.misses++;
    return undefined;
};
exports.getCached = getCached;
/**
 * DISABLED: Set an item in cache - does nothing to ensure fresh data
 * @param key Cache key
 * @param data Data to cache (ignored)
 * @param ttl Optional custom TTL in milliseconds (ignored)
 */
const setCached = (key, data, ttl) => {
    // DISABLED: Do nothing to prevent caching
    console.log(`🚫 Cache DISABLED - setCached(${key}) ignored to ensure fresh data`);
    cacheStats.sets++;
};
exports.setCached = setCached;
/**
 * DISABLED: Delete an item from cache - does nothing
 * @param key Cache key
 */
const deleteCached = (key) => {
    // DISABLED: Do nothing since there's no cache
    console.log(`🚫 Cache DISABLED - deleteCached(${key}) ignored`);
    cacheStats.deletes++;
};
exports.deleteCached = deleteCached;
/**
 * DISABLED: Clear all cache entries - does nothing
 */
const clearCache = () => {
    // DISABLED: Do nothing since there's no cache
    console.log(`🚫 Cache DISABLED - clearCache() ignored`);
};
exports.clearCache = clearCache;
/**
 * Get cache statistics - shows disabled cache stats
 */
const getCacheStats = () => {
    return {
        ...cacheStats,
        size: 0, // No cache
        hitRate: 0, // No cache hits since cache is disabled
        status: 'DISABLED - No caching for fresh data'
    };
};
exports.getCacheStats = getCacheStats;
// DISABLED: No cleanup needed since there's no cache
exports.default = {
    get: exports.getCached,
    set: exports.setCached,
    delete: exports.deleteCached,
    clear: exports.clearCache,
    stats: exports.getCacheStats
};
