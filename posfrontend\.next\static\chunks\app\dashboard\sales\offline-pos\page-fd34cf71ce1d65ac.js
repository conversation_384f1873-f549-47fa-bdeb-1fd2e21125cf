(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[785],{60811:(e,t,a)=>{Promise.resolve().then(a.bind(a,72879))},72879:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var n=a(95155),s=a(12115),r=a(5565),i=a(36060),o=a(88908),l=a(52080),d=a(93968),c=a(21633),u=a(75912),y=a(7045),h=a(33429);let f=()=>{let{user:e}=(0,i.A)(),[t,a]=(0,s.useState)(!1),[n,r]=(0,s.useState)([]),[f,p]=(0,s.useState)(null),[m,g]=(0,s.useState)({isOffline:!1,pendingSales:0,failedSales:0,cachedProducts:0}),{data:b,isLoading:x}=(0,d.r3)({page:1,limit:1e3},{skip:t,refetchOnMountOrArgChange:!1,refetchOnReconnect:!1}),[w]=(0,c.E$)(),{data:v}=(0,h.Rv)((null==e?void 0:e.id)||0,{skip:!(null==e?void 0:e.id)}),S=(0,s.useCallback)(async()=>{try{let e=await o.V.getCachedProducts();r(e)}catch(e){console.error("Failed to load cached products:",e)}},[]),j=(0,s.useCallback)(async()=>{try{let e=await o.V.getStorageStats(),t=await l.zy.getSyncStatus();g({isOffline:!navigator.onLine,pendingSales:e.pendingSales,failedSales:e.failedSales,cachedProducts:e.cachedProducts,lastSync:t.lastSyncAttempt}),console.log("✅ Offline status updated:",{isOffline:!navigator.onLine,cachedProducts:e.cachedProducts})}catch(e){console.error("Failed to update offline status:",e)}},[]);(0,s.useEffect)(()=>{(async()=>{try{await o.V.init(),await j(),await S(),console.log("✅ Offline POS initialized")}catch(e){console.error("Failed to initialize offline POS:",e)}})();let e=setInterval(()=>{navigator.onLine&&(j(),S())},3e5);return()=>clearInterval(e)},[S,j]),(0,s.useEffect)(()=>{let e=()=>{let e=!navigator.onLine;a(e),j(),console.log("Network status changed: ".concat(e?"offline":"online"))};return e(),window.addEventListener("online",e),window.addEventListener("offline",e),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",e)}},[j]),(0,s.useEffect)(()=>{(async()=>{var e;if((null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.products)&&!t)try{let e=b.data.products.map(e=>({id:e.id,name:e.name,price:e.price,stockQuantity:e.stockQuantity,sku:e.sku,barcode:e.barcode,imageUrl:e.imageUrl||void 0,categoryId:e.categoryId,lastUpdated:new Date}));await o.V.cacheProducts(e),await S()}catch(e){console.error("Failed to cache products:",e)}})()},[b,t,S]),(0,s.useEffect)(()=>{(async()=>{if((null==v?void 0:v.data)&&!t)try{await o.V.cacheStore(v.data),p(v.data)}catch(e){console.error("Failed to cache store details:",e)}})()},[v,t]);let N=(0,s.useCallback)(()=>{var e;return!t&&(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.products)?b.data.products.map(e=>({id:e.id,name:e.name,price:e.price,stockQuantity:e.stockQuantity,sku:e.sku,barcode:e.barcode,imageUrl:e.imageUrl||void 0,categoryId:e.categoryId,lastUpdated:new Date})):n},[t,b,n]),k=(0,s.useCallback)(async e=>{if(t)return await o.V.searchProducts(e);let a=N(),n=e.toLowerCase();return a.filter(e=>e.name.toLowerCase().includes(n)||e.sku&&e.sku.toLowerCase().includes(n)||e.barcode&&e.barcode.toLowerCase().includes(n))},[t,N]),D=(0,s.useCallback)(async e=>t?await o.V.getProductByBarcode(e):N().find(t=>t.barcode===e||t.sku===e),[t,N]),P=(0,s.useCallback)(async(a,n,s)=>{if(!e)throw Error("User not authenticated");let r=a.reduce((e,t)=>e+t.price*t.quantity,0),i="";try{let e=(0,y.jY)({id:Date.now(),totalAmount:r,paymentMethod:n,transactionDate:new Date().toISOString(),items:a.map(e=>({productName:e.productName,quantity:e.quantity,price:e.price}))},{name:"POS System",address:"",city:"",country:"Ghana"});i=await (0,y.fS)(e)}catch(e){console.warn("Failed to generate receipt:",e)}if(t)try{let t=await o.V.saveOfflineSale({totalAmount:r,paymentMethod:n,items:a,receiptUrl:i,storeId:s,createdBy:e.id});return await j(),(0,u.r)("success","Sale saved offline - will sync when connection returns"),{success:!0,saleId:t,isOffline:!0}}catch(e){throw console.error("Failed to save offline sale:",e),Error("Failed to save sale offline")}else try{let e={totalAmount:r,paymentMethod:n,items:a.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:i,storeId:s},t=await w(e).unwrap();if(t.success){var l,d,c,h;return(0,u.r)("success","Sale created successfully"),{success:!0,saleId:(null===(h=t.data)||void 0===h?void 0:null===(c=h.sales)||void 0===c?void 0:null===(d=c[0])||void 0===d?void 0:null===(l=d.saleId)||void 0===l?void 0:l.toString())||"unknown"}}throw Error(t.message||"Sale creation failed")}catch(t){console.error("Online sale failed:",t);try{let t=await o.V.saveOfflineSale({totalAmount:r,paymentMethod:n,items:a,receiptUrl:i,storeId:s,createdBy:e.id});return await j(),(0,u.r)("warning","Online sale failed - saved offline instead"),{success:!0,saleId:t,isOffline:!0}}catch(e){throw console.error("Offline fallback failed:",e),Error("Both online and offline sale creation failed")}}},[e,t,w,j]),I=(0,s.useCallback)(async()=>await o.V.getOfflineSales(),[]),E=(0,s.useCallback)(async()=>{await l.zy.forceSyncNow(),await j()},[j]),A=(0,s.useCallback)(()=>!t&&(null==v?void 0:v.data)?v.data:f,[t,v,f]);return{isOffline:t,offlineStatus:m,isLoading:x&&0===n.length,products:N(),searchProducts:k,findProductByBarcode:D,createSale:P,getOfflineSales:I,forceSyncNow:E,updateOfflineStatus:j,getStore:A}};var p={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var g=(e,t)=>{let a=(0,s.forwardRef)((a,n)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,children:d,...c}=a;return(0,s.createElement)("svg",{ref:n,...p,width:i,height:i,stroke:r,strokeWidth:l?24*Number(o)/Number(i):o,className:"lucide lucide-".concat(m(e)),...c},[...t.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...(Array.isArray(d)?d:[d])||[]])});return a.displayName="".concat(e),a};let b=g("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),x=g("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),w=g("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),v=g("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),S=()=>{let{isOffline:e,offlineStatus:t,isLoading:a,products:i,searchProducts:o,findProductByBarcode:l,createSale:d,forceSyncNow:c,getStore:y}=f(),[h,p]=(0,s.useState)([]),[m,g]=(0,s.useState)(""),[S,j]=(0,s.useState)(""),[N,k]=(0,s.useState)(!1),[D,P]=(0,s.useState)("cash"),I=(0,s.useMemo)(()=>m?i.filter(e=>e.name.toLowerCase().includes(m.toLowerCase())||e.sku&&e.sku.toLowerCase().includes(m.toLowerCase())||e.barcode&&e.barcode.toLowerCase().includes(m.toLowerCase())).slice(0,50):i.slice(0,50),[i,m]),E=e=>{if(e.stockQuantity<=0){(0,u.r)("warning","Product out of stock");return}p(t=>t.find(t=>t.productId===e.id)?t.map(t=>t.productId===e.id?{...t,quantity:t.quantity+1}:t):[...t,{productId:e.id,productName:e.name,quantity:1,price:Number(e.price)}])},A=(e,t)=>{t<=0?p(t=>t.filter(t=>t.productId!==e)):p(a=>a.map(a=>a.productId===e?{...a,quantity:t}:a))},C=e=>{p(t=>t.filter(t=>t.productId!==e))},L=async e=>{if(e.preventDefault(),S.trim())try{let e=await l(S.trim());e?(E(e),j(""),(0,u.r)("success","Added ".concat(e.name," to cart"))):(0,u.r)("error","Product not found")}catch(e){(0,u.r)("error","Failed to find product")}},O=(0,s.useMemo)(()=>h.reduce((e,t)=>e+t.price*t.quantity,0),[h]),T=O+0,_=async()=>{if(0===h.length){(0,u.r)("error","Cart is empty");return}k(!0);try{let e=await d(h,D);e.success&&(p([]),P("cash"),e.isOffline?(0,u.r)("success","Sale saved offline - will sync when online"):(0,u.r)("success","Sale completed successfully"))}catch(e){console.error("Sale failed:",e),(0,u.r)("error",e instanceof Error?e.message:"Sale failed")}finally{k(!1)}},B=y?y():null;return a?(0,n.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading POS system..."})]})}):(0,n.jsxs)("div",{className:"h-screen flex bg-gray-50",children:[e&&B&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 rounded shadow text-blue-900",children:[(0,n.jsx)("div",{className:"font-bold text-lg",children:B.name||"Store Name"}),B.address&&(0,n.jsx)("div",{className:"text-sm",children:B.address}),B.phone&&(0,n.jsxs)("div",{className:"text-sm",children:["Phone: ",B.phone]})]}),(0,n.jsxs)("div",{className:"w-1/3 bg-white border-r border-gray-200 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"POS System"}),(0,n.jsx)("div",{className:"flex items-center gap-2",children:e?(0,n.jsxs)("div",{className:"flex items-center gap-1 text-orange-600",children:[(0,n.jsx)(b,{size:16}),(0,n.jsx)("span",{className:"text-sm font-medium",children:"Offline"})]}):(0,n.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,n.jsx)(x,{size:16}),(0,n.jsx)("span",{className:"text-sm font-medium",children:"Online"})]})})]}),(t.pendingSales>0||t.failedSales>0)&&(0,n.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-2",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(w,{size:16,className:"text-yellow-600"}),(0,n.jsxs)("span",{className:"text-sm text-yellow-800",children:[t.pendingSales," pending, ",t.failedSales," failed"]})]}),(0,n.jsxs)("button",{onClick:c,className:"text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700",disabled:e,children:[(0,n.jsx)(v,{size:12,className:"inline mr-1"}),"Sync"]})]})}),(0,n.jsx)("form",{onSubmit:L,className:"mb-2",children:(0,n.jsx)("input",{type:"text",value:S,onChange:e=>j(e.target.value),placeholder:"Scan or enter barcode...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})})]}),(0,n.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-700 mb-3",children:"Cart Items"}),0===h.length?(0,n.jsxs)("div",{className:"text-center text-gray-400 py-8",children:[(0,n.jsx)("p",{children:"Cart is empty"}),(0,n.jsx)("p",{className:"text-sm",children:"Add products to start a sale"})]}):(0,n.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-800 text-sm",children:e.productName}),(0,n.jsx)("button",{onClick:()=>C(e.productId),className:"text-red-500 hover:text-red-700 text-xs",children:"✕"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("button",{onClick:()=>A(e.productId,e.quantity-1),className:"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300",children:"-"}),(0,n.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,n.jsx)("button",{onClick:()=>A(e.productId,e.quantity+1),className:"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300",children:"+"})]}),(0,n.jsxs)("span",{className:"font-semibold text-blue-600",children:["₵",(e.price*e.quantity).toFixed(2)]})]})]},e.productId))})]}),(0,n.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,n.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Subtotal:"}),(0,n.jsxs)("span",{children:["₵",O.toFixed(2)]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Tax:"}),(0,n.jsxs)("span",{children:["₵","0.00"]})]}),(0,n.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[(0,n.jsx)("span",{children:"Total:"}),(0,n.jsxs)("span",{className:"text-blue-600",children:["₵",T.toFixed(2)]})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,n.jsxs)("select",{value:D,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"cash",children:"Cash"}),(0,n.jsx)("option",{value:"card",children:"Card"}),(0,n.jsx)("option",{value:"mobile_money",children:"Mobile Money"})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("button",{onClick:_,disabled:0===h.length||N,className:"w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:N?"Processing...":"PAY ₵".concat(T.toFixed(2))}),(0,n.jsx)("button",{onClick:()=>p([]),disabled:0===h.length,className:"w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 disabled:bg-gray-300",children:"Clear Cart"})]})]})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-gray-200 bg-white",children:[(0,n.jsx)("input",{type:"text",value:m,onChange:e=>g(e.target.value),placeholder:"Search products...",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e?"".concat(t.cachedProducts," cached products"):"".concat(i.length," products available")})]}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,n.jsx)("div",{className:"grid grid-cols-4 xl:grid-cols-6 gap-3",children:I.map(e=>(0,n.jsxs)("button",{onClick:()=>E(e),disabled:e.stockQuantity<=0,className:"aspect-square rounded-lg p-3 flex flex-col items-center justify-center text-center transition-all ".concat(e.stockQuantity<=0?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white border-2 border-gray-200 hover:border-blue-400 hover:shadow-md active:scale-95"),children:[e.imageUrl?(0,n.jsx)(r.default,{src:e.imageUrl,alt:e.name,width:48,height:48,className:"object-cover rounded mb-2"}):(0,n.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded mb-2 flex items-center justify-center text-2xl",children:"\uD83D\uDECD️"}),(0,n.jsx)("span",{className:"font-medium text-xs text-gray-800 truncate w-full mb-1",children:e.name}),(0,n.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:["₵",Number(e.price).toFixed(2)]}),e.stockQuantity<=5&&e.stockQuantity>0&&(0,n.jsx)("span",{className:"text-xs text-orange-600",children:"Low stock"})]},e.id))})})]})]})},j=()=>(0,n.jsx)("div",{className:"h-screen",children:(0,n.jsx)(S,{})})},36060:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var n=a(83391),s=a(70854),r=a(63065),i=a(7875);let o=()=>{let e=(0,n.wA)(),{user:t,accessToken:a}=(0,n.d4)(e=>e.auth),o=(0,s._)(),{refetch:l}=(0,r.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!a,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let n=await l();console.log("useAuth - Refetch result:",n);let s=n.data;if((null==s?void 0:s.success)&&(null==s?void 0:s.data)){console.log("useAuth - API response data:",s.data);let n=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let r=s.data.phone||t.phone||"",o=s.data.createdAt||t.createdAt||"",l=s.data.lastPaymentDate||t.lastPaymentDate||void 0,d=s.data.nextPaymentDue||t.nextPaymentDue||null,c=s.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:s.data.phone,userPhone:t.phone,finalPhone:r,apiCreatedAt:s.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:s.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:l,apiNextPaymentDue:s.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:s.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let u={...s.data,phone:r,createdAt:o,lastPaymentDate:l,nextPaymentDue:d,createdBy:c,paymentStatus:n};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,i.gV)({user:u,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==s?void 0:s.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:c}}},70854:(e,t,a)=>{"use strict";a.d(t,{_:()=>o});var n=a(12115),s=a(83391),r=a(21455),i=a.n(r);let o=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,a]=(0,n.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,n.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,n=!1,s=!0,r="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){n=!0,s=!1,r="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let r=i()(e.nextPaymentDue),o=i()();if(t=r.diff(o,"day"),a){let a=i()().diff(i()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:n,needsPayment:s})}}}else"pending"===e.paymentStatus?(n=!1,s=!0,r="pending"):"overdue"===e.paymentStatus?(n=!1,s=!0,r="overdue"):(n=!1,s=!0,r="inactive");a({isActive:n,daysRemaining:t,status:r,needsPayment:s})},[e]),t}},7045:(e,t,a)=>{"use strict";a.d(t,{fS:()=>r,iG:()=>o,jY:()=>i});let n="dutmiedgk",s="pos_receipts",r=async e=>{try{var t;let r=document.createElement("div");r.innerHTML=e,r.style.width="350px",r.style.padding="20px",r.style.backgroundColor="white",r.style.color="#555555",r.style.fontFamily="Arial, sans-serif",r.style.position="absolute",r.style.left="-9999px",r.style.borderLeft="1px solid #000000",r.style.borderRight="1px solid #000000",r.style.borderTop="1px solid #000000",r.style.borderBottom="1px solid #000000",r.style.boxSizing="border-box",r.style.fontSize="14px",r.style.lineHeight="1.5",document.body.appendChild(r);let i=(await a.e(4316).then(a.t.bind(a,40078,23))).default,o=await i(r,{scale:3,backgroundColor:"white",logging:!1,width:350,height:r.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(r);let l=await new Promise(e=>{o.toBlob(t=>{e(t)},"image/png",.95)}),d=new FormData;d.append("file",l),d.append("upload_preset",s);let c=await fetch("https://api.cloudinary.com/v1_1/".concat(n,"/image/upload"),{method:"POST",body:d}),u=await c.json();if(c.ok)return u.secure_url;throw console.error("Cloudinary upload failed:",u),Error((null===(t=u.error)||void 0===t?void 0:t.message)||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},i=(e,t)=>{let a=new Date(e.transactionDate),n=a.toLocaleDateString(),s=a.toLocaleTimeString(),r=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return'\n  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">\n\n  \x3c!-- Header and Title --\x3e\n  <div style="text-align: center; margin-bottom: 10px;">\n    <div style="font-size: 18px; font-weight: bold;">'.concat(t.name||"POS System",'</div>\n    <div style="font-size: 16px; font-weight: bold;">#INV-').concat(e.id,"-").concat(new Date().getFullYear()).concat((new Date().getMonth()+1).toString().padStart(2,"0")).concat(new Date().getDate().toString().padStart(2,"0"),'</div>\n    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">\n      ').concat(t.address?"<div>".concat(t.address,"</div>"):"","\n      ").concat(t.city?"<div>".concat(t.city,"</div>"):"","\n      ").concat(t.country?"<div>".concat(t.country,"</div>"):"",'\n    </div>\n  </div>\n\n  \x3c!-- Items Table --\x3e\n  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">\n    <thead>\n      <tr>\n        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>\n        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>\n      </tr>\n    </thead>\n    <tbody>\n      ').concat(e.items.map((e,t)=>{let a="string"==typeof e.price?parseFloat(e.price):e.price,n=a*e.quantity;return'\n          <tr>\n            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">'.concat(t+1,". ").concat(e.productName,'</td>\n            <td style="text-align: right;">').concat(e.quantity,'</td>\n            <td style="text-align: right;">').concat(a.toFixed(2),'</td>\n            <td style="text-align: right;">').concat(n.toFixed(2),"</td>\n          </tr>\n        ")}).join(""),'\n    </tbody>\n  </table>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Totals --\x3e\n  <div style="display: flex; justify-content: space-between; font-weight: bold;">\n    <div>TOTAL</div>\n    <div>GHS ').concat("string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2),'</div>\n  </div>\n  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">\n    <div>TAX</div>\n    <div>0.00</div>\n  </div>\n\n  \x3c!-- Dotted Divider --\x3e\n  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>\n\n  \x3c!-- Payment Info --\x3e\n  <div style="font-size: 11px; margin-bottom: 6px;">\n    <div>Payment: ').concat(r.toUpperCase(),"</div>\n    ").concat(r.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":"",'\n  </div>\n\n  \x3c!-- Date/Time --\x3e\n  <div style="display: flex; justify-content: space-between; font-size: 11px;">\n    <div><strong>Time:</strong> ').concat(s,"</div>\n    <div><strong>Date:</strong> ").concat(n,'</div>\n  </div>\n\n  \x3c!-- Barcode --\x3e\n  <div style="text-align: center; margin: 12px 0;">\n    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>\n    <div style="font-size: 11px; margin-top: 4px;">').concat(e.id,'</div>\n  </div>\n\n  \x3c!-- Footer --\x3e\n  <div style="text-align: center; font-size: 12px; margin-top: 8px;">\n    THANK YOU!\n  </div>\n</div>\n\n')},o=async e=>{try{var t;let a=new FormData;a.append("file",e),a.append("upload_preset",s),a.append("folder","products");let r=await fetch("https://api.cloudinary.com/v1_1/".concat(n,"/image/upload"),{method:"POST",body:a}),i=await r.json();if(r.ok)return i.secure_url;throw console.error("Cloudinary upload failed:",i),Error((null===(t=i.error)||void 0===t?void 0:t.message)||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},88908:(e,t,a)=>{"use strict";a.d(t,{V:()=>r});var n=a(10733);class s{async init(){try{this.db=await (0,n.P2)(this.DB_NAME,this.DB_VERSION,{upgrade(e){let t=e.createObjectStore("offlineSales",{keyPath:"id"});t.createIndex("by-timestamp","timestamp"),t.createIndex("by-status","status");let a=e.createObjectStore("products",{keyPath:"id"});a.createIndex("by-name","name"),a.createIndex("by-barcode","barcode");let n=e.createObjectStore("syncQueue",{keyPath:"id"});n.createIndex("by-priority","priority"),n.createIndex("by-timestamp","timestamp"),e.objectStoreNames.contains("store")||e.createObjectStore("store")}}),console.log("✅ Offline storage initialized")}catch(e){throw console.error("❌ Failed to initialize offline storage:",e),e}}async saveOfflineSale(e){if(!this.db)throw Error("Database not initialized");let t="offline_sale_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),a={...e,id:t,timestamp:new Date,status:"pending",syncAttempts:0};return await this.db.add("offlineSales",a),console.log("\uD83D\uDCBE Offline sale saved:",t),t}async getOfflineSales(e){if(!this.db)throw Error("Database not initialized");return e?await this.db.getAllFromIndex("offlineSales","by-status",e):await this.db.getAll("offlineSales")}async updateSaleStatus(e,t,a){if(!this.db)throw Error("Database not initialized");let n=await this.db.get("offlineSales",e);if(!n)throw Error("Sale not found");n.status=t,n.syncAttempts+=1,n.lastSyncAttempt=new Date,a&&(n.errorMessage=a),await this.db.put("offlineSales",n)}async deleteOfflineSale(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("offlineSales",e)}async cacheProducts(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction("products","readwrite"),a=t.objectStore("products");for(let t of(await a.clear(),e))await a.add({...t,lastUpdated:new Date});await t.done,console.log("\uD83D\uDCBE Cached ".concat(e.length," products for offline use"))}async getCachedProducts(){if(!this.db)throw Error("Database not initialized");return await this.db.getAll("products")}async searchProducts(e){if(!this.db)throw Error("Database not initialized");let t=await this.db.getAll("products"),a=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(a)||e.sku&&e.sku.toLowerCase().includes(a)||e.barcode&&e.barcode.toLowerCase().includes(a))}async getProductByBarcode(e){if(!this.db)throw Error("Database not initialized");return await this.db.getFromIndex("products","by-barcode",e)}async addToSyncQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"sync_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:new Date,retryCount:0};await this.db.add("syncQueue",t)}async getSyncQueue(){if(!this.db)throw Error("Database not initialized");return await this.db.getAllFromIndex("syncQueue","by-priority")}async removeSyncQueueItem(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("syncQueue",e)}async cacheStore(e){if(!this.db)throw Error("Database not initialized");await this.db.put("store",e,"current-store"),console.log("\uD83D\uDCBE Store details cached for offline use")}async getCachedStore(){if(!this.db)throw Error("Database not initialized");return await this.db.get("store","current-store")}async getStorageStats(){if(!this.db)throw Error("Database not initialized");let[e,t,a,n,s]=await Promise.all([this.db.getAllFromIndex("offlineSales","by-status","pending"),this.db.getAllFromIndex("offlineSales","by-status","synced"),this.db.getAllFromIndex("offlineSales","by-status","failed"),this.db.getAll("products"),this.db.getAll("syncQueue")]);return{pendingSales:e.length,syncedSales:t.length,failedSales:a.length,cachedProducts:n.length,queueItems:s.length}}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=this.db.transaction(["offlineSales","products","syncQueue"],"readwrite");await Promise.all([e.objectStore("offlineSales").clear(),e.objectStore("products").clear(),e.objectStore("syncQueue").clear()]),await e.done,console.log("\uD83D\uDDD1️ All offline data cleared")}constructor(){this.db=null,this.DB_NAME="NEXAPO_POS_OFFLINE",this.DB_VERSION=1}}let r=new s;r.init().catch(console.error)},52080:(e,t,a)=>{"use strict";a.d(t,{zy:()=>l});var n=a(88908),s=a(90821),r=a(75912),i=a(2818);class o{setupNetworkListeners(){window.addEventListener("online",()=>{console.log("\uD83C\uDF10 Network connection restored"),(0,r.r)("success","Connection restored - syncing offline sales..."),this.syncOfflineSales()}),window.addEventListener("offline",()=>{console.log("\uD83D\uDCF1 Network connection lost - switching to offline mode"),(0,r.r)("warning","Working offline - sales will sync when connection returns")})}startPeriodicSync(){this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{navigator.onLine&&!this.syncInProgress&&this.syncOfflineSales()},this.SYNC_INTERVAL)}async syncOfflineSales(){if(this.syncInProgress){console.log("⏳ Sync already in progress, skipping...");return}if(!navigator.onLine){console.log("\uD83D\uDCF1 Offline - skipping sync");return}this.syncInProgress=!0,console.log("\uD83D\uDD04 Starting offline sales sync...");try{let e=await n.V.getOfflineSales("pending"),t=await n.V.getOfflineSales("failed"),a=[...e,...t.filter(e=>e.syncAttempts<this.MAX_RETRY_ATTEMPTS)];if(0===a.length){console.log("✅ No offline sales to sync");return}console.log("\uD83D\uDD04 Syncing ".concat(a.length," offline sales..."));let s=0,i=0;for(let e of a)try{await this.syncSingleSale(e),s++}catch(t){console.error("❌ Failed to sync sale ".concat(e.id,":"),t),i++,await n.V.updateSaleStatus(e.id,"failed",t instanceof Error?t.message:"Unknown error")}s>0&&(0,r.r)("success","✅ Synced ".concat(s," offline sales")),i>0&&(0,r.r)("warning","⚠️ ".concat(i," sales failed to sync - will retry later"))}catch(e){console.error("❌ Sync process failed:",e),(0,r.r)("error","Sync failed - will retry automatically")}finally{this.syncInProgress=!1}}async syncSingleSale(e){let t=s.M.getState().auth,a=null==t?void 0:t.accessToken;if(!a)throw Error("No authentication token available");let r={totalAmount:e.totalAmount,paymentMethod:e.paymentMethod,items:e.items.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:e.receiptUrl||"",storeId:e.storeId},o=await fetch("".concat(i.env.NEXT_PUBLIC_API_URL,"/sales"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify({mode:"createnew",saleData:r})});if(!o.ok)throw Error((await o.json().catch(()=>({}))).message||"HTTP ".concat(o.status,": ").concat(o.statusText));let l=await o.json();if(!l.success)throw Error(l.message||"Sale creation failed");await n.V.updateSaleStatus(e.id,"synced"),console.log("✅ Successfully synced sale ".concat(e.id)),setTimeout(()=>{n.V.deleteOfflineSale(e.id).catch(console.error)},864e5)}async forceSyncNow(){if(!navigator.onLine){(0,r.r)("warning","No internet connection - cannot sync now");return}(0,r.r)("success","Starting manual sync..."),await this.syncOfflineSales()}async getSyncStatus(){let e=await n.V.getStorageStats();return{isOnline:navigator.onLine,syncInProgress:this.syncInProgress,pendingSales:e.pendingSales,failedSales:e.failedSales,lastSyncAttempt:new Date}}destroy(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null)}constructor(){this.syncInProgress=!1,this.syncInterval=null,this.SYNC_INTERVAL=3e4,this.MAX_RETRY_ATTEMPTS=3,this.setupNetworkListeners(),this.startPeriodicSync()}}let l=new o},75912:(e,t,a)=>{"use strict";a.d(t,{r:()=>s});var n=a(55037);let s=(e,t)=>{"success"===e?n.oR.success(t):"error"===e?n.oR.error(t):"warning"===e&&(0,n.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},10733:(e,t,a)=>{"use strict";let n,s;a.d(t,{P2:()=>y});let r=(e,t)=>t.some(t=>e instanceof t),i=new WeakMap,o=new WeakMap,l=new WeakMap,d={get(e,t,a){if(e instanceof IDBTransaction){if("done"===t)return i.get(e);if("store"===t)return a.objectStoreNames[1]?void 0:a.objectStore(a.objectStoreNames[0])}return c(e[t])},set:(e,t,a)=>(e[t]=a,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function c(e){var t;if(e instanceof IDBRequest)return function(e){let t=new Promise((t,a)=>{let n=()=>{e.removeEventListener("success",s),e.removeEventListener("error",r)},s=()=>{t(c(e.result)),n()},r=()=>{a(e.error),n()};e.addEventListener("success",s),e.addEventListener("error",r)});return l.set(t,e),t}(e);if(o.has(e))return o.get(e);let a="function"==typeof(t=e)?(s||(s=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(u(this),e),c(this.request)}:function(...e){return c(t.apply(u(this),e))}:(t instanceof IDBTransaction&&function(e){if(i.has(e))return;let t=new Promise((t,a)=>{let n=()=>{e.removeEventListener("complete",s),e.removeEventListener("error",r),e.removeEventListener("abort",r)},s=()=>{t(),n()},r=()=>{a(e.error||new DOMException("AbortError","AbortError")),n()};e.addEventListener("complete",s),e.addEventListener("error",r),e.addEventListener("abort",r)});i.set(e,t)}(t),r(t,n||(n=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,d):t;return a!==e&&(o.set(e,a),l.set(a,e)),a}let u=e=>l.get(e);function y(e,t,{blocked:a,upgrade:n,blocking:s,terminated:r}={}){let i=indexedDB.open(e,t),o=c(i);return n&&i.addEventListener("upgradeneeded",e=>{n(c(i.result),e.oldVersion,e.newVersion,c(i.transaction),e)}),a&&i.addEventListener("blocked",e=>a(e.oldVersion,e.newVersion,e)),o.then(e=>{r&&e.addEventListener("close",()=>r()),s&&e.addEventListener("versionchange",e=>s(e.oldVersion,e.newVersion,e))}).catch(()=>{}),o}let h=["get","getKey","getAll","getAllKeys","count"],f=["put","add","delete","clear"],p=new Map;function m(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(p.get(t))return p.get(t);let a=t.replace(/FromIndex$/,""),n=t!==a,s=f.includes(a);if(!(a in(n?IDBIndex:IDBObjectStore).prototype)||!(s||h.includes(a)))return;let r=async function(e,...t){let r=this.transaction(e,s?"readwrite":"readonly"),i=r.store;return n&&(i=i.index(t.shift())),(await Promise.all([i[a](...t),s&&r.done]))[0]};return p.set(t,r),r}d=(e=>({...e,get:(t,a,n)=>m(t,a)||e.get(t,a,n),has:(t,a)=>!!m(t,a)||e.has(t,a)}))(d);let g=["continue","continuePrimaryKey","advance"],b={},x=new WeakMap,w=new WeakMap,v={get(e,t){if(!g.includes(t))return e[t];let a=b[t];return a||(a=b[t]=function(...e){x.set(this,w.get(this)[t](...e))}),a}};async function*S(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;let a=new Proxy(t,v);for(w.set(a,t),l.set(a,u(t));t;)yield a,t=await (x.get(a)||t.continue()),x.delete(a)}function j(e,t){return t===Symbol.asyncIterator&&r(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&r(e,[IDBIndex,IDBObjectStore])}d=(e=>({...e,get:(t,a,n)=>j(t,a)?S:e.get(t,a,n),has:(t,a)=>j(t,a)||e.has(t,a)}))(d)}},e=>{var t=t=>e(e.s=t);e.O(0,[1961,4831,5037,5565,821,8441,1517,7358],()=>t(60811)),_N_E=e.O()}]);