"use client";

import React from "react";
import { Descriptions, Button, Spin, Tag } from "antd";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useProductDetail } from "@/hooks/products/useProductDetail";
import { LoadingOutlined, ShoppingOutlined, CalendarOutlined, BarcodeOutlined, DollarOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import "./product-panels.css";

interface ProductDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
  productId: number | null;
  onEdit?: (productId: number) => void;
}

const ProductDetailPanel: React.FC<ProductDetailPanelProps> = ({
  isOpen,
  onClose,
  productId,
  onEdit,
}) => {
  const { product, isLoading } = useProductDetail(productId);

  // Format date for display
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format currency for display
  const formatCurrency = (amount?: string | null) => {
    if (!amount) return "N/A";
    try {
      return new Intl.NumberFormat('en-GH', {
        style: 'currency',
        currency: 'GHS',
      }).format(parseFloat(amount));
    } catch (error) {
      return "Invalid amount";
    }
  };

  // Get current user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Check if user can edit (only admin can edit products they created)
  const canEdit = (userRole === "admin" && user?.id === product?.createdBy);

  // Panel footer with close button and edit button (if user can edit)
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-white hover:text-white"
        style={{ borderColor: '#6B7280', background: 'transparent' }}
      >
        Close
      </Button>
      {onEdit && product && canEdit && (
        <Button
          type="primary"
          onClick={() => onEdit(product.id)}
        >
          Edit
        </Button>
      )}
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title="Product Details"
      width="500px" // This will be overridden on mobile by the SlidingPanel component
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-full min-h-[300px]">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
        </div>
      ) : product ? (
        <>
          {/* Product detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <ShoppingOutlined className="mr-2" />
              Product: {product.name}
            </h2>
            <p className="text-gray-600 mt-1 flex items-center">
              Complete product information and details
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="product-detail-light"
            labelStyle={{ color: '#333', backgroundColor: '#f5f5f5' }}
            contentStyle={{ color: '#333', backgroundColor: '#ffffff' }}
          >
            <Descriptions.Item label={<span><ShoppingOutlined /> Product ID</span>}>
              {product.id}
            </Descriptions.Item>

            <Descriptions.Item label={<span><ShoppingOutlined /> Name</span>}>
              {product.name}
            </Descriptions.Item>

            <Descriptions.Item label={<span><BarcodeOutlined /> SKU</span>}>
              {product.sku || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item label={<span><BarcodeOutlined /> Barcode</span>}>
              {product.barcode || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item label={<span><DollarOutlined /> Price</span>}>
              {formatCurrency(product.price)}
            </Descriptions.Item>

            <Descriptions.Item label={<span><DollarOutlined /> Cost</span>}>
              {formatCurrency(product.cost)}
            </Descriptions.Item>

            <Descriptions.Item label="Stock Quantity">
              <Tag color={
                product.stockQuantity <= 0
                  ? 'red'
                  : product.stockQuantity <= (product.minStockLevel || 5)
                    ? 'orange'
                    : 'green'
              }>
                {product.stockQuantity}
              </Tag>
            </Descriptions.Item>

            <Descriptions.Item label="Min Stock Level">
              {product.minStockLevel || "N/A"}
            </Descriptions.Item>

            <Descriptions.Item label="Expiry Date">
              {formatDate(product.expiryDate)}
            </Descriptions.Item>

            <Descriptions.Item label={<span><CalendarOutlined /> Created At</span>}>
              {formatDate(product.createdAt)}
            </Descriptions.Item>
          </Descriptions>
        </>
      ) : (
        <div className="flex justify-center items-center h-full min-h-[300px]">
          <p className="text-gray-800">No product data available</p>
        </div>
      )}
    </SlidingPanel>
  );
};

export default ProductDetailPanel;
