"use client";

import React, { useState } from "react";
import { Card, Button, Spin, Input, Select, DatePicker, Space, notification } from "antd";
import {
  DollarOutlined,
  LoadingOutlined,
  SearchOutlined,
  PlusOutlined,
  FilterOutlined,
  ReloadOutlined,
  PrinterOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGetAllExpensesQuery, useDeleteExpenseMutation, type Expense } from "@/reduxRTK/services/expenseApi";
import { useGetAllExpenseCategoriesQuery } from "@/reduxRTK/services/expenseCategoryApi";
import ExpenseTable from "@/components/Expenses/ExpenseTable";
import ExpensePagination from "@/components/Expenses/ExpensePagination";
import ExpenseFormPanel from "@/components/Expenses/ExpenseFormPanel";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { showMessage } from "@/utils/showMessage";
import { UserRole } from "@/types/user";
import dayjs from "dayjs";
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ExpensesPage: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const userRole = user?.role as UserRole;

  // State management
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<number | undefined>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [selectedExpenses, setSelectedExpenses] = useState<number[]>([]);
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);

  // Confirmation dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [expenseToDelete, setExpenseToDelete] = useState<number | null>(null);
  const [expensesToDelete, setExpensesToDelete] = useState<number[]>([]);

  // Check permissions
  const canAddExpense = userRole === "admin" || userRole === "superadmin";
  const canDeleteExpense = userRole === "admin" || userRole === "superadmin";

  // Prepare query parameters
  const queryParams = {
    page,
    limit,
    search: search.trim(),
    categoryId: categoryFilter,
    startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
    endDate: dateRange?.[1]?.endOf('day').toISOString(),
  };

  // API hooks
  const { data: expensesData, isLoading, error, refetch } = useGetAllExpensesQuery(queryParams);
  const { data: categoriesData } = useGetAllExpenseCategoriesQuery({ page: 1, limit: 100 });
  const [deleteExpense, { isLoading: isDeleting }] = useDeleteExpenseMutation();

  const expenses = expensesData?.data?.expenses || [];
  const total = expensesData?.data?.total || 0;
  const categories = categoriesData?.data?.categories || [];

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle category filter
  const handleCategoryFilter = (categoryId: number | undefined) => {
    setCategoryFilter(categoryId);
    setPage(1);
  };

  // Handle date range filter
  const handleDateRangeFilter = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setDateRange([dates[0], dates[1].endOf('day')]);
    } else {
      setDateRange(null);
    }
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle add expense
  const handleAddExpense = () => {
    if (!canAddExpense) {
      showMessage.error("You don't have permission to add expenses");
      return;
    }
    setEditingExpense(null);
    setIsAddPanelOpen(true);
  };

  // Handle edit expense
  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense);
    setIsAddPanelOpen(true);
  };

  // Handle delete expense
  const handleDeleteExpense = (expenseId: number) => {
    if (!canDeleteExpense) {
      showMessage.error("You don't have permission to delete expenses");
      return;
    }

    setExpenseToDelete(expenseId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete expense
  const confirmDeleteExpense = async () => {
    if (expenseToDelete) {
      try {
        const result = await deleteExpense(expenseToDelete).unwrap();
        if (result.success) {
          showMessage.success("Expense deleted successfully!");
          setSelectedExpenses(prev => prev.filter(id => id !== expenseToDelete));
          setIsDeleteDialogOpen(false);
          setExpenseToDelete(null);
        } else {
          showMessage.error(result.message || "Failed to delete expense");
        }
      } catch (error: any) {
        console.error("Error deleting expense:", error);
        showMessage.error(error?.data?.message || "Failed to delete expense");
      }
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setExpenseToDelete(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (expenseIds: number[]) => {
    if (!canDeleteExpense) {
      showMessage.error("You don't have permission to delete expenses");
      return;
    }

    setExpensesToDelete(expenseIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    try {
      // Delete expenses one by one (since backend doesn't have bulk delete)
      const deletePromises = expensesToDelete.map(id => deleteExpense(id).unwrap());
      await Promise.all(deletePromises);

      showMessage.success(`${expensesToDelete.length} expense(s) deleted successfully!`);
      setSelectedExpenses([]);
      setIsBulkDeleteDialogOpen(false);
      setExpensesToDelete([]);
    } catch (error: any) {
      console.error("Error deleting expenses:", error);
      showMessage.error("Failed to delete some expenses");
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setExpensesToDelete([]);
  };

  // Handle form success
  const handleFormSuccess = () => {
    refetch();
    setSelectedExpenses([]);
  };

  // Clear filters
  const clearFilters = () => {
    setSearch("");
    setCategoryFilter(undefined);
    setDateRange(null);
    setPage(1);
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      notification.error({ message: 'Please allow popups to print expenses' });
      return;
    }
    const printContent = `
      <html>
        <head>
          <title>Expenses Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print {
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Expenses Report</h2>
            <p class="date">Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Title</th>
                <th>Amount</th>
                <th>Category</th>
                <th>Date</th>
                <th>Payment Method</th>
                <th>Vendor</th>
              </tr>
            </thead>
            <tbody>
              ${expenses.map(expense => `
                <tr>
                  <td>${expense.title}</td>
                  <td>₵${parseFloat(expense.amount).toFixed(2)}</td>
                  <td>${expense.category?.name || 'No category'}</td>
                  <td>${dayjs(expense.expenseDate).format('MMM DD, YYYY')}</td>
                  <td>${expense.paymentMethod}</td>
                  <td>${expense.vendor || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;
    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  const handleExportPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text('Expenses Report', 14, 15);
    doc.setFontSize(10);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}`, 14, 22);
    const tableData = expenses.map(expense => [
      expense.title,
      `₵${parseFloat(expense.amount).toFixed(2)}`,
      expense.category?.name || 'No category',
      dayjs(expense.expenseDate).format('MMM DD, YYYY'),
      expense.paymentMethod,
      expense.vendor || '-'
    ]);
    autoTable(doc, {
      head: [['Title', 'Amount', 'Category', 'Date', 'Payment Method', 'Vendor']],
      body: tableData,
      startY: 30,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [41, 128, 185] }
    });
    doc.save('expenses-report.pdf');
  };

  if (error) {
    return (
      <div className="w-full p-2 sm:p-4">
        <Card className="w-full">
          <div className="text-center text-red-500 p-8">
            <p>Error loading expenses. Please try again.</p>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Expense Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          <div className={isMobile ? "flex flex-col gap-2" : "flex flex-row gap-2 items-center"}>
            <Button
              type="primary"
              icon={<PlusOutlined />} 
              onClick={handleAddExpense}
              size={isMobile ? "small" : "middle"}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMobile ? "" : "Add Expense"}
            </Button>
            <Button
              type="primary"
              icon={<PrinterOutlined />} 
              onClick={handlePrint}
              size={isMobile ? "small" : "middle"}
              className="bg-green-600 hover:bg-green-700"
            >
              {isMobile ? "" : "Print"}
            </Button>
            <Button
              type="primary"
              icon={<DownloadOutlined />} 
              onClick={handleExportPDF}
              size={isMobile ? "small" : "middle"}
              className="bg-green-600 hover:bg-green-700"
            >
              {isMobile ? "" : "Export"}
            </Button>
          </div>
        }
      >
        {/* Filters */}
        <div className="mb-4 space-y-3">
          <div className="flex flex-col sm:flex-row gap-3">
            <Input
              placeholder="Search expenses..."
              prefix={<SearchOutlined />}
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              className="flex-1"
              allowClear
            />

            <Select
              placeholder="Filter by category"
              value={categoryFilter}
              onChange={handleCategoryFilter}
              className="w-full sm:w-48"
              allowClear
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </div>
                </Option>
              ))}
            </Select>

            <RangePicker
              value={dateRange}
              onChange={handleDateRangeFilter}
              className="w-full sm:w-64"
              format="YYYY-MM-DD"
            />

            {(search || categoryFilter || dateRange) && (
              <Button onClick={clearFilters} icon={<FilterOutlined />}>
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="min-h-96">
          {isLoading ? (
            <div className="flex justify-center items-center h-96">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
            </div>
          ) : (
            <>
              {expenses.length > 0 ? (
                <ExpenseTable
                  expenses={expenses}
                  loading={isLoading}
                  onEdit={handleEditExpense}
                  onDelete={handleDeleteExpense}
                  onBulkDelete={canDeleteExpense ? handleBulkDelete : undefined}
                  selectedExpenses={selectedExpenses}
                  onSelectionChange={setSelectedExpenses}
                  isMobile={isMobile}
                />
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <DollarOutlined className="text-4xl mb-4" />
                  {search || categoryFilter || dateRange ? (
                    <p>No expenses found matching your filters.</p>
                  ) : (
                    <p>No expenses found. {canAddExpense && "Click 'Add Expense' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {expenses.length > 0 && (
                <ExpensePagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add/Edit Expense Panel */}
      <ExpenseFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => {
          setIsAddPanelOpen(false);
          setEditingExpense(null);
        }}
        onSuccess={handleFormSuccess}
        expense={editingExpense}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteExpense}
        title="Delete Expense"
        message="Are you sure you want to delete this expense? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Expenses"
        message={`Are you sure you want to delete ${expensesToDelete.length} expenses? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />
    </div>
  );
};

export default ExpensesPage;
