"use client";

import React, { useState, useEffect } from 'react';
import { Button, Modal, Typography, Space, Alert } from 'antd';
import { DownloadOutlined, MobileOutlined, DesktopOutlined, CloseOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkInstallation = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = (window.navigator as any).standalone === true;
      const isInstalled = isStandaloneMode || isIOSStandalone;
      
      setIsStandalone(isInstalled);
      setIsInstalled(isInstalled);
    };

    checkInstallation();

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show install prompt after a delay if not already installed
      if (!isInstalled) {
        setTimeout(() => {
          setShowInstallPrompt(true);
        }, 5000); // Show after 5 seconds
      }
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    } catch (error) {
      console.error('Error during installation:', error);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // Don't show again for this session
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  };

  // Don't show if already installed or dismissed this session
  if (isInstalled || sessionStorage.getItem('pwa-install-dismissed')) {
    return null;
  }

  return (
    <Modal
      title={
        <div className="flex items-center">
          <DownloadOutlined className="mr-2 text-blue-600" />
          <span>Install NEXAPO POS</span>
        </div>
      }
      open={showInstallPrompt}
      onCancel={handleDismiss}
      footer={[
        <Button key="dismiss" onClick={handleDismiss}>
          Not Now
        </Button>,
        <Button
          key="install"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleInstallClick}
          disabled={!deferredPrompt}
        >
          Install App
        </Button>,
      ]}
      width={400}
      centered
    >
      <div className="text-center">
        <div className="mb-4">
          <MobileOutlined className="text-4xl text-blue-600 mr-2" />
          <DesktopOutlined className="text-4xl text-green-600" />
        </div>
        
        <Title level={4} className="mb-3">
          Install for Better Experience
        </Title>
        
        <Paragraph className="text-gray-600 mb-4">
          Install NEXAPO POS for faster access, offline functionality, and a native app experience.
        </Paragraph>

        <div className="bg-green-50 p-3 rounded-lg border border-green-200 mb-4">
          <Title level={5} className="text-green-700 mb-2">
            ✨ Benefits of Installing:
          </Title>
          <div className="text-left text-green-600 text-sm space-y-1">
            <div>• Works completely offline</div>
            <div>• Faster loading times</div>
            <div>• Desktop/mobile app experience</div>
            <div>• Background sync when online</div>
            <div>• Push notifications (coming soon)</div>
          </div>
        </div>

        <Alert
          message="Offline POS Ready"
          description="Once installed, you can use the POS system even without internet connection."
          type="info"
          showIcon
          className="text-left"
        />
      </div>
    </Modal>
  );
};

export default PWAInstallPrompt;
