import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createPurchase,
  getAllPurchases,
  getPurchaseById,
  updatePurchaseById,
  deletePurchaseById,
} from "../services/purchaseService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handlePurchaseRequest = async (req: Request, res: Response): Promise<void> => {
  const { mode, purchaseId, page, limit, ...data } = req.body;
  const requester = req.user as DecodedToken; // ✅ Extract requester from middleware

  // ✅ Validate mode
  const validModes = ["createnew", "update", "delete", "retrieve"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      // ✅ Create New Purchase
      case "createnew": {
        if (!data.productId || !data.quantity || !data.costPrice || !data.totalCost) {
          return sendResponse(res, 400, false, "All purchase details are required.");
        }

        const newPurchase = await createPurchase(requester, {
          supplierId: data.supplierId,
          productId: data.productId,
          quantity: data.quantity,
          costPrice: data.costPrice,
          totalCost: data.totalCost,
        });

        return sendResponse(res, 201, true, "Purchase created successfully.", newPurchase);
      }

      // ✅ Retrieve Purchases (Single or All)
      case "retrieve": {
        if (purchaseId) {
          // Fetch a single purchase by ID
          const purchase = await getPurchaseById(requester, Number(purchaseId));
          return sendResponse(res, 200, true, "Purchase retrieved successfully.", purchase);
        } else {
          // Fetch all purchases with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const purchases = await getAllPurchases(requester, pageNum, limitNum);
          return sendResponse(res, 200, true, "Purchases retrieved successfully.", purchases);
        }
      }

      // ✅ Update Purchase
      case "update": {
        if (!purchaseId) {
          return sendResponse(res, 400, false, "Purchase ID is required for updating.");
        }

        const updatedPurchase = await updatePurchaseById(requester, Number(purchaseId), data);
        return sendResponse(res, 200, true, "Purchase updated successfully.", updatedPurchase);
      }

      // ✅ Delete Purchase (Single or Multiple)
      case "delete": {
        // Check if we have purchaseIds (array) or purchaseId (single)
        const { purchaseIds } = req.body;

        if (!purchaseId && !purchaseIds) {
          return sendResponse(res, 400, false, "Purchase ID(s) are required for deletion.");
        }

        // Use purchaseIds if provided, otherwise use single purchaseId
        const idsToDelete = purchaseIds
          ? (Array.isArray(purchaseIds) ? purchaseIds : [purchaseIds])
          : [Number(purchaseId)];

        const result = await deletePurchaseById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} purchases deleted successfully.`
          : "Purchase deleted successfully.";

        return sendResponse(res, 200, true, message, result);
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(res, 500, false, error.message || "Internal Server Error");
  }
};
