"use client";

import React from "react";
import { Descriptions, Tag, <PERSON><PERSON>, Spin } from "antd";
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  WarningOutlined,
  LoadingOutlined
} from "@ant-design/icons";
import { PaymentStatus, UserRole } from "@/types/user";
import dayjs from "dayjs";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useUserDetail } from "@/hooks/users/useUserDetail";
import { formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";
import "./user-panels.css";

interface UserDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number | null;
}

const UserDetailPanel: React.FC<UserDetailPanelProps> = ({
  isOpen,
  onClose,
  userId,
}) => {
  // Use custom hook to fetch user details
  const { user, isLoading } = useUserDetail(userId);

  const getPaymentStatusTag = (status: PaymentStatus) => {
    switch (status) {
      case "paid":
        return <Tag icon={<CheckCircleOutlined />} color="success">Paid</Tag>;
      case "pending":
        return <Tag icon={<ClockCircleOutlined />} color="warning">Pending</Tag>;
      case "overdue":
        return <Tag icon={<WarningOutlined />} color="error">Overdue</Tag>;
      case "inactive":
        return <Tag icon={<StopOutlined />} color="default">Inactive</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const getRoleTag = (role: UserRole) => {
    switch (role) {
      case "superadmin":
        return <Tag color="purple">Super Admin</Tag>;
      case "admin":
        return <Tag color="blue">Admin</Tag>;
      case "cashier":
        return <Tag color="green">Cashier</Tag>;
      default:
        return <Tag color="default">{role}</Tag>;
    }
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Panel footer with close button
  const panelFooter = (
    <div className="flex justify-end">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Close
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title="User Details"
      width="500px" // This will be overridden on mobile by the SlidingPanel component
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-full min-h-[300px]">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
        </div>
      ) : user ? (
        <>
          {/* User detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              User Profile: {user.name}
              <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                user.role === 'superadmin' ? 'bg-purple-100 text-purple-800' :
                user.role === 'admin' ? 'bg-blue-100 text-blue-800' :
                'bg-green-100 text-green-800'
              }`}>
                {user.role === 'superadmin' ? 'Super Admin' :
                 user.role === 'admin' ? 'Admin' :
                 'Cashier'}
              </span>
            </h2>
            <p className="text-gray-600 mt-1 flex items-center">
              Complete user information and account details
              <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                user.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :
                user.paymentStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                user.paymentStatus === 'overdue' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {user.paymentStatus === 'paid' ? 'Paid' :
                 user.paymentStatus === 'pending' ? 'Pending' :
                 user.paymentStatus === 'overdue' ? 'Overdue' :
                 'Inactive'}
              </span>
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="user-detail-dark"
            labelStyle={{ color: '#333', backgroundColor: '#f5f5f5' }}
            contentStyle={{ color: '#333', backgroundColor: '#ffffff' }}
          >
          <Descriptions.Item label={<span><UserOutlined /> Name</span>}>
            {user.name}
          </Descriptions.Item>

          <Descriptions.Item label={<span><MailOutlined /> Email</span>}>
            {user.email}
          </Descriptions.Item>

          <Descriptions.Item label={<span><PhoneOutlined /> Phone</span>}>
            {formatPhoneNumberForDisplay(user.phone)}
          </Descriptions.Item>

          <Descriptions.Item label="Role">
            {getRoleTag(user.role)}
          </Descriptions.Item>

          <Descriptions.Item label="Payment Status">
            {getPaymentStatusTag(user.paymentStatus)}
          </Descriptions.Item>

          <Descriptions.Item label={<span><CalendarOutlined /> Created At</span>}>
            {formatDate(user.createdAt)}
          </Descriptions.Item>

          <Descriptions.Item label="Last Payment Date">
            {formatDate(user.lastPaymentDate)}
          </Descriptions.Item>

          <Descriptions.Item label="Next Payment Due">
            {formatDate(user.nextPaymentDue)}
          </Descriptions.Item>
        </Descriptions>
        </>
      ) : (
        <div className="text-center py-8 text-gray-800">User not found</div>
      )}
    </SlidingPanel>
  );
};

export default UserDetailPanel;
