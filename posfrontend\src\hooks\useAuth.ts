import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useCheckPaymentStatus } from "./useCheckPaymentStatus";
import { UserRole, ApiResponse, User } from "@/types/user";
import { useGetUserByIdQuery } from "@/reduxRTK/services/authApi";
import { setUser } from "@/reduxRTK/services/authSlice";

/**
 * Custom hook to check authentication status and user permissions
 * Provides helper methods for checking auth status, roles, and payment status
 */

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, accessToken } = useSelector((state: RootState) => state.auth);
  const paymentStatus = useCheckPaymentStatus();

  // Get the getUserById query (but don't execute it yet)
  const { refetch } = useGetUserByIdQuery(user?.id || 0, {
    skip: !user?.id, // Skip the query if there's no user ID
  });

  // Debug log (always log for now to debug the issue)
  console.log("useAuth - Auth State:", {
    isAuthenticated: !!user && !!accessToken,
    role: user?.role,
    phone: user?.phone,
    phoneType: user?.phone ? typeof user.phone : 'undefined/null',
    createdAt: user?.createdAt,
    createdAtType: user?.createdAt ? typeof user.createdAt : 'undefined/null'
  });

  // Log the complete user object for debugging
  console.log("useAuth - Complete user object:", JSON.stringify(user, null, 2));

  // Check if user is authenticated
  const isAuthenticated = !!user && !!accessToken;

  // Check if user has a specific role
  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false;

    if (Array.isArray(roles)) {
      return roles.includes(user.role);
    }

    return user.role === roles;
  };

  // Check if user is a superadmin
  const isSuperAdmin = (): boolean => {
    return user?.role === "superadmin";
  };

  // Check if user is an admin
  const isAdmin = (): boolean => {
    return user?.role === "admin";
  };

  // Check if user is a cashier
  const isCashier = (): boolean => {
    return user?.role === "cashier";
  };

  // Check if payment is required
  const needsPayment = (): boolean => {
    if (!user) return false;
    if (user.role === "superadmin") return false;
    return paymentStatus.needsPayment;
  };

  // Function to refresh user data from the backend
  const refreshUser = async (): Promise<void> => {
    if (!user?.id) {
      console.error("Cannot refresh user data: No user ID available");
      return;
    }

    try {
      console.log("useAuth - Refreshing user data for ID:", user.id);

      // Refetch user data from the backend
      const result = await refetch();
      console.log("useAuth - Refetch result:", result);

      const apiResponse = result.data as ApiResponse<User>;

      if (apiResponse?.success && apiResponse?.data) {
        console.log("useAuth - API response data:", apiResponse.data);

        // Store the current payment status and dates
        const currentPaymentStatus = user.paymentStatus;
        const currentLastPaymentDate = user.lastPaymentDate;
        const currentNextPaymentDue = user.nextPaymentDue;

        // Ensure all fields are properly defined
        const phoneValue = apiResponse.data.phone || user.phone || "";
        const createdAtValue = apiResponse.data.createdAt || user.createdAt || "";
        const lastPaymentDateValue = apiResponse.data.lastPaymentDate || user.lastPaymentDate || undefined;
        const nextPaymentDueValue = apiResponse.data.nextPaymentDue || user.nextPaymentDue || null;
        const createdByValue = apiResponse.data.createdBy || user.createdBy || undefined;

        console.log("useAuth - User field values:", {
          apiPhone: apiResponse.data.phone,
          userPhone: user.phone,
          finalPhone: phoneValue,
          apiCreatedAt: apiResponse.data.createdAt,
          userCreatedAt: user.createdAt,
          finalCreatedAt: createdAtValue,
          apiLastPaymentDate: apiResponse.data.lastPaymentDate,
          userLastPaymentDate: user.lastPaymentDate,
          finalLastPaymentDate: lastPaymentDateValue,
          apiNextPaymentDue: apiResponse.data.nextPaymentDue,
          userNextPaymentDue: user.nextPaymentDue,
          finalNextPaymentDue: nextPaymentDueValue,
          apiCreatedBy: apiResponse.data.createdBy,
          userCreatedBy: user.createdBy,
          finalCreatedBy: createdByValue
        });

        // Update the user in the Redux store but preserve payment status
        // This prevents unwanted redirects to payment page after profile update
        const updatedUserData = {
          ...apiResponse.data,
          phone: phoneValue,
          createdAt: createdAtValue,
          lastPaymentDate: lastPaymentDateValue,
          nextPaymentDue: nextPaymentDueValue,
          createdBy: createdByValue,
          // Keep original payment status to prevent redirect
          paymentStatus: currentPaymentStatus
        };

        console.log("useAuth - Updating Redux store with:", updatedUserData);

        // IMPORTANT: Use the current access token from Redux store
        // This prevents token issues that could cause redirects
        console.log("useAuth - Using current access token:",
          accessToken ? "Token exists (not showing for security)" : "No token found");

        // Set a flag to indicate we're updating the profile
        // This will prevent any redirects during the update
        (window as any).__PROFILE_UPDATE_IN_PROGRESS = true;

        // Store the current pathname to help with debugging
        (window as any).__LAST_PROFILE_UPDATE_PATH = window.location.pathname;

        dispatch(setUser({
          user: updatedUserData,
          accessToken: accessToken || ''
        }));

        // Clear the flag after a short delay to ensure the update is complete
        setTimeout(() => {
          (window as any).__PROFILE_UPDATE_IN_PROGRESS = false;
          console.log("useAuth - Profile update flag cleared");
        }, 500);

        console.log("User data refreshed successfully (payment status preserved)");
      } else {
        console.error("Failed to refresh user data:", apiResponse?.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
    }
  };

  return {
    user,
    accessToken,
    isAuthenticated,
    hasRole,
    isSuperAdmin,
    isAdmin,
    isCashier,
    needsPayment,
    paymentStatus,
    refreshUser,
  };
};


