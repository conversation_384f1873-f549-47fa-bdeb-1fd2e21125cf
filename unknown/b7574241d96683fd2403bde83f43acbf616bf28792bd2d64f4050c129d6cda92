"use client";

import React, { useState } from "react";
import { Card, Button, Spin, Input, Select, DatePicker, Space, notification } from "antd";
import {
  DollarOutlined,
  LoadingOutlined,
  SearchOutlined,
  PlusOutlined,
  FilterOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGetAllExpensesQuery, useDeleteExpenseMutation, type Expense } from "@/reduxRTK/services/expenseApi";
import { useGetAllExpenseCategoriesQuery } from "@/reduxRTK/services/expenseCategoryApi";
import ExpenseTable from "@/components/Expenses/ExpenseTable";
import ExpensePagination from "@/components/Expenses/ExpensePagination";
import ExpenseFormPanel from "@/components/Expenses/ExpenseFormPanel";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { showMessage } from "@/utils/showMessage";
import { UserRole } from "@/types/user";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;
const { Option } = Select;

const ExpensesPage: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const userRole = user?.role as UserRole;

  // State management
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<number | undefined>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [selectedExpenses, setSelectedExpenses] = useState<number[]>([]);
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);

  // Confirmation dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [expenseToDelete, setExpenseToDelete] = useState<number | null>(null);
  const [expensesToDelete, setExpensesToDelete] = useState<number[]>([]);

  // Check permissions
  const canAddExpense = userRole === "admin" || userRole === "superadmin";
  const canDeleteExpense = userRole === "admin" || userRole === "superadmin";

  // Prepare query parameters
  const queryParams = {
    page,
    limit,
    search: search.trim(),
    categoryId: categoryFilter,
    startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
    endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
  };

  // API hooks
  const { data: expensesData, isLoading, error, refetch } = useGetAllExpensesQuery(queryParams);
  const { data: categoriesData } = useGetAllExpenseCategoriesQuery({ page: 1, limit: 100 });
  const [deleteExpense, { isLoading: isDeleting }] = useDeleteExpenseMutation();

  const expenses = expensesData?.data?.expenses || [];
  const total = expensesData?.data?.total || 0;
  const categories = categoriesData?.data?.categories || [];

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle category filter
  const handleCategoryFilter = (categoryId: number | undefined) => {
    setCategoryFilter(categoryId);
    setPage(1);
  };

  // Handle date range filter
  const handleDateRangeFilter = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setDateRange([dates[0], dates[1]]);
    } else {
      setDateRange(null);
    }
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle add expense
  const handleAddExpense = () => {
    if (!canAddExpense) {
      showMessage.error("You don't have permission to add expenses");
      return;
    }
    setEditingExpense(null);
    setIsAddPanelOpen(true);
  };

  // Handle edit expense
  const handleEditExpense = (expense: Expense) => {
    setEditingExpense(expense);
    setIsAddPanelOpen(true);
  };

  // Handle delete expense
  const handleDeleteExpense = (expenseId: number) => {
    if (!canDeleteExpense) {
      showMessage.error("You don't have permission to delete expenses");
      return;
    }

    setExpenseToDelete(expenseId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete expense
  const confirmDeleteExpense = async () => {
    if (expenseToDelete) {
      try {
        const result = await deleteExpense(expenseToDelete).unwrap();
        if (result.success) {
          showMessage.success("Expense deleted successfully!");
          setSelectedExpenses(prev => prev.filter(id => id !== expenseToDelete));
          setIsDeleteDialogOpen(false);
          setExpenseToDelete(null);
        } else {
          showMessage.error(result.message || "Failed to delete expense");
        }
      } catch (error: any) {
        console.error("Error deleting expense:", error);
        showMessage.error(error?.data?.message || "Failed to delete expense");
      }
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setExpenseToDelete(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (expenseIds: number[]) => {
    if (!canDeleteExpense) {
      showMessage.error("You don't have permission to delete expenses");
      return;
    }

    setExpensesToDelete(expenseIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    try {
      // Delete expenses one by one (since backend doesn't have bulk delete)
      const deletePromises = expensesToDelete.map(id => deleteExpense(id).unwrap());
      await Promise.all(deletePromises);

      showMessage.success(`${expensesToDelete.length} expense(s) deleted successfully!`);
      setSelectedExpenses([]);
      setIsBulkDeleteDialogOpen(false);
      setExpensesToDelete([]);
    } catch (error: any) {
      console.error("Error deleting expenses:", error);
      showMessage.error("Failed to delete some expenses");
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setExpensesToDelete([]);
  };

  // Handle form success
  const handleFormSuccess = () => {
    refetch();
    setSelectedExpenses([]);
  };

  // Clear filters
  const clearFilters = () => {
    setSearch("");
    setCategoryFilter(undefined);
    setDateRange(null);
    setPage(1);
  };

  if (error) {
    return (
      <div className="w-full p-2 sm:p-4">
        <Card className="w-full">
          <div className="text-center text-red-500 p-8">
            <p>Error loading expenses. Please try again.</p>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Expense Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          canAddExpense && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddExpense}
              size={isMobile ? "small" : "middle"}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMobile ? "" : "Add Expense"}
            </Button>
          )
        }
      >
        {/* Filters */}
        <div className="mb-4 space-y-3">
          <div className="flex flex-col sm:flex-row gap-3">
            <Input
              placeholder="Search expenses..."
              prefix={<SearchOutlined />}
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              className="flex-1"
              allowClear
            />

            <Select
              placeholder="Filter by category"
              value={categoryFilter}
              onChange={handleCategoryFilter}
              className="w-full sm:w-48"
              allowClear
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </div>
                </Option>
              ))}
            </Select>

            <RangePicker
              value={dateRange}
              onChange={handleDateRangeFilter}
              className="w-full sm:w-64"
              format="YYYY-MM-DD"
            />

            {(search || categoryFilter || dateRange) && (
              <Button onClick={clearFilters} icon={<FilterOutlined />}>
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="min-h-96">
          {isLoading ? (
            <div className="flex justify-center items-center h-96">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
            </div>
          ) : (
            <>
              {expenses.length > 0 ? (
                <ExpenseTable
                  expenses={expenses}
                  loading={isLoading}
                  onEdit={handleEditExpense}
                  onDelete={handleDeleteExpense}
                  onBulkDelete={canDeleteExpense ? handleBulkDelete : undefined}
                  selectedExpenses={selectedExpenses}
                  onSelectionChange={setSelectedExpenses}
                  isMobile={isMobile}
                />
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <DollarOutlined className="text-4xl mb-4" />
                  {search || categoryFilter || dateRange ? (
                    <p>No expenses found matching your filters.</p>
                  ) : (
                    <p>No expenses found. {canAddExpense && "Click 'Add Expense' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {expenses.length > 0 && (
                <ExpensePagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add/Edit Expense Panel */}
      <ExpenseFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => {
          setIsAddPanelOpen(false);
          setEditingExpense(null);
        }}
        onSuccess={handleFormSuccess}
        expense={editingExpense}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteExpense}
        title="Delete Expense"
        message="Are you sure you want to delete this expense? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Expenses"
        message={`Are you sure you want to delete ${expensesToDelete.length} expenses? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />
    </div>
  );
};

export default ExpensesPage;
