import { and, eq, desc, sql, count, gte, lt, inArray, or, ilike } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { expenses, expenseCategories, users } from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";
import { expenseSchema, expenseCategorySchema } from "../validation/schema";

/**
 * ✅ Create a new expense
 */
export const createExpense = async (
  requester: JwtPayload,
  expenseData: {
    title: string;
    description?: string;
    amount: number;
    categoryId?: number;
    expenseDate?: Date;
    paymentMethod: string;
    receiptUrl?: string;
    vendor?: string;
    isRecurring?: boolean;
    recurringFrequency?: string;
    tags?: string;
    storeId?: number;
  }
) => {
  // Validate expense data
  expenseSchema.parse(expenseData);
  await authorizeAction(requester, "create", "expenses");

  const createdBy = requester.id;

  // Insert new expense record
  const [newExpense] = await postgresDb
    .insert(expenses)
    .values({
      ...expenseData,
      amount: expenseData.amount.toString(),
      createdBy,
    })
    .returning();

  return newExpense;
};

/**
 * ✅ Get all expenses with pagination and filtering
 */
export const getAllExpenses = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10,
  search: string = '',
  categoryId?: number,
  startDate?: Date,
  endDate?: Date
) => {
  await authorizeAction(requester, "getAll", "expenses");

  // Build team member IDs for admin users
  let teamMemberIds: number[] = [];
  if (requester.role === "admin") {
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);
  }

  // Include the requester's ID in the team IDs
  const teamIds = requester.role === "admin"
    ? [requester.id, ...teamMemberIds]
    : [requester.id];

  // Build where conditions
  const whereConditions = [inArray(expenses.createdBy, teamIds)];

  if (search && search.trim()) {
    const searchTerm = `%${search.trim()}%`;
    whereConditions.push(
      sql`(
        ${expenses.title} ILIKE ${searchTerm} OR
        ${expenses.description} ILIKE ${searchTerm} OR
        ${expenses.vendor} ILIKE ${searchTerm}
      )`
    );
  }

  if (categoryId) {
    whereConditions.push(eq(expenses.categoryId, categoryId));
  }

  if (startDate) {
    whereConditions.push(gte(expenses.expenseDate, startDate));
  }

  if (endDate) {
    whereConditions.push(lt(expenses.expenseDate, endDate));
  }

  // Get total count
  const totalCountResult = await postgresDb
    .select({ count: count() })
    .from(expenses)
    .where(and(...whereConditions));

  const totalCount = totalCountResult[0]?.count || 0;

  // Get expenses with category information
  const expensesResult = await postgresDb
    .select({
      id: expenses.id,
      title: expenses.title,
      description: expenses.description,
      amount: expenses.amount,
      categoryId: expenses.categoryId,
      categoryName: expenseCategories.name,
      categoryColor: expenseCategories.color,
      expenseDate: expenses.expenseDate,
      paymentMethod: expenses.paymentMethod,
      receiptUrl: expenses.receiptUrl,
      vendor: expenses.vendor,
      isRecurring: expenses.isRecurring,
      recurringFrequency: expenses.recurringFrequency,
      tags: expenses.tags,
      storeId: expenses.storeId,
      createdBy: expenses.createdBy,
      createdAt: expenses.createdAt,
      updatedAt: expenses.updatedAt,
    })
    .from(expenses)
    .leftJoin(expenseCategories, eq(expenses.categoryId, expenseCategories.id))
    .where(and(...whereConditions))
    .orderBy(desc(expenses.expenseDate))
    .limit(limit)
    .offset((page - 1) * limit);

  return {
    expenses: expensesResult.map(expense => ({
      ...expense,
      category: expense.categoryName ? {
        id: expense.categoryId!,
        name: expense.categoryName,
        color: expense.categoryColor || '#6B7280'
      } : undefined
    })),
    total: totalCount,
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
  };
};

/**
 * ✅ Get expense by ID
 */
export const getExpenseById = async (requester: JwtPayload, expenseId: number) => {
  await authorizeAction(requester, "getById", "expenses", expenseId);

  const expenseResult = await postgresDb
    .select({
      id: expenses.id,
      title: expenses.title,
      description: expenses.description,
      amount: expenses.amount,
      categoryId: expenses.categoryId,
      categoryName: expenseCategories.name,
      categoryColor: expenseCategories.color,
      expenseDate: expenses.expenseDate,
      paymentMethod: expenses.paymentMethod,
      receiptUrl: expenses.receiptUrl,
      vendor: expenses.vendor,
      isRecurring: expenses.isRecurring,
      recurringFrequency: expenses.recurringFrequency,
      tags: expenses.tags,
      storeId: expenses.storeId,
      createdBy: expenses.createdBy,
      createdAt: expenses.createdAt,
      updatedAt: expenses.updatedAt,
    })
    .from(expenses)
    .leftJoin(expenseCategories, eq(expenses.categoryId, expenseCategories.id))
    .where(eq(expenses.id, expenseId))
    .limit(1);

  if (expenseResult.length === 0) {
    throw new Error("Expense not found.");
  }

  return expenseResult[0];
};

/**
 * ✅ Update expense by ID
 */
export const updateExpenseById = async (
  requester: JwtPayload,
  expenseId: number,
  updateData: Partial<{
    title: string;
    description: string;
    amount: number;
    categoryId: number;
    expenseDate: Date;
    paymentMethod: string;
    receiptUrl: string;
    vendor: string;
    isRecurring: boolean;
    recurringFrequency: string;
    tags: string;
    storeId: number;
  }>
) => {
  await authorizeAction(requester, "update", "expenses", expenseId);

  // Convert amount to string if provided
  const processedUpdateData = {
    ...updateData,
    ...(updateData.amount && { amount: updateData.amount.toString() }),
    updatedAt: new Date(),
  };

  const [updatedExpense] = await postgresDb
    .update(expenses)
    .set({
      ...processedUpdateData,
      amount: typeof updateData.amount === 'number' ? updateData.amount.toString() : updateData.amount,
    })
    .where(eq(expenses.id, expenseId))
    .returning();

  if (!updatedExpense) {
    throw new Error("Expense not found or update failed.");
  }

  return updatedExpense;
};

/**
 * ✅ Delete expense(s) by ID(s)
 */
export const deleteExpenseById = async (
  requester: JwtPayload,
  expenseIds: number[]
) => {
  // Validate each expense before deletion
  for (const expenseId of expenseIds) {
    await authorizeAction(requester, "delete", "expenses", expenseId);
  }

  const deletedExpenses = await postgresDb
    .delete(expenses)
    .where(inArray(expenses.id, expenseIds))
    .returning({ id: expenses.id });

  if (deletedExpenses.length === 0) {
    throw new Error("No expenses found for deletion.");
  }

  return {
    deletedCount: deletedExpenses.length,
    deletedIds: deletedExpenses.map(e => e.id),
  };
};

/**
 * ✅ Get expense statistics for dashboard
 */
export const getExpenseStats = async (
  requester: JwtPayload,
  startDate?: Date,
  endDate?: Date
) => {
  await authorizeAction(requester, "getAll", "expenses");

  // Build team member IDs for admin users
  let teamMemberIds: number[] = [];
  if (requester.role === "admin") {
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);
  }

  const teamIds = requester.role === "admin"
    ? [requester.id, ...teamMemberIds]
    : [requester.id];

  // Build where conditions
  const whereConditions = [inArray(expenses.createdBy, teamIds)];

  if (startDate) {
    whereConditions.push(gte(expenses.expenseDate, startDate));
  }

  if (endDate) {
    whereConditions.push(lt(expenses.expenseDate, endDate));
  }

  // Get total expenses and amount
  const totalResult = await postgresDb
    .select({
      totalAmount: sql<string>`COALESCE(SUM(CAST(${expenses.amount} AS DECIMAL)), 0)`,
      totalCount: count(),
    })
    .from(expenses)
    .where(and(...whereConditions));

  // Get expenses by category
  const categoryResult = await postgresDb
    .select({
      categoryId: expenses.categoryId,
      categoryName: expenseCategories.name,
      categoryColor: expenseCategories.color,
      totalAmount: sql<string>`COALESCE(SUM(CAST(${expenses.amount} AS DECIMAL)), 0)`,
      count: count(),
    })
    .from(expenses)
    .leftJoin(expenseCategories, eq(expenses.categoryId, expenseCategories.id))
    .where(and(...whereConditions))
    .groupBy(expenses.categoryId, expenseCategories.name, expenseCategories.color);

  return {
    totalAmount: parseFloat(totalResult[0]?.totalAmount || "0"),
    totalCount: totalResult[0]?.totalCount || 0,
    byCategory: categoryResult.map(cat => ({
      categoryId: cat.categoryId,
      categoryName: cat.categoryName || "Uncategorized",
      categoryColor: cat.categoryColor || "#6B7280",
      totalAmount: parseFloat(cat.totalAmount || "0"),
      count: cat.count,
    })),
  };
};

