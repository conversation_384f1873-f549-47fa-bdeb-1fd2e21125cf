"use client";

import React, { useState } from "react";
import { <PERSON>, But<PERSON>, Spin } from "antd";
import { UserOutlined, LoadingOutlined } from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSupplierList } from "@/hooks/suppliers/useSupplierList";
import { useSupplierDelete } from "@/hooks/suppliers/useSupplierDelete";
import { useSupplierBulkDelete } from "@/hooks/suppliers/useSupplierBulkDelete";
import SupplierTable from "@/components/Suppliers/SupplierTable";
import SupplierPagination from "@/components/Suppliers/SupplierPagination";
import SupplierFormPanel from "@/components/Suppliers/SupplierFormPanel";
import SupplierDetailPanel from "@/components/Suppliers/SupplierDetailPanel";
import SupplierSearch from "@/components/Suppliers/SupplierSearch";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { Supplier } from "@/reduxRTK/services/supplierApi";
import { UserRole } from "@/types/user";

const SuppliersPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // UI state
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [selectedSupplierId, setSelectedSupplierId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Supplier list state and handlers
  const {
    suppliers,
    total,
    page,
    limit,
    isLoading,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
  } = useSupplierList();

  // Debug output
  console.log("SuppliersPage - Current user:", currentUser);
  console.log("SuppliersPage - Suppliers:", suppliers);
  console.log("SuppliersPage - Total:", total);

  // Delete supplier handler
  const { deleteSupplier, isDeleting } = useSupplierDelete(() => {
    setIsDeleteDialogOpen(false);
    refetch();
  });

  // Bulk delete supplier handler
  const { bulkDeleteSuppliers, isDeleting: isBulkDeleting } = useSupplierBulkDelete(() => {
    setIsBulkDeleteDialogOpen(false);
    refetch();
  });

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [suppliersToDelete, setSuppliersToDelete] = useState<number[]>([]);

  // Check if user can add suppliers (only admin can add suppliers)
  const canAddSupplier = currentUser?.role === "admin";

  // Handle add supplier button click
  const handleAddSupplier = () => {
    setIsAddPanelOpen(true);
  };

  // Handle view supplier button click
  const handleViewSupplier = (supplierId: number) => {
    setSelectedSupplierId(supplierId);
    setIsDetailPanelOpen(true);
  };

  // Handle edit supplier button click
  const handleEditSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsEditPanelOpen(true);
  };

  // Handle edit from detail panel
  const handleEditFromDetail = (supplierId: number) => {
    const supplier = suppliers.find(s => s.id === supplierId) || null;
    if (supplier) {
      setSelectedSupplier(supplier);
      setIsDetailPanelOpen(false);
      setIsEditPanelOpen(true);
    }
  };

  // Handle delete supplier button click
  const handleDeleteSupplier = (supplierId: number) => {
    setSelectedSupplierId(supplierId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete supplier
  const confirmDeleteSupplier = async () => {
    if (selectedSupplierId) {
      await deleteSupplier(selectedSupplierId);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedSupplierId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (supplierIds: number[]) => {
    console.log("handleBulkDelete called with supplierIds:", supplierIds);
    setSuppliersToDelete(supplierIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with suppliers:", suppliersToDelete);

    if (suppliersToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple suppliers
        await bulkDeleteSuppliers(suppliersToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setSuppliersToDelete([]);
  };

  return (
    <div className="p-2 sm:p-4 w-full">
      <Card
        title={<span className="text-gray-800">Supplier Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: { padding: '12px', overflow: 'hidden', backgroundColor: '#ffffff' },
          header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
        }}
        extra={
          canAddSupplier && (
            <Button
              type="primary"
              icon={<UserOutlined />}
              onClick={handleAddSupplier}
              size={isMobile ? "small" : "middle"}
            >
              {isMobile ? "" : "Add Supplier"}
            </Button>
          )
        }
      >
        <div className="w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200">
          {/* Search Component - Always visible */}
          <SupplierSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex justify-center items-center h-60 bg-gray-50">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
            </div>
          ) : (
            <>
              {/* Supplier Table Component */}
              {suppliers.length > 0 ? (
                <SupplierTable
                  suppliers={suppliers}
                  loading={false}
                  onView={handleViewSupplier}
                  onEdit={handleEditSupplier}
                  onDelete={handleDeleteSupplier}
                  onBulkDelete={handleBulkDelete}
                  isMobile={isMobile}
                />
              ) : (
                <div className="flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800">
                  {searchTerm ? (
                    <>
                      <p>No suppliers found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm('')}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No suppliers found. {canAddSupplier && "Click 'Add Supplier' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {suppliers.length > 0 && (
                <SupplierPagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add Supplier Panel */}
      <SupplierFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => setIsAddPanelOpen(false)}
        onSuccess={() => {
          setIsAddPanelOpen(false);
          refetch();
        }}
        currentUser={currentUser}
      />

      {/* Edit Supplier Panel */}
      <SupplierFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);
          refetch();
        }}
        supplier={selectedSupplier}
        currentUser={currentUser}
      />

      {/* View Supplier Details Panel */}
      <SupplierDetailPanel
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false);
          setSelectedSupplierId(null);
        }}
        supplierId={selectedSupplierId}
        onEdit={handleEditFromDetail}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteSupplier}
        title="Delete Supplier"
        message="Are you sure you want to delete this supplier? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Suppliers"
        message={`Are you sure you want to delete ${suppliersToDelete.length} suppliers? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default SuppliersPage;
