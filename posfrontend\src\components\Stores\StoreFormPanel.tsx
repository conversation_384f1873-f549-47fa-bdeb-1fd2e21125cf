"use client";

import React, { useEffect } from "react";
import { Form, Input, Button } from "antd";
import {
  ShopOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  IdcardOutlined
} from "@ant-design/icons";
import { Store, CreateStoreDto, UpdateStoreDto } from "@/types/store";
import { useCreateStoreMutation, useUpdateStoreMutation, useGetAllStoresQuery } from "@/reduxRTK/services/storeApi";
import { showMessage } from "@/utils/showMessage";
import SlidingPanel from "@/components/ui/SlidingPanel";
import "./store-panels.css";

interface StoreFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  editStore?: Store | null;
  mode: "create" | "edit";
}

const StoreFormPanel: React.FC<StoreFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editStore,
  mode,
}) => {
  const [form] = Form.useForm();

  // Create and update mutations
  const [createStore, { isLoading: isCreating }] = useCreateStoreMutation();
  const [updateStore, { isLoading: isUpdating }] = useUpdateStoreMutation();
  const { refetch: refetchStores } = useGetAllStoresQuery({ page: 1, limit: 1000 }, { skip: true });

  // Set form values when editing
  useEffect(() => {
    if (isOpen && mode === "edit" && editStore) {
      form.setFieldsValue({
        name: editStore.name,
        address: editStore.address,
        city: editStore.city,
        state: editStore.state,
        country: editStore.country,
        phone: editStore.phone,
        email: editStore.email,
        website: editStore.website,
        taxId: editStore.taxId,
      });
    } else if (isOpen && mode === "create") {
      form.resetFields();
    }
  }, [isOpen, mode, editStore, form]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (mode === "create") {
        // Create new store
        const storeData: CreateStoreDto = {
          name: values.name,
          address: values.address,
          city: values.city,
          state: values.state,
          country: values.country,
          phone: values.phone,
          email: values.email,
          website: values.website,
          taxId: values.taxId,
        };

        const response = await createStore(storeData).unwrap();

        if (response.success) {
          showMessage("success", "Store created successfully");
          form.resetFields();
          // Wait 500ms to ensure backend transaction is complete
          setTimeout(() => {
            onClose();
            if (onSuccess) onSuccess();
            refetchStores();
          }, 500);
        } else {
          showMessage("error", response.message || "Failed to create store");
        }
      } else if (mode === "edit" && editStore) {
        // Update existing store
        const updateData: UpdateStoreDto = {
          name: values.name,
          address: values.address,
          city: values.city,
          state: values.state,
          country: values.country,
          phone: values.phone,
          email: values.email,
          website: values.website,
          taxId: values.taxId,
        };

        const response = await updateStore({
          storeId: editStore.id,
          updateData,
        }).unwrap();

        if (response.success) {
          showMessage("success", "Store updated successfully");
          onClose();
          if (onSuccess) onSuccess();
        } else {
          showMessage("error", response.message || "Failed to update store");
        }
      }
    } catch (error: any) {
      showMessage(
        "error",
        error.data?.message || "An error occurred while saving the store"
      );
    }
  };

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ background: '#f5f5f5', borderColor: '#d9d9d9' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        onClick={() => form.submit()}
        loading={isCreating || isUpdating}
        icon={<ShopOutlined />}
        className="bg-blue-600 hover:bg-blue-700 border-blue-600"
      >
        {mode === "create" ? "Create Store" : "Update Store"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      title={mode === "create" ? "Create Store" : "Edit Store"}
      isOpen={isOpen}
      onClose={onClose}
      width="500px"
      footer={panelFooter}
    >
      <div className="p-6 bg-white">
        <Form
          form={form}
          layout="vertical"
          className="store-form"
          onFinish={handleSubmit}
          requiredMark={true}
        >
          {/* Basic Information Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Store Information
            </h3>

            <Form.Item
              name="name"
              label={<span className="flex items-center"><ShopOutlined className="mr-1" /> Store Name</span>}
              rules={[{ required: true, message: "Please enter the store name" }]}
              tooltip="Name of your store or business"
            >
              <Input placeholder="Enter store name" />
            </Form.Item>

            <Form.Item
              name="address"
              label={<span className="flex items-center"><EnvironmentOutlined className="mr-1" /> Address</span>}
              tooltip="Physical address of your store"
            >
              <Input.TextArea
                rows={2}
                placeholder="Enter store address"
              />
            </Form.Item>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Form.Item
                name="city"
                label={<span className="flex items-center"><EnvironmentOutlined className="mr-1" /> City</span>}
              >
                <Input placeholder="Enter city" />
              </Form.Item>

              <Form.Item
                name="state"
                label={<span className="flex items-center"><EnvironmentOutlined className="mr-1" /> State/Province</span>}
              >
                <Input placeholder="Enter state/province" />
              </Form.Item>

              <Form.Item
                name="country"
                label={<span className="flex items-center"><EnvironmentOutlined className="mr-1" /> Country</span>}
              >
                <Input placeholder="Enter country" />
              </Form.Item>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                name="phone"
                label={<span className="flex items-center"><PhoneOutlined className="mr-1" /> Phone</span>}
                tooltip="Contact phone number for the store"
              >
                <Input placeholder="Enter phone number" />
              </Form.Item>

              <Form.Item
                name="email"
                label={<span className="flex items-center"><MailOutlined className="mr-1" /> Email</span>}
                tooltip="Contact email for the store"
                rules={[
                  {
                    type: "email",
                    message: "Please enter a valid email address",
                  },
                ]}
              >
                <Input placeholder="Enter email address" />
              </Form.Item>
            </div>

            <Form.Item
              name="website"
              label={<span className="flex items-center"><GlobalOutlined className="mr-1" /> Website</span>}
              tooltip="Store website URL (if available)"
            >
              <Input placeholder="Enter website URL" />
            </Form.Item>

            <Form.Item
              name="taxId"
              label={<span className="flex items-center"><IdcardOutlined className="mr-1" /> Tax ID / Business Registration</span>}
              tooltip="Tax ID or business registration number"
            >
              <Input placeholder="Enter tax ID or registration number" />
            </Form.Item>
          </div>
        </Form>
      </div>
    </SlidingPanel>
  );
};

export default StoreFormPanel;
