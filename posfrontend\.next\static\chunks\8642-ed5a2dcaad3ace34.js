"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8642],{97139:(t,e,n)=>{n.d(e,{A:()=>l});var r=n(85407),o=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var a=n(84021);let l=o.forwardRef(function(t,e){return o.createElement(a.A,(0,r.A)({},t,{ref:e,icon:i}))})},53452:(t,e,n)=>{n.d(e,{A:()=>l});var r=n(85407),o=n(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z"}}]},name:"printer",theme:"outlined"};var a=n(84021);let l=o.forwardRef(function(t,e){return o.createElement(a.A,(0,r.A)({},t,{ref:e,icon:i}))})},47537:(t,e,n)=>{n.d(e,{$:()=>tl,a8:()=>ts});var r={},o=function(t,e,n,o,i){var a=new Worker(r[e]||(r[e]=URL.createObjectURL(new Blob([t+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return a.onmessage=function(t){var e=t.data,n=e.$e$;if(n){var r=Error(n[0]);r.code=n[1],r.stack=n[2],i(r,null)}else i(null,e)},a.postMessage(n,o),a},i=Uint8Array,a=Uint16Array,l=Int32Array,s=new i([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),u=new i([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),h=new i([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),f=function(t,e){for(var n=new a(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];for(var o=new l(n[30]),r=1;r<30;++r)for(var i=n[r];i<n[r+1];++i)o[i]=i-n[r]<<5|r;return{b:n,r:o}},c=f(s,2),d=c.b,g=c.r;d[28]=258,g[258]=28;for(var p=f(u,0),v=p.b,y=p.r,m=new a(32768),w=0;w<32768;++w){var b=(43690&w)>>1|(21845&w)<<1;b=(61680&(b=(52428&b)>>2|(13107&b)<<2))>>4|(3855&b)<<4,m[w]=((65280&b)>>8|(255&b)<<8)>>1}for(var x=function(t,e,n){for(var r,o=t.length,i=0,l=new a(e);i<o;++i)t[i]&&++l[t[i]-1];var s=new a(e);for(i=1;i<e;++i)s[i]=s[i-1]+l[i-1]<<1;if(n){r=new a(1<<e);var u=15-e;for(i=0;i<o;++i)if(t[i])for(var h=i<<4|t[i],f=e-t[i],c=s[t[i]-1]++<<f,d=c|(1<<f)-1;c<=d;++c)r[m[c]>>u]=h}else for(i=0,r=new a(o);i<o;++i)t[i]&&(r[i]=m[s[t[i]-1]++]>>15-t[i]);return r},S=new i(288),w=0;w<144;++w)S[w]=8;for(var w=144;w<256;++w)S[w]=9;for(var w=256;w<280;++w)S[w]=7;for(var w=280;w<288;++w)S[w]=8;for(var W=new i(32),w=0;w<32;++w)W[w]=5;var P=x(S,9,0),D=x(S,9,1),C=x(W,5,0),F=x(W,5,1),H=function(t){for(var e=t[0],n=1;n<t.length;++n)t[n]>e&&(e=t[n]);return e},k=function(t,e,n){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(7&e)&n},z=function(t,e){var n=e/8|0;return(t[n]|t[n+1]<<8|t[n+2]<<16)>>(7&e)},j=function(t){return(t+7)/8|0},A=function(t,e,n){return(null==e||e<0)&&(e=0),(null==n||n>t.length)&&(n=t.length),new i(t.subarray(e,n))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],T=function(t,e,n){var r=Error(e||M[t]);if(r.code=t,Error.captureStackTrace&&Error.captureStackTrace(r,T),!n)throw r;return r},R=function(t,e,n,r){var o=t.length,a=r?r.length:0;if(!o||e.f&&!e.l)return n||new i(0);var l=!n,f=l||2!=e.i,c=e.i;l&&(n=new i(3*o));var g=function(t){var e=n.length;if(t>e){var r=new i(Math.max(2*e,t));r.set(n),n=r}},p=e.f||0,y=e.p||0,m=e.b||0,w=e.l,b=e.d,S=e.m,W=e.n,P=8*o;do{if(!w){p=k(t,y,1);var C=k(t,y+1,3);if(y+=3,C){if(1==C)w=D,b=F,S=9,W=5;else if(2==C){var M=k(t,y,31)+257,R=k(t,y+10,15)+4,E=M+k(t,y+5,31)+1;y+=14;for(var L=new i(E),B=new i(19),N=0;N<R;++N)B[h[N]]=k(t,y+3*N,7);y+=3*R;for(var O=H(B),_=(1<<O)-1,I=x(B,O,1),N=0;N<E;){var Y=I[k(t,y,_)];y+=15&Y;var V=Y>>4;if(V<16)L[N++]=V;else{var K=0,U=0;for(16==V?(U=3+k(t,y,3),y+=2,K=L[N-1]):17==V?(U=3+k(t,y,7),y+=3):18==V&&(U=11+k(t,y,127),y+=7);U--;)L[N++]=K}}var $=L.subarray(0,M),q=L.subarray(M);S=H($),W=H(q),w=x($,S,1),b=x(q,W,1)}else T(1)}else{var V=j(y)+4,G=t[V-4]|t[V-3]<<8,J=V+G;if(J>o){c&&T(0);break}f&&g(m+G),n.set(t.subarray(V,J),m),e.b=m+=G,e.p=y=8*J,e.f=p;continue}if(y>P){c&&T(0);break}}f&&g(m+131072);for(var Q=(1<<S)-1,X=(1<<W)-1,Z=y;;Z=y){var K=w[z(t,y)&Q],tt=K>>4;if((y+=15&K)>P){c&&T(0);break}if(K||T(2),tt<256)n[m++]=tt;else if(256==tt){Z=y,w=null;break}else{var te=tt-254;if(tt>264){var N=tt-257,tn=s[N];te=k(t,y,(1<<tn)-1)+d[N],y+=tn}var tr=b[z(t,y)&X],to=tr>>4;tr||T(3),y+=15&tr;var q=v[to];if(to>3){var tn=u[to];q+=z(t,y)&(1<<tn)-1,y+=tn}if(y>P){c&&T(0);break}f&&g(m+131072);var ti=m+te;if(m<q){var ta=a-q,tl=Math.min(q,ti);for(ta+m<0&&T(3);m<tl;++m)n[m]=r[ta+m]}for(;m<ti;++m)n[m]=n[m-q]}}e.l=w,e.p=Z,e.b=m,e.f=p,w&&(p=1,e.m=S,e.d=b,e.n=W)}while(!p);return m!=n.length&&l?A(n,0,m):n.subarray(0,m)},E=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8},L=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8,t[r+2]|=n>>16},B=function(t,e){for(var n=[],r=0;r<t.length;++r)t[r]&&n.push({s:r,f:t[r]});var o=n.length,l=n.slice();if(!o)return{t:K,l:0};if(1==o){var s=new i(n[0].s+1);return s[n[0].s]=1,{t:s,l:1}}n.sort(function(t,e){return t.f-e.f}),n.push({s:-1,f:25001});var u=n[0],h=n[1],f=0,c=1,d=2;for(n[0]={s:-1,f:u.f+h.f,l:u,r:h};c!=o-1;)u=n[n[f].f<n[d].f?f++:d++],h=n[f!=c&&n[f].f<n[d].f?f++:d++],n[c++]={s:-1,f:u.f+h.f,l:u,r:h};for(var g=l[0].s,r=1;r<o;++r)l[r].s>g&&(g=l[r].s);var p=new a(g+1),v=N(n[c-1],p,0);if(v>e){var r=0,y=0,m=v-e,w=1<<m;for(l.sort(function(t,e){return p[e.s]-p[t.s]||t.f-e.f});r<o;++r){var b=l[r].s;if(p[b]>e)y+=w-(1<<v-p[b]),p[b]=e;else break}for(y>>=m;y>0;){var x=l[r].s;p[x]<e?y-=1<<e-p[x]++-1:++r}for(;r>=0&&y;--r){var S=l[r].s;p[S]==e&&(--p[S],++y)}v=e}return{t:new i(p),l:v}},N=function(t,e,n){return -1==t.s?Math.max(N(t.l,e,n+1),N(t.r,e,n+1)):e[t.s]=n},O=function(t){for(var e=t.length;e&&!t[--e];);for(var n=new a(++e),r=0,o=t[0],i=1,l=function(t){n[r++]=t},s=1;s<=e;++s)if(t[s]==o&&s!=e)++i;else{if(!o&&i>2){for(;i>138;i-=138)l(32754);i>2&&(l(i>10?i-11<<5|28690:i-3<<5|12305),i=0)}else if(i>3){for(l(o),--i;i>6;i-=6)l(8304);i>2&&(l(i-3<<5|8208),i=0)}for(;i--;)l(o);i=1,o=t[s]}return{c:n.subarray(0,r),n:e}},_=function(t,e){for(var n=0,r=0;r<e.length;++r)n+=t[r]*e[r];return n},I=function(t,e,n){var r=n.length,o=j(e+2);t[o]=255&r,t[o+1]=r>>8,t[o+2]=255^t[o],t[o+3]=255^t[o+1];for(var i=0;i<r;++i)t[o+i+4]=n[i];return(o+4+r)*8},Y=function(t,e,n,r,o,i,l,f,c,d,g){E(e,g++,n),++o[256];for(var p,v,y,m,w=B(o,15),b=w.t,D=w.l,F=B(i,15),H=F.t,k=F.l,z=O(b),j=z.c,A=z.n,M=O(H),T=M.c,R=M.n,N=new a(19),Y=0;Y<j.length;++Y)++N[31&j[Y]];for(var Y=0;Y<T.length;++Y)++N[31&T[Y]];for(var V=B(N,7),K=V.t,U=V.l,$=19;$>4&&!K[h[$-1]];--$);var q=d+5<<3,G=_(o,S)+_(i,W)+l,J=_(o,b)+_(i,H)+l+14+3*$+_(N,K)+2*N[16]+3*N[17]+7*N[18];if(c>=0&&q<=G&&q<=J)return I(e,g,t.subarray(c,c+d));if(E(e,g,1+(J<G)),g+=2,J<G){p=x(b,D,0),v=b,y=x(H,k,0),m=H;var Q=x(K,U,0);E(e,g,A-257),E(e,g+5,R-1),E(e,g+10,$-4),g+=14;for(var Y=0;Y<$;++Y)E(e,g+3*Y,K[h[Y]]);g+=3*$;for(var X=[j,T],Z=0;Z<2;++Z)for(var tt=X[Z],Y=0;Y<tt.length;++Y){var te=31&tt[Y];E(e,g,Q[te]),g+=K[te],te>15&&(E(e,g,tt[Y]>>5&127),g+=tt[Y]>>12)}}else p=P,v=S,y=C,m=W;for(var Y=0;Y<f;++Y){var tn=r[Y];if(tn>255){var te=tn>>18&31;L(e,g,p[te+257]),g+=v[te+257],te>7&&(E(e,g,tn>>23&31),g+=s[te]);var tr=31&tn;L(e,g,y[tr]),g+=m[tr],tr>3&&(L(e,g,tn>>5&8191),g+=u[tr])}else L(e,g,p[tn]),g+=v[tn]}return L(e,g,p[256]),g+v[256]},V=new l([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),K=new i(0),U=function(t,e,n,r,o,h){var f=h.z||t.length,c=new i(r+f+5*(1+Math.ceil(f/7e3))+o),d=c.subarray(r,c.length-o),p=h.l,v=7&(h.r||0);if(e){v&&(d[0]=h.r>>3);for(var m=V[e-1],w=m>>13,b=8191&m,x=(1<<n)-1,S=h.p||new a(32768),W=h.h||new a(x+1),P=Math.ceil(n/3),D=2*P,C=function(e){return(t[e]^t[e+1]<<P^t[e+2]<<D)&x},F=new l(25e3),H=new a(288),k=new a(32),z=0,M=0,T=h.i||0,R=0,E=h.w||0,L=0;T+2<f;++T){var B=C(T),N=32767&T,O=W[B];if(S[N]=O,W[B]=N,E<=T){var _=f-T;if((z>7e3||R>24576)&&(_>423||!p)){v=Y(t,d,0,F,H,k,M,R,L,T-L,v),R=z=M=0,L=T;for(var K=0;K<286;++K)H[K]=0;for(var K=0;K<30;++K)k[K]=0}var U=2,$=0,q=b,G=N-O&32767;if(_>2&&B==C(T-G))for(var J=Math.min(w,_)-1,Q=Math.min(32767,T),X=Math.min(258,_);G<=Q&&--q&&N!=O;){if(t[T+U]==t[T+U-G]){for(var Z=0;Z<X&&t[T+Z]==t[T+Z-G];++Z);if(Z>U){if(U=Z,$=G,Z>J)break;for(var tt=Math.min(G,Z-2),te=0,K=0;K<tt;++K){var tn=T-G+K&32767,tr=S[tn],to=tn-tr&32767;to>te&&(te=to,O=tn)}}}O=S[N=O],G+=N-O&32767}if($){F[R++]=0x10000000|g[U]<<18|y[$];var ti=31&g[U],ta=31&y[$];M+=s[ti]+u[ta],++H[257+ti],++k[ta],E=T+U,++z}else F[R++]=t[T],++H[t[T]]}}for(T=Math.max(T,E);T<f;++T)F[R++]=t[T],++H[t[T]];v=Y(t,d,p,F,H,k,M,R,L,T-L,v),p||(h.r=7&v|d[v/8|0]<<3,v-=7,h.h=W,h.p=S,h.i=T,h.w=E)}else{for(var T=h.w||0;T<f+p;T+=65535){var tl=T+65535;tl>=f&&(d[v/8|0]=p,tl=f),v=I(d,v+1,t.subarray(T,tl))}h.i=f}return A(c,0,r+j(v)+o)},$=function(){var t=1,e=0;return{p:function(n){for(var r=t,o=e,i=0|n.length,a=0;a!=i;){for(var l=Math.min(a+2655,i);a<l;++a)o+=r+=n[a];r=(65535&r)+15*(r>>16),o=(65535&o)+15*(o>>16)}t=r,e=o},d:function(){return t%=65521,e%=65521,(255&t)<<24|(65280&t)<<8|(255&e)<<8|e>>8}}},q=function(t,e,n,r,o){if(!o&&(o={l:1},e.dictionary)){var a=e.dictionary.subarray(-32768),l=new i(a.length+t.length);l.set(a),l.set(t,a.length),t=l,o.w=a.length}return U(t,null==e.level?6:e.level,null==e.mem?o.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):20:12+e.mem,n,r,o)},G=function(t,e){var n={};for(var r in t)n[r]=t[r];for(var r in e)n[r]=e[r];return n},J=function(t,e,n){for(var r=t(),o=t.toString(),i=o.slice(o.indexOf("[")+1,o.lastIndexOf("]")).replace(/\s+/g,"").split(","),a=0;a<r.length;++a){var l=r[a],s=i[a];if("function"==typeof l){e+=";"+s+"=";var u=l.toString();if(l.prototype){if(-1!=u.indexOf("[native code]")){var h=u.indexOf(" ",8)+1;e+=u.slice(h,u.indexOf("(",h))}else for(var f in e+=u,l.prototype)e+=";"+s+".prototype."+f+"="+l.prototype[f].toString()}else e+=u}else n[s]=l}return e},Q=function(t){var e=[];for(var n in t)t[n].buffer&&e.push((t[n]=new t[n].constructor(t[n])).buffer);return e},X=function(t,e,n,r){if(!null[n]){for(var i="",a={},l=t.length-1,s=0;s<l;++s)i=J(t[s],i,a);null[n]={c:J(t[l],i,a),e:a}}var u=G({},null[n].e);return o(null[n].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",n,u,Q(u),r)},Z=function(t){return postMessage(t,[t.buffer])},tt=function(t){return t&&{out:t.size&&new i(t.size),dictionary:t.dictionary}},te=function(t,e){return t[e]|t[e+1]<<8},tn=function(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0},tr=function(t,e){return tn(t,e)+0x100000000*tn(t,e+4)},to=function(t,e,n){for(;n;++e)t[e]=n,n>>>=8},ti=function(t,e){var n=e.level;if(t[0]=120,t[1]=(0==n?0:n<6?1:9==n?3:2)<<6|(e.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,e.dictionary){var r=$();r.p(e.dictionary),to(t,2,r.d())}};function ta(t,e){return R(t,{i:2},e&&e.out,e&&e.dictionary)}function tl(t,e){e||(e={});var n=$();n.p(t);var r=q(t,e,e.dictionary?6:2,4);return ti(r,e),to(r,r.length-4,n.d()),r}function ts(t,e){var n,r;return R(t.subarray((n=t,r=e&&e.dictionary,((15&n[0])!=8||n[0]>>4>7||(n[0]<<8|n[1])%31)&&T(6,"invalid zlib data"),(n[1]>>5&1)==+!r&&T(6,"invalid zlib data: "+(32&n[1]?"need":"unexpected")+" dictionary"),(n[1]>>3&4)+2),-4),{i:2},e&&e.out,e&&e.dictionary)}var tu="undefined"!=typeof TextDecoder&&new TextDecoder;try{tu.decode(K,{stream:!0})}catch(t){}"function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout&&setTimeout},42531:(t,e,n)=>{function r(t,e,n,r,o){r=r||{};var i=o.internal.scaleFactor,a=o.internal.getFontSize()/i,l=a*(o.getLineHeightFactor?o.getLineHeightFactor():1.15),s="",u=1;if(("middle"===r.valign||"bottom"===r.valign||"center"===r.halign||"right"===r.halign)&&(u=(s="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=.8500000000000001*a,"middle"===r.valign?n-=u/2*l:"bottom"===r.valign&&(n-=u*l),"center"===r.halign||"right"===r.halign){var h=a;if("center"===r.halign&&(h*=.5),s&&u>=1){for(var f=0;f<s.length;f++)o.text(s[f],e-o.getStringUnitWidth(s[f])*h,n),n+=l;return o}e-=o.getStringUnitWidth(t)*h}return"justify"===r.halign?o.text(t,e,n,{maxWidth:r.maxWidth||100,align:"justify"}):o.text(t,e,n),o}n.d(e,{Ay:()=>N});var o,i={},a=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:i=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var r,o,i,a=this.jsPDFDocument.internal.getFont(),l=a.fontStyle,s=a.fontName;if(e.font&&(s=e.font),e.fontStyle){l=e.fontStyle;var u=this.getFontList()[s];u&&-1===u.indexOf(l)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(u[0]),l=u[0])}if(this.jsPDFDocument.setFont(s,l),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var h=t.unifyColor(e.fillColor);h&&(r=this.jsPDFDocument).setFillColor.apply(r,h),(h=t.unifyColor(e.textColor))&&(o=this.jsPDFDocument).setTextColor.apply(o,h),(h=t.unifyColor(e.lineColor))&&(i=this.jsPDFDocument).setDrawColor.apply(i,h),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,r,o){return this.jsPDFDocument.rect(t,e,n,r,o)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return i||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}(),l=function(t,e){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function s(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}"function"==typeof SuppressedError&&SuppressedError;var u=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return s(e,t),e}(Array);function h(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map(function(t){return n.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0)}function f(t,e,n,r){var o=e.settings.tableLineWidth,i=e.settings.tableLineColor;t.applyStyles({lineWidth:o,lineColor:i});var a=c(o,!1);a&&t.rect(n.x,n.y,e.getWidth(t.pageSize().width),r.y-n.y,a)}function c(t,e){var n=t>0,r=e||0===e;return n&&r?"DF":n?"S":r?"F":null}function d(t,e){var n,r,o,i;if(Array.isArray(t=t||e)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(r=t.top)&&void 0!==r?r:e,right:null!==(o=t.right)&&void 0!==o?o:e,bottom:null!==(i=t.bottom)&&void 0!==i?i:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}function g(t,e){var n=d(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}function p(t,e){var n=function t(e,n){var r=n(e);return"rgba(0, 0, 0, 0)"!==r&&"transparent"!==r&&"initial"!==r&&"inherit"!==r?r:null==e.parentElement?null:t(e.parentElement,n)}(t,e);if(!n)return null;var r=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!r||!Array.isArray(r))return null;var o=[parseInt(r[1]),parseInt(r[2]),parseInt(r[3])];return 0===parseInt(r[4])||isNaN(o[0])||isNaN(o[1])||isNaN(o[2])?null:o}function v(t,e,n,r,o){void 0===r&&(r=!1),void 0===o&&(o=!1),l="string"==typeof e?n.document.querySelector(e):e;var i,a,l,s=Object.keys(t.getFontList()),h=t.scaleFactor(),f=[],c=[],g=[];if(!l)return console.error("Html table could not be found with input: ",e),{head:f,body:c,foot:g};for(var v=0;v<l.rows.length;v++){var y=l.rows[v],m=null===(a=null===(i=null==y?void 0:y.parentElement)||void 0===i?void 0:i.tagName)||void 0===a?void 0:a.toLowerCase(),w=function(t,e,n,r,o,i){for(var a=new u(r),l=0;l<r.cells.length;l++){var s=r.cells[l],h=n.getComputedStyle(s);if(o||"none"!==h.display){var f=void 0;i&&(f=function(t,e,n,r,o){var i={},a=96/72,l=p(e,function(t){return o.getComputedStyle(t).backgroundColor});null!=l&&(i.fillColor=l);var s=p(e,function(t){return o.getComputedStyle(t).color});null!=s&&(i.textColor=s);var u=function(t,e){var n=[t.paddingTop,t.paddingRight,t.paddingBottom,t.paddingLeft],r=96/(72/e),o=(parseInt(t.lineHeight)-parseInt(t.fontSize))/e/2,i=d(n.map(function(t){return parseInt(t||"0")/r}),0);return o>i.top&&(i.top=o),o>i.bottom&&(i.bottom=o),i}(r,n);u&&(i.cellPadding=u);var h="borderTopColor",f=a*n,c=r.borderTopWidth;if(r.borderBottomWidth===c&&r.borderRightWidth===c&&r.borderLeftWidth===c){var g=(parseFloat(c)||0)/f;g&&(i.lineWidth=g)}else i.lineWidth={top:(parseFloat(r.borderTopWidth)||0)/f,right:(parseFloat(r.borderRightWidth)||0)/f,bottom:(parseFloat(r.borderBottomWidth)||0)/f,left:(parseFloat(r.borderLeftWidth)||0)/f},!i.lineWidth.top&&(i.lineWidth.right?h="borderRightColor":i.lineWidth.bottom?h="borderBottomColor":i.lineWidth.left&&(h="borderLeftColor"));var v=p(e,function(t){return o.getComputedStyle(t)[h]});null!=v&&(i.lineColor=v);var y=["left","right","center","justify"];-1!==y.indexOf(r.textAlign)&&(i.halign=r.textAlign),-1!==(y=["middle","bottom","top"]).indexOf(r.verticalAlign)&&(i.valign=r.verticalAlign);var m=parseInt(r.fontSize||"");isNaN(m)||(i.fontSize=m/a);var w=function(t){var e="";return("bold"===t.fontWeight||"bolder"===t.fontWeight||parseInt(t.fontWeight)>=700)&&(e="bold"),("italic"===t.fontStyle||"oblique"===t.fontStyle)&&(e+="italic"),e}(r);w&&(i.fontStyle=w);var b=(r.fontFamily||"").toLowerCase();return -1!==t.indexOf(b)&&(i.font=b),i}(t,s,e,h,n)),a.push({rowSpan:s.rowSpan,colSpan:s.colSpan,styles:f,_element:s,content:function(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map(function(t){return t.trim()}).join("\n"),e.innerText||e.textContent||""}(s)})}}var c=n.getComputedStyle(r);if(a.length>0&&(o||"none"!==c.display))return a}(s,h,n,y,r,o);w&&("thead"===m?f.push(w):"tfoot"===m?g.push(w):c.push(w))}return{head:f,body:c,foot:g}}function y(t,e,n,r,o){if(null==t)throw TypeError("Cannot convert undefined or null to object");for(var i=Object(t),a=1;a<arguments.length;a++){var l=arguments[a];if(null!=l)for(var s in l)Object.prototype.hasOwnProperty.call(l,s)&&(i[s]=l[s])}return i}function m(t,e){var n,r,o,i,l,s,u,h,f,c,g,p,m,w,b,x,S,W,P,D,C,F,H,k,z,j,A=new a(t),M=A.getDocumentOptions(),T=A.getGlobalOptions();!function(t,e,n){for(var r=0,o=[t,e,n];r<o.length;r++){var i=o[r];i&&"object"!=typeof i&&console.error("The options parameter should be of type object, is: "+typeof i),i.startY&&"number"!=typeof i.startY&&(console.error("Invalid value for startY option",i.startY),delete i.startY)}}(T,M,e);var R=y({},T,M,e);"undefined"!=typeof window&&(j=window);var E=function(t,e,n){for(var r={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},o=function(o){if("columnStyles"===o){var i=t[o],a=e[o],l=n[o];r.columnStyles=y({},i,a,l)}else{var s=[t,e,n].map(function(t){return t[o]||{}});r[o]=y({},s[0],s[1],s[2])}},i=0,a=Object.keys(r);i<a.length;i++)o(a[i]);return r}(T,M,e),L=function(t,e,n){for(var r={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},o=0,i=[t,e,n];o<i.length;o++){var a=i[o];a.didParseCell&&r.didParseCell.push(a.didParseCell),a.willDrawCell&&r.willDrawCell.push(a.willDrawCell),a.didDrawCell&&r.didDrawCell.push(a.didDrawCell),a.willDrawPage&&r.willDrawPage.push(a.willDrawPage),a.didDrawPage&&r.didDrawPage.push(a.didDrawPage)}return r}(T,M,e),B=(D=d(R.margin,40/A.scaleFactor()),C=null!==(n=R.startY,r=A.getLastAutoTable(),o=A.scaleFactor(),i=A.pageNumber(),l=!1,r&&r.startPageNumber&&(l=r.startPageNumber+r.pageNumber-1===i),s="number"==typeof n?n:(null==n||!1===n)&&l&&(null==r?void 0:r.finalY)!=null?r.finalY+20/o:null)&&void 0!==s?s:D.top,W=!0===R.showFoot?"everyPage":!1===R.showFoot?"never":null!==(u=R.showFoot)&&void 0!==u?u:"everyPage",P=!0===R.showHead?"everyPage":!1===R.showHead?"never":null!==(h=R.showHead)&&void 0!==h?h:"everyPage",F=null!==(f=R.useCss)&&void 0!==f&&f,H=R.theme||(F?"plain":"striped"),k=!!R.horizontalPageBreak,z=null!==(c=R.horizontalPageBreakRepeat)&&void 0!==c?c:null,{includeHiddenHtml:null!==(g=R.includeHiddenHtml)&&void 0!==g&&g,useCss:F,theme:H,startY:C,margin:D,pageBreak:null!==(p=R.pageBreak)&&void 0!==p?p:"auto",rowPageBreak:null!==(m=R.rowPageBreak)&&void 0!==m?m:"auto",tableWidth:null!==(w=R.tableWidth)&&void 0!==w?w:"auto",showHead:P,showFoot:W,tableLineWidth:null!==(b=R.tableLineWidth)&&void 0!==b?b:0,tableLineColor:null!==(x=R.tableLineColor)&&void 0!==x?x:200,horizontalPageBreak:k,horizontalPageBreakRepeat:z,horizontalPageBreakBehaviour:null!==(S=R.horizontalPageBreakBehaviour)&&void 0!==S?S:"afterAllRows"}),N=function(t,e,n){var r,o,i,a,l,s=e.head||[],u=e.body||[],h=e.foot||[];if(e.html){var f=e.includeHiddenHtml;if(n){var c=v(t,e.html,n,f,e.useCss)||{};s=c.head||s,u=c.body||s,h=c.foot||s}else console.error("Cannot parse html in non browser environment")}return{columns:e.columns||(r=s,o=u,i=h,a=r[0]||o[0]||i[0]||[],l=[],Object.keys(a).filter(function(t){return"_element"!==t}).forEach(function(t){var e,n=1;"object"!=typeof(e=Array.isArray(a)?a[parseInt(t)]:a[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var r=0;r<n;r++){var o={dataKey:Array.isArray(a)?l.length:t+(r>0?"_".concat(r):"")};l.push(o)}}),l),head:s,body:u,foot:h}}(A,R,j);return{id:e.tableId,content:N,hooks:L,styles:E,settings:B}}var w=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()},b=function(t){function e(e,n,r,o,i,a){var l=t.call(this,e,n,a)||this;return l.cell=r,l.row=o,l.column=i,l.section=o.section,l}return s(e,t),e}(w),x=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,r,o,i){for(var a=0;a<e.length;a++){var l=!1===(0,e[a])(new b(t,this,n,r,o,i));if(n.text=Array.isArray(n.text)?n.text:[n.text],l)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,r=this.hooks.didDrawPage;n<r.length;n++)(0,r[n])(new w(t,this,e))},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,r=this.hooks.willDrawPage;n<r.length;n++)(0,r[n])(new w(t,this,e))},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(t,e){return t+e.wrappedWidth},0);var e=this.settings.margin;return t-e.left-e.right},t}(),S=function(){function t(t,e,n,r,o){void 0===o&&(o=!1),this.height=0,this.raw=t,t instanceof u&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=r,this.spansMultiplePages=o}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(t,n){var r;return Math.max(t,(null===(r=e.cells[n.index])||void 0===r?void 0:r.height)||0)},0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1}).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(t,r){var o=n.cells[r.index];if(!o)return 0;var i=e.getLineHeight(o.styles.fontSize),a=o.padding("vertical")+i;return a>t?a:t},0)},t}(),W=function(){function t(t,e,n){this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var r,o=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,o=null!==(r=t.content)&&void 0!==r?r:t,t._element&&(this.raw=t._element));var i=null!=o?""+o:"";this.text=i.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var t,e,n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var r=this.width-this.padding("horizontal");e=this.x+r/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){return void 0===e&&(e=1.15),Math.max((Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical"),this.styles.minCellHeight)},t.prototype.padding=function(t){var e=d(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}(),P=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,r=t.allRows();n<r.length;n++){var o=r[n].cells[this.index];o&&"number"==typeof o.styles.cellWidth&&(e=Math.max(e,o.styles.cellWidth))}return e},t}();function D(t,e,n){for(var r=e,o=t.reduce(function(t,e){return t+e.wrappedWidth},0),i=0;i<t.length;i++){var a=t[i],l=r*(a.wrappedWidth/o),s=a.width+l,u=n(a),h=s<u?u:s;e-=h-a.width,a.width=h}if(e=Math.round(1e10*e)/1e10){var f=t.filter(function(t){return!(e<0)||t.width>n(t)});f.length&&(e=D(f,e,n))}return e}function C(t,e,n,r,o){return t.map(function(t){return function(t,e,n,r,o){var i=1e4*r.scaleFactor();if((e=Math.ceil(e*i)/i)>=h(t,n,r))return t;for(;e<h(t+o,n,r)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+o}(t,e,n,r,o)})}function F(t,e){var n=new a(t),r=function(t,e){var n=t.content,r=n.columns.map(function(t,e){var n;return new P("object"==typeof t&&null!==(n=t.dataKey)&&void 0!==n?n:e,t,e)});if(0===n.head.length){var o=k(r,"head");o&&n.head.push(o)}if(0===n.foot.length){var o=k(r,"foot");o&&n.foot.push(o)}var i=t.settings.theme,a=t.styles;return{columns:r,head:H("head",n.head,r,a,i,e),body:H("body",n.body,r,a,i,e),foot:H("foot",n.foot,r,a,i,e)}}(e,n.scaleFactor()),o=new x(e,r);return!function(t,e){n=t.scaleFactor(),r=e.settings.horizontalPageBreak,o=g(t,e),e.allRows().forEach(function(i){for(var a=0,l=e.columns;a<l.length;a++){var s=l[a],u=i.cells[s.index];if(u){var f=e.hooks.didParseCell;e.callCellHooks(t,f,u,i,s,null);var c=u.padding("horizontal");u.contentWidth=h(u.text,u.styles,t)+c;var d=h(u.text.join(" ").split(/[^\S\u00A0]+/),u.styles,t);if(u.minReadableWidth=d+u.padding("horizontal"),"number"==typeof u.styles.cellWidth)u.minWidth=u.styles.cellWidth,u.wrappedWidth=u.styles.cellWidth;else if("wrap"===u.styles.cellWidth||!0===r)u.contentWidth>o?(u.minWidth=o,u.wrappedWidth=o):(u.minWidth=u.contentWidth,u.wrappedWidth=u.contentWidth);else{var g=10/n;u.minWidth=u.styles.minCellWidth||g,u.wrappedWidth=u.contentWidth,u.minWidth>u.wrappedWidth&&(u.wrappedWidth=u.minWidth)}}}}),e.allRows().forEach(function(t){for(var n=0,r=e.columns;n<r.length;n++){var o=r[n],i=t.cells[o.index];if(i&&1===i.colSpan)o.wrappedWidth=Math.max(o.wrappedWidth,i.wrappedWidth),o.minWidth=Math.max(o.minWidth,i.minWidth),o.minReadableWidth=Math.max(o.minReadableWidth,i.minReadableWidth);else{var a=e.styles.columnStyles[o.dataKey]||e.styles.columnStyles[o.index]||{},l=a.cellWidth||a.minCellWidth;l&&"number"==typeof l&&(o.minWidth=l,o.wrappedWidth=l)}i&&(i.colSpan>1&&!o.minWidth&&(o.minWidth=i.minWidth),i.colSpan>1&&!o.wrappedWidth&&(o.wrappedWidth=i.minWidth))}});var n,r,o,i=[],a=0;e.columns.forEach(function(t){var n=t.getMaxCustomCellWidth(e);n?t.width=n:(t.width=t.wrappedWidth,i.push(t)),a+=t.width});var l=e.getWidth(t.pageSize().width)-a;l&&(l=D(i,l,function(t){return Math.max(t.minReadableWidth,t.minWidth)})),l&&(l=D(i,l,function(t){return t.minWidth})),l=Math.abs(l),!e.settings.horizontalPageBreak&&l>.1/t.scaleFactor()&&(l=l<1?l:Math.round(l),console.warn("Of the table content, ".concat(l," units width could not fit page"))),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var r=e[n],o=null,i=0,a=0,l=0;l<t.columns.length;l++){var s=t.columns[l];if((a-=1)>1&&t.columns[l+1])i+=s.width,delete r.cells[s.index];else if(o){var u=o;delete r.cells[s.index],o=null,u.width=s.width+i}else{var u=r.cells[s.index];if(!u)continue;if(a=u.colSpan,i=0,u.colSpan>1){o=u,i+=s.width;continue}u.width=s.width+i}}}(e),function(t,e){for(var n={count:0,height:0},r=0,o=t.allRows();r<o.length;r++){for(var i=o[r],a=0,l=t.columns;a<l.length;a++){var s=l[a],u=i.cells[s.index];if(u){e.applyStyles(u.styles,!0);var h=u.width-u.padding("horizontal");if("linebreak"===u.styles.overflow)u.text=e.splitTextToSize(u.text,h+1/e.scaleFactor(),{fontSize:u.styles.fontSize});else if("ellipsize"===u.styles.overflow)u.text=C(u.text,h,u.styles,e,"...");else if("hidden"===u.styles.overflow)u.text=C(u.text,h,u.styles,e,"");else if("function"==typeof u.styles.overflow){var f=u.styles.overflow(u.text,h);"string"==typeof f?u.text=[f]:u.text=f}u.contentHeight=u.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var c=u.contentHeight/u.rowSpan;u.rowSpan>1&&n.count*n.height<c*u.rowSpan?n={height:c,count:u.rowSpan}:n&&n.count>0&&n.height>c&&(c=n.height),c>i.height&&(i.height=c)}}n.count--}}(e,t),function(t){for(var e={},n=1,r=t.allRows(),o=0;o<r.length;o++)for(var i=r[o],a=0,l=t.columns;a<l.length;a++){var s=l[a],u=e[s.index];if(n>1)n--,delete i.cells[s.index];else if(u)u.cell.height+=i.height,n=u.cell.colSpan,delete i.cells[s.index],u.left--,u.left<=1&&delete e[s.index];else{var h=i.cells[s.index];if(!h)continue;if(h.height=i.height,h.rowSpan>1){var f=r.length-o,c=h.rowSpan>f?f:h.rowSpan;e[s.index]={cell:h,left:c,row:i}}}}}(e)}(n,o),n.applyStyles(n.userStyles),o}function H(t,e,n,r,o,i){var a={};return e.map(function(e,l){for(var s=0,u={},h=0,f=0,c=0;c<n.length;c++){var d=n[c];if(null==a[d.index]||0===a[d.index].left){if(0===f){var g=void 0;g=Array.isArray(e)?e[d.index-h-s]:e[d.dataKey];var p={};"object"!=typeof g||Array.isArray(g)||(p=(null==g?void 0:g.styles)||{});var v=new W(g,function(t,e,n,r,o,i,a){var l,s={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}}[r];"head"===t?l=o.headStyles:"body"===t?l=o.bodyStyles:"foot"===t&&(l=o.footStyles);var u=y({},s.table,s[t],o.styles,l),h=o.columnStyles[e.dataKey]||o.columnStyles[e.index]||{},f="body"===t&&n%2==0?y({},s.alternateRow,o.alternateRowStyles):{},c=y({},{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/i,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0},u,f,"body"===t?h:{});return y(c,a)}(t,d,l,o,r,i,p),t);u[d.dataKey]=v,u[d.index]=v,f=v.colSpan-1,a[d.index]={left:v.rowSpan-1,times:f}}else f--,h++}else a[d.index].left--,f=a[d.index].times,s++}return new S(e,l,t,u)})}function k(t,e){var n={};return t.forEach(function(t){if(null!=t.raw){var r=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=r&&(n[t.dataKey]=r)}}),Object.keys(n).length>0?n:null}function z(t,e){var n=e.settings,r=n.startY,o=n.margin,i={x:o.left,y:r},l=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),s=r+o.bottom+l;"avoid"===n.pageBreak&&(s+=e.body.reduce(function(t,e){return t+e.height},0));var u=new a(t);("always"===n.pageBreak||null!=n.startY&&s>u.pageSize().height)&&(B(u),i.y=o.top),e.callWillDrawPageHooks(u,i);var h=y({},i);e.startPageNumber=u.pageNumber(),n.horizontalPageBreak?function(t,e,n,r){var o=function(t,e){for(var n=[],r=0;r<e.columns.length;r++){var o=function(t,e,n){void 0===n&&(n={});var r,o=g(t,e),i=new Map,a=[],l=[],s=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?s=e.settings.horizontalPageBreakRepeat:("string"==typeof e.settings.horizontalPageBreakRepeat||"number"==typeof e.settings.horizontalPageBreakRepeat)&&(s=[e.settings.horizontalPageBreakRepeat]),s.forEach(function(t){var n=e.columns.find(function(e){return e.dataKey===t||e.index===t});n&&!i.has(n.index)&&(i.set(n.index,!0),a.push(n.index),l.push(e.columns[n.index]),o-=n.wrappedWidth)});for(var u=!0,h=null!==(r=null==n?void 0:n.start)&&void 0!==r?r:0;h<e.columns.length;){if(i.has(h)){h++;continue}var f=e.columns[h].wrappedWidth;if(u||o>=f)u=!1,a.push(h),l.push(e.columns[h]),o-=f;else break;h++}return{colIndexes:a,columns:l,lastIndex:h-1}}(t,e,{start:r});o.columns.length&&(n.push(o),r=o.lastIndex)}return n}(t,e);if("afterAllRows"===e.settings.horizontalPageBreakBehaviour)o.forEach(function(o,i){var a;t.applyStyles(t.userStyles),i>0?L(t,e,n,r,o.columns,!0):j(t,e,r,o.columns),a=o.columns,t.applyStyles(t.userStyles),e.body.forEach(function(o,i){var l=i===e.body.length-1;T(t,e,o,l,n,r,a)}),M(t,e,r,o.columns)});else for(var i=-1,a=o[0];i<e.body.length-1;)!function(){var l=i;if(a){t.applyStyles(t.userStyles);var s=a.columns;i>=0?L(t,e,n,r,s,!0):j(t,e,r,s),l=A(t,e,i+1,r,s),M(t,e,r,s)}var u=l-i;o.slice(1).forEach(function(o){t.applyStyles(t.userStyles),L(t,e,n,r,o.columns,!0),A(t,e,i+1,r,o.columns,u),M(t,e,r,o.columns)}),i=l}()}(u,e,h,i):(u.applyStyles(u.userStyles),("firstPage"===n.showHead||"everyPage"===n.showHead)&&e.head.forEach(function(t){return R(u,e,t,i,e.columns)}),u.applyStyles(u.userStyles),e.body.forEach(function(t,n){var r=n===e.body.length-1;T(u,e,t,r,h,i,e.columns)}),u.applyStyles(u.userStyles),("lastPage"===n.showFoot||"everyPage"===n.showFoot)&&e.foot.forEach(function(t){return R(u,e,t,i,e.columns)})),f(u,e,h,i),e.callEndPageHooks(u,i),e.finalY=i.y,t.lastAutoTable=e,u.applyStyles(u.userStyles)}function j(t,e,n,r){var o=e.settings;t.applyStyles(t.userStyles),("firstPage"===o.showHead||"everyPage"===o.showHead)&&e.head.forEach(function(o){return R(t,e,o,n,r)})}function A(t,e,n,r,o,i){t.applyStyles(t.userStyles);var a=Math.min(n+(i=null!=i?i:e.body.length),e.body.length),l=-1;return e.body.slice(n,a).forEach(function(i,a){var s=n+a===e.body.length-1,u=E(t,e,s,r);i.canEntireRowFit(u,o)&&(R(t,e,i,r,o),l=n+a)}),l}function M(t,e,n,r){var o=e.settings;t.applyStyles(t.userStyles),("lastPage"===o.showFoot||"everyPage"===o.showFoot)&&e.foot.forEach(function(o){return R(t,e,o,n,r)})}function T(t,e,n,r,o,i,a){var l=E(t,e,r,i);if(n.canEntireRowFit(l,a))R(t,e,n,i,a);else if(function(t,e,n,r){var o=t.pageSize().height,i=r.settings.margin,a=o-(i.top+i.bottom);"body"===e.section&&(a-=r.getHeadHeight(r.columns)+r.getFootHeight(r.columns));var l=e.getMinimumRowHeight(r.columns,t);if(l>a)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!(l<n))return!1;var s=e.hasRowSpan(r.columns);return e.getMaxCellHeight(r.columns)>a?(s&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!s&&"avoid"!==r.settings.rowPageBreak}(t,n,l,e)){var s=function(t,e,n,r){var o={};t.spansMultiplePages=!0,t.height=0;for(var i=0,a=0,l=n.columns;a<l.length;a++){var s=l[a],u=t.cells[s.index];if(u){Array.isArray(u.text)||(u.text=[u.text]);var h=new W(u.raw,u.styles,u.section);(h=y(h,u)).text=[];var f=function(t,e,n){var r=n.getLineHeight(t.styles.fontSize);return Math.max(0,Math.floor((e-t.padding("vertical"))/r))}(u,e,r);u.text.length>f&&(h.text=u.text.splice(f,u.text.length));var c=r.scaleFactor(),d=r.getLineHeightFactor();u.contentHeight=u.getContentHeight(c,d),u.contentHeight>=e&&(u.contentHeight=e,h.styles.minCellHeight-=e),u.contentHeight>t.height&&(t.height=u.contentHeight),h.contentHeight=h.getContentHeight(c,d),h.contentHeight>i&&(i=h.contentHeight),o[s.index]=h}}var g=new S(t.raw,-1,t.section,o,!0);g.height=i;for(var p=0,v=n.columns;p<v.length;p++){var s=v[p],h=g.cells[s.index];h&&(h.height=g.height);var u=t.cells[s.index];u&&(u.height=t.height)}return g}(n,l,e,t);R(t,e,n,i,a),L(t,e,o,i,a),T(t,e,s,r,o,i,a)}else L(t,e,o,i,a),T(t,e,n,r,o,i,a)}function R(t,e,n,o,i){o.x=e.settings.margin.left;for(var a=0;a<i.length;a++){var l=i[a],s=n.cells[l.index];if(!s||(t.applyStyles(s.styles),s.x=o.x,s.y=o.y,!1===e.callCellHooks(t,e.hooks.willDrawCell,s,n,l,o))){o.x+=l.width;continue}!function(t,e,n){var r=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof r.lineWidth){var o=c(r.lineWidth,r.fillColor);o&&t.rect(e.x,n.y,e.width,e.height,o)}else"object"==typeof r.lineWidth&&(r.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,r){var o,i,a,l;function s(e,n,r,o,i){t.getDocument().setLineWidth(e),t.getDocument().line(n,r,o,i,"S")}r.top&&(o=n.x,i=n.y,a=n.x+e.width,l=n.y,r.right&&(a+=.5*r.right),r.left&&(o-=.5*r.left),s(r.top,o,i,a,l)),r.bottom&&(o=n.x,i=n.y+e.height,a=n.x+e.width,l=n.y+e.height,r.right&&(a+=.5*r.right),r.left&&(o-=.5*r.left),s(r.bottom,o,i,a,l)),r.left&&(o=n.x,i=n.y,a=n.x,l=n.y+e.height,r.top&&(i-=.5*r.top),r.bottom&&(l+=.5*r.bottom),s(r.left,o,i,a,l)),r.right&&(o=n.x+e.width,i=n.y,a=n.x+e.width,l=n.y+e.height,r.top&&(i-=.5*r.top),r.bottom&&(l+=.5*r.bottom),s(r.right,o,i,a,l))}(t,e,n,r.lineWidth))}(t,s,o);var u=s.getTextPos();r(s.text,u.x,u.y,{halign:s.styles.halign,valign:s.styles.valign,maxWidth:Math.ceil(s.width-s.padding("left")-s.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,s,n,l,o),o.x+=l.width}o.y+=n.height}function E(t,e,n,r){var o=e.settings.margin.bottom,i=e.settings.showFoot;return("everyPage"===i||"lastPage"===i&&n)&&(o+=e.getFootHeight(e.columns)),t.pageSize().height-r.y-o}function L(t,e,n,r,o,i){void 0===o&&(o=[]),void 0===i&&(i=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||i||e.foot.forEach(function(n){return R(t,e,n,r,o)}),e.callEndPageHooks(t,r);var a=e.settings.margin;f(t,e,n,r),B(t),e.pageNumber++,r.x=a.left,r.y=a.top,n.y=a.top,e.callWillDrawPageHooks(t,r),"everyPage"===e.settings.showHead&&(e.head.forEach(function(n){return R(t,e,n,r,o)}),t.applyStyles(t.userStyles))}function B(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}function N(t,e){var n=m(t,e),r=F(t,n);z(t,r)}try{if("undefined"!=typeof window&&window){var O=window,_=O.jsPDF||(null===(o=O.jspdf)||void 0===o?void 0:o.jsPDF);_&&function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z(this,F(this,m(this,t[0]))),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,e,n,o){r(t,e,n,o,this)},t.API.autoTableSetDefaults=function(t){return a.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){a.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var n,r=v(new a(this),t,window,e,!1),o=r.head,i=r.body;return{columns:(null===(n=o[0])||void 0===n?void 0:n.map(function(t){return t.content}))||[],rows:i,data:i}}}(_)}}catch(t){console.error("Could not apply autoTable plugin",t)}}}]);