"use client";

import { useCreateProductMutation, CreateProductDto } from "@/reduxRTK/services/productApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useProductCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a product
  const [createProduct, { isLoading }] = useCreateProductMutation();

  const createNewProduct = async (productData: CreateProductDto) => {
    try {
      console.log("useProductCreate - Starting product creation with data:", productData);

      const result = await createProduct(productData).unwrap() as ApiResponse<any>;
      console.log("useProductCreate - API response:", result);

      if (!result.success) {
        console.error("useProductCreate - API returned error:", result.message);
        throw new Error(result.message || "Failed to create product");
      }

      showMessage("success", "Product created successfully");

      // Call onSuccess with a small delay to ensure the cache is invalidated
      if (onSuccess) {
        console.log("Calling onSuccess callback for product creation");
        onSuccess();

        // Call it again after a delay to ensure the table refreshes
        setTimeout(() => {
          console.log("Calling delayed onSuccess to ensure table refresh after product creation");
          onSuccess();
        }, 300);
      }

      // The backend returns an array of products in the 'products' field
      // Extract the first product from the array
      const createdProduct = result.data?.products?.[0] || result.data;
      return createdProduct;
    } catch (error: any) {
      console.error("Create product error:", error);
      showMessage("error", error.message || "Failed to create product");
      throw error;
    }
  };

  return {
    createProduct: createNewProduct,
    isCreating: isLoading
  };
};
