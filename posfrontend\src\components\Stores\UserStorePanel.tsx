"use client";

import React, { useState, useEffect } from "react";
import { Form, Select, Button, Table, Spin, Empty, Tag, Switch } from "antd";
import {
  UserOutlined,
  ShopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useGetAllUsersQuery } from "@/reduxRTK/services/authApi";
import {
  useGetUserStoresQuery,
  useAssociateUserWithStoreMutation,
  useSetUserDefaultStoreMutation,
  useRemoveUserFromStoreMutation,
} from "@/reduxRTK/services/userStoreApi";
import { Store } from "@/types/store";
import { User } from "@/types/user";
import { showMessage } from "@/utils/showMessage";
import SlidingPanel from "@/components/ui/SlidingPanel";
import "./store-panels.css";

interface UserStorePanelProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store | null;
  onSuccess?: () => void;
}

const UserStorePanel: React.FC<UserStorePanelProps> = ({
  isOpen,
  onClose,
  store,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [userStores, setUserStores] = useState<any[]>([]);

  // Fetch users
  const { data: usersData, isLoading: isLoadingUsers } = useGetAllUsersQuery({
    page: 1,
    limit: 100,
  });

  // Fetch user stores when a user is selected
  const {
    data: userStoresData,
    isLoading: isLoadingUserStores,
    refetch: refetchUserStores,
  } = useGetUserStoresQuery(selectedUser || 0, { skip: !selectedUser });

  // Mutations
  const [associateUserWithStore, { isLoading: isAssociating }] =
    useAssociateUserWithStoreMutation();
  const [setUserDefaultStore, { isLoading: isSettingDefault }] =
    useSetUserDefaultStoreMutation();
  const [removeUserFromStore, { isLoading: isRemoving }] =
    useRemoveUserFromStoreMutation();

  // Update user stores when data changes
  useEffect(() => {
    if (userStoresData?.data) {
      setUserStores(userStoresData.data);
    } else {
      setUserStores([]);
    }
  }, [userStoresData]);

  // Handle user selection
  const handleUserChange = (userId: number) => {
    setSelectedUser(userId);
    form.setFieldsValue({ userId });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (!store) {
        showMessage("error", "No store selected");
        return;
      }

      const values = await form.validateFields();

      const response = await associateUserWithStore({
        userId: values.userId,
        storeId: store.id,
        isDefault: values.isDefault,
      }).unwrap();

      if (response.success) {
        showMessage("success", "User associated with store successfully");
        refetchUserStores();
        if (onSuccess) onSuccess();
      } else {
        showMessage(
          "error",
          response.message || "Failed to associate user with store",
        );
      }
    } catch (error: any) {
      showMessage("error", error.data?.message || "An error occurred");
    }
  };

  // Handle setting default store
  const handleSetDefault = async (userId: number, storeId: number) => {
    try {
      const response = await setUserDefaultStore({
        userId,
        storeId,
      }).unwrap();

      if (response.success) {
        showMessage("success", "Default store set successfully");
        refetchUserStores();
        if (onSuccess) onSuccess();
      } else {
        showMessage("error", response.message || "Failed to set default store");
      }
    } catch (error: any) {
      showMessage("error", error.data?.message || "An error occurred");
    }
  };

  // Handle removing user from store
  const handleRemoveFromStore = async (userId: number, storeId: number) => {
    try {
      if (
        window.confirm(
          "Are you sure you want to remove this user from the store?",
        )
      ) {
        const response = await removeUserFromStore({
          userId,
          storeId,
        }).unwrap();

        if (response.success) {
          showMessage("success", "User removed from store successfully");
          refetchUserStores();
          if (onSuccess) onSuccess();
        } else {
          showMessage(
            "error",
            response.message || "Failed to remove user from store",
          );
        }
      }
    } catch (error: any) {
      showMessage("error", error.data?.message || "An error occurred");
    }
  };

  // Table columns
  const columns = [
    {
      title: "Store",
      dataIndex: "name",
      key: "name",
      render: (text: string) => (
        <div className="flex items-center">
          <ShopOutlined className="mr-2" />
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: "Default",
      dataIndex: "isDefault",
      key: "isDefault",
      render: (isDefault: boolean, record: any) => (
        <Switch
          checked={isDefault}
          onChange={() => handleSetDefault(selectedUser!, record.id)}
          loading={isSettingDefault}
          checkedChildren={<CheckCircleOutlined />}
          unCheckedChildren={<CloseCircleOutlined />}
        />
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: any) => (
        <Button
          danger
          onClick={() => handleRemoveFromStore(selectedUser!, record.id)}
          loading={isRemoving}
          className="border-red-600 bg-red-600 text-white hover:bg-red-700"
        >
          Remove
        </Button>
      ),
    },
  ];

  // Panel footer with close button
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ background: "#f5f5f5", borderColor: "#d9d9d9" }}
      >
        Close
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      title={`Manage Users for ${store?.name || "Store"}`}
      isOpen={isOpen}
      onClose={onClose}
      width="500px"
      footer={panelFooter}
    >
      <div className="bg-white p-6 pt-20">
        <div className="mb-6">
          <h3 className="mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800">
            Associate User with Store
          </h3>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="store-form"
          >
            <Form.Item
              name="userId"
              label={
                <span className="flex items-center">
                  <UserOutlined className="mr-1" /> User
                </span>
              }
              rules={[{ required: true, message: "Please select a user" }]}
            >
              <Select
                placeholder="Select a user"
                loading={isLoadingUsers}
                onChange={handleUserChange}
              >
                {(usersData as any)?.data?.users?.map((user: User) => (
                  <Select.Option key={user.id} value={user.id}>
                    <div className="flex items-center">
                      <UserOutlined className="mr-2" />
                      {user.name} ({user.role})
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="isDefault"
              valuePropName="checked"
              initialValue={false}
            >
              <Switch
                checkedChildren="Set as default store"
                unCheckedChildren="Not default"
              />
            </Form.Item>

            <Button
              type="primary"
              htmlType="submit"
              loading={isAssociating}
              className="border-blue-600 bg-blue-600 hover:bg-blue-700"
              disabled={!store || !selectedUser}
            >
              Associate User with Store
            </Button>
          </Form>
        </div>

        {selectedUser && (
          <div className="mb-4">
            <h3 className="mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800">
              User&quot;s Stores
            </h3>

            {isLoadingUserStores ? (
              <div className="flex h-40 items-center justify-center">
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{ fontSize: 24, color: "#1890ff" }}
                      spin
                    />
                  }
                />
              </div>
            ) : userStores.length > 0 ? (
              <Table
                dataSource={userStores}
                columns={columns}
                rowKey="id"
                pagination={false}
                className="user-stores-table"
              />
            ) : (
              <Empty description="No stores associated with this user" />
            )}
          </div>
        )}
      </div>
    </SlidingPanel>
  );
};

export default UserStorePanel;
