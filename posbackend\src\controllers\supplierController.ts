import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createSupplier,
  getAllSuppliers,
  getSupplierById,
  updateSupplierById,
  deleteSupplierById,
} from "../services/supplierService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleSupplierRequest = async (req: Request, res: Response): Promise<void> => {
  const { mode, supplierId, page, limit, ...data } = req.body;
  const requester = req.user as DecodedToken; // ✅ Use `req.user` from `authMiddleware`

  // ✅ Validate mode
  const validModes = ["createnew", "update", "delete", "retrieve"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      // ✅ Create New Supplier
      case "createnew": {
        const requiredFields = ["name", "phone"];
        const missingFields = requiredFields.filter((field) => !data[field]);

        if (missingFields.length > 0) {
          return sendResponse(res, 400, false, `Missing required fields: ${missingFields.join(", ")}`);
        }

        const newSupplier = await createSupplier(requester, {
          name: data.name,
          contactPerson: data.contactPerson,
          phone: data.phone,
          email: data.email,
          address: data.address,
        });

        return sendResponse(res, 201, true, "Supplier created successfully.", newSupplier);
      }

      // ✅ Retrieve Suppliers (Single or All)
      case "retrieve": {
        if (supplierId) {
          // Fetch a single supplier by ID
          const supplier = await getSupplierById(requester, Number(supplierId));
          return sendResponse(res, 200, true, "Supplier retrieved successfully.", supplier);
        } else {
          // Fetch all suppliers with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const suppliers = await getAllSuppliers(requester, pageNum, limitNum);
          return sendResponse(res, 200, true, "Suppliers retrieved successfully.", suppliers);
        }
      }

      // ✅ Update Supplier
      case "update": {
        if (!supplierId) {
          return sendResponse(res, 400, false, "Supplier ID is required for updating.");
        }

        const updatedSupplier = await updateSupplierById(requester, Number(supplierId), data);
        return sendResponse(res, 200, true, "Supplier updated successfully.", updatedSupplier);
      }

      // ✅ Delete Supplier (Single or Multiple)
      case "delete": {
        // Check if we have supplierIds (array) or supplierId (single)
        const { supplierIds } = req.body;

        if (!supplierId && !supplierIds) {
          return sendResponse(res, 400, false, "Supplier ID(s) are required for deletion.");
        }

        // Use supplierIds if provided, otherwise use single supplierId
        const idsToDelete = supplierIds
          ? (Array.isArray(supplierIds) ? supplierIds : [supplierIds])
          : [Number(supplierId)];

        const result = await deleteSupplierById(requester, idsToDelete);

        return sendResponse(res, 200, true, result.message, result);
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(res, 500, false, error.message || "Internal Server Error");
  }
};
