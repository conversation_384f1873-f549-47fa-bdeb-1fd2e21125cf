import React, { useState, useMemo } from "react";
import Image from "next/image";
import { useOfflinePOS, CartItem } from "@/hooks/useOfflinePOS";
import { showMessage } from "@/utils/showMessage";
import { WifiOff, Wifi, Refresh<PERSON><PERSON>, AlertTriangle } from "lucide-react";

const OfflinePOSPanel: React.FC = () => {
  const {
    isOffline,
    offlineStatus,
    isLoading,
    products,
    searchProducts,
    findProductByBarcode,
    createSale,
    forceSyncNow,
    getStore
  } = useOfflinePOS();

  const [cart, setCart] = useState<CartItem[]>([]);
  const [search, setSearch] = useState("");
  const [barcodeInput, setBarcodeInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'mobile_money'>('cash');

  // Filtered products
  const filteredProducts = useMemo(() => {
    if (!search) return products.slice(0, 50); // Limit for performance
    return products.filter(p =>
      p.name.toLowerCase().includes(search.toLowerCase()) ||
      (p.sku && p.sku.toLowerCase().includes(search.toLowerCase())) ||
      (p.barcode && p.barcode.toLowerCase().includes(search.toLowerCase()))
    ).slice(0, 50);
  }, [products, search]);

  // Cart management
  const handleAddToCart = (product: any) => {
    if (product.stockQuantity <= 0) {
      showMessage("warning", "Product out of stock");
      return;
    }

    setCart(prev => {
      const existingItem = prev.find(item => item.productId === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, {
        productId: product.id,
        productName: product.name,
        quantity: 1,
        price: Number(product.price)
      }];
    });
  };

  const handleUpdateQuantity = (productId: number, quantity: number) => {
    if (quantity <= 0) {
      setCart(prev => prev.filter(item => item.productId !== productId));
    } else {
      setCart(prev => prev.map(item =>
        item.productId === productId ? { ...item, quantity } : item
      ));
    }
  };

  const handleRemoveFromCart = (productId: number) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };

  const handleClearCart = () => setCart([]);

  // Barcode scanning
  const handleBarcodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!barcodeInput.trim()) return;

    try {
      const product = await findProductByBarcode(barcodeInput.trim());
      if (product) {
        handleAddToCart(product);
        setBarcodeInput("");
        showMessage("success", `Added ${product.name} to cart`);
      } else {
        showMessage("error", "Product not found");
      }
    } catch (error) {
      showMessage("error", "Failed to find product");
    }
  };

  // Calculate totals
  const subtotal = useMemo(() => 
    cart.reduce((sum, item) => sum + (item.price * item.quantity), 0), [cart]
  );
  const tax = 0; // Set to 0 as per requirements
  const total = subtotal + tax;

  // Process sale
  const handleProcessSale = async () => {
    if (cart.length === 0) {
      showMessage("error", "Cart is empty");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createSale(cart, paymentMethod);
      
      if (result.success) {
        setCart([]);
        setPaymentMethod('cash');
        
        if (result.isOffline) {
          showMessage("success", "Sale saved offline - will sync when online");
        } else {
          showMessage("success", "Sale completed successfully");
        }
      }
    } catch (error) {
      console.error('Sale failed:', error);
      showMessage("error", error instanceof Error ? error.message : "Sale failed");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get store details
  const store = getStore ? getStore() : null;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading POS system...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Store Details (show in offline mode) */}
      {isOffline && store && (
        <div className="mb-4 p-3 bg-blue-50 rounded shadow text-blue-900">
          <div className="font-bold text-lg">{store.name || 'Store Name'}</div>
          {store.address && <div className="text-sm">{store.address}</div>}
          {store.phone && <div className="text-sm">Phone: {store.phone}</div>}
        </div>
      )}

      {/* Left Panel - Cart */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        {/* Header with Status */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xl font-bold text-gray-800">POS System</h2>
            <div className="flex items-center gap-2">
              {isOffline ? (
                <div className="flex items-center gap-1 text-orange-600">
                  <WifiOff size={16} />
                  <span className="text-sm font-medium">Offline</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-green-600">
                  <Wifi size={16} />
                  <span className="text-sm font-medium">Online</span>
                </div>
              )}
            </div>
          </div>

          {/* Offline Status */}
          {(offlineStatus.pendingSales > 0 || offlineStatus.failedSales > 0) && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertTriangle size={16} className="text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    {offlineStatus.pendingSales} pending, {offlineStatus.failedSales} failed
                  </span>
                </div>
                <button
                  onClick={forceSyncNow}
                  className="text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700"
                  disabled={isOffline}
                >
                  <RefreshCw size={12} className="inline mr-1" />
                  Sync
                </button>
              </div>
            </div>
          )}

          {/* Barcode Scanner */}
          <form onSubmit={handleBarcodeSubmit} className="mb-2">
            <input
              type="text"
              value={barcodeInput}
              onChange={(e) => setBarcodeInput(e.target.value)}
              placeholder="Scan or enter barcode..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </form>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-4">
          <h3 className="font-semibold text-gray-700 mb-3">Cart Items</h3>
          {cart.length === 0 ? (
            <div className="text-center text-gray-400 py-8">
              <p>Cart is empty</p>
              <p className="text-sm">Add products to start a sale</p>
            </div>
          ) : (
            <div className="space-y-2">
              {cart.map((item) => (
                <div key={item.productId} className="bg-gray-50 rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-800 text-sm">{item.productName}</h4>
                    <button
                      onClick={() => handleRemoveFromCart(item.productId)}
                      className="text-red-500 hover:text-red-700 text-xs"
                    >
                      ✕
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1)}
                        className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                      >
                        -
                      </button>
                      <span className="w-8 text-center text-sm">{item.quantity}</span>
                      <button
                        onClick={() => handleUpdateQuantity(item.productId, item.quantity + 1)}
                        className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                      >
                        +
                      </button>
                    </div>
                    <span className="font-semibold text-blue-600">
                      ₵{(item.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Totals and Payment */}
        <div className="border-t border-gray-200 p-4">
          <div className="space-y-2 mb-4">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>₵{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax:</span>
              <span>₵{tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Total:</span>
              <span className="text-blue-600">₵{total.toFixed(2)}</span>
            </div>
          </div>

          {/* Payment Method */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method
            </label>
            <select
              value={paymentMethod}
              onChange={(e) => setPaymentMethod(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="cash">Cash</option>
              <option value="card">Card</option>
              <option value="mobile_money">Mobile Money</option>
            </select>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <button
              onClick={handleProcessSale}
              disabled={cart.length === 0 || isSubmitting}
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Processing..." : `PAY ₵${total.toFixed(2)}`}
            </button>
            <button
              onClick={handleClearCart}
              disabled={cart.length === 0}
              className="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 disabled:bg-gray-300"
            >
              Clear Cart
            </button>
          </div>
        </div>
      </div>

      {/* Right Panel - Products */}
      <div className="flex-1 flex flex-col">
        {/* Search */}
        <div className="p-4 border-b border-gray-200 bg-white">
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search products..."
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            {isOffline ? `${offlineStatus.cachedProducts} cached products` : `${products.length} products available`}
          </p>
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="grid grid-cols-4 xl:grid-cols-6 gap-3">
            {filteredProducts.map((product) => (
              <button
                key={product.id}
                onClick={() => handleAddToCart(product)}
                disabled={product.stockQuantity <= 0}
                className={`aspect-square rounded-lg p-3 flex flex-col items-center justify-center text-center transition-all ${
                  product.stockQuantity <= 0
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-white border-2 border-gray-200 hover:border-blue-400 hover:shadow-md active:scale-95'
                }`}
              >
                {product.imageUrl ? (
                  <Image
                    src={product.imageUrl}
                    alt={product.name}
                    width={48}
                    height={48}
                    className="object-cover rounded mb-2"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gray-100 rounded mb-2 flex items-center justify-center text-2xl">
                    🛍️
                  </div>
                )}
                <span className="font-medium text-xs text-gray-800 truncate w-full mb-1">
                  {product.name}
                </span>
                <span className="text-sm font-bold text-blue-600">
                  ₵{Number(product.price).toFixed(2)}
                </span>
                {product.stockQuantity <= 5 && product.stockQuantity > 0 && (
                  <span className="text-xs text-orange-600">Low stock</span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfflinePOSPanel;
