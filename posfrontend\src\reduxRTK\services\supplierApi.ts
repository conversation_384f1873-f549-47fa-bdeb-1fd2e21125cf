// services/supplierApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Supplier types
export interface Supplier {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  contactPerson: string;
  createdBy: number;
  createdAt: string;
}

export interface PaginatedSuppliers {
  total: number;
  page: number;
  perPage: number;
  suppliers: Supplier[];
}

export interface CreateSupplierDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  contactPerson?: string;
}

export interface UpdateSupplierDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  contactPerson?: string;
}

export const supplierApi = createApi({
  reducerPath: 'supplierApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Supplier'] as const,
  endpoints: (builder) => ({
    // Get all suppliers (paginated)
    getAllSuppliers: builder.query<ApiResponse<PaginatedSuppliers>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        // Get user ID from store
        const userId = store.getState().auth.user?.id;

        console.log("API call - User ID:", userId);

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
            // Include userId for debugging, but we'll filter client-side
            userId
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Supplier'],
    }),

    // Get supplier by ID
    getSupplierById: builder.query<ApiResponse<Supplier>, number>({
      query: (supplierId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'retrieve',
            supplierId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'Supplier', id }],
    }),

    // Create new supplier
    createSupplier: builder.mutation<ApiResponse<Supplier>, CreateSupplierDto>({
      query: (supplierData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'createnew',
            ...supplierData,
          },
          token,
        };
      },
      invalidatesTags: ['Supplier'],
    }),

    // Update supplier
    updateSupplier: builder.mutation<ApiResponse<Supplier>, { supplierId: number; data: UpdateSupplierDto }>({
      query: ({ supplierId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'update',
            supplierId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { supplierId }) => [
        { type: 'Supplier', id: supplierId },
        'Supplier',
      ],
    }),

    // Delete supplier (single)
    deleteSupplier: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (supplierId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'delete',
            supplierId,
          },
          token,
        };
      },
      invalidatesTags: ['Supplier'],
    }),

    // Bulk delete suppliers
    bulkDeleteSuppliers: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (supplierIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/suppliers',
          payloaddata: {
            mode: 'delete',
            supplierIds,
          },
          token,
        };
      },
      invalidatesTags: ['Supplier'],
    }),
  }),
});

export const {
  useGetAllSuppliersQuery,
  useGetSupplierByIdQuery,
  useCreateSupplierMutation,
  useUpdateSupplierMutation,
  useDeleteSupplierMutation,
  useBulkDeleteSuppliersMutation,
} = supplierApi;
