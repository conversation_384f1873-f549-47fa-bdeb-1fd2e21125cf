"use client";

import React, { useState, useEffect } from "react";
import { Card, Button, Spin, notification } from "antd";
import { ShoppingOutlined, LoadingOutlined, ReloadOutlined } from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile, MOBILE_BREAKPOINT } from "@/hooks/use-mobile";
import { useProductList } from "@/hooks/products/useProductList";
import { useProductDelete } from "@/hooks/products/useProductDelete";
import { useProductBulkDelete } from "@/hooks/products/useProductBulkDelete";
import ProductTable from "@/components/Products/ProductTable";
import ProductPagination from "@/components/Products/ProductPagination";
import ProductFormPanel from "@/components/Products/ProductFormPanel";
import ProductDetailPanel from "@/components/Products/ProductDetailPanel";
import ProductSearch from "@/components/Products/ProductSearch";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import StockAdjustmentFormPanel from "@/components/StockAdjustments/StockAdjustmentFormPanel";
import { Product } from "@/reduxRTK/services/productApi";
import { UserRole } from "@/types/user";

const ProductsPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // UI state
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [isStockAdjustmentPanelOpen, setIsStockAdjustmentPanelOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Add a refresh counter to force re-renders
  const [refreshCounter, setRefreshCounter] = useState(0);

  // Product list state and handlers
  const {
    products,
    total,
    page,
    limit,
    isLoading,
    refetch,
    forceRefresh, // Use the new forceRefresh function
    searchTerm,
    setSearchTerm,
    handlePageChange,
  } = useProductList();

  // Delete product handler
  const { deleteProduct, isDeleting } = useProductDelete(() => {
    setIsDeleteDialogOpen(false);
    // Use forceRefresh instead of refetch
    forceRefresh();
  });

  // Bulk delete product handler
  const { bulkDeleteProducts, isDeleting: isBulkDeleting } = useProductBulkDelete(() => {
    setIsBulkDeleteDialogOpen(false);
    // Use forceRefresh instead of refetch
    forceRefresh();
  });

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [productsToDelete, setProductsToDelete] = useState<number[]>([]);

  // Check if user can add products (only admin can add products)
  const canAddProduct = currentUser?.role === "admin";

  // Handle add product button click
  const handleAddProduct = () => {
    setIsAddPanelOpen(true);
  };

  // Handle view product button click
  const handleViewProduct = (productId: number) => {
    setSelectedProductId(productId);
    setIsDetailPanelOpen(true);
  };

  // Handle edit product button click
  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setIsEditPanelOpen(true);
  };

  // Handle edit from detail panel
  const handleEditFromDetail = (productId: number) => {
    const product = products.find(p => p.id === productId) || null;
    if (product) {
      setSelectedProduct(product);
      setIsDetailPanelOpen(false);
      setIsEditPanelOpen(true);
    }
  };

  // Handle delete product button click
  const handleDeleteProduct = (productId: number) => {
    setSelectedProductId(productId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete product
  const confirmDeleteProduct = async () => {
    if (selectedProductId) {
      await deleteProduct(selectedProductId);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedProductId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (productIds: number[]) => {
    console.log("handleBulkDelete called with productIds:", productIds);
    setProductsToDelete(productIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with products:", productsToDelete);

    if (productsToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple products
        await bulkDeleteProducts(productsToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setProductsToDelete([]);
  };

  // Handle stock adjustment button click
  const handleAdjustStock = (product: Product) => {
    setSelectedProduct(product);
    setIsStockAdjustmentPanelOpen(true);
  };

  // Force a refetch when the refresh counter changes
  useEffect(() => {
    if (refreshCounter > 0) {
      console.log(`Refreshing products due to counter change: ${refreshCounter}`);
      // Use window.location.reload() as a last resort if nothing else works
      // window.location.reload();

      // Instead, use the refetch function from RTK Query
      refetch();
    }
  }, [refreshCounter, refetch]);

  // We'll handle empty state within the main layout to keep the search visible

  return (
    <div className="p-2 sm:p-4 w-full">
      <Card
        title={<span className="text-gray-800">Product Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: { padding: '12px', overflow: 'hidden', backgroundColor: '#ffffff' },
          header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
        }}
        extra={
          <div className="flex space-x-2">
            {/* Refresh Button - Always visible */}
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                notification.info({
                  message: 'Refreshing Products',
                  description: 'Getting the latest product data...',
                  icon: <ReloadOutlined style={{ color: '#108ee9' }} />,
                  duration: 2,
                });
                // Use forceRefresh instead of refetch
                forceRefresh();
              }}
              size={isMobile ? "small" : "middle"}
            >
              {isMobile ? "" : "Refresh"}
            </Button>

            {/* Add Product Button - Admin only */}
            {canAddProduct && (
              <Button
                type="primary"
                icon={<ShoppingOutlined />}
                onClick={handleAddProduct}
                size={isMobile ? "small" : "middle"}
              >
                {isMobile ? "" : "Add Product"}
              </Button>
            )}
          </div>
        }
      >
        <div className="w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200">
          {/* Search Component - Always visible */}
          <ProductSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex justify-center items-center h-60 bg-gray-50">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
            </div>
          ) : (
            <>
              {/* Product Table Component */}
              {products.length > 0 ? (
                <ProductTable
                  products={products}
                  loading={false}
                  onView={handleViewProduct}
                  onEdit={handleEditProduct}
                  onDelete={handleDeleteProduct}
                  onBulkDelete={handleBulkDelete}
                  onAdjustStock={handleAdjustStock}
                  isMobile={isMobile}
                />
              ) : (
                <div className="flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800">
                  {searchTerm ? (
                    <>
                      <p>No products found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm('')}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No products found. {canAddProduct && "Click 'Add Product' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {products.length > 0 && (
                <ProductPagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add Product Panel */}
      <ProductFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => setIsAddPanelOpen(false)}
        onSuccess={() => {
          setIsAddPanelOpen(false);

          // Show a notification to the user
          notification.success({
            message: 'Product Added Successfully',
            description: 'Refreshing product data...',
            icon: <ReloadOutlined style={{ color: '#108ee9' }} />,
            duration: 2,
          });

          console.log("Product creation completed, refreshing product data");

          // Use forceRefresh for an immediate refresh
          forceRefresh();
        }}
        currentUser={currentUser}
      />

      {/* Edit Product Panel */}
      <ProductFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);

          // Show a notification to the user
          notification.success({
            message: 'Product Updated Successfully',
            description: 'Refreshing product data...',
            icon: <ReloadOutlined style={{ color: '#108ee9' }} />,
            duration: 2,
          });

          console.log("Product update completed, refreshing product data");

          // Use forceRefresh for an immediate refresh
          forceRefresh();
        }}
        product={selectedProduct}
        currentUser={currentUser}
      />

      {/* View Product Details Panel */}
      <ProductDetailPanel
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false);
          setSelectedProductId(null);
        }}
        productId={selectedProductId}
        onEdit={handleEditFromDetail}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteProduct}
        title="Delete Product"
        message="Are you sure you want to delete this product? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Products"
        message={`Are you sure you want to delete ${productsToDelete.length} products? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />

      {/* Stock Adjustment Panel */}
      <StockAdjustmentFormPanel
        isOpen={isStockAdjustmentPanelOpen}
        onClose={() => setIsStockAdjustmentPanelOpen(false)}
        onSuccess={() => {
          // Close the panel first
          setIsStockAdjustmentPanelOpen(false);

          // Show a notification to the user
          notification.success({
            message: 'Stock Adjustment Successful',
            description: 'Refreshing product data...',
            icon: <ReloadOutlined style={{ color: '#108ee9' }} />,
            duration: 2,
          });

          console.log("Stock adjustment completed, refreshing product data");

          // Use forceRefresh for an immediate refresh
          forceRefresh();
        }}
        product={selectedProduct}
      />
    </div>
  );
};

export default ProductsPage;


