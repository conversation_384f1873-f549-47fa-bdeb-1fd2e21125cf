"use strict";exports.id=3950,exports.ids=[3950],exports.modules={53950:(e,t,n)=>{n.d(t,{Ay:()=>L});var r=n(43984),o=n(58009),a=n.n(o),l=n(38636),s=n(27343),i=n(54979),c=n(90185),u=n(22127),p=n(43119),m=n(66937),d=n(36211),g=n(88752),f=n(56073),y=n.n(f),v=n(62312),O=n(90334),b=n(1439),$=n(78371),h=n(47285),x=n(13662),j=n(10941);let E=e=>{let{componentCls:t,iconCls:n,boxShadow:r,colorText:o,colorSuccess:a,colorError:l,colorWarning:s,colorInfo:i,fontSizeLG:c,motionEaseInOutCirc:u,motionDurationSlow:p,marginXS:m,paddingXS:d,borderRadiusLG:g,zIndexPopup:f,contentPadding:y,contentBg:v}=e,O=`${t}-notice`,$=new b.Mo("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:d,transform:"translateY(0)",opacity:1}}),x=new b.Mo("MessageMoveOut",{"0%":{maxHeight:e.height,padding:d,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),j={padding:d,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:m,fontSize:c},[`${O}-content`]:{display:"inline-block",padding:y,background:v,borderRadius:g,boxShadow:r,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:a},[`${t}-error > ${n}`]:{color:l},[`${t}-warning > ${n}`]:{color:s},[`${t}-info > ${n},
      ${t}-loading > ${n}`]:{color:i}};return[{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{color:o,position:"fixed",top:m,width:"100%",pointerEvents:"none",zIndex:f,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:$,animationDuration:p,animationPlayState:"paused",animationTimingFunction:u},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:x,animationDuration:p,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${O}-wrapper`]:Object.assign({},j)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},j),{padding:0,textAlign:"start"})}]},C=(0,x.OF)("Message",e=>[E((0,j.oX)(e,{height:150}))],e=>({zIndexPopup:e.zIndexPopupBase+$.jH+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}));var w=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let P={info:o.createElement(d.A,null),success:o.createElement(u.A,null),error:o.createElement(p.A,null),warning:o.createElement(m.A,null),loading:o.createElement(g.A,null)},N=e=>{let{prefixCls:t,type:n,icon:r,children:a}=e;return o.createElement("div",{className:y()(`${t}-custom-content`,`${t}-${n}`)},r||P[n],o.createElement("span",null,a))};var A=n(97071),I=n(22505);function k(e){let t;let n=new Promise(n=>{t=e(()=>{n(!0)})}),r=()=>{null==t||t()};return r.then=(e,t)=>n.then(e,t),r.promise=n,r}var M=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let S=e=>{let{children:t,prefixCls:n}=e,r=(0,O.A)(n),[a,l,s]=C(n,r);return a(o.createElement(v.ph,{classNames:{list:y()(l,s,r)}},t))},F=(e,t)=>{let{prefixCls:n,key:r}=t;return o.createElement(S,{prefixCls:n,key:r},e)},R=o.forwardRef((e,t)=>{let{top:n,prefixCls:r,getContainer:a,maxCount:l,duration:i=3,rtl:c,transitionName:u,onAllRemoved:p}=e,{getPrefixCls:m,getPopupContainer:d,message:g,direction:f}=o.useContext(s.QO),O=r||m("message"),b=o.createElement("span",{className:`${O}-close-x`},o.createElement(A.A,{className:`${O}-close-icon`})),[$,h]=(0,v.hN)({prefixCls:O,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>y()({[`${O}-rtl`]:null!=c?c:"rtl"===f}),motion:()=>(function(e,t){return{motionName:null!=t?t:`${e}-move-up`}})(O,u),closable:!1,closeIcon:b,duration:i,getContainer:()=>(null==a?void 0:a())||(null==d?void 0:d())||document.body,maxCount:l,onAllRemoved:p,renderNotifications:F});return o.useImperativeHandle(t,()=>Object.assign(Object.assign({},$),{prefixCls:O,message:g})),h}),H=0;function z(e){let t=o.useRef(null);return(0,I.rJ)("Message"),[o.useMemo(()=>{let e=e=>{var n;null===(n=t.current)||void 0===n||n.close(e)},n=n=>{if(!t.current){let e=()=>{};return e.then=()=>{},e}let{open:r,prefixCls:a,message:l}=t.current,s=`${a}-notice`,{content:i,icon:c,type:u,key:p,className:m,style:d,onClose:g}=n,f=M(n,["content","icon","type","key","className","style","onClose"]),v=p;return null==v&&(H+=1,v=`antd-message-${H}`),k(t=>(r(Object.assign(Object.assign({},f),{key:v,content:o.createElement(N,{prefixCls:a,type:u,icon:c},i),placement:"top",className:y()(u&&`${s}-${u}`,m,null==l?void 0:l.className),style:Object.assign(Object.assign({},null==l?void 0:l.style),d),onClose:()=>{null==g||g(),t()}})),()=>{e(v)}))},r={open:n,destroy:n=>{var r;void 0!==n?e(n):null===(r=t.current)||void 0===r||r.destroy()}};return["info","success","warning","error","loading"].forEach(e=>{r[e]=(t,r,o)=>{let a,l,s;return a=t&&"object"==typeof t&&"content"in t?t:{content:t},"function"==typeof r?s=r:(l=r,s=o),n(Object.assign(Object.assign({onClose:s,duration:l},a),{type:e}))}}),r},[]),o.createElement(R,Object.assign({key:"message-holder"},e,{ref:t}))]}let B=null,D=e=>e(),T=[],Q={};function Y(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:o}=Q,a=(null==e?void 0:e())||document.body;return{getContainer:()=>a,duration:t,rtl:n,maxCount:r,top:o}}let K=a().forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:i}=(0,o.useContext)(s.QO),c=Q.prefixCls||i("message"),u=(0,o.useContext)(l.B),[p,m]=z(Object.assign(Object.assign(Object.assign({},n),{prefixCls:c}),u.message));return a().useImperativeHandle(t,()=>{let e=Object.assign({},p);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),p[t].apply(p,arguments)}}),{instance:e,sync:r}}),m}),X=a().forwardRef((e,t)=>{let[n,r]=a().useState(Y),o=()=>{r(Y)};a().useEffect(o,[]);let l=(0,i.cr)(),s=l.getRootPrefixCls(),c=l.getIconPrefixCls(),u=l.getTheme(),p=a().createElement(K,{ref:t,sync:o,messageConfig:n});return a().createElement(i.Ay,{prefixCls:s,iconPrefixCls:c,theme:u},l.holderRender?l.holderRender(p):p)});function G(){if(!B){let e=document.createDocumentFragment(),t={fragment:e};B=t,D(()=>{(0,c.K)()(a().createElement(X,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,G())})}}),e)});return}B.instance&&(T.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":D(()=>{let t=B.instance.open(Object.assign(Object.assign({},Q),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":D(()=>{null==B||B.instance.destroy(e.key)});break;default:D(()=>{var n;let o=(n=B.instance)[t].apply(n,(0,r.A)(e.args));null==o||o.then(e.resolve),e.setCloseFn(o)})}}),T=[])}let J={open:function(e){let t=k(t=>{let n;let r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return T.push(r),()=>{n?D(()=>{n()}):r.skipped=!0}});return G(),t},destroy:e=>{T.push({type:"destroy",key:e}),G()},config:function(e){Q=Object.assign(Object.assign({},Q),e),D(()=>{var e;null===(e=null==B?void 0:B.sync)||void 0===e||e.call(B)})},useMessage:function(e){return z(e)},_InternalPanelDoNotUseOrYouWillBeFired:e=>{let{prefixCls:t,className:n,type:r,icon:a,content:l}=e,i=w(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:c}=o.useContext(s.QO),u=t||c("message"),p=(0,O.A)(u),[m,d,g]=C(u,p);return m(o.createElement(v.$T,Object.assign({},i,{prefixCls:u,className:y()(n,d,`${u}-notice-pure-panel`,g,p),eventKey:"pure",duration:null,content:o.createElement(N,{prefixCls:u,type:r,icon:a},l)})))}};["success","info","warning","error","loading"].forEach(e=>{J[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(e,t){(0,i.cr)();let n=k(n=>{let r;let o={type:e,args:t,resolve:n,setCloseFn:e=>{r=e}};return T.push(o),()=>{r?D(()=>{r()}):o.skipped=!0}});return G(),n}(e,n)}});let L=J}};