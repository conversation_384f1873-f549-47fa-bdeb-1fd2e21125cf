"use client";

import React from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import "@/styles/search.css";

interface SupplierSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isMobile?: boolean;
}

const SupplierSearch: React.FC<SupplierSearchProps> = ({
  searchTerm,
  setSearchTerm,
  isMobile = false,
}) => {
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    console.log("Supplier search input changed:", value);
    setSearchTerm(value);
  };

  return (
    <div className="sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3">
      <Input
        placeholder="Search by name, email, or phone..."
        prefix={<SearchOutlined className="text-gray-500" />}
        value={searchTerm}
        onChange={handleSearchChange}
        className="border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500"
        style={{
          width: isMobile ? "100%" : "300px",
          height: "36px",
          backgroundColor: "white",
          color: "#333",
        }}
        allowClear={{
          clearIcon: <span className="text-gray-500">×</span>,
        }}
      />
      {searchTerm && (
        <div className="ml-1 mt-1 text-xs text-gray-600">
          Searching for: &quot;{searchTerm}&quot;
        </div>
      )}
    </div>
  );
};

export default SupplierSearch;
