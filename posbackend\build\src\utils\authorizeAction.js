"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeAction = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const entityConfig = {
    product: { table: schema_1.products, createdByColumn: schema_1.products.createdBy },
    category: { table: schema_1.categories, createdByColumn: schema_1.categories.createdBy },
    user: { table: schema_1.users, createdByColumn: schema_1.users.createdBy },
    supplier: { table: schema_1.suppliers, createdByColumn: schema_1.suppliers.createdBy },
    purchases: { table: schema_1.purchases, createdByColumn: schema_1.purchases.createdBy },
    stockAdjustments: {
        table: schema_1.stockAdjustments,
        createdByColumn: schema_1.stockAdjustments.createdBy,
    },
    sales: { table: schema_1.sales, createdByColumn: schema_1.sales.createdBy },
    receipts: { table: schema_1.receipts, createdByColumn: schema_1.receipts.createdBy },
    stores: { table: schema_1.stores, createdByColumn: schema_1.stores.createdBy },
    expenses: { table: schema_1.expenses, createdByColumn: schema_1.expenses.createdBy },
    expense_categories: { table: schema_1.expenseCategories, createdByColumn: schema_1.expenseCategories.createdBy },
};
/**
 * Role-based authorization function.
 * Returns a query for fetch operations (`getAll`, `getById`),
 * or a filtering query for update/delete actions.
 *
 * This is an async function that must be awaited.
 * It can perform database lookups to determine proper authorization.
 */
const authorizeAction = async (requester, action, entity, entityId) => {
    const { table, createdByColumn } = entityConfig[entity];
    // ✅ Cashier: Can see products created by their admin & create sales
    if (requester.role === "cashier") {
        if (entity === "product") {
            if (action === "getAll") {
                // Find the cashier's admin ID
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.eq)(createdByColumn, db_1.postgresDb
                    .select({ adminId: schema_1.users.createdBy })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
                    .limit(1)));
            }
            if (action === "getById") {
                if (!entityId)
                    throw new Error(`Unauthorized: Missing ${entity} ID.`);
                // For getById, we need to check if the product was created by the cashier's admin
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(table.id, entityId), (0, drizzle_orm_1.eq)(createdByColumn, db_1.postgresDb
                    .select({ adminId: schema_1.users.createdBy })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
                    .limit(1))));
            }
        }
        // Handle sales and receipts for cashiers
        if (entity === "sales" || entity === "receipts") {
            if (action === "create") {
                return db_1.postgresDb.select().from(table); // ✅ Cashier can create sales and receipts
            }
            if (action === "getAll" || action === "getById") {
                // Find the cashier's creator (admin)
                const creatorResult = await db_1.postgresDb
                    .select({ creatorId: schema_1.users.createdBy })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
                    .limit(1);
                const creatorId = creatorResult[0]?.creatorId;
                if (creatorId) {
                    // Get all users created by the same admin (siblings)
                    const siblingsResult = await db_1.postgresDb
                        .select({ siblingId: schema_1.users.id })
                        .from(schema_1.users)
                        .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
                    const siblingIds = siblingsResult.map((s) => s.siblingId);
                    // Cashier can view sales/receipts made by them, their admin, and their siblings
                    return db_1.postgresDb
                        .select()
                        .from(table)
                        .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(createdByColumn, requester.id), (0, drizzle_orm_1.eq)(createdByColumn, creatorId), (0, drizzle_orm_1.inArray)(createdByColumn, siblingIds)));
                }
                else {
                    // If no creator found, just show their own sales/receipts
                    return db_1.postgresDb
                        .select()
                        .from(table)
                        .where((0, drizzle_orm_1.eq)(createdByColumn, requester.id));
                }
            }
        }
        // ❌ Explicitly prevent cashiers from performing stock adjustments
        if (entity === "stockAdjustments") {
            throw new Error("Unauthorized: Cashiers cannot perform stock adjustments.");
        }
        // ❌ Explicitly prevent cashiers from managing expenses
        if (entity === "expenses" || entity === "expense_categories") {
            throw new Error("Unauthorized: Cashiers cannot manage expenses.");
        }
        if (action === "getById") {
            if (!entityId)
                throw new Error(`Unauthorized: Missing ${entity} ID.`);
            if (entity === "user") {
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.eq)(table.id, requester.id)); // ✅ Cashier can only see themselves
            }
            if (entity === "category") {
                return db_1.postgresDb.select().from(table).where((0, drizzle_orm_1.eq)(table.id, entityId)); // ✅ Cashier can view categories
            }
        }
        throw new Error(`Unauthorized: Cashiers cannot ${action} ${entity}s.`);
    }
    // ✅ Superadmin: Full access
    if (requester.role === "superadmin") {
        return db_1.postgresDb.select().from(table);
    }
    // ✅ Admin Access Rules
    if (requester.role === "admin") {
        if (action === "create") {
            return db_1.postgresDb.select().from(table);
        }
        if (action === "getAll") {
            // Special case for users - admins can see users they created
            if (entity === "user") {
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(table.id, requester.id), (0, drizzle_orm_1.eq)(createdByColumn, requester.id)));
            }
            // Special case for sales, receipts, expenses, and expense_categories - admins can ONLY see these from their team
            if (entity === "sales" || entity === "receipts" || entity === "expenses" || entity === "expense_categories") {
                // Get all users created by this admin
                const teamMembersResult = await db_1.postgresDb
                    .select({ memberId: schema_1.users.id })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
                const teamMemberIds = teamMembersResult.map((m) => m.memberId);
                // Admin can see their own sales/receipts/expenses and those from users they created
                if (teamMemberIds.length > 0) {
                    return db_1.postgresDb
                        .select()
                        .from(table)
                        .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(createdByColumn, requester.id), (0, drizzle_orm_1.inArray)(createdByColumn, teamMemberIds)));
                }
                else {
                    // If no team members, just show their own
                    return db_1.postgresDb
                        .select()
                        .from(table)
                        .where((0, drizzle_orm_1.eq)(createdByColumn, requester.id));
                }
            }
            // For other entities, admins can only see what they created
            return db_1.postgresDb
                .select()
                .from(table)
                .where((0, drizzle_orm_1.eq)(createdByColumn, requester.id));
        }
        if (!entityId) {
            throw new Error(`Unauthorized: Missing ${entity} ID.`);
        }
        // 🔹 Special Case for Users: Allow Admins to Fetch Their Own Data
        if (entity === "user") {
            return db_1.postgresDb
                .select()
                .from(table)
                .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(table.id, requester.id), (0, drizzle_orm_1.eq)(createdByColumn, requester.id))); // ✅ Admin can get their own user details
        }
        // Special case for sales, receipts, expenses, and expense_categories - admins can ONLY see these from their team
        if (entity === "sales" || entity === "receipts" || entity === "expenses" || entity === "expense_categories") {
            // Get all users created by this admin
            const teamMembersResult = await db_1.postgresDb
                .select({ memberId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
            const teamMemberIds = teamMembersResult.map((m) => m.memberId);
            if (teamMemberIds.length > 0) {
                // Admin can see their own sales/receipts/expenses and those from users they created
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(table.id, entityId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(createdByColumn, requester.id), (0, drizzle_orm_1.inArray)(createdByColumn, teamMemberIds))));
            }
            else {
                // If no team members, just show their own
                return db_1.postgresDb
                    .select()
                    .from(table)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(table.id, entityId), (0, drizzle_orm_1.eq)(createdByColumn, requester.id)));
            }
        }
        return db_1.postgresDb
            .select()
            .from(table)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(table.id, entityId), (0, drizzle_orm_1.eq)(createdByColumn, requester.id)));
    }
    throw new Error("Unauthorized: Invalid role.");
};
exports.authorizeAction = authorizeAction;
