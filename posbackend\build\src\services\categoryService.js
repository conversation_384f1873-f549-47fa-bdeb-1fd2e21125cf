"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCategoryById = exports.updateCategoryById = exports.getCategoryById = exports.getAllCategories = exports.createCategory = void 0;
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const schema_2 = require("../validation/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
// ✅ **Create Category**
const createCategory = async (requester, categoryData) => {
    schema_2.categorySchema.parse(categoryData);
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "category");
    const createdBy = requester.id;
    // ✅ Check if the user already has this category name
    const existingCategory = await db_1.postgresDb
        .select()
        .from(schema_1.categories)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.name, categoryData.name), (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, createdBy)))
        .limit(1);
    if (existingCategory.length > 0) {
        throw new Error("You already have a category with this name.");
    }
    // ✅ Create the category for this user
    const [newCategory] = await db_1.postgresDb
        .insert(schema_1.categories)
        .values({
        ...categoryData,
        createdBy,
        createdAt: new Date(),
    })
        .returning();
    if (!newCategory)
        throw new Error("Category creation failed.");
    return { category: newCategory };
};
exports.createCategory = createCategory;
// ✅ **Get All Categories**
const getAllCategories = async (requester, page = 1, limit = 10, searchTerm) => {
    const offset = (page - 1) * limit;
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "category");
    const isSuperadmin = requester.role === "superadmin";
    // Apply search filter if provided
    if (searchTerm && searchTerm.trim() !== '') {
        // Use case-insensitive search with wildcards on both sides
        const searchPattern = `%${searchTerm.trim()}%`;
        // Log the exact search pattern for debugging
        console.log(`Category search pattern: "${searchPattern}"`);
        // Create search condition
        const searchCondition = (0, drizzle_orm_1.or)((0, drizzle_orm_1.like)(schema_1.categories.name, searchPattern), (0, drizzle_orm_1.like)(schema_1.categories.description || '', searchPattern));
        // Combine with role-based condition
        if (isSuperadmin) {
            // Superadmin can see all categories that match search
            const [categoryData, totalCategories] = await Promise.all([
                db_1.postgresDb
                    .select()
                    .from(schema_1.categories)
                    .where(searchCondition)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.categories.createdAt))
                    .limit(limit)
                    .offset(offset),
                db_1.postgresDb
                    .select({ count: (0, drizzle_orm_1.count)() })
                    .from(schema_1.categories)
                    .where(searchCondition),
            ]);
            return {
                total: totalCategories[0].count,
                page,
                perPage: limit,
                categories: categoryData,
            };
        }
        else {
            // Others can only see their own categories that match search
            const [categoryData, totalCategories] = await Promise.all([
                db_1.postgresDb
                    .select()
                    .from(schema_1.categories)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id), searchCondition))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.categories.createdAt))
                    .limit(limit)
                    .offset(offset),
                db_1.postgresDb
                    .select({ count: (0, drizzle_orm_1.count)() })
                    .from(schema_1.categories)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id), searchCondition)),
            ]);
            return {
                total: totalCategories[0].count,
                page,
                perPage: limit,
                categories: categoryData,
            };
        }
    }
    // ✅ If no search term, fetch either all categories (for superadmin) or only the user's categories
    const [categoryData, totalCategories] = await Promise.all([
        db_1.postgresDb
            .select()
            .from(schema_1.categories)
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.categories.createdAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.categories)
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id)),
    ]);
    return {
        total: totalCategories[0].count,
        page,
        perPage: limit,
        categories: categoryData,
    };
};
exports.getAllCategories = getAllCategories;
// ✅ **Get Category by ID**
const getCategoryById = async (requester, id) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "category", id);
    const isSuperadmin = requester.role === "superadmin";
    const categoryData = await db_1.postgresDb
        .select()
        .from(schema_1.categories)
        .where(isSuperadmin ? (0, drizzle_orm_1.eq)(schema_1.categories.id, id) : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.id, id), (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id)))
        .limit(1);
    if (categoryData.length === 0)
        throw new Error("Category not found or unauthorized.");
    return categoryData[0];
};
exports.getCategoryById = getCategoryById;
// ✅ **Update Category**
const updateCategoryById = async (requester, categoryId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "category", categoryId);
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Ensure the category exists (Superadmin can update any, others only their own)
    const existingCategory = await db_1.postgresDb
        .select()
        .from(schema_1.categories)
        .where(isSuperadmin ? (0, drizzle_orm_1.eq)(schema_1.categories.id, categoryId) : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.id, categoryId), (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id)))
        .limit(1);
    if (existingCategory.length === 0) {
        throw new Error("Category not found or unauthorized.");
    }
    // ✅ Check if the new name already exists for this user (Superadmin is excluded from this check)
    if (!isSuperadmin && updateData.name) {
        const duplicateCategory = await db_1.postgresDb
            .select()
            .from(schema_1.categories)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.name, updateData.name), (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id), 
        // Exclude the current category being updated - use the 'ne' (not equal) operator
        (0, drizzle_orm_1.ne)(schema_1.categories.id, categoryId)))
            .limit(1);
        if (duplicateCategory.length > 0) {
            throw new Error("You already have a category with this name.");
        }
    }
    // ✅ Update the category
    const updatedCategory = await db_1.postgresDb
        .update(schema_1.categories)
        .set(updateData)
        .where((0, drizzle_orm_1.eq)(schema_1.categories.id, categoryId))
        .returning();
    if (!updatedCategory || updatedCategory.length === 0) {
        throw new Error("Update failed: Category not found.");
    }
    return { updatedCategory: updatedCategory[0] };
};
exports.updateCategoryById = updateCategoryById;
// ✅ **Delete Categories (Single or Multiple)**
const deleteCategoryById = async (requester, ids) => {
    const categoryIds = Array.isArray(ids) ? ids : [ids];
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Check authorization for each category
    for (const id of categoryIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "category", id);
        // ✅ Ensure the category exists (Superadmin can delete any, others only their own)
        const existingCategory = await db_1.postgresDb
            .select()
            .from(schema_1.categories)
            .where(isSuperadmin ? (0, drizzle_orm_1.eq)(schema_1.categories.id, id) : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.categories.id, id), (0, drizzle_orm_1.eq)(schema_1.categories.createdBy, requester.id)))
            .limit(1);
        if (existingCategory.length === 0) {
            throw new Error(`Category with ID ${id} not found or unauthorized.`);
        }
    }
    // ✅ Delete the categories
    const deletedCategories = await db_1.postgresDb
        .delete(schema_1.categories)
        .where((0, drizzle_orm_1.inArray)(schema_1.categories.id, categoryIds))
        .returning({ deletedId: schema_1.categories.id });
    if (!deletedCategories || deletedCategories.length === 0) {
        throw new Error("Delete failed: Categories not found.");
    }
    return {
        deletedIds: deletedCategories.map(category => category.deletedId)
    };
};
exports.deleteCategoryById = deleteCategoryById;
