import {
  pgTable, serial, varchar, integer, decimal, timestamp, text, index,
  unique, boolean
} from "drizzle-orm/pg-core";

import { sql } from "drizzle-orm";

// ======================= USERS TABLE =======================
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  email: varchar("email", { length: 100 }).unique().notNull(),
  phone: varchar("phone", { length: 20 }).unique().notNull(),
  passwordHash: varchar("password_hash").notNull(),
  role: varchar("role", { length: 20 }).notNull(),
  paymentStatus: varchar("payment_status", { length: 20 }).notNull().default("pending"),
  lastPaymentDate: timestamp("last_payment_date").defaultNow(),
  nextPaymentDue: timestamp("next_payment_due"), // ✅ NEW COLUMN ADDED
  createdBy: integer("created_by"),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  roleCheck: sql`CHECK (${table.role} IN ('superadmin', 'admin', 'cashier'))`,
  paymentStatusCheck: sql`CHECK (${table.paymentStatus} IN ('pending', 'paid', 'overdue', 'inactive'))`,
  idxEmail: index("idx_users_email").on(table.email),
  idxPhone: index("idx_users_phone").on(table.phone),
  idxRoleCreatedAt: index("idx_users_role_created_at").on(table.role, table.createdAt.desc()),
  idxCreatedByCreatedAt: index("idx_users_created_by_created_at").on(table.createdBy, table.createdAt.desc()),
  idxPaymentStatus: index("idx_users_payment_status").on(table.paymentStatus),
  idxLastPaymentDate: index("idx_users_last_payment_date").on(table.lastPaymentDate),
  idxNextPaymentDue: index("idx_users_next_payment_due").on(table.nextPaymentDue), // ✅ NEW INDEX ADDED
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));




// ======================= CATEGORIES TABLE =======================
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  description: text("description"),
  createdBy: integer("created_by").references(() => users.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxCategoryName: index("idx_categories_name").on(table.name),
}));


export const userCategories = pgTable("user_categories", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  categoryId: integer("category_id").notNull().references(() => categories.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxUserCategory: index("idx_user_category").on(table.userId, table.categoryId),
  uniqueUserCategory: unique("uq_user_category").on(table.userId, table.categoryId) // Prevent duplicate category links per user
}));



// ======================= PRODUCTS TABLE =======================
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  categoryId: integer("category_id")
    .references(() => categories.id, { onDelete: "cascade" })
    .notNull(),
  sku: varchar("sku", { length: 50 }).unique(),
  barcode: varchar("barcode", { length: 50 }).unique(),
  imageUrl: text("image_url"), // Add this line to store Cloudinary image URLs
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  cost: decimal("cost", { precision: 10, scale: 2 }).notNull(),
  stockQuantity: integer("stock_quantity").default(0).notNull(),
  minStockLevel: integer("min_stock_level").default(0),
  expiryDate: timestamp("expiry_date"),
  createdBy: integer("created_by").notNull(), // ✅ Ensure products always have an owner
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxBarcode: index("idx_products_barcode").on(table.barcode),
  idxSKU: index("idx_products_sku").on(table.sku),
  idxCategory: index("idx_products_category").on(table.categoryId),
  idxCreatedBy: index("idx_products_created_by").on(table.createdBy), // ✅ Index for fast filtering
  checkStock: sql`CHECK (${table.stockQuantity} >= 0)`,
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE CASCADE`
}));


// ======================= SALES TABLE =======================
export const sales = pgTable("sales", {
  id: serial("id").primaryKey(),
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
  paymentMethod: varchar("payment_method", { length: 20 }).notNull(),
  transactionDate: timestamp("transaction_date").defaultNow(),
  createdBy: integer("created_by").notNull(),
  storeId: integer("store_id"),
}, (table) => ({
  paymentMethodCheck: sql`CHECK (${table.paymentMethod} IN ('cash', 'card', 'paystack'))`,
  idxTransactionDate: index("idx_sales_transaction_date").on(table.transactionDate),
  idxStoreId: index("idx_sales_store_id").on(table.storeId),
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`,
  fkStoreId: sql`FOREIGN KEY (${table.storeId}) REFERENCES stores(id) ON DELETE SET NULL`
}));

// ======================= SALES ITEMS TABLE =======================
export const salesItems = pgTable("sales_items", {
  id: serial("id").primaryKey(),
  saleId: integer("sale_id").references(() => sales.id, { onDelete: "cascade" }).notNull(),
  productId: integer("product_id").references(() => products.id, { onDelete: "cascade" }).notNull(),
  quantity: integer("quantity").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
}, (table) => ({
  idxSale: index("idx_sales_items_sale").on(table.saleId),
  idxProduct: index("idx_sales_items_product").on(table.productId)
}));

// ======================= STOCK ADJUSTMENTS TABLE =======================
export const stockAdjustments = pgTable("stock_adjustments", {
  id: serial("id").primaryKey(),
  productId: integer("product_id").references(() => products.id, { onDelete: "cascade" }).notNull(),
  quantityChange: integer("quantity_change").notNull(),
  reason: text("reason").notNull(),
  adjustedBy: integer("adjusted_by").references(() => users.id, { onDelete: "set null" }),
  createdBy: integer("created_by"),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxProductAdjustments: index("idx_stock_adjustments_product").on(table.productId),
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));

// ======================= SUPPLIERS TABLE =======================
export const suppliers = pgTable("suppliers", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  contactPerson: varchar("contact_person", { length: 100 }),
  phone: varchar("phone", { length: 20 }).unique().notNull(),
  email: varchar("email", { length: 100 }), // Made optional by removing unique constraint
  address: text("address"),
  createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" }), // Track creator - FIXED: Removed notNull()
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxSupplierName: index("idx_suppliers_name").on(table.name),
  idxSupplierPhone: index("idx_suppliers_phone").on(table.phone),
  // Only index email if it exists
  idxSupplierEmail: index("idx_suppliers_email").on(table.email)
}));

export const userSuppliers = pgTable("user_suppliers", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  supplierId: integer("supplier_id").notNull().references(() => suppliers.id, { onDelete: "cascade" }),
  createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" }), // Track who linked them - FIXED: Removed notNull()
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxUserSupplier: index("idx_user_supplier").on(table.userId, table.supplierId),
  uniqueUserSupplier: unique("uq_user_supplier").on(table.userId, table.supplierId)
}));



// ======================= PURCHASES TABLE =======================
export const purchases = pgTable("purchases", {
  id: serial("id").primaryKey(),
  supplierId: integer("supplier_id").references(() => suppliers.id, { onDelete: "set null" }),
  productId: integer("product_id").references(() => products.id, { onDelete: "cascade" }).notNull(),
  quantity: integer("quantity").notNull(),
  costPrice: decimal("cost_price", { precision: 10, scale: 2 }).notNull(),
  totalCost: decimal("total_cost", { precision: 10, scale: 2 }).notNull(),
  purchaseDate: timestamp("purchase_date").defaultNow(),
  purchasedBy: integer("purchased_by").references(() => users.id, { onDelete: "set null" }),
  createdBy: integer("created_by"),
}, (table) => ({
  idxSupplier: index("idx_purchases_supplier").on(table.supplierId),
  idxProduct: index("idx_purchases_product").on(table.productId),
  idxPurchaseDate: index("idx_purchases_date").on(table.purchaseDate),
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));


// ======================= RECEIPTS TABLE =======================
export const receipts = pgTable("receipts", {
  id: serial("id").primaryKey(),
  saleId: integer("sale_id").references(() => sales.id, { onDelete: "cascade" }).notNull(),
  receiptUrl: text("receipt_url").notNull(),
  createdBy: integer("created_by"),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxReceiptSale: index("idx_receipts_sale").on(table.saleId),
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));

// ======================= STORES TABLE =======================
export const stores = pgTable("stores", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  address: text("address"),
  city: varchar("city", { length: 100 }),
  state: varchar("state", { length: 100 }),
  country: varchar("country", { length: 100 }),
  phone: varchar("phone", { length: 20 }),
  email: varchar("email", { length: 100 }),
  logo: text("logo"),
  website: varchar("website", { length: 255 }),
  taxId: varchar("tax_id", { length: 50 }),
  createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
}, (table) => ({
  idxStoreName: index("idx_stores_name").on(table.name),
  idxStoreCreatedBy: index("idx_stores_created_by").on(table.createdBy)
}));

// ======================= PAYMENTS TABLE =======================
export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  provider: varchar("provider", { length: 20 }).notNull(),
  transactionId: varchar("transaction_id", { length: 100 }).unique().notNull(),
  status: varchar("status", { length: 20 }).notNull().default("pending"),
  paidAt: timestamp("paid_at").defaultNow(),
  paystackReference: varchar("paystack_reference", { length: 100 }).unique(),
  authorizationCode: varchar("authorization_code", { length: 100 }),
  channel: varchar("channel", { length: 20 }),
  currency: varchar("currency", { length: 3 }).default("GHS"),
  subscriptionPeriod: integer("subscription_period").default(1) // Number of months (1, 3, 12)
}, (table) => ({
  providerCheck: sql`CHECK (${table.provider} IN ('paystack'))`,
  statusCheck: sql`CHECK (${table.status} IN ('pending', 'successful', 'failed', 'abandoned'))`,
  idxUser: index("idx_payments_user").on(table.userId),
  idxProvider: index("idx_payments_provider").on(table.provider),
  idxStatus: index("idx_payments_status").on(table.status)
}));

// ======================= EXPENSE CATEGORIES TABLE =======================
export const expenseCategories = pgTable("expense_categories", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  description: text("description"),
  color: varchar("color", { length: 7 }).default("#6B7280"), // Hex color for UI
  isDefault: boolean("is_default").default(false), // System default categories
  createdBy: integer("created_by").references(() => users.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxExpenseCategoryName: index("idx_expense_categories_name").on(table.name),
  idxCreatedBy: index("idx_expense_categories_created_by").on(table.createdBy)
}));

// ======================= EXPENSES TABLE =======================
export const expenses = pgTable("expenses", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  categoryId: integer("category_id")
    .references(() => expenseCategories.id, { onDelete: "set null" }),
  expenseDate: timestamp("expense_date").defaultNow(),
  paymentMethod: varchar("payment_method", { length: 20 }).notNull(),
  receiptUrl: text("receipt_url"), // Optional receipt/invoice attachment
  vendor: varchar("vendor", { length: 255 }), // Who was paid
  isRecurring: boolean("is_recurring").default(false),
  recurringFrequency: varchar("recurring_frequency", { length: 20 }), // monthly, weekly, yearly
  tags: text("tags"), // JSON array of tags for better categorization
  storeId: integer("store_id").references(() => stores.id, { onDelete: "set null" }),
  createdBy: integer("created_by").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
}, (table) => ({
  paymentMethodCheck: sql`CHECK (${table.paymentMethod} IN ('cash', 'card', 'mobile_money', 'bank_transfer', 'cheque'))`,
  recurringFrequencyCheck: sql`CHECK (${table.recurringFrequency} IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly') OR ${table.recurringFrequency} IS NULL)`,
  idxExpenseDate: index("idx_expenses_date").on(table.expenseDate),
  idxCategory: index("idx_expenses_category").on(table.categoryId),
  idxCreatedBy: index("idx_expenses_created_by").on(table.createdBy),
  idxStore: index("idx_expenses_store").on(table.storeId),
  idxAmount: index("idx_expenses_amount").on(table.amount),
  fkCreatedBy: sql`FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE CASCADE`
}));

// ======================= USER STORES TABLE =======================
export const userStores = pgTable("user_stores", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  storeId: integer("store_id").notNull().references(() => stores.id, { onDelete: "cascade" }),
  isDefault: boolean("is_default").default(false).notNull(),
  createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" }), // FIXED: Changed to SET NULL and removed notNull()
  createdAt: timestamp("created_at").defaultNow()
}, (table) => ({
  idxUserStore: index("idx_user_store").on(table.userId, table.storeId),
  uniqueUserStore: unique("uq_user_store").on(table.userId, table.storeId)
}));




