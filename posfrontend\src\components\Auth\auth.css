/* Custom styles for auth components */

/* Override Ant Design form validation error styles */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
  margin-top: 4px;
}

/* Style for input placeholders */
.ant-input::placeholder,
.ant-input-password input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for input text */
.ant-input,
.ant-input-password input {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for input icons */
.ant-input-password-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for form labels */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}
