import { User } from './user';

export type PaymentProvider = 'paystack';
export type PaymentStatus = 'pending' | 'successful' | 'failed' | 'abandoned';

export interface Payment {
  id: number;
  userId: number;
  amount: number;
  provider: PaymentProvider;
  transactionId: string;
  status: PaymentStatus;
  paidAt: string;
  paystackReference?: string;
  authorizationCode?: string;
  channel?: string;
  currency?: string;
  subscriptionPeriod?: number; // Number of months (1, 3, 12)
  user?: User; // Optional user information
}



export interface InitializePaymentDto {
  amount: number;
  email: string;
  callbackUrl?: string;
  subscriptionPeriod?: number; // Number of months (1, 3, 12)
}

export interface PaymentInitializationResponse {
  payment: Payment;
  authorizationUrl: string;
  accessCode: string;
  reference: string;
}

export interface PaginatedPayments {
  total: number;
  page: number;
  perPage: number;
  payments: Payment[];
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
}

export interface SubscriptionInfo {
  status: 'active' | 'inactive' | 'pending' | 'expired';
  startDate: string;
  endDate: string;
  nextBillingDate: string;
  amount: number;
}
