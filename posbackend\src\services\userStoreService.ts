import { and, eq, count, desc, inArray, or } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { stores, userStores, users } from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";

/**
 * Associate a user with a store
 * @param requester The user making the request
 * @param userId The user ID to associate
 * @param storeId The store ID to associate
 * @param isDefault Whether this is the default store for the user
 * @returns The created user-store association
 */
export const associateUserWithStore = async (
  requester: JwtPayload,
  userId: number,
  storeId: number,
  isDefault: boolean = false
) => {
  // Only admins and superadmins can associate users with stores
  if (requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: Only admins and superadmins can associate users with stores.");
  }

  // Check if the user exists
  const userExists = await postgresDb
    .select({ id: users.id })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  if (userExists.length === 0) {
    throw new Error("User not found.");
  }

  // Check if the store exists
  const storeExists = await postgresDb
    .select({ id: stores.id })
    .from(stores)
    .where(eq(stores.id, storeId))
    .limit(1);

  if (storeExists.length === 0) {
    throw new Error("Store not found.");
  }

  // Check if the association already exists
  const associationExists = await postgresDb
    .select({ id: userStores.id })
    .from(userStores)
    .where(
      and(
        eq(userStores.userId, userId),
        eq(userStores.storeId, storeId)
      )
    )
    .limit(1);

  if (associationExists.length > 0) {
    throw new Error("User is already associated with this store.");
  }

  // If this is the default store, unset any existing default
  if (isDefault) {
    await postgresDb
      .update(userStores)
      .set({ isDefault: false })
      .where(eq(userStores.userId, userId));
  }

  // Create the association
  const [newAssociation] = await postgresDb
    .insert(userStores)
    .values({
      userId,
      storeId,
      isDefault,
      createdBy: requester.id,
    })
    .returning();

  return newAssociation;
};

/**
 * Get all stores for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @returns List of stores associated with the user
 */
export const getUserStores = async (
  requester: JwtPayload,
  userId: number
) => {
  // Users can only see their own stores unless they're admin/superadmin
  if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: You can only view your own stores.");
  }

  // Get all store IDs associated with the user
  const userStoreAssociations = await postgresDb
    .select({
      storeId: userStores.storeId,
      isDefault: userStores.isDefault,
    })
    .from(userStores)
    .where(eq(userStores.userId, userId));

  const storeIds = userStoreAssociations.map((s: { storeId: number; isDefault: boolean }) => s.storeId);

  if (storeIds.length === 0) {
    return [];
  }

  // Get store details
  const storesData = await postgresDb
    .select({
      id: stores.id,
      name: stores.name,
      address: stores.address,
      city: stores.city,
      state: stores.state,
      country: stores.country,
      phone: stores.phone,
      email: stores.email,
      logo: stores.logo,
      website: stores.website,
      taxId: stores.taxId,
      createdBy: stores.createdBy,
      createdByName: users.name,
      createdAt: stores.createdAt,
      updatedAt: stores.updatedAt,
      isDefault: userStores.isDefault,
    })
    .from(stores)
    .leftJoin(users, eq(stores.createdBy, users.id))
    .leftJoin(
      userStores,
      and(
        eq(userStores.storeId, stores.id),
        eq(userStores.userId, userId)
      )
    )
    .where(inArray(stores.id, storeIds))
    .orderBy(desc(userStores.isDefault), desc(stores.createdAt));

  return storesData;
};

/**
 * Get the default store for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @returns The default store for the user
 */
export const getUserDefaultStore = async (
  requester: JwtPayload,
  userId: number
) => {
  // Users can only see their own default store unless they're admin/superadmin
  if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: You can only view your own default store.");
  }

  // Get the default store association
  const defaultStoreAssociation = await postgresDb
    .select({
      storeId: userStores.storeId,
    })
    .from(userStores)
    .where(
      and(
        eq(userStores.userId, userId),
        eq(userStores.isDefault, true)
      )
    )
    .limit(1);

  if (defaultStoreAssociation.length === 0) {
    // No default store found, try to get any store
    const anyStoreAssociation = await postgresDb
      .select({
        storeId: userStores.storeId,
      })
      .from(userStores)
      .where(eq(userStores.userId, userId))
      .limit(1);

    if (anyStoreAssociation.length === 0) {
      return null; // No stores associated with this user
    }

    // Get the store details
    const store = await postgresDb
      .select({
        id: stores.id,
        name: stores.name,
        address: stores.address,
        city: stores.city,
        state: stores.state,
        country: stores.country,
        phone: stores.phone,
        email: stores.email,
        logo: stores.logo,
        website: stores.website,
        taxId: stores.taxId,
        createdBy: stores.createdBy,
        createdByName: users.name,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
      })
      .from(stores)
      .leftJoin(users, eq(stores.createdBy, users.id))
      .where(eq(stores.id, anyStoreAssociation[0].storeId))
      .limit(1);

    return store.length > 0 ? { ...store[0], isDefault: false } : null;
  }

  // Get the default store details
  const store = await postgresDb
    .select({
      id: stores.id,
      name: stores.name,
      address: stores.address,
      city: stores.city,
      state: stores.state,
      country: stores.country,
      phone: stores.phone,
      email: stores.email,
      logo: stores.logo,
      website: stores.website,
      taxId: stores.taxId,
      createdBy: stores.createdBy,
      createdByName: users.name,
      createdAt: stores.createdAt,
      updatedAt: stores.updatedAt,
    })
    .from(stores)
    .leftJoin(users, eq(stores.createdBy, users.id))
    .where(eq(stores.id, defaultStoreAssociation[0].storeId))
    .limit(1);

  return store.length > 0 ? { ...store[0], isDefault: true } : null;
};

/**
 * Set the default store for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @param storeId The store ID to set as default
 * @returns The updated user-store association
 */
export const setUserDefaultStore = async (
  requester: JwtPayload,
  userId: number,
  storeId: number
) => {
  // Users can only set their own default store unless they're admin/superadmin
  if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: You can only set your own default store.");
  }

  // Check if the user is associated with the store
  const associationExists = await postgresDb
    .select({ id: userStores.id })
    .from(userStores)
    .where(
      and(
        eq(userStores.userId, userId),
        eq(userStores.storeId, storeId)
      )
    )
    .limit(1);

  if (associationExists.length === 0) {
    throw new Error("User is not associated with this store.");
  }

  // Unset any existing default
  await postgresDb
    .update(userStores)
    .set({ isDefault: false })
    .where(eq(userStores.userId, userId));

  // Set the new default
  const [updatedAssociation] = await postgresDb
    .update(userStores)
    .set({ isDefault: true })
    .where(
      and(
        eq(userStores.userId, userId),
        eq(userStores.storeId, storeId)
      )
    )
    .returning();

  return updatedAssociation;
};

/**
 * Remove a user-store association
 * @param requester The user making the request
 * @param userId The user ID
 * @param storeId The store ID
 * @returns The deleted association ID
 */
export const removeUserFromStore = async (
  requester: JwtPayload,
  userId: number,
  storeId: number
) => {
  // Only admins and superadmins can remove user-store associations
  if (requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: Only admins and superadmins can remove user-store associations.");
  }

  // Delete the association
  const deletedAssociation = await postgresDb
    .delete(userStores)
    .where(
      and(
        eq(userStores.userId, userId),
        eq(userStores.storeId, storeId)
      )
    )
    .returning({ deletedId: userStores.id });

  if (!deletedAssociation || deletedAssociation.length === 0) {
    throw new Error("Delete failed: Association not found.");
  }

  return { deletedId: deletedAssociation[0].deletedId };
};
