"use client";

import { useEffect, ReactNode, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter, usePathname } from "next/navigation";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useCheckPaymentStatus } from "@/hooks/useCheckPaymentStatus";
import dayjs from "dayjs";
import { clearUser } from "@/reduxRTK/services/authSlice";

interface PaymentGuardProps {
  children: ReactNode;
}

/**
 * PaymentGuard component to protect routes based on payment status
 * Redirects to payment page if payment is required
 * Superadmins bypass this check
 */
const PaymentGuard = ({ children }: PaymentGuardProps) => {
  const { user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();
  const pathname = usePathname();
  const { needsPayment, status, isActive, daysRemaining } = useCheckPaymentStatus();
  const prevPaymentStatus = useRef(user?.paymentStatus);
  const dispatch = useDispatch();

  console.log("🛡️ PaymentGuard COMPONENT RENDERED - This confirms PaymentGuard is being called");

  // Enhanced debug log to see payment status (always log for debugging payment issues)
  console.log("🛡️ PaymentGuard - Payment Status Check:", {
    userRole: user?.role,
    userPaymentStatus: user?.paymentStatus,
    needsPayment,
    status,
    isActive,
    daysRemaining,
    pathname,
    lastPaymentDate: user?.lastPaymentDate,
    nextPaymentDue: user?.nextPaymentDue,
    createdAt: user?.createdAt
  });

  // Check if user is in free trial period
  if (user?.createdAt && user?.paymentStatus === 'paid' && !user?.lastPaymentDate) {
    const daysSinceCreation = dayjs().diff(dayjs(user.createdAt), 'day');
    console.log("🎁 PaymentGuard - FREE TRIAL USER DETECTED:", {
      email: user.email,
      daysSinceCreation,
      isInFreeTrial: daysSinceCreation <= 90,
      trialDaysRemaining: Math.max(0, 90 - daysSinceCreation),
      paymentStatus: user.paymentStatus,
      nextPaymentDue: user.nextPaymentDue
    });
  }

  // Special debug for pending users
  if (user?.paymentStatus === 'pending') {
    console.log("🚨 PaymentGuard - PENDING USER DETECTED! Should redirect to payment page");
  }

  // Check if user might have stale data (recently paid but frontend shows pending)
  if (user?.paymentStatus !== 'paid' && typeof window !== 'undefined') {
    const lastPaymentCheck = sessionStorage.getItem('lastPaymentCheck');
    const now = Date.now();

    // If we haven't checked in the last 30 seconds, force a refresh
    if (!lastPaymentCheck || (now - parseInt(lastPaymentCheck)) > 30000) {
      console.log('🔄 PaymentGuard - Forcing user data refresh due to non-paid status');
      sessionStorage.setItem('lastPaymentCheck', now.toString());

      // Try to refresh user data
      if (typeof window !== 'undefined' && (window as any).__FORCE_REFRESH_USER_DATA) {
        (window as any).__FORCE_REFRESH_USER_DATA();
      }
    }
  }

  // Force logout and redirect to login if payment status is not paid
  useEffect(() => {
    if (!user) return;
    if (user.role === "superadmin") return;
    if (user.paymentStatus !== "paid") {
      router.replace("/payment");
    }
  }, [user, router]);

  useEffect(() => {
    // If no user, don't do anything (AuthGuard will handle this)
    if (!user) return;

    // Superadmins bypass payment checks
    if (user.role === "superadmin") return;

    // Check if we're on the profile page
    const isProfilePage = pathname.includes("/profile") || pathname.includes("/dashboard/profile");

    // Check if a profile update is in progress
    const isProfileUpdateInProgress = (window as any).__PROFILE_UPDATE_IN_PROGRESS === true;
    const lastProfileUpdatePath = (window as any).__LAST_PROFILE_UPDATE_PATH || '';

    // If we're on the profile page or a profile update is in progress, don't redirect
    if (isProfilePage || isProfileUpdateInProgress) {
      console.log("PaymentGuard - Skipping payment check:", {
        isProfilePage,
        isProfileUpdateInProgress,
        pathname,
        lastProfileUpdatePath
      });
      return;
    }

    // Check if current path is exempt from payment checks (payment page)
    const isExemptPath = pathname === "/payment";

    // Log the status
    console.log("PaymentGuard - Payment check status:", {
      pathname,
      isProfilePage,
      isExemptPath,
      needsPayment
    });

    // If payment is needed and not on an exempt page, redirect to payment page
    // Also check user's payment status directly to ensure paid users aren't redirected
    // ENHANCED: Force redirect for any non-paid status regardless of hook result
    const shouldRedirectToPayment = (needsPayment && !isExemptPath && user.paymentStatus !== 'paid') ||
                                   (!isExemptPath && user.paymentStatus !== 'paid');

    console.log("🔍 PaymentGuard - Redirect decision breakdown:", {
      needsPayment,
      isExemptPath,
      userPaymentStatus: user.paymentStatus,
      'user.paymentStatus !== "paid"': user.paymentStatus !== 'paid',
      shouldRedirectToPayment,
      pathname
    });

    // Special debug for pending users
    if (user.paymentStatus === 'pending') {
      console.log("🚨 PaymentGuard - PENDING USER REDIRECT CHECK:", {
        needsPayment,
        isExemptPath,
        userPaymentStatus: user.paymentStatus,
        shouldRedirectToPayment,
        'Will redirect?': shouldRedirectToPayment ? 'YES' : 'NO'
      });
    }

    if (shouldRedirectToPayment) {
      console.log("🚨 PaymentGuard - Redirecting to payment page from:", pathname);
      router.push("/payment");
    } else if (user.paymentStatus === 'paid' && needsPayment) {
      console.log("⚠️ PaymentGuard - User has paid status but needsPayment is true. This might indicate a cache issue.");
    } else if (user.paymentStatus === 'pending' && !shouldRedirectToPayment) {
      console.log("🚨 PaymentGuard - PENDING USER NOT REDIRECTED! This is the bug!");
    }
  }, [user, needsPayment, router, pathname]);

  // Live redirect if payment status changes from paid to not paid
  useEffect(() => {
    if (!user) return;
    if (user.role === "superadmin") return;
    if (
      user.paymentStatus !== "paid" &&
      typeof window !== "undefined" &&
      window.location.pathname !== "/payment"
    ) {
      router.replace("/payment");
    }
  }, [user, router]);

  // Show loading spinner while checking payment status
  if (!user) {
    return <LoadingSpinner tip="Checking payment status..." fullScreen />;
  }

  // Superadmins bypass payment checks
  if (user.role === "superadmin") {
    return <>{children}</>;
  }

  // Admin users no longer bypass payment checks
  // Removed admin bypass to enforce payment for all non-superadmin users

  // Check if we're on the profile page
  const isProfilePage = pathname.includes("/profile") || pathname.includes("/dashboard/profile");

  // Check if a profile update is in progress
  const isProfileUpdateInProgress = (window as any).__PROFILE_UPDATE_IN_PROGRESS === true;
  const lastProfileUpdatePath = (window as any).__LAST_PROFILE_UPDATE_PATH || '';

  // If we're on the profile page or a profile update is in progress, don't show loading spinner
  if (isProfilePage || isProfileUpdateInProgress) {
    // Log the status
    console.log("PaymentGuard (render) - Skipping payment check:", {
      isProfilePage,
      isProfileUpdateInProgress,
      pathname,
      lastProfileUpdatePath
    });

    // Continue to render children for profile pages or during profile updates
    return <>{children}</>;
  }

  // Check if current path is exempt from payment checks (payment page)
  const isExemptPath = pathname === "/payment";

  // If payment is needed and not on an exempt page, show loading
  // Also check user's payment status directly to ensure paid users don't see the loading spinner
  // ENHANCED: Force loading for any non-paid status regardless of hook result
  const shouldShowPaymentLoading = (needsPayment && !isExemptPath && user.paymentStatus !== 'paid') ||
                                   (!isExemptPath && user.paymentStatus !== 'paid');

  console.log("🎨 PaymentGuard (render) - Final render decision:", {
    needsPayment,
    isExemptPath,
    userPaymentStatus: user.paymentStatus,
    'user.paymentStatus !== "paid"': user.paymentStatus !== 'paid',
    shouldShowPaymentLoading,
    pathname
  });

  if (shouldShowPaymentLoading) {
    return <LoadingSpinner tip="Checking payment status..." fullScreen />;
  }

  return <>{children}</>;
};

export default PaymentGuard;
