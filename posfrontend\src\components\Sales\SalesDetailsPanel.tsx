"use client";

import React from "react";
import { Descriptions, Table, Spin, Empty, Tag, Button, Image } from "antd";
import {
  LoadingOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  CalendarOutlined,
  UserOutlined,
  CreditCardOutlined,
  ShopOutlined,
  PrinterOutlined
} from "@ant-design/icons";
import { useGetSaleByIdQuery, Sale, SaleItem } from "@/reduxRTK/services/salesApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import dayjs from "dayjs";
import "./sales-panels.css";
import { ApiResponse } from "@/types/user";

interface SalesDetailsPanelProps {
  sale: Sale | null;
  isOpen: boolean;
  onClose: () => void;
}

const SalesDetailsPanel: React.FC<SalesDetailsPanelProps> = ({ sale, isOpen, onClose }) => {

  const { data: saleDetails, isLoading } = useGetSaleByIdQuery(sale?.id || 0, {
    skip: !sale || !isOpen,
  }) as { data: ApiResponse<{sale: Sale & {items?: SaleItem[]}, saleItems?: SaleItem[]}> | undefined, isLoading: boolean };

  // Get sale items from API response
  const saleItems = React.useMemo(() => {
    // If we have saleItems from the API, use those
    if (saleDetails?.data?.saleItems && Array.isArray(saleDetails.data.saleItems) && saleDetails.data.saleItems.length > 0) {
      return saleDetails.data.saleItems;
    }

    // If we have items in the sale object, use those
    if (saleDetails?.data?.sale?.items && Array.isArray(saleDetails.data.sale.items) && saleDetails.data.sale.items.length > 0) {
      return saleDetails.data.sale.items;
    }

    // If we have no items from API, return an empty array
    return [];
  }, [saleDetails]);

  // Format currency
  const formatCurrency = (amount: string | number | undefined | null) => {
    if (amount === undefined || amount === null) return 'GHS 0.00';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `GHS ${numAmount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return dayjs(dateString).format("MMM D, YYYY HH:mm");
  };

  // Helper function to safely get sale data from different possible sources
  const getSaleData = (field: keyof Sale | 'createdByName' | 'items') => {
    // Special case for createdByName
    if (field === 'createdByName') {
      // If the sale has createdByName, use that
      if (saleDetails?.data?.sale?.createdByName) {
        return saleDetails.data.sale.createdByName;
      }
      if (sale?.createdByName) {
        return sale.createdByName;
      }
      // If we have a createdBy ID but no name, show "User ID: X"
      const createdBy = saleDetails?.data?.sale?.createdBy || sale?.createdBy;
      if (createdBy) {
        return `User ID: ${createdBy}`;
      }
      // Default fallback
      return null;
    }

    // Special case for items
    if (field === 'items') {
      // Return the saleItems array that we've already processed
      return saleItems;
    }

    // For other fields, try to get from API response first
    if (saleDetails?.data?.sale && field in saleDetails.data.sale) {
      return saleDetails.data.sale[field as keyof Sale];
    }
    // Then try from the prop
    if (sale && field in sale) {
      return sale[field as keyof Sale];
    }
    // Default fallback
    return null;
  };

  // Get payment method tag color
  const getPaymentMethodColor = (method: string) => {
    switch(method) {
      case 'cash': return 'green';
      case 'card': return 'blue';
      case 'mobile_money': return 'purple';
      default: return 'default';
    }
  };

  // Format payment method display
  const formatPaymentMethod = (method: string) => {
    return method?.replace("_", " ").toUpperCase() || "N/A";
  };

  // Panel footer with close button
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Close
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      title="Sale Details"
      isOpen={isOpen}
      onClose={onClose}
      width="500px"
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-full min-h-[300px]">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
        </div>
      ) : saleDetails?.data ? (
        <div className="p-4 sales-form">
          {/* Sale detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <ShoppingCartOutlined className="mr-2" />
              Sale #{saleDetails?.data?.sale?.id || sale?.id || 'N/A'}
            </h2>
            <p className="text-gray-600 mt-1 flex items-center">
              Complete sale information and details
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="sale-detail-light"
            labelStyle={{ color: '#333', backgroundColor: '#f5f5f5' }}
            contentStyle={{ color: '#333', backgroundColor: '#ffffff' }}
          >
            <Descriptions.Item label={<span><ShoppingCartOutlined /> Sale ID</span>}>
              {String(getSaleData('id')) || 'N/A'}
            </Descriptions.Item>

            <Descriptions.Item label={<span><DollarOutlined /> Total Amount</span>}>
              {formatCurrency(
                typeof getSaleData('totalAmount') === 'string' || typeof getSaleData('totalAmount') === 'number'
                  ? String(getSaleData('totalAmount'))
                  : '0'
              )}
            </Descriptions.Item>

            <Descriptions.Item label={<span><CreditCardOutlined /> Payment Method</span>}>
              {getSaleData('paymentMethod') ? (
                <Tag color={getPaymentMethodColor(getSaleData('paymentMethod') as string)}>
                  {formatPaymentMethod(getSaleData('paymentMethod') as string)}
                </Tag>
              ) : 'N/A'}
            </Descriptions.Item>

            <Descriptions.Item label={<span><CalendarOutlined /> Transaction Date</span>}>
              {formatDate(getSaleData('transactionDate') as string || '')}
            </Descriptions.Item>

            <Descriptions.Item label={<span><UserOutlined /> Created By</span>}>
              {String(getSaleData('createdByName')) || 'N/A'}
            </Descriptions.Item>

            <Descriptions.Item label={<span><ShopOutlined /> Store</span>}>
              {String(getSaleData('storeName') || 'N/A')}
            </Descriptions.Item>

            {getSaleData('receiptUrl') && (
              <Descriptions.Item label={<span><PrinterOutlined /> Receipt</span>}>
                <div className="flex flex-col items-center">
                  <Image
                    src={getSaleData('receiptUrl') as string}
                    alt="Receipt"
                    width={200}
                    className="border border-gray-700 rounded-md"
                    fallback="data:image/png;base64,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"
                  />
                  <Button
                    type="link"
                    href={getSaleData('receiptUrl') as string}
                    target="_blank"
                    className="mt-2"
                  >
                    View Full Receipt
                  </Button>
                </div>
              </Descriptions.Item>
            )}
          </Descriptions>

          <div className="mt-8 bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
              <ShoppingCartOutlined className="mr-2" /> Sale Items
            </h3>

            {/* Custom table implementation */}
            <div className="overflow-x-auto rounded-md border border-gray-200 shadow-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      PRODUCT
                    </th>
                    <th scope="col" className="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider w-24">
                      QUANTITY
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32">
                      PRICE
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32">
                      SUBTOT
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {saleItems.length > 0 ? (
                    saleItems.map((item, index) => (
                      <tr key={`sale-item-${index}`} className="hover:bg-gray-50 transition-colors duration-150">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                          {item.productName || "Unknown Product"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-center">
                          {item.quantity || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right">
                          GHS {(item.price ? parseFloat(item.price) : 0).toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right">
                          GHS {((item.price ? parseFloat(item.price) : 0) * (item.quantity || 0)).toFixed(2)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-8 text-center text-sm text-gray-500">
                        No items found for this sale
                      </td>
                    </tr>
                  )}
                </tbody>
                <tfoot className="bg-gray-100">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800">
                      Total
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-center">
                      {saleItems.length > 0
                        ? saleItems.reduce((total, item) => total + (item.quantity || 0), 0)
                        : 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-right">
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600 text-right">
                      GHS {saleItems.length > 0
                        ? saleItems.reduce((total, item) => {
                            const price = item.price ? parseFloat(item.price) : 0;
                            const quantity = item.quantity || 0;
                            return total + (price * quantity);
                          }, 0).toFixed(2)
                        : (typeof getSaleData('totalAmount') === 'string'
                            ? parseFloat(getSaleData('totalAmount') as string)
                            : parseFloat(String(getSaleData('totalAmount') || '0'))).toFixed(2)
                      }
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-white h-full flex items-center justify-center">
          <Empty description="Sale details not found" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      )}
    </SlidingPanel>
  );
};

export default SalesDetailsPanel;
