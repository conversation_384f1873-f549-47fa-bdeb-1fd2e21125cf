import { Request, Response, NextFunction } from "express";
import { DecodedToken } from "../types/type";
import { getUserById } from "../services/authService"; // Ensure this function fetches the user from the DB
import {
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  asyncHandler
} from "./errorHandler";

export const checkPaymentStatus = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const requester = req.user as DecodedToken;

  if (!requester) {
    throw new AuthenticationError("Unauthorized: User not authenticated.");
  }

  // Superadmins bypass payment checks
  if (requester.role === "superadmin") {
    return next();
  }

  // Fetch the authenticated user's payment status
  const user = await getUserById(requester, requester.id);

  if (!user) {
    throw new NotFoundError("User not found.");
  }

  console.log("Checking Payment Status for:", (user as any).id, (user as any).paymentStatus);

  // Check if payment status requires payment
  if ((user as any).paymentStatus && ["pending", "overdue", "inactive"].includes((user as any).paymentStatus.toLowerCase())) {
    throw new AuthorizationError("Access denied: Payment required to continue using the app.");
  }

  // Payment status is valid, proceed to the next middleware
  next();
});

