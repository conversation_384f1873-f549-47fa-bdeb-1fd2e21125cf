"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deletePurchaseById = exports.updatePurchaseById = exports.getPurchaseById = exports.getAllPurchases = exports.createPurchase = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const schema_2 = require("../validation/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
/**
 * ✅ **Create a Purchase**
 */
const createPurchase = async (requester, purchaseData) => {
    schema_2.purchaseSchema.parse(purchaseData);
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "purchases");
    const createdBy = requester.id;
    // ✅ Ensure product exists
    const productExists = await db_1.postgresDb
        .select()
        .from(schema_1.products)
        .where((0, drizzle_orm_1.eq)(schema_1.products.id, purchaseData.productId))
        .limit(1);
    if (productExists.length === 0) {
        throw new Error("Product not found.");
    }
    // ✅ Insert new purchase record
    const [newPurchase] = await db_1.postgresDb
        .insert(schema_1.purchases)
        .values({
        ...purchaseData,
        purchasedBy: createdBy,
        createdBy,
    })
        .returning();
    if (!newPurchase)
        throw new Error("Purchase creation failed.");
    return { purchase: newPurchase };
};
exports.createPurchase = createPurchase;
/**
 * ✅ **Get All Purchases (Paginated)**
 */
const getAllPurchases = async (requester, page = 1, limit = 10) => {
    const offset = (page - 1) * limit;
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "purchases");
    const isSuperadmin = requester.role === "superadmin";
    const [purchasesData, totalPurchases] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.purchases.id,
            supplier: schema_1.suppliers.name,
            product: schema_1.products.name,
            quantity: schema_1.purchases.quantity,
            costPrice: schema_1.purchases.costPrice,
            totalCost: schema_1.purchases.totalCost,
            purchaseDate: schema_1.purchases.purchaseDate,
            purchasedBy: schema_1.users.name,
        })
            .from(schema_1.purchases)
            .leftJoin(schema_1.suppliers, (0, drizzle_orm_1.eq)(schema_1.purchases.supplierId, schema_1.suppliers.id))
            .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.purchases.productId, schema_1.products.id))
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.purchases.purchasedBy, schema_1.users.id))
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.purchases.purchaseDate))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.purchases)
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id)),
    ]);
    return {
        total: totalPurchases[0].count,
        page,
        perPage: limit,
        purchases: purchasesData,
    };
};
exports.getAllPurchases = getAllPurchases;
/**
 * ✅ **Get Purchase by ID**
 */
const getPurchaseById = async (requester, id) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "purchases", id);
    const isSuperadmin = requester.role === "superadmin";
    const purchaseData = await db_1.postgresDb
        .select({
        id: schema_1.purchases.id,
        supplier: schema_1.suppliers.name,
        product: schema_1.products.name,
        quantity: schema_1.purchases.quantity,
        costPrice: schema_1.purchases.costPrice,
        totalCost: schema_1.purchases.totalCost,
        purchaseDate: schema_1.purchases.purchaseDate,
        purchasedBy: schema_1.users.name,
    })
        .from(schema_1.purchases)
        .leftJoin(schema_1.suppliers, (0, drizzle_orm_1.eq)(schema_1.purchases.supplierId, schema_1.suppliers.id))
        .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.purchases.productId, schema_1.products.id))
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.purchases.purchasedBy, schema_1.users.id))
        .where(isSuperadmin
        ? (0, drizzle_orm_1.eq)(schema_1.purchases.id, id)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.purchases.id, id), (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id)))
        .limit(1);
    if (purchaseData.length === 0)
        throw new Error("Purchase not found or unauthorized.");
    return purchaseData[0];
};
exports.getPurchaseById = getPurchaseById;
/**
 * ✅ **Update Purchase by ID**
 */
const updatePurchaseById = async (requester, purchaseId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "purchases", purchaseId);
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Ensure the purchase exists
    const existingPurchase = await db_1.postgresDb
        .select()
        .from(schema_1.purchases)
        .where(isSuperadmin
        ? (0, drizzle_orm_1.eq)(schema_1.purchases.id, purchaseId)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.purchases.id, purchaseId), (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id)))
        .limit(1);
    if (existingPurchase.length === 0) {
        throw new Error("Purchase not found or unauthorized.");
    }
    // ✅ Update the purchase record
    const updatedPurchase = await db_1.postgresDb
        .update(schema_1.purchases)
        .set(updateData)
        .where((0, drizzle_orm_1.eq)(schema_1.purchases.id, purchaseId))
        .returning();
    if (!updatedPurchase || updatedPurchase.length === 0) {
        throw new Error("Update failed: Purchase not found.");
    }
    return { updatedPurchase: updatedPurchase[0] };
};
exports.updatePurchaseById = updatePurchaseById;
/**
 * ✅ **Delete Purchase by ID (Single or Multiple)**
 */
const deletePurchaseById = async (requester, ids) => {
    const purchaseIds = Array.isArray(ids) ? ids : [ids];
    const isSuperadmin = requester.role === "superadmin";
    // Check authorization for each purchase
    for (const id of purchaseIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "purchases", id);
        // Ensure the purchase exists
        const existingPurchase = await db_1.postgresDb
            .select()
            .from(schema_1.purchases)
            .where(isSuperadmin
            ? (0, drizzle_orm_1.eq)(schema_1.purchases.id, id)
            : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.purchases.id, id), (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id)))
            .limit(1);
        if (existingPurchase.length === 0) {
            throw new Error(`Purchase with ID ${id} not found or unauthorized.`);
        }
    }
    // Delete the purchases
    const deletedPurchases = await db_1.postgresDb
        .delete(schema_1.purchases)
        .where(isSuperadmin
        ? (0, drizzle_orm_1.inArray)(schema_1.purchases.id, purchaseIds)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.purchases.id, purchaseIds), (0, drizzle_orm_1.eq)(schema_1.purchases.createdBy, requester.id)))
        .returning({ deletedId: schema_1.purchases.id });
    if (!deletedPurchases || deletedPurchases.length === 0) {
        throw new Error("Delete failed: Purchase(s) not found.");
    }
    return {
        deletedIds: deletedPurchases.map((purchase) => purchase.deletedId)
    };
};
exports.deletePurchaseById = deletePurchaseById;
