"use client";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import { useRouter } from "next/navigation";
import { useCheckPaymentStatus } from "@/hooks/useCheckPaymentStatus";

const PaymentNotification: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();
  const [showNotification, setShowNotification] = useState(true);
  const { daysRemaining, needsPayment, status } = useCheckPaymentStatus();

  // Don't show notification for superadmins or if notification is dismissed or payment is not needed
  if (!user || !showNotification || !needsPayment || user.role === "superadmin") {
    return null;
  }

  const handleMakePayment = () => {
    router.push("/payment");
  };

  const getNotificationType = () => {
    if (status === "overdue" || (daysRemaining !== null && daysRemaining < 0)) {
      return "error";
    } else if (daysRemaining !== null && daysRemaining <= 3) {
      return "warning";
    } else {
      return "info";
    }
  };

  const getMessage = () => {
    if (status === "overdue" || (daysRemaining !== null && daysRemaining < 0)) {
      return "Your subscription payment is overdue. Please make a payment to continue using the system.";
    } else if (daysRemaining === 0) {
      return "Your subscription payment is due today. Please make a payment to avoid service interruption.";
    } else if (daysRemaining !== null) {
      return `Your subscription payment is due in ${daysRemaining} day${daysRemaining > 1 ? 's' : ''}.`;
    } else if (status === "pending") {
      return "Your payment is pending verification. If you've already made a payment, please wait for confirmation.";
    } else {
      return "Your subscription is inactive. Please make a payment to activate your account.";
    }
  };

  return (
    <Alert
      message={`Payment Reminder: ${getMessage()}`}
      type={getNotificationType()}
      showIcon
      action={
        <Button size="small" type="primary" onClick={handleMakePayment}>
          Make Payment
        </Button>
      }
      closable
      onClose={() => setShowNotification(false)}
      className="mb-4"
    />
  );
};

export default PaymentNotification;
