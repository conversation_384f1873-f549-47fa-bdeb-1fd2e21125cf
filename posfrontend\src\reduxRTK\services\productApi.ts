// services/productApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Product types
export interface Product {
  id: number;
  name: string;
  categoryId: number;
  sku?: string;
  barcode?: string;
  price: string;
  cost: string;
  stockQuantity: number;
  minStockLevel?: number;
  expiryDate?: string | null;
  createdBy: number;
  createdAt: string;
  imageUrl?: string | null;
}

export interface PaginatedProducts {
  total: number;
  page: number;
  perPage: number;
  products: Product[];
}

export interface CreateProductDto {
  name: string;
  categoryId: number;
  sku?: string;
  barcode?: string;
  price: number;
  cost: number;
  stockQuantity?: number;
  minStockLevel?: number;
  expiryDate?: string | null;
  imageUrl?: string | null;
}

export interface UpdateProductDto {
  name?: string;
  categoryId?: number;
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  stockQuantity?: number;
  minStockLevel?: number;
  expiryDate?: string | null;
  imageUrl?: string | null;
}

export const productApi = createApi({
  reducerPath: 'productApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Product'] as const,
  endpoints: (builder) => ({
    // Get all products (paginated)
    getAllProducts: builder.query<ApiResponse<PaginatedProducts>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
          },
          token,
        };
      },
      keepUnusedDataFor: 0, // Don't keep data in cache
      providesTags: ['Product'],
    }),

    // Get product by ID
    getProductById: builder.query<ApiResponse<Product>, number>({
      query: (productId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'retrieve',
            productId,
          },
          token,
        };
      },
      keepUnusedDataFor: 0, // Don't keep data in cache
      providesTags: (_result, _error, id) => [{ type: 'Product', id }],
    }),

    // Get product by barcode
    getProductByBarcode: builder.query<ApiResponse<Product>, string>({
      query: (barcode): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'barcode',
            barcode,
          },
          token,
        };
      },
      keepUnusedDataFor: 0, // Don't keep data in cache
      providesTags: (_result, _error, barcode) => [{ type: 'Product', id: `barcode-${barcode}` }],
    }),

    // Create new product
    createProduct: builder.mutation<ApiResponse<Product>, CreateProductDto>({
      query: (productData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        // Backend expects an array of products
        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'createnew',
            productsData: [productData], // Wrap in array to match backend expectation
          },
          token,
        };
      },
      invalidatesTags: ['Product'],
    }),

    // Update product
    updateProduct: builder.mutation<ApiResponse<Product>, { productId: number; data: UpdateProductDto }>({
      query: ({ productId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'update',
            productId,
            ...data,
          },
          token,
        };
      },
      // Invalidate all Product tags to ensure the list is refreshed
      invalidatesTags: ['Product'],
    }),

    // Delete product (single)
    deleteProduct: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (productId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'delete',
            productId,
          },
          token,
        };
      },
      invalidatesTags: ['Product'],
    }),

    // Bulk delete products
    bulkDeleteProducts: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (productIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/products',
          payloaddata: {
            mode: 'delete',
            productIds,
          },
          token,
        };
      },
      invalidatesTags: ['Product'],
    }),
  }),
});

export const {
  useGetAllProductsQuery,
  useGetProductByIdQuery,
  useGetProductByBarcodeQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useBulkDeleteProductsMutation,
} = productApi;
