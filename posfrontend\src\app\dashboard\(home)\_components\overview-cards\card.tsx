import { ArrowDownIcon, ArrowUpIcon } from "@/assets/icons";
import { cn } from "@/lib/utils";
import type { JSX, SVGProps } from "react";

type PropsType = {
  label: string;
  data: {
    value: number | string;
    growthRate: number | string;
  };
  Icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
};

export function OverviewCard({ label, data, Icon }: PropsType) {
  // Check if growth rate is negative by looking at the first character if it's a string
  const isDecreasing = typeof data.growthRate === 'number'
    ? data.growthRate < 0
    : data.growthRate.toString().startsWith('-');

  return (
    <div className="rounded-[10px] bg-white p-6 shadow-1 border border-gray-200">
      <Icon />

      <div className="mt-6 flex items-end justify-between">
        <dl>
          <dt className="mb-1.5 text-heading-6 font-bold text-gray-800">
            {data.value}
          </dt>

          <dd className="text-sm font-medium text-gray-600">{label}</dd>
        </dl>

        {data.growthRate && (
          <dl
            className={cn(
              "text-sm font-medium pr-3", // Added right padding
              data.growthRate === "Declining" ? "text-red" :
              data.growthRate === "New" ? "text-blue-500" :
              data.growthRate === "Growing" ? "text-green" :
              data.growthRate === "Stable" ? "text-amber-500" : "text-gray-500"
            )}
          >
            <dt className="flex items-center gap-1.5 whitespace-nowrap">
              <span>{data.growthRate}</span>
              {data.growthRate === "Declining" ? (
                <ArrowDownIcon aria-hidden />
              ) : data.growthRate === "Growing" || data.growthRate === "New" ? (
                <ArrowUpIcon aria-hidden />
              ) : null}
            </dt>

            <dd className="sr-only">
              {label} status: {data.growthRate}
            </dd>
          </dl>
        )}
      </div>
    </div>
  );
}
