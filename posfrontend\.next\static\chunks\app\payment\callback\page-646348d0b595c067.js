(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4891],{74936:(e,t,a)=>{Promise.resolve().then(a.bind(a,22506))},22506:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(95155),r=a(12115),n=a(76046),o=a(72093),i=a(20148),c=a(43316),l=a(16419),d=a(1227),u=a(98195),m=a(78767),f=a(63065),y=a(83391),h=a(75912),g=a(19840);let p=()=>{let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),a=(0,y.wA)(),{user:p,accessToken:w}=(0,y.d4)(e=>e.auth),[x]=(0,m.Ex)(),{refetch:b}=(0,f.go)(void 0,{skip:!0}),[v,P]=(0,r.useState)("loading"),[S,D]=(0,r.useState)(""),j=(0,r.useCallback)(async e=>{try{console.log("\uD83D\uDD0D Verifying payment with reference:",e);let t=await x({reference:e}).unwrap();if(t.success){if(P("success"),D("Payment verified successfully! Your subscription is now active."),(0,h.r)("success","Payment successful! Your subscription is now active."),console.log("✅ Payment verification successful, updating user data..."),p&&w)try{let e=await (0,g.hB)({dispatch:a,accessToken:w,currentUser:p,maxRetries:5,retryDelay:2e3});e.success&&e.user?(console.log("\uD83C\uDF89 User data refresh successful!"),(0,g.jH)(e.user)?console.log("✅ Payment status verification passed"):console.log("⚠️ Payment status verification failed, but proceeding anyway")):console.log("⚠️ User data refresh failed:",e.error);try{window.__FORCE_REFRESH_USER_DATA&&await window.__FORCE_REFRESH_USER_DATA(),localStorage.removeItem("user_cache"),localStorage.removeItem("payment_status_cache"),await new Promise(e=>setTimeout(e,1e3)),window.location.replace("/dashboard")}catch(e){console.error("Error refreshing user data:",e),window.location.replace("/dashboard")}}catch(e){console.error("❌ Error in user data refresh:",e),setTimeout(()=>{window.location.href="/dashboard"},1e3)}else setTimeout(()=>{window.location.href="/dashboard"},1e3)}else P("failed"),D(t.message||"Payment verification failed."),console.error("❌ Payment verification failed:",t.message)}catch(e){var t;console.error("❌ Payment verification error:",e),P("failed"),D((null===(t=e.data)||void 0===t?void 0:t.message)||e.message||"Payment verification failed.")}},[x,p,w,a]);(0,r.useEffect)(()=>{let e=null==t?void 0:t.get("reference"),a=null==t?void 0:t.get("trxref"),s=e||a;s?j(s):(P("failed"),D("No payment reference found in the URL."))},[t,j]);let A=async()=>{if(p&&w&&"success"===v)try{await (0,g.hB)({dispatch:a,accessToken:w,currentUser:p,maxRetries:3,retryDelay:1e3}),window.location.href="/dashboard"}catch(e){console.error("Error in manual refresh:",e),window.location.href="/dashboard"}else window.location.href="/dashboard"};return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,s.jsx)("div",{className:"w-full max-w-md rounded-lg bg-white p-6 shadow-md",children:(0,s.jsxs)("div",{className:"text-center",children:["loading"===v&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{indicator:(0,s.jsx)(l.A,{style:{fontSize:48},spin:!0})}),(0,s.jsx)("h2",{className:"mb-2 mt-4 text-xl font-semibold",children:"Verifying Payment"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Please wait while we verify your payment..."})]}),"success"===v&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{style:{fontSize:48,color:"#52c41a"}}),(0,s.jsx)("h2",{className:"mb-2 mt-4 text-xl font-semibold text-green-600",children:"Payment Successful!"}),(0,s.jsx)("p",{className:"mb-4 text-gray-600",children:S}),(0,s.jsx)(i.A,{message:"Subscription Activated",description:"You will be redirected to your dashboard in a few seconds.",type:"success",showIcon:!0,className:"mb-4"}),(0,s.jsx)(c.Ay,{type:"primary",onClick:A,className:"w-full",children:"Go to Dashboard"})]}),"failed"===v&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{style:{fontSize:48,color:"#ff4d4f"}}),(0,s.jsx)("h2",{className:"mb-2 mt-4 text-xl font-semibold text-red-600",children:"Payment Failed"}),(0,s.jsx)("p",{className:"mb-4 text-gray-600",children:S}),(0,s.jsx)(i.A,{message:"Payment Verification Failed",description:"Please try again or contact support if the issue persists.",type:"error",showIcon:!0,className:"mb-4"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.Ay,{type:"primary",onClick:()=>{e.push("/payment")},className:"w-full",children:"Try Again"}),(0,s.jsx)(c.Ay,{onClick:A,className:"w-full",children:"Return to Dashboard"})]})]})]})})})}},19840:(e,t,a)=>{"use strict";a.d(t,{hB:()=>n,jH:()=>o});var s=a(7875),r=a(63065);let n=async e=>{let{dispatch:t,accessToken:a,currentUser:n,maxRetries:o=3,retryDelay:i=2e3}=e;console.log("\uD83D\uDD04 Starting user data refresh after payment..."),t(r.i$.util.invalidateTags(["User"])),t(r.i$.util.resetApiState()),console.log("\uD83E\uDDF9 Cleared all RTK Query cache");for(let e=1;e<=o;e++)try{var c;console.log("\uD83D\uDCE1 Attempt ".concat(e,"/").concat(o," - Fetching fresh user data...")),e>1&&await new Promise(e=>setTimeout(e,i));let l=await t(r.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in l&&(null===(c=l.data)||void 0===c?void 0:c.success)&&l.data.data){let r=l.data.data;if(console.log("✅ Successfully fetched updated user data:",{id:r.id,paymentStatus:r.paymentStatus,lastPaymentDate:r.lastPaymentDate,nextPaymentDue:r.nextPaymentDue,attempt:e}),t((0,s.gV)({user:r,accessToken:a})),"paid"===r.paymentStatus)return console.log('\uD83C\uDF89 Payment status successfully updated to "paid"'),{success:!0,user:r};if(console.log('⚠️ Payment status is "'.concat(r.paymentStatus,'", not "paid". Retrying...')),e===o){console.log("⚠️ Max retries reached, using fallback update");let e={...n,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return t((0,s.gV)({user:e,accessToken:a})),{success:!0,user:e}}continue}if(console.log("⚠️ Attempt ".concat(e,": Backend response not successful or no data")),e===o)throw Error("Failed to fetch user data from backend");continue}catch(r){if(console.error("❌ Attempt ".concat(e," failed:"),r),e===o){if(console.log("\uD83D\uDCA5 All attempts failed, using fallback update"),!n)return{success:!1,error:"Failed to refresh user data and no current user for fallback"};{let e={...n,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return t((0,s.gV)({user:e,accessToken:a})),console.log("✅ Fallback: Updated user payment status in Redux"),{success:!0,user:e}}}}return{success:!1,error:"Unexpected error in user data refresh"}},o=e=>{if(!e)return console.log("❌ No user data to verify"),!1;let t="paid"===e.paymentStatus,a=e.lastPaymentDate&&new Date(e.lastPaymentDate).getTime()>Date.now()-3e5;return console.log("\uD83D\uDD0D Payment status verification:",{paymentStatus:e.paymentStatus,isPaymentStatusPaid:t,lastPaymentDate:e.lastPaymentDate,hasRecentPaymentDate:a,nextPaymentDue:e.nextPaymentDue}),t}},75912:(e,t,a)=>{"use strict";a.d(t,{r:()=>r});var s=a(55037);let r=(e,t)=>{"success"===e?s.oR.success(t):"error"===e?s.oR.error(t):"warning"===e&&(0,s.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};r.success=e=>r("success",e),r.error=e=>r("error",e),r.warning=e=>r("warning",e)}},e=>{var t=t=>e(e.s=t);e.O(0,[6754,1961,2261,4831,3316,2093,5037,9579,821,8441,1517,7358],()=>t(74936)),_N_E=e.O()}]);