"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin, Empty, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ShopOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  UserOutlined,
  LoadingOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { useGetAllStoresQuery } from "@/reduxRTK/services/storeApi";
import { Store } from "@/types/store";
import StoresPagination from "./StoresPagination";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { useStoreDelete } from "@/hooks/stores/useStoreDelete";
import dayjs from "dayjs";

interface StoresTableProps {
  search: string;
  onEdit: (store: Store) => void;
  onView: (store: Store) => void;
  onSuccess: () => void;
  onSelect?: (store: Store | null) => void;
  onBulkDelete?: (storeIds: number[]) => void;
  isMobile?: boolean;
}

const StoresTable: React.FC<StoresTableProps> = ({
  search,
  onEdit,
  onView,
  onSuccess,
  onSelect,
  onBulkDelete,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRowKey, setSelectedRowKey] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [storeToDelete, setStoreToDelete] = useState<number | null>(null);

  // State for selected stores (bulk delete)
  const [selectedStores, setSelectedStores] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Fetch stores
  const {
    data: storesData,
    isLoading,
    isFetching,
    refetch,
  } = useGetAllStoresQuery({
    page: currentPage,
    limit: pageSize,
    search,
  });

  // Delete store handler
  const { deleteStore, isDeleting } = useStoreDelete(() => {
    setIsDeleteDialogOpen(false);
    onSuccess();
  });

  // Handle delete button click
  const handleDeleteClick = (storeId: number) => {
    setStoreToDelete(storeId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete store
  const confirmDeleteStore = async () => {
    if (storeToDelete) {
      await deleteStore(storeToDelete);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setStoreToDelete(null);
  };

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all stores
      const storeIds = stores.map(store => store.id);
      setSelectedStores(storeIds);
    } else {
      // Deselect all stores
      setSelectedStores([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (storeId: number, checked: boolean) => {
    if (checked) {
      setSelectedStores(prev => [...prev, storeId]);
    } else {
      setSelectedStores(prev => prev.filter(id => id !== storeId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedStores.length > 0 && onBulkDelete) {
      onBulkDelete(selectedStores);
      setSelectedStores([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No stores selected',
        description: 'Please select at least one store to delete.',
      });
    }
  };

  // Handle pagination change
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  // Handle row selection
  const handleRowClick = (store: Store) => {
    if (selectedRowKey === store.id) {
      setSelectedRowKey(null);
      if (onSelect) onSelect(null);
    } else {
      setSelectedRowKey(store.id);
      if (onSelect) onSelect(store);
    }
  };

  // Get stores data
  const stores = storesData?.data?.stores || [];

  // Empty state is now handled in the parent component

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteStore}
        title="Delete Store"
        message="Are you sure you want to delete this store? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Button - Show only when stores are selected */}
      {selectedStores.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedStores.length} {selectedStores.length === 1 ? 'store' : 'stores'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 200px 150px 150px"
          minWidth="800px"
        >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={stores.length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          <span className="flex items-center">
            <ShopOutlined className="mr-1" />
            Name
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <EnvironmentOutlined className="mr-1" />
            Location
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <PhoneOutlined className="mr-1" />
            Contact
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <UserOutlined className="mr-1" />
            Created By
          </span>
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
          {/* Mobile Rows */}
          {stores.map((store) => (
            <TableRow
              key={store.id}
              selected={selectedStores.includes(store.id)}
            >
              <TableCell className="text-center">
                <Checkbox
                  checked={selectedStores.includes(store.id)}
                  onChange={(e) => handleCheckboxChange(store.id, e.target.checked)}
                />
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {store.name}
                </div>
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis text-gray-600">
                  {[store.address, store.city, store.state, store.country]
                    .filter(Boolean)
                    .join(", ") || "N/A"}
                </div>
              </TableCell>
              <TableCell>
                {store.phone || store.email ? (
                  <div className="max-w-[130px] overflow-hidden text-ellipsis">
                    {store.phone && <div className="font-mono text-sm">{store.phone}</div>}
                    {store.email && <div className="text-blue-600 text-sm">{store.email}</div>}
                  </div>
                ) : (
                  "N/A"
                )}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onView(store);
                      }}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  <Tooltip title="Edit">
                    <Button
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdit(store);
                      }}
                      type="text"
                      className="edit-button text-blue-500 hover:text-blue-400"
                      size="small"
                    />
                  </Tooltip>
                  <Tooltip title="Delete">
                    <Button
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(store.id);
                      }}
                      type="text"
                      className="delete-button text-red-500 hover:text-red-400"
                      size="small"
                    />
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={stores.length === 0}
                  />
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShopOutlined className="mr-1" />
                    Name
                  </span>
                </th>

                {/* Location Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <EnvironmentOutlined className="mr-1" />
                    Location
                  </span>
                </th>

                {/* Contact Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <PhoneOutlined className="mr-1" />
                    Contact
                  </span>
                </th>

                {/* Created By Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <UserOutlined className="mr-1" />
                    Created By
                  </span>
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {stores.map((store) => (
                <tr key={store.id} className={selectedStores.includes(store.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    <Checkbox
                      checked={selectedStores.includes(store.id)}
                      onChange={(e) => handleCheckboxChange(store.id, e.target.checked)}
                    />
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {store.name}
                    </div>
                  </td>

                  {/* Location Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[200px] overflow-hidden text-ellipsis">
                      {[store.address, store.city, store.state, store.country]
                        .filter(Boolean)
                        .join(", ") || "N/A"}
                    </div>
                  </td>

                  {/* Contact Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {store.phone || store.email ? (
                      <div className="max-w-[150px] overflow-hidden text-ellipsis">
                        {store.phone && <div>{store.phone}</div>}
                        {store.email && <div>{store.email}</div>}
                      </div>
                    ) : (
                      "N/A"
                    )}
                  </td>

                  {/* Created By Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {store.createdByName || "N/A"}
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            onView(store);
                          }}
                          type="text"
                          className="view-button text-green-500"
                          size="middle"
                        />
                      </Tooltip>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit(store);
                          }}
                          type="text"
                          className="edit-button text-blue-500"
                          size="middle"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(store.id);
                          }}
                          type="text"
                          className="delete-button text-red-500"
                          size="middle"
                        />
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {storesData?.data && (
        <StoresPagination
          current={currentPage}
          pageSize={pageSize}
          total={storesData.data.total}
          onChange={handlePageChange}
          isMobile={isMobile}
        />
      )}
    </div>
  );
};

export default StoresTable;
