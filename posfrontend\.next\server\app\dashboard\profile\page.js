(()=>{var e={};e.id=399,e.ids=[399],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},21388:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=a(70260),s=a(28203),n=a(25155),o=a.n(n),l=a(67292),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let c=["",{children:["dashboard",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,49212)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,68375)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/profile/page",pathname:"/dashboard/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2956:(e,t,a)=>{Promise.resolve().then(a.bind(a,49212))},66508:(e,t,a)=>{Promise.resolve().then(a.bind(a,3407))},27241:(e,t,a)=>{Promise.resolve().then(a.bind(a,87053))},69097:(e,t,a)=>{Promise.resolve().then(a.bind(a,45546))},39193:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},51592:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},2961:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},53180:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},23847:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},4472:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},63440:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0014.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h676c17.7 0 32-14.3 32-32V535a175 175 0 0015.6-28.9c9.5-22.3 14.4-46 14.4-70.4V304c0-17-13.3-30.9-30-31.9zM214 184h596v88H214v-88zm362 656.1H448V736h128v104.1zm234 0H640V704c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v136.1H214V597.9c2.9 1.4 5.9 2.8 9 4 22.3 9.4 46 14.1 70.4 14.1s48-4.7 70.4-14.1c13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 3-1.3 6-2.6 9-4v242.2zm30-404.4c0 59.8-49 108.3-109.3 108.3-40.8 0-76.4-22.1-95.2-54.9-2.9-5-8.1-8.1-13.9-8.1h-.6c-5.7 0-11 3.1-13.9 8.1A109.24 109.24 0 01512 544c-40.7 0-76.2-22-95-54.7-3-5.1-8.4-8.3-14.3-8.3s-11.4 3.2-14.3 8.3a109.63 109.63 0 01-95.1 54.7C233 544 184 495.5 184 435.7v-91.2c0-.3.2-.5.5-.5h655c.3 0 .5.2.5.5v91.2z"}}]},name:"shop",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},67586:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},24648:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},45211:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var o=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(o.A,(0,r.A)({},e,{ref:t,icon:n}))})},12325:(e,t,a)=>{"use strict";a.d(t,{A:()=>O});var r=a(58009),s=a(56073),n=a.n(s),o=a(61849),l=a(73924);let i=e=>e?"function"==typeof e?e():e:null;var c=a(46219),d=a(2866),u=a(70001),m=a(60495),p=a(27343),h=a(47285),g=a(66801),f=a(36725),y=a(50127),x=a(85094),b=a(13662),v=a(10941);let w=e=>{let{componentCls:t,popoverColor:a,titleMinWidth:r,fontWeightStrong:s,innerPadding:n,boxShadowSecondary:o,colorTextHeading:l,borderRadiusLG:i,zIndexPopup:c,titleMarginBottom:d,colorBgElevated:u,popoverBg:m,titleBorderBottom:p,innerContentPadding:g,titlePadding:y}=e;return[{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:c,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":u,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:i,boxShadow:o,padding:n},[`${t}-title`]:{minWidth:r,marginBottom:d,color:l,fontWeight:s,borderBottom:p,padding:y},[`${t}-inner-content`]:{color:a,padding:g}})},(0,f.Ay)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},P=e=>{let{componentCls:t}=e;return{[t]:x.s.map(a=>{let r=e[`${a}6`];return{[`&${t}-${a}`]:{"--antd-arrow-background-color":r,[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{background:"transparent"}}}})}},j=(0,b.OF)("Popover",e=>{let{colorBgElevated:t,colorText:a}=e,r=(0,v.oX)(e,{popoverBg:t,popoverColor:a});return[w(r),P(r),(0,g.aB)(r,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:a,fontHeight:r,padding:s,wireframe:n,zIndexPopupBase:o,borderRadiusLG:l,marginXS:i,lineType:c,colorSplit:d,paddingSM:u}=e,m=a-r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:o+30},(0,y.n)(e)),(0,f.Ke)({contentRadius:l,limitVerticalRadius:!0})),{innerPadding:n?0:12,titleMarginBottom:n?0:i,titlePadding:n?`${m/2}px ${s}px ${m/2-t}px`:0,titleBorderBottom:n?`${t}px ${c} ${d}`:"none",innerContentPadding:n?`${u}px ${s}px`:0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var A=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]]);return a};let N=e=>{let{title:t,content:a,prefixCls:s}=e;return t||a?r.createElement(r.Fragment,null,t&&r.createElement("div",{className:`${s}-title`},t),a&&r.createElement("div",{className:`${s}-inner-content`},a)):null},S=e=>{let{hashId:t,prefixCls:a,className:s,style:o,placement:l="top",title:c,content:d,children:u}=e,p=i(c),h=i(d),g=n()(t,a,`${a}-pure`,`${a}-placement-${l}`,s);return r.createElement("div",{className:g,style:o},r.createElement("div",{className:`${a}-arrow`}),r.createElement(m.z,Object.assign({},e,{className:t,prefixCls:a}),u||r.createElement(N,{prefixCls:a,title:p,content:h})))};var E=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]]);return a};let C=r.forwardRef((e,t)=>{var a,s;let{prefixCls:m,title:h,content:g,overlayClassName:f,placement:y="top",trigger:x="hover",children:b,mouseEnterDelay:v=.1,mouseLeaveDelay:w=.1,onOpenChange:P,overlayStyle:A={},styles:S,classNames:C}=e,O=E(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:D,className:R,style:$,classNames:z,styles:k}=(0,p.TP)("popover"),F=D("popover",m),[I,M,T]=j(F),_=D(),B=n()(f,M,T,R,z.root,null==C?void 0:C.root),U=n()(z.body,null==C?void 0:C.body),[V,L]=(0,o.A)(!1,{value:null!==(a=e.open)&&void 0!==a?a:e.visible,defaultValue:null!==(s=e.defaultOpen)&&void 0!==s?s:e.defaultVisible}),H=(e,t)=>{L(e,!0),null==P||P(e,t)},q=e=>{e.keyCode===l.A.ESC&&H(!1,e)},W=i(h),Y=i(g);return I(r.createElement(u.A,Object.assign({placement:y,trigger:x,mouseEnterDelay:v,mouseLeaveDelay:w},O,{prefixCls:F,classNames:{root:B,body:U},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},k.root),$),A),null==S?void 0:S.root),body:Object.assign(Object.assign({},k.body),null==S?void 0:S.body)},ref:t,open:V,onOpenChange:e=>{H(e)},overlay:W||Y?r.createElement(N,{prefixCls:F,title:W,content:Y}):null,transitionName:(0,c.b)(_,"zoom-big",O.transitionName),"data-popover-inject":!0}),(0,d.Ob)(b,{onKeyDown:e=>{var t,a;r.isValidElement(b)&&(null===(a=null==b?void 0:(t=b.props).onKeyDown)||void 0===a||a.call(t,e)),q(e)}})))});C._InternalPanelDoNotUseOrYouWillBeFired=e=>{let{prefixCls:t,className:a}=e,s=A(e,["prefixCls","className"]),{getPrefixCls:o}=r.useContext(p.QO),l=o("popover",t),[i,c,d]=j(l);return i(r.createElement(S,Object.assign({},s,{prefixCls:l,hashId:c,className:n()(a,d)})))};let O=C},3407:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ea});var r=a(45512),s=a(58009),n=a.n(s),o=a(3117),l=a(30450),i=a(51592),c=a(63440),d=a(79334),u=a(41257),m=a(37764),p=a(92273),h=a(42211),g=a(97245),f=a(49792),y=a(48228);let x=({user:e,onProfileUpdated:t})=>{let[a]=u.A.useForm(),[l,i]=(0,s.useState)(!1),c=(0,p.wA)(),[d]=(0,g.H7)(),x=(0,p.d4)(e=>e.auth.accessToken)||"";n().useEffect(()=>{console.log("ProfileForm - Raw user object:",e),console.log("ProfileForm - Raw user object (stringified):",JSON.stringify(e,null,2));let t=(0,y.n4)(e.phone),r=(0,y.oB)(e.phone);console.log("ProfileForm - Phone value:",{originalPhone:e.phone,phoneType:typeof e.phone,formattedPhone:t,displayPhone:r}),a.setFieldsValue({name:e.name,email:e.email,phone:t}),console.log("ProfileForm - Form values set:",{name:e.name,email:e.email,phone:t}),setTimeout(()=>{a.setFieldsValue({name:e.name,email:e.email,phone:t}),console.log("ProfileForm - Form values refreshed:",{name:e.name,email:e.email,phone:t})},500)},[a,e]);let b=async r=>{i(!0),window.__PROFILE_UPDATE_IN_PROGRESS=!0,console.log("ProfileForm - Profile update in progress flag set");try{let s=(0,y.n4)(r.phone);console.log("ProfileForm - Updating user with values:",{userId:e.id,name:r.name,originalPhone:r.phone,formattedPhone:s,phoneType:typeof s});let n=await d({userId:e.id,data:{name:r.name,phone:s}});if(console.log("ProfileForm - Update response:",n),"data"in n){let r=n.data;if(r.success){let s=r.data?.updatedUser;if(s){console.log("ProfileForm - Updated user data from backend:",s),console.log("ProfileForm - Updated user data (stringified):",JSON.stringify(s,null,2));let n=s.phone||e.phone||"",o=e.createdAt||s.createdAt||"",l=e.lastPaymentDate||s.lastPaymentDate||void 0,i=e.nextPaymentDue||s.nextPaymentDue||null,d=e.createdBy||s.createdBy||void 0;console.log("ProfileForm - Critical fields:",{updatedUserPhone:s.phone,userPhone:e.phone,finalPhone:n,updatedUserCreatedAt:s.createdAt,userCreatedAt:e.createdAt,finalCreatedAt:o,updatedUserLastPaymentDate:s.lastPaymentDate,userLastPaymentDate:e.lastPaymentDate,finalLastPaymentDate:l,updatedUserNextPaymentDue:s.nextPaymentDue,userNextPaymentDue:e.nextPaymentDue,finalNextPaymentDue:i,updatedUserCreatedBy:s.createdBy,userCreatedBy:e.createdBy,finalCreatedBy:d});let u={...e,name:s.name,phone:n,createdAt:o,lastPaymentDate:l,nextPaymentDue:i,createdBy:d,paymentStatus:e.paymentStatus};console.log("ProfileForm - Updating Redux store with:",u),console.log("ProfileForm - Updating Redux store with (stringified):",JSON.stringify(u,null,2)),console.log("ProfileForm - Using access token from Redux:",x?"Token exists (not showing for security)":"No token found"),c((0,h.gV)({user:u,accessToken:x})),(0,f.r)("success",r.message||"Profile updated successfully");let m=(0,y.n4)(s.phone);a.setFieldsValue({name:s.name,email:s.email,phone:m}),console.log("ProfileForm - Form values updated after save:",{name:s.name,email:s.email,originalPhone:s.phone,formattedPhone:m}),t&&t()}}else(0,f.r)("error",r.message||"Failed to update profile")}}catch(t){console.error("Error updating profile:",t);let e=t.data?.message||t.message||"Failed to update profile. Please try again.";(0,f.r)("error",e)}finally{i(!1),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("ProfileForm - Profile update in progress flag cleared")},1e3)}};return(0,r.jsxs)(u.A,{form:a,layout:"vertical",onFinish:b,className:"text-gray-800",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsx)(u.A.Item,{name:"name",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Full Name"}),rules:[{required:!0,message:"Please enter your name"}],children:(0,r.jsx)(m.A,{className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"})}),(0,r.jsx)(u.A.Item,{name:"email",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Email Address"}),rules:[{required:!0,message:"Please enter your email"},{type:"email",message:"Please enter a valid email"}],children:(0,r.jsx)(m.A,{className:"bg-gray-100 border-gray-300 text-gray-600",disabled:!0,title:"Email cannot be changed"})}),(0,r.jsx)(u.A.Item,{name:"phone",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Phone Number"}),rules:[{required:!0,message:"Please enter your phone number"},{min:5,message:"Phone number must be at least 5 digits"},{max:20,message:"Phone number must be at most 20 digits"},{pattern:/^[0-9+\-\s()]*$/,message:"Phone number can only contain digits, spaces, and the characters +, -, (, )"}],tooltip:"Enter your phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)",children:(0,r.jsx)(m.A,{className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"e.g., ************"})})]}),(0,r.jsx)(u.A.Item,{className:"mt-4",children:(0,r.jsx)(o.Ay,{type:"primary",htmlType:"submit",loading:l,className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Save Changes"})})]})};var b=a(2961);let v=({userId:e})=>{let[t]=u.A.useForm(),[a,n]=(0,s.useState)(!1),[l]=(0,g.SV)(),i=async a=>{if(a.newPassword!==a.confirmPassword){(0,f.r)("error","New passwords do not match");return}n(!0);try{if(console.log("ChangePasswordForm - Attempting to change password for user:",e),console.log("ChangePasswordForm - Password lengths:",{currentPasswordLength:a.currentPassword?.length||0,newPasswordLength:a.newPassword?.length||0,confirmPasswordLength:a.confirmPassword?.length||0}),!a.currentPassword||!a.newPassword){(0,f.r)("error","All password fields are required"),n(!1);return}let r=await l({userId:e,currentPassword:a.currentPassword,newPassword:a.newPassword});if("data"in r){let e=r.data;console.log("ChangePasswordForm - Response:",e),e.success?((0,f.r)("success",e.message||"Password changed successfully"),t.resetFields()):(console.error("ChangePasswordForm - Error:",e.message),(0,f.r)("error",e.message||"Failed to change password"))}else if("error"in r){let e=r.error;console.error("ChangePasswordForm - Error response:",e),e?.data?.message?(0,f.r)("error",e.data.message):e?.message?(0,f.r)("error",e.message):(0,f.r)("error","Failed to change password. Please try again.")}}catch(t){console.error("Error changing password:",t),console.error("ChangePasswordForm - Detailed error:",{message:t.message,data:t.data,status:t.status,stack:t.stack});let e=t.data?.message||t.message||"Failed to change password. Please check your current password and try again.";(0,f.r)("error",e)}finally{n(!1)}};return(0,r.jsxs)(u.A,{form:t,layout:"vertical",onFinish:i,className:"text-gray-800",children:[(0,r.jsx)(u.A.Item,{name:"currentPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Current Password"}),rules:[{required:!0,message:"Please enter your current password"}],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(b.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Enter your current password"})}),(0,r.jsx)(u.A.Item,{name:"newPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"New Password"}),rules:[{required:!0,message:"Please enter your new password"},{min:8,message:"Password must be at least 8 characters"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,message:"Password must contain uppercase, lowercase, number and special character"}],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(b.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Enter your new password"})}),(0,r.jsx)(u.A.Item,{name:"confirmPassword",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Confirm New Password"}),rules:[{required:!0,message:"Please confirm your new password"},({getFieldValue:e})=>({validator:(t,a)=>a&&e("newPassword")!==a?Promise.reject(Error("The two passwords do not match")):Promise.resolve()})],children:(0,r.jsx)(m.A.Password,{prefix:(0,r.jsx)(b.A,{className:"text-gray-500"}),className:"bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400",placeholder:"Confirm your new password"})}),(0,r.jsx)(u.A.Item,{className:"mt-6",children:(0,r.jsx)(o.Ay,{type:"primary",htmlType:"submit",loading:a,className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Change Password"})}),(0,r.jsxs)("div",{className:"mt-4 text-gray-600 text-sm",children:[(0,r.jsx)("p",{children:"Password requirements:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 mt-1",children:[(0,r.jsx)("li",{children:"Minimum 8 characters"}),(0,r.jsx)("li",{children:"At least one uppercase letter"}),(0,r.jsx)("li",{children:"At least one lowercase letter"}),(0,r.jsx)("li",{children:"At least one number"}),(0,r.jsx)("li",{children:"At least one special character (@$!%*?&)"})]})]})]})};var w=a(21419),P=a(4472),j=a(46211);let A=({user:e,onStoreUpdated:t})=>{let[a]=u.A.useForm(),[n,l]=(0,s.useState)(!1),{data:i,isLoading:d,refetch:p}=(0,j.Rv)(e.id),[h]=(0,j.He)(),[g]=(0,j.aW)();(0,s.useEffect)(()=>{if(i?.data){let e=i.data;a.setFieldsValue({name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId})}else a.resetFields()},[i,a]);let y=async()=>{try{l(!0);let r=await a.validateFields();if(i?.data){let e=await g({storeId:i.data.id,data:r}).unwrap();e.success?((0,f.r)("success","Store information updated successfully"),t&&t(),p()):(0,f.r)("error",e.message||"Failed to update store information")}else{let a=await h({data:{...r,userId:e.id}}).unwrap();a.success?((0,f.r)("success","Store information saved successfully"),t&&t(),p()):(0,f.r)("error",a.message||"Failed to save store information")}}catch(e){console.error("Error submitting store form:",e),(0,f.r)("error",e.data?.message||"An error occurred while saving store information")}finally{l(!1)}};return d?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)(w.A,{})}):(0,r.jsxs)("div",{className:"text-gray-800",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-800 mb-2 flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-2"}),(0,r.jsx)("span",{children:"Store Information"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"This information will appear on receipts and invoices"})]}),(0,r.jsxs)(u.A,{form:a,layout:"vertical",onFinish:y,className:"text-gray-800",children:[(0,r.jsx)(u.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"text-gray-700",children:["Store Name ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),rules:[{required:!0,message:"Please enter your store name"}],children:(0,r.jsx)(m.A,{placeholder:"Enter store name",prefix:(0,r.jsx)(c.A,{className:"site-form-item-icon"}),className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(u.A.Item,{name:"address",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Address"}),children:(0,r.jsx)(m.A.TextArea,{placeholder:"Enter store address",className:"bg-white border-gray-300 text-gray-800",rows:3})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(u.A.Item,{name:"city",label:(0,r.jsx)("span",{className:"text-gray-700",children:"City"}),children:(0,r.jsx)(m.A,{placeholder:"Enter city",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(u.A.Item,{name:"state",label:(0,r.jsx)("span",{className:"text-gray-700",children:"State/Region"}),children:(0,r.jsx)(m.A,{placeholder:"Enter state or region",className:"bg-white border-gray-300 text-gray-800"})})]}),(0,r.jsx)(u.A.Item,{name:"country",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Country"}),children:(0,r.jsx)(m.A,{placeholder:"Enter country",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(u.A.Item,{name:"phone",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Phone"}),children:(0,r.jsx)(m.A,{placeholder:"Enter store phone",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(u.A.Item,{name:"email",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Email"}),children:(0,r.jsx)(m.A,{placeholder:"Enter store email",className:"bg-white border-gray-300 text-gray-800",type:"email"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(u.A.Item,{name:"website",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Website"}),children:(0,r.jsx)(m.A,{placeholder:"Enter website URL",className:"bg-white border-gray-300 text-gray-800"})}),(0,r.jsx)(u.A.Item,{name:"taxId",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Tax ID"}),children:(0,r.jsx)(m.A,{placeholder:"Enter tax ID",className:"bg-white border-gray-300 text-gray-800"})})]}),(0,r.jsx)(u.A.Item,{className:"mt-6",children:(0,r.jsx)(o.Ay,{type:"primary",htmlType:"submit",loading:n,icon:(0,r.jsx)(P.A,{}),className:"bg-blue-600 hover:bg-blue-700 border-none text-white",block:!0,children:i?.data?"Update Store Information":"Save Store Information"})})]})]})};var N=a(56073),S=a.n(N),E=a(21776),C=a(80799),O=a(83893),D=a(27343),R=a(90334),$=a(43089),z=a(52271);let k=s.createContext({});var F=a(1439),I=a(47285),M=a(13662),T=a(10941);let _=e=>{let{antCls:t,componentCls:a,iconCls:r,avatarBg:s,avatarColor:n,containerSize:o,containerSizeLG:l,containerSizeSM:i,textFontSize:c,textFontSizeLG:d,textFontSizeSM:u,borderRadius:m,borderRadiusLG:p,borderRadiusSM:h,lineWidth:g,lineType:f}=e,y=(e,t,s)=>({width:e,height:e,borderRadius:"50%",[`&${a}-square`]:{borderRadius:s},[`&${a}-icon`]:{fontSize:t,[`> ${r}`]:{margin:0}}});return{[a]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,I.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:n,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:s,border:`${(0,F.zA)(g)} ${f} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),y(o,c,m)),{"&-lg":Object.assign({},y(l,d,p)),"&-sm":Object.assign({},y(i,u,h)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},B=e=>{let{componentCls:t,groupBorderColor:a,groupOverlapping:r,groupSpace:s}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:a},"> *:not(:first-child)":{marginInlineStart:r}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:s}}}},U=(0,M.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:a}=e,r=(0,T.oX)(e,{avatarBg:a,avatarColor:t});return[_(r),B(r)]},e=>{let{controlHeight:t,controlHeightLG:a,controlHeightSM:r,fontSize:s,fontSizeLG:n,fontSizeXL:o,fontSizeHeading3:l,marginXS:i,marginXXS:c,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:a,containerSizeSM:r,textFontSize:Math.round((n+o)/2),textFontSizeLG:l,textFontSizeSM:s,groupSpace:c,groupOverlapping:-i,groupBorderColor:d}});var V=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]]);return a};let L=s.forwardRef((e,t)=>{let a;let{prefixCls:r,shape:n,size:o,src:l,srcSet:i,icon:c,className:d,rootClassName:u,style:m,alt:p,draggable:h,children:g,crossOrigin:f,gap:y=4,onError:x}=e,b=V(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[v,w]=s.useState(1),[P,j]=s.useState(!1),[A,N]=s.useState(!0),F=s.useRef(null),I=s.useRef(null),M=(0,C.K4)(t,F),{getPrefixCls:T,avatar:_}=s.useContext(D.QO),B=s.useContext(k),L=()=>{if(!I.current||!F.current)return;let e=I.current.offsetWidth,t=F.current.offsetWidth;0!==e&&0!==t&&2*y<t&&w(t-2*y<e?(t-2*y)/e:1)};s.useEffect(()=>{j(!0)},[]),s.useEffect(()=>{N(!0),w(1)},[l]),s.useEffect(L,[y]);let H=(0,$.A)(e=>{var t,a;return null!==(a=null!==(t=null!=o?o:null==B?void 0:B.size)&&void 0!==t?t:e)&&void 0!==a?a:"default"}),q=Object.keys("object"==typeof H&&H||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),W=(0,z.A)(q),Y=s.useMemo(()=>{if("object"!=typeof H)return{};let e=H[O.ye.find(e=>W[e])];return e?{width:e,height:e,fontSize:e&&(c||g)?e/2:18}:{}},[W,H]),J=T("avatar",r),G=(0,R.A)(J),[K,Q,X]=U(J,G),Z=S()({[`${J}-lg`]:"large"===H,[`${J}-sm`]:"small"===H}),ee=s.isValidElement(l),et=n||(null==B?void 0:B.shape)||"circle",ea=S()(J,Z,null==_?void 0:_.className,`${J}-${et}`,{[`${J}-image`]:ee||l&&A,[`${J}-icon`]:!!c},X,G,d,u,Q),er="number"==typeof H?{width:H,height:H,fontSize:c?H/2:18}:{};if("string"==typeof l&&A)a=s.createElement("img",{src:l,draggable:h,srcSet:i,onError:()=>{!1!==(null==x?void 0:x())&&N(!1)},alt:p,crossOrigin:f});else if(ee)a=l;else if(c)a=c;else if(P||1!==v){let e=`scale(${v})`;a=s.createElement(E.A,{onResize:L},s.createElement("span",{className:`${J}-string`,ref:I,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},g))}else a=s.createElement("span",{className:`${J}-string`,style:{opacity:0},ref:I},g);return K(s.createElement("span",Object.assign({},b,{style:Object.assign(Object.assign(Object.assign(Object.assign({},er),Y),null==_?void 0:_.style),m),className:ea,ref:M}),a))});var H=a(86866),q=a(2866),W=a(12325);let Y=e=>{let{size:t,shape:a}=s.useContext(k),r=s.useMemo(()=>({size:e.size||t,shape:e.shape||a}),[e.size,e.shape,t,a]);return s.createElement(k.Provider,{value:r},e.children)};L.Group=e=>{var t,a,r,n;let{getPrefixCls:o,direction:l}=s.useContext(D.QO),{prefixCls:i,className:c,rootClassName:d,style:u,maxCount:m,maxStyle:p,size:h,shape:g,maxPopoverPlacement:f,maxPopoverTrigger:y,children:x,max:b}=e,v=o("avatar",i),w=`${v}-group`,P=(0,R.A)(v),[j,A,N]=U(v,P),E=S()(w,{[`${w}-rtl`]:"rtl"===l},N,P,c,d,A),C=(0,H.A)(x).map((e,t)=>(0,q.Ob)(e,{key:`avatar-key-${t}`})),O=(null==b?void 0:b.count)||m,$=C.length;if(O&&O<$){let e=C.slice(0,O),o=C.slice(O,$),l=(null==b?void 0:b.style)||p,i=(null===(t=null==b?void 0:b.popover)||void 0===t?void 0:t.trigger)||y||"hover",c=(null===(a=null==b?void 0:b.popover)||void 0===a?void 0:a.placement)||f||"top",d=Object.assign(Object.assign({content:o},null==b?void 0:b.popover),{classNames:{root:S()(`${w}-popover`,null===(n=null===(r=null==b?void 0:b.popover)||void 0===r?void 0:r.classNames)||void 0===n?void 0:n.root)},placement:c,trigger:i});return e.push(s.createElement(W.A,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},d),s.createElement(L,{style:l},`+${$-O}`))),j(s.createElement(Y,{shape:g,size:h},s.createElement("div",{className:E,style:u},e)))}return j(s.createElement(Y,{shape:g,size:h},s.createElement("div",{className:E,style:u},C)))};var J=a(24648),G=a(53180),K=a(23847),Q=a(16589),X=a.n(Q);let Z=({user:e})=>{let t=e=>{if(!e)return"N/A";console.log("ProfileAvatar - Formatting date:",e);try{return X()(e).format("MMM D, YYYY")}catch(e){return console.error("Error formatting date:",e),"Invalid date"}};return console.log("ProfileAvatar - User data:",{name:e.name,email:e.email,phone:e.phone,phoneType:typeof e.phone,createdAt:e.createdAt,createdAtType:typeof e.createdAt,role:e.role,paymentStatus:e.paymentStatus}),console.log("ProfileAvatar - Complete user object:",JSON.stringify(e,null,2)),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(L,{size:120,icon:(0,r.jsx)(J.A,{}),className:"mb-4 bg-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-1",children:e.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-4 capitalize",children:e.role}),(0,r.jsxs)("div",{className:"mt-6 w-full",children:[(0,r.jsx)("h4",{className:"text-gray-800 font-medium mb-2",children:"Account Information"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(G.A,{className:"mr-2"}),"Email:"]}),(0,r.jsx)("span",{className:"text-gray-800",children:e.email})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(K.A,{className:"mr-2"}),"Phone:"]}),(0,r.jsx)("span",{className:"text-gray-800",children:e&&e.phone?(0,y.oB)(e.phone):"Not provided"})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Role:"}),(0,r.jsx)("span",{className:"text-gray-800 capitalize",children:e.role})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Payment Status:"}),(0,r.jsx)("span",{className:`capitalize ${"paid"===e.paymentStatus?"text-green-500":"pending"===e.paymentStatus?"text-yellow-500":"text-red-500"}`,children:e.paymentStatus})]}),e.nextPaymentDue&&(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Next Payment:"}),(0,r.jsx)("span",{className:"text-gray-800",children:t(e.nextPaymentDue)})]}),(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Member Since:"}),(0,r.jsx)("span",{className:"text-gray-800",children:e&&e.createdAt?t(e.createdAt):"N/A"})]}),e.lastPaymentDate&&(0,r.jsxs)("p",{className:"flex justify-between py-2 border-b border-gray-200",children:[(0,r.jsx)("span",{children:"Last Payment:"}),(0,r.jsx)("span",{className:"text-gray-800",children:t(e.lastPaymentDate)})]})]})]})]})};var ee=a(60636),et=a(46902);function ea(){var e;let{user:t,refreshUser:a}=(0,ee.A)(),n=(0,d.useRouter)();(0,d.usePathname)();let[u,m]=(0,s.useState)("1"),[p,h]=(0,s.useState)(0);console.log("ProfilePage - Complete user object:",JSON.stringify(t,null,2)),console.log("ProfilePage - Critical fields:",{phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"});let g=async()=>{console.log("ProfilePage - Refreshing user data");try{await a(),console.log("ProfilePage - User data after refresh:",{phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),h(e=>e+1),console.log("ProfilePage - Refresh key updated:",p+1)}catch(e){console.error("Error updating profile:",e)}};return t?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"My Profile"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your account information and settings"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-1",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:(0,r.jsx)(Z,{user:t},`avatar-${p}`)}),"superadmin"!==t.role&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6 border border-gray-200",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-800 mb-4 flex items-center",children:[(0,r.jsx)(i.A,{className:"mr-2"}),(0,r.jsx)("span",{children:"Subscription Details"})]}),(0,r.jsx)(et.A,{}),(0,r.jsx)("p",{className:"mb-4 text-gray-600",children:"Your subscription allows you to access all features of the POS system."}),t.nextPaymentDue&&(0,r.jsxs)("p",{className:"mb-4 text-gray-600",children:["Next payment due: ",(0,r.jsx)("span",{className:"text-gray-800",children:(e=t.nextPaymentDue)?X()(e).format("MMM D, YYYY"):"N/A"})]}),"cashier"!==t.role&&(0,r.jsx)(o.Ay,{type:"primary",block:!0,onClick:()=>n.push("/payment"),className:"bg-blue-600 hover:bg-blue-700 border-none text-white",children:"Manage Subscription"})]})]}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:(0,r.jsx)(l.A,{activeKey:u,onChange:e=>{m(e)},items:[{key:"1",label:"Profile Information",children:(0,r.jsx)(x,{user:t,onProfileUpdated:g},`profile-form-${p}`)},{key:"2",label:"Change Password",children:(0,r.jsx)(v,{userId:t.id},`password-form-${p}`)},..."admin"===t.role?[{key:"3",label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Store Information"]}),children:(0,r.jsx)(A,{user:t,onStoreUpdated:g},`store-form-${p}`)}]:[]],className:"profile-tabs"})})})]})]}):(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-500",children:"Loading profile information..."})})}a(11267)},46902:(e,t,a)=>{"use strict";a.d(t,{A:()=>w});var r=a(45512),s=a(58009),n=a(92273),o=a(49198),l=a(53950),i=a(6987),c=a(3117),d=a(26222),u=a(60380),m=a(39193),p=a(45211),h=a(67586),g=a(56403),f=a(79334),y=a(16589),x=a.n(y),b=a(25510),v=a(79188);let w=({showPayButton:e=!0})=>{let t=(0,n.d4)(e=>e.auth.user),a=(0,n.d4)(e=>e.auth.accessToken),y=(0,n.wA)(),w=(0,f.useRouter)(),{needsPayment:P,status:j,daysRemaining:A}=(0,b._)(),[N,S]=(0,s.useState)(!1);if(!t)return(0,r.jsx)(o.A,{message:"Authentication Required",description:"Please log in to view your payment status.",type:"warning",showIcon:!0});let E=()=>{switch(t.paymentStatus){case"paid":return"success";case"pending":return"warning";case"overdue":return"error";default:return"default"}},C=e=>e?x()(e).format("MMM D, YYYY"):"N/A",O=async()=>{if(!t||!a){l.Ay.error("Unable to refresh: User not authenticated");return}S(!0),console.log("\uD83D\uDD04 Manual payment status refresh triggered");try{(await (0,v.hB)({dispatch:y,accessToken:a,currentUser:t,maxRetries:3,retryDelay:1e3})).success?(l.Ay.success("Payment status refreshed successfully! Reloading page..."),console.log("✅ Manual refresh successful"),setTimeout(()=>{window.location.reload()},1500)):(l.Ay.warning("Refresh completed but status may not have changed"),console.log("⚠️ Manual refresh completed with warnings"))}catch(e){console.error("❌ Manual refresh failed:",e),l.Ay.error("Failed to refresh payment status. Please try again.")}finally{S(!1)}};return(0,r.jsx)(i.A,{title:(0,r.jsxs)("div",{className:"flex items-center justify-between pl-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(()=>{switch(t.paymentStatus){case"paid":return(0,r.jsx)(u.A,{className:"text-green-500 text-xl"});case"pending":return(0,r.jsx)(m.A,{className:"text-yellow-500 text-xl"});case"overdue":return(0,r.jsx)(p.A,{className:"text-red-500 text-xl"});case"inactive":return(0,r.jsx)(h.A,{className:"text-gray-500 text-xl"});default:return null}})(),(0,r.jsx)("span",{className:"ml-2 text-gray-800",children:"Subscription Status"})]}),(0,r.jsx)(c.Ay,{type:"text",icon:(0,r.jsx)(g.A,{spin:N}),onClick:O,loading:N,size:"small",className:"text-gray-600 hover:text-blue-600",title:"Refresh payment status"})]}),className:"w-full shadow-md bg-white border border-gray-200",styles:{header:{backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"},body:{backgroundColor:"#ffffff"}},children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4 px-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,r.jsx)(d.A,{status:E(),text:t.paymentStatus.toUpperCase()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Plan:"}),(0,r.jsx)("span",{className:"text-gray-800",children:(()=>{if(!t?.lastPaymentDate||!t?.nextPaymentDue)return"Monthly (₵40/month)";let e=x()(t.lastPaymentDate),a=x()(t.nextPaymentDue),r=a.diff(e,"day"),s=a.diff(e,"month",!0);return r>=350||s>=11?(console.log("✅ Detected: Annual subscription"),"Annual (₵360/year)"):r>=80||s>=2.5?(console.log("✅ Detected: Quarterly subscription"),"Quarterly (₵108/3 months)"):(console.log("✅ Detected: Monthly subscription"),"Monthly (₵40/month)")})()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last Payment:"}),(0,r.jsx)("span",{className:"text-gray-800",children:C(t.lastPaymentDate)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next Payment Due:"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-gray-800",children:C(t.nextPaymentDue)}),t.nextPaymentDue&&"paid"===t.paymentStatus&&(0,r.jsx)("div",{className:"text-xs text-green-600",children:(()=>{let e=x()(t.nextPaymentDue).diff(x()(),"day");return e>0?`${e} days remaining`:0===e?"Due today":`${Math.abs(e)} days overdue`})()})]})]}),(0,r.jsx)(o.A,{message:(()=>{switch(t.paymentStatus){case"paid":return"Your subscription is active";case"pending":return"Your payment is pending verification";case"overdue":return"Your payment is overdue";case"inactive":return"Your subscription is inactive";default:return"Unknown status"}})(),type:E(),showIcon:!0,className:"mt-2"}),e&&P&&(0,r.jsx)(c.Ay,{type:"primary",onClick:()=>{w.push("/payment")},className:"w-full mt-4 bg-blue-600 hover:bg-blue-700 h-10 font-medium",children:"Manage Subscription"})]})})}},60636:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(92273),s=a(25510),n=a(97245),o=a(42211);let l=()=>{let e=(0,r.wA)(),{user:t,accessToken:a}=(0,r.d4)(e=>e.auth),l=(0,s._)(),{refetch:i}=(0,n.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!a,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let r=await i();console.log("useAuth - Refetch result:",r);let s=r.data;if(s?.success&&s?.data){console.log("useAuth - API response data:",s.data);let r=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let n=s.data.phone||t.phone||"",l=s.data.createdAt||t.createdAt||"",i=s.data.lastPaymentDate||t.lastPaymentDate||void 0,c=s.data.nextPaymentDue||t.nextPaymentDue||null,d=s.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:s.data.phone,userPhone:t.phone,finalPhone:n,apiCreatedAt:s.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:l,apiLastPaymentDate:s.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:s.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:s.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...s.data,phone:n,createdAt:l,lastPaymentDate:i,nextPaymentDue:c,createdBy:d,paymentStatus:r};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,o.gV)({user:u,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",s?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&l.needsPayment,paymentStatus:l,refreshUser:d}}},48228:(e,t,a)=>{"use strict";a.d(t,{n4:()=>r,oB:()=>s});let r=e=>{if(!e)return"";let t=e.replace(/\D/g,"");return e.startsWith("0")?e:t.startsWith("233")||e.startsWith("+233")?"0"+t.substring(3):e},s=e=>{let t=r(e);if(!t)return"";let a=t.replace(/\D/g,"");return 10===a.length&&a.startsWith("0")?`${a.substring(0,3)} ${a.substring(3,7)} ${a.substring(7)}`:t}},79188:(e,t,a)=>{"use strict";a.d(t,{hB:()=>n,jH:()=>o});var r=a(42211),s=a(97245);let n=async({dispatch:e,accessToken:t,currentUser:a,maxRetries:n=3,retryDelay:o=2e3})=>{console.log("\uD83D\uDD04 Starting user data refresh after payment..."),e(s.i$.util.invalidateTags(["User"])),e(s.i$.util.resetApiState()),console.log("\uD83E\uDDF9 Cleared all RTK Query cache");for(let l=1;l<=n;l++)try{console.log(`📡 Attempt ${l}/${n} - Fetching fresh user data...`),l>1&&await new Promise(e=>setTimeout(e,o));let i=await e(s.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in i&&i.data?.success&&i.data.data){let s=i.data.data;if(console.log("✅ Successfully fetched updated user data:",{id:s.id,paymentStatus:s.paymentStatus,lastPaymentDate:s.lastPaymentDate,nextPaymentDue:s.nextPaymentDue,attempt:l}),e((0,r.gV)({user:s,accessToken:t})),"paid"===s.paymentStatus)return console.log('\uD83C\uDF89 Payment status successfully updated to "paid"'),{success:!0,user:s};if(console.log(`⚠️ Payment status is "${s.paymentStatus}", not "paid". Retrying...`),l===n){console.log("⚠️ Max retries reached, using fallback update");let s={...a,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return e((0,r.gV)({user:s,accessToken:t})),{success:!0,user:s}}continue}if(console.log(`⚠️ Attempt ${l}: Backend response not successful or no data`),l===n)throw Error("Failed to fetch user data from backend");continue}catch(s){if(console.error(`❌ Attempt ${l} failed:`,s),l===n){if(console.log("\uD83D\uDCA5 All attempts failed, using fallback update"),!a)return{success:!1,error:"Failed to refresh user data and no current user for fallback"};{let s={...a,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return e((0,r.gV)({user:s,accessToken:t})),console.log("✅ Fallback: Updated user payment status in Redux"),{success:!0,user:s}}}}return{success:!1,error:"Unexpected error in user data refresh"}},o=e=>{if(!e)return console.log("❌ No user data to verify"),!1;let t="paid"===e.paymentStatus,a=e.lastPaymentDate&&new Date(e.lastPaymentDate).getTime()>Date.now()-3e5;return console.log("\uD83D\uDD0D Payment status verification:",{paymentStatus:e.paymentStatus,isPaymentStatusPaid:t,lastPaymentDate:e.lastPaymentDate,hasRecentPaymentDate:a,nextPaymentDue:e.nextPaymentDue}),t}},68375:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var r=a(62740),s=a(87053);function n({children:e}){return(0,r.jsx)(s.default,{checkPayment:!1,children:e})}},49212:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\page.tsx","default")},87053:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Auth\\\\ProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\ProtectedRoute.tsx","default")},11267:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,3950,5482,106,4286],()=>a(21388));module.exports=r})();