/* Custom styles for the users table */
.user-table .ant-table-container {
  overflow-x: auto;
  background-color: #ffffff;
}

.user-table .ant-table-body {
  overflow-x: auto !important;
}

/* Ensure all table cells have light background */
.user-table .ant-table {
  background-color: #ffffff;
}

.user-table .ant-table-thead > tr > th,
.user-table .ant-table-tbody > tr > td {
  background-color: #ffffff;
  color: #333333;
}

/* Ensure fixed columns work properly */
.user-table .ant-table-cell.ant-table-cell-fix-left,
.user-table .ant-table-cell.ant-table-cell-fix-right {
  z-index: 2;
  background-color: #ffffff !important;
}

/* Fix for sticky header */
.user-table .ant-table-header {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.user-table .ant-table-cell-fix-left-last::after,
.user-table .ant-table-cell-fix-right-first::after {
  background-color: #ffffff !important;
}

/* Improve tag display on mobile */
@media (max-width: 640px) {
  .user-table .ant-tag {
    margin-right: 0;
    padding: 0 4px;
    font-size: 11px;
  }

  .user-table .ant-table-thead > tr > th,
  .user-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    background-color: #ffffff !important;
    color: #333333;
  }

  .user-table .ant-table-filter-trigger {
    margin-right: -4px;
  }

  /* Ensure action buttons are properly spaced */
  .user-table .ant-space-item {
    margin-right: 0 !important;
  }

  .user-table .ant-btn {
    padding: 0 4px;
  }

  /* Additional fixes for mobile */
  .user-table .ant-table-container::before,
  .user-table .ant-table-container::after {
    background-color: #ffffff !important;
  }

  .user-table .ant-table-cell-scrollbar {
    box-shadow: none;
    background-color: #ffffff !important;
  }

  /* Fix for row hover effect */
  .user-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #f5f5f5 !important;
  }
}

/* Ensure the table doesn't affect the layout */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  background-color: #ffffff;
}

/* Fix for any remaining transparent areas */
.user-table .ant-table-wrapper,
.user-table .ant-spin-nested-loading,
.user-table .ant-spin-container,
.user-table .ant-table,
.user-table .ant-table-content,
.user-table .ant-table-scroll,
.user-table .ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-cell-fix-left-first,
.user-table .ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-cell-fix-right-last {
  background-color: #ffffff !important;
}

/* Fix for striped rows if enabled */
.user-table .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: #ffffff !important;
}

.user-table .ant-table-tbody > tr.ant-table-row:nth-child(even) > td {
  background-color: #f9f9f9 !important;
}

/* Fix for sticky header and scrollbar */
.user-table .ant-table-sticky-scroll {
  background-color: #ffffff !important;
}

.user-table .ant-table-sticky-scroll-bar {
  background-color: #e6e6e6 !important;
}

/* Fix for any shadow effects */
.user-table .ant-table-cell-fix-left-last::after,
.user-table .ant-table-cell-fix-right-first::after {
  box-shadow: none !important;
  background-color: #ffffff !important;
}

/* Fix for any remaining transparent areas */
.user-table .ant-table-container::before,
.user-table .ant-table-container::after,
.user-table .ant-table-header::before,
.user-table .ant-table-header::after,
.user-table .ant-table-body::before,
.user-table .ant-table-body::after {
  display: none !important;
  background-color: #ffffff !important;
}

/* Fix for tag backgrounds */
.ant-tag {
  background-color: #f0f0f0 !important;
  border-color: #d9d9d9 !important;
}

/* Custom tag colors */
.ant-tag-purple {
  color: #722ed1 !important;
  border-color: #d3adf7 !important;
  background-color: #f9f0ff !important;
}

.ant-tag-blue {
  color: #1890ff !important;
  border-color: #91d5ff !important;
  background-color: #e6f7ff !important;
}

.ant-tag-green {
  color: #52c41a !important;
  border-color: #b7eb8f !important;
  background-color: #f6ffed !important;
}

.ant-tag-success {
  color: #52c41a !important;
  border-color: #b7eb8f !important;
  background-color: #f6ffed !important;
}

.ant-tag-warning {
  color: #faad14 !important;
  border-color: #ffe58f !important;
  background-color: #fffbe6 !important;
}

.ant-tag-error {
  color: #f5222d !important;
  border-color: #ffa39e !important;
  background-color: #fff1f0 !important;
}

.ant-tag-default {
  color: #595959 !important;
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5 !important;
}

/* Custom styles for action buttons */
.view-button .anticon-eye {
  color: #3b82f6 !important; /* Blue color for view icon */
  font-size: 18px !important;
}

.edit-button .anticon-edit {
  color: #10b981 !important; /* Green color for edit icon */
  font-size: 18px !important;
}

.delete-button .anticon-delete {
  color: #ef4444 !important; /* Red color for delete icon */
  font-size: 18px !important;
}

/* Hover effects for action buttons */
.view-button:hover {
  background-color: rgba(59, 130, 246, 0.2) !important; /* Blue hover */
}

.edit-button:hover {
  background-color: rgba(16, 185, 129, 0.2) !important; /* Green hover */
}

.delete-button:hover {
  background-color: rgba(239, 68, 68, 0.2) !important; /* Red hover */
}

/* Button background */
.view-button, .edit-button, .delete-button {
  background-color: #f5f5f5 !important;
  border: none !important;
}

/* Pagination styles */
.bg-white {
  background-color: #ffffff !important;
}

.border-gray-200, .border-gray-300 {
  border-color: #e8e8e8 !important;
}

.text-gray-500, .text-gray-700 {
  color: #595959 !important;
}

.bg-blue-50 {
  background-color: #e6f7ff !important;
}

.border-blue-500 {
  border-color: #1890ff !important;
}

.text-blue-600 {
  color: #1890ff !important;
}

.hover\:bg-gray-50:hover {
  background-color: #f5f5f5 !important;
}
