import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import {
  useInitializePaymentMutation,
  useVerifyPaymentMutation,
  useVerifyPaystackPaymentMutation,
  useUpdateUserPaymentStatusMutation
} from "@/reduxRTK/services/paymentApi";
import { setUser } from "@/reduxRTK/services/authSlice";
import { ApiResponse, Payment, PaymentProvider } from "@/types/payment";
import { User } from "@/types/user";

export const usePayment = () => {
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);
  const token = useSelector((state: RootState) => state.auth.accessToken);

  const [state, setState] = useState<{
    loading: boolean;
    error: string;
    data: Payment | null;
  }>({
    loading: false,
    error: "",
    data: null,
  });

  const [initializePayment] = useInitializePaymentMutation();
  const [verifyPayment] = useVerifyPaymentMutation();
  const [verifyPaystackPayment] = useVerifyPaystackPaymentMutation();
  const [updateUserPaymentStatus] = useUpdateUserPaymentStatusMutation();



  const initializePaystackPayment = async (
    amount: number,
    email: string,
    callbackUrl?: string
  ) => {
    if (!user || !token) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    setState({ ...state, loading: true, error: "" });

    try {
      const response = await initializePayment({
        amount,
        email,
        callbackUrl,
      }).unwrap();

      setState({
        loading: false,
        error: "",
        data: null,
      });

      return response;
    } catch (error: any) {
      const errorMessage =
        error?.data?.message || error.message || "Payment initialization failed";
      setState({
        loading: false,
        error: errorMessage,
        data: null,
      });
      return {
        success: false,
        message: errorMessage,
      };
    }
  };

  const verifyPaystackPaymentByReference = async (reference: string) => {
    if (!user || !token) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    setState({ ...state, loading: true, error: "" });

    try {
      const response = await verifyPaystackPayment({
        reference,
      }).unwrap();

      setState({
        loading: false,
        error: "",
        data: response.data || null,
      });

      return response;
    } catch (error: any) {
      const errorMessage =
        error?.data?.message || error.message || "Payment verification failed";
      setState({
        loading: false,
        error: errorMessage,
        data: null,
      });
      return {
        success: false,
        message: errorMessage,
      };
    }
  };

  return {
    initializePaystackPayment,
    verifyPaystackPaymentByReference,
    loading: state.loading,
    error: state.error,
    data: state.data,
  };
};
