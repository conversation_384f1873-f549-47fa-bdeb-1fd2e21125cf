"use client";

import { useGetUserByIdQuery } from "@/reduxRTK/services/authApi";
import { ApiResponse, User } from "@/types/user";

export const useUserDetail = (userId: number | null) => {
  // Fetch user details when userId is available
  const { 
    data: response, 
    isLoading 
  } = useGetUserByIdQuery(userId || 0, {
    skip: !userId,
  }) as {
    data?: ApiResponse<User>;
    isLoading: boolean
  };

  // Extract user from response
  const user = response?.data;

  return {
    user,
    isLoading
  };
};
