import { useState } from "react";
import { useDispatch } from "react-redux";
import { setUser } from "@/reduxRTK/services/authSlice";
import { useLoginUserMutation } from "@/reduxRTK/services/authApi";
import { ApiResponse, UserResponse } from "@/types/user";

export const useLoginUser = () => {
  const dispatch = useDispatch();
  const [state, setState] = useState<{
    loading: boolean;
    error: string;
    data: UserResponse | null; // 👈 Just store the actual data, not the full ApiResponse
  }>({
    loading: false,
    error: "",
    data: null,
  });

  const [loginUser] = useLoginUserMutation<ApiResponse<UserResponse>>();

  const login = async (email: string, password: string) => {
    setState((prev) => ({ ...prev, loading: true, error: "" }));

    try {
      const response = (await loginUser({
        email,
        password,
      }).unwrap()) as unknown as ApiResponse<UserResponse>;

      if (
        response.success &&
        response.data?.user &&
        response.data?.accessToken
      ) {
        const { user, accessToken } = response.data;

        // Log the user data from the login response
        console.log("useLoginUser - User data from login response:", user);
        console.log("useLoginUser - User data (stringified):", JSON.stringify(user, null, 2));
        console.log("useLoginUser - Critical fields:", {
          phone: user.phone,
          phoneType: typeof user.phone,
          createdAt: user.createdAt,
          createdAtType: typeof user.createdAt,
          lastPaymentDate: user.lastPaymentDate,
          nextPaymentDue: user.nextPaymentDue
        });

        // Ensure all fields are properly defined
        const userData = {
          ...user,
          phone: user.phone || "",
          createdAt: user.createdAt || "",
          lastPaymentDate: user.lastPaymentDate || null,
          nextPaymentDue: user.nextPaymentDue || null,
          createdBy: user.createdBy || null
        };

        console.log("useLoginUser - Updated user data:", userData);

        dispatch(setUser({ user: userData, accessToken }));

        setState({ loading: false, error: "", data: { user: userData, accessToken } });

        return {
          success: true,
          message: response.message,
          data: { user: userData, accessToken },
        };
      } else {
        const message = response.message || "Login failed";
        setState({ loading: false, error: message, data: null });

        return {
          success: false,
          message,
          data: null,
        };
      }
    } catch (error: any) {
      const errorMessage =
        error?.data?.message || error.message || "Something went wrong";
      setState({ loading: false, error: errorMessage, data: null });

      return {
        success: false,
        message: errorMessage,
        data: null,
      };
    }
  };

  return {
    login,
    ...state,
  };
};
