import { useBulkDeleteSalesMutation } from "@/reduxRTK/services/salesApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useSaleBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting sales
  const [bulkDeleteSales, { isLoading }] = useBulkDeleteSalesMutation();

  const deleteSales = async (saleIds: number[]) => {
    try {
      console.log("Bulk deleting sales with IDs:", saleIds);
      
      const result = await bulkDeleteSales(saleIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete sales");
      }

      showMessage("success", `${saleIds.length} sales deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete sales error:", error);
      showMessage("error", error.message || "Failed to delete sales");
      throw error;
    }
  };

  return {
    bulkDeleteSales: deleteSales,
    isDeleting: isLoading
  };
};
