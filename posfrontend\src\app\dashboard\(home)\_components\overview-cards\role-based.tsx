"use client";

import React, { useState, useEffect } from "react";
import { OverviewCard } from "./card";
import * as icons from "./icons";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useGetDashboardStatsQuery, dashboardApi } from "@/reduxRTK/services/dashboardApi";
import {
  SuperAdminStats,
  AdminStats,
  CashierStats
} from "@/reduxRTK/services/dashboardApi";
import { compactFormat, formatCurrency, formatGrowthRate } from "@/utils/formatUtils";
import { useDispatch } from "react-redux";

// SuperAdmin Overview Cards
export function SuperAdminOverviewCards() {
  const dispatch = useDispatch();

  // Use the dashboard API query hook with refetch function
  const { data: apiData, isLoading, error, refetch } = useGetDashboardStatsQuery(undefined, {
    refetchOnMountOrArgChange: true, // Force refetch when component mounts
    refetchOnFocus: true, // Refetch when window gains focus
    refetchOnReconnect: true, // Refetch when reconnecting
  });

  // Function to clear all caches and force fresh data
  const forceRefresh = () => {
    console.log("🔄 Force refreshing dashboard data...");

    // Clear the dashboard API cache
    dispatch(dashboardApi.util.invalidateTags(['Dashboard']));

    // Clear browser cache for this request
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }

    // Force refetch
    refetch();

    // Also force a page reload as last resort
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  };

  // Cast the data to SuperAdminStats type if it exists
  const dashboardData = apiData?.data as SuperAdminStats | undefined;

  // Debug log to see what data we're getting
  useEffect(() => {
    console.log("🔍 SuperAdmin Dashboard Debug Info:");
    console.log("   Raw API Data:", apiData);
    console.log("   Dashboard Data:", dashboardData);
    console.log("   Is Loading:", isLoading);
    console.log("   Error:", error);

    if (error) {
      console.error("❌ Error fetching dashboard data:", error);
      console.error("❌ Full error object:", JSON.stringify(error, null, 2));
    }

    if (apiData) {
      console.log("✅ API Data received:", JSON.stringify(apiData, null, 2));
      if (apiData.data) {
        console.log("✅ Dashboard stats:", {
          stores: (apiData.data as any).stores?.value,
          revenue: (apiData.data as any).revenue?.value,
          admins: (apiData.data as any).admins?.value,
          users: (apiData.data as any).users?.value
        });
      }
    }

    // Check authentication
    const authData = localStorage.getItem('persist:root');
    if (authData) {
      try {
        const parsedAuth = JSON.parse(authData);
        const authState = JSON.parse(parsedAuth.auth);
        console.log("🔐 Auth token exists:", !!authState.accessToken);
        console.log("🔐 User role:", authState.user?.role);
      } catch (e) {
        console.error("❌ Error parsing auth data:", e);
      }
    } else {
      console.error("❌ No auth data found in localStorage");
    }
  }, [dashboardData, error, isLoading, refetch, apiData]);

  if (isLoading || !dashboardData) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full flex justify-center">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      </div>
    );
  }

  // Check if we have the expected data structure
  if (!('stores' in dashboardData) || !('revenue' in dashboardData) ||
      !('admins' in dashboardData) || !('users' in dashboardData)) {
    console.error("Unexpected dashboard data structure:", dashboardData);
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full text-center">
          <p className="text-red-500">Error loading dashboard data</p>
        </div>
      </div>
    );
  }

  // Extract data from the API response - using real data from backend
  const { stores, revenue, admins, users } = dashboardData;

  console.log("SuperAdmin Dashboard - Real Data:", {
    stores: stores.value,
    revenue: revenue.value,
    admins: admins.value,
    users: users.value
  });

  return (
    <div className="space-y-4">
      {/* Manual Refresh Button */}
      <div className="flex justify-end">
        <button
          onClick={forceRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
          disabled={isLoading}
        >
          {isLoading ? "Refreshing..." : "Force Refresh Data"}
        </button>
      </div>

      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 w-full overflow-hidden">
      <OverviewCard
        label="Active Stores"
        data={{
          ...stores,
          value: compactFormat(stores.value),
          growthRate: formatGrowthRate(stores.growthRate)
        }}
        Icon={icons.Views}
      />

      <OverviewCard
        label="Revenue"
        data={{
          ...revenue,
          value: formatCurrency(revenue.value),
          growthRate: formatGrowthRate(revenue.growthRate)
        }}
        Icon={icons.Profit}
      />

      <OverviewCard
        label="Admin Users"
        data={{
          ...admins,
          value: compactFormat(admins.value),
          growthRate: formatGrowthRate(admins.growthRate)
        }}
        Icon={icons.Product}
      />

      <OverviewCard
        label="Total Users"
        data={{
          ...users,
          value: compactFormat(users.value),
          growthRate: formatGrowthRate(users.growthRate)
        }}
        Icon={icons.Users}
      />
      </div>
    </div>
  );
}

// Admin Overview Cards
export function AdminOverviewCards() {
  // Use the dashboard API query hook
  const { data: apiData, isLoading, error } = useGetDashboardStatsQuery();

  // Cast the data to AdminStats type if it exists
  const dashboardData = apiData?.data as AdminStats | undefined;

  // If there's an error, log it
  useEffect(() => {
    if (error) {
      console.error("Error fetching dashboard data:", error);
    }
  }, [error]);

  if (isLoading || !dashboardData) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full flex justify-center">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      </div>
    );
  }

  // Check if we have the expected data structure
  if (!('sales' in dashboardData) || !('revenue' in dashboardData) ||
      !('products' in dashboardData) || !('cashiers' in dashboardData)) {
    console.error("Unexpected dashboard data structure:", dashboardData);
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full text-center">
          <p className="text-red-500">Error loading dashboard data</p>
        </div>
      </div>
    );
  }

  const { sales, revenue, products, cashiers } = dashboardData;

  console.log("Admin Dashboard - Real Data:", {
    sales: sales.value,
    revenue: revenue.value,
    products: products.value,
    cashiers: cashiers.value
  });

  return (
    <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 w-full overflow-hidden">
      <OverviewCard
        label="Monthly Sales"
        data={{
          ...sales,
          value: compactFormat(sales.value),
          growthRate: formatGrowthRate(sales.growthRate)
        }}
        Icon={icons.Views}
      />

      <OverviewCard
        label="Revenue"
        data={{
          ...revenue,
          value: formatCurrency(revenue.value),
          growthRate: formatGrowthRate(revenue.growthRate)
        }}
        Icon={icons.Profit}
      />

      <OverviewCard
        label="Store Products"
        data={{
          ...products,
          value: compactFormat(products.value),
          growthRate: formatGrowthRate(products.growthRate)
        }}
        Icon={icons.Product}
      />

      <OverviewCard
        label="Active Cashiers"
        data={{
          ...cashiers,
          value: compactFormat(cashiers.value),
          growthRate: formatGrowthRate(cashiers.growthRate)
        }}
        Icon={icons.Users}
      />
    </div>
  );
}

// Cashier Overview Cards
export function CashierOverviewCards() {
  // Use the dashboard API query hook
  const { data: apiData, isLoading, error } = useGetDashboardStatsQuery();

  // Cast the data to CashierStats type if it exists
  const dashboardData = apiData?.data as CashierStats | undefined;

  // If there's an error, log it
  useEffect(() => {
    if (error) {
      console.error("Error fetching dashboard data:", error);
    }
  }, [error]);

  if (isLoading || !dashboardData) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full flex justify-center">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      </div>
    );
  }

  // Check if we have the expected data structure
  if (!('todaySales' in dashboardData) || !('todayRevenue' in dashboardData) ||
      !('totalProducts' in dashboardData) || !('totalSales' in dashboardData)) {
    console.error("Unexpected dashboard data structure:", dashboardData);
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full text-center">
          <p className="text-red-500">Error loading dashboard data</p>
        </div>
      </div>
    );
  }

  const { todaySales, todayRevenue, totalProducts, totalSales } = dashboardData;

  console.log("Cashier Dashboard - Real Data:", {
    todaySales: todaySales.value,
    todayRevenue: todayRevenue.value,
    totalProducts: totalProducts.value,
    totalSales: totalSales.value
  });

  return (
    <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 w-full overflow-hidden">
      <OverviewCard
        label="Today's Sales"
        data={{
          ...todaySales,
          value: compactFormat(todaySales.value),
          growthRate: formatGrowthRate(todaySales.growthRate)
        }}
        Icon={icons.Views}
      />

      <OverviewCard
        label="Revenue Today"
        data={{
          ...todayRevenue,
          value: formatCurrency(todayRevenue.value),
          growthRate: formatGrowthRate(todayRevenue.growthRate)
        }}
        Icon={icons.Profit}
      />

      <OverviewCard
        label="Total Products"
        data={{
          ...totalProducts,
          value: compactFormat(totalProducts.value),
          growthRate: formatGrowthRate(totalProducts.growthRate)
        }}
        Icon={icons.Product}
      />

      <OverviewCard
        label="Total Sales"
        data={{
          ...totalSales,
          value: compactFormat(totalSales.value),
          growthRate: formatGrowthRate(totalSales.growthRate)
        }}
        Icon={icons.Users}
      />
    </div>
  );
}
