"use client";

import { useCreateSaleMutation, CreateSaleDto } from "@/reduxRTK/services/salesApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useSaleCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a sale
  const [createSale, { isLoading: isSubmitting }] = useCreateSaleMutation();

  // Function to handle sale creation
  const handleCreateSale = async (saleData: CreateSaleDto) => {
    try {
      const response = await createSale(saleData).unwrap();
      
      if (response.success) {
        showMessage("success", "Sale created successfully");
        if (onSuccess) onSuccess();
        return { success: true, data: response.data };
      } else {
        showMessage("error", response.message || "Failed to create sale");
        return { success: false, message: response.message };
      }
    } catch (error: any) {
      const errorMessage = error.data?.message || error.message || "An error occurred while creating the sale";
      showMessage("error", errorMessage);
      return { success: false, message: errorMessage };
    }
  };

  return {
    createSale: handleCreateSale,
    isSubmitting,
  };
};
