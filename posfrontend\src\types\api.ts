// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// Generic API error type
export interface ApiError {
  success: false;
  message: string;
  error?: string;
}

// Pagination types
export interface PaginationMeta {
  page: number;
  perPage: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends ApiResponse<{
  items: T[];
  pagination: PaginationMeta;
}> {}

// Common API request types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  [key: string]: any;
}

// Combined query params
export interface QueryParams extends PaginationParams, SortParams, FilterParams {}
