"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSupplierRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const supplierService_1 = require("../services/supplierService");
const modeValidator_1 = require("../utils/modeValidator");
const handleSupplierRequest = async (req, res) => {
    const { mode, supplierId, page, limit, ...data } = req.body;
    const requester = req.user; // ✅ Use `req.user` from `authMiddleware`
    // ✅ Validate mode
    const validModes = ["createnew", "update", "delete", "retrieve"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            // ✅ Create New Supplier
            case "createnew": {
                const requiredFields = ["name", "phone"];
                const missingFields = requiredFields.filter((field) => !data[field]);
                if (missingFields.length > 0) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, `Missing required fields: ${missingFields.join(", ")}`);
                }
                const newSupplier = await (0, supplierService_1.createSupplier)(requester, {
                    name: data.name,
                    contactPerson: data.contactPerson,
                    phone: data.phone,
                    email: data.email,
                    address: data.address,
                });
                return (0, responseHelper_1.sendResponse)(res, 201, true, "Supplier created successfully.", newSupplier);
            }
            // ✅ Retrieve Suppliers (Single or All)
            case "retrieve": {
                if (supplierId) {
                    // Fetch a single supplier by ID
                    const supplier = await (0, supplierService_1.getSupplierById)(requester, Number(supplierId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Supplier retrieved successfully.", supplier);
                }
                else {
                    // Fetch all suppliers with pagination
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    const suppliers = await (0, supplierService_1.getAllSuppliers)(requester, pageNum, limitNum);
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Suppliers retrieved successfully.", suppliers);
                }
            }
            // ✅ Update Supplier
            case "update": {
                if (!supplierId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Supplier ID is required for updating.");
                }
                const updatedSupplier = await (0, supplierService_1.updateSupplierById)(requester, Number(supplierId), data);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Supplier updated successfully.", updatedSupplier);
            }
            // ✅ Delete Supplier (Single or Multiple)
            case "delete": {
                // Check if we have supplierIds (array) or supplierId (single)
                const { supplierIds } = req.body;
                if (!supplierId && !supplierIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Supplier ID(s) are required for deletion.");
                }
                // Use supplierIds if provided, otherwise use single supplierId
                const idsToDelete = supplierIds
                    ? (Array.isArray(supplierIds) ? supplierIds : [supplierIds])
                    : [Number(supplierId)];
                const result = await (0, supplierService_1.deleteSupplierById)(requester, idsToDelete);
                return (0, responseHelper_1.sendResponse)(res, 200, true, result.message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handleSupplierRequest = handleSupplierRequest;
