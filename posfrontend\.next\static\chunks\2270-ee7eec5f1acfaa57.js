(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2270],{4768:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(85407),o=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};var l=n(84021);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},86260:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(85407),o=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var l=n(84021);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},76170:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(85407),o=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var l=n(84021);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},75365:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(85407),o=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M723 620.5C666.8 571.6 593.4 542 513 542s-153.8 29.6-210.1 78.6a8.1 8.1 0 00-.8 11.2l36 42.9c2.9 3.4 8 3.8 11.4.9C393.1 637.2 450.3 614 513 614s119.9 23.2 163.5 61.5c3.4 2.9 8.5 2.5 11.4-.9l36-42.9c2.8-3.3 2.4-8.3-.9-11.2zm117.4-140.1C751.7 406.5 637.6 362 513 362s-238.7 44.5-327.5 118.4a8.05 8.05 0 00-1 11.3l36 42.9c2.8 3.4 7.9 3.8 11.2 1C308 472.2 406.1 434 513 434s205 38.2 281.2 101.6c3.4 2.8 8.4 2.4 11.2-1l36-42.9c2.8-3.4 2.4-8.5-1-11.3zm116.7-139C835.7 241.8 680.3 182 511 182c-168.2 0-322.6 59-443.7 157.4a8 8 0 00-1.1 11.4l36 42.9c2.8 3.3 7.8 3.8 11.1 1.1C222 306.7 360.3 254 511 254c151.8 0 291 53.5 400 142.7 3.4 2.8 8.4 2.3 11.2-1.1l36-42.9c2.9-3.4 2.4-8.5-1.1-11.3zM448 778a64 64 0 10128 0 64 64 0 10-128 0z"}}]},name:"wifi",theme:"outlined"};var l=n(84021);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},73937:(e,t,n)=>{"use strict";n.d(t,{A:()=>ev});var r=n(12115),o=n(86260),a=n(4617),l=n.n(a),i=n(30377),c=n(63588),s=n(66105),u=n(35015),d=n(70527),f=n(15231),p=n(88959),m=n(31049),g=n(55315),b=n(6457),y=n(85407);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var v=n(84021),x=r.forwardRef(function(e,t){return r.createElement(v.A,(0,y.A)({},e,{ref:t,icon:h}))}),E=n(23672),w=n(58292),O=n(25392),C=n(70695),j=n(1086),S=n(28405),k=n(67548);let A=(e,t,n,r)=>{let{titleMarginBottom:o,fontWeightStrong:a}=r;return{marginBottom:o,color:n,fontWeight:a,fontSize:e,lineHeight:t}},I=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=A(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)}),t},P=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,C.Y1)(e)),{userSelect:"text",["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},M=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:S.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),D=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:"calc(1em - ".concat((0,k.zA)(n),")")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},R=e=>({["".concat(e.componentCls,"-copy-success")]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},["".concat(e.componentCls,"-copy-icon-only")]:{marginInlineStart:0}}),T=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),B=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccessText},["&".concat(t,"-warning")]:{color:e.colorWarningText},["&".concat(t,"-danger")]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},I(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),M(e)),P(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-collapse,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,C.Y1)(e)),{marginInlineStart:e.marginXXS})}),D(e)),R(e)),T()),{"&-rtl":{direction:"rtl"}})}},L=(0,j.OF)("Typography",e=>[B(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),z=e=>{let{prefixCls:t,"aria-label":n,className:o,style:a,direction:i,maxLength:c,autoSize:s=!0,value:u,onSave:d,onCancel:f,onEnd:p,component:m,enterIcon:g=r.createElement(x,null)}=e,b=r.useRef(null),y=r.useRef(!1),h=r.useRef(null),[v,C]=r.useState(u);r.useEffect(()=>{C(u)},[u]),r.useEffect(()=>{var e;if(null===(e=b.current)||void 0===e?void 0:e.resizableTextArea){let{textArea:e}=b.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let j=()=>{d(v.trim())},[S,k,A]=L(t),I=l()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===i,["".concat(t,"-").concat(m)]:!!m},o,k,A);return S(r.createElement("div",{className:I,style:a},r.createElement(O.A,{ref:b,maxLength:c,value:v,onChange:e=>{let{target:t}=e;C(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(h.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:r,metaKey:o,shiftKey:a}=e;h.current!==t||y.current||n||r||o||a||(t===E.A.ENTER?(j(),null==p||p()):t===E.A.ESC&&f())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{j()},"aria-label":n,rows:1,autoSize:s}),null!==g?(0,w.Ob)(g,{className:"".concat(t,"-edit-content-confirm")}):null))};var N=n(21079),_=n.n(N),H=n(97262);let W=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&null==e?[]:Array.isArray(e)?e:[e]},F=e=>{let{copyConfig:t,children:n}=e,[o,a]=r.useState(!1),[l,i]=r.useState(!1),c=r.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},u={};return t.format&&(u.format=t.format),r.useEffect(()=>s,[]),{copied:o,copyLoading:l,onClick:(0,H.A)(e=>(function(e,t,n,r){return new(n||(n=Promise))(function(o,a){function l(e){try{c(r.next(e))}catch(e){a(e)}}function i(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(l,i)}c((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var r;null==e||e.preventDefault(),null==e||e.stopPropagation(),i(!0);try{let o="function"==typeof t.text?yield t.text():t.text;_()(o||W(n,!0).join("")||"",u),i(!1),a(!0),s(),c.current=setTimeout(()=>{a(!1)},3e3),null===(r=t.onCopy)||void 0===r||r.call(t,e)}catch(e){throw i(!1),e}}))}};function U(e,t){return r.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}let K=e=>{let t=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{t.current=e}),t.current},V=(e,t,n)=>(0,r.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,r.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]);var q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let X=r.forwardRef((e,t)=>{let{prefixCls:n,component:o="article",className:a,rootClassName:i,setContentRef:c,children:s,direction:u,style:d}=e,p=q(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:g,direction:b,className:y,style:h}=(0,m.TP)("typography"),v=c?(0,f.K4)(t,c):t,x=g("typography",n),[E,w,O]=L(x),C=l()(x,y,{["".concat(x,"-rtl")]:"rtl"===(null!=u?u:b)},a,i,w,O),j=Object.assign(Object.assign({},h),d);return E(r.createElement(o,Object.assign({className:C,style:j,ref:v},p),s))});var Q=n(4768);let Y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var Z=r.forwardRef(function(e,t){return r.createElement(v.A,(0,y.A)({},e,{ref:t,icon:Y}))}),G=n(16419);function J(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function $(e,t,n){return!0===e||void 0===e?t:e||n&&t}let ee=e=>["string","number"].includes(typeof e),et=e=>{let{prefixCls:t,copied:n,locale:o,iconOnly:a,tooltips:i,icon:c,tabIndex:s,onCopy:u,loading:d}=e,f=J(i),p=J(c),{copied:m,copy:g}=null!=o?o:{},y=n?m:g,h=$(f[n?1:0],y),v="string"==typeof h?h:y;return r.createElement(b.A,{title:h},r.createElement("button",{type:"button",className:l()("".concat(t,"-copy"),{["".concat(t,"-copy-success")]:n,["".concat(t,"-copy-icon-only")]:a}),onClick:u,"aria-label":v,tabIndex:s},n?$(p[1],r.createElement(Q.A,null),!0):$(p[0],d?r.createElement(G.A,null):r.createElement(Z,null),!0)))};var en=n(39014);let er=r.forwardRef((e,t)=>{let{style:n,children:o}=e,a=r.useRef(null);return r.useImperativeHandle(t,()=>({isExceed:()=>{let e=a.current;return e.scrollHeight>e.clientHeight},getHeight:()=>a.current.clientHeight})),r.createElement("span",{"aria-hidden":!0,ref:a,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},o)}),eo=e=>e.reduce((e,t)=>e+(ee(t)?String(t).length:1),0);function ea(e,t){let n=0,r=[];for(let o=0;o<e.length;o+=1){if(n===t)return r;let a=e[o],l=n+(ee(a)?String(a).length:1);if(l>t){let e=t-n;return r.push(String(a).slice(0,e)),r}r.push(a),n=l}return e}let el={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ei(e){let{enableMeasure:t,width:n,text:o,children:a,rows:l,expanded:i,miscDeps:u,onEllipsis:d}=e,f=r.useMemo(()=>(0,c.A)(o),[o]),p=r.useMemo(()=>eo(f),[o]),m=r.useMemo(()=>a(f,!1),[o]),[g,b]=r.useState(null),y=r.useRef(null),h=r.useRef(null),v=r.useRef(null),x=r.useRef(null),E=r.useRef(null),[w,O]=r.useState(!1),[C,j]=r.useState(0),[S,k]=r.useState(0),[A,I]=r.useState(null);(0,s.A)(()=>{t&&n&&p?j(1):j(0)},[n,o,l,t,f]),(0,s.A)(()=>{var e,t,n,r;if(1===C)j(2),I(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===C){let o=!!(null===(e=v.current)||void 0===e?void 0:e.isExceed());j(o?3:4),b(o?[0,p]:null),O(o),k(Math.max((null===(t=v.current)||void 0===t?void 0:t.getHeight())||0,(1===l?0:(null===(n=x.current)||void 0===n?void 0:n.getHeight())||0)+((null===(r=E.current)||void 0===r?void 0:r.getHeight())||0))+1),d(o)}},[C]);let P=g?Math.ceil((g[0]+g[1])/2):0;(0,s.A)(()=>{var e;let[t,n]=g||[0,0];if(t!==n){let r=((null===(e=y.current)||void 0===e?void 0:e.getHeight())||0)>S,o=P;n-t==1&&(o=r?t:n),b(r?[t,o]:[o,n])}},[g,P]);let M=r.useMemo(()=>{if(!t)return a(f,!1);if(3!==C||!g||g[0]!==g[1]){let e=a(f,!1);return[4,0].includes(C)?e:r.createElement("span",{style:Object.assign(Object.assign({},el),{WebkitLineClamp:l})},e)}return a(i?f:ea(f,g[0]),w)},[i,C,g,f].concat((0,en.A)(u))),D={width:n,margin:0,padding:0,whiteSpace:"nowrap"===A?"normal":"inherit"};return r.createElement(r.Fragment,null,M,2===C&&r.createElement(r.Fragment,null,r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},D),el),{WebkitLineClamp:l}),ref:v},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},D),el),{WebkitLineClamp:l-1}),ref:x},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},D),el),{WebkitLineClamp:1}),ref:E},a([],!0))),3===C&&g&&g[0]!==g[1]&&r.createElement(er,{style:Object.assign(Object.assign({},D),{top:400}),ref:y},a(ea(f,P),!0)),1===C&&r.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}let ec=e=>{let{enableEllipsis:t,isEllipsis:n,children:o,tooltipProps:a}=e;return(null==a?void 0:a.title)&&t?r.createElement(b.A,Object.assign({open:!!n&&void 0},a),o):o};var es=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eu=r.forwardRef((e,t)=>{var n;let{prefixCls:a,className:y,style:h,type:v,disabled:x,children:E,ellipsis:w,editable:O,copyable:C,component:j,title:S}=e,k=es(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:A,direction:I}=r.useContext(m.QO),[P]=(0,g.A)("Text"),M=r.useRef(null),D=r.useRef(null),R=A("typography",a),T=(0,d.A)(k,["mark","code","delete","underline","strong","keyboard","italic"]),[B,L]=U(O),[N,_]=(0,u.A)(!1,{value:L.editing}),{triggerType:H=["icon"]}=L,W=e=>{var t;e&&(null===(t=L.onStart)||void 0===t||t.call(L)),_(e)},q=K(N);(0,s.A)(()=>{var e;!N&&q&&(null===(e=D.current)||void 0===e||e.focus())},[N]);let Q=e=>{null==e||e.preventDefault(),W(!0)},[Y,Z]=U(C),{copied:G,copyLoading:J,onClick:$}=F({copyConfig:Z,children:E}),[en,er]=r.useState(!1),[eo,ea]=r.useState(!1),[el,eu]=r.useState(!1),[ed,ef]=r.useState(!1),[ep,em]=r.useState(!0),[eg,eb]=U(w,{expandable:!1,symbol:e=>e?null==P?void 0:P.collapse:null==P?void 0:P.expand}),[ey,eh]=(0,u.A)(eb.defaultExpanded||!1,{value:eb.expanded}),ev=eg&&(!ey||"collapsible"===eb.expandable),{rows:ex=1}=eb,eE=r.useMemo(()=>ev&&(void 0!==eb.suffix||eb.onEllipsis||eb.expandable||B||Y),[ev,eb,B,Y]);(0,s.A)(()=>{eg&&!eE&&(er((0,p.F)("webkitLineClamp")),ea((0,p.F)("textOverflow")))},[eE,eg]);let[ew,eO]=r.useState(ev),eC=r.useMemo(()=>!eE&&(1===ex?eo:en),[eE,eo,en]);(0,s.A)(()=>{eO(eC&&ev)},[eC,ev]);let ej=ev&&(ew?ed:el),eS=ev&&1===ex&&ew,ek=ev&&ex>1&&ew,eA=(e,t)=>{var n;eh(t.expanded),null===(n=eb.onExpand)||void 0===n||n.call(eb,e,t)},[eI,eP]=r.useState(0),eM=e=>{var t;eu(e),el!==e&&(null===(t=eb.onEllipsis)||void 0===t||t.call(eb,e))};r.useEffect(()=>{let e=M.current;if(eg&&ew&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),r=t.getBoundingClientRect();return e.removeChild(t),n.left>r.left||r.right>n.right||n.top>r.top||r.bottom>n.bottom}(e);ed!==t&&ef(t)}},[eg,ew,E,ek,ep,eI]),r.useEffect(()=>{let e=M.current;if("undefined"==typeof IntersectionObserver||!e||!ew||!ev)return;let t=new IntersectionObserver(()=>{em(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[ew,ev]);let eD=V(eb.tooltip,L.text,E),eR=r.useMemo(()=>{if(eg&&!ew)return[L.text,E,S,eD.title].find(ee)},[eg,ew,S,eD.title,ej]);if(N)return r.createElement(z,{value:null!==(n=L.text)&&void 0!==n?n:"string"==typeof E?E:"",onSave:e=>{var t;null===(t=L.onChange)||void 0===t||t.call(L,e),W(!1)},onCancel:()=>{var e;null===(e=L.onCancel)||void 0===e||e.call(L),W(!1)},onEnd:L.onEnd,prefixCls:R,className:y,style:h,direction:I,component:j,maxLength:L.maxLength,autoSize:L.autoSize,enterIcon:L.enterIcon});let eT=()=>{let{expandable:e,symbol:t}=eb;return e?r.createElement("button",{type:"button",key:"expand",className:"".concat(R,"-").concat(ey?"collapse":"expand"),onClick:e=>eA(e,{expanded:!ey}),"aria-label":ey?P.collapse:null==P?void 0:P.expand},"function"==typeof t?t(ey):t):null},eB=()=>{if(!B)return;let{icon:e,tooltip:t,tabIndex:n}=L,a=(0,c.A)(t)[0]||(null==P?void 0:P.edit),l="string"==typeof a?a:"";return H.includes("icon")?r.createElement(b.A,{key:"edit",title:!1===t?"":a},r.createElement("button",{type:"button",ref:D,className:"".concat(R,"-edit"),onClick:Q,"aria-label":l,tabIndex:n},e||r.createElement(o.A,{role:"button"}))):null},eL=()=>Y?r.createElement(et,Object.assign({key:"copy"},Z,{prefixCls:R,copied:G,locale:P,onCopy:$,loading:J,iconOnly:null==E})):null,ez=e=>[e&&eT(),eB(),eL()],eN=e=>[e&&!ey&&r.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),eb.suffix,ez(e)];return r.createElement(i.A,{onResize:e=>{let{offsetWidth:t}=e;eP(t)},disabled:!ev},n=>r.createElement(ec,{tooltipProps:eD,enableEllipsis:ev,isEllipsis:ej},r.createElement(X,Object.assign({className:l()({["".concat(R,"-").concat(v)]:v,["".concat(R,"-disabled")]:x,["".concat(R,"-ellipsis")]:eg,["".concat(R,"-ellipsis-single-line")]:eS,["".concat(R,"-ellipsis-multiple-line")]:ek},y),prefixCls:a,style:Object.assign(Object.assign({},h),{WebkitLineClamp:ek?ex:void 0}),component:j,ref:(0,f.K4)(n,M,t),direction:I,onClick:H.includes("text")?Q:void 0,"aria-label":null==eR?void 0:eR.toString(),title:S},T),r.createElement(ei,{enableMeasure:ev&&!ew,text:E,rows:ex,width:eI,onEllipsis:eM,expanded:ey,miscDeps:[G,ey,J,B,Y,P]},(t,n)=>(function(e,t){let{mark:n,code:o,underline:a,delete:l,strong:i,keyboard:c,italic:s}=e,u=t;function d(e,t){t&&(u=r.createElement(e,{},u))}return d("strong",i),d("u",a),d("del",l),d("code",o),d("mark",n),d("kbd",c),d("i",s),u})(e,r.createElement(r.Fragment,null,t.length>0&&n&&!ey&&eR?r.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eN(n)))))))});var ed=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ef=r.forwardRef((e,t)=>{var{ellipsis:n,rel:o}=e,a=ed(e,["ellipsis","rel"]);let l=Object.assign(Object.assign({},a),{rel:void 0===o&&"_blank"===a.target?"noopener noreferrer":o});return delete l.navigate,r.createElement(eu,Object.assign({},l,{ref:t,ellipsis:!!n,component:"a"}))}),ep=r.forwardRef((e,t)=>r.createElement(eu,Object.assign({ref:t},e,{component:"div"})));var em=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eg=r.forwardRef((e,t)=>{var{ellipsis:n}=e,o=em(e,["ellipsis"]);let a=r.useMemo(()=>n&&"object"==typeof n?(0,d.A)(n,["expandable","rows"]):n,[n]);return r.createElement(eu,Object.assign({ref:t},o,{ellipsis:a,component:"span"}))});var eb=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ey=[1,2,3,4,5],eh=r.forwardRef((e,t)=>{let{level:n=1}=e,o=eb(e,["level"]),a=ey.includes(n)?"h".concat(n):"h1";return r.createElement(eu,Object.assign({ref:t},o,{component:a}))});X.Text=eg,X.Link=ef,X.Title=eh,X.Paragraph=ep;let ev=X},21079:(e,t,n)=>{"use strict";var r=n(9018),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,a,l,i,c,s,u,d,f=!1;t||(t={}),l=t.debug||!1;try{if(c=r(),s=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){l&&console.warn("unable to use e.clipboardData"),l&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=o[t.format]||o.default;window.clipboardData.setData(r,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(d),s.selectNodeContents(d),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");f=!0}catch(r){l&&console.error("unable to copy using execCommand: ",r),l&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),f=!0}catch(r){l&&console.error("unable to copy using clipboardData: ",r),l&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=n.replace(/#{\s*key\s*}/g,a),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),d&&document.body.removeChild(d),c()}return f}},48173:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return g}});let r=n(60306),o=n(95155),a=r._(n(12115)),l=n(70180),i=n(71394),c=n(64116),s=n(4445),u=n(45353),d=n(12170),f=n(49544);function p(e,t,n){"undefined"!=typeof window&&(async()=>e.prefetch(t,n))().catch(e=>{})}function m(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}n(42363);let g=a.default.forwardRef(function(e,t){let n,r;let{href:l,as:g,children:b,prefetch:y=null,passHref:h,replace:v,shallow:x,scroll:E,onClick:w,onMouseEnter:O,onTouchStart:C,legacyBehavior:j=!1,...S}=e;n=b,j&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let k=a.default.useContext(i.AppRouterContext),A=!1!==y,I=null===y?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:P,as:M}=a.default.useMemo(()=>{let e=m(l);return{href:e,as:g?m(g):e}},[l,g]),D=a.default.useRef(P),R=a.default.useRef(M);j&&(r=a.default.Children.only(n));let T=j?r&&"object"==typeof r&&r.ref:t,[B,L,z]=(0,c.useIntersection)({rootMargin:"200px"}),N=a.default.useCallback(e=>{(R.current!==M||D.current!==P)&&(z(),R.current=M,D.current=P),B(e)},[M,P,z,B]),_=(0,u.useMergedRef)(N,T);a.default.useEffect(()=>{k&&L&&A&&p(k,P,{kind:I})},[M,P,L,A,k,I]);let H={ref:_,onClick(e){j||"function"!=typeof w||w(e),j&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),k&&!e.defaultPrevented&&function(e,t,n,r,o,l,i){let{nodeName:c}=e.currentTarget;"A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||(e.preventDefault(),a.default.startTransition(()=>{let e=null==i||i;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:l,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})}))}(e,k,P,M,v,x,E)},onMouseEnter(e){j||"function"!=typeof O||O(e),j&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),k&&A&&p(k,P,{kind:I})},onTouchStart:function(e){j||"function"!=typeof C||C(e),j&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),k&&A&&p(k,P,{kind:I})}};return(0,d.isAbsoluteUrl)(M)?H.href=M:j&&!h&&("a"!==r.type||"href"in r.props)||(H.href=(0,f.addBasePath)(M)),j?a.default.cloneElement(r,H):(0,o.jsx)("a",{...S,...H,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68571:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64116:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return c}});let r=n(12115),o=n(68571),a="function"==typeof IntersectionObserver,l=new Map,i=[];function c(e){let{rootRef:t,rootMargin:n,disabled:c}=e,s=c||!a,[u,d]=(0,r.useState)(!1),f=(0,r.useRef)(null),p=(0,r.useCallback)(e=>{f.current=e},[]);return(0,r.useEffect)(()=>{if(a){if(s||u)return;let e=f.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:a}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=i.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=l.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},i.push(n),l.set(n,t),t}(n);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(r);let e=i.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!u){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,n,t,u,f.current]),[p,u,(0,r.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70180:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let r=n(29955)._(n(54156)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",c=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),c&&"object"==typeof c&&(c=String(r.urlQueryToSearchParams(c)));let u=e.search||c&&"?"+c||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),i&&"#"!==i[0]&&(i="#"+i),u&&"?"!==u[0]&&(u="?"+u),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},54156:(e,t)=>{"use strict";function n(e){let t={};return e.forEach((e,n)=>{void 0===t[n]?t[n]=e:Array.isArray(t[n])?t[n].push(e):t[n]=[t[n],e]}),t}function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,o]=e;Array.isArray(o)?o.forEach(e=>t.append(n,r(e))):t.set(n,r(o))}),t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,n)=>e.append(n,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},12170:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return h},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return b},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return c},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.');return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class h extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},88959:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var r=n(30306),o=function(e){if((0,r.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some(function(e){return e in n.style})}return!1},a=function(e,t){if(!o(e))return!1;var n=document.createElement("div"),r=n.style[e];return n.style[e]=t,n.style[e]!==r};function l(e,t){return Array.isArray(e)||void 0===t?o(e):a(e,t)}},9018:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},10733:(e,t,n)=>{"use strict";let r,o;n.d(t,{P2:()=>f});let a=(e,t)=>t.some(t=>e instanceof t),l=new WeakMap,i=new WeakMap,c=new WeakMap,s={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return l.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return u(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function u(e){var t;if(e instanceof IDBRequest)return function(e){let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("success",o),e.removeEventListener("error",a)},o=()=>{t(u(e.result)),r()},a=()=>{n(e.error),r()};e.addEventListener("success",o),e.addEventListener("error",a)});return c.set(t,e),t}(e);if(i.has(e))return i.get(e);let n="function"==typeof(t=e)?(o||(o=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(d(this),e),u(this.request)}:function(...e){return u(t.apply(d(this),e))}:(t instanceof IDBTransaction&&function(e){if(l.has(e))return;let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",a),e.removeEventListener("abort",a)},o=()=>{t(),r()},a=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",o),e.addEventListener("error",a),e.addEventListener("abort",a)});l.set(e,t)}(t),a(t,r||(r=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,s):t;return n!==e&&(i.set(e,n),c.set(n,e)),n}let d=e=>c.get(e);function f(e,t,{blocked:n,upgrade:r,blocking:o,terminated:a}={}){let l=indexedDB.open(e,t),i=u(l);return r&&l.addEventListener("upgradeneeded",e=>{r(u(l.result),e.oldVersion,e.newVersion,u(l.transaction),e)}),n&&l.addEventListener("blocked",e=>n(e.oldVersion,e.newVersion,e)),i.then(e=>{a&&e.addEventListener("close",()=>a()),o&&e.addEventListener("versionchange",e=>o(e.oldVersion,e.newVersion,e))}).catch(()=>{}),i}let p=["get","getKey","getAll","getAllKeys","count"],m=["put","add","delete","clear"],g=new Map;function b(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(g.get(t))return g.get(t);let n=t.replace(/FromIndex$/,""),r=t!==n,o=m.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(o||p.includes(n)))return;let a=async function(e,...t){let a=this.transaction(e,o?"readwrite":"readonly"),l=a.store;return r&&(l=l.index(t.shift())),(await Promise.all([l[n](...t),o&&a.done]))[0]};return g.set(t,a),a}s=(e=>({...e,get:(t,n,r)=>b(t,n)||e.get(t,n,r),has:(t,n)=>!!b(t,n)||e.has(t,n)}))(s);let y=["continue","continuePrimaryKey","advance"],h={},v=new WeakMap,x=new WeakMap,E={get(e,t){if(!y.includes(t))return e[t];let n=h[t];return n||(n=h[t]=function(...e){v.set(this,x.get(this)[t](...e))}),n}};async function*w(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;let n=new Proxy(t,E);for(x.set(n,t),c.set(n,d(t));t;)yield n,t=await (v.get(n)||t.continue()),v.delete(n)}function O(e,t){return t===Symbol.asyncIterator&&a(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&a(e,[IDBIndex,IDBObjectStore])}s=(e=>({...e,get:(t,n,r)=>O(t,n)?w:e.get(t,n,r),has:(t,n)=>O(t,n)||e.has(t,n)}))(s)}}]);