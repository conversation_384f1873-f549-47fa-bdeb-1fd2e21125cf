"use client";

import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { Card, Alert } from "antd";

/**
 * SimpleDashboard component for testing
 * A minimal dashboard that should work even if other components have issues
 */
const SimpleDashboard: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth.user);

  console.log("SimpleDashboard - User:", user);

  if (!user) {
    return (
      <Alert
        message="Authentication Required"
        description="Please log in to view the dashboard."
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div className="p-4">
      <Card title="Simple Dashboard" className="mb-4">
        <p className="text-lg">Welcome, {user.name}!</p>
        <p>You are logged in as: <strong>{user.role}</strong></p>
        <p>Email: {user.email}</p>
        <p>Payment Status: {user.paymentStatus}</p>
      </Card>
      
      <Alert
        message="Debug Mode"
        description="This is a simplified dashboard for debugging purposes."
        type="info"
        showIcon
      />
    </div>
  );
};

export default SimpleDashboard;
