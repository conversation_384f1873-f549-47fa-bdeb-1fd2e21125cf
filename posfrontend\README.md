# POS System Frontend

A modern Point of Sale (POS) system frontend built with Next.js, featuring comprehensive inventory management, sales tracking, and user management capabilities.

## Features

- **Dashboard Analytics**: Real-time sales and revenue tracking with interactive charts
- **Inventory Management**: Complete product and category management system
- **Sales Management**: Process sales transactions and track sales history
- **User Management**: Role-based access control (SuperAdmin, Admin, Cashier)
- **Store Management**: Multi-store support with store-specific operations
- **Supplier Management**: Track suppliers and purchase orders
- **Receipt Generation**: Digital receipt generation and management
- **Payment Integration**: Paystack payment gateway integration
- **Responsive Design**: Mobile-friendly interface with light theme
- **Real-time Updates**: Live data synchronization across the application

Built with Next.js 15, TypeScript, Tailwind CSS, and Ant Design components.

## Installation

1. Clone the repository and navigate to the frontend directory:

```bash
cd posfrontend
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
- `NEXT_PUBLIC_API_BASE_URL`: Backend API URL
- `NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY`: Paystack public key for payments

4. Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Ant Design
- **Charts**: ApexCharts
- **State Management**: Redux Toolkit with RTK Query
- **Authentication**: JWT-based authentication
- **Payment**: Paystack integration
- **Icons**: Ant Design Icons

## Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # Reusable UI components
├── hooks/              # Custom React hooks
├── reduxRTK/           # Redux store and API slices
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── css/                # Global styles and fonts
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check
