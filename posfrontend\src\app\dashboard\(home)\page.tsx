"use client";

import React, { useState, useEffect } from "react";
import DashboardContent from "@/components/Dashboard/DashboardContent";
import { useSearchParams } from "next/navigation";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

export default function Home() {
  const searchParams = useSearchParams();
  const timeFrame = searchParams?.get("selected_time_frame") || null;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log("Dashboard Home mounted", { timeFrame });

    // Add a small delay to ensure smooth loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [timeFrame]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
      </div>
    );
  }

  return <DashboardContent selected_time_frame={timeFrame} />;
}
