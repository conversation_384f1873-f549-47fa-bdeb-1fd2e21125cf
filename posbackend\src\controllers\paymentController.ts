import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  initializePayment,
  getAllPayments,
  getPaymentById,
  verifyPayment,
  verifyPaystackPaymentByReference,
  getUserPaymentHistory
} from "../services/paymentService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

/**
 * Handle Payment Requests
 * This controller handles all payment-related operations
 */
export const handlePaymentRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, paymentId, page, limit, amount, provider, transactionId, userId, email, callbackUrl, reference, subscriptionPeriod } = req.body;
  const requester = req.user as DecodedToken;

  // Validate mode
  const validModes = ["initialize", "retrieve", "verify", "verify-paystack", "history"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      /**
       * Initialize a Paystack payment
       * Required fields: amount, email
       */
      case "initialize": {
        console.log('=== PAYMENT CONTROLLER DEBUG ===');
        console.log('Request body:', JSON.stringify(req.body, null, 2));
        console.log('Amount:', amount, typeof amount);
        console.log('Email:', email);
        console.log('Callback URL:', callbackUrl);
        console.log('Subscription Period:', subscriptionPeriod);
        console.log('User ID:', requester.id);
        console.log('User Role:', requester.role);
        console.log('================================');

        // Validate required fields
        if (!amount || !email) {
          return sendResponse(
            res,
            400,
            false,
            "Amount and email are required."
          );
        }

        // Initialize payment
        const paymentInit = await initializePayment(requester, {
          amount: Number(amount),
          email,
          callbackUrl,
          subscriptionPeriod: subscriptionPeriod ? Number(subscriptionPeriod) : 1
        });

        return sendResponse(
          res,
          201,
          true,
          "Payment initialized successfully.",
          paymentInit
        );
      }



      /**
       * Retrieve payments (single or all)
       * If paymentId is provided, retrieve a single payment
       * Otherwise, retrieve all payments with pagination
       */
      case "retrieve": {
        if (paymentId) {
          // Retrieve a single payment
          const payment = await getPaymentById(requester, Number(paymentId));
          return sendResponse(
            res,
            200,
            true,
            "Payment retrieved successfully.",
            payment
          );
        } else {
          // Retrieve all payments with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const payments = await getAllPayments(requester, pageNum, limitNum);
          return sendResponse(
            res,
            200,
            true,
            "Payments retrieved successfully.",
            payments
          );
        }
      }

      /**
       * Verify a Paystack payment by reference
       * Required field: reference
       */
      case "verify-paystack": {
        if (!reference) {
          return sendResponse(
            res,
            400,
            false,
            "Paystack reference is required for verification."
          );
        }

        const verificationResult = await verifyPaystackPaymentByReference(requester, reference);
        return sendResponse(
          res,
          200,
          verificationResult.success,
          verificationResult.message,
          verificationResult.payment
        );
      }

      /**
       * Verify a Paystack payment by transaction ID
       * Required field: transactionId
       */
      case "verify": {
        if (!transactionId) {
          return sendResponse(
            res,
            400,
            false,
            "Transaction ID is required for verification."
          );
        }

        const verificationResult = await verifyPayment(requester, transactionId);
        return sendResponse(
          res,
          200,
          verificationResult.success,
          verificationResult.message,
          verificationResult.payment
        );
      }

      /**
       * Get payment history for a user
       * If userId is provided, get history for that user (admin/superadmin only)
       * Otherwise, get history for the requester
       */
      case "history": {
        const targetUserId = userId ? Number(userId) : requester.id;

        // Only allow admins/superadmins to view other users' payment history
        if (targetUserId !== requester.id &&
            requester.role !== "admin" &&
            requester.role !== "superadmin") {
          return sendResponse(
            res,
            403,
            false,
            "Unauthorized: You can only view your own payment history."
          );
        }

        const pageNum = Number(page) || 1;
        const limitNum = Number(limit) || 10;

        if (pageNum < 1 || limitNum < 1) {
          return sendResponse(res, 400, false, "Invalid pagination values.");
        }

        const paymentHistory = await getUserPaymentHistory(
          requester,
          targetUserId,
          pageNum,
          limitNum
        );

        return sendResponse(
          res,
          200,
          true,
          "Payment history retrieved successfully.",
          paymentHistory
        );
      }

      default:
        return sendResponse(res, 400, false, "Invalid mode.");
    }
  } catch (error: any) {
    console.error("Payment request error:", error);
    return sendResponse(
      res,
      error.statusCode || 500,
      false,
      error.message || "Internal Server Error"
    );
  }
};
