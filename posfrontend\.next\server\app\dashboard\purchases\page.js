(()=>{var e={};e.id=1330,e.ids=[1330],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},95192:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(70260),a=s(28203),l=s(25155),n=s.n(l),i=s(67292),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o=["",{children:["dashboard",{children:["purchases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99597)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\purchases\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\purchases\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/purchases/page",pathname:"/dashboard/purchases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},27907:(e,t,s)=>{Promise.resolve().then(s.bind(s,99597))},93563:(e,t,s)=>{Promise.resolve().then(s.bind(s,50066))},73021:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},10685:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"}}]},name:"shopping",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},24648:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},50066:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>er});var r=s(45512),a=s(58009),l=s(6987),n=s(3117),i=s(21419),c=s(10685),o=s(88752),d=s(60636),h=s(765),u=s(65906),x=s(78337);let p=(e=1,t=10)=>{let[s,r]=(0,a.useState)(e),[l,n]=(0,a.useState)(t),[i,c]=(0,a.useState)(""),o=(0,x.d)(i,500);(0,a.useEffect)(()=>{r(1)},[o]);let{data:d,error:h,isLoading:p,refetch:m}=(0,u.hZ)({page:s,limit:l,search:o}),g=d?.data?.purchases||[],f=d?.data?.total||0;return console.log("Purchases from API:",g),console.log("Total purchases:",f),{purchases:g,total:f,page:s,limit:l,isLoading:p,error:h,refetch:m,searchTerm:i,setSearchTerm:c,handlePageChange:e=>{r(e)},handleLimitChange:e=>{n(e),r(1)}}};var m=s(49792);let g=e=>{let[t,{isLoading:s}]=(0,u.PY)();return{deletePurchase:async s=>{try{let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete purchase");return(0,m.r)("success","Purchase deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete purchase error:",e),(0,m.r)("error",e.message||"Failed to delete purchase"),e}},isDeleting:s}},f=e=>{let[t,{isLoading:s}]=(0,u.oq)();return{bulkDeletePurchases:async s=>{try{console.log("Bulk deleting purchases with IDs:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete purchases");return(0,m.r)("success",`${s.length} purchases deleted successfully`),e&&e(),r.data}catch(e){throw console.error("Bulk delete purchases error:",e),(0,m.r)("error",e.message||"Failed to delete purchases"),e}},isDeleting:s}};var y=s(48752),j=s(77067),b=s(70001),v=s(25421),A=s(24648),w=s(81045),N=s(25834),P=s(99261),C=s(86977),S=s(73021),k=s(63844),E=s(73542),D=s(16589),z=s.n(D),I=s(92273);s(16664);let F=({purchases:e,loading:t,onView:s,onEdit:l,onDelete:i,onBulkDelete:o,isMobile:d=!1})=>{let h=(0,E.E)(),u=(0,I.d4)(e=>e.auth.user),x=u?.role,[p,m]=(0,a.useState)([]),[g,f]=(0,a.useState)(!1),D=t=>{let s=t.target.checked;f(s),s?m(e.filter(e=>_(e)).map(e=>e.id)):m([])},F=(e,t)=>{t?m(t=>[...t,e]):m(t=>t.filter(t=>t!==e))},T=e=>z()(e).format("MMM D, YYYY"),M=e=>new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS",minimumFractionDigits:2}).format(Number(e)),_=e=>"admin"===x;return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[p.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[p.length," ",1===p.length?"purchase":"purchases"," selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(v.A,{}),onClick:()=>{p.length>0&&o?(o(p),m([]),f(!1)):y.Ay.warning({message:"No purchases selected",description:"Please select at least one purchase to delete."})},className:"ml-2",children:"Delete Selected"})]}),d||h?(0,r.jsxs)(k.jB,{columns:"50px 200px 120px 120px 120px 150px",minWidth:"800px",children:[(0,r.jsx)(k.A0,{className:"text-center",children:(0,r.jsx)(j.A,{checked:g,onChange:D,disabled:0===e.filter(e=>_(e)).length})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Product"]})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Supplier"]})}),(0,r.jsx)(k.A0,{children:"Quantity"}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)(k.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,r.jsxs)(k.Hj,{selected:p.includes(e.id),children:[(0,r.jsx)(k.nA,{className:"text-center",children:_(e)&&(0,r.jsx)(j.A,{checked:p.includes(e.id),onChange:t=>F(e.id,t.target.checked)})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.product||"N/A"})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis text-gray-600",children:e.supplier||"N/A"})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("span",{className:"font-medium",children:e.quantity})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("span",{className:"text-sm",children:T(e.purchaseDate)})}),(0,r.jsx)(k.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(b.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(N.A,{}),onClick:()=>s(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),_(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(b.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>i(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(j.A,{checked:g,onChange:D,disabled:0===e.filter(e=>_(e)).length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Product"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Supplier"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Quantity"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Cost Price"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Total Cost"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:p.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:_(e)&&(0,r.jsx)(j.A,{checked:p.includes(e.id),onChange:t=>F(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.product||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.supplier||"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-500 text-white",children:e.quantity})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:M(e.costPrice)}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:M(e.totalCost)}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:T(e.purchaseDate)}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(b.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(N.A,{}),onClick:()=>s(e.id),type:"text",className:"view-button text-green-500",size:"middle"})}),_(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500",size:"middle"})}),(0,r.jsx)(b.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>i(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})]})})]},e.id))})]})})]})};var T=s(59022),M=s(60165);let _=({current:e,pageSize:t,total:s,onChange:a,isMobile:l=!1})=>{let n=Math.ceil(s/t);return(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,s)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(T.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(t,s)=>{let l=s+1;return(0,r.jsx)("button",{onClick:()=>a(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:l},l)}),(0,r.jsxs)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=n?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(M.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,r.jsx)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var O=s(7325),R=s(41257),q=s(88472),B=s(63087),H=s(2274),V=s(98776);let G=e=>{let[t,{isLoading:s}]=(0,u.o3)();return{createPurchase:async s=>{try{console.log("usePurchaseCreate - Starting purchase creation with data:",s);let r=await t(s).unwrap();if(console.log("usePurchaseCreate - API response:",r),!r.success)throw console.error("usePurchaseCreate - API returned error:",r.message),Error(r.message||"Failed to create purchase");return(0,m.r)("success","Purchase created successfully"),e&&e(),r.data}catch(e){throw console.error("Create purchase error:",e),(0,m.r)("error",e.message||"Failed to create purchase"),e}},isSubmitting:s}},U=e=>{let[t,{isLoading:s}]=(0,u.xc)();return{updatePurchase:async(s,r)=>{try{let a=await t({purchaseId:s,data:r}).unwrap();if(!a.success)throw Error(a.message||"Failed to update purchase");return(0,m.r)("success","Purchase updated successfully"),e&&e(),a.data}catch(e){throw console.error("Update purchase error:",e),(0,m.r)("error",e.message||"Failed to update purchase"),e}},isUpdating:s}};var L=s(11855);let $={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 394c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H400V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v236H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h228v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h164c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V394h164zM628 630H400V394h228v236z"}}]},name:"number",theme:"outlined"};var Y=s(78480),J=a.forwardRef(function(e,t){return a.createElement(Y.A,(0,L.A)({},e,{ref:t,icon:$}))});let{Option:Q}=O.A,W=({isOpen:e,onClose:t,onSuccess:s,purchase:l,currentUser:i})=>{let[o]=R.A.useForm(),d=!!l,[h,u]=(0,a.useState)("0"),{createPurchase:x,isSubmitting:p}=G(s),{updatePurchase:m,isUpdating:g}=U(s),{data:f,refetch:y}=(0,B.w$)({},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),j=(0,a.useMemo)(()=>f?.data?.suppliers||[],[f]),{data:b,refetch:v}=(0,H.r3)({page:1,limit:1e3,search:""},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),w=(0,a.useMemo)(()=>b?.data?.products||[],[b]),N=()=>{let e=((o.getFieldValue("quantity")||0)*(o.getFieldValue("costPrice")||0)).toFixed(2);u(e),o.setFieldsValue({totalCost:e})};(0,a.useEffect)(()=>{e&&(console.log("\uD83D\uDED2 Purchase panel opened - fetching fresh data"),v(),y(),o.resetFields(),l||u("0.00"))},[o,e,v,y,l]),(0,a.useEffect)(()=>{if(e&&l&&w.length>0&&j.length>0){let e=w.find(e=>e.name===l.product),t=j.find(e=>e.name===l.supplier);o.setFieldsValue({productId:e?.id,supplierId:t?.id,quantity:l.quantity,costPrice:l.costPrice,totalCost:l.totalCost}),u(l.totalCost)}},[e,l,w,j,o]);let P=async e=>{try{let t={...e,costPrice:e.costPrice?.toString()||"0",totalCost:e.totalCost?.toString()||"0"};console.log("Submitting purchase with formatted values:",t),d&&l?await m(l.id,t):await x(t)}catch(e){console.error("Failed to save purchase:",e)}},C=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:t,disabled:p||g,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(n.Ay,{type:"primary",loading:p||g,onClick:()=>o.submit(),children:d?"Update":"Save"})]});return(0,r.jsx)(V.A,{isOpen:e,onClose:t,title:d?"Edit Purchase":"Add Purchase",width:"500px",footer:C,children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Edit Purchase"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Add New Purchase"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:d?"Update purchase information":"Fill in the details to add a new purchase"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(R.A,{form:o,layout:"vertical",onFinish:P,className:"purchase-form",requiredMark:!0,onValuesChange:(e,t)=>{("quantity"in t||"costPrice"in t)&&N()},children:[(0,r.jsx)(R.A.Item,{name:"productId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"})," Product"]}),rules:[{required:!0,message:"Please select a product"}],tooltip:"Select the product you are purchasing",children:(0,r.jsx)(O.A,{placeholder:"Select a product",showSearch:!0,optionFilterProp:"children",children:w.map(e=>(0,r.jsx)(Q,{value:e.id,children:e.name},e.id))})}),(0,r.jsx)(R.A.Item,{name:"supplierId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"})," Supplier"]}),tooltip:"Select the supplier (optional)",children:(0,r.jsx)(O.A,{placeholder:"Select a supplier (optional)",allowClear:!0,showSearch:!0,optionFilterProp:"children",children:j.map(e=>(0,r.jsx)(Q,{value:e.id,children:e.name},e.id))})}),(0,r.jsx)(R.A.Item,{name:"quantity",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(J,{className:"mr-1"})," Quantity"]}),rules:[{required:!0,message:"Please enter quantity"},{type:"number",min:1,message:"Quantity must be at least 1"}],tooltip:"The quantity of products purchased",children:(0,r.jsx)(q.A,{min:1,style:{width:"100%"},placeholder:"Enter quantity"})}),(0,r.jsx)(R.A.Item,{name:"costPrice",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Cost Price Per Unit (GHS)"]}),rules:[{required:!0,message:"Please enter cost price"},{type:"number",min:.01,message:"Cost price must be greater than 0"}],tooltip:"The cost price per unit",children:(0,r.jsx)(q.A,{min:.01,step:.01,style:{width:"100%"},placeholder:"Enter cost price",formatter:e=>`GHS ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>parseFloat(e.replace(/GHS\s?|(,*)/g,""))})}),(0,r.jsx)(R.A.Item,{name:"totalCost",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Total Cost (GHS)"]}),tooltip:"The total cost (calculated automatically)",children:(0,r.jsx)(q.A,{disabled:!0,style:{width:"100%"},value:h,formatter:e=>`GHS ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/GHS\s?|(,*)/g,"")})})]})]})})};var K=s(12869);let X=({isOpen:e,onClose:t,purchaseId:s,onEdit:a})=>{let l=(0,I.d4)(e=>e.auth.user),d=l?.role,{data:h,isLoading:x,error:p}=(0,u.Pt)(s||0,{skip:!e||!s}),m=h?.data,g=!!m;console.log("Purchase detail - User ID:",l?.id),console.log("Purchase detail - Purchase:",m),console.log("Purchase detail - Can view purchase:",g);let f=e=>e?new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS",minimumFractionDigits:2}).format(Number(e)):"N/A",y="admin"===d&&!!m,j=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),a&&m&&y&&(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>a(m.id),children:"Edit"})]});return(0,r.jsx)(V.A,{isOpen:e,onClose:t,title:"Purchase Details",width:"500px",footer:j,children:x?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):m&&g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Purchase Details"]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"Complete purchase information and details"})]}),(0,r.jsxs)(K.A,{bordered:!0,column:1,className:"purchase-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Purchase ID"]}),children:m.id}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Product"]}),children:m.product}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(A.A,{})," Supplier"]}),children:m.supplier||"N/A"}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Quantity"]}),children:m.quantity}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Cost Price"]}),children:f(m.costPrice)}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Total Cost"]}),children:f(m.totalCost)}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(w.A,{})," Purchase Date"]}),children:(e=>{if(!e)return"N/A";try{return z()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}})(m.purchaseDate)}),(0,r.jsx)(K.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(A.A,{})," Purchased By"]}),children:m.purchasedBy||"N/A"})]})]}):m&&!g?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-red-500",children:'You don"t have permission to view this purchase.'})}):(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-gray-800",children:"No purchase data available"})})})};var Z=s(37764),ee=s(58733);s(86078);let et=({searchTerm:e,setSearchTerm:t,isMobile:s=!1})=>(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(Z.A,{placeholder:"Search by product or supplier...",prefix:(0,r.jsx)(ee.A,{className:"text-gray-500"}),value:e,onChange:e=>{let s=e.target.value;console.log("Purchase search input changed:",s),t(s)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:s?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var es=s(51531);let er=()=>{let{user:e}=(0,d.A)(),t=(0,h.a)(),[s,u]=(0,a.useState)(!1),[x,m]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[b,v]=(0,a.useState)(null),[A,w]=(0,a.useState)(null),[N,P]=(0,a.useState)(!1),{purchases:C,total:S,page:k,limit:E,isLoading:D,refetch:z,searchTerm:I,setSearchTerm:T,handlePageChange:M}=p();console.log("PurchasesPage - Current user:",e),console.log("PurchasesPage - Purchases:",C),console.log("PurchasesPage - Total:",S);let{deletePurchase:O,isDeleting:R}=g(()=>{P(!1),z()}),{bulkDeletePurchases:q,isDeleting:B}=f(()=>{V(!1),z()}),[H,V]=(0,a.useState)(!1),[G,U]=(0,a.useState)([]),L=e?.role==="admin",$=async()=>{A&&await O(A)},Y=async()=>{if(console.log("confirmBulkDelete called with purchases:",G),G.length>0)try{await q(G)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Purchase Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:L&&(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(c.A,{}),onClick:()=>{u(!0)},size:t?"small":"middle",children:t?"":"Add Purchase"}),children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,r.jsx)(et,{searchTerm:I,setSearchTerm:T,isMobile:t}),D?(0,r.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[C.length>0?(0,r.jsx)(F,{purchases:C,loading:!1,onView:e=>{w(e),j(!0)},onEdit:e=>{v(e),m(!0)},onDelete:e=>{w(e),P(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with purchaseIds:",e),U(e),V(!0)},isMobile:t}):(0,r.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No purchases found matching your search criteria."}),(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>T(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,r.jsxs)("p",{children:["No purchases found. ",L&&"Click 'Add Purchase' to create one."]})}),C.length>0&&(0,r.jsx)(_,{current:k,pageSize:E,total:S,onChange:M,isMobile:t})]})]})}),(0,r.jsx)(W,{isOpen:s,onClose:()=>u(!1),onSuccess:()=>{u(!1),z()},currentUser:e}),(0,r.jsx)(W,{isOpen:x,onClose:()=>m(!1),onSuccess:()=>{m(!1),z()},purchase:b,currentUser:e}),(0,r.jsx)(X,{isOpen:y,onClose:()=>{j(!1),w(null)},purchaseId:A,onEdit:e=>{let t=C.find(t=>t.id===e)||null;t&&(v(t),j(!1),m(!0))}}),(0,r.jsx)(es.A,{isOpen:N,onClose:()=>{P(!1),w(null)},onConfirm:$,title:"Delete Purchase",message:"Are you sure you want to delete this purchase? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:R,type:"danger"}),(0,r.jsx)(es.A,{isOpen:H,onClose:()=>{V(!1),U([])},onConfirm:Y,title:"Delete Multiple Purchases",message:`Are you sure you want to delete ${G.length} purchases? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:B,type:"danger"})]})}},51531:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(45512);s(58009);var a=s(88206),l=s(3117),n=s(75238);let i=({isOpen:e,onClose:t,onConfirm:s,title:i,message:c,confirmText:o="Confirm",cancelText:d="Cancel",isLoading:h=!1,type:u="danger"})=>(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{style:{color:"danger"===u?"#ff4d4f":"warning"===u?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:i})]}),open:e,onCancel:t,footer:[(0,r.jsx)(l.Ay,{onClick:t,disabled:h,children:d},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===u?"primary":"default",danger:"danger"===u,onClick:s,loading:h,children:o},"confirm")],maskClosable:!h,closable:!h,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:c})})},63844:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,Hj:()=>c,jB:()=>l,nA:()=>i});var r=s(45512);s(58009);var a=s(44195);let l=({children:e,columns:t,className:s,minWidth:l="800px"})=>(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",s),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0","block"),style:{},children:e})}),n=({children:e,className:t,sticky:s})=>(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",s&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[s],t),children:e}),i=({children:e,className:t,sticky:s})=>(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",s&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[s],t),children:e}),c=({children:e,className:t,selected:s=!1,onClick:l})=>(0,r.jsx)("div",{className:(0,a.cn)("contents",s&&"bg-blue-50",l&&"cursor-pointer hover:bg-gray-50",t),onClick:l,children:e})},98776:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(45512),a=s(58009),l=s(3117),n=s(97071);let i=({isOpen:e,onClose:t,title:s,children:i,width:c="400px",footer:o,fullWidth:d=!1})=>{let[h,u]=(0,a.useState)(!1),[x,p]=(0,a.useState)(!1),[m,g]=(0,a.useState)(1024);if((0,a.useEffect)(()=>{let e=()=>{g(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",s),e)p(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{u(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{u(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{p(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,s]),!x)return null;let f="Point of Sale"===s||d||"100vw"===c;return(0,r.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${f?"sales-panel-container":""}`,children:[(0,r.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${h?"opacity-50":"opacity-0"}`,onClick:t}),(0,r.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${h?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===s||d||"100vw"===c||m<640?"100vw":m<1024?"500px":"string"==typeof c&&c.includes("px")&&parseInt(c)>600?"600px":c},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:s}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(n.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:i}),o&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:o})]})]})}},60636:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(92273),a=s(25510),l=s(97245),n=s(42211);let i=()=>{let e=(0,r.wA)(),{user:t,accessToken:s}=(0,r.d4)(e=>e.auth),i=(0,a._)(),{refetch:c}=(0,l.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let o=!!t&&!!s,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let r=await c();console.log("useAuth - Refetch result:",r);let a=r.data;if(a?.success&&a?.data){console.log("useAuth - API response data:",a.data);let r=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=a.data.phone||t.phone||"",i=a.data.createdAt||t.createdAt||"",c=a.data.lastPaymentDate||t.lastPaymentDate||void 0,o=a.data.nextPaymentDue||t.nextPaymentDue||null,d=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:c,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:o,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let h={...a.data,phone:l,createdAt:i,lastPaymentDate:c,nextPaymentDue:o,createdBy:d,paymentStatus:r};console.log("useAuth - Updating Redux store with:",h),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:h,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",a?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:o,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:d}}},78337:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(58009);function a(e,t){let[s,a]=(0,r.useState)(e);return s}},73542:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(58009);let a=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},99597:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\purchases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\purchases\\page.tsx","default")},16664:()=>{},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,1785,8472,5482,106,4286],()=>s(95192));module.exports=r})();