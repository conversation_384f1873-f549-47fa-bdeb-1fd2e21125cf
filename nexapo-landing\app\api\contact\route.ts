import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

// Email configuration
const createTransporter = () => {
  const gmailUser = process.env.GMAIL_USER
  const gmailPassword = process.env.GMAIL_APP_PASSWORD

  if (!gmailUser || !gmailPassword) {
    throw new Error('Email configuration is missing. Please set GMAIL_USER and GMAIL_APP_PASSWORD environment variables.')
  }

  return nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: gmailUser,
      pass: gmailPassword,
    },
  })
}

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate Ghana phone number format
const isValidGhanaPhone = (phone: string): boolean => {
  if (!phone) return true // Phone is optional
  const phoneRegex = /^(\+233|0)[2-9][0-9]{8}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      firstName,
      lastName,
      email,
      phone,
      businessName,
      subject,
      message,
      newsletter
    } = body

    // Validation
    const errors: Record<string, string> = {}

    if (!firstName?.trim()) {
      errors.firstName = 'First name is required'
    }

    if (!lastName?.trim()) {
      errors.lastName = 'Last name is required'
    }

    if (!email?.trim()) {
      errors.email = 'Email is required'
    } else if (!isValidEmail(email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (phone && !isValidGhanaPhone(phone)) {
      errors.phone = 'Please enter a valid Ghana phone number'
    }

    if (!subject?.trim()) {
      errors.subject = 'Subject is required'
    }

    if (!message?.trim()) {
      errors.message = 'Message is required'
    } else if (message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters long'
    }

    if (Object.keys(errors).length > 0) {
      return NextResponse.json(
        { success: false, errors },
        { status: 400 }
      )
    }

    // Create email transporter
    let transporter: any
    try {
      transporter = createTransporter()
    } catch (configError) {
      console.error('Email configuration error:', configError)
      return NextResponse.json(
        {
          success: false,
          message: 'Email service is currently unavailable. Please try again later or contact us <NAME_EMAIL>.'
        },
        { status: 503 }
      )
    }

    // Email to admin (NEXAPO team)
    const adminEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">🚀 New NEXAPO Contact Form Submission</h1>
        </div>

        <div style="padding: 30px; background: #f8f9fa;">
          <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">Contact Details</h2>

            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Name:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee;">${firstName} ${lastName}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Email:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee;"><a href="mailto:${email}" style="color: #667eea;">${email}</a></td>
              </tr>
              ${phone ? `
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Phone:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee;"><a href="tel:${phone}" style="color: #667eea;">${phone}</a></td>
              </tr>
              ` : ''}
              ${businessName ? `
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Business:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee;">${businessName}</td>
              </tr>
              ` : ''}
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Subject:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee;"><span style="background: #e3f2fd; padding: 4px 8px; border-radius: 4px; color: #1976d2;">${subject}</span></td>
              </tr>
              <tr>
                <td style="padding: 10px 0; font-weight: bold; color: #555;">Newsletter:</td>
                <td style="padding: 10px 0;">${newsletter ? '✅ Yes' : '❌ No'}</td>
              </tr>
            </table>

            <h3 style="color: #333; margin-top: 25px;">Message</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #667eea;">
              ${message.replace(/\n/g, '<br>')}
            </div>

            <div style="margin-top: 25px; padding: 15px; background: #e8f5e8; border-radius: 5px; border-left: 4px solid #4caf50;">
              <strong>📧 Quick Actions:</strong><br>
              <a href="mailto:${email}?subject=Re: ${subject}" style="color: #4caf50; text-decoration: none;">Reply to ${firstName}</a> |
              <a href="tel:${phone}" style="color: #4caf50; text-decoration: none;">Call ${phone}</a>
            </div>
          </div>
        </div>

        <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
          <p>This email was sent from the NEXAPO contact form at ${new Date().toLocaleString()}</p>
        </div>
      </div>
    `

    // Email to customer (auto-reply)
    const customerEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">🇬🇭 Thank You for Contacting NEXAPO!</h1>
        </div>

        <div style="padding: 30px; background: #f8f9fa;">
          <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">Hi ${firstName}! 👋</h2>

            <p style="color: #555; line-height: 1.6;">
              Thank you for your interest in <strong>NEXAPO</strong> - Ghana's revolutionary POS system!
              We've received your message and our team will respond within <strong>24 hours</strong>.
            </p>

            <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #1976d2; margin-top: 0;">📋 Your Submission Summary</h3>
              <p style="margin: 5px 0;"><strong>Subject:</strong> ${subject}</p>
              <p style="margin: 5px 0;"><strong>Message:</strong> ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}</p>
            </div>

            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #4caf50; margin-top: 0;">🚀 While You Wait...</h3>
              <ul style="color: #555; line-height: 1.6;">
                <li>📱 Follow us for updates and tips</li>
                <li>📞 For urgent matters, call us at <strong>+233 24 814 1505</strong></li>
                <li>💬 WhatsApp us at <strong>+233 24 814 1505</strong></li>
                <li>🌐 Visit our website: <a href="https://nexapo.com" style="color: #4caf50;">www.nexapo.com</a></li>
              </ul>
            </div>

            <div style="background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #f57c00; margin-top: 0;">💰 Special Offer</h3>
              <p style="color: #555; margin: 0;">
                As a potential early adopter, you'll get <strong>priority support</strong> and
                <strong>30-day money-back guarantee</strong> when you join NEXAPO!
              </p>
            </div>

            <p style="color: #555; line-height: 1.6;">
              Best regards,<br>
              <strong>The NEXAPO Team</strong><br>
              🇬🇭 Proudly Ghanaian - Built for Ghanaian Businesses
            </p>
          </div>
        </div>

        <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
          <p>This is an automated response. Please do not reply to this email.</p>
          <p>For support, contact us at <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a></p>
        </div>
      </div>
    `

    // Send email to admin
    await transporter.sendMail({
      from: `"NEXAPO Contact Form" <${process.env.GMAIL_USER}>`,
      to: process.env.ADMIN_EMAIL || process.env.GMAIL_USER,
      subject: `🚀 New NEXAPO Contact: ${subject} - ${firstName} ${lastName}`,
      html: adminEmailHtml,
      replyTo: email,
    })

    // Send auto-reply to customer
    await transporter.sendMail({
      from: `"NEXAPO Team" <${process.env.GMAIL_USER}>`,
      to: email,
      subject: `🇬🇭 Thank you for contacting NEXAPO - We'll respond within 24 hours!`,
      html: customerEmailHtml,
    })

    return NextResponse.json({
      success: true,
      message: 'Thank you for your message! We\'ll get back to you within 24 hours.'
    })

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Sorry, there was an error sending your message. Please try again or contact us directly.'
      },
      { status: 500 }
    )
  }
}

