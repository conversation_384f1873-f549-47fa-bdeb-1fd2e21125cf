exports.id=8042,exports.ids=[8042],exports.modules={27241:(e,t,a)=>{Promise.resolve().then(a.bind(a,87053))},69097:(e,t,a)=>{Promise.resolve().then(a.bind(a,45546))},45546:(e,t,a)=>{"use strict";a.d(t,{default:()=>f});var n=a(45512),s=a(58009),r=a(92273),i=a(79334),u=a(24715);let o=({children:e})=>{let{user:t,accessToken:a}=(0,r.d4)(e=>e.auth),o=(0,i.useRouter)(),l=(0,i.usePathname)(),[d,c]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{c(!0),console.log("AuthGuard mounted on client",{isAuthenticated:!!t&&!!a,userRole:t?.role,pathname:l})},[a,l,t]),(0,s.useEffect)(()=>{!d||t&&a||m||(console.log("AuthGuard - Redirecting to login",{pathname:l}),p(!0),sessionStorage.setItem("redirectUrl",l),setTimeout(()=>{o.push("/")},100))},[t,a,o,l,d,m]),d&&t&&a)?(0,n.jsx)(n.Fragment,{children:e}):(0,n.jsx)(u.A,{fullScreen:!0})};var l=a(49198);let d=({children:e,allowedRoles:t,fallbackPath:a="/dashboard"})=>{let{user:o}=(0,r.d4)(e=>e.auth),d=(0,i.useRouter)();return((0,s.useEffect)(()=>{o&&!t.includes(o.role)&&d.push(a)},[o,t,d,a]),o)?t.includes(o.role)?(0,n.jsx)(n.Fragment,{children:e}):(0,n.jsx)("div",{className:"flex h-screen w-full items-center justify-center",children:(0,n.jsx)(l.A,{message:"Access Denied",description:`You don't have permission to access this page. This area requires ${t.join(" or ")} role.`,type:"error",showIcon:!0})}):(0,n.jsx)(u.A,{fullScreen:!0})};var c=a(25510),m=a(16589),p=a.n(m);let y=({children:e})=>{let{user:t}=(0,r.d4)(e=>e.auth),a=(0,i.useRouter)(),o=(0,i.usePathname)(),{needsPayment:l,status:d,isActive:m,daysRemaining:y}=(0,c._)();if((0,s.useRef)(t?.paymentStatus),(0,r.wA)(),console.log("\uD83D\uDEE1️ PaymentGuard COMPONENT RENDERED - This confirms PaymentGuard is being called"),console.log("\uD83D\uDEE1️ PaymentGuard - Payment Status Check:",{userRole:t?.role,userPaymentStatus:t?.paymentStatus,needsPayment:l,status:d,isActive:m,daysRemaining:y,pathname:o,lastPaymentDate:t?.lastPaymentDate,nextPaymentDue:t?.nextPaymentDue,createdAt:t?.createdAt}),t?.createdAt&&t?.paymentStatus==="paid"&&!t?.lastPaymentDate){let e=p()().diff(p()(t.createdAt),"day");console.log("\uD83C\uDF81 PaymentGuard - FREE TRIAL USER DETECTED:",{email:t.email,daysSinceCreation:e,isInFreeTrial:e<=90,trialDaysRemaining:Math.max(0,90-e),paymentStatus:t.paymentStatus,nextPaymentDue:t.nextPaymentDue})}if(t?.paymentStatus==="pending"&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER DETECTED! Should redirect to payment page"),t?.paymentStatus,(0,s.useEffect)(()=>{t&&"superadmin"!==t.role&&"paid"!==t.paymentStatus&&a.replace("/payment")},[t,a]),(0,s.useEffect)(()=>{if(!t||"superadmin"===t.role)return;let e=o.includes("/profile")||o.includes("/dashboard/profile"),n=!0===window.__PROFILE_UPDATE_IN_PROGRESS,s=window.__LAST_PROFILE_UPDATE_PATH||"";if(e||n){console.log("PaymentGuard - Skipping payment check:",{isProfilePage:e,isProfileUpdateInProgress:n,pathname:o,lastProfileUpdatePath:s});return}let r="/payment"===o;console.log("PaymentGuard - Payment check status:",{pathname:o,isProfilePage:e,isExemptPath:r,needsPayment:l});let i=l&&!r&&"paid"!==t.paymentStatus||!r&&"paid"!==t.paymentStatus;console.log("\uD83D\uDD0D PaymentGuard - Redirect decision breakdown:",{needsPayment:l,isExemptPath:r,userPaymentStatus:t.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==t.paymentStatus,shouldRedirectToPayment:i,pathname:o}),"pending"===t.paymentStatus&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER REDIRECT CHECK:",{needsPayment:l,isExemptPath:r,userPaymentStatus:t.paymentStatus,shouldRedirectToPayment:i,"Will redirect?":i?"YES":"NO"}),i?(console.log("\uD83D\uDEA8 PaymentGuard - Redirecting to payment page from:",o),a.push("/payment")):"paid"===t.paymentStatus&&l?console.log("⚠️ PaymentGuard - User has paid status but needsPayment is true. This might indicate a cache issue."):"pending"!==t.paymentStatus||i||console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER NOT REDIRECTED! This is the bug!")},[t,l,a,o]),(0,s.useEffect)(()=>{t&&"superadmin"!==t.role&&t.paymentStatus},[t,a]),!t)return(0,n.jsx)(u.A,{tip:"Checking payment status...",fullScreen:!0});if("superadmin"===t.role)return(0,n.jsx)(n.Fragment,{children:e});let f=o.includes("/profile")||o.includes("/dashboard/profile"),g=!0===window.__PROFILE_UPDATE_IN_PROGRESS,P=window.__LAST_PROFILE_UPDATE_PATH||"";if(f||g)return console.log("PaymentGuard (render) - Skipping payment check:",{isProfilePage:f,isProfileUpdateInProgress:g,pathname:o,lastProfileUpdatePath:P}),(0,n.jsx)(n.Fragment,{children:e});let S="/payment"===o,h=l&&!S&&"paid"!==t.paymentStatus||!S&&"paid"!==t.paymentStatus;return(console.log("\uD83C\uDFA8 PaymentGuard (render) - Final render decision:",{needsPayment:l,isExemptPath:S,userPaymentStatus:t.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==t.paymentStatus,shouldShowPaymentLoading:h,pathname:o}),h)?(0,n.jsx)(u.A,{tip:"Checking payment status...",fullScreen:!0}):(0,n.jsx)(n.Fragment,{children:e})},f=({children:e,allowedRoles:t=["superadmin","admin","cashier"],checkPayment:a=!0,fallbackPath:s="/dashboard"})=>(0,n.jsx)(o,{children:a?(0,n.jsx)(y,{children:(0,n.jsx)(d,{allowedRoles:t,fallbackPath:s,children:e})}):(0,n.jsx)(d,{allowedRoles:t,fallbackPath:s,children:e})})},25510:(e,t,a)=>{"use strict";a.d(t,{_:()=>u});var n=a(58009),s=a(92273),r=a(16589),i=a.n(r);let u=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,a]=(0,n.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,n.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,n=!1,s=!0,r="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){n=!0,s=!1,r="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let r=i()(e.nextPaymentDue),u=i()();if(t=r.diff(u,"day"),a){let a=i()().diff(i()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:n,needsPayment:s})}}}else"pending"===e.paymentStatus?(n=!1,s=!0,r="pending"):"overdue"===e.paymentStatus?(n=!1,s=!0,r="overdue"):(n=!1,s=!0,r="inactive");a({isActive:n,daysRemaining:t,status:r,needsPayment:s})},[e]),t}},79188:(e,t,a)=>{"use strict";a.d(t,{hB:()=>r,jH:()=>i});var n=a(42211),s=a(97245);let r=async({dispatch:e,accessToken:t,currentUser:a,maxRetries:r=3,retryDelay:i=2e3})=>{console.log("\uD83D\uDD04 Starting user data refresh after payment..."),e(s.i$.util.invalidateTags(["User"])),e(s.i$.util.resetApiState()),console.log("\uD83E\uDDF9 Cleared all RTK Query cache");for(let u=1;u<=r;u++)try{console.log(`📡 Attempt ${u}/${r} - Fetching fresh user data...`),u>1&&await new Promise(e=>setTimeout(e,i));let o=await e(s.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in o&&o.data?.success&&o.data.data){let s=o.data.data;if(console.log("✅ Successfully fetched updated user data:",{id:s.id,paymentStatus:s.paymentStatus,lastPaymentDate:s.lastPaymentDate,nextPaymentDue:s.nextPaymentDue,attempt:u}),e((0,n.gV)({user:s,accessToken:t})),"paid"===s.paymentStatus)return console.log('\uD83C\uDF89 Payment status successfully updated to "paid"'),{success:!0,user:s};if(console.log(`⚠️ Payment status is "${s.paymentStatus}", not "paid". Retrying...`),u===r){console.log("⚠️ Max retries reached, using fallback update");let s={...a,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return e((0,n.gV)({user:s,accessToken:t})),{success:!0,user:s}}continue}if(console.log(`⚠️ Attempt ${u}: Backend response not successful or no data`),u===r)throw Error("Failed to fetch user data from backend");continue}catch(s){if(console.error(`❌ Attempt ${u} failed:`,s),u===r){if(console.log("\uD83D\uDCA5 All attempts failed, using fallback update"),!a)return{success:!1,error:"Failed to refresh user data and no current user for fallback"};{let s={...a,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return e((0,n.gV)({user:s,accessToken:t})),console.log("✅ Fallback: Updated user payment status in Redux"),{success:!0,user:s}}}}return{success:!1,error:"Unexpected error in user data refresh"}},i=e=>{if(!e)return console.log("❌ No user data to verify"),!1;let t="paid"===e.paymentStatus,a=e.lastPaymentDate&&new Date(e.lastPaymentDate).getTime()>Date.now()-3e5;return console.log("\uD83D\uDD0D Payment status verification:",{paymentStatus:e.paymentStatus,isPaymentStatusPaid:t,lastPaymentDate:e.lastPaymentDate,hasRecentPaymentDate:a,nextPaymentDue:e.nextPaymentDue}),t}},49792:(e,t,a)=>{"use strict";a.d(t,{r:()=>s});var n=a(22403);let s=(e,t)=>{"success"===e?n.oR.success(t):"error"===e?n.oR.error(t):"warning"===e&&(0,n.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},33439:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var n=a(62740),s=a(87053);function r({children:e}){return(0,n.jsx)(s.default,{checkPayment:!1,children:e})}},87053:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});let n=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Auth\\\\ProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\ProtectedRoute.tsx","default")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var n=a(88077);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};