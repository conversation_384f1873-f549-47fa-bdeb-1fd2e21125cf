"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5211],{87181:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(85407),o=n(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var c=n(84021);let r=o.forwardRef(function(e,t){return o.createElement(c.A,(0,a.A)({},e,{ref:t,icon:l}))})},17084:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(85407),o=n(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zm-200 0H360v-72h304v72z"}}]},name:"delete",theme:"filled"};var c=n(84021);let r=o.forwardRef(function(e,t){return o.createElement(c.A,(0,a.A)({},e,{ref:t,icon:l}))})},27656:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(85407),o=n(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var c=n(84021);let r=o.forwardRef(function(e,t){return o.createElement(c.A,(0,a.A)({},e,{ref:t,icon:l}))})},33621:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(85407),o=n(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var c=n(84021);let r=o.forwardRef(function(e,t){return o.createElement(c.A,(0,a.A)({},e,{ref:t,icon:l}))})},92895:(e,t,n)=>{n.d(t,{A:()=>C});var a=n(12115),o=n(4617),l=n.n(o),c=n(37801),r=n(15231),i=n(71054),s=n(43144),d=n(31049),u=n(30033),b=n(7926),p=n(30149);let m=a.createContext(null);var g=n(24631),f=n(83427),v=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let h=a.forwardRef((e,t)=>{var n;let{prefixCls:o,className:h,rootClassName:y,children:O,indeterminate:j=!1,style:x,onMouseEnter:C,onMouseLeave:k,skipGroup:w=!1,disabled:S}=e,E=v(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:A,direction:z,checkbox:N}=a.useContext(d.QO),P=a.useContext(m),{isFormItemInput:B}=a.useContext(p.$W),I=a.useContext(u.A),H=null!==(n=(null==P?void 0:P.disabled)||S)&&void 0!==n?n:I,M=a.useRef(E.value),R=a.useRef(null),L=(0,r.K4)(t,R);a.useEffect(()=>{null==P||P.registerValue(E.value)},[]),a.useEffect(()=>{if(!w)return E.value!==M.current&&(null==P||P.cancelValue(M.current),null==P||P.registerValue(E.value),M.current=E.value),()=>null==P?void 0:P.cancelValue(E.value)},[E.value]),a.useEffect(()=>{var e;(null===(e=R.current)||void 0===e?void 0:e.input)&&(R.current.input.indeterminate=j)},[j]);let T=A("checkbox",o),D=(0,b.A)(T),[W,F,V]=(0,g.Ay)(T,D),X=Object.assign({},E);P&&!w&&(X.onChange=function(){E.onChange&&E.onChange.apply(E,arguments),P.toggleOption&&P.toggleOption({label:O,value:E.value})},X.name=P.name,X.checked=P.value.includes(E.value));let G=l()("".concat(T,"-wrapper"),{["".concat(T,"-rtl")]:"rtl"===z,["".concat(T,"-wrapper-checked")]:X.checked,["".concat(T,"-wrapper-disabled")]:H,["".concat(T,"-wrapper-in-form-item")]:B},null==N?void 0:N.className,h,y,V,D,F),_=l()({["".concat(T,"-indeterminate")]:j},s.D,F),[q,K]=(0,f.A)(X.onClick);return W(a.createElement(i.A,{component:"Checkbox",disabled:H},a.createElement("label",{className:G,style:Object.assign(Object.assign({},null==N?void 0:N.style),x),onMouseEnter:C,onMouseLeave:k,onClick:q},a.createElement(c.A,Object.assign({},X,{onClick:K,prefixCls:T,className:_,disabled:H,ref:L})),void 0!==O&&a.createElement("span",{className:"".concat(T,"-label")},O))))});var y=n(39014),O=n(70527),j=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let x=a.forwardRef((e,t)=>{let{defaultValue:n,children:o,options:c=[],prefixCls:r,className:i,rootClassName:s,style:u,onChange:p}=e,f=j(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:v,direction:x}=a.useContext(d.QO),[C,k]=a.useState(f.value||n||[]),[w,S]=a.useState([]);a.useEffect(()=>{"value"in f&&k(f.value||[])},[f.value]);let E=a.useMemo(()=>c.map(e=>"string"==typeof e||"number"==typeof e?{label:e,value:e}:e),[c]),A=v("checkbox",r),z="".concat(A,"-group"),N=(0,b.A)(A),[P,B,I]=(0,g.Ay)(A,N),H=(0,O.A)(f,["value","disabled"]),M=c.length?E.map(e=>a.createElement(h,{prefixCls:A,key:e.value.toString(),disabled:"disabled"in e?e.disabled:f.disabled,value:e.value,checked:C.includes(e.value),onChange:e.onChange,className:"".concat(z,"-item"),style:e.style,title:e.title,id:e.id,required:e.required},e.label)):o,R={toggleOption:e=>{let t=C.indexOf(e.value),n=(0,y.A)(C);-1===t?n.push(e.value):n.splice(t,1),"value"in f||k(n),null==p||p(n.filter(e=>w.includes(e)).sort((e,t)=>E.findIndex(t=>t.value===e)-E.findIndex(e=>e.value===t)))},value:C,disabled:f.disabled,name:f.name,registerValue:e=>{S(t=>[].concat((0,y.A)(t),[e]))},cancelValue:e=>{S(t=>t.filter(t=>t!==e))}},L=l()(z,{["".concat(z,"-rtl")]:"rtl"===x},i,s,I,N,B);return P(a.createElement("div",Object.assign({className:L,style:u},H,{ref:t}),a.createElement(m.Provider,{value:R},M)))});h.Group=x,h.__ANT_CHECKBOX=!0;let C=h},24631:(e,t,n)=>{n.d(t,{Ay:()=>s,gd:()=>i});var a=n(67548),o=n(70695),l=n(56204),c=n(1086);let r=e=>{let{checkboxCls:t}=e,n="".concat(t,"-wrapper");return[{["".concat(t,"-group")]:Object.assign(Object.assign({},(0,o.dF)(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,["> ".concat(e.antCls,"-row")]:{flex:1}}),[n]:Object.assign(Object.assign({},(0,o.dF)(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},["& + ".concat(n)]:{marginInlineStart:0},["&".concat(n,"-in-form-item")]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},(0,o.dF)(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",["".concat(t,"-input")]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,["&:focus-visible + ".concat(t,"-inner")]:Object.assign({},(0,o.jk)(e))},["".concat(t,"-inner")]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:"".concat((0,a.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:"all ".concat(e.motionDurationSlow),"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:"".concat((0,a.zA)(e.lineWidthBold)," solid ").concat(e.colorWhite),borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:"all ".concat(e.motionDurationFast," ").concat(e.motionEaseInBack,", opacity ").concat(e.motionDurationFast)}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{["\n        ".concat(n,":not(").concat(n,"-disabled),\n        ").concat(t,":not(").concat(t,"-disabled)\n      ")]:{["&:hover ".concat(t,"-inner")]:{borderColor:e.colorPrimary}},["".concat(n,":not(").concat(n,"-disabled)")]:{["&:hover ".concat(t,"-checked:not(").concat(t,"-disabled) ").concat(t,"-inner")]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},["&:hover ".concat(t,"-checked:not(").concat(t,"-disabled):after")]:{borderColor:e.colorPrimaryHover}}},{["".concat(t,"-checked")]:{["".concat(t,"-inner")]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseOutBack," ").concat(e.motionDurationFast)}}},["\n        ".concat(n,"-checked:not(").concat(n,"-disabled),\n        ").concat(t,"-checked:not(").concat(t,"-disabled)\n      ")]:{["&:hover ".concat(t,"-inner")]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{["".concat(t,"-inner")]:{backgroundColor:"".concat(e.colorBgContainer," !important"),borderColor:"".concat(e.colorBorder," !important"),"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},["&:hover ".concat(t,"-inner")]:{backgroundColor:"".concat(e.colorBgContainer," !important"),borderColor:"".concat(e.colorPrimary," !important")}}}},{["".concat(n,"-disabled")]:{cursor:"not-allowed"},["".concat(t,"-disabled")]:{["&, ".concat(t,"-input")]:{cursor:"not-allowed",pointerEvents:"none"},["".concat(t,"-inner")]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},["&".concat(t,"-indeterminate ").concat(t,"-inner::after")]:{background:e.colorTextDisabled}}}]};function i(e,t){return[r((0,l.oX)(t,{checkboxCls:".".concat(e),checkboxSize:t.controlInteractiveSize}))]}let s=(0,c.OF)("Checkbox",(e,t)=>{let{prefixCls:n}=t;return[i(n,e)]})},83427:(e,t,n)=>{n.d(t,{A:()=>l});var a=n(12115),o=n(13379);function l(e){let t=a.useRef(null),n=()=>{o.A.cancel(t.current),t.current=null};return[()=>{n(),t.current=(0,o.A)(()=>{t.current=null})},a=>{t.current&&(a.stopPropagation(),n()),null==e||e(a)}]}},67649:(e,t,n)=>{n.d(t,{A:()=>z});var a=n(12115),o=n(4617),l=n.n(o),c=n(45049),r=n(31049),i=n(27651),s=n(7703);let d={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},u=a.createContext({});var b=n(63588),p=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let m=e=>(0,b.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var g=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let f=(e,t)=>{let[n,o]=(0,a.useMemo)(()=>(function(e,t){let n=[],a=[],o=!1,l=0;return e.filter(e=>e).forEach(e=>{let{filled:c}=e,r=g(e,["filled"]);if(c){a.push(r),n.push(a),a=[],l=0;return}let i=t-l;(l+=e.span||1)>=t?(l>t?(o=!0,a.push(Object.assign(Object.assign({},r),{span:i}))):a.push(r),n.push(a),a=[],l=0):a.push(r)}),a.length>0&&n.push(a),[n=n.map(e=>{let n=e.reduce((e,t)=>e+(t.span||1),0);if(n<t){let a=e[e.length-1];a.span=t-(n-(a.span||1))}return e}),o]})(t,e),[t,e]);return n},v=e=>{let{itemPrefixCls:t,component:n,span:o,className:c,style:r,labelStyle:i,contentStyle:s,bordered:d,label:b,content:p,colon:m,type:g,styles:f}=e,{classNames:v}=a.useContext(u);return d?a.createElement(n,{className:l()({["".concat(t,"-item-label")]:"label"===g,["".concat(t,"-item-content")]:"content"===g,["".concat(null==v?void 0:v.label)]:"label"===g,["".concat(null==v?void 0:v.content)]:"content"===g},c),style:r,colSpan:o},null!=b&&a.createElement("span",{style:Object.assign(Object.assign({},i),null==f?void 0:f.label)},b),null!=p&&a.createElement("span",{style:Object.assign(Object.assign({},i),null==f?void 0:f.content)},p)):a.createElement(n,{className:l()("".concat(t,"-item"),c),style:r,colSpan:o},a.createElement("div",{className:"".concat(t,"-item-container")},(b||0===b)&&a.createElement("span",{className:l()("".concat(t,"-item-label"),null==v?void 0:v.label,{["".concat(t,"-item-no-colon")]:!m}),style:Object.assign(Object.assign({},i),null==f?void 0:f.label)},b),(p||0===p)&&a.createElement("span",{className:l()("".concat(t,"-item-content"),null==v?void 0:v.content),style:Object.assign(Object.assign({},s),null==f?void 0:f.content)},p)))};function h(e,t,n){let{colon:o,prefixCls:l,bordered:c}=t,{component:r,type:i,showLabel:s,showContent:d,labelStyle:u,contentStyle:b,styles:p}=n;return e.map((e,t)=>{let{label:n,children:m,prefixCls:g=l,className:f,style:h,labelStyle:y,contentStyle:O,span:j=1,key:x,styles:C}=e;return"string"==typeof r?a.createElement(v,{key:"".concat(i,"-").concat(x||t),className:f,style:h,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},u),null==p?void 0:p.label),y),null==C?void 0:C.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},b),null==p?void 0:p.content),O),null==C?void 0:C.content)},span:j,colon:o,component:r,itemPrefixCls:g,bordered:c,label:s?n:null,content:d?m:null,type:i}):[a.createElement(v,{key:"label-".concat(x||t),className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},u),null==p?void 0:p.label),h),y),null==C?void 0:C.label),span:1,colon:o,component:r[0],itemPrefixCls:g,bordered:c,label:n,type:"label"}),a.createElement(v,{key:"content-".concat(x||t),className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},b),null==p?void 0:p.content),h),O),null==C?void 0:C.content),span:2*j-1,component:r[1],itemPrefixCls:g,bordered:c,content:m,type:"content"})]})}let y=e=>{let t=a.useContext(u),{prefixCls:n,vertical:o,row:l,index:c,bordered:r}=e;return o?a.createElement(a.Fragment,null,a.createElement("tr",{key:"label-".concat(c),className:"".concat(n,"-row")},h(l,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),a.createElement("tr",{key:"content-".concat(c),className:"".concat(n,"-row")},h(l,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):a.createElement("tr",{key:c,className:"".concat(n,"-row")},h(l,e,Object.assign({component:r?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var O=n(67548),j=n(70695),x=n(1086),C=n(56204);let k=e=>{let{componentCls:t,labelBg:n}=e;return{["&".concat(t,"-bordered")]:{["> ".concat(t,"-view")]:{border:"".concat((0,O.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"> table":{tableLayout:"auto"},["".concat(t,"-row")]:{borderBottom:"".concat((0,O.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderBottom:"none"},["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,O.zA)(e.padding)," ").concat((0,O.zA)(e.paddingLG)),borderInlineEnd:"".concat((0,O.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderInlineEnd:"none"}},["> ".concat(t,"-item-label")]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},["&".concat(t,"-middle")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,O.zA)(e.paddingSM)," ").concat((0,O.zA)(e.paddingLG))}}},["&".concat(t,"-small")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,O.zA)(e.paddingXS)," ").concat((0,O.zA)(e.padding))}}}}}},w=e=>{let{componentCls:t,extraColor:n,itemPaddingBottom:a,itemPaddingEnd:o,colonMarginRight:l,colonMarginLeft:c,titleMarginBottom:r}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,j.dF)(e)),k(e)),{"&-rtl":{direction:"rtl"},["".concat(t,"-header")]:{display:"flex",alignItems:"center",marginBottom:r},["".concat(t,"-title")]:Object.assign(Object.assign({},j.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),["".concat(t,"-extra")]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},["".concat(t,"-view")]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},["".concat(t,"-row")]:{"> th, > td":{paddingBottom:a,paddingInlineEnd:o},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},["".concat(t,"-item-label")]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:"".concat((0,O.zA)(c)," ").concat((0,O.zA)(l))},["&".concat(t,"-item-no-colon::after")]:{content:'""'}},["".concat(t,"-item-no-label")]:{"&::after":{margin:0,content:'""'}},["".concat(t,"-item-content")]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},["".concat(t,"-item")]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",["".concat(t,"-item-label")]:{display:"inline-flex",alignItems:"baseline"},["".concat(t,"-item-content")]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},S=(0,x.OF)("Descriptions",e=>w((0,C.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let A=e=>{let{prefixCls:t,title:n,extra:o,column:b,colon:g=!0,bordered:v,layout:h,children:O,className:j,rootClassName:x,style:C,size:k,labelStyle:w,contentStyle:A,styles:z,items:N,classNames:P}=e,B=E(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:I,direction:H,className:M,style:R,classNames:L,styles:T}=(0,r.TP)("descriptions"),D=I("descriptions",t),W=(0,s.A)(),F=a.useMemo(()=>{var e;return"number"==typeof b?b:null!==(e=(0,c.ko)(W,Object.assign(Object.assign({},d),b)))&&void 0!==e?e:3},[W,b]),V=function(e,t,n){let o=a.useMemo(()=>t||m(n),[t,n]);return a.useMemo(()=>o.map(t=>{var{span:n}=t,a=p(t,["span"]);return"filled"===n?Object.assign(Object.assign({},a),{filled:!0}):Object.assign(Object.assign({},a),{span:"number"==typeof n?n:(0,c.ko)(e,n)})}),[o,e])}(W,N,O),X=(0,i.A)(k),G=f(F,V),[_,q,K]=S(D),Q=a.useMemo(()=>({labelStyle:w,contentStyle:A,styles:{content:Object.assign(Object.assign({},T.content),null==z?void 0:z.content),label:Object.assign(Object.assign({},T.label),null==z?void 0:z.label)},classNames:{label:l()(L.label,null==P?void 0:P.label),content:l()(L.content,null==P?void 0:P.content)}}),[w,A,z,P,L,T]);return _(a.createElement(u.Provider,{value:Q},a.createElement("div",Object.assign({className:l()(D,M,L.root,null==P?void 0:P.root,{["".concat(D,"-").concat(X)]:X&&"default"!==X,["".concat(D,"-bordered")]:!!v,["".concat(D,"-rtl")]:"rtl"===H},j,x,q,K),style:Object.assign(Object.assign(Object.assign(Object.assign({},R),T.root),null==z?void 0:z.root),C)},B),(n||o)&&a.createElement("div",{className:l()("".concat(D,"-header"),L.header,null==P?void 0:P.header),style:Object.assign(Object.assign({},T.header),null==z?void 0:z.header)},n&&a.createElement("div",{className:l()("".concat(D,"-title"),L.title,null==P?void 0:P.title),style:Object.assign(Object.assign({},T.title),null==z?void 0:z.title)},n),o&&a.createElement("div",{className:l()("".concat(D,"-extra"),L.extra,null==P?void 0:P.extra),style:Object.assign(Object.assign({},T.extra),null==z?void 0:z.extra)},o)),a.createElement("div",{className:"".concat(D,"-view")},a.createElement("table",null,a.createElement("tbody",null,G.map((e,t)=>a.createElement(y,{key:t,index:t,colon:g,prefixCls:D,vertical:"vertical"===h,bordered:v,row:e}))))))))};A.Item=e=>{let{children:t}=e;return t};let z=A},37801:(e,t,n)=>{n.d(t,{A:()=>p});var a=n(85407),o=n(85268),l=n(1568),c=n(59912),r=n(64406),i=n(4617),s=n.n(i),d=n(35015),u=n(12115),b=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"];let p=(0,u.forwardRef)(function(e,t){var n=e.prefixCls,i=void 0===n?"rc-checkbox":n,p=e.className,m=e.style,g=e.checked,f=e.disabled,v=e.defaultChecked,h=e.type,y=void 0===h?"checkbox":h,O=e.title,j=e.onChange,x=(0,r.A)(e,b),C=(0,u.useRef)(null),k=(0,u.useRef)(null),w=(0,d.A)(void 0!==v&&v,{value:g}),S=(0,c.A)(w,2),E=S[0],A=S[1];(0,u.useImperativeHandle)(t,function(){return{focus:function(e){var t;null===(t=C.current)||void 0===t||t.focus(e)},blur:function(){var e;null===(e=C.current)||void 0===e||e.blur()},input:C.current,nativeElement:k.current}});var z=s()(i,p,(0,l.A)((0,l.A)({},"".concat(i,"-checked"),E),"".concat(i,"-disabled"),f));return u.createElement("span",{className:z,title:O,style:m,ref:k},u.createElement("input",(0,a.A)({},x,{className:"".concat(i,"-input"),ref:C,onChange:function(t){f||("checked"in e||A(t.target.checked),null==j||j({target:(0,o.A)((0,o.A)({},e),{},{type:y,checked:t.target.checked}),stopPropagation:function(){t.stopPropagation()},preventDefault:function(){t.preventDefault()},nativeEvent:t.nativeEvent}))},disabled:f,checked:!!E,type:y})),u.createElement("span",{className:"".concat(i,"-inner")}))})}}]);