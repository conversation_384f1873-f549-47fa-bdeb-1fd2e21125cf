/**
 * Formats a phone number from international format to local format
 * 
 * Examples:
 * - "+233 20 123 4567" -> "************"
 * - "+233201234567" -> "0201234567"
 * - "233201234567" -> "0201234567"
 * - "0201234567" -> "0201234567" (already in local format)
 * 
 * @param phone The phone number to format
 * @returns The formatted phone number
 */
export const formatPhoneNumber = (phone: string | null | undefined): string => {
  // Return empty string if phone is null or undefined
  if (!phone) return "";
  
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, "");
  
  // If the number already starts with 0, return it as is
  if (phone.startsWith("0")) {
    return phone;
  }
  
  // If the number starts with country code (233 for Ghana)
  if (digitsOnly.startsWith("233")) {
    // Replace 233 with 0
    return "0" + digitsOnly.substring(3);
  }
  
  // If the number starts with + and country code
  if (phone.startsWith("+233")) {
    // Replace +233 with 0
    return "0" + digitsOnly.substring(3);
  }
  
  // If we can't determine the format, return the original
  return phone;
};

/**
 * Formats a phone number for display, adding spaces for readability
 * 
 * Examples:
 * - "0201234567" -> "020 1234 567"
 * 
 * @param phone The phone number to format
 * @returns The formatted phone number with spaces
 */
export const formatPhoneNumberForDisplay = (phone: string | null | undefined): string => {
  // First convert to local format
  const localFormat = formatPhoneNumber(phone);
  
  if (!localFormat) return "";
  
  // Remove all non-digit characters
  const digitsOnly = localFormat.replace(/\D/g, "");
  
  // Format based on Ghana mobile number pattern (10 digits: 0xx xxxx xxx)
  if (digitsOnly.length === 10 && digitsOnly.startsWith("0")) {
    return `${digitsOnly.substring(0, 3)} ${digitsOnly.substring(3, 7)} ${digitsOnly.substring(7)}`;
  }
  
  // If we can't determine the format, return the original local format
  return localFormat;
};

/**
 * Validates if a phone number is in a valid format
 * 
 * @param phone The phone number to validate
 * @returns True if the phone number is valid, false otherwise
 */
export const isValidPhoneNumber = (phone: string | null | undefined): boolean => {
  if (!phone) return false;
  
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, "");
  
  // Check if it's a valid Ghana number (should be 10 digits after formatting)
  const localFormat = formatPhoneNumber(phone);
  const localDigitsOnly = localFormat.replace(/\D/g, "");
  
  return localDigitsOnly.length === 10 && localDigitsOnly.startsWith("0");
};
