"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Button, Form, Input, Typography } from "antd";
import { LockOutlined, MailOutlined, LoadingOutlined } from "@ant-design/icons";
import { useLoginUser } from "@/hooks/useLoginUser";
import { showMessage } from "@/utils/showMessage";
import { useRouter } from "next/navigation";

// Custom styles for form validation errors
import "./auth.css";

export default function SigninWithPassword() {
  const [loading, setLoading] = useState(false);
  const { login } = useLoginUser();
  const router = useRouter();
  const onFinish = async (values: { email: string; password: string }) => {
    setLoading(true);

    const response = await login(values.email, values.password);

    if (response?.success) {
      setLoading(false);
      showMessage("success", response?.message || "Login successful");
      router.replace("/dashboard");
    } else {
      setLoading(false);
      // showMessage(
      //   "error",
      //   response?.message || "Login failed. Please try again.",
      // );

      if (
        response?.message ===
        "getaddrinfo ENOTFOUND ep-icy-resonance-a5p24f0y-pooler.us-east-2.aws.neon.tech"
      ) {
        showMessage("error", "Error in network connection. Check your connection");
      }
      else {
        showMessage("error", response?.message);
      }
    }
  };

  return (
    <div className="w-full max-w-md">
      {/* Logo Image */}
      <div className="mb-8 flex justify-center">
        <Image
          src="/images/logo.png"
          alt="Logo"
          width={150}
          height={64}
          priority
        />
      </div>

      <h2 className="text-gray-800 text-2xl font-bold mb-6 text-center">Sign In</h2>

      <Form
        name="signin"
        onFinish={onFinish}
        layout="vertical"
        className="text-gray-800"
        validateMessages={{ required: '${label} is required' }}
      >
        <Form.Item
          name="email"
          label={<span className="text-gray-700">Email Address</span>}
          rules={[{ required: true, message: "Please enter your email" }]}
        >
          <Input
            size="large"
            prefix={
              <MailOutlined className="text-gray-500" />
            }
            placeholder="Email Address"
            className="rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={<span className="text-gray-700">Password</span>}
          rules={[{ required: true, message: "Please enter your password" }]}
        >
          <Input.Password
            size="large"
            prefix={
              <LockOutlined className="text-gray-500" />
            }
            placeholder="Password"
            className="rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"
          />
        </Form.Item>

        <Form.Item className="mb-4">
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            className="w-full rounded-md bg-blue-500 text-white hover:bg-blue-600 border border-blue-500"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <LoadingOutlined style={{ color: "white", marginRight: "8px" }} spin />
                <span>Logging in...</span>
              </span>
            ) : (
              "Log in"
            )}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
