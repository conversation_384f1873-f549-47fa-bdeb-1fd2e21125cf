// services/receiptApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Receipt types
export interface Receipt {
  id: number;
  saleId: number;
  receiptUrl: string;
  createdBy: string;
  createdAt: string;
  storeName?: string;
  storeAddress?: string;
  storeCity?: string;
  storeState?: string;
  storeCountry?: string;
  storePhone?: string;
  storeEmail?: string;
  storeLogo?: string;
}

export interface PaginatedReceipts {
  total: number;
  page: number;
  perPage: number;
  receipts: Receipt[];
}

export const receiptApi = createApi({
  reducerPath: 'receiptApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Receipt'] as const,
  endpoints: (builder) => ({
    // Get all receipts (paginated)
    getAllReceipts: builder.query<ApiResponse<PaginatedReceipts>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/receipt',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search,
          },
          token,
        };
      },
      providesTags: ['Receipt'],
    }),

    // Get receipt by sale ID
    getReceiptBySaleId: builder.query<ApiResponse<Receipt>, number>({
      query: (saleId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/receipt',
          payloaddata: {
            mode: 'retrieveBySaleId',
            saleId,
          },
          token,
        };
      },
      providesTags: ['Receipt'],
    }),

    // Delete receipt (single)
    deleteReceipt: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (receiptId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/receipt',
          payloaddata: {
            mode: 'delete',
            id: receiptId,
          },
          token,
        };
      },
      invalidatesTags: ['Receipt'],
    }),

    // Bulk delete receipts
    bulkDeleteReceipts: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (receiptIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/receipt',
          payloaddata: {
            mode: 'delete',
            receiptIds,
          },
          token,
        };
      },
      invalidatesTags: ['Receipt'],
    }),


  }),
});

export const {
  useGetAllReceiptsQuery,
  useGetReceiptBySaleIdQuery,
  useDeleteReceiptMutation,
  useBulkDeleteReceiptsMutation,
} = receiptApi;
