import { useState } from 'react';
import { useDeleteExpenseMutation } from '@/reduxRTK/services/expenseApi';
import { showMessage } from '@/utils/showMessage';

export const useExpenseDelete = () => {
  const [deleteExpense, { isLoading }] = useDeleteExpenseMutation();
  const [deletingIds, setDeletingIds] = useState<number[]>([]);

  const handleDelete = async (expenseId: number): Promise<boolean> => {
    try {
      setDeletingIds(prev => [...prev, expenseId]);
      
      const result = await deleteExpense(expenseId).unwrap();
      
      if (result.success) {
        showMessage.success('Expense deleted successfully!');
        return true;
      } else {
        showMessage.error(result.message || 'Failed to delete expense');
        return false;
      }
    } catch (error: any) {
      console.error('Error deleting expense:', error);
      showMessage.error(
        error?.data?.message || 
        error?.message || 
        'Failed to delete expense'
      );
      return false;
    } finally {
      setDeletingIds(prev => prev.filter(id => id !== expenseId));
    }
  };

  const isDeleting = (expenseId: number) => deletingIds.includes(expenseId);

  return {
    handleDelete,
    isDeleting,
    isLoading,
  };
};
