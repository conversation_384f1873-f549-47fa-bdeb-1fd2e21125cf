/******/ (() => { // webpackBootstrap
/******/ 	"use strict";


self.fallback = async request => {
  // https://developer.mozilla.org/en-US/docs/Web/API/RequestDestination
  switch (request.destination) {
    case 'document':
      if (true) return caches.match("/offline.html", {
        ignoreSearch: true
      });
    case 'image':
      if (true) return caches.match("/images/offline-fallback.png", {
        ignoreSearch: true
      });
    case 'audio':
      if (true) return caches.match("/audio/offline-fallback.mp3", {
        ignoreSearch: true
      });
    case 'video':
      if (true) return caches.match("/video/offline-fallback.mp4", {
        ignoreSearch: true
      });
    case 'font':
      if (true) return caches.match("/fonts/offline-fallback.woff2", {
        ignoreSearch: true
      });
    case '':
      if (false) {}
    default:
      return Response.error();
  }
};
/******/ })()
;