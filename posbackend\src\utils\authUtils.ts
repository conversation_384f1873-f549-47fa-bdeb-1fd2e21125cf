import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { JWT_SECRET, SALT_ROUNDS } from "../constant/constant";
import { UserRecord, UserRole, JwtPayload } from "../types/type";

// ✅ **Generate JWT Token**
export const generateToken = (id: number, role: UserRole): string => {
  const payload: JwtPayload = { id, role };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: "7d" });
};

// ✅ **Hash Password**
export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

// ✅ **Compare Password**
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};

// ✅ Helper function to exclude password
export const omitPassword = <T extends Record<string, any>>(user: T): Omit<T, "passwordHash"> => {
  const { passwordHash, id, ...safeUser } = user;
  return { ...safeUser, id: Number(id) } as Omit<T, "passwordHash"> & { id: number };
};


