"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeUserFromStore = exports.setUserDefaultStore = exports.getUserDefaultStore = exports.getUserStores = exports.associateUserWithStore = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
/**
 * Associate a user with a store
 * @param requester The user making the request
 * @param userId The user ID to associate
 * @param storeId The store ID to associate
 * @param isDefault Whether this is the default store for the user
 * @returns The created user-store association
 */
const associateUserWithStore = async (requester, userId, storeId, isDefault = false) => {
    // Only admins and superadmins can associate users with stores
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins and superadmins can associate users with stores.");
    }
    // Check if the user exists
    const userExists = await db_1.postgresDb
        .select({ id: schema_1.users.id })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
        .limit(1);
    if (userExists.length === 0) {
        throw new Error("User not found.");
    }
    // Check if the store exists
    const storeExists = await db_1.postgresDb
        .select({ id: schema_1.stores.id })
        .from(schema_1.stores)
        .where((0, drizzle_orm_1.eq)(schema_1.stores.id, storeId))
        .limit(1);
    if (storeExists.length === 0) {
        throw new Error("Store not found.");
    }
    // Check if the association already exists
    const associationExists = await db_1.postgresDb
        .select({ id: schema_1.userStores.id })
        .from(schema_1.userStores)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
        .limit(1);
    if (associationExists.length > 0) {
        throw new Error("User is already associated with this store.");
    }
    // If this is the default store, unset any existing default
    if (isDefault) {
        await db_1.postgresDb
            .update(schema_1.userStores)
            .set({ isDefault: false })
            .where((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId));
    }
    // Create the association
    const [newAssociation] = await db_1.postgresDb
        .insert(schema_1.userStores)
        .values({
        userId,
        storeId,
        isDefault,
        createdBy: requester.id,
    })
        .returning();
    return newAssociation;
};
exports.associateUserWithStore = associateUserWithStore;
/**
 * Get all stores for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @returns List of stores associated with the user
 */
const getUserStores = async (requester, userId) => {
    // Users can only see their own stores unless they're admin/superadmin
    if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: You can only view your own stores.");
    }
    // Get all store IDs associated with the user
    const userStoreAssociations = await db_1.postgresDb
        .select({
        storeId: schema_1.userStores.storeId,
        isDefault: schema_1.userStores.isDefault,
    })
        .from(schema_1.userStores)
        .where((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId));
    const storeIds = userStoreAssociations.map((s) => s.storeId);
    if (storeIds.length === 0) {
        return [];
    }
    // Get store details
    const storesData = await db_1.postgresDb
        .select({
        id: schema_1.stores.id,
        name: schema_1.stores.name,
        address: schema_1.stores.address,
        city: schema_1.stores.city,
        state: schema_1.stores.state,
        country: schema_1.stores.country,
        phone: schema_1.stores.phone,
        email: schema_1.stores.email,
        logo: schema_1.stores.logo,
        website: schema_1.stores.website,
        taxId: schema_1.stores.taxId,
        createdBy: schema_1.stores.createdBy,
        createdByName: schema_1.users.name,
        createdAt: schema_1.stores.createdAt,
        updatedAt: schema_1.stores.updatedAt,
        isDefault: schema_1.userStores.isDefault,
    })
        .from(schema_1.stores)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.stores.createdBy, schema_1.users.id))
        .leftJoin(schema_1.userStores, (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.storeId, schema_1.stores.id), (0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId)))
        .where((0, drizzle_orm_1.inArray)(schema_1.stores.id, storeIds))
        .orderBy((0, drizzle_orm_1.desc)(schema_1.userStores.isDefault), (0, drizzle_orm_1.desc)(schema_1.stores.createdAt));
    return storesData;
};
exports.getUserStores = getUserStores;
/**
 * Get the default store for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @returns The default store for the user
 */
const getUserDefaultStore = async (requester, userId) => {
    // Users can only see their own default store unless they're admin/superadmin
    if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: You can only view your own default store.");
    }
    // Get the default store association
    const defaultStoreAssociation = await db_1.postgresDb
        .select({
        storeId: schema_1.userStores.storeId,
    })
        .from(schema_1.userStores)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userStores.isDefault, true)))
        .limit(1);
    if (defaultStoreAssociation.length === 0) {
        // No default store found, try to get any store
        const anyStoreAssociation = await db_1.postgresDb
            .select({
            storeId: schema_1.userStores.storeId,
        })
            .from(schema_1.userStores)
            .where((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId))
            .limit(1);
        if (anyStoreAssociation.length === 0) {
            return null; // No stores associated with this user
        }
        // Get the store details
        const store = await db_1.postgresDb
            .select({
            id: schema_1.stores.id,
            name: schema_1.stores.name,
            address: schema_1.stores.address,
            city: schema_1.stores.city,
            state: schema_1.stores.state,
            country: schema_1.stores.country,
            phone: schema_1.stores.phone,
            email: schema_1.stores.email,
            logo: schema_1.stores.logo,
            website: schema_1.stores.website,
            taxId: schema_1.stores.taxId,
            createdBy: schema_1.stores.createdBy,
            createdByName: schema_1.users.name,
            createdAt: schema_1.stores.createdAt,
            updatedAt: schema_1.stores.updatedAt,
        })
            .from(schema_1.stores)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.stores.createdBy, schema_1.users.id))
            .where((0, drizzle_orm_1.eq)(schema_1.stores.id, anyStoreAssociation[0].storeId))
            .limit(1);
        return store.length > 0 ? { ...store[0], isDefault: false } : null;
    }
    // Get the default store details
    const store = await db_1.postgresDb
        .select({
        id: schema_1.stores.id,
        name: schema_1.stores.name,
        address: schema_1.stores.address,
        city: schema_1.stores.city,
        state: schema_1.stores.state,
        country: schema_1.stores.country,
        phone: schema_1.stores.phone,
        email: schema_1.stores.email,
        logo: schema_1.stores.logo,
        website: schema_1.stores.website,
        taxId: schema_1.stores.taxId,
        createdBy: schema_1.stores.createdBy,
        createdByName: schema_1.users.name,
        createdAt: schema_1.stores.createdAt,
        updatedAt: schema_1.stores.updatedAt,
    })
        .from(schema_1.stores)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.stores.createdBy, schema_1.users.id))
        .where((0, drizzle_orm_1.eq)(schema_1.stores.id, defaultStoreAssociation[0].storeId))
        .limit(1);
    return store.length > 0 ? { ...store[0], isDefault: true } : null;
};
exports.getUserDefaultStore = getUserDefaultStore;
/**
 * Set the default store for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @param storeId The store ID to set as default
 * @returns The updated user-store association
 */
const setUserDefaultStore = async (requester, userId, storeId) => {
    // Users can only set their own default store unless they're admin/superadmin
    if (requester.id !== userId && requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: You can only set your own default store.");
    }
    // Check if the user is associated with the store
    const associationExists = await db_1.postgresDb
        .select({ id: schema_1.userStores.id })
        .from(schema_1.userStores)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
        .limit(1);
    if (associationExists.length === 0) {
        throw new Error("User is not associated with this store.");
    }
    // Unset any existing default
    await db_1.postgresDb
        .update(schema_1.userStores)
        .set({ isDefault: false })
        .where((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId));
    // Set the new default
    const [updatedAssociation] = await db_1.postgresDb
        .update(schema_1.userStores)
        .set({ isDefault: true })
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
        .returning();
    return updatedAssociation;
};
exports.setUserDefaultStore = setUserDefaultStore;
/**
 * Remove a user-store association
 * @param requester The user making the request
 * @param userId The user ID
 * @param storeId The store ID
 * @returns The deleted association ID
 */
const removeUserFromStore = async (requester, userId, storeId) => {
    // Only admins and superadmins can remove user-store associations
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins and superadmins can remove user-store associations.");
    }
    // Delete the association
    const deletedAssociation = await db_1.postgresDb
        .delete(schema_1.userStores)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userStores.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userStores.storeId, storeId)))
        .returning({ deletedId: schema_1.userStores.id });
    if (!deletedAssociation || deletedAssociation.length === 0) {
        throw new Error("Delete failed: Association not found.");
    }
    return { deletedId: deletedAssociation[0].deletedId };
};
exports.removeUserFromStore = removeUserFromStore;
