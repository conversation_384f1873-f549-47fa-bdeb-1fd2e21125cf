"use client";

import React from "react";
import { Avatar } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined } from "@ant-design/icons";
import { User } from "@/types/user";
import dayjs from "dayjs";
import { formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";

interface ProfileAvatarProps {
  user: User;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ user }) => {
  // Format date for display
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";

    // Log the date string for debugging
    console.log("ProfileAvatar - Formatting date:", dateString);

    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  // Log user data for debugging
  console.log("ProfileAvatar - User data:", {
    name: user.name,
    email: user.email,
    phone: user.phone,
    phoneType: typeof user.phone,
    createdAt: user.createdAt,
    createdAtType: typeof user.createdAt,
    role: user.role,
    paymentStatus: user.paymentStatus
  });

  // Log the complete user object for debugging
  console.log("ProfileAvatar - Complete user object:", JSON.stringify(user, null, 2));

  return (
    <div className="flex flex-col items-center">
      <Avatar
        size={120}
        icon={<UserOutlined />}
        className="mb-4 bg-blue-600"
      />

      <h3 className="text-lg font-medium text-gray-800 mb-1">{user.name}</h3>
      <p className="text-gray-600 mb-4 capitalize">{user.role}</p>

      <div className="mt-6 w-full">
        <h4 className="text-gray-800 font-medium mb-2">Account Information</h4>
        <div className="text-gray-600">
          <p className="flex justify-between py-2 border-b border-gray-200">
            <span className="flex items-center">
              <MailOutlined className="mr-2" />
              Email:
            </span>
            <span className="text-gray-800">{user.email}</span>
          </p>
          <p className="flex justify-between py-2 border-b border-gray-200">
            <span className="flex items-center">
              <PhoneOutlined className="mr-2" />
              Phone:
            </span>
            <span className="text-gray-800">
              {user && user.phone ? formatPhoneNumberForDisplay(user.phone) : "Not provided"}
            </span>
          </p>
          <p className="flex justify-between py-2 border-b border-gray-200">
            <span>Role:</span>
            <span className="text-gray-800 capitalize">{user.role}</span>
          </p>
          <p className="flex justify-between py-2 border-b border-gray-200">
            <span>Payment Status:</span>
            <span className={`capitalize ${
              user.paymentStatus === 'paid' ? 'text-green-500' :
              user.paymentStatus === 'pending' ? 'text-yellow-500' :
              'text-red-500'
            }`}>
              {user.paymentStatus}
            </span>
          </p>
          {user.nextPaymentDue && (
            <p className="flex justify-between py-2 border-b border-gray-200">
              <span>Next Payment:</span>
              <span className="text-gray-800">
                {formatDate(user.nextPaymentDue)}
              </span>
            </p>
          )}
          <p className="flex justify-between py-2 border-b border-gray-200">
            <span>Member Since:</span>
            <span className="text-gray-800">
              {user && user.createdAt ? formatDate(user.createdAt) : "N/A"}
            </span>
          </p>
          {user.lastPaymentDate && (
            <p className="flex justify-between py-2 border-b border-gray-200">
              <span>Last Payment:</span>
              <span className="text-gray-800">
                {formatDate(user.lastPaymentDate)}
              </span>
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileAvatar;
