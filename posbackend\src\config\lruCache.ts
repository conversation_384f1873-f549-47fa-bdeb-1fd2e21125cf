// DISABLED: All caching removed to ensure fresh data

// Cache statistics for monitoring
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0
};

/**
 * DISABLED: Get an item from cache - always returns undefined for fresh data
 * @param key Cache key
 * @returns Always undefined to force fresh database queries
 */
export const getCached = <T>(key: string): T | undefined => {
  // DISABLED: Always return undefined to force fresh data
  console.log(`🚫 Cache DISABLED - getCached(${key}) returning undefined to force fresh data`);
  cacheStats.misses++;
  return undefined;
};

/**
 * DISABLED: Set an item in cache - does nothing to ensure fresh data
 * @param key Cache key
 * @param data Data to cache (ignored)
 * @param ttl Optional custom TTL in milliseconds (ignored)
 */
export const setCached = <T>(key: string, data: T, ttl?: number): void => {
  // DISABLED: Do nothing to prevent caching
  console.log(`🚫 Cache DISABLED - setCached(${key}) ignored to ensure fresh data`);
  cacheStats.sets++;
};

/**
 * DISABLED: Delete an item from cache - does nothing
 * @param key Cache key
 */
export const deleteCached = (key: string): void => {
  // DISABLED: Do nothing since there's no cache
  console.log(`🚫 Cache DISABLED - deleteCached(${key}) ignored`);
  cacheStats.deletes++;
};

/**
 * DISABLED: Clear all cache entries - does nothing
 */
export const clearCache = (): void => {
  // DISABLED: Do nothing since there's no cache
  console.log(`🚫 Cache DISABLED - clearCache() ignored`);
};

/**
 * Get cache statistics - shows disabled cache stats
 */
export const getCacheStats = () => {
  return {
    ...cacheStats,
    size: 0, // No cache
    hitRate: 0, // No cache hits since cache is disabled
    status: 'DISABLED - No caching for fresh data'
  };
};

// DISABLED: No cleanup needed since there's no cache

export default {
  get: getCached,
  set: setCached,
  delete: deleteCached,
  clear: clearCache,
  stats: getCacheStats
};
