(()=>{var e={};e.id=5343,e.ids=[5343],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},97418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(70260),a=s(28203),l=s(25155),n=s.n(l),i=s(67292),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d=["",{children:["dashboard",{children:["expenses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28604)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expenses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expenses\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/expenses/page",pathname:"/dashboard/expenses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},59532:(e,t,s)=>{Promise.resolve().then(s.bind(s,28604))},46380:(e,t,s)=>{Promise.resolve().then(s.bind(s,23719))},81045:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},51592:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},73021:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},97464:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},4472:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},99730:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},23719:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ei});var r=s(45512),a=s(58009),l=s(55735),n=s(7325),i=s(48752),c=s(6987),d=s(3117),o=s(37764),x=s(21419),m=s(56403),p=s(37287),h=s(91054),u=s(27069),g=s(58733),y=s(11855);let f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var j=s(78480),v=a.forwardRef(function(e,t){return a.createElement(j.A,(0,y.A)({},e,{ref:t,icon:f}))}),b=s(88752),A=s(73021),N=s(60636),w=s(765),C=s(95239),M=s(48991),k=s(31111),E=s(87173),z=s(77067),D=s(70001),P=s(25421),S=s(81045),Y=s(25834),R=s(99261),F=s(86977),q=s(63844),V=s(73542),H=s(16589),L=s.n(H),I=s(92273),T=s(41561),_=s(5607);let $=({expenses:e,loading:t=!1,onEdit:s,onDelete:a,onBulkDelete:l,onView:n,selectedExpenses:i=[],onSelectionChange:c,isMobile:o=!1})=>{let x=(0,V.E)(),p=o||x,g=(0,I.d4)(e=>e.auth.user),y=g?.role,f="admin"===y||"superadmin"===y,j="admin"===y||"superadmin"===y,v=(e,t)=>{c&&c(t?[...i,e]:i.filter(t=>t!==e))},b=t=>{c&&(t.target.checked?c(e.map(e=>e.id)):c([]))},N=e.length>0&&i.length===e.length,w=i.length>0&&i.length<e.length,C=e=>{let t={cash:{label:"Cash",color:"green"},card:{label:"Card",color:"blue"},mobile_money:{label:"Mobile Money",color:"purple"},bank_transfer:{label:"Bank Transfer",color:"orange"},cheque:{label:"Cheque",color:"cyan"}}[e]||{label:e,color:"default"};return(0,r.jsx)(k.A,{color:t.color,children:t.label})},M=e=>`₵${parseFloat(e).toFixed(2)}`;return E.A,d.Ay,h.A,d.Ay,u.A,(0,r.jsxs)("div",{children:[i.length>0&&j&&l&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-red-700",children:[i.length," expense(s) selected"]}),(0,r.jsx)(d.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(P.A,{}),onClick:()=>l(i),size:p?"small":"middle",children:"Delete Selected"})]})}),p?(0,r.jsxs)(q.jB,{columns:c?"50px 200px 120px 120px 120px 150px":"200px 120px 120px 120px 150px",minWidth:c?"800px":"750px",children:[c&&(0,r.jsx)(q.A0,{className:"text-center",children:(0,r.jsx)(z.A,{indeterminate:w,checked:N,onChange:b})}),(0,r.jsx)(q.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Expense"]})}),(0,r.jsx)(q.A0,{children:"Amount"}),(0,r.jsx)(q.A0,{children:"Category"}),(0,r.jsx)(q.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)(q.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,r.jsxs)(q.Hj,{selected:i.includes(e.id),children:[c&&(0,r.jsx)(q.nA,{className:"text-center",children:(0,r.jsx)(z.A,{checked:i.includes(e.id),onChange:t=>v(e.id,t.target.checked)})}),(0,r.jsx)(q.nA,{children:(0,r.jsxs)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis",children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),e.isRecurring&&(0,r.jsxs)(k.A,{color:"blue",className:"mt-1 text-xs",children:[(0,r.jsx)(m.A,{className:"mr-1"}),e.recurringFrequency]})]})}),(0,r.jsx)(q.nA,{children:(0,r.jsx)("span",{className:"font-semibold text-green-600",children:M(e.amount)})}),(0,r.jsx)(q.nA,{children:e.category?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:e.category.color}}),(0,r.jsx)("span",{className:"text-sm truncate max-w-[80px]",children:e.category.name})]}):(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"No category"})}),(0,r.jsx)(q.nA,{children:(0,r.jsx)("span",{className:"text-sm",children:L()(e.expenseDate).format("MMM DD, YYYY")})}),(0,r.jsx)(q.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[n&&(0,r.jsx)(D.A,{title:"View Details",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",icon:(0,r.jsx)(Y.A,{}),onClick:()=>n(e),className:"text-green-500 hover:text-green-400"})}),f&&s&&(0,r.jsx)(D.A,{title:"Edit",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",icon:(0,r.jsx)(R.A,{}),onClick:()=>s(e),className:"text-blue-500 hover:text-blue-400"})}),j&&a&&(0,r.jsx)(D.A,{title:"Delete",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",danger:!0,icon:(0,r.jsx)(F.A,{}),onClick:()=>a(e.id),className:"text-red-500 hover:text-red-400"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[c&&(0,r.jsx)("th",{className:"px-4 py-3 text-left",children:(0,r.jsx)(z.A,{indeterminate:w,checked:N,onChange:b})}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Expense"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vendor"}),(0,r.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[c&&(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,r.jsx)(z.A,{checked:i.includes(e.id),onChange:t=>v(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"px-4 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),e.description&&(0,r.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description}),e.isRecurring&&(0,r.jsxs)(k.A,{color:"blue",className:"mt-1 text-xs",children:[(0,r.jsx)(m.A,{className:"mr-1"}),e.recurringFrequency]})]})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-lg font-semibold text-green-600",children:M(e.amount)})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:e.category?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.category.color}}),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.category.name})]}):(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"No category"})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:L()(e.expenseDate).format("MMM DD, YYYY")}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:C(e.paymentMethod)}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:e.vendor||"-"}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[n&&(0,r.jsx)(D.A,{title:"View Details",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",icon:(0,r.jsx)(Y.A,{}),onClick:()=>n(e),className:"text-green-500"})}),f&&s&&(0,r.jsx)(D.A,{title:"Edit",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",icon:(0,r.jsx)(R.A,{}),onClick:()=>s(e),className:"text-blue-500"})}),j&&a&&(0,r.jsx)(D.A,{title:"Delete",children:(0,r.jsx)(d.Ay,{type:"text",size:"small",danger:!0,icon:(0,r.jsx)(F.A,{}),onClick:()=>a(e.id),className:"text-red-500"})})]})})]},e.id))})]})})]})};var O=s(85187),B=s(41257),G=s(88472),J=s(46542),U=s(98776),W=s(4472),K=s(97464),Q=s(99730),X=s(51592),Z=s(24648),ee=s(49792);let{Option:et}=n.A,{TextArea:es}=o.A,er=({isOpen:e,onClose:t,onSuccess:s,expense:i})=>{let[c]=B.A.useForm(),x=!!i,[m,{isLoading:p}]=(0,C.Nx)(),[h,{isLoading:u}]=(0,C.cp)(),{data:g,isLoading:y}=(0,M.L)({page:1,limit:100}),f=g?.data?.categories||[],j=p||u,v=[{value:"daily",label:"Daily"},{value:"weekly",label:"Weekly"},{value:"monthly",label:"Monthly"},{value:"quarterly",label:"Quarterly"},{value:"yearly",label:"Yearly"}];(0,a.useEffect)(()=>{e&&(x&&i?c.setFieldsValue({title:i.title,description:i.description,amount:parseFloat(i.amount),categoryId:i.categoryId,expenseDate:i.expenseDate?L()(i.expenseDate):L()(),paymentMethod:i.paymentMethod,vendor:i.vendor,isRecurring:i.isRecurring,recurringFrequency:i.recurringFrequency,tags:i.tags}):(c.resetFields(),c.setFieldsValue({expenseDate:L()(),paymentMethod:"cash",isRecurring:!1})))},[e,x,i,c]);let N=async e=>{try{let r;let a={title:e.title,description:e.description,amount:e.amount,expenseDate:e.expenseDate?.toISOString(),paymentMethod:e.paymentMethod,vendor:e.vendor,isRecurring:e.isRecurring||!1,recurringFrequency:e.isRecurring?e.recurringFrequency:void 0,tags:e.tags};e.categoryId&&(a.categoryId=e.categoryId),(r=x&&i?await h({expenseId:i.id,expenseData:a}).unwrap():await m(a).unwrap()).success?(ee.r.success(x?"Expense updated successfully!":"Expense created successfully!"),c.resetFields(),s?.(),t()):ee.r.error(r.message||"Failed to save expense")}catch(e){console.error("Error saving expense:",e),ee.r.error(e?.data?.message||e?.message||`Failed to ${x?"update":"create"} expense`)}},w=()=>{c.resetFields(),t()},k=x?"Edit Expense":"Add New Expense",E=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(d.Ay,{onClick:w,disabled:j,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsxs)(d.Ay,{type:"primary",onClick:()=>c.submit(),loading:j,icon:j?(0,r.jsx)(b.A,{}):(0,r.jsx)(W.A,{}),children:[x?"Update":"Create"," Expense"]})]});return(0,r.jsxs)(U.A,{isOpen:e,onClose:w,title:k,width:"500px",footer:E,children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Expense: ",i?.title]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{className:"mr-2"}),"Add New Expense"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:x?"Update expense information":"Record a new business expense"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(B.A,{form:c,layout:"vertical",onFinish:N,className:"expense-form",requiredMark:!0,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,r.jsx)(B.A.Item,{name:"title",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(K.A,{className:"mr-1"})," Expense Title ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter expense title"}],tooltip:"Brief description of the expense",children:(0,r.jsx)(o.A,{placeholder:"e.g., Office Rent, Utilities, Equipment Purchase",maxLength:255})}),(0,r.jsx)(B.A.Item,{name:"description",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(K.A,{className:"mr-1"})," Description"]}),tooltip:"Additional details about the expense",children:(0,r.jsx)(es,{placeholder:"Additional details about this expense...",rows:3,maxLength:500})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Financial Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(B.A.Item,{name:"amount",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"})," Amount (₵) ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter amount"},{type:"number",min:.01,message:"Amount must be greater than 0"}],tooltip:"The total amount spent",children:(0,r.jsx)(G.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00",prefix:"₵"})}),(0,r.jsx)(B.A.Item,{name:"categoryId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Q.A,{className:"mr-1"})," Category"]}),tooltip:"Select expense category for better organization",children:(0,r.jsx)(n.A,{placeholder:"Select category",loading:y,allowClear:!0,showSearch:!0,optionFilterProp:"children",children:f.map(e=>(0,r.jsx)(et,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),e.name]})},e.id))})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(B.A.Item,{name:"expenseDate",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Expense Date ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please select expense date"}],tooltip:"When this expense occurred",children:(0,r.jsx)(l.A,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"Select date"})}),(0,r.jsx)(B.A.Item,{name:"paymentMethod",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(X.A,{className:"mr-1"})," Payment Method ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please select payment method"}],tooltip:"How this expense was paid",children:(0,r.jsx)(n.A,{placeholder:"Select payment method",children:[{value:"cash",label:"Cash"},{value:"card",label:"Card"},{value:"mobile_money",label:"Mobile Money"},{value:"bank_transfer",label:"Bank Transfer"},{value:"cheque",label:"Cheque"}].map(e=>(0,r.jsx)(et,{value:e.value,children:e.label},e.value))})})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Additional Information"}),(0,r.jsx)(B.A.Item,{name:"vendor",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Z.A,{className:"mr-1"})," Vendor/Supplier"]}),tooltip:"Who was paid for this expense",children:(0,r.jsx)(o.A,{placeholder:"e.g., ABC Company, John Doe, Utility Company",maxLength:255})}),(0,r.jsx)(B.A.Item,{name:"tags",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Q.A,{className:"mr-1"})," Tags"]}),tooltip:"Comma-separated tags for better categorization",children:(0,r.jsx)(o.A,{placeholder:"e.g., urgent, monthly, equipment",maxLength:200})}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)(B.A.Item,{name:"isRecurring",valuePropName:"checked",label:(0,r.jsx)("span",{className:"flex items-center",children:"Recurring Expense"}),tooltip:"Check if this is a recurring expense",children:(0,r.jsx)(J.A,{})}),(0,r.jsx)(B.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isRecurring!==t.isRecurring,children:({getFieldValue:e})=>e("isRecurring")?(0,r.jsx)(B.A.Item,{name:"recurringFrequency",label:"Frequency",rules:[{required:!0,message:"Please select frequency"}],children:(0,r.jsx)(n.A,{placeholder:"Select frequency",children:v.map(e=>(0,r.jsx)(et,{value:e.value,children:e.label},e.value))})}):null})]})]})]})]})};var ea=s(51531);let{RangePicker:el}=l.A,{Option:en}=n.A,ei=()=>{let{user:e}=(0,N.A)(),t=(0,w.a)(),s=e?.role,[l,y]=(0,a.useState)(1),[f]=(0,a.useState)(10),[j,k]=(0,a.useState)(""),[E,z]=(0,a.useState)(),[D,P]=(0,a.useState)(null),[S,Y]=(0,a.useState)([]),[R,F]=(0,a.useState)(!1),[q,V]=(0,a.useState)(null),[H,I]=(0,a.useState)(!1),[B,G]=(0,a.useState)(!1),[J,U]=(0,a.useState)(null),[W,K]=(0,a.useState)([]),Q="admin"===s||"superadmin"===s,X="admin"===s||"superadmin"===s,Z={page:l,limit:f,search:j.trim(),categoryId:E,startDate:D?.[0]?.format("YYYY-MM-DD"),endDate:D?.[1]?.endOf("day").toISOString()},{data:et,isLoading:es,error:ei,refetch:ec}=(0,C.cY)(Z),{data:ed}=(0,M.L)({page:1,limit:100}),[eo,{isLoading:ex}]=(0,C.GH)(),em=et?.data?.expenses||[],ep=et?.data?.total||0,eh=ed?.data?.categories||[],eu=e=>{k(e),y(1)},eg=async()=>{if(J)try{let e=await eo(J).unwrap();e.success?(ee.r.success("Expense deleted successfully!"),Y(e=>e.filter(e=>e!==J)),I(!1),U(null)):ee.r.error(e.message||"Failed to delete expense")}catch(e){console.error("Error deleting expense:",e),ee.r.error(e?.data?.message||"Failed to delete expense")}},ey=async()=>{try{let e=W.map(e=>eo(e).unwrap());await Promise.all(e),ee.r.success(`${W.length} expense(s) deleted successfully!`),Y([]),G(!1),K([])}catch(e){console.error("Error deleting expenses:",e),ee.r.error("Failed to delete some expenses")}};return ei?(0,r.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,r.jsx)(c.A,{className:"w-full",children:(0,r.jsxs)("div",{className:"text-center text-red-500 p-8",children:[(0,r.jsx)("p",{children:"Error loading expenses. Please try again."}),(0,r.jsx)(d.Ay,{type:"primary",icon:(0,r.jsx)(m.A,{}),onClick:()=>ec(),className:"mt-4",children:"Retry"})]})})}):(0,r.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,r.jsxs)(c.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Expense Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,r.jsxs)("div",{className:t?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,r.jsx)(d.Ay,{type:"primary",icon:(0,r.jsx)(p.A,{}),onClick:()=>{if(!Q){ee.r.error("You don't have permission to add expenses");return}V(null),F(!0)},size:t?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:t?"":"Add Expense"}),(0,r.jsx)(d.Ay,{type:"primary",icon:(0,r.jsx)(h.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){i.Ay.error({message:"Please allow popups to print expenses"});return}let t=`
      <html>
        <head>
          <title>Expenses Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print {
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Expenses Report</h2>
            <p class="date">Generated on: ${L()().format("MMMM D, YYYY h:mm A")}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Title</th>
                <th>Amount</th>
                <th>Category</th>
                <th>Date</th>
                <th>Payment Method</th>
                <th>Vendor</th>
              </tr>
            </thead>
            <tbody>
              ${em.map(e=>`
                <tr>
                  <td>${e.title}</td>
                  <td>₵${parseFloat(e.amount).toFixed(2)}</td>
                  <td>${e.category?.name||"No category"}</td>
                  <td>${L()(e.expenseDate).format("MMM DD, YYYY")}</td>
                  <td>${e.paymentMethod}</td>
                  <td>${e.vendor||"-"}</td>
                </tr>
              `).join("")}
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;e.document.write(t),e.document.close()},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Print"}),(0,r.jsx)(d.Ay,{type:"primary",icon:(0,r.jsx)(u.A,{}),onClick:()=>{let e=new T.uE;e.setFontSize(16),e.text("Expenses Report",14,15),e.setFontSize(10),e.text(`Generated on: ${L()().format("MMMM D, YYYY h:mm A")}`,14,22);let t=em.map(e=>[e.title,`₵${parseFloat(e.amount).toFixed(2)}`,e.category?.name||"No category",L()(e.expenseDate).format("MMM DD, YYYY"),e.paymentMethod,e.vendor||"-"]);(0,_.Ay)(e,{head:[["Title","Amount","Category","Date","Payment Method","Vendor"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("expenses-report.pdf")},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Export"})]}),children:[(0,r.jsx)("div",{className:"mb-4 space-y-3",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsx)(o.A,{placeholder:"Search expenses...",prefix:(0,r.jsx)(g.A,{}),value:j,onChange:e=>eu(e.target.value),className:"flex-1",allowClear:!0}),(0,r.jsx)(n.A,{placeholder:"Filter by category",value:E,onChange:e=>{z(e),y(1)},className:"w-full sm:w-48",allowClear:!0,children:eh.map(e=>(0,r.jsx)(en,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),e.name]})},e.id))}),(0,r.jsx)(el,{value:D,onChange:e=>{e&&e[0]&&e[1]?P([e[0],e[1].endOf("day")]):P(null),y(1)},className:"w-full sm:w-64",format:"YYYY-MM-DD"}),(j||E||D)&&(0,r.jsx)(d.Ay,{onClick:()=>{k(""),z(void 0),P(null),y(1)},icon:(0,r.jsx)(v,{}),children:"Clear"})]})}),(0,r.jsx)("div",{className:"min-h-96",children:es?(0,r.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,r.jsx)(x.A,{indicator:(0,r.jsx)(b.A,{style:{fontSize:48},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[em.length>0?(0,r.jsx)($,{expenses:em,loading:es,onEdit:e=>{V(e),F(!0)},onDelete:e=>{if(!X){ee.r.error("You don't have permission to delete expenses");return}U(e),I(!0)},onBulkDelete:X?e=>{if(!X){ee.r.error("You don't have permission to delete expenses");return}K(e),G(!0)}:void 0,selectedExpenses:S,onSelectionChange:Y,isMobile:t}):(0,r.jsxs)("div",{className:"text-center text-gray-500 py-12",children:[(0,r.jsx)(A.A,{className:"text-4xl mb-4"}),j||E||D?(0,r.jsx)("p",{children:"No expenses found matching your filters."}):(0,r.jsxs)("p",{children:["No expenses found. ",Q&&"Click 'Add Expense' to create one."]})]}),em.length>0&&(0,r.jsx)(O.A,{current:l,pageSize:f,total:ep,onChange:e=>{y(e)},isMobile:t})]})})]}),(0,r.jsx)(er,{isOpen:R,onClose:()=>{F(!1),V(null)},onSuccess:()=>{ec(),Y([])},expense:q}),(0,r.jsx)(ea.A,{isOpen:H,onClose:()=>{I(!1),U(null)},onConfirm:eg,title:"Delete Expense",message:"Are you sure you want to delete this expense? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:ex,type:"danger"}),(0,r.jsx)(ea.A,{isOpen:B,onClose:()=>{G(!1),K([])},onConfirm:ey,title:"Delete Multiple Expenses",message:`Are you sure you want to delete ${W.length} expenses? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:ex,type:"danger"})]})}},85187:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(45512);s(58009);var a=s(59022),l=s(60165);let n=({current:e,pageSize:t,total:s,onChange:n,isMobile:i=!1})=>{let c=Math.ceil(s/t);return 0===s?null:(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,s)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>n(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(a.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,c)},(t,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>n(a),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:a},a)}),(0,r.jsxs)("button",{onClick:()=>n(e+1),disabled:e>=c,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=c?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(l.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>n(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",c]}),(0,r.jsx)("button",{onClick:()=>n(e+1),disabled:e>=c,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=c?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})}},28604:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\expenses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expenses\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,8472,9486,5735,7277,5482,106,4286,6165],()=>s(97418));module.exports=r})();