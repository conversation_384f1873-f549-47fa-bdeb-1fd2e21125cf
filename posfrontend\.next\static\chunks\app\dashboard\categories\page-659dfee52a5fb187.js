(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1038],{15129:(e,t,r)=>{Promise.resolve().then(r.bind(r,46666))},86260:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},89895:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(85407),s=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};var n=r(84021);let i=s.forwardRef(function(e,t){return s.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},46666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var a=r(95155),s=r(12115),l=r(71349),n=r(72093),i=r(43316),o=r(16419),d=r(89895),c=r(36060),u=r(10927),g=r(9273);let x=function(){var e,t,r,a,l,n,i;let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,[c,x]=(0,s.useState)(o),[h,m]=(0,s.useState)(d),[y,p]=(0,s.useState)(""),f=(0,g.d)(y,500);(0,s.useEffect)(()=>{x(1)},[f]);let{data:v,error:j,isLoading:b,isFetching:w,refetch:A}=(0,u.lg)({page:c,limit:h,search:f},{refetchOnMountOrArgChange:!0});console.log("Category search state:",{searchTerm:y,debouncedSearchTerm:f,resultsCount:(null==v?void 0:null===(t=v.data)||void 0===t?void 0:null===(e=t.categories)||void 0===e?void 0:e.length)||0,totalResults:(null==v?void 0:null===(r=v.data)||void 0===r?void 0:r.total)||0});let N=(null==v?void 0:null===(a=v.data)||void 0===a?void 0:a.categories)||[],C=(null==v?void 0:null===(l=v.data)||void 0===l?void 0:l.total)||0;return{categories:N,total:C,page:(null==v?void 0:null===(n=v.data)||void 0===n?void 0:n.page)||c,limit:(null==v?void 0:null===(i=v.data)||void 0===i?void 0:i.perPage)||h,isLoading:b||w,isFetching:w,error:j,refetch:A,searchTerm:y,setSearchTerm:p,handlePageChange:e=>{x(e)},handleLimitChange:e=>{m(e),x(1)}}};var h=r(75912);let m=e=>{let[t,{isLoading:r}]=(0,u.ec)();return{deleteCategory:async r=>{try{let a=await t(r).unwrap();if(!a.success)throw Error(a.message||"Failed to delete category");return(0,h.r)("success","Category deleted successfully"),e&&e(),a.data}catch(e){throw console.error("Delete category error:",e),(0,h.r)("error",e.message||"Failed to delete category"),e}},isDeleting:r}},y=e=>{let[t,{isLoading:r}]=(0,u.gH)();return{bulkDeleteCategories:async r=>{try{console.log("Bulk deleting categories with IDs:",r);let a=await t(r).unwrap();if(!a.success)throw Error(a.message||"Failed to delete categories");return(0,h.r)("success","".concat(r.length," categories deleted successfully")),e&&e(),a.data}catch(e){throw console.error("Bulk delete categories error:",e),(0,h.r)("error",e.message||"Failed to delete categories"),e}},isDeleting:r}};var p=r(80766),f=r(92895),v=r(6457),j=r(17084),b=r(80519),w=r(86260),A=r(27656),N=r(60102),C=r(91256),k=r(21455),P=r.n(k),S=r(83391);let D=e=>{let{categories:t,loading:r,onView:l,onEdit:n,onDelete:o,onBulkDelete:d,isMobile:c=!1}=e,u=(0,C.E)(),g=(0,S.d4)(e=>e.auth.user),x=null==g?void 0:g.role,[h,m]=(0,s.useState)([]),[y,k]=(0,s.useState)(!1),D=e=>{let r=e.target.checked;k(r),r?m(t.filter(e=>L(e)).map(e=>e.id)):m([])},E=(e,t)=>{t?m(t=>[...t,e]):m(t=>t.filter(t=>t!==e))},z=e=>P()(e).format("MMM D, YYYY"),L=e=>"superadmin"===x||"admin"===x&&(null==g?void 0:g.id)===e.createdBy;return(0,a.jsxs)("div",{className:"overflow-hidden bg-white",children:[h.length>0&&(0,a.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[h.length," ",1===h.length?"category":"categories"," selected"]}),(0,a.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,a.jsx)(j.A,{}),onClick:()=>{h.length>0&&d?(d(h),m([]),k(!1)):p.Ay.warning({message:"No categories selected",description:"Please select at least one category to delete."})},className:"ml-2",children:"Delete Selected"})]}),c||u?(0,a.jsxs)(N.jB,{columns:"50px 200px 120px 150px",minWidth:"700px",children:[(0,a.jsx)(N.A0,{className:"text-center",children:(0,a.jsx)(f.A,{checked:y,onChange:D,disabled:0===t.filter(e=>L(e)).length})}),(0,a.jsx)(N.A0,{children:"Name"}),(0,a.jsx)(N.A0,{children:"Created At"}),(0,a.jsx)(N.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,a.jsxs)(N.Hj,{selected:h.includes(e.id),children:[(0,a.jsx)(N.nA,{className:"text-center",children:L(e)&&(0,a.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>E(e.id,t.target.checked)})}),(0,a.jsx)(N.nA,{children:(0,a.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,a.jsx)(N.nA,{children:(0,a.jsx)("div",{className:"max-w-[110px] overflow-hidden text-ellipsis",children:z(e.createdAt)})}),(0,a.jsx)(N.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(v.A,{title:"View",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(b.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),L(e)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{title:"Edit",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(w.A,{}),onClick:()=>n(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,a.jsx)(v.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(A.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,a.jsx)(f.A,{checked:y,onChange:D,disabled:0===t.filter(e=>L(e)).length})}),(0,a.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Created At"}),(0,a.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:h.includes(e.id)?"bg-blue-50":"",children:[(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:L(e)&&(0,a.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>E(e.id,t.target.checked)})}),(0,a.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,a.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,a.jsx)("div",{className:"max-w-[200px] overflow-hidden text-ellipsis",children:e.description||"No description"})}),(0,a.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,a.jsx)("div",{className:"max-w-[130px] overflow-hidden text-ellipsis",children:z(e.createdAt)})}),(0,a.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(v.A,{title:"View",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(b.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-blue-600",size:"middle"})}),L(e)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{title:"Edit",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(w.A,{}),onClick:()=>n(e),type:"text",className:"edit-button",size:"middle"})}),(0,a.jsx)(v.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(A.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]})]})})]},e.id))})]})})]})};var E=r(33621),z=r(44549);let L=e=>{let{current:t,pageSize:r,total:s,onChange:l,isMobile:n=!1}=e,i=Math.ceil(s/r);return(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*r+1})," to"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*r,s)})," of"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)(E.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,i)},(e,r)=>{let s=r+1;return(0,a.jsx)("button",{onClick:()=>l(s),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===s?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:s},s)}),(0,a.jsxs)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=i?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)(z.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",i]}),(0,a.jsx)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=i?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})};var F=r(41657),T=r(83414),R=r(24988);let M=e=>{let[t,{isLoading:r}]=(0,u.XY)();return{createCategory:async r=>{try{var a,s;console.log("useCategoryCreate - Starting category creation with data:",r);let l=null===(a=window.__REDUX_STATE)||void 0===a?void 0:a.auth;console.log("useCategoryCreate - Current auth state:",l?{hasUser:!!l.user,hasToken:!!l.accessToken,userRole:null===(s=l.user)||void 0===s?void 0:s.role}:"Not available");let n=await t(r).unwrap();if(console.log("useCategoryCreate - API response:",n),!n.success)throw console.error("useCategoryCreate - API returned error:",n.message),Error(n.message||"Failed to create category");return(0,h.r)("success","Category created successfully"),e&&e(),n.data}catch(e){throw console.error("Create category error:",e),(0,h.r)("error",e.message||"Failed to create category"),e}},isCreating:r}},I=e=>{let[t,{isLoading:r}]=(0,u.ez)();return{updateCategory:async(r,a)=>{try{let s=await t({categoryId:r,data:a}).unwrap();if(!s.success)throw Error(s.message||"Failed to update category");return(0,h.r)("success","Category updated successfully"),e&&e(),s.data}catch(e){throw console.error("Update category error:",e),(0,h.r)("error",e.message||"Failed to update category"),e}},isUpdating:r}};r(88965);let{TextArea:_}=F.A,B=e=>{let{isOpen:t,onClose:r,onSuccess:l,category:n,currentUser:o}=e,[d]=T.A.useForm(),c=!!n,{createCategory:u,isCreating:g}=M(l),{updateCategory:x,isUpdating:h}=I(l),m=g||h;(0,s.useEffect)(()=>{console.log("CategoryFormPanel - isOpen changed:",t),t&&(console.log("CategoryFormPanel - Panel is open, category:",n),n?d.setFieldsValue({name:n.name,description:n.description||""}):d.resetFields())},[t,n,d]);let y=async()=>{try{let e=await d.validateFields();c&&n?await x(n.id,e):await u(e),r()}catch(e){console.error("Form submission error:",e)}},p=(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(i.Ay,{onClick:r,disabled:m,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,a.jsx)(i.Ay,{type:"primary",loading:m,onClick:y,children:c?"Update":"Create"})]});return(0,a.jsxs)(R.A,{isOpen:t,onClose:r,title:c?"Edit Category":"Add New Category",width:"450px",footer:p,children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Category: ",null==n?void 0:n.name]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Create New Category"]})}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:c?"Update category information using the form below":"Fill in the details to create a new category"})]}),(0,a.jsx)(T.A,{form:d,layout:"vertical",className:"text-gray-800 w-full",style:{color:"#333"},children:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Category Information"}),(0,a.jsx)(T.A.Item,{name:"name",label:"Category Name",rules:[{required:!0,message:"Please enter category name"}],children:(0,a.jsx)(F.A,{placeholder:"Enter category name"})}),(0,a.jsx)(T.A.Item,{name:"description",label:"Description",children:(0,a.jsx)(_,{placeholder:"Enter category description (optional)",rows:4})})]})})]})};var O=r(67649);let U=e=>{let{data:t,error:r,isLoading:a,refetch:s}=(0,u.OU)(e||0,{skip:!e});return{category:(null==t?void 0:t.data)||null,isLoading:a,error:r,refetch:s}};var W=r(87181);let H=e=>{let{isOpen:t,onClose:r,categoryId:s,onEdit:l}=e,{category:c,isLoading:u}=U(s),g=e=>{if(!e)return"N/A";try{return P()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},x=(0,S.d4)(e=>e.auth.user),h="admin"===(null==x?void 0:x.role)&&(null==x?void 0:x.id)===(null==c?void 0:c.createdBy),m=(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(i.Ay,{onClick:r,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),l&&c&&h&&(0,a.jsx)(i.Ay,{type:"primary",onClick:()=>l(c.id),children:"Edit"})]});return(0,a.jsx)(R.A,{isOpen:t,onClose:r,title:"Category Details",width:"500px",footer:m,children:u?(0,a.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Category: ",c.name]}),(0,a.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete category information and details"})]}),(0,a.jsxs)(O.A,{bordered:!0,column:1,className:"category-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,a.jsx)(O.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(d.A,{})," Category ID"]}),children:c.id}),(0,a.jsx)(O.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(d.A,{})," Name"]}),children:c.name}),(0,a.jsx)(O.A.Item,{label:"Description",children:c.description||"No description provided"}),(0,a.jsx)(O.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(W.A,{})," Created At"]}),children:g(c.createdAt)}),c.updatedAt&&(0,a.jsx)(O.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(W.A,{})," Last Updated"]}),children:g(c.updatedAt)})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-gray-800",children:"Category not found"})})};var Y=r(5413);r(66202);let V=e=>{let{searchTerm:t,setSearchTerm:r,isMobile:s=!1}=e;return(0,a.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,a.jsx)(F.A,{placeholder:"Search by name or description...",prefix:(0,a.jsx)(Y.A,{className:"text-gray-500"}),value:t,onChange:e=>{let t=e.target.value;console.log("Category search input changed:",t),r(t)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:s?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,a.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),t&&(0,a.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',t,'"']})]})};var G=r(12467);r(92685);let X=()=>{let[e,t]=(0,s.useState)({width:window.innerWidth,height:window.innerHeight});return(0,s.useEffect)(()=>{function e(){t({width:window.innerWidth,height:window.innerHeight})}return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[]),e},$=()=>{let{user:e}=(0,c.A)(),{width:t}=X(),r=t<640,[u,g]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),[f,v]=(0,s.useState)(!1),[j,b]=(0,s.useState)(null),[w,A]=(0,s.useState)(null),[N,C]=(0,s.useState)(!1),{categories:k,total:P,page:S,limit:E,isLoading:z,refetch:F,searchTerm:T,setSearchTerm:R,handlePageChange:M}=x(),{deleteCategory:I,isDeleting:_}=m(()=>{F(),C(!1)}),{bulkDeleteCategories:O,isDeleting:U}=y(()=>{Y(!1),F()}),[W,Y]=(0,s.useState)(!1),[$,q]=(0,s.useState)([]),J=e=>{b(e),p(!0)},Q=async()=>{if(w)try{await I(w)}catch(e){console.error("Error deleting category:",e)}},K=async()=>{if(console.log("confirmBulkDelete called with categories:",$),$.length>0)try{await O($)}catch(e){console.error("Error in confirmBulkDelete:",e)}},Z=(null==e?void 0:e.role)==="admin";return((0,s.useEffect)(()=>{console.log("isAddPanelOpen changed:",u)},[u]),z)?(0,a.jsx)("div",{className:"p-2 sm:p-4 w-full",children:(0,a.jsx)(l.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"Category Management"}),styles:{body:{padding:"16px",minHeight:"200px",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,a.jsx)("div",{className:"flex justify-center items-center h-60",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})})})}):(0,a.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,a.jsx)(l.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"Category Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:Z&&(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(d.A,{}),onClick:()=>{console.log("Add category button clicked"),b(null),setTimeout(()=>{g(!0)},0)},size:r?"small":"middle",children:r?"":"Add Category"}),children:(0,a.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,a.jsx)(V,{searchTerm:T,setSearchTerm:R,isMobile:r}),z?(0,a.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,a.jsx)(n.A,{indicator:(0,a.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,a.jsxs)(a.Fragment,{children:[k.length>0?(0,a.jsx)(D,{categories:k,loading:!1,onView:e=>{A(e),v(!0)},onEdit:J,onDelete:e=>{A(e),C(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with categoryIds:",e),q(e),Y(!0)},isMobile:r}):(0,a.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{children:"No categories found matching your search criteria."}),(0,a.jsx)(i.Ay,{type:"primary",onClick:()=>R(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,a.jsxs)("p",{children:["No categories found. ",Z&&"Click 'Add Category' to create one."]})}),k.length>0&&(0,a.jsx)(L,{current:S,pageSize:E,total:P,onChange:M,isMobile:r})]})]})}),(0,a.jsx)(B,{isOpen:u,onClose:()=>g(!1),onSuccess:()=>{g(!1),F()},currentUser:e}),(0,a.jsx)(B,{isOpen:h,onClose:()=>p(!1),onSuccess:()=>{p(!1),F()},category:j,currentUser:e}),(0,a.jsx)(H,{isOpen:f,onClose:()=>{v(!1),A(null)},categoryId:w,onEdit:e=>{v(!1);let t=k.find(t=>t.id===e)||null;t&&J(t)}}),(0,a.jsx)(G.A,{isOpen:N,onClose:()=>{C(!1),A(null)},onConfirm:Q,title:"Delete Category",message:"Are you sure you want to delete this category? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:_,type:"danger"}),(0,a.jsx)(G.A,{isOpen:W,onClose:()=>{Y(!1),q([])},onConfirm:K,title:"Delete Multiple Categories",message:"Are you sure you want to delete ".concat($.length," categories? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:U,type:"danger"})]})}},12467:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155);r(12115);var s=r(46102),l=r(43316),n=r(75218);let i=e=>{let{isOpen:t,onClose:r,onConfirm:i,title:o,message:d,confirmText:c="Confirm",cancelText:u="Cancel",isLoading:g=!1,type:x="danger"}=e;return(0,a.jsx)(s.A,{title:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{style:{color:"danger"===x?"#ff4d4f":"warning"===x?"#faad14":"#1890ff",marginRight:8}}),(0,a.jsx)("span",{children:o})]}),open:t,onCancel:r,footer:[(0,a.jsx)(l.Ay,{onClick:r,disabled:g,children:u},"cancel"),(0,a.jsx)(l.Ay,{type:"danger"===x?"primary":"default",danger:"danger"===x,onClick:i,loading:g,children:c},"confirm")],maskClosable:!g,closable:!g,centered:!0,children:(0,a.jsx)("p",{className:"my-4",children:d})})}},60102:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,Hj:()=>o,jB:()=>l,nA:()=>i});var a=r(95155);r(12115);var s=r(21567);let l=e=>{let{children:t,columns:r,className:l,minWidth:n="800px"}=e,i=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,a.jsx)("div",{className:(0,s.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:r,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:r,sticky:l}=e,n=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],r),children:t})},i=e=>{let{children:t,className:r,sticky:l}=e,n=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,s.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],r),children:t})},o=e=>{let{children:t,className:r,selected:l=!1,onClick:n}=e;return(0,a.jsx)("div",{className:(0,s.cn)("contents",l&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",r),onClick:n,children:t})}},24988:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(12115),l=r(43316),n=r(79624);let i=e=>{let{isOpen:t,onClose:r,title:i,children:o,width:d="400px",footer:c,fullWidth:u=!1}=e,[g,x]=(0,s.useState)(!1),[h,m]=(0,s.useState)(!1),[y,p]=(0,s.useState)(window.innerWidth);if((0,s.useEffect)(()=>{let e=()=>{p(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,s.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)m(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{x(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{x(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{m(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!h)return null;let f="Point of Sale"===i||u||"100vw"===d;return(0,a.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(f?"sales-panel-container":""),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(g?"opacity-50":"opacity-0"),onClick:r}),(0,a.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(g?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||u||"100vw"===d||y<640?"100vw":y<1024?"500px":"string"==typeof d&&d.includes("px")&&parseInt(d)>600?"600px":d},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,a.jsx)(l.Ay,{type:"text",icon:(0,a.jsx)(n.A,{style:{color:"#333"}}),onClick:r,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:o}),c&&(0,a.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},36060:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(83391),s=r(70854),l=r(63065),n=r(7875);let i=()=>{let e=(0,a.wA)(),{user:t,accessToken:r}=(0,a.d4)(e=>e.auth),i=(0,s._)(),{refetch:o}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!r,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await o();console.log("useAuth - Refetch result:",a);let s=a.data;if((null==s?void 0:s.success)&&(null==s?void 0:s.data)){console.log("useAuth - API response data:",s.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=s.data.phone||t.phone||"",i=s.data.createdAt||t.createdAt||"",o=s.data.lastPaymentDate||t.lastPaymentDate||void 0,d=s.data.nextPaymentDue||t.nextPaymentDue||null,c=s.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:s.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:s.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:s.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:s.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:s.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let u={...s.data,phone:l,createdAt:i,lastPaymentDate:o,nextPaymentDue:d,createdBy:c,paymentStatus:a};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:u,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==s?void 0:s.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:c}}},70854:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var a=r(12115),s=r(83391),l=r(21455),n=r.n(l);let i=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,r]=(0,a.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,a.useEffect)(()=>{if(!e){r({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,a=!1,s=!0,l="inactive";if("superadmin"===e.role){r({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){a=!0,s=!1,l="active";let r=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),i=n()();if(t=l.diff(i,"day"),r){let r=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:r,daysRemaining:t,trialDaysUsed:r,trialDaysRemaining:t,isActive:a,needsPayment:s})}}}else"pending"===e.paymentStatus?(a=!1,s=!0,l="pending"):"overdue"===e.paymentStatus?(a=!1,s=!0,l="overdue"):(a=!1,s=!0,l="inactive");r({isActive:a,daysRemaining:t,status:l,needsPayment:s})},[e]),t}},9273:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var a=r(12115);function s(e,t){let[r,s]=(0,a.useState)(e);return(0,a.useEffect)(()=>{console.log("Debouncing value:",e);let r=setTimeout(()=>{console.log("Debounce timer completed, setting value:",e),s(e)},t);return()=>{clearTimeout(r)}},[e,t]),r}},91256:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var a=r(12115);let s=()=>{let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(43463),s=r(69795);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},75912:(e,t,r)=>{"use strict";r.d(t,{r:()=>s});var a=r(55037);let s=(e,t)=>{"success"===e?a.oR.success(t):"error"===e?a.oR.error(t):"warning"===e&&(0,a.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},92685:()=>{},88965:()=>{},66202:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[5140,8059,3228,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,766,5211,821,8441,1517,7358],()=>t(15129)),_N_E=e.O()}]);