(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5589],{56826:(e,t,s)=>{Promise.resolve().then(s.bind(s,6570))},6570:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95155);s(12115);var n=s(71349),r=s(22810),l=s(2796),i=s(43316),d=s(62704),o=s(19397),c=s(50147),u=s(53582),m=s(36060),x=s(30555),h=s(76046);let p=()=>{let{user:e}=(0,m.A)(),t=(0,x.a)(),s=(0,h.useRouter)(),p=[{title:"Sales Reports",description:"Analyze sales performance, revenue trends, and customer insights",icon:(0,a.jsx)(d.A,{className:"text-4xl text-blue-600"}),path:"/dashboard/reports/sales",features:["Sales summary and trends","Product performance analysis","Customer purchase patterns","Revenue breakdowns"]},{title:"Inventory Reports",description:"Monitor stock levels, track inventory movement, and manage reorders",icon:(0,a.jsx)(o.A,{className:"text-4xl text-green-600"}),path:"/dashboard/reports/inventory",features:["Current stock levels","Low stock alerts","Inventory valuation","Stock movement tracking"]}],y=e=>{s.push(e)};return(0,a.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,a.jsx)(n.A,{title:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"text-blue-600"}),(0,a.jsx)("span",{className:"text-gray-800",children:"Reports & Analytics"})]}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,a.jsxs)("div",{className:"w-full space-y-6",children:[(0,a.jsx)(n.A,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Business Intelligence Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Generate comprehensive reports to gain insights into your business performance"})]})}),(0,a.jsx)(r.A,{gutter:[24,24],children:p.map((e,t)=>(0,a.jsx)(l.A,{xs:24,lg:12,children:(0,a.jsxs)(n.A,{className:"h-full hover:shadow-lg transition-shadow duration-300 border-gray-200",styles:{body:{padding:"24px"}},children:[(0,a.jsx)("div",{className:"text-center mb-4",children:e.icon}),(0,a.jsxs)("div",{className:"text-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Features:"}),(0,a.jsx)("ul",{className:"space-y-2",children:e.features.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"}),e]},t))})]}),(0,a.jsxs)(i.Ay,{type:"primary",block:!0,size:"large",onClick:()=>y(e.path),className:"bg-blue-600 hover:bg-blue-700",icon:(0,a.jsx)(u.A,{}),children:["View ",e.title]})]})},t))}),(0,a.jsx)(n.A,{title:"Quick Overview",className:"bg-gray-50",children:(0,a.jsxs)(r.A,{gutter:[16,16],children:[(0,a.jsx)(l.A,{xs:12,sm:6,children:(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"2"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Report Types"})]})}),(0,a.jsx)(l.A,{xs:12,sm:6,children:(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"∞"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Data Points"})]})}),(0,a.jsx)(l.A,{xs:12,sm:6,children:(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"24/7"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Availability"})]})}),(0,a.jsx)(l.A,{xs:12,sm:6,children:(0,a.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:"PDF"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Export Format"})]})})]})}),(0,a.jsx)(n.A,{className:"bg-yellow-50 border-yellow-200",children:(0,a.jsxs)("div",{className:"text-center text-yellow-800",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDE80 More Reports Coming Soon"}),(0,a.jsx)("p",{className:"text-sm mb-3",children:"We're working on additional reporting features to help you make better business decisions."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs",children:[(0,a.jsx)("div",{children:"• Financial Reports"}),(0,a.jsx)("div",{children:"• Customer Analytics"}),(0,a.jsx)("div",{children:"• Expense Reports"}),(0,a.jsx)("div",{children:"• Performance Dashboards"})]})]})})]})})})}},30555:(e,t,s)=>{"use strict";s.d(t,{a:()=>n});var a=s(12115);function n(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},36060:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(83391),n=s(70854),r=s(63065),l=s(7875);let i=()=>{let e=(0,a.wA)(),{user:t,accessToken:s}=(0,a.d4)(e=>e.auth),i=(0,n._)(),{refetch:d}=(0,r.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let o=!!t&&!!s,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await d();console.log("useAuth - Refetch result:",a);let n=a.data;if((null==n?void 0:n.success)&&(null==n?void 0:n.data)){console.log("useAuth - API response data:",n.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let r=n.data.phone||t.phone||"",i=n.data.createdAt||t.createdAt||"",d=n.data.lastPaymentDate||t.lastPaymentDate||void 0,o=n.data.nextPaymentDue||t.nextPaymentDue||null,c=n.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:n.data.phone,userPhone:t.phone,finalPhone:r,apiCreatedAt:n.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:n.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:d,apiNextPaymentDue:n.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:o,apiCreatedBy:n.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let u={...n.data,phone:r,createdAt:i,lastPaymentDate:d,nextPaymentDue:o,createdBy:c,paymentStatus:a};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,l.gV)({user:u,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==n?void 0:n.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:o,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:c}}},70854:(e,t,s)=>{"use strict";s.d(t,{_:()=>i});var a=s(12115),n=s(83391),r=s(21455),l=s.n(r);let i=()=>{let e=(0,n.d4)(e=>e.auth.user),[t,s]=(0,a.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,a.useEffect)(()=>{if(!e){s({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,a=!1,n=!0,r="inactive";if("superadmin"===e.role){s({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){a=!0,n=!1,r="active";let s=!e.lastPaymentDate;if(e.nextPaymentDue){let r=l()(e.nextPaymentDue),i=l()();if(t=r.diff(i,"day"),s){let s=l()().diff(l()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:s,daysRemaining:t,trialDaysUsed:s,trialDaysRemaining:t,isActive:a,needsPayment:n})}}}else"pending"===e.paymentStatus?(a=!1,n=!0,r="pending"):"overdue"===e.paymentStatus?(a=!1,n=!0,r="overdue"):(a=!1,n=!0,r="inactive");s({isActive:a,daysRemaining:t,status:r,needsPayment:n})},[e]),t}}},e=>{var t=t=>e(e.s=t);e.O(0,[6754,1961,2261,4831,3316,9135,1388,9907,3288,1349,6818,821,8441,1517,7358],()=>t(56826)),_N_E=e.O()}]);