// services/salesApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Sale types
export interface SaleItem {
  id: number;
  saleId: number;
  productId: number;
  productName: string;
  quantity: number;
  price: string;
}

export interface Sale {
  id: number;
  totalAmount: string;
  paymentMethod: 'cash' | 'card' | 'mobile_money';
  transactionDate: string;
  createdBy: number;
  createdByName?: string;
  storeId?: number;
  storeName?: string;
  receiptUrl?: string;
  items?: SaleItem[];
}

export interface PaginatedSales {
  total: number;
  page: number;
  perPage: number;
  sales: Sale[];
}

export interface CreateSaleItemDto {
  productId: number;
  quantity: number;
  price: number;
}

export interface CreateSaleDto {
  totalAmount: number;
  paymentMethod: 'cash' | 'card' | 'mobile_money';
  items: CreateSaleItemDto[];
  receiptUrl: string; // Make this required
  storeId?: number; // Store ID for the sale
}

export interface UpdateSaleDto {
  totalAmount?: number;
  paymentMethod?: 'cash' | 'card' | 'mobile_money';
}

export const salesApi = createApi({
  reducerPath: 'salesApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Sale'] as const,
  endpoints: (builder) => ({
    // Get all sales (paginated)
    getAllSales: builder.query<ApiResponse<PaginatedSales>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Sale'],
    }),

    // Get sale by ID
    getSaleById: builder.query<ApiResponse<{sale: Sale & {items?: SaleItem[]}, saleItems?: SaleItem[]}>, number>({
      query: (saleId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'retrieve',
            saleId,
          },
          token,
        };
      },
      providesTags: (result, error, id) => [{ type: 'Sale', id }],
    }),

    // Create new sale
    createSale: builder.mutation<ApiResponse<{ sales: { saleId: number }[] }>, CreateSaleDto>({
      query: (saleData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'createnew',
            saleData,
          },
          token,
        };
      },
      invalidatesTags: ['Sale'],
    }),

    // Update sale
    updateSale: builder.mutation<ApiResponse<Sale>, { saleId: number; updateData: UpdateSaleDto }>({
      query: ({ saleId, updateData }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'update',
            saleId,
            ...updateData,
          },
          token,
        };
      },
      invalidatesTags: (result, error, { saleId }) => [{ type: 'Sale', id: saleId }],
    }),

    // Delete sale (single)
    deleteSale: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (saleId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'delete',
            saleId,
          },
          token,
        };
      },
      invalidatesTags: ['Sale'],
    }),

    // Bulk delete sales
    bulkDeleteSales: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (saleIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/sales',
          payloaddata: {
            mode: 'delete',
            saleIds,
          },
          token,
        };
      },
      invalidatesTags: ['Sale'],
    }),
  }),
});

export const {
  useGetAllSalesQuery,
  useGetSaleByIdQuery,
  useCreateSaleMutation,
  useUpdateSaleMutation,
  useDeleteSaleMutation,
  useBulkDeleteSalesMutation,
} = salesApi;
