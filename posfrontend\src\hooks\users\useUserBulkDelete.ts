import { useBulkDeleteUsersMutation } from "@/reduxRTK/services/authApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useUserBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting users
  const [bulkDeleteUsers, { isLoading }] = useBulkDeleteUsersMutation();

  const deleteUsers = async (userIds: number[]) => {
    try {
      console.log("Bulk deleting users with IDs:", userIds);
      
      const result = await bulkDeleteUsers(userIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete users");
      }

      showMessage("success", `${userIds.length} users deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete users error:", error);
      showMessage("error", error.message || "Failed to delete users");
      throw error;
    }
  };

  return {
    bulkDeleteUsers: deleteUsers,
    isDeleting: isLoading
  };
};
