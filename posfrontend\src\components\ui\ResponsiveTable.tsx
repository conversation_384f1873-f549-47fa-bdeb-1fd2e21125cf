"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ResponsiveTableProps {
  children: React.ReactNode;
  className?: string;
  minWidth?: string;
}

/**
 * Responsive table wrapper that provides horizontal scrolling
 * Uses CSS Grid for better responsiveness
 */
export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  children,
  className,
  minWidth = "800px"
}) => {
  return (
    <div className={cn(
      "w-full overflow-x-auto overflow-y-visible",
      "border border-gray-200 rounded-lg shadow-sm",
      "bg-white",
      className
    )}>
      <div 
        className="min-w-full"
        style={{ minWidth }}
      >
        {children}
      </div>
    </div>
  );
};

interface ResponsiveTableGridProps {
  children: React.ReactNode;
  columns: string; // CSS Grid template columns
  className?: string;
  minWidth?: string;
}

/**
 * CSS Grid-based responsive table for better control
 */
export const ResponsiveTableGrid: React.FC<ResponsiveTableGridProps> = ({
  children,
  columns,
  className,
  minWidth = "800px"
}) => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  return (
    <div className={cn(
      "w-full overflow-x-auto overflow-y-visible",
      "border border-gray-200 rounded-lg shadow-sm",
      "bg-white",
      // Ensure smooth scrolling on mobile
      "scroll-smooth",
      className
    )}>
      <div
        className={cn(
          "gap-0",
          isMobile ? "grid" : "block" // Use grid only on mobile
        )}
        style={isMobile ? {
          gridTemplateColumns: columns,
          minWidth,
          width: "max-content"
        } : {}}
      >
        {children}
      </div>
    </div>
  );
};

interface TableHeaderProps {
  children: React.ReactNode;
  className?: string;
  sticky?: "left" | "right";
}

export const TableHeader: React.FC<TableHeaderProps> = ({
  children,
  className,
  sticky
}) => {
  // For mobile, disable sticky positioning to allow proper scrolling
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  const stickyClasses = {
    left: !isMobile ? "sticky left-0 z-20 bg-gray-50 border-r border-gray-200" : "",
    right: !isMobile ? "sticky right-0 z-20 bg-gray-50 border-l border-gray-200" : ""
  };

  return (
    <div className={cn(
      "bg-gray-50 border-b border-gray-200",
      "font-medium text-xs text-gray-700 uppercase tracking-wider",
      "px-3 py-3 text-left",
      "sticky top-0 z-10",
      sticky && stickyClasses[sticky],
      className
    )}>
      {children}
    </div>
  );
};

interface TableCellProps {
  children: React.ReactNode;
  className?: string;
  sticky?: "left" | "right";
}

export const TableCell: React.FC<TableCellProps> = ({
  children,
  className,
  sticky
}) => {
  // For mobile, disable sticky positioning to allow proper scrolling
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  const stickyClasses = {
    left: !isMobile ? "sticky left-0 z-10 bg-white border-r border-gray-200" : "",
    right: !isMobile ? "sticky right-0 z-10 bg-white border-l border-gray-200" : ""
  };

  return (
    <div className={cn(
      "px-3 py-4 text-sm text-gray-900",
      "border-b border-gray-200",
      "whitespace-nowrap",
      sticky && stickyClasses[sticky],
      className
    )}>
      {children}
    </div>
  );
};

interface TableRowProps {
  children: React.ReactNode;
  className?: string;
  selected?: boolean;
  onClick?: () => void;
}

export const TableRow: React.FC<TableRowProps> = ({
  children,
  className,
  selected = false,
  onClick
}) => {
  return (
    <div 
      className={cn(
        "contents",
        selected && "bg-blue-50",
        onClick && "cursor-pointer hover:bg-gray-50",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

/**
 * Hybrid table component that uses HTML table for desktop and CSS Grid for mobile
 */
interface HybridTableProps {
  children: React.ReactNode;
  className?: string;
  columns?: string; // Grid columns for mobile
  minWidth?: string;
}

export const HybridTable: React.FC<HybridTableProps> = ({
  children,
  className,
  columns = "1fr",
  minWidth = "800px"
}) => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  if (isMobile) {
    // Use CSS Grid for mobile
    return (
      <div className={cn(
        "w-full overflow-x-auto overflow-y-visible",
        "border border-gray-200 rounded-lg shadow-sm",
        "bg-white scroll-smooth",
        className
      )}>
        <div
          className="grid gap-0"
          style={{
            gridTemplateColumns: columns,
            minWidth,
            width: "max-content"
          }}
        >
          {children}
        </div>
      </div>
    );
  }

  // Use traditional HTML table for desktop
  return (
    <div className={cn(
      "w-full overflow-x-auto overflow-y-visible",
      "border border-gray-200 rounded-lg shadow-sm",
      "bg-white",
      className
    )}>
      <table className="min-w-full divide-y divide-gray-200">
        {children}
      </table>
    </div>
  );
};

// Export default as ResponsiveTable for backward compatibility
export default ResponsiveTable;
