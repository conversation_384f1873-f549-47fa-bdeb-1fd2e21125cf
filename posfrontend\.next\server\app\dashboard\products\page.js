(()=>{var e={};e.id=9926,e.ids=[9926],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},68718:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>d,pages:()=>s,routeModule:()=>m,tree:()=>p});var i=a(70260),n=a(28203),o=a(25155),l=a.n(o),r=a(67292),c={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);a.d(t,c);let p=["",{children:["dashboard",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,77961)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],s=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/products/page",pathname:"/dashboard/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},11663:(e,t,a)=>{Promise.resolve().then(a.bind(a,77961))},19911:(e,t,a)=>{Promise.resolve().then(a.bind(a,58589))},73021:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var i=a(11855),n=a(58009);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var l=a(78480);let r=n.forwardRef(function(e,t){return n.createElement(l.A,(0,i.A)({},e,{ref:t,icon:o}))})},10685:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var i=a(11855),n=a(58009);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"}}]},name:"shopping",theme:"outlined"};var l=a(78480);let r=n.forwardRef(function(e,t){return n.createElement(l.A,(0,i.A)({},e,{ref:t,icon:o}))})},90351:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var a=Array.isArray(t)?t:t.split(",");if(0===a.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),o=n.replace(/\/.*$/,"");return a.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):n===t})}return!0}},58589:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ts});var i=a(45512),n=a(58009),o=a.n(n),l=a(6987),r=a(3117),c=a(48752),p=a(21419),s=a(56403),d=a(10685),m=a(88752),x=a(60636),u=a(765),v=a(2274),f=a(78337);let g=(e=1,t=10)=>{let[a,i]=(0,n.useState)(e),[o,l]=(0,n.useState)(t),[r,c]=(0,n.useState)(""),p=(0,f.d)(r,500);(0,n.useEffect)(()=>{i(1)},[p]);let{data:s,error:d,isLoading:m,refetch:x}=(0,v.r3)({page:a,limit:o,search:p},{refetchOnMountOrArgChange:!0});return{products:s?.data?.products||[],total:s?.data?.total||0,page:a,limit:o,isLoading:m,error:d,refetch:x,forceRefresh:()=>{console.log("Forcing product list refresh"),x()},searchTerm:r,setSearchTerm:c,handlePageChange:e=>{i(e)},handleLimitChange:e=>{l(e),i(1)}}};var h=a(49792);let b=e=>{let[t,{isLoading:a}]=(0,v.lY)();return{deleteProduct:async a=>{try{let i=await t(a).unwrap();if(!i.success)throw Error(i.message||"Failed to delete product");return(0,h.r)("success","Product deleted successfully"),e&&e(),i.data}catch(e){throw console.error("Delete product error:",e),(0,h.r)("error",e.message||"Failed to delete product"),e}},isDeleting:a}},y=e=>{let[t,{isLoading:a}]=(0,v.IT)();return{bulkDeleteProducts:async a=>{try{console.log("Bulk deleting products with IDs:",a);let i=await t(a).unwrap();if(!i.success)throw Error(i.message||"Failed to delete products");return(0,h.r)("success",`${a.length} products deleted successfully`),e&&e(),i.data}catch(e){throw console.error("Bulk delete products error:",e),(0,h.r)("error",e.message||"Failed to delete products"),e}},isDeleting:a}};var j=a(45103),w=a(77067),k=a(31111),A=a(70001),N=a(11855);let z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"};var S=a(78480),C=n.forwardRef(function(e,t){return n.createElement(S.A,(0,N.A)({},e,{ref:t,icon:z}))}),D=a(25421),P=a(73021),F=a(25834),E=a(99261),O=a(86977);let q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M696 480H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"plus-circle",theme:"outlined"};var I=n.forwardRef(function(e,t){return n.createElement(S.A,(0,N.A)({},e,{ref:t,icon:q}))}),M=a(63844),T=a(73542),R=a(43087),$=a(92273);let L=({products:e,loading:t,onView:a,onEdit:o,onDelete:l,onBulkDelete:p,onAdjustStock:s,isMobile:m=!1})=>{let x=(0,T.E)(),u=(0,$.d4)(e=>e.auth.user),v=u?.role,[f,g]=(0,n.useState)([]),[h,b]=(0,n.useState)(!1),{data:y}=(0,R.lg)({page:1,limit:100}),N=y?.data?.categories||[],z=e=>{let t=N.find(t=>t.id===e);return t?t.name:"Uncategorized"},S=t=>{let a=t.target.checked;b(a),a?g(e.filter(e=>L(e)).map(e=>e.id)):g([])},q=(e,t)=>{t?g(t=>[...t,e]):g(t=>t.filter(t=>t!==e))},L=e=>"superadmin"===v||"admin"===v&&u?.id===e.createdBy,U="admin"===v,_=e=>e.imageUrl?(0,i.jsx)(j.default,{src:e.imageUrl,alt:e.name,width:48,height:48,className:"object-cover rounded-md"}):(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center",children:(0,i.jsx)(C,{className:"text-2xl text-gray-400"})});return(0,i.jsxs)("div",{className:"overflow-hidden bg-white",children:[f.length>0&&(0,i.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[f.length," ",1===f.length?"product":"products"," selected"]}),(0,i.jsx)(r.Ay,{type:"primary",danger:!0,icon:(0,i.jsx)(D.A,{}),onClick:()=>{f.length>0&&p?(p(f),g([]),b(!1)):c.Ay.warning({message:"No products selected",description:"Please select at least one product to delete."})},className:"ml-2",children:"Delete Selected"})]}),m||x?(0,i.jsxs)(M.jB,{columns:"50px 200px 120px 150px",minWidth:"700px",children:[(0,i.jsx)(M.A0,{className:"text-center",children:(0,i.jsx)(w.A,{checked:h,onChange:S,disabled:0===e.filter(e=>L(e)).length})}),(0,i.jsx)(M.A0,{children:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"}),"Name"]})}),(0,i.jsx)(M.A0,{children:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(P.A,{className:"mr-1"}),"Price"]})}),(0,i.jsx)(M.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,i.jsxs)(M.Hj,{selected:f.includes(e.id),children:[(0,i.jsx)(M.nA,{className:"text-center",children:L(e)&&(0,i.jsx)(w.A,{checked:f.includes(e.id),onChange:t=>q(e.id,t.target.checked)})}),(0,i.jsx)(M.nA,{children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[_(e),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name}),(0,i.jsx)(k.A,{color:"blue",className:"mt-1",children:z(e.categoryId)})]})]})}),(0,i.jsxs)(M.nA,{children:[(0,i.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,i.jsx)(P.A,{className:"mr-1"}),parseFloat(e.price).toFixed(2)]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:[(0,i.jsx)(d.A,{className:"mr-1"}),e.stockQuantity]})]}),(0,i.jsx)(M.nA,{className:"text-right",children:(0,i.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,i.jsx)(A.A,{title:"View",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(F.A,{}),onClick:()=>a(e.id),type:"text",className:"view-button text-blue-600",size:"small"})}),L(e)&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A.A,{title:"Edit",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(E.A,{}),onClick:()=>o(e),type:"text",className:"edit-button",size:"small"})}),(0,i.jsx)(A.A,{title:"Delete",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(O.A,{}),onClick:()=>l(e.id),type:"text",className:"delete-button",danger:!0,size:"small"})})]}),U&&(0,i.jsx)(A.A,{title:"Adjust Stock",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(I,{}),onClick:()=>s(e),type:"text",className:"adjust-stock-button",size:"small"})})]})})]},e.id))]}):(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,i.jsx)(w.A,{checked:h,onChange:S,disabled:0===e.filter(e=>L(e)).length})}),(0,i.jsx)("th",{scope:"col",className:"w-12 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Image"}),(0,i.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,i.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Category"}),(0,i.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"SKU"}),(0,i.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Barcode"}),(0,i.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Price"}),(0,i.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Stock"}),(0,i.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,i.jsxs)("tr",{className:f.includes(e.id)?"bg-blue-50":"",children:[(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:L(e)&&(0,i.jsx)(w.A,{checked:f.includes(e.id),onChange:t=>q(e.id,t.target.checked)})}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap",children:_(e)}),(0,i.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,i.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,i.jsx)(k.A,{color:"blue",children:z(e.categoryId)})}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.sku||"-"}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.barcode||"-"}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,i.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,i.jsx)(P.A,{className:"mr-1"}),parseFloat(e.price).toFixed(2)]})}),(0,i.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"}),e.stockQuantity]})}),(0,i.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,i.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,i.jsx)(A.A,{title:"View",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(F.A,{}),onClick:()=>a(e.id),type:"text",className:"view-button text-blue-600",size:"middle"})}),L(e)&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A.A,{title:"Edit",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(E.A,{}),onClick:()=>o(e),type:"text",className:"edit-button",size:"middle"})}),(0,i.jsx)(A.A,{title:"Delete",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(O.A,{}),onClick:()=>l(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]}),U&&(0,i.jsx)(A.A,{title:"Adjust Stock",children:(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(I,{}),onClick:()=>s(e),type:"text",className:"adjust-stock-button",size:"middle"})})]})})]},e.id))})]})})]})};var U=a(59022),_=a(60165);let B=({current:e,pageSize:t,total:a,onChange:n,isMobile:o=!1})=>{let l=Math.ceil(a/t);return(0,i.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,i.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,i.jsx)("div",{children:(0,i.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,i.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,i.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,a)})," of"," ",(0,i.jsx)("span",{className:"font-medium text-gray-900",children:a})," results"]})}),(0,i.jsx)("div",{children:(0,i.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,i.jsxs)("button",{onClick:()=>n(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,i.jsx)("span",{className:"sr-only",children:"Previous"}),(0,i.jsx)(U.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,l)},(t,a)=>{let o=a+1;return(0,i.jsx)("button",{onClick:()=>n(o),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===o?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:o},o)}),(0,i.jsxs)("button",{onClick:()=>n(e+1),disabled:e>=l,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=l?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,i.jsx)("span",{className:"sr-only",children:"Next"}),(0,i.jsx)(_.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,i.jsx)("button",{onClick:()=>n(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,i.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",l]}),(0,i.jsx)("button",{onClick:()=>n(e+1),disabled:e>=l,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=l?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var V=a(7325),H=a(41257),G=a(53950),K=a(37764),Q=a(88472),Y=a(55735),W=a(98776);let J=e=>{let[t,{isLoading:a}]=(0,v.Q$)();return{createProduct:async a=>{try{console.log("useProductCreate - Starting product creation with data:",a);let i=await t(a).unwrap();if(console.log("useProductCreate - API response:",i),!i.success)throw console.error("useProductCreate - API returned error:",i.message),Error(i.message||"Failed to create product");return(0,h.r)("success","Product created successfully"),e&&(console.log("Calling onSuccess callback for product creation"),e(),setTimeout(()=>{console.log("Calling delayed onSuccess to ensure table refresh after product creation"),e()},300)),i.data?.products?.[0]||i.data}catch(e){throw console.error("Create product error:",e),(0,h.r)("error",e.message||"Failed to create product"),e}},isCreating:a}},X=e=>{let[t,{isLoading:a}]=(0,v.vM)();return{updateProduct:async(a,i)=>{try{console.log("Updating product:",a,i);let n=await t({productId:a,data:i}).unwrap();if(!n.success)throw Error(n.message||"Failed to update product");return(0,h.r)("success","Product updated successfully"),e&&(console.log("Calling onSuccess callback for product update"),e(),setTimeout(()=>{console.log("Calling delayed onSuccess to ensure table refresh after product update"),e()},300)),n.data}catch(e){throw console.error("Update product error:",e),(0,h.r)("error",e.message||"Failed to update product"),e}},isUpdating:a}},Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M120 160H72c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zm833 0h-48c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zM200 736h112c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H200c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm321 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm126 0h178c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H647c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-255 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-79 64H201c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm257 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm256 0H648c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h178c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-385 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"barcode",theme:"outlined"};var ee=n.forwardRef(function(e,t){return n.createElement(S.A,(0,N.A)({},e,{ref:t,icon:Z}))}),et=a(81045),ea=a(16589),ei=a.n(ea),en=a(66785);a(12306);var eo=a(28200);function el(e,t,a,i){return new(a||(a=Promise))(function(n,o){function l(e){try{c(i.next(e))}catch(e){o(e)}}function r(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(l,r)}c((i=i.apply(e,t||[])).next())})}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;let er=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function ec(e,t,a){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let a=t.split(".").pop().toLowerCase(),i=er.get(a);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,o="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&ep(i,"path",o),void 0!==a&&Object.defineProperty(i,"handle",{value:a,writable:!1,configurable:!1,enumerable:!0}),ep(i,"relativePath",o),i}function ep(e,t,a){Object.defineProperty(e,t,{value:a,writable:!1,configurable:!1,enumerable:!0})}let es=[".DS_Store","Thumbs.db"];function ed(e){return"object"==typeof e&&null!==e}function em(e){return e.filter(e=>-1===es.indexOf(e.name))}function ex(e){if(null===e)return[];let t=[];for(let a=0;a<e.length;a++){let i=e[a];t.push(i)}return t}function eu(e){if("function"!=typeof e.webkitGetAsEntry)return ev(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?eg(t):ev(e,t)}function ev(e,t){return el(this,void 0,void 0,function*(){var a;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,ec(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return ec(i,null!==(a=null==t?void 0:t.fullPath)&&void 0!==a?a:void 0)})}function ef(e){return el(this,void 0,void 0,function*(){return e.isDirectory?eg(e):function(e){return el(this,void 0,void 0,function*(){return new Promise((t,a)=>{e.file(a=>{t(ec(a,e.fullPath))},e=>{a(e)})})})}(e)})}function eg(e){let t=e.createReader();return new Promise((e,a)=>{let i=[];!function n(){t.readEntries(t=>el(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(ef));i.push(e),n()}else try{let t=yield Promise.all(i);e(t)}catch(e){a(e)}}),e=>{a(e)})}()})}var eh=a(90351);function eb(e){return function(e){if(Array.isArray(e))return eN(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||eA(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ey(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function ej(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(a),!0).forEach(function(t){ew(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ey(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function ew(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function ek(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||eA(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eA(e,t){if(e){if("string"==typeof e)return eN(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return eN(e,t)}}function eN(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}var ez="function"==typeof eh?eh:eh.default,eS=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),a=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(a)}},eC=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},eD=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},eP={code:"too-many-files",message:"Too many files"};function eF(e,t){var a="application/x-moz-file"===e.type||ez(e,t);return[a,a?null:eS(t)]}function eE(e,t,a){if(eO(e.size)){if(eO(t)&&eO(a)){if(e.size>a)return[!1,eC(a)];if(e.size<t)return[!1,eD(t)]}else if(eO(t)&&e.size<t)return[!1,eD(t)];else if(eO(a)&&e.size>a)return[!1,eC(a)]}return[!0,null]}function eO(e){return null!=e}function eq(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function eI(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function eM(e){e.preventDefault()}function eT(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return function(e){for(var a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return t.some(function(t){return!eq(e)&&t&&t.apply(void 0,[e].concat(i)),eq(e)})}}function eR(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function e$(e){return/^.*\.[\w]+$/.test(e)}var eL=["children"],eU=["open"],e_=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],eB=["refKey","onChange","onClick"];function eV(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||eH(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eH(e,t){if(e){if("string"==typeof e)return eG(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return eG(e,t)}}function eG(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function eK(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function eQ(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?eK(Object(a),!0).forEach(function(t){eY(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):eK(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function eY(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function eW(e,t){if(null==e)return{};var a,i,n=function(e,t){if(null==e)return{};var a,i,n={},o=Object.keys(e);for(i=0;i<o.length;i++)a=o[i],t.indexOf(a)>=0||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}var eJ=(0,n.forwardRef)(function(e,t){var a=e.children,i=e0(eW(e,eL)),o=i.open,l=eW(i,eU);return(0,n.useImperativeHandle)(t,function(){return{open:o}},[o]),n.createElement(n.Fragment,null,a(eQ(eQ({},l),{},{open:o})))});eJ.displayName="Dropzone";var eX={disabled:!1,getFilesFromEvent:function(e){return el(this,void 0,void 0,function*(){return ed(e)&&ed(e.dataTransfer)?function(e,t){return el(this,void 0,void 0,function*(){if(e.items){let a=ex(e.items).filter(e=>"file"===e.kind);return"drop"!==t?a:em(function e(t){return t.reduce((t,a)=>[...t,...Array.isArray(a)?e(a):[a]],[])}((yield Promise.all(a.map(eu)))))}return em(ex(e.files).map(e=>ec(e)))})}(e.dataTransfer,e.type):ed(e)&&ed(e.target)?ex(e.target.files).map(e=>ec(e)):Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return el(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>ec(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};eJ.defaultProps=eX,eJ.propTypes={children:eo.func,accept:eo.objectOf(eo.arrayOf(eo.string)),multiple:eo.bool,preventDropOnDocument:eo.bool,noClick:eo.bool,noKeyboard:eo.bool,noDrag:eo.bool,noDragEventsBubbling:eo.bool,minSize:eo.number,maxSize:eo.number,maxFiles:eo.number,disabled:eo.bool,getFilesFromEvent:eo.func,onFileDialogCancel:eo.func,onFileDialogOpen:eo.func,useFsAccessApi:eo.bool,autoFocus:eo.bool,onDragEnter:eo.func,onDragLeave:eo.func,onDragOver:eo.func,onDrop:eo.func,onDropAccepted:eo.func,onDropRejected:eo.func,onError:eo.func,validator:eo.func};var eZ={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function e0(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=eQ(eQ({},eX),e),a=t.accept,i=t.disabled,o=t.getFilesFromEvent,l=t.maxSize,r=t.minSize,c=t.multiple,p=t.maxFiles,s=t.onDragEnter,d=t.onDragLeave,m=t.onDragOver,x=t.onDrop,u=t.onDropAccepted,v=t.onDropRejected,f=t.onFileDialogCancel,g=t.onFileDialogOpen,h=t.useFsAccessApi,b=t.autoFocus,y=t.preventDropOnDocument,j=t.noClick,w=t.noKeyboard,k=t.noDrag,A=t.noDragEventsBubbling,N=t.onError,z=t.validator,S=(0,n.useMemo)(function(){return function(e){if(eO(e))return Object.entries(e).reduce(function(e,t){var a=ek(t,2),i=a[0],n=a[1];return[].concat(eb(e),[i],eb(n))},[]).filter(function(e){return eR(e)||e$(e)}).join(",")}(a)},[a]),C=(0,n.useMemo)(function(){return eO(a)?[{description:"Files",accept:Object.entries(a).filter(function(e){var t=ek(e,2),a=t[0],i=t[1],n=!0;return eR(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(e$)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,t){var a=ek(t,2),i=a[0],n=a[1];return ej(ej({},e),{},ew({},i,n))},{})}]:a},[a]),D=(0,n.useMemo)(function(){return"function"==typeof g?g:e8},[g]),P=(0,n.useMemo)(function(){return"function"==typeof f?f:e8},[f]),F=(0,n.useRef)(null),E=(0,n.useRef)(null),O=eV((0,n.useReducer)(e1,eZ),2),q=O[0],I=O[1],M=q.isFocused,T=q.isFileDialogActive,R=(0,n.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),$=function(){!R.current&&T&&setTimeout(function(){E.current&&!E.current.files.length&&(I({type:"closeDialog"}),P())},300)};(0,n.useEffect)(function(){return window.addEventListener("focus",$,!1),function(){window.removeEventListener("focus",$,!1)}},[E,T,P,R]);var L=(0,n.useRef)([]),U=function(e){F.current&&F.current.contains(e.target)||(e.preventDefault(),L.current=[])};(0,n.useEffect)(function(){return y&&(document.addEventListener("dragover",eM,!1),document.addEventListener("drop",U,!1)),function(){y&&(document.removeEventListener("dragover",eM),document.removeEventListener("drop",U))}},[F,y]),(0,n.useEffect)(function(){return!i&&b&&F.current&&F.current.focus(),function(){}},[F,b,i]);var _=(0,n.useCallback)(function(e){N?N(e):console.error(e)},[N]),B=(0,n.useCallback)(function(e){var t;e.preventDefault(),e.persist(),ea(e),L.current=[].concat(function(e){if(Array.isArray(e))return eG(e)}(t=L.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||eH(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),eI(e)&&Promise.resolve(o(e)).then(function(t){if(!eq(e)||A){var a,i,n,o,d,m,x,u,v=t.length,f=v>0&&(i=(a={files:t,accept:S,minSize:r,maxSize:l,multiple:c,maxFiles:p,validator:z}).files,n=a.accept,o=a.minSize,d=a.maxSize,m=a.multiple,x=a.maxFiles,u=a.validator,(!!m||!(i.length>1))&&(!m||!(x>=1)||!(i.length>x))&&i.every(function(e){var t=ek(eF(e,n),1)[0],a=ek(eE(e,o,d),1)[0],i=u?u(e):null;return t&&a&&!i}));I({isDragAccept:f,isDragReject:v>0&&!f,isDragActive:!0,type:"setDraggedFiles"}),s&&s(e)}}).catch(function(e){return _(e)})},[o,s,_,A,S,r,l,c,p,z]),V=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e);var t=eI(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&m&&m(e),!1},[m,A]),H=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e);var t=L.current.filter(function(e){return F.current&&F.current.contains(e)}),a=t.indexOf(e.target);-1!==a&&t.splice(a,1),L.current=t,!(t.length>0)&&(I({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),eI(e)&&d&&d(e))},[F,d,A]),G=(0,n.useCallback)(function(e,t){var a=[],i=[];e.forEach(function(e){var t=eV(eF(e,S),2),n=t[0],o=t[1],c=eV(eE(e,r,l),2),p=c[0],s=c[1],d=z?z(e):null;if(n&&p&&!d)a.push(e);else{var m=[o,s];d&&(m=m.concat(d)),i.push({file:e,errors:m.filter(function(e){return e})})}}),(!c&&a.length>1||c&&p>=1&&a.length>p)&&(a.forEach(function(e){i.push({file:e,errors:[eP]})}),a.splice(0)),I({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),x&&x(a,i,t),i.length>0&&v&&v(i,t),a.length>0&&u&&u(a,t)},[I,c,S,r,l,p,x,u,v,z]),K=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e),L.current=[],eI(e)&&Promise.resolve(o(e)).then(function(t){(!eq(e)||A)&&G(t,e)}).catch(function(e){return _(e)}),I({type:"reset"})},[o,G,_,A]),Q=(0,n.useCallback)(function(){if(R.current){I({type:"openDialog"}),D(),window.showOpenFilePicker({multiple:c,types:C}).then(function(e){return o(e)}).then(function(e){G(e,null),I({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(P(e),I({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(R.current=!1,E.current?(E.current.value=null,E.current.click()):_(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):_(e)});return}E.current&&(I({type:"openDialog"}),D(),E.current.value=null,E.current.click())},[I,D,P,h,G,_,C,c]),Y=(0,n.useCallback)(function(e){F.current&&F.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),Q())},[F,Q]),W=(0,n.useCallback)(function(){I({type:"focus"})},[]),J=(0,n.useCallback)(function(){I({type:"blur"})},[]),X=(0,n.useCallback)(function(){j||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(Q,0):Q())},[j,Q]),Z=function(e){return i?null:e},ee=function(e){return w?null:Z(e)},et=function(e){return k?null:Z(e)},ea=function(e){A&&e.stopPropagation()},ei=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.role,n=e.onKeyDown,o=e.onFocus,l=e.onBlur,r=e.onClick,c=e.onDragEnter,p=e.onDragOver,s=e.onDragLeave,d=e.onDrop,m=eW(e,e_);return eQ(eQ(eY({onKeyDown:ee(eT(n,Y)),onFocus:ee(eT(o,W)),onBlur:ee(eT(l,J)),onClick:Z(eT(r,X)),onDragEnter:et(eT(c,B)),onDragOver:et(eT(p,V)),onDragLeave:et(eT(s,H)),onDrop:et(eT(d,K)),role:"string"==typeof a&&""!==a?a:"presentation"},void 0===t?"ref":t,F),i||w?{}:{tabIndex:0}),m)}},[F,Y,W,J,X,B,V,H,K,w,k,i]),en=(0,n.useCallback)(function(e){e.stopPropagation()},[]),eo=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.onChange,i=e.onClick,n=eW(e,eB);return eQ(eQ({},eY({accept:S,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Z(eT(a,K)),onClick:Z(eT(i,en)),tabIndex:-1},void 0===t?"ref":t,E)),n)}},[E,a,c,K,i]);return eQ(eQ({},q),{},{isFocused:M&&!i,getRootProps:ei,getInputProps:eo,rootRef:F,inputRef:E,open:Z(Q)})}function e1(e,t){switch(t.type){case"focus":return eQ(eQ({},e),{},{isFocused:!0});case"blur":return eQ(eQ({},e),{},{isFocused:!1});case"openDialog":return eQ(eQ({},eZ),{},{isFileDialogActive:!0});case"closeDialog":return eQ(eQ({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return eQ(eQ({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return eQ(eQ({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return eQ({},eZ);default:return e}}function e8(){}let e4=({imageUrl:e,setImageUrl:t,form:a})=>{let[n,l]=o().useState(!1),[c,p]=o().useState(!1),{getRootProps:s,getInputProps:d,isDragActive:x}=e0({accept:{"image/*":[".jpeg",".jpg",".png",".gif",".webp"]},maxFiles:1,onDrop:async e=>{if(e.length>0)try{l(!0),p(!1);let i=await (0,en.iG)(e[0]);t(i),a.setFieldsValue({imageUrl:i}),G.Ay.success("Image uploaded successfully")}catch(e){console.error("Upload error:",e),G.Ay.error("Failed to upload image"),t(null),a.setFieldsValue({imageUrl:null})}finally{l(!1)}}});return(0,i.jsxs)("div",{className:"flex flex-col items-start",children:[(0,i.jsxs)("div",{...s(),className:`border-2 border-dashed rounded-lg p-6 w-full text-center cursor-pointer transition-all duration-200 ${x?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"}`,children:[(0,i.jsx)("input",{...d()}),(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-3",children:[(0,i.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center",children:(0,i.jsx)(C,{className:"text-2xl text-gray-400"})}),x?(0,i.jsx)("p",{className:"text-blue-500 font-medium",children:"Drop the image here..."}):(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("p",{className:"text-gray-600 font-medium",children:"Drag & drop an image here, or click to select"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: JPG, PNG, GIF, WEBP"})]})]})]}),e&&!c&&(0,i.jsxs)("div",{className:"mt-4 relative group",children:[(0,i.jsx)(j.default,{src:e,alt:"Product",width:200,height:200,className:"object-cover border border-gray-200 rounded-lg shadow-sm",onError:()=>{console.error("Image failed to load:",e),p(!0),t(null),a.setFieldsValue({imageUrl:null}),G.Ay.error("Failed to load image preview")}}),(0,i.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,i.jsx)(r.Ay,{type:"text",danger:!0,icon:(0,i.jsx)(O.A,{}),onClick:e=>{e.stopPropagation(),t(null),a.setFieldsValue({imageUrl:null})},className:"text-white hover:text-red-500",children:"Remove"})})]}),n&&(0,i.jsxs)("div",{className:"mt-2 text-blue-500 flex items-center",children:[(0,i.jsx)(m.A,{className:"mr-2"}),"Uploading..."]})]})},{Option:e2}=V.A,e3=({isOpen:e,onClose:t,onSuccess:a,product:o,currentUser:l})=>{let[c]=H.A.useForm(),p=!!o,[s,m]=(0,n.useState)(null),[x,u]=(0,n.useState)(!1),[v,f]=(0,n.useState)(!1),g=l?.role||"cashier",{data:h,isLoading:b}=(0,R.lg)({page:1,limit:100}),y=h?.data?.categories||[],{createProduct:j,isCreating:w}=J(a),{updateProduct:k,isUpdating:A}=X(a),N=w||A;(0,n.useEffect)(()=>{e&&(p&&o?(c.setFieldsValue({...o,expiryDate:o.expiryDate?ei()(o.expiryDate):void 0,price:parseFloat(o.price),cost:parseFloat(o.cost),imageUrl:o.imageUrl||null}),m(o.imageUrl||null)):(c.resetFields(),m(null)))},[e,o,c,p]);let{getRootProps:z,getInputProps:S,isDragActive:D}=e0({accept:{"image/*":[".jpeg",".jpg",".png",".gif",".webp"]},maxFiles:1,onDrop:async e=>{e.length>0&&await F(e[0])}}),F=async e=>{try{u(!0);let t=await (0,en.iG)(e);return m(t),c.setFieldsValue({imageUrl:t}),G.Ay.success("Image uploaded successfully"),!1}catch(e){return G.Ay.error("Failed to upload image"),!1}finally{u(!1)}},E=async e=>{try{let a={...e,price:Number(e.price),cost:Number(e.cost),imageUrl:s||null,expiryDate:e.expiryDate?e.expiryDate.format("YYYY-MM-DD"):null};p&&o?await k(o.id,a):await j(a),t()}catch(e){console.error("Error submitting product:",e),G.Ay.error("Failed to save product")}},O=p?`Edit Product${"admin"===g?" (Admin)":""}`:`Add New Product${"admin"===g?" (Admin)":""}`,q=(0,i.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,i.jsx)(r.Ay,{onClick:t,disabled:N,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,i.jsx)(r.Ay,{type:"primary",loading:N,onClick:()=>c.submit(),children:p?"Update":"Create"})]});return(0,i.jsx)(W.A,{isOpen:e,onClose:t,title:O,width:"500px",footer:q,children:(0,i.jsxs)("div",{className:"p-4",children:[(0,i.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:p?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Product: ",o?.name]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),"Create New Product"]})}),(0,i.jsx)("p",{className:"text-gray-600 mt-1",children:p?"Update product information using the form below":"Fill in the details to create a new product"})]}),(0,i.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,i.jsxs)(H.A,{form:c,layout:"vertical",onFinish:E,className:"product-form",requiredMark:!0,onValuesChange:(e,t)=>{if("name"in e&&!v){let t=e.name||"";if(t){let e=t.replace(/[^A-Za-z0-9]/g,"").toUpperCase().slice(0,4),a=Math.floor(1e3+9e3*Math.random()),i=`${e}-${a}`;c.setFieldsValue({sku:i})}else c.setFieldsValue({sku:""})}},children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,i.jsx)(H.A.Item,{name:"name",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"})," Product Name"]}),rules:[{required:!0,message:"Please enter product name"}],tooltip:"The name of the product as it will appear to customers",children:(0,i.jsx)(K.A,{placeholder:"Enter product name"})}),(0,i.jsx)(H.A.Item,{name:"categoryId",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"})," Category"]}),rules:[{required:!0,message:"Please select a category"}],tooltip:"The category this product belongs to",children:(0,i.jsx)(V.A,{placeholder:"Select category",loading:b,disabled:b,children:y.map(e=>(0,i.jsx)(e2,{value:e.id,children:e.name},e.id))})}),(0,i.jsx)(H.A.Item,{name:"imageUrl",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(C,{className:"mr-1"})," Product Image"]}),tooltip:"Upload a product image (optional)",children:(0,i.jsx)(e4,{imageUrl:s,setImageUrl:m,form:c})})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Pricing Information"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsx)(H.A.Item,{name:"price",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(P.A,{className:"mr-1"})," Selling Price (GHS)"]}),rules:[{required:!0,message:"Please enter selling price"}],tooltip:"The price at which you sell the product to customers",children:(0,i.jsx)(Q.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00"})}),(0,i.jsx)(H.A.Item,{name:"cost",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(P.A,{className:"mr-1"})," Purchase Cost (GHS)"]}),rules:[{required:!0,message:"Please enter purchase cost"}],tooltip:"The cost price you pay to acquire the product",children:(0,i.jsx)(Q.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00"})})]})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Inventory Management"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsx)(H.A.Item,{name:"stockQuantity",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"})," Stock Quantity"]}),initialValue:0,tooltip:"Current quantity in stock",children:(0,i.jsx)(Q.A,{min:0,style:{width:"100%"},placeholder:"0"})}),(0,i.jsx)(H.A.Item,{name:"minStockLevel",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-1"})," Min Stock Level"]}),initialValue:5,tooltip:"Minimum quantity before restock alert",children:(0,i.jsx)(Q.A,{min:0,style:{width:"100%"},placeholder:"5"})})]})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Product Details"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsx)(H.A.Item,{name:"sku",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(ee,{className:"mr-1"})," SKU (Optional)"]}),tooltip:"Stock Keeping Unit - unique identifier for inventory management",children:(0,i.jsx)(K.A,{placeholder:"Enter SKU",onChange:()=>f(!0)})}),(0,i.jsx)(H.A.Item,{name:"barcode",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(ee,{className:"mr-1"})," Barcode (Optional)"]}),tooltip:"Product barcode for scanning",children:(0,i.jsx)(K.A,{placeholder:"Enter barcode"})})]}),(0,i.jsx)(H.A.Item,{name:"expiryDate",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(et.A,{className:"mr-1"})," Expiry Date (Optional)"]}),tooltip:"Date when the product expires",children:(0,i.jsx)(Y.A,{style:{width:"100%"}})})]})]})]})})};var e6=a(12869);let e5=e=>{let{data:t,error:a,isLoading:i,refetch:n}=(0,v.hi)(e||0,{skip:!e});return{product:t?.data||null,isLoading:i,error:a,refetch:n}},e7=({isOpen:e,onClose:t,productId:a,onEdit:n})=>{let{product:o,isLoading:l}=e5(a),c=e=>{if(!e)return"N/A";try{return ei()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},s=e=>{if(!e)return"N/A";try{return new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS"}).format(parseFloat(e))}catch(e){return"Invalid amount"}},x=(0,$.d4)(e=>e.auth.user),u="admin"===x?.role&&x?.id===o?.createdBy,v=(0,i.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,i.jsx)(r.Ay,{onClick:t,className:"text-white hover:text-white",style:{borderColor:"#6B7280",background:"transparent"},children:"Close"}),n&&o&&u&&(0,i.jsx)(r.Ay,{type:"primary",onClick:()=>n(o.id),children:"Edit"})]});return(0,i.jsx)(W.A,{isOpen:e,onClose:t,title:"Product Details",width:"500px",footer:v,children:l?(0,i.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,i.jsx)(p.A,{indicator:(0,i.jsx)(m.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):o?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,i.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-2"}),"Product: ",o.name]}),(0,i.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete product information and details"})]}),(0,i.jsxs)(e6.A,{bordered:!0,column:1,className:"product-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(d.A,{})," Product ID"]}),children:o.id}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(d.A,{})," Name"]}),children:o.name}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(ee,{})," SKU"]}),children:o.sku||"N/A"}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(ee,{})," Barcode"]}),children:o.barcode||"N/A"}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(P.A,{})," Price"]}),children:s(o.price)}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(P.A,{})," Cost"]}),children:s(o.cost)}),(0,i.jsx)(e6.A.Item,{label:"Stock Quantity",children:(0,i.jsx)(k.A,{color:o.stockQuantity<=0?"red":o.stockQuantity<=(o.minStockLevel||5)?"orange":"green",children:o.stockQuantity})}),(0,i.jsx)(e6.A.Item,{label:"Min Stock Level",children:o.minStockLevel||"N/A"}),(0,i.jsx)(e6.A.Item,{label:"Expiry Date",children:c(o.expiryDate)}),(0,i.jsx)(e6.A.Item,{label:(0,i.jsxs)("span",{children:[(0,i.jsx)(et.A,{})," Created At"]}),children:c(o.createdAt)})]})]}):(0,i.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,i.jsx)("p",{className:"text-gray-800",children:"No product data available"})})})};var e9=a(58733);a(86078);let te=({searchTerm:e,setSearchTerm:t,isMobile:a=!1})=>(0,i.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,i.jsx)(K.A,{placeholder:"Search by name, SKU, or barcode...",prefix:(0,i.jsx)(e9.A,{className:"text-gray-500"}),value:e,onChange:e=>{let a=e.target.value;console.log("Product search input changed:",a),t(a)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:a?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,i.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,i.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var tt=a(51531),ta=a(58493),ti=a(11104);let tn=e=>{let[t,{isLoading:a}]=(0,ti.Wn)(),{refetch:i}=(0,v.r3)({page:1,limit:10},{skip:!1});return{createStockAdjustment:async e=>{try{console.log("useStockAdjustmentCreate - Starting stock adjustment creation with data:",e);let a=await t(e).unwrap();if(console.log("useStockAdjustmentCreate - API response:",a),!a.success)throw console.error("useStockAdjustmentCreate - API returned error:",a.message),Error(a.message||"Failed to create stock adjustment");return(0,h.r)("success","Stock adjustment created successfully"),a.data}catch(e){throw console.error("Create stock adjustment error:",e),(0,h.r)("error",e.message||"Failed to create stock adjustment"),e}},isSubmitting:a}};var to=a(37287);let tl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"};var tr=n.forwardRef(function(e,t){return n.createElement(S.A,(0,N.A)({},e,{ref:t,icon:tl}))});let{TextArea:tc}=K.A,tp=({isOpen:e,onClose:t,onSuccess:a,product:o})=>{let[l]=H.A.useForm(),{createStockAdjustment:c,isSubmitting:p}=tn(a),{data:s,refetch:m}=(0,v.hi)(o?.id||0,{skip:!o?.id||!e}),x=s?.data||o;(0,n.useEffect)(()=>{e&&o&&(o.id&&m(),l.resetFields(),l.setFieldsValue({adjustmentType:"add",quantityChange:1,reason:""}))},[l,e,o,m]);let u=async e=>{if(x)try{let t="add"===e.adjustmentType?Math.abs(e.quantityChange):-Math.abs(e.quantityChange),i={productId:x.id,quantityChange:t,reason:e.reason};console.log("Submitting stock adjustment:",i);let n=await c(i);console.log("Stock adjustment result:",n),m(),setTimeout(()=>{console.log("Delayed product refetch after stock adjustment"),m()},500),a&&(console.log("Calling onSuccess to refresh the product table"),a())}catch(e){console.error("Failed to create stock adjustment:",e),alert("Failed to adjust stock. Please try again.")}},f=x?`Adjust Stock: ${x.name}`:"Adjust Stock",g=(0,i.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,i.jsx)(r.Ay,{onClick:t,disabled:p,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,i.jsx)(r.Ay,{type:"primary",loading:p,onClick:()=>l.submit(),children:"Adjust Stock"})]});return(0,i.jsx)(W.A,{isOpen:e,onClose:t,title:f,width:"500px",footer:g,children:(0,i.jsxs)("div",{className:"p-6 bg-white",children:[(0,i.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,i.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,i.jsx)(d.A,{className:"mr-2"}),"Stock Adjustment"]}),(0,i.jsxs)("p",{className:"text-gray-600 mt-1",children:["Adjust the stock quantity for ",x?.name]})]}),(0,i.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,i.jsxs)(H.A,{form:l,layout:"vertical",onFinish:u,className:"product-form",requiredMark:!0,children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Current Stock Information"}),(0,i.jsxs)("div",{className:"bg-gray-100 p-3 rounded mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Current Stock:"}),(0,i.jsx)("span",{className:"text-gray-800 font-medium",children:x?.stockQuantity||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center mt-1",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Minimum Stock Level:"}),(0,i.jsx)("span",{className:"text-gray-800 font-medium",children:x?.minStockLevel||0})]})]})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Adjustment Details"}),(0,i.jsx)(H.A.Item,{name:"adjustmentType",label:(0,i.jsx)("span",{className:"flex items-center text-gray-800",children:"Adjustment Type"}),rules:[{required:!0,message:"Please select adjustment type"}],initialValue:"add",children:(0,i.jsxs)(ta.Ay.Group,{children:[(0,i.jsx)(ta.Ay,{value:"add",className:"text-gray-800",children:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(to.A,{className:"mr-1 text-green-500"})," Add Stock"]})}),(0,i.jsx)(ta.Ay,{value:"remove",className:"text-gray-800",children:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(tr,{className:"mr-1 text-red-500"})," Remove Stock"]})})]})}),(0,i.jsx)(H.A.Item,{name:"quantityChange",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(E.A,{className:"mr-1"})," Quantity"]}),rules:[{required:!0,message:"Please enter quantity"},{type:"number",min:1,message:"Quantity must be at least 1"}],initialValue:1,tooltip:"The quantity to add or remove",children:(0,i.jsx)(Q.A,{min:1,style:{width:"100%"},placeholder:"Enter quantity"})}),(0,i.jsx)(H.A.Item,{name:"reason",label:(0,i.jsxs)("span",{className:"flex items-center",children:[(0,i.jsx)(E.A,{className:"mr-1"})," Reason"]}),rules:[{required:!0,message:"Please enter reason for adjustment"}],tooltip:"Why you are adjusting the stock",children:(0,i.jsx)(tc,{rows:4,placeholder:"Enter reason for adjustment"})})]})]})]})})},ts=()=>{let{user:e}=(0,x.A)(),t=(0,u.a)(),[a,o]=(0,n.useState)(!1),[v,f]=(0,n.useState)(!1),[h,j]=(0,n.useState)(!1),[w,k]=(0,n.useState)(!1),[A,N]=(0,n.useState)(null),[z,S]=(0,n.useState)(null),[C,D]=(0,n.useState)(!1),[P,F]=(0,n.useState)(0),{products:E,total:O,page:q,limit:I,isLoading:M,refetch:T,forceRefresh:R,searchTerm:$,setSearchTerm:U,handlePageChange:_}=g(),{deleteProduct:V,isDeleting:H}=b(()=>{D(!1),R()}),{bulkDeleteProducts:G,isDeleting:K}=y(()=>{Y(!1),R()}),[Q,Y]=(0,n.useState)(!1),[W,J]=(0,n.useState)([]),X=e?.role==="admin",Z=async()=>{z&&await V(z)},ee=async()=>{if(console.log("confirmBulkDelete called with products:",W),W.length>0)try{await G(W)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,n.useEffect)(()=>{P>0&&(console.log(`Refreshing products due to counter change: ${P}`),T())},[P,T]),(0,i.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,i.jsx)(l.A,{title:(0,i.jsx)("span",{className:"text-gray-800",children:"Product Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(r.Ay,{icon:(0,i.jsx)(s.A,{}),onClick:()=>{c.Ay.info({message:"Refreshing Products",description:"Getting the latest product data...",icon:(0,i.jsx)(s.A,{style:{color:"#108ee9"}}),duration:2}),R()},size:t?"small":"middle",children:t?"":"Refresh"}),X&&(0,i.jsx)(r.Ay,{type:"primary",icon:(0,i.jsx)(d.A,{}),onClick:()=>{o(!0)},size:t?"small":"middle",children:t?"":"Add Product"})]}),children:(0,i.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,i.jsx)(te,{searchTerm:$,setSearchTerm:U,isMobile:t}),M?(0,i.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,i.jsx)(p.A,{indicator:(0,i.jsx)(m.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,i.jsxs)(i.Fragment,{children:[E.length>0?(0,i.jsx)(L,{products:E,loading:!1,onView:e=>{S(e),j(!0)},onEdit:e=>{N(e),f(!0)},onDelete:e=>{S(e),D(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with productIds:",e),J(e),Y(!0)},onAdjustStock:e=>{N(e),k(!0)},isMobile:t}):(0,i.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:$?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"No products found matching your search criteria."}),(0,i.jsx)(r.Ay,{type:"primary",onClick:()=>U(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,i.jsxs)("p",{children:["No products found. ",X&&"Click 'Add Product' to create one."]})}),E.length>0&&(0,i.jsx)(B,{current:q,pageSize:I,total:O,onChange:_,isMobile:t})]})]})}),(0,i.jsx)(e3,{isOpen:a,onClose:()=>o(!1),onSuccess:()=>{o(!1),c.Ay.success({message:"Product Added Successfully",description:"Refreshing product data...",icon:(0,i.jsx)(s.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Product creation completed, refreshing product data"),R()},currentUser:e}),(0,i.jsx)(e3,{isOpen:v,onClose:()=>f(!1),onSuccess:()=>{f(!1),c.Ay.success({message:"Product Updated Successfully",description:"Refreshing product data...",icon:(0,i.jsx)(s.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Product update completed, refreshing product data"),R()},product:A,currentUser:e}),(0,i.jsx)(e7,{isOpen:h,onClose:()=>{j(!1),S(null)},productId:z,onEdit:e=>{let t=E.find(t=>t.id===e)||null;t&&(N(t),j(!1),f(!0))}}),(0,i.jsx)(tt.A,{isOpen:C,onClose:()=>{D(!1),S(null)},onConfirm:Z,title:"Delete Product",message:"Are you sure you want to delete this product? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:H,type:"danger"}),(0,i.jsx)(tt.A,{isOpen:Q,onClose:()=>{Y(!1),J([])},onConfirm:ee,title:"Delete Multiple Products",message:`Are you sure you want to delete ${W.length} products? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:K,type:"danger"}),(0,i.jsx)(tp,{isOpen:w,onClose:()=>k(!1),onSuccess:()=>{k(!1),c.Ay.success({message:"Stock Adjustment Successful",description:"Refreshing product data...",icon:(0,i.jsx)(s.A,{style:{color:"#108ee9"}}),duration:2}),console.log("Stock adjustment completed, refreshing product data"),R()},product:A})]})}},78337:(e,t,a)=>{"use strict";a.d(t,{d:()=>n});var i=a(58009);function n(e,t){let[a,n]=(0,i.useState)(e);return a}},66785:(e,t,a)=>{"use strict";a.d(t,{fS:()=>o,iG:()=>r,jY:()=>l});let i="dutmiedgk",n="pos_receipts",o=async e=>{try{let t=document.createElement("div");t.innerHTML=e,t.style.width="350px",t.style.padding="20px",t.style.backgroundColor="white",t.style.color="#555555",t.style.fontFamily="Arial, sans-serif",t.style.position="absolute",t.style.left="-9999px",t.style.borderLeft="1px solid #000000",t.style.borderRight="1px solid #000000",t.style.borderTop="1px solid #000000",t.style.borderBottom="1px solid #000000",t.style.boxSizing="border-box",t.style.fontSize="14px",t.style.lineHeight="1.5",document.body.appendChild(t);let o=(await a.e(2835).then(a.bind(a,2835))).default,l=await o(t,{scale:3,backgroundColor:"white",logging:!1,width:350,height:t.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(t);let r=await new Promise(e=>{l.toBlob(t=>{e(t)},"image/png",.95)}),c=new FormData;c.append("file",r),c.append("upload_preset",n);let p=await fetch(`https://api.cloudinary.com/v1_1/${i}/image/upload`,{method:"POST",body:c}),s=await p.json();if(p.ok)return s.secure_url;throw console.error("Cloudinary upload failed:",s),Error(s.error?.message||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},l=(e,t)=>{let a=new Date(e.transactionDate),i=a.toLocaleDateString(),n=a.toLocaleTimeString(),o=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return`
  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">

  <!-- Header and Title -->
  <div style="text-align: center; margin-bottom: 10px;">
    <div style="font-size: 18px; font-weight: bold;">${t.name||"POS System"}</div>
    <div style="font-size: 16px; font-weight: bold;">#INV-${e.id}-${new Date().getFullYear()}${(new Date().getMonth()+1).toString().padStart(2,"0")}${new Date().getDate().toString().padStart(2,"0")}</div>
    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">
      ${t.address?`<div>${t.address}</div>`:""}
      ${t.city?`<div>${t.city}</div>`:""}
      ${t.country?`<div>${t.country}</div>`:""}
    </div>
  </div>

  <!-- Items Table -->
  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">
    <thead>
      <tr>
        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>
      </tr>
    </thead>
    <tbody>
      ${e.items.map((e,t)=>{let a="string"==typeof e.price?parseFloat(e.price):e.price,i=a*e.quantity;return`
          <tr>
            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">${t+1}. ${e.productName}</td>
            <td style="text-align: right;">${e.quantity}</td>
            <td style="text-align: right;">${a.toFixed(2)}</td>
            <td style="text-align: right;">${i.toFixed(2)}</td>
          </tr>
        `}).join("")}
    </tbody>
  </table>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Totals -->
  <div style="display: flex; justify-content: space-between; font-weight: bold;">
    <div>TOTAL</div>
    <div>GHS ${"string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2)}</div>
  </div>
  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">
    <div>TAX</div>
    <div>0.00</div>
  </div>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Payment Info -->
  <div style="font-size: 11px; margin-bottom: 6px;">
    <div>Payment: ${o.toUpperCase()}</div>
    ${o.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":""}
  </div>

  <!-- Date/Time -->
  <div style="display: flex; justify-content: space-between; font-size: 11px;">
    <div><strong>Time:</strong> ${n}</div>
    <div><strong>Date:</strong> ${i}</div>
  </div>

  <!-- Barcode -->
  <div style="text-align: center; margin: 12px 0;">
    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>
    <div style="font-size: 11px; margin-top: 4px;">${e.id}</div>
  </div>

  <!-- Footer -->
  <div style="text-align: center; font-size: 12px; margin-top: 8px;">
    THANK YOU!
  </div>
</div>

`},r=async e=>{try{let t=new FormData;t.append("file",e),t.append("upload_preset",n),t.append("folder","products");let a=await fetch(`https://api.cloudinary.com/v1_1/${i}/image/upload`,{method:"POST",body:t}),o=await a.json();if(a.ok)return o.secure_url;throw console.error("Cloudinary upload failed:",o),Error(o.error?.message||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},77961:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx","default")},12306:()=>{},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,1785,3950,8472,5735,8493,5482,106,4286,6165],()=>a(68718));module.exports=i})();