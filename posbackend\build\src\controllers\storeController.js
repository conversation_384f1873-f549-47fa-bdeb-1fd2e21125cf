"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleStoreRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const storeService_1 = require("../services/storeService");
const modeValidator_1 = require("../utils/modeValidator");
/**
 * Handle Store Requests
 * This controller handles all store-related operations
 */
const handleStoreRequest = async (req, res) => {
    const { mode, storeId, page, limit, ...storeData } = req.body;
    const requester = req.user;
    // Validate mode
    const validModes = ["createnew", "retrieve", "update", "delete"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            /**
             * Create a new store
             * Required fields: name
             */
            case "createnew": {
                // Validate required fields
                if (!storeData.name) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Store name is required.");
                }
                // Create store
                const store = await (0, storeService_1.createStore)(requester, storeData);
                return (0, responseHelper_1.sendResponse)(res, 201, true, "Store created successfully.", store);
            }
            /**
             * Retrieve stores (single or all)
             * If storeId is provided, retrieve a single store
             * Otherwise, retrieve all stores with pagination
             */
            case "retrieve": {
                if (storeId) {
                    // Retrieve a single store
                    const store = await (0, storeService_1.getStoreById)(requester, Number(storeId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Store retrieved successfully.", store);
                }
                else {
                    // Retrieve all stores with pagination
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    const stores = await (0, storeService_1.getAllStores)(requester, pageNum, limitNum);
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Stores retrieved successfully.", stores);
                }
            }
            /**
             * Update a store
             * Required fields: storeId
             */
            case "update": {
                if (!storeId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Store ID is required for update.");
                }
                // Update store
                const updatedStore = await (0, storeService_1.updateStore)(requester, Number(storeId), storeData);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Store updated successfully.", updatedStore);
            }
            /**
             * Delete stores (Single or Multiple)
             * Required fields: storeId or storeIds
             */
            case "delete": {
                // Check if we have storeIds (array) or storeId (single)
                const { storeIds } = req.body;
                if (!storeId && !storeIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Store ID(s) are required for deletion.");
                }
                // Use storeIds if provided, otherwise use single storeId
                const idsToDelete = storeIds
                    ? (Array.isArray(storeIds) ? storeIds : [storeIds])
                    : [Number(storeId)];
                const result = await (0, storeService_1.deleteStore)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} stores deleted successfully.`
                    : "Store deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid mode.");
        }
    }
    catch (error) {
        console.error("Store request error:", error);
        return (0, responseHelper_1.sendResponse)(res, error.statusCode || 500, false, error.message || "Internal Server Error");
    }
};
exports.handleStoreRequest = handleStoreRequest;
