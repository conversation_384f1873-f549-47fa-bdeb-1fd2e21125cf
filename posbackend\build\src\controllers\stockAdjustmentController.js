"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleStockAdjustmentRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const stockAdjustmentService_1 = require("../services/stockAdjustmentService");
const modeValidator_1 = require("../utils/modeValidator");
const handleStockAdjustmentRequest = async (req, res) => {
    const { mode, adjustmentId, page, limit, ...data } = req.body;
    const requester = req.user; // ✅ Extract requester from middleware
    // ✅ Validate mode
    const validModes = ["createnew", "update", "delete", "retrieve"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            // ✅ Create New Stock Adjustment
            case "createnew": {
                if (!data.productId || !data.quantityChange || !data.reason) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "All stock adjustment details are required.");
                }
                console.log(`Processing stock adjustment: Product ID ${data.productId}, Change: ${data.quantityChange}, Reason: ${data.reason}`);
                try {
                    const newAdjustment = await (0, stockAdjustmentService_1.createStockAdjustment)(requester, {
                        productId: data.productId,
                        quantityChange: data.quantityChange,
                        reason: data.reason,
                    });
                    console.log(`Stock adjustment successful: ${JSON.stringify(newAdjustment)}`);
                    return (0, responseHelper_1.sendResponse)(res, 201, true, "Stock adjustment created successfully.", newAdjustment);
                }
                catch (error) {
                    console.error(`Stock adjustment failed: ${error.message}`);
                    return (0, responseHelper_1.sendResponse)(res, 500, false, `Stock adjustment failed: ${error.message}`);
                }
            }
            // ✅ Retrieve Stock Adjustments (Single or All)
            case "retrieve": {
                if (adjustmentId) {
                    // Fetch a single stock adjustment by ID
                    const adjustment = await (0, stockAdjustmentService_1.getStockAdjustmentById)(requester, Number(adjustmentId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Stock adjustment retrieved successfully.", adjustment);
                }
                else {
                    // Fetch all stock adjustments with pagination
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    const adjustments = await (0, stockAdjustmentService_1.getAllStockAdjustments)(requester, pageNum, limitNum);
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Stock adjustments retrieved successfully.", adjustments);
                }
            }
            // ✅ Update Stock Adjustment
            case "update": {
                if (!adjustmentId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Stock adjustment ID is required for updating.");
                }
                const updatedAdjustment = await (0, stockAdjustmentService_1.updateStockAdjustmentById)(requester, Number(adjustmentId), data);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Stock adjustment updated successfully.", updatedAdjustment);
            }
            // ✅ Delete Stock Adjustment
            case "delete": {
                if (!adjustmentId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Stock adjustment ID is required for deletion.");
                }
                const result = await (0, stockAdjustmentService_1.deleteStockAdjustmentById)(requester, Number(adjustmentId));
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Stock adjustment deleted successfully.", result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handleStockAdjustmentRequest = handleStockAdjustmentRequest;
