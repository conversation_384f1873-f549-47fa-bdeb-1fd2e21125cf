(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1518],{30522:(e,t,a)=>{Promise.resolve().then(a.bind(a,56705))},56705:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var s=a(95155),r=a(12115),l=a(51554),n=a(21614),d=a(27114),i=a(71349),o=a(43316),c=a(22810),m=a(2796),x=a(89801),u=a(72093),h=a(62704),p=a(53452),y=a(97139),f=a(87181),g=a(16419),b=a(36060),j=a(30555),v=a(7165),N=a(21633),A=a(93968),w=a(21455),P=a.n(w),D=a(10142),S=a(42531);let{RangePicker:M}=l.A,{Option:Y}=n.A,C=()=>{var e,t;let{user:a}=(0,b.A)(),l=(0,j.a)(),[w,C]=(0,r.useState)([P()().subtract(30,"days"),P()()]),[R,F]=(0,r.useState)("summary"),[O,T]=(0,r.useState)(!0),{data:_,isLoading:k}=(0,v.P)(),{data:E,isLoading:B,refetch:I}=(0,N.JZ)({page:1,limit:1e3,search:""}),{data:U,isLoading:L}=(0,A.r3)({page:1,limit:1e3,search:""}),z=k||B||L,q=(0,r.useMemo)(()=>{var e,t;let a=(null==E?void 0:null===(e=E.data)||void 0===e?void 0:e.sales)||[],s=(null==U?void 0:null===(t=U.data)||void 0===t?void 0:t.products)||[];null==_||_.data;let r=w?a.filter(e=>{let t=P()(e.transactionDate);return t.isAfter(w[0].startOf("day"))&&t.isBefore(w[1].endOf("day"))}):a,l=r.reduce((e,t)=>e+parseFloat(t.totalAmount),0),n=r.length,d={};r.forEach(e=>{e.items&&e.items.length>0&&e.items.forEach(e=>{let t=e.productId.toString();d[t]||(d[t]={quantity:0,revenue:0,name:e.productName||"Product ".concat(e.productId)}),d[t].quantity+=e.quantity,d[t].revenue+=parseFloat(e.price)*e.quantity})});let i=Object.entries(d).sort((e,t)=>{let[,a]=e,[,s]=t;return s.quantity-a.quantity})[0],o="No sales data";i?o=i[1].name:s.length>0&&(o="".concat(s[0].name," (No sales yet)"));let c={};r.forEach(e=>{let t=e.createdBy.toString(),a=e.createdByName||"User ".concat(e.createdBy);c[t]||(c[t]={transactions:0,revenue:0,name:a}),c[t].transactions+=1,c[t].revenue+=parseFloat(e.totalAmount)});let m={};return r.forEach(e=>{let t=e.paymentMethod;m[t]||(m[t]={count:0,revenue:0}),m[t].count+=1,m[t].revenue+=parseFloat(e.totalAmount)}),{totalSales:l,totalTransactions:n,averageOrderValue:n>0?l/n:0,topSellingProduct:o,productPerformance:Object.entries(d).map(e=>{let[t,a]=e;return{productId:parseInt(t),productName:a.name,quantity:a.quantity,revenue:a.revenue}}).sort((e,t)=>t.revenue-e.revenue).slice(0,10),customerAnalysis:Object.entries(c).map(e=>{let[t,a]=e;return{cashierId:parseInt(t),cashierName:a.name,transactions:a.transactions,revenue:a.revenue,averageOrderValue:a.transactions>0?a.revenue/a.transactions:0}}).sort((e,t)=>t.revenue-e.revenue),paymentMethods:Object.entries(m).map(e=>{let[t,a]=e;return{method:t,count:a.count,revenue:a.revenue,percentage:n>0?a.count/n*100:0}}).sort((e,t)=>t.revenue-e.revenue)}},[E,U,w,_,O,R]),V=((null==E?void 0:null===(e=E.data)||void 0===e?void 0:e.sales)||[]).filter(e=>{if(!w)return!0;let t=P()(e.transactionDate);return t.isAfter(w[0].startOf("day"))&&t.isBefore(w[1].endOf("day"))}),H=V.reduce((e,t)=>e+parseFloat(t.totalAmount),0);return(0,s.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,s.jsx)(i.A,{title:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"text-blue-600"}),(0,s.jsx)("span",{className:"text-gray-800",children:"Sales Reports"})]}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:l?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,s.jsxs)("div",{className:l?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,s.jsx)(o.Ay,{type:"primary",icon:(0,s.jsx)(p.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){d.Ay.error("Please allow popups to print sales report");return}let t='\n      <html>\n        <head>\n          <title>Sales Report</title>\n          <style>\n            body { font-family: Arial, sans-serif; }\n            table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n            th { background-color: #f5f5f5; }\n            .header { text-align: center; margin-bottom: 20px; }\n            .date { color: #666; font-size: 0.9em; }\n            @media print { .no-print { display: none; } }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h2>Sales Report</h2>\n            <p class="date">Generated on: '.concat(P()().format("MMMM D, YYYY h:mm A"),"</p>\n          </div>\n          <table>\n            <thead>\n              <tr>\n                <th>Date</th>\n                <th>Month</th>\n                <th>Amount</th>\n                <th>Payment Method</th>\n                <th>Cashier</th>\n              </tr>\n            </thead>\n            <tbody>\n              ").concat(V.map(e=>"\n                <tr>\n                  <td>".concat(P()(e.transactionDate).format("YYYY-MM-DD HH:mm"),"</td>\n                  <td>").concat(P()(e.transactionDate).format("MMMM YYYY"),"</td>\n                  <td>₵").concat(parseFloat(e.totalAmount).toFixed(2),"</td>\n                  <td>").concat(e.paymentMethod.replace("_"," ").toUpperCase(),"</td>\n                  <td>").concat(e.createdByName||"User ".concat(e.createdBy),"</td>\n                </tr>\n              ")).join(""),'\n              <tr>\n                <td colspan="2" style="font-weight:bold; text-align:right;">Total</td>\n                <td style="font-weight:bold;">₵').concat(H.toFixed(2),'</td>\n                <td colspan="2"></td>\n              </tr>\n            </tbody>\n          </table>\n          <div class="no-print" style="margin-top: 20px; text-align: center;">\n            <button onclick="window.print()">Print Report</button>\n          </div>\n        </body>\n      </html>\n    ');e.document.write(t),e.document.close()},size:l?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:l?"":"Print"}),(0,s.jsx)(o.Ay,{type:"primary",icon:(0,s.jsx)(y.A,{}),onClick:()=>{let e=new D.uE;e.setFontSize(16),e.text("Sales Report",14,15),e.setFontSize(10),e.text("Generated on: ".concat(P()().format("MMMM D, YYYY h:mm A")),14,22);let t=V.map(e=>[P()(e.transactionDate).format("YYYY-MM-DD HH:mm"),P()(e.transactionDate).format("MMMM YYYY"),"₵".concat(parseFloat(e.totalAmount).toFixed(2)),e.paymentMethod.replace("_"," ").toUpperCase(),e.createdByName||"User ".concat(e.createdBy)]);t.push(["Total","","₵".concat(H.toFixed(2)),"",""]),(0,S.Ay)(e,{head:[["Date","Month","Amount","Payment Method","Cashier"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("sales-report.pdf")},size:l?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:l?"":"Export"})]}),children:(0,s.jsxs)("div",{className:"w-full space-y-6",children:[(0,s.jsx)(i.A,{title:"Report Filters",size:"small",className:"bg-gray-50",children:(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(m.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Date Range"}),(0,s.jsx)(M,{value:w,onChange:e=>{e&&e[0]&&e[1]?C([e[0],e[1]]):C(null)},className:"w-full",format:"YYYY-MM-DD",suffixIcon:(0,s.jsx)(f.A,{}),getPopupContainer:e=>e.parentElement||document.body,style:{zIndex:1e3}})]})}),(0,s.jsx)(m.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Report Type"}),(0,s.jsxs)(n.A,{value:R,onChange:F,className:"w-full",children:[(0,s.jsx)(Y,{value:"summary",children:"Sales Summary"}),(0,s.jsx)(Y,{value:"detailed",children:"Detailed Sales"}),(0,s.jsx)(Y,{value:"products",children:"Product Performance"}),(0,s.jsx)(Y,{value:"customers",children:"Customer Analysis"})]})]})}),(0,s.jsx)(m.A,{xs:24,sm:24,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xa0"}),(0,s.jsx)(o.Ay,{type:"primary",onClick:()=>{T(!0),I(),d.Ay.success("Report generated successfully!")},loading:z,className:"w-full bg-blue-600 hover:bg-blue-700",icon:(0,s.jsx)(h.A,{}),children:"Generate Report"})]})})]})}),(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(i.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(x.A,{title:"Total Sales",value:q.totalSales,precision:2,prefix:"₵",valueStyle:{color:"#3f8600",fontSize:"18px"},className:"w-full"})})}),(0,s.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(i.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(x.A,{title:"Transactions",value:q.totalTransactions,valueStyle:{color:"#1890ff",fontSize:"18px"},className:"w-full"})})}),(0,s.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(i.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(x.A,{title:"Avg. Order Value",value:q.averageOrderValue,precision:2,prefix:"₵",valueStyle:{color:"#722ed1",fontSize:"18px"},className:"w-full"})})}),(0,s.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(i.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Top Product"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-gray-800 break-words max-w-full",style:{wordBreak:"break-word",whiteSpace:"normal"},title:q.topSellingProduct,children:q.topSellingProduct})]})})})]}),(0,s.jsx)(i.A,{title:"Report Data",className:"min-h-96",children:z?(0,s.jsx)("div",{className:"flex h-60 items-center justify-center",children:(0,s.jsx)(u.A,{indicator:(0,s.jsx)(g.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):O?(0,s.jsxs)("div",{className:"space-y-6",children:["summary"===R&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Sales Summary"}),(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Metric"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Value"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Description"})]})}),(0,s.jsxs)("tbody",{children:[(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Total Revenue"}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",q.totalSales.toFixed(2)]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Total sales revenue for selected period"})]}),(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Total Transactions"}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:q.totalTransactions}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Number of completed sales"})]}),(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Average Order Value"}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",q.averageOrderValue.toFixed(2)]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Average revenue per transaction"})]}),(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Top Selling Product"}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:q.topSellingProduct}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Best performing product by quantity"})]})]})]})})})]}),"products"===R&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Product Performance"}),q.productPerformance&&q.productPerformance.length>0?(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[500px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Rank"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Product Name"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Quantity Sold"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"})]})}),(0,s.jsx)("tbody",{children:q.productPerformance.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:t+1}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.productName}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.quantity}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]})]},t))})]})})}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,s.jsx)("p",{children:"No product performance data available."}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"This could be because there are no sales with product details in the selected period."})]})]}),"customers"===R&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Cashier Performance"}),q.customerAnalysis&&q.customerAnalysis.length>0?(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Rank"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Cashier Name"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Transactions"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Avg. Order Value"})]})}),(0,s.jsx)("tbody",{children:q.customerAnalysis.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:t+1}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.cashierName}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.transactions}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.averageOrderValue.toFixed(2)]})]},t))})]})})}):(0,s.jsx)("div",{className:"text-center text-gray-500 py-10",children:(0,s.jsx)("p",{children:"No cashier performance data available."})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Payment Methods Analysis"}),q.paymentMethods&&q.paymentMethods.length>0?(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[500px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Payment Method"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Transactions"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Percentage"})]})}),(0,s.jsx)("tbody",{children:q.paymentMethods.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.method.replace("_"," ").toUpperCase()}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.count}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-green-600",children:[e.percentage.toFixed(1),"%"]})]},t))})]})})}):(0,s.jsx)("div",{className:"text-center text-gray-500 py-10",children:(0,s.jsx)("p",{children:"No payment method data available."})})]})]}),"detailed"===R&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Detailed Sales"}),(null==E?void 0:null===(t=E.data)||void 0===t?void 0:t.sales)&&E.data.sales.length>0?(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Date"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Month"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Amount"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Payment Method"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Cashier"})]})}),(0,s.jsx)("tbody",{children:E.data.sales.filter(e=>{if(!w)return!0;let t=P()(e.transactionDate);return t.isAfter(w[0].startOf("day"))&&t.isBefore(w[1].endOf("day"))}).map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:P()(e.transactionDate).format("YYYY-MM-DD HH:mm")}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:P()(e.transactionDate).format("MMMM YYYY")}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",parseFloat(e.totalAmount).toFixed(2)]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.paymentMethod.replace("_"," ").toUpperCase()}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.createdByName||"User ".concat(e.createdBy)})]},e.id))})]})}),(0,s.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t text-sm text-gray-600",children:["Showing ",E.data.sales.filter(e=>{if(!w)return!0;let t=P()(e.transactionDate);return t.isAfter(w[0].startOf("day"))&&t.isBefore(w[1].endOf("day"))}).length," sales transactions"]})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,s.jsx)("p",{children:"No sales data available for the selected period."}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Try adjusting the date range or check if there are any sales in the system."})]})]})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-20",children:[(0,s.jsx)(h.A,{className:"text-4xl mb-4"}),(0,s.jsx)("p",{children:'Select filters and click "Generate Report" to view sales data'}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Charts and detailed analytics will appear here"})]})}),(0,s.jsx)(i.A,{className:"bg-blue-50 border-blue-200",children:(0,s.jsxs)("div",{className:"text-center text-blue-700",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCCA Advanced Analytics Coming Soon"}),(0,s.jsx)("p",{className:"text-sm",children:"Interactive charts, trend analysis, and detailed breakdowns will be available in future updates."})]})})]})})})}},30555:(e,t,a)=>{"use strict";a.d(t,{a:()=>r});var s=a(12115);function r(){let[e,t]=(0,s.useState)();return(0,s.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),a=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",a),()=>e.removeEventListener("change",a)},[]),!!e}},36060:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var s=a(83391),r=a(70854),l=a(63065),n=a(7875);let d=()=>{let e=(0,s.wA)(),{user:t,accessToken:a}=(0,s.d4)(e=>e.auth),d=(0,r._)(),{refetch:i}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let o=!!t&&!!a,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let s=await i();console.log("useAuth - Refetch result:",s);let r=s.data;if((null==r?void 0:r.success)&&(null==r?void 0:r.data)){console.log("useAuth - API response data:",r.data);let s=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=r.data.phone||t.phone||"",d=r.data.createdAt||t.createdAt||"",i=r.data.lastPaymentDate||t.lastPaymentDate||void 0,o=r.data.nextPaymentDue||t.nextPaymentDue||null,c=r.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:r.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:r.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:d,apiLastPaymentDate:r.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:r.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:o,apiCreatedBy:r.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let m={...r.data,phone:l,createdAt:d,lastPaymentDate:i,nextPaymentDue:o,createdBy:c,paymentStatus:s};console.log("useAuth - Updating Redux store with:",m),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:m,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==r?void 0:r.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:o,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&d.needsPayment,paymentStatus:d,refreshUser:c}}},70854:(e,t,a)=>{"use strict";a.d(t,{_:()=>d});var s=a(12115),r=a(83391),l=a(21455),n=a.n(l);let d=()=>{let e=(0,r.d4)(e=>e.auth.user),[t,a]=(0,s.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,s.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,s=!1,r=!0,l="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){s=!0,r=!1,l="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),d=n()();if(t=l.diff(d,"day"),a){let a=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:s,needsPayment:r})}}}else"pending"===e.paymentStatus?(s=!1,r=!0,l="pending"):"overdue"===e.paymentStatus?(s=!1,r=!0,l="overdue"):(s=!1,r=!0,l="inactive");a({isActive:s,daysRemaining:t,status:l,needsPayment:r})},[e]),t}}},e=>{var t=t=>e(e.s=t);e.O(0,[3930,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,2204,1349,4798,2375,1614,7114,1554,8642,7852,821,8441,1517,7358],()=>t(30522)),_N_E=e.O()}]);