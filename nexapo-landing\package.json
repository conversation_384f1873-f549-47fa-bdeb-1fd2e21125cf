{"name": "nexapo-landing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:production": "NODE_ENV=production next build", "start": "next start", "start:production": "NODE_ENV=production next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.16", "next": "14.0.0", "nodemailer": "^7.0.3", "postcss": "^8.4.31", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.4", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.0", "typescript": "^5"}}