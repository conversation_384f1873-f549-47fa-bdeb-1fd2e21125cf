# Fix: User Deletion Foreign Key Constraint Error

## Problem Description

When attempting to delete an admin user as a SuperAdmin, the following error occurred:

```
null value in column "created_by" of relation "suppliers" violates not-null constraint
```

## Root Cause

The issue was caused by contradictory database constraints in several tables:

1. **Suppliers Table**: `created_by` column was marked as `NOT NULL` but had a foreign key constraint with `ON DELETE SET NULL`
2. **User Suppliers Table**: Same issue with `created_by` column
3. **User Stores Table**: Same issue with `created_by` column

When a user was deleted, the database tried to set the `created_by` fields to `NULL` (due to the `ON DELETE SET NULL` constraint), but the `NOT NULL` constraint prevented this, causing the error.

## Solution Applied

### 1. Schema Changes

Modified the following tables in `posbackend/src/db/schema/schema.ts`:

#### Suppliers Table (Line 137)
```typescript
// BEFORE (Problematic)
createdBy: integer("created_by").notNull().references(() => users.id, { onDelete: "set null" })

// AFTER (Fixed)
createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" })
```

#### User Suppliers Table (Line 150)
```typescript
// BEFORE (Problematic)
createdBy: integer("created_by").notNull().references(() => users.id, { onDelete: "set null" })

// AFTER (Fixed)
createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" })
```

#### User Stores Table (Line 283)
```typescript
// BEFORE (Problematic)
createdBy: integer("created_by").notNull().references(() => users.id, { onDelete: "cascade" })

// AFTER (Fixed)
createdBy: integer("created_by").references(() => users.id, { onDelete: "set null" })
```

### 2. Database Migration

Created migration file: `posbackend/migrations/0008_fix_created_by_constraints.sql`

```sql
-- Remove NOT NULL constraints from created_by columns
ALTER TABLE suppliers 
ALTER COLUMN created_by DROP NOT NULL;

ALTER TABLE user_suppliers 
ALTER COLUMN created_by DROP NOT NULL;

-- Fix user_stores table constraint
ALTER TABLE user_stores 
DROP CONSTRAINT IF EXISTS user_stores_created_by_users_id_fk;

ALTER TABLE user_stores 
ALTER COLUMN created_by DROP NOT NULL;

ALTER TABLE user_stores 
ADD CONSTRAINT user_stores_created_by_users_id_fk 
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
```

### 3. Applied Changes

Executed the schema changes using:
```bash
cd posbackend && npm run db:push
```

## Verification

The following tables now have properly configured `created_by` columns that allow `NULL` values when referenced users are deleted:

✅ **suppliers** - `created_by` can be NULL  
✅ **user_suppliers** - `created_by` can be NULL  
✅ **user_stores** - `created_by` can be NULL  
✅ **stock_adjustments** - `created_by` already properly configured  
✅ **purchases** - `created_by` already properly configured  
✅ **receipts** - `created_by` already properly configured  
✅ **stores** - `created_by` already properly configured  

## Impact

- **SuperAdmin** can now successfully delete admin users without constraint violations
- **Data integrity** is maintained - related records are preserved with `created_by` set to `NULL`
- **Audit trail** is partially preserved - we know records exist but not who created them after user deletion
- **No data loss** - suppliers, user relationships, and stores remain intact

## Testing

After applying this fix, SuperAdmin should be able to:

1. ✅ Delete admin users successfully
2. ✅ See suppliers with `created_by = NULL` for deleted users
3. ✅ See user-supplier relationships preserved
4. ✅ See user-store relationships preserved
5. ✅ Continue normal operations without constraint errors

## Future Considerations

Consider implementing soft deletes for users if maintaining full audit trails is important:

```typescript
// Alternative approach for future consideration
deletedAt: timestamp("deleted_at"),
deletedBy: integer("deleted_by").references(() => users.id)
```

This would preserve the audit trail while still allowing "deletion" functionality.
