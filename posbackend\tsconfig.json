{
    "compilerOptions": {
      "target": "ES2020",
      "module": "commonjs",
      "rootDir": "./",
      "outDir": "./build",
      "esModuleInterop": true,
      "strict": true,
      "skipLibCheck": true,
      "resolveJsonModule": true,
      "allowJs": true,
      "typeRoots": [
        "node_modules/@types",
        "./src/types" // Ensure your custom types are included
      ],

    },
    "include": [
      "./index.ts",
      "./src/**/*.ts",
      "src/types/**/*.d.ts"
    ],
    "exclude": ["node_modules"]
  }
