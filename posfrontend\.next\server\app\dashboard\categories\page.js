(()=>{var e={};e.id=1038,e.ids=[1038],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},68390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>g,tree:()=>d});var s=r(70260),a=r(28203),l=r(25155),n=r.n(l),o=r(67292),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d=["",{children:["dashboard",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52465)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/categories/page",pathname:"/dashboard/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21351:(e,t,r)=>{Promise.resolve().then(r.bind(r,52465))},51615:(e,t,r)=>{Promise.resolve().then(r.bind(r,26573))},99730:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};var n=r(78480);let o=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:l}))})},26573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>J});var s=r(45512),a=r(58009),l=r(6987),n=r(21419),o=r(3117),i=r(88752),d=r(99730),c=r(60636),x=r(43087),g=r(78337);let h=(e=1,t=10)=>{let[r,s]=(0,a.useState)(e),[l,n]=(0,a.useState)(t),[o,i]=(0,a.useState)(""),d=(0,g.d)(o,500);(0,a.useEffect)(()=>{s(1)},[d]);let{data:c,error:h,isLoading:u,isFetching:m,refetch:p}=(0,x.lg)({page:r,limit:l,search:d},{refetchOnMountOrArgChange:!0});console.log("Category search state:",{searchTerm:o,debouncedSearchTerm:d,resultsCount:c?.data?.categories?.length||0,totalResults:c?.data?.total||0});let y=c?.data?.categories||[],f=c?.data?.total||0;return{categories:y,total:f,page:c?.data?.page||r,limit:c?.data?.perPage||l,isLoading:u||m,isFetching:m,error:h,refetch:p,searchTerm:o,setSearchTerm:i,handlePageChange:e=>{s(e)},handleLimitChange:e=>{n(e),s(1)}}};var u=r(49792);let m=e=>{let[t,{isLoading:r}]=(0,x.ec)();return{deleteCategory:async r=>{try{let s=await t(r).unwrap();if(!s.success)throw Error(s.message||"Failed to delete category");return(0,u.r)("success","Category deleted successfully"),e&&e(),s.data}catch(e){throw console.error("Delete category error:",e),(0,u.r)("error",e.message||"Failed to delete category"),e}},isDeleting:r}},p=e=>{let[t,{isLoading:r}]=(0,x.gH)();return{bulkDeleteCategories:async r=>{try{console.log("Bulk deleting categories with IDs:",r);let s=await t(r).unwrap();if(!s.success)throw Error(s.message||"Failed to delete categories");return(0,u.r)("success",`${r.length} categories deleted successfully`),e&&e(),s.data}catch(e){throw console.error("Bulk delete categories error:",e),(0,u.r)("error",e.message||"Failed to delete categories"),e}},isDeleting:r}};var y=r(48752),f=r(77067),b=r(70001),j=r(25421),w=r(25834),v=r(99261),A=r(86977),C=r(63844),N=r(73542),k=r(16589),P=r.n(k),S=r(92273);let E=({categories:e,loading:t,onView:r,onEdit:l,onDelete:n,onBulkDelete:i,isMobile:d=!1})=>{let c=(0,N.E)(),x=(0,S.d4)(e=>e.auth.user),g=x?.role,[h,u]=(0,a.useState)([]),[m,p]=(0,a.useState)(!1),k=t=>{let r=t.target.checked;p(r),r?u(e.filter(e=>z(e)).map(e=>e.id)):u([])},E=(e,t)=>{t?u(t=>[...t,e]):u(t=>t.filter(t=>t!==e))},D=e=>P()(e).format("MMM D, YYYY"),z=e=>"superadmin"===g||"admin"===g&&x?.id===e.createdBy;return(0,s.jsxs)("div",{className:"overflow-hidden bg-white",children:[h.length>0&&(0,s.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[h.length," ",1===h.length?"category":"categories"," selected"]}),(0,s.jsx)(o.Ay,{type:"primary",danger:!0,icon:(0,s.jsx)(j.A,{}),onClick:()=>{h.length>0&&i?(i(h),u([]),p(!1)):y.Ay.warning({message:"No categories selected",description:"Please select at least one category to delete."})},className:"ml-2",children:"Delete Selected"})]}),d||c?(0,s.jsxs)(C.jB,{columns:"50px 200px 120px 150px",minWidth:"700px",children:[(0,s.jsx)(C.A0,{className:"text-center",children:(0,s.jsx)(f.A,{checked:m,onChange:k,disabled:0===e.filter(e=>z(e)).length})}),(0,s.jsx)(C.A0,{children:"Name"}),(0,s.jsx)(C.A0,{children:"Created At"}),(0,s.jsx)(C.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,s.jsxs)(C.Hj,{selected:h.includes(e.id),children:[(0,s.jsx)(C.nA,{className:"text-center",children:z(e)&&(0,s.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>E(e.id,t.target.checked)})}),(0,s.jsx)(C.nA,{children:(0,s.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,s.jsx)(C.nA,{children:(0,s.jsx)("div",{className:"max-w-[110px] overflow-hidden text-ellipsis",children:D(e.createdAt)})}),(0,s.jsx)(C.nA,{className:"text-right",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,s.jsx)(b.A,{title:"View",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(w.A,{}),onClick:()=>r(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),z(e)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{title:"Edit",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(v.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,s.jsx)(b.A,{title:"Delete",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(A.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,s.jsx)(f.A,{checked:m,onChange:k,disabled:0===e.filter(e=>z(e)).length})}),(0,s.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Created At"}),(0,s.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("tr",{className:h.includes(e.id)?"bg-blue-50":"",children:[(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:z(e)&&(0,s.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>E(e.id,t.target.checked)})}),(0,s.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,s.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,s.jsx)("div",{className:"max-w-[200px] overflow-hidden text-ellipsis",children:e.description||"No description"})}),(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,s.jsx)("div",{className:"max-w-[130px] overflow-hidden text-ellipsis",children:D(e.createdAt)})}),(0,s.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,s.jsx)(b.A,{title:"View",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(w.A,{}),onClick:()=>r(e.id),type:"text",className:"view-button text-blue-600",size:"middle"})}),z(e)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{title:"Edit",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(v.A,{}),onClick:()=>l(e),type:"text",className:"edit-button",size:"middle"})}),(0,s.jsx)(b.A,{title:"Delete",children:(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(A.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]})]})})]},e.id))})]})})]})};var D=r(59022),z=r(60165);let _=({current:e,pageSize:t,total:r,onChange:a,isMobile:l=!1})=>{let n=Math.ceil(r/t);return(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,s.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,r)})," of"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:r})," results"]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,s.jsxs)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Previous"}),(0,s.jsx)(D.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(t,r)=>{let l=r+1;return(0,s.jsx)("button",{onClick:()=>a(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:l},l)}),(0,s.jsxs)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=n?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Next"}),(0,s.jsx)(z.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,s.jsx)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,s.jsx)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var T=r(37764),M=r(41257),F=r(98776);let R=e=>{let[t,{isLoading:r}]=(0,x.XY)();return{createCategory:async r=>{try{console.log("useCategoryCreate - Starting category creation with data:",r);let s=window.__REDUX_STATE?.auth;console.log("useCategoryCreate - Current auth state:",s?{hasUser:!!s.user,hasToken:!!s.accessToken,userRole:s.user?.role}:"Not available");let a=await t(r).unwrap();if(console.log("useCategoryCreate - API response:",a),!a.success)throw console.error("useCategoryCreate - API returned error:",a.message),Error(a.message||"Failed to create category");return(0,u.r)("success","Category created successfully"),e&&e(),a.data}catch(e){throw console.error("Create category error:",e),(0,u.r)("error",e.message||"Failed to create category"),e}},isCreating:r}},I=e=>{let[t,{isLoading:r}]=(0,x.ez)();return{updateCategory:async(r,s)=>{try{let a=await t({categoryId:r,data:s}).unwrap();if(!a.success)throw Error(a.message||"Failed to update category");return(0,u.r)("success","Category updated successfully"),e&&e(),a.data}catch(e){throw console.error("Update category error:",e),(0,u.r)("error",e.message||"Failed to update category"),e}},isUpdating:r}};r(16361);let{TextArea:L}=T.A,O=({isOpen:e,onClose:t,onSuccess:r,category:l,currentUser:n})=>{let[i]=M.A.useForm(),d=!!l,{createCategory:c,isCreating:x}=R(r),{updateCategory:g,isUpdating:h}=I(r),u=x||h;(0,a.useEffect)(()=>{console.log("CategoryFormPanel - isOpen changed:",e),e&&(console.log("CategoryFormPanel - Panel is open, category:",l),l?i.setFieldsValue({name:l.name,description:l.description||""}):i.resetFields())},[e,l,i]);let m=async()=>{try{let e=await i.validateFields();d&&l?await g(l.id,e):await c(e),t()}catch(e){console.error("Form submission error:",e)}},p=(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(o.Ay,{onClick:t,disabled:u,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,s.jsx)(o.Ay,{type:"primary",loading:u,onClick:m,children:d?"Update":"Create"})]});return(0,s.jsxs)(F.A,{isOpen:e,onClose:t,title:d?"Edit Category":"Add New Category",width:"450px",footer:p,children:[(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Category: ",l?.name]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Create New Category"]})}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:d?"Update category information using the form below":"Fill in the details to create a new category"})]}),(0,s.jsx)(M.A,{form:i,layout:"vertical",className:"text-gray-800 w-full",style:{color:"#333"},children:(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Category Information"}),(0,s.jsx)(M.A.Item,{name:"name",label:"Category Name",rules:[{required:!0,message:"Please enter category name"}],children:(0,s.jsx)(T.A,{placeholder:"Enter category name"})}),(0,s.jsx)(M.A.Item,{name:"description",label:"Description",children:(0,s.jsx)(L,{placeholder:"Enter category description (optional)",rows:4})})]})})]})};var B=r(12869);let U=e=>{let{data:t,error:r,isLoading:s,refetch:a}=(0,x.OU)(e||0,{skip:!e});return{category:t?.data||null,isLoading:s,error:r,refetch:a}};var $=r(81045);let q=({isOpen:e,onClose:t,categoryId:r,onEdit:a})=>{let{category:l,isLoading:c}=U(r),x=e=>{if(!e)return"N/A";try{return P()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},g=(0,S.d4)(e=>e.auth.user),h="admin"===g?.role&&g?.id===l?.createdBy,u=(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(o.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),a&&l&&h&&(0,s.jsx)(o.Ay,{type:"primary",onClick:()=>a(l.id),children:"Edit"})]});return(0,s.jsx)(F.A,{isOpen:e,onClose:t,title:"Category Details",width:"500px",footer:u,children:c?(0,s.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,s.jsx)(n.A,{indicator:(0,s.jsx)(i.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Category: ",l.name]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete category information and details"})]}),(0,s.jsxs)(B.A,{bordered:!0,column:1,className:"category-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,s.jsx)(B.A.Item,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)(d.A,{})," Category ID"]}),children:l.id}),(0,s.jsx)(B.A.Item,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)(d.A,{})," Name"]}),children:l.name}),(0,s.jsx)(B.A.Item,{label:"Description",children:l.description||"No description provided"}),(0,s.jsx)(B.A.Item,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)($.A,{})," Created At"]}),children:x(l.createdAt)}),l.updatedAt&&(0,s.jsx)(B.A.Item,{label:(0,s.jsxs)("span",{children:[(0,s.jsx)($.A,{})," Last Updated"]}),children:x(l.updatedAt)})]})]}):(0,s.jsx)("div",{className:"text-center py-8 text-gray-800",children:"Category not found"})})};var Y=r(58733);r(86078);let G=({searchTerm:e,setSearchTerm:t,isMobile:r=!1})=>(0,s.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,s.jsx)(T.A,{placeholder:"Search by name or description...",prefix:(0,s.jsx)(Y.A,{className:"text-gray-500"}),value:e,onChange:e=>{let r=e.target.value;console.log("Category search input changed:",r),t(r)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:r?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,s.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,s.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var H=r(51531);r(30453);let V=()=>{let[e,t]=(0,a.useState)({width:0,height:0});return(0,a.useEffect)(()=>{},[]),e},J=()=>{let{user:e}=(0,c.A)(),{width:t}=V(),r=t<640,[x,g]=(0,a.useState)(!1),[u,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(!1),[j,w]=(0,a.useState)(null),[v,A]=(0,a.useState)(null),[C,N]=(0,a.useState)(!1),{categories:k,total:P,page:S,limit:D,isLoading:z,refetch:T,searchTerm:M,setSearchTerm:F,handlePageChange:R}=h(),{deleteCategory:I,isDeleting:L}=m(()=>{T(),N(!1)}),{bulkDeleteCategories:B,isDeleting:U}=p(()=>{Y(!1),T()}),[$,Y]=(0,a.useState)(!1),[J,W]=(0,a.useState)([]),X=e=>{w(e),y(!0)},K=async()=>{if(v)try{await I(v)}catch(e){console.error("Error deleting category:",e)}},Q=async()=>{if(console.log("confirmBulkDelete called with categories:",J),J.length>0)try{await B(J)}catch(e){console.error("Error in confirmBulkDelete:",e)}},Z=e?.role==="admin";return((0,a.useEffect)(()=>{console.log("isAddPanelOpen changed:",x)},[x]),z)?(0,s.jsx)("div",{className:"p-2 sm:p-4 w-full",children:(0,s.jsx)(l.A,{title:(0,s.jsx)("span",{className:"text-gray-800",children:"Category Management"}),styles:{body:{padding:"16px",minHeight:"200px",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,s.jsx)("div",{className:"flex justify-center items-center h-60",children:(0,s.jsx)(n.A,{indicator:(0,s.jsx)(i.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})})})}):(0,s.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,s.jsx)(l.A,{title:(0,s.jsx)("span",{className:"text-gray-800",children:"Category Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:r?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:Z&&(0,s.jsx)(o.Ay,{type:"primary",icon:(0,s.jsx)(d.A,{}),onClick:()=>{console.log("Add category button clicked"),w(null),setTimeout(()=>{g(!0)},0)},size:r?"small":"middle",children:r?"":"Add Category"}),children:(0,s.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,s.jsx)(G,{searchTerm:M,setSearchTerm:F,isMobile:r}),z?(0,s.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,s.jsx)(n.A,{indicator:(0,s.jsx)(i.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,s.jsxs)(s.Fragment,{children:[k.length>0?(0,s.jsx)(E,{categories:k,loading:!1,onView:e=>{A(e),b(!0)},onEdit:X,onDelete:e=>{A(e),N(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with categoryIds:",e),W(e),Y(!0)},isMobile:r}):(0,s.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:M?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:"No categories found matching your search criteria."}),(0,s.jsx)(o.Ay,{type:"primary",onClick:()=>F(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,s.jsxs)("p",{children:["No categories found. ",Z&&"Click 'Add Category' to create one."]})}),k.length>0&&(0,s.jsx)(_,{current:S,pageSize:D,total:P,onChange:R,isMobile:r})]})]})}),(0,s.jsx)(O,{isOpen:x,onClose:()=>g(!1),onSuccess:()=>{g(!1),T()},currentUser:e}),(0,s.jsx)(O,{isOpen:u,onClose:()=>y(!1),onSuccess:()=>{y(!1),T()},category:j,currentUser:e}),(0,s.jsx)(q,{isOpen:f,onClose:()=>{b(!1),A(null)},categoryId:v,onEdit:e=>{b(!1);let t=k.find(t=>t.id===e)||null;t&&X(t)}}),(0,s.jsx)(H.A,{isOpen:C,onClose:()=>{N(!1),A(null)},onConfirm:K,title:"Delete Category",message:"Are you sure you want to delete this category? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:L,type:"danger"}),(0,s.jsx)(H.A,{isOpen:$,onClose:()=>{Y(!1),W([])},onConfirm:Q,title:"Delete Multiple Categories",message:`Are you sure you want to delete ${J.length} categories? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:U,type:"danger"})]})}},51531:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(45512);r(58009);var a=r(88206),l=r(3117),n=r(75238);let o=({isOpen:e,onClose:t,onConfirm:r,title:o,message:i,confirmText:d="Confirm",cancelText:c="Cancel",isLoading:x=!1,type:g="danger"})=>(0,s.jsx)(a.A,{title:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{style:{color:"danger"===g?"#ff4d4f":"warning"===g?"#faad14":"#1890ff",marginRight:8}}),(0,s.jsx)("span",{children:o})]}),open:e,onCancel:t,footer:[(0,s.jsx)(l.Ay,{onClick:t,disabled:x,children:c},"cancel"),(0,s.jsx)(l.Ay,{type:"danger"===g?"primary":"default",danger:"danger"===g,onClick:r,loading:x,children:d},"confirm")],maskClosable:!x,closable:!x,centered:!0,children:(0,s.jsx)("p",{className:"my-4",children:i})})},63844:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,Hj:()=>i,jB:()=>l,nA:()=>o});var s=r(45512);r(58009);var a=r(44195);let l=({children:e,columns:t,className:r,minWidth:l="800px"})=>(0,s.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",r),children:(0,s.jsx)("div",{className:(0,a.cn)("gap-0","block"),style:{},children:e})}),n=({children:e,className:t,sticky:r})=>(0,s.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",r&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[r],t),children:e}),o=({children:e,className:t,sticky:r})=>(0,s.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",r&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[r],t),children:e}),i=({children:e,className:t,selected:r=!1,onClick:l})=>(0,s.jsx)("div",{className:(0,a.cn)("contents",r&&"bg-blue-50",l&&"cursor-pointer hover:bg-gray-50",t),onClick:l,children:e})},98776:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(45512),a=r(58009),l=r(3117),n=r(97071);let o=({isOpen:e,onClose:t,title:r,children:o,width:i="400px",footer:d,fullWidth:c=!1})=>{let[x,g]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),[m,p]=(0,a.useState)(1024);if((0,a.useEffect)(()=>{let e=()=>{p(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",r),e)u(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{g(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{g(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{u(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,r]),!h)return null;let y="Point of Sale"===r||c||"100vw"===i;return(0,s.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${y?"sales-panel-container":""}`,children:[(0,s.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${x?"opacity-50":"opacity-0"}`,onClick:t}),(0,s.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${x?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===r||c||"100vw"===i||m<640?"100vw":m<1024?"500px":"string"==typeof i&&i.includes("px")&&parseInt(i)>600?"600px":i},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:r}),(0,s.jsx)(l.Ay,{type:"text",icon:(0,s.jsx)(n.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:o}),d&&(0,s.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:d})]})]})}},60636:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(92273),a=r(25510),l=r(97245),n=r(42211);let o=()=>{let e=(0,s.wA)(),{user:t,accessToken:r}=(0,s.d4)(e=>e.auth),o=(0,a._)(),{refetch:i}=(0,l.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!r,c=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let s=await i();console.log("useAuth - Refetch result:",s);let a=s.data;if(a?.success&&a?.data){console.log("useAuth - API response data:",a.data);let s=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=a.data.phone||t.phone||"",o=a.data.createdAt||t.createdAt||"",i=a.data.lastPaymentDate||t.lastPaymentDate||void 0,d=a.data.nextPaymentDue||t.nextPaymentDue||null,c=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let x={...a.data,phone:l,createdAt:o,lastPaymentDate:i,nextPaymentDue:d,createdBy:c,paymentStatus:s};console.log("useAuth - Updating Redux store with:",x),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:x,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",a?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:c}}},78337:(e,t,r)=>{"use strict";r.d(t,{d:()=>a});var s=r(58009);function a(e,t){let[r,a]=(0,s.useState)(e);return r}},73542:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(58009);let a=()=>{let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},52465:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\categories\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx","default")},30453:()=>{},16361:()=>{},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,5050,1785,5482,106,4286],()=>r(68390));module.exports=s})();