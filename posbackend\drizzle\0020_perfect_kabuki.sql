ALTER TABLE "payments" ALTER COLUMN "transaction_id" SET DATA TYPE varchar(100);--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "paystack_reference" varchar(100);--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "authorization_code" varchar(100);--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "channel" varchar(20);--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "currency" varchar(3) DEFAULT 'GHS';--> statement-breakpoint
ALTER TABLE "payments" ADD CONSTRAINT "payments_paystack_reference_unique" UNIQUE("paystack_reference");