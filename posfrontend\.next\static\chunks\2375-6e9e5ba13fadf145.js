"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2375],{89842:(e,n,t)=>{t.d(n,{B:()=>o});let o=t(12115).createContext({})},22946:(e,n,t)=>{t.d(n,{$T:()=>h,ph:()=>A,hN:()=>w});var o=t(39014),r=t(59912),c=t(64406),a=t(12115),i=t(85268),l=t(47650),s=t(85407),u=t(1568),f=t(4617),v=t.n(f),d=t(72261),m=t(21855),y=t(23672),p=t(97181);let h=a.forwardRef(function(e,n){var t=e.prefixCls,o=e.style,c=e.className,i=e.duration,l=void 0===i?4.5:i,f=e.showProgress,d=e.pauseOnHover,h=void 0===d||d,k=e.eventKey,A=e.content,g=e.closable,N=e.closeIcon,E=void 0===N?"x":N,C=e.props,x=e.onClick,b=e.onNoticeClose,R=e.times,w=e.hovering,S=a.useState(!1),M=(0,r.A)(S,2),I=M[0],j=M[1],F=a.useState(0),H=(0,r.A)(F,2),L=H[0],O=H[1],W=a.useState(0),D=(0,r.A)(W,2),P=D[0],T=D[1],_=w||I,K=l>0&&f,q=function(){b(k)};a.useEffect(function(){if(!_&&l>0){var e=Date.now()-P,n=setTimeout(function(){q()},1e3*l-P);return function(){h&&clearTimeout(n),T(Date.now()-e)}}},[l,_,R]),a.useEffect(function(){if(!_&&K&&(h||0===P)){var e,n=performance.now();return function t(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var o=Math.min((e+P-n)/(1e3*l),1);O(100*o),o<1&&t()})}(),function(){h&&cancelAnimationFrame(e)}}},[l,P,_,K,R]);var B=a.useMemo(function(){return"object"===(0,m.A)(g)&&null!==g?g:g?{closeIcon:E}:{}},[g,E]),X=(0,p.A)(B,!0),$=100-(!L||L<0?0:L>100?100:L),z="".concat(t,"-notice");return a.createElement("div",(0,s.A)({},C,{ref:n,className:v()(z,c,(0,u.A)({},"".concat(z,"-closable"),g)),style:o,onMouseEnter:function(e){var n;j(!0),null==C||null===(n=C.onMouseEnter)||void 0===n||n.call(C,e)},onMouseLeave:function(e){var n;j(!1),null==C||null===(n=C.onMouseLeave)||void 0===n||n.call(C,e)},onClick:x}),a.createElement("div",{className:"".concat(z,"-content")},A),g&&a.createElement("a",(0,s.A)({tabIndex:0,className:"".concat(z,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===y.A.ENTER)&&q()},"aria-label":"Close"},X,{onClick:function(e){e.preventDefault(),e.stopPropagation(),q()}}),B.closeIcon),K&&a.createElement("progress",{className:"".concat(z,"-progress"),max:"100",value:$},$+"%"))});var k=a.createContext({});let A=function(e){var n=e.children,t=e.classNames;return a.createElement(k.Provider,{value:{classNames:t}},n)},g=function(e){var n,t,o,r={offset:8,threshold:3,gap:16};return e&&"object"===(0,m.A)(e)&&(r.offset=null!==(n=e.offset)&&void 0!==n?n:8,r.threshold=null!==(t=e.threshold)&&void 0!==t?t:3,r.gap=null!==(o=e.gap)&&void 0!==o?o:16),[!!e,r]};var N=["className","style","classNames","styles"];let E=function(e){var n=e.configList,t=e.placement,l=e.prefixCls,f=e.className,m=e.style,y=e.motion,p=e.onAllNoticeRemoved,A=e.onNoticeClose,E=e.stack,C=(0,a.useContext)(k).classNames,x=(0,a.useRef)({}),b=(0,a.useState)(null),R=(0,r.A)(b,2),w=R[0],S=R[1],M=(0,a.useState)([]),I=(0,r.A)(M,2),j=I[0],F=I[1],H=n.map(function(e){return{config:e,key:String(e.key)}}),L=g(E),O=(0,r.A)(L,2),W=O[0],D=O[1],P=D.offset,T=D.threshold,_=D.gap,K=W&&(j.length>0||H.length<=T),q="function"==typeof y?y(t):y;return(0,a.useEffect)(function(){W&&j.length>1&&F(function(e){return e.filter(function(e){return H.some(function(n){return e===n.key})})})},[j,H,W]),(0,a.useEffect)(function(){var e,n;W&&x.current[null===(e=H[H.length-1])||void 0===e?void 0:e.key]&&S(x.current[null===(n=H[H.length-1])||void 0===n?void 0:n.key])},[H,W]),a.createElement(d.aF,(0,s.A)({key:t,className:v()(l,"".concat(l,"-").concat(t),null==C?void 0:C.list,f,(0,u.A)((0,u.A)({},"".concat(l,"-stack"),!!W),"".concat(l,"-stack-expanded"),K)),style:m,keys:H,motionAppear:!0},q,{onAllRemoved:function(){p(t)}}),function(e,n){var r=e.config,u=e.className,f=e.style,d=e.index,m=r.key,y=r.times,p=String(m),k=r.className,g=r.style,E=r.classNames,b=r.styles,R=(0,c.A)(r,N),S=H.findIndex(function(e){return e.key===p}),M={};if(W){var I=H.length-1-(S>-1?S:d-1),L="top"===t||"bottom"===t?"-50%":"0";if(I>0){M.height=K?null===(O=x.current[p])||void 0===O?void 0:O.offsetHeight:null==w?void 0:w.offsetHeight;for(var O,D,T,q,B=0,X=0;X<I;X++)B+=(null===(q=x.current[H[H.length-1-X].key])||void 0===q?void 0:q.offsetHeight)+_;var $=(K?B:I*P)*(t.startsWith("top")?1:-1),z=!K&&null!=w&&w.offsetWidth&&null!==(D=x.current[p])&&void 0!==D&&D.offsetWidth?((null==w?void 0:w.offsetWidth)-2*P*(I<3?I:3))/(null===(T=x.current[p])||void 0===T?void 0:T.offsetWidth):1;M.transform="translate3d(".concat(L,", ").concat($,"px, 0) scaleX(").concat(z,")")}else M.transform="translate3d(".concat(L,", 0, 0)")}return a.createElement("div",{ref:n,className:v()("".concat(l,"-notice-wrapper"),u,null==E?void 0:E.wrapper),style:(0,i.A)((0,i.A)((0,i.A)({},f),M),null==b?void 0:b.wrapper),onMouseEnter:function(){return F(function(e){return e.includes(p)?e:[].concat((0,o.A)(e),[p])})},onMouseLeave:function(){return F(function(e){return e.filter(function(e){return e!==p})})}},a.createElement(h,(0,s.A)({},R,{ref:function(e){S>-1?x.current[p]=e:delete x.current[p]},prefixCls:l,classNames:E,styles:b,className:v()(k,null==C?void 0:C.notice),style:g,times:y,key:m,eventKey:m,onNoticeClose:A,hovering:W&&j.length>0})))})};var C=a.forwardRef(function(e,n){var t=e.prefixCls,c=void 0===t?"rc-notification":t,s=e.container,u=e.motion,f=e.maxCount,v=e.className,d=e.style,m=e.onAllRemoved,y=e.stack,p=e.renderNotifications,h=a.useState([]),k=(0,r.A)(h,2),A=k[0],g=k[1],N=function(e){var n,t=A.find(function(n){return n.key===e});null==t||null===(n=t.onClose)||void 0===n||n.call(t),g(function(n){return n.filter(function(n){return n.key!==e})})};a.useImperativeHandle(n,function(){return{open:function(e){g(function(n){var t,r=(0,o.A)(n),c=r.findIndex(function(n){return n.key===e.key}),a=(0,i.A)({},e);return c>=0?(a.times=((null===(t=n[c])||void 0===t?void 0:t.times)||0)+1,r[c]=a):(a.times=0,r.push(a)),f>0&&r.length>f&&(r=r.slice(-f)),r})},close:function(e){N(e)},destroy:function(){g([])}}});var C=a.useState({}),x=(0,r.A)(C,2),b=x[0],R=x[1];a.useEffect(function(){var e={};A.forEach(function(n){var t=n.placement,o=void 0===t?"topRight":t;o&&(e[o]=e[o]||[],e[o].push(n))}),Object.keys(b).forEach(function(n){e[n]=e[n]||[]}),R(e)},[A]);var w=function(e){R(function(n){var t=(0,i.A)({},n);return(t[e]||[]).length||delete t[e],t})},S=a.useRef(!1);if(a.useEffect(function(){Object.keys(b).length>0?S.current=!0:S.current&&(null==m||m(),S.current=!1)},[b]),!s)return null;var M=Object.keys(b);return(0,l.createPortal)(a.createElement(a.Fragment,null,M.map(function(e){var n=b[e],t=a.createElement(E,{key:e,configList:n,placement:e,prefixCls:c,className:null==v?void 0:v(e),style:null==d?void 0:d(e),motion:u,onNoticeClose:N,onAllNoticeRemoved:w,stack:y});return p?p(t,{prefixCls:c,key:e}):t})),s)}),x=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],b=function(){return document.body},R=0;function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.getContainer,t=void 0===n?b:n,i=e.motion,l=e.prefixCls,s=e.maxCount,u=e.className,f=e.style,v=e.onAllRemoved,d=e.stack,m=e.renderNotifications,y=(0,c.A)(e,x),p=a.useState(),h=(0,r.A)(p,2),k=h[0],A=h[1],g=a.useRef(),N=a.createElement(C,{container:k,ref:g,prefixCls:l,motion:i,maxCount:s,className:u,style:f,onAllRemoved:v,stack:d,renderNotifications:m}),E=a.useState([]),w=(0,r.A)(E,2),S=w[0],M=w[1],I=a.useMemo(function(){return{open:function(e){var n=function(){for(var e={},n=arguments.length,t=Array(n),o=0;o<n;o++)t[o]=arguments[o];return t.forEach(function(n){n&&Object.keys(n).forEach(function(t){var o=n[t];void 0!==o&&(e[t]=o)})}),e}(y,e);(null===n.key||void 0===n.key)&&(n.key="rc-notification-".concat(R),R+=1),M(function(e){return[].concat((0,o.A)(e),[{type:"open",config:n}])})},close:function(e){M(function(n){return[].concat((0,o.A)(n),[{type:"close",key:e}])})},destroy:function(){M(function(e){return[].concat((0,o.A)(e),[{type:"destroy"}])})}}},[]);return a.useEffect(function(){A(t())}),a.useEffect(function(){if(g.current&&S.length){var e,n;S.forEach(function(e){switch(e.type){case"open":g.current.open(e.config);break;case"close":g.current.close(e.key);break;case"destroy":g.current.destroy()}}),M(function(t){return e===t&&n||(e=t,n=t.filter(function(e){return!S.includes(e)})),n})}},[S]),[I,N]}}}]);