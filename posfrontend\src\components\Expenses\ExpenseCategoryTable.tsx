"use client";

import React from "react";
import { <PERSON><PERSON>, Tooltip, Checkbox, Tag } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  DeleteOutlined,
  TagOutlined,
  DeleteFilled,
  CrownOutlined,
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { ExpenseCategory } from "@/reduxRTK/services/expenseCategoryApi";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface ExpenseCategoryTableProps {
  categories: ExpenseCategory[];
  loading?: boolean;
  onEdit?: (category: ExpenseCategory) => void;
  onDelete?: (categoryId: number) => void;
  onBulkDelete?: (categoryIds: number[]) => void;
  selectedCategories?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  isMobile?: boolean;
}

const ExpenseCategoryTable: React.FC<ExpenseCategoryTableProps> = ({
  categories,
  loading = false,
  onEdit,
  onDelete,
  onBulkDelete,
  selectedCategories = [],
  onSelectionChange,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Check permissions
  const canEdit = userRole === "admin" || userRole === "superadmin";
  const canDelete = userRole === "admin" || userRole === "superadmin";

  // Handle individual checkbox change
  const handleCheckboxChange = (categoryId: number, checked: boolean) => {
    if (!onSelectionChange) return;

    const newSelection = checked
      ? [...selectedCategories, categoryId]
      : selectedCategories.filter(id => id !== categoryId);
    
    onSelectionChange(newSelection);
  };

  // Handle select all checkbox
  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (!onSelectionChange) return;

    if (e.target.checked) {
      // Only select non-default categories for bulk operations
      const selectableIds = categories
        .filter(category => !category.isDefault)
        .map(category => category.id);
      onSelectionChange(selectableIds);
    } else {
      onSelectionChange([]);
    }
  };

  // Check if all selectable categories are selected
  const selectableCategories = categories.filter(category => !category.isDefault);
  const isAllSelected = selectableCategories.length > 0 && 
    selectedCategories.length === selectableCategories.length;
  const isIndeterminate = selectedCategories.length > 0 && 
    selectedCategories.length < selectableCategories.length;

  return (
    <div>
      {/* Bulk Actions */}
      {selectedCategories.length > 0 && canDelete && onBulkDelete && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-red-700">
              {selectedCategories.length} category(s) selected
            </span>
            <Button
              type="primary"
              danger
              icon={<DeleteFilled />}
              onClick={() => onBulkDelete(selectedCategories)}
              size={isMobile ? "small" : "middle"}
            >
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns={onSelectionChange ? "50px 200px 120px 100px 150px" : "200px 120px 100px 150px"}
          minWidth={onSelectionChange ? "700px" : "650px"}
        >
          {/* Mobile Headers */}
          {onSelectionChange && (
            <TableHeader className="text-center">
              <Checkbox
                indeterminate={isIndeterminate}
                checked={isAllSelected}
                onChange={handleSelectAll}
              />
            </TableHeader>
          )}
          <TableHeader>
            <span className="flex items-center">
              <TagOutlined className="mr-1" />
              Category
            </span>
          </TableHeader>
          <TableHeader>
            Color
          </TableHeader>
          <TableHeader>
            Type
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {categories.map((category) => (
            <TableRow
              key={category.id}
              selected={selectedCategories.includes(category.id)}
            >
              {onSelectionChange && (
                <TableCell className="text-center">
                  {!category.isDefault && (
                    <Checkbox
                      checked={selectedCategories.includes(category.id)}
                      onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                    />
                  )}
                </TableCell>
              )}
              <TableCell>
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-2"
                    style={{ backgroundColor: category.color }}
                  />
                  <div className="max-w-[150px] overflow-hidden text-ellipsis font-medium">
                    {category.name}
                    {category.isDefault && (
                      <CrownOutlined className="ml-1 text-yellow-500" title="System Default" />
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Tag color={category.color} className="text-xs">
                  {category.color}
                </Tag>
              </TableCell>
              <TableCell>
                {category.isDefault ? (
                  <Tag color="gold" icon={<CrownOutlined />} className="text-xs">
                    Default
                  </Tag>
                ) : (
                  <Tag color="blue" className="text-xs">
                    Custom
                  </Tag>
                )}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  {canEdit && onEdit && !category.isDefault && (
                    <Tooltip title="Edit">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => onEdit(category)}
                        className="text-blue-500 hover:text-blue-400"
                      />
                    </Tooltip>
                  )}
                  {canDelete && onDelete && !category.isDefault && (
                    <Tooltip title="Delete">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(category.id)}
                        className="text-red-500 hover:text-red-400"
                      />
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <thead className="bg-gray-50">
              <tr>
                {onSelectionChange && (
                  <th className="px-4 py-3 text-left">
                    <Checkbox
                      indeterminate={isIndeterminate}
                      checked={isAllSelected}
                      onChange={handleSelectAll}
                    />
                  </th>
                )}
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Color
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>

            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50">
                  {onSelectionChange && (
                    <td className="px-4 py-4 whitespace-nowrap">
                      {!category.isDefault && (
                        <Checkbox
                          checked={selectedCategories.includes(category.id)}
                          onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                        />
                      )}
                    </td>
                  )}
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <div
                        className="w-4 h-4 rounded-full mr-3"
                        style={{ backgroundColor: category.color }}
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900 flex items-center">
                          {category.name}
                          {category.isDefault && (
                            <CrownOutlined className="ml-2 text-yellow-500" title="System Default" />
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {category.description || '-'}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <Tag color={category.color} className="text-xs">
                      {category.color}
                    </Tag>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {category.isDefault ? (
                      <Tag color="gold" icon={<CrownOutlined />}>
                        System Default
                      </Tag>
                    ) : (
                      <Tag color="blue">
                        Custom
                      </Tag>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {canEdit && onEdit && !category.isDefault && (
                        <Tooltip title="Edit">
                          <Button
                            type="text"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => onEdit(category)}
                            className="text-blue-500"
                          />
                        </Tooltip>
                      )}
                      {canDelete && onDelete && !category.isDefault && (
                        <Tooltip title="Delete">
                          <Button
                            type="text"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => onDelete(category.id)}
                            className="text-red-500"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ExpenseCategoryTable;
