"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface AdminProductsChartProps {
  products: any[];
  sales: any[];
}

const AdminProductsChart: React.FC<AdminProductsChartProps> = ({ products, sales }) => {
  const chartData = useMemo(() => {
    console.log('📊 Products Chart - Products found:', products.length, 'Sales found:', sales.length);
    console.log('📊 Sample sale structure:', sales[0]);
    console.log('📊 Sample product structure:', products[0]);

    // Count sales by product - using multiple possible field names
    const productSales = new Map<string, { name: string; count: number; revenue: number; productId: string }>();

    // Initialize with all products
    products.forEach(product => {
      const productKey = String(product.id || product.productId || product._id);
      productSales.set(productKey, {
        name: product.name || product.productName || 'Unknown Product',
        count: 0,
        revenue: 0,
        productId: productKey
      });
    });

    console.log('📊 Initialized product sales map:', Array.from(productSales.entries()).slice(0, 3));

    // Count sales for each product - check multiple possible structures
    sales.forEach((sale, saleIndex) => {
      console.log(`📊 Processing sale ${saleIndex}:`, {
        saleId: sale.id,
        saleItems: sale.saleItems,
        items: sale.items,
        products: sale.products
      });

      // Try different possible structures for sale items
      const saleItems = sale.saleItems || sale.items || sale.products || [];

      if (Array.isArray(saleItems)) {
        saleItems.forEach((item: any, itemIndex: number) => {
          console.log(`📊 Processing item ${itemIndex}:`, item);

          // Try different possible field names for product ID
          const productId = String(
            item.productId ||
            item.product_id ||
            item.id ||
            item.product?.id ||
            item.product?.productId ||
            ''
          );

          // Try different possible field names for quantity and price
          const quantity = Number(item.quantity || item.qty || item.amount || 1);
          const totalPrice = parseFloat(
            item.totalPrice ||
            item.total_price ||
            item.price ||
            item.total ||
            item.amount ||
            '0'
          );

          console.log(`📊 Extracted data:`, { productId, quantity, totalPrice });

          if (productId && productSales.has(productId)) {
            const productData = productSales.get(productId)!;
            productData.count += quantity;
            productData.revenue += totalPrice;
            console.log(`📊 Updated product ${productId}:`, productData);
          } else {
            console.log(`📊 Product ${productId} not found in products map`);
          }
        });
      }
    });

    // Get all products with sales (non-zero) and sort by sales count
    const productsWithSales = Array.from(productSales.values())
      .filter(p => p.count > 0)
      .sort((a, b) => b.count - a.count);

    console.log('📊 Products with sales:', productsWithSales);

    // If we have many products, show top 15, otherwise show all with sales
    const topProducts = productsWithSales.slice(0, 15);

    const categories = topProducts.map(p => {
      // Truncate long product names for better display
      return p.name.length > 20 ? p.name.substring(0, 17) + '...' : p.name;
    });
    const salesData = topProducts.map(p => p.count);
    const revenueData = topProducts.map(p => p.revenue);

    console.log('📈 Final chart data:', {
      totalProducts: products.length,
      productsWithSales: productsWithSales.length,
      topProductsShown: topProducts.length,
      categories,
      salesData,
      revenueData
    });

    return {
      categories,
      salesData,
      revenueData,
      totalProductsWithSales: productsWithSales.length,
      totalProducts: products.length
    };
  }, [products, sales]);

  // Use horizontal bars if we have many products for better readability
  const useHorizontalBars = chartData.categories.length > 8;

  const options: ApexOptions = {
    chart: {
      type: 'bar',
      height: useHorizontalBars ? Math.max(300, chartData.categories.length * 25) : 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#5750F1', '#0ABEF9'],
    plotOptions: {
      bar: {
        horizontal: useHorizontalBars,
        columnWidth: useHorizontalBars ? '70%' : '60%',
        borderRadius: 4,
      },
    },
    dataLabels: {
      enabled: false,
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '14px',
      fontFamily: 'inherit',
      markers: {
        size: 8,
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: [
        {
          formatter: function(value) {
            return `${value} units sold`;
          }
        },
        {
          formatter: function(value) {
            return `₵${value.toFixed(2)} revenue`;
          }
        }
      ]
    },
    xaxis: {
      categories: chartData.categories,
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '11px',
        },
        rotate: useHorizontalBars ? 0 : -45,
        maxHeight: useHorizontalBars ? undefined : 60,
        trim: true,
      }
    },
    yaxis: [
      {
        title: {
          text: 'Units Sold',
          style: {
            fontSize: '12px',
          }
        },
        labels: {
          formatter: function(value) {
            return Math.round(value).toString();
          },
          style: {
            fontSize: '12px',
          }
        }
      },
      {
        opposite: true,
        title: {
          text: 'Revenue (₵)',
          style: {
            fontSize: '12px',
          }
        },
        labels: {
          formatter: function(value) {
            return `₵${value.toFixed(0)}`;
          },
          style: {
            fontSize: '12px',
          }
        }
      }
    ]
  };

  const series = [
    {
      name: 'Units Sold',
      data: chartData.salesData
    },
    {
      name: 'Revenue',
      data: chartData.revenueData
    }
  ];

  if (chartData.categories.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No product sales data available</p>
          <p className="text-sm">You have {chartData.totalProducts} products, but none have been sold yet</p>
          <p className="text-sm">Chart will appear when products are sold</p>
        </div>
      </div>
    );
  }

  const chartHeight = useHorizontalBars ? Math.max(250, chartData.categories.length * 25) : 230;
  const containerHeight = useHorizontalBars ? 'auto' : 'h-64';

  return (
    <div className={containerHeight} style={{ minHeight: useHorizontalBars ? chartHeight + 50 : undefined }}>
      {/* Chart Info */}
      <div className="mb-2 text-xs text-gray-500 text-center">
        Showing top {chartData.categories.length} of {chartData.totalProductsWithSales} products with sales
        ({chartData.totalProducts} total products)
      </div>

      <Chart
        options={options}
        series={series}
        type="bar"
        height={chartHeight}
      />
    </div>
  );
};

export default AdminProductsChart;
