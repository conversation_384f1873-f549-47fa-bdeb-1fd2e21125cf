"use client";

import React, { useState } from "react";
import { Card, But<PERSON>, Spin } from "antd";
import {
  ShoppingCartOutlined,
  LoadingOutlined,
  SearchOutlined,
  ShopOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSalesList } from "@/hooks/sales/useSalesList";
import { useSaleDelete } from "@/hooks/sales/useSaleDelete";
import { useSaleBulkDelete } from "@/hooks/sales/useSaleBulkDelete";
import SalesTable from "@/components/Sales/SalesTable";
import SalesPagination from "@/components/Sales/SalesPagination";
import SalesFormPanel from "@/components/Sales/SalesFormPanel";
import SalesDetailsPanel from "@/components/Sales/SalesDetailsPanel";
import SalesSearch from "@/components/Sales/SalesSearch";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { Sale } from "@/reduxRTK/services/salesApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useGetAllProductsQuery } from '@/reduxRTK/services/productApi';

const SalesPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // UI state
  const [isCreatePanelOpen, setIsCreatePanelOpen] = useState(false);
  const [isDetailsPanelOpen, setIsDetailsPanelOpen] = useState(false);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [selectedSaleId, setSelectedSaleId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);


  // Sales list state and handlers
  const {
    sales,
    total,
    page,
    limit,
    isLoading,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
  } = useSalesList();

  // Delete sale handler
  const { deleteSale, isDeleting } = useSaleDelete(() => {
    setIsDeleteDialogOpen(false);
    refetch();
  });

  // Bulk delete sale handler
  const { bulkDeleteSales, isDeleting: isBulkDeleting } = useSaleBulkDelete(
    () => {
      setIsBulkDeleteDialogOpen(false);
      refetch();
    },
  );

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [salesToDelete, setSalesToDelete] = useState<number[]>([]);

  // Fetch products for the sales page
  const { data: productsData, isLoading: isLoadingProducts } = useGetAllProductsQuery({ page: 1, limit: 1 });
  const hasProducts = (productsData?.data?.products?.length ?? 0) > 0;

  // Handle add sale button click
  const handleAddSale = () => {
    setIsCreatePanelOpen(true);
  };

  // Handle view sale button click
  const handleViewSale = (sale: Sale) => {
    setSelectedSale(sale);
    setIsDetailsPanelOpen(true);
  };

  // Handle delete sale button click
  const handleDeleteSale = (saleId: number) => {
    setSelectedSaleId(saleId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete sale
  const confirmDeleteSale = async () => {
    if (selectedSaleId) {
      await deleteSale(selectedSaleId);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedSaleId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (saleIds: number[]) => {
    console.log("handleBulkDelete called with saleIds:", saleIds);
    setSalesToDelete(saleIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with sales:", salesToDelete);

    if (salesToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple sales
        await bulkDeleteSales(salesToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setSalesToDelete([]);
  };

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Sales Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          hasProducts && (
            <Button
              type="primary"
              icon={<ShoppingCartOutlined />}
              onClick={handleAddSale}
              size={isMobile ? "small" : "middle"}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMobile ? "" : "New Sale"}
            </Button>
          )
        }
      >
        <div className="w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm">
          {/* Search Component - Always visible */}
          <SalesSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex h-60 items-center justify-center bg-gray-50">
              <Spin
                indicator={
                  <LoadingOutlined
                    style={{ fontSize: 24, color: "#1890ff" }}
                    spin
                  />
                }
              />
            </div>
          ) : (
            <>
              {!hasProducts ? (
                <div className="flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800">
                  <ShopOutlined className="text-4xl mb-4" />
                  <p>No products found. Please add products to start selling.</p>
                </div>
              ) : (
                <>
                  {/* Sales Table Component */}
                  {sales.length > 0 ? (
                    <SalesTable
                      sales={sales}
                      loading={false}
                      onViewSale={handleViewSale}
                      onDelete={handleDeleteSale}
                      onBulkDelete={handleBulkDelete}
                      isMobile={isMobile}
                    />
                  ) : (
                    <div className="flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800">
                      {searchTerm ? (
                        <>
                          <p>No sales found matching your search criteria.</p>
                          <Button
                            type="primary"
                            onClick={() => setSearchTerm("")}
                            className="mt-4 bg-blue-600 hover:bg-blue-700"
                            icon={<SearchOutlined />}
                          >
                            Clear Search
                          </Button>
                        </>
                      ) : (
                        <p>No sales found. Click &quot;New Sale&quot; to create one.</p>
                      )}
                    </div>
                  )}

                  {/* Pagination Component - Only show if we have results */}
                  {sales.length > 0 && (
                    <SalesPagination
                      current={page}
                      pageSize={limit}
                      total={total}
                      onChange={handlePageChange}
                      isMobile={isMobile}
                    />
                  )}
                </>
              )}
            </>
          )}
        </div>
      </Card>

      {/* SalesFormPanel */}
      <SalesFormPanel
        isOpen={isCreatePanelOpen}
        onClose={() => setIsCreatePanelOpen(false)}
        onSuccess={() => {
          setIsCreatePanelOpen(false);
          refetch(); // Refresh the sales list
        }}
      />
      {/* Sale Details Panel */}
      <SalesDetailsPanel
        sale={selectedSale}
        isOpen={isDetailsPanelOpen}
        onClose={() => {
          setIsDetailsPanelOpen(false);
          setSelectedSale(null);
        }}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteSale}
        title="Delete Sale"
        message="Are you sure you want to delete this sale? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Sales"
        message={`Are you sure you want to delete ${salesToDelete.length} sales? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default SalesPage;
