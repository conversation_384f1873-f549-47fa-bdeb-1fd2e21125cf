"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     Supplier:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: ABC Distributors Ltd
 *         contactPerson:
 *           type: string
 *           example: <PERSON>
 *         phone:
 *           type: string
 *           example: +233123456789
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         address:
 *           type: string
 *           example: 123 Industrial Area, Accra, Ghana
 *         createdBy:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: 2024-01-01T10:30:00Z
 *
 *     Purchase:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         supplierId:
 *           type: integer
 *           example: 1
 *         productId:
 *           type: integer
 *           example: 1
 *         quantity:
 *           type: integer
 *           example: 50
 *         costPrice:
 *           type: string
 *           example: "3.00"
 *         totalCost:
 *           type: string
 *           example: "150.00"
 *         purchaseDate:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *         purchasedBy:
 *           type: integer
 *           example: 1
 *         createdBy:
 *           type: integer
 *           example: 1
 *         supplier:
 *           $ref: '#/components/schemas/Supplier'
 *         product:
 *           $ref: '#/components/schemas/Product'
 *
 *     StockAdjustment:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         productId:
 *           type: integer
 *           example: 1
 *         quantityChange:
 *           type: integer
 *           example: -5
 *           description: Positive for increase, negative for decrease
 *         reason:
 *           type: string
 *           example: "Damaged goods"
 *         adjustedBy:
 *           type: integer
 *           example: 1
 *         createdBy:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T10:30:00Z"
 *         product:
 *           $ref: '#/components/schemas/Product'
 */
/**
 * @swagger
 * /suppliers:
 *   post:
 *     summary: Manage suppliers
 *     description: Create, read, update, or delete suppliers
 *     tags: [Inventory Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [createnew]
 *                     example: createnew
 *                   name:
 *                     type: string
 *                     example: "ABC Distributors Ltd"
 *                   contactPerson:
 *                     type: string
 *                     example: "John Smith"
 *                   phone:
 *                     type: string
 *                     example: "+233123456789"
 *                   email:
 *                     type: string
 *                     format: email
 *                     example: "<EMAIL>"
 *                   address:
 *                     type: string
 *                     example: "123 Industrial Area, Accra, Ghana"
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                   search:
 *                     type: string
 *                     example: "ABC"
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 */
/**
 * @swagger
 * /purchases:
 *   post:
 *     summary: Manage purchases
 *     description: Create, read, update, or delete purchase records
 *     tags: [Inventory Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [createnew]
 *                     example: createnew
 *                   supplierId:
 *                     type: integer
 *                     example: 1
 *                   productId:
 *                     type: integer
 *                     example: 1
 *                   quantity:
 *                     type: integer
 *                     example: 50
 *                   costPrice:
 *                     type: number
 *                     format: decimal
 *                     example: 3.00
 *                   totalCost:
 *                     type: number
 *                     format: decimal
 *                     example: 150.00
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                   limit:
 *                     type: integer
 *                     example: 10
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 */
/**
 * @swagger
 * /stockadjustment:
 *   post:
 *     summary: Manage stock adjustments
 *     description: Create, read stock adjustment records
 *     tags: [Inventory Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [createnew]
 *                     example: createnew
 *                   productId:
 *                     type: integer
 *                     example: 1
 *                   quantityChange:
 *                     type: integer
 *                     example: -5
 *                     description: "Positive for increase, negative for decrease"
 *                   reason:
 *                     type: string
 *                     example: "Damaged goods"
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                   limit:
 *                     type: integer
 *                     example: 10
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 */
