"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPaymentStatus = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const checkPaymentStatus = async (requesterId) => {
    // 🚨 **Fetch requester's role & payment status**
    const requesterResult = await db_1.postgresDb
        .select({ role: schema_1.users.role, paymentStatus: schema_1.users.paymentStatus })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.eq)(schema_1.users.id, requesterId))
        .limit(1);
    const requester = requesterResult[0];
    if (!requester) {
        throw new Error("Unauthorized: User not found.");
    }
    // 🚨 **Block unpaid admins & cashiers**
    if ((requester.role === "admin" || requester.role === "cashier") && requester.paymentStatus !== "successful") {
        throw new Error("Unauthorized: Payment required.");
    }
    return true; // ✅ Payment is valid, proceed
};
exports.checkPaymentStatus = checkPaymentStatus;
