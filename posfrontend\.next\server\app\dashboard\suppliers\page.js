(()=>{var e={};e.id=6091,e.ids=[6091],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},8784:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(70260),a=t(28203),l=t(25155),n=t.n(l),i=t(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d=["",{children:["dashboard",{children:["suppliers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67520)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\suppliers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\suppliers\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/suppliers/page",pathname:"/dashboard/suppliers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57424:(e,s,t)=>{Promise.resolve().then(t.bind(t,67520))},67152:(e,s,t)=>{Promise.resolve().then(t.bind(t,4799))},53180:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(11855),a=t(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var n=t(78480);let i=a.forwardRef(function(e,s){return a.createElement(n.A,(0,r.A)({},e,{ref:s,icon:l}))})},23847:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(11855),a=t(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var n=t(78480);let i=a.forwardRef(function(e,s){return a.createElement(n.A,(0,r.A)({},e,{ref:s,icon:l}))})},24648:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(11855),a=t(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var n=t(78480);let i=a.forwardRef(function(e,s){return a.createElement(n.A,(0,r.A)({},e,{ref:s,icon:l}))})},4799:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Q});var r=t(45512),a=t(58009),l=t(6987),n=t(3117),i=t(21419),o=t(24648),d=t(88752),c=t(60636),p=t(765),x=t(63087),m=t(78337),u=t(92273);let h=(e=1,s=10)=>{let[t,r]=(0,a.useState)(e),[l,n]=(0,a.useState)(s),[i,o]=(0,a.useState)(""),d=(0,u.d4)(e=>e.auth.user),c=d?.role,p=d?.id,h=(0,m.d)(i,500);(0,a.useEffect)(()=>{r(1)},[h]);let{data:g,error:f,isLoading:y,refetch:j}=(0,x.w$)({page:t,limit:l,search:h}),b=g?.data?.suppliers||[],A=g?.data?.total||0;return console.log("Suppliers from API:",b),console.log("Total suppliers:",A),console.log("Current user ID:",p),console.log("User role:",c),{suppliers:b,total:A,page:t,limit:l,isLoading:y,error:f,refetch:j,searchTerm:i,setSearchTerm:o,handlePageChange:e=>{r(e)},handleLimitChange:e=>{n(e),r(1)}}};var g=t(49792);let f=e=>{let[s,{isLoading:t}]=(0,x.kH)();return{deleteSupplier:async t=>{try{let r=await s(t).unwrap();if(!r.success)throw Error(r.message||"Failed to delete supplier");return(0,g.r)("success","Supplier deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete supplier error:",e),(0,g.r)("error",e.message||"Failed to delete supplier"),e}},isDeleting:t}},y=e=>{let[s,{isLoading:t}]=(0,x.tr)();return{bulkDeleteSuppliers:async t=>{try{console.log("Bulk deleting suppliers with IDs:",t);let r=await s(t).unwrap();if(!r.success)throw Error(r.message||"Failed to delete suppliers");return(0,g.r)("success",`${t.length} suppliers deleted successfully`),e&&e(),r.data}catch(e){throw console.error("Bulk delete suppliers error:",e),(0,g.r)("error",e.message||"Failed to delete suppliers"),e}},isDeleting:t}};var j=t(48752),b=t(77067),A=t(70001),v=t(25421),w=t(53180),N=t(23847),C=t(81045),S=t(25834),P=t(99261),k=t(86977),E=t(63844),D=t(73542),z=t(16589),I=t.n(z);let M=({suppliers:e,loading:s,onView:t,onEdit:l,onDelete:i,onBulkDelete:d,isMobile:c=!1})=>{let p=(0,D.E)(),x=c||p,m=(0,u.d4)(e=>e.auth.user),h=m?.role,[g,f]=(0,a.useState)([]),[y,z]=(0,a.useState)(!1),M=s=>{let t=s.target.checked;z(t),t?f(e.filter(e=>L(e)).map(e=>e.id)):f([])},T=(e,s)=>{s?f(s=>[...s,e]):f(s=>s.filter(s=>s!==e))},_=e=>I()(e).format("MMM D, YYYY"),L=e=>"admin"===h;return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[g.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[g.length," ",1===g.length?"supplier":"suppliers"," selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(v.A,{}),onClick:()=>{g.length>0&&d?(d(g),f([]),z(!1)):j.Ay.warning({message:"No suppliers selected",description:"Please select at least one supplier to delete."})},className:"ml-2",children:"Delete Selected"})]}),x?(0,r.jsxs)(E.jB,{columns:"50px 200px 150px 120px 120px 150px",minWidth:"800px",children:[(0,r.jsx)(E.A0,{className:"text-center",children:(0,r.jsx)(b.A,{checked:y,onChange:M,disabled:0===e.filter(e=>L(e)).length})}),(0,r.jsx)(E.A0,{sticky:x?void 0:"left",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)(E.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Email"]})}),(0,r.jsx)(E.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"mr-1"}),"Phone"]})}),!x&&(0,r.jsx)(E.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"}),"Contact Person"]})}),(0,r.jsx)(E.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(C.A,{className:"mr-1"}),"Created At"]})}),(0,r.jsx)(E.A0,{sticky:x?void 0:"right",className:"text-right",children:"Actions"}),e.map(e=>(0,r.jsxs)(E.Hj,{selected:g.includes(e.id),children:[(0,r.jsx)(E.nA,{className:"text-center",children:L(e)&&(0,r.jsx)(b.A,{checked:g.includes(e.id),onChange:s=>T(e.id,s.target.checked)})}),(0,r.jsx)(E.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,r.jsx)(E.nA,{children:(0,r.jsx)("span",{className:"text-blue-600",children:e.email||"N/A"})}),(0,r.jsx)(E.nA,{children:(0,r.jsx)("span",{className:"font-mono text-sm",children:e.phone||"N/A"})}),(0,r.jsx)(E.nA,{children:_(e.createdAt)}),(0,r.jsx)(E.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(S.A,{}),onClick:()=>t(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),L(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(k.A,{}),onClick:()=>i(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(b.A,{checked:y,onChange:M,disabled:0===e.filter(e=>L(e)).length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Email"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"mr-1"}),"Phone"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"}),"Contact Person"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(C.A,{className:"mr-1"}),"Created At"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:g.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:L(e)&&(0,r.jsx)(b.A,{checked:g.includes(e.id),onChange:s=>T(e.id,s.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[200px] overflow-hidden text-ellipsis",children:e.email||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[140px] overflow-hidden text-ellipsis",children:e.phone||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[140px] overflow-hidden text-ellipsis",children:e.contactPerson||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[130px] overflow-hidden text-ellipsis",children:_(e.createdAt)})}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(S.A,{}),onClick:()=>t(e.id),type:"text",className:"view-button text-green-500",size:"middle"})}),L(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500",size:"middle"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(k.A,{}),onClick:()=>i(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})]})})]},e.id))})]})})]})};var T=t(59022),_=t(60165);let L=({current:e,pageSize:s,total:t,onChange:a,isMobile:l=!1})=>{let n=Math.ceil(t/s);return(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*s+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*s,t)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:t})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(T.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(s,t)=>{let l=t+1;return(0,r.jsx)("button",{onClick:()=>a(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:l},l)}),(0,r.jsxs)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=n?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(_.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,r.jsx)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var R=t(41257),F=t(37764),B=t(98776);let O=e=>{let[s,{isLoading:t}]=(0,x.$i)();return{createSupplier:async t=>{try{console.log("useSupplierCreate - Starting supplier creation with data:",t);let r=await s(t).unwrap();if(console.log("useSupplierCreate - API response:",r),!r.success)throw console.error("useSupplierCreate - API returned error:",r.message),Error(r.message||"Failed to create supplier");return(0,g.r)("success","Supplier created successfully"),e&&e(),r.data}catch(e){throw console.error("Create supplier error:",e),(0,g.r)("error",e.message||"Failed to create supplier"),e}},isSubmitting:t}},U=e=>{let[s,{isLoading:t}]=(0,x.mP)();return{updateSupplier:async(t,r)=>{try{let a=await s({supplierId:t,data:r}).unwrap();if(!a.success)throw Error(a.message||"Failed to update supplier");return(0,g.r)("success","Supplier updated successfully"),e&&e(),a.data}catch(e){throw console.error("Update supplier error:",e),(0,g.r)("error",e.message||"Failed to update supplier"),e}},isUpdating:t}};var V=t(11855);let q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var H=t(78480),$=a.forwardRef(function(e,s){return a.createElement(H.A,(0,V.A)({},e,{ref:s,icon:q}))});t(75528);let Y=({isOpen:e,onClose:s,onSuccess:t,supplier:l,currentUser:i})=>{let[d]=R.A.useForm(),c=!!l,{createSupplier:p,isSubmitting:x}=O(t),{updateSupplier:m,isUpdating:u}=U(t);(0,a.useEffect)(()=>{e&&(d.resetFields(),l&&d.setFieldsValue({name:l.name,email:l.email,phone:l.phone,address:l.address,contactPerson:l.contactPerson}))},[d,e,l]);let h=async e=>{try{c&&l?await m(l.id,e):await p(e)}catch(e){console.error("Failed to save supplier:",e)}},g=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,disabled:x||u,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(n.Ay,{type:"primary",loading:x||u,onClick:()=>d.submit(),children:c?"Update":"Save"})]});return(0,r.jsx)(B.A,{isOpen:e,onClose:s,title:c?"Edit Supplier":"Add Supplier",width:"500px",footer:g,children:(0,r.jsxs)("div",{className:"p-6 bg-white",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit Supplier: ",l?.name]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),"Add New Supplier"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:c?"Update supplier information":"Fill in the details to add a new supplier"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(R.A,{form:d,layout:"vertical",onFinish:h,className:"supplier-form",requiredMark:!0,children:[(0,r.jsx)(R.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"})," Supplier Name ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter supplier name"}],tooltip:"Name of the supplier company or individual",children:(0,r.jsx)(F.A,{placeholder:"Enter supplier name"})}),(0,r.jsx)(R.A.Item,{name:"phone",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"mr-1"})," Phone ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter phone number"}],tooltip:"Contact phone number for the supplier",children:(0,r.jsx)(F.A,{placeholder:"Enter phone number"})}),(0,r.jsx)(R.A.Item,{name:"email",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"})," Email"]}),rules:[{type:"email",message:"Please enter a valid email"}],tooltip:"Contact email for the supplier (optional)",children:(0,r.jsx)(F.A,{placeholder:"Enter email address (optional)"})}),(0,r.jsx)(R.A.Item,{name:"address",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)($,{className:"mr-1"})," Address"]}),tooltip:"Physical address of the supplier (optional)",children:(0,r.jsx)(F.A.TextArea,{placeholder:"Enter address (optional)",rows:3})}),(0,r.jsx)(R.A.Item,{name:"contactPerson",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-1"})," Contact Person"]}),tooltip:"Name of the primary contact person (optional)",children:(0,r.jsx)(F.A,{placeholder:"Enter contact person name (optional)"})}),(0,r.jsxs)("div",{className:"text-gray-500 text-sm mt-4 mb-2",children:[(0,r.jsx)("span",{className:"text-red-500",children:"*"})," Required fields"]})]})]})})};var G=t(12869);let J=({isOpen:e,onClose:s,supplierId:t,onEdit:a})=>{let l=(0,u.d4)(e=>e.auth.user),c=l?.role,{data:p,isLoading:m,error:h}=(0,x.w1)(t||0,{skip:!e||!t}),g=p?.data,f=!!g;console.log("Supplier detail - User ID:",l?.id),console.log("Supplier detail - Supplier:",g),console.log("Supplier detail - Can view supplier:",f);let y="admin"===c&&!!g,j=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),a&&g&&y&&(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>a(g.id),children:"Edit"})]});return(0,r.jsx)(B.A,{isOpen:e,onClose:s,title:"Supplier Details",width:"500px",footer:j,children:m?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(d.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):g&&f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(o.A,{className:"mr-2"}),"Supplier: ",g.name]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"Complete supplier information and details"})]}),(0,r.jsxs)(G.A,{bordered:!0,column:1,className:"supplier-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(o.A,{})," Supplier ID"]}),children:g.id}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(o.A,{})," Name"]}),children:g.name}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(w.A,{})," Email"]}),children:g.email||"N/A"}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(N.A,{})," Phone"]}),children:g.phone||"N/A"}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(o.A,{})," Contact Person"]}),children:g.contactPerson||"N/A"}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)($,{})," Address"]}),children:g.address||"N/A"}),(0,r.jsx)(G.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(C.A,{})," Created At"]}),children:(e=>{if(!e)return"N/A";try{return I()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}})(g.createdAt)})]})]}):g&&!f?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-red-500",children:'You don"t have permission to view this supplier.'})}):(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-gray-800",children:"No supplier data available"})})})};var W=t(58733);t(86078);let K=({searchTerm:e,setSearchTerm:s,isMobile:t=!1})=>(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(F.A,{placeholder:"Search by name, email, or phone...",prefix:(0,r.jsx)(W.A,{className:"text-gray-500"}),value:e,onChange:e=>{let t=e.target.value;console.log("Supplier search input changed:",t),s(t)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:t?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var X=t(51531);let Q=()=>{let{user:e}=(0,c.A)(),s=(0,p.a)(),[t,x]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),[g,j]=(0,a.useState)(!1),[b,A]=(0,a.useState)(null),[v,w]=(0,a.useState)(null),[N,C]=(0,a.useState)(!1),{suppliers:S,total:P,page:k,limit:E,isLoading:D,refetch:z,searchTerm:I,setSearchTerm:T,handlePageChange:_}=h();console.log("SuppliersPage - Current user:",e),console.log("SuppliersPage - Suppliers:",S),console.log("SuppliersPage - Total:",P);let{deleteSupplier:R,isDeleting:F}=f(()=>{C(!1),z()}),{bulkDeleteSuppliers:B,isDeleting:O}=y(()=>{V(!1),z()}),[U,V]=(0,a.useState)(!1),[q,H]=(0,a.useState)([]),$=e?.role==="admin",G=async()=>{v&&await R(v)},W=async()=>{if(console.log("confirmBulkDelete called with suppliers:",q),q.length>0)try{await B(q)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Supplier Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:s?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:$&&(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(o.A,{}),onClick:()=>{x(!0)},size:s?"small":"middle",children:s?"":"Add Supplier"}),children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,r.jsx)(K,{searchTerm:I,setSearchTerm:T,isMobile:s}),D?(0,r.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(d.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[S.length>0?(0,r.jsx)(M,{suppliers:S,loading:!1,onView:e=>{w(e),j(!0)},onEdit:e=>{A(e),u(!0)},onDelete:e=>{w(e),C(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with supplierIds:",e),H(e),V(!0)},isMobile:s}):(0,r.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No suppliers found matching your search criteria."}),(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>T(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,r.jsxs)("p",{children:["No suppliers found. ",$&&"Click 'Add Supplier' to create one."]})}),S.length>0&&(0,r.jsx)(L,{current:k,pageSize:E,total:P,onChange:_,isMobile:s})]})]})}),(0,r.jsx)(Y,{isOpen:t,onClose:()=>x(!1),onSuccess:()=>{x(!1),z()},currentUser:e}),(0,r.jsx)(Y,{isOpen:m,onClose:()=>u(!1),onSuccess:()=>{u(!1),z()},supplier:b,currentUser:e}),(0,r.jsx)(J,{isOpen:g,onClose:()=>{j(!1),w(null)},supplierId:v,onEdit:e=>{let s=S.find(s=>s.id===e)||null;s&&(A(s),j(!1),u(!0))}}),(0,r.jsx)(X.A,{isOpen:N,onClose:()=>{C(!1),w(null)},onConfirm:G,title:"Delete Supplier",message:"Are you sure you want to delete this supplier? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:F,type:"danger"}),(0,r.jsx)(X.A,{isOpen:U,onClose:()=>{V(!1),H([])},onConfirm:W,title:"Delete Multiple Suppliers",message:`Are you sure you want to delete ${q.length} suppliers? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:O,type:"danger"})]})}},51531:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(45512);t(58009);var a=t(88206),l=t(3117),n=t(75238);let i=({isOpen:e,onClose:s,onConfirm:t,title:i,message:o,confirmText:d="Confirm",cancelText:c="Cancel",isLoading:p=!1,type:x="danger"})=>(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{style:{color:"danger"===x?"#ff4d4f":"warning"===x?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:i})]}),open:e,onCancel:s,footer:[(0,r.jsx)(l.Ay,{onClick:s,disabled:p,children:c},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===x?"primary":"default",danger:"danger"===x,onClick:t,loading:p,children:d},"confirm")],maskClosable:!p,closable:!p,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:o})})},63844:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,Hj:()=>o,jB:()=>l,nA:()=>i});var r=t(45512);t(58009);var a=t(44195);let l=({children:e,columns:s,className:t,minWidth:l="800px"})=>(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",t),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0","block"),style:{},children:e})}),n=({children:e,className:s,sticky:t})=>(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",t&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[t],s),children:e}),i=({children:e,className:s,sticky:t})=>(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",t&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[t],s),children:e}),o=({children:e,className:s,selected:t=!1,onClick:l})=>(0,r.jsx)("div",{className:(0,a.cn)("contents",t&&"bg-blue-50",l&&"cursor-pointer hover:bg-gray-50",s),onClick:l,children:e})},98776:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(45512),a=t(58009),l=t(3117),n=t(97071);let i=({isOpen:e,onClose:s,title:t,children:i,width:o="400px",footer:d,fullWidth:c=!1})=>{let[p,x]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),[h,g]=(0,a.useState)(1024);if((0,a.useEffect)(()=>{let e=()=>{g(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",t),e)u(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{x(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{x(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{u(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,t]),!m)return null;let f="Point of Sale"===t||c||"100vw"===o;return(0,r.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${f?"sales-panel-container":""}`,children:[(0,r.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${p?"opacity-50":"opacity-0"}`,onClick:s}),(0,r.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${p?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===t||c||"100vw"===o||h<640?"100vw":h<1024?"500px":"string"==typeof o&&o.includes("px")&&parseInt(o)>600?"600px":o},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:t}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(n.A,{style:{color:"#333"}}),onClick:s,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:i}),d&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:d})]})]})}},60636:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(92273),a=t(25510),l=t(97245),n=t(42211);let i=()=>{let e=(0,r.wA)(),{user:s,accessToken:t}=(0,r.d4)(e=>e.auth),i=(0,a._)(),{refetch:o}=(0,l.$f)(s?.id||0,{skip:!s?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!s&&!!t,role:s?.role,phone:s?.phone,phoneType:s?.phone?typeof s.phone:"undefined/null",createdAt:s?.createdAt,createdAtType:s?.createdAt?typeof s.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(s,null,2));let d=!!s&&!!t,c=async()=>{if(!s?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",s.id);let r=await o();console.log("useAuth - Refetch result:",r);let a=r.data;if(a?.success&&a?.data){console.log("useAuth - API response data:",a.data);let r=s.paymentStatus;s.lastPaymentDate,s.nextPaymentDue;let l=a.data.phone||s.phone||"",i=a.data.createdAt||s.createdAt||"",o=a.data.lastPaymentDate||s.lastPaymentDate||void 0,d=a.data.nextPaymentDue||s.nextPaymentDue||null,c=a.data.createdBy||s.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:s.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:s.createdAt,finalCreatedAt:i,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:s.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:s.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:a.data.createdBy,userCreatedBy:s.createdBy,finalCreatedBy:c});let p={...a.data,phone:l,createdAt:i,lastPaymentDate:o,nextPaymentDue:d,createdBy:c,paymentStatus:r};console.log("useAuth - Updating Redux store with:",p),console.log("useAuth - Using current access token:",t?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:p,accessToken:t||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",a?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:s,accessToken:t,isAuthenticated:d,hasRole:e=>!!s&&(Array.isArray(e)?e.includes(s.role):s.role===e),isSuperAdmin:()=>s?.role==="superadmin",isAdmin:()=>s?.role==="admin",isCashier:()=>s?.role==="cashier",needsPayment:()=>!!s&&"superadmin"!==s.role&&i.needsPayment,paymentStatus:i,refreshUser:c}}},78337:(e,s,t)=>{"use strict";t.d(s,{d:()=>a});var r=t(58009);function a(e,s){let[t,a]=(0,r.useState)(e);return t}},73542:(e,s,t)=>{"use strict";t.d(s,{E:()=>a});var r=t(58009);let a=()=>{let[e,s]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{s(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},67520:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\suppliers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\suppliers\\page.tsx","default")},75528:()=>{},86078:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,5050,1785,5482,106,4286],()=>t(8784));module.exports=r})();