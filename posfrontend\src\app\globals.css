@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* Light mode is always used */

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Override Ant Design input hover styles */
.ant-input,
.ant-input:hover,
.ant-input:focus,
.ant-input-focused,
.ant-input-affix-wrapper,
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused,
.ant-input-number,
.ant-input-number:hover,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-picker,
.ant-picker:hover,
.ant-picker-focused,
.ant-textarea,
.ant-textarea:hover,
.ant-textarea:focus,
.ant-textarea-focused {
  background-color: white !important;
  color: #333 !important;
  border-color: #d9d9d9 !important;
}

.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: #333 !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.65) !important;
}

/* Override Ant Design input placeholder color */
.ant-input::placeholder,
.ant-textarea::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Fix for input number controls */
.ant-input-number-handler-wrap {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.ant-input-number-handler-up,
.ant-input-number-handler-down {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: #1890ff !important;
}

/* Light theme for Descriptions component */
.product-detail-dark .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.product-detail-dark .ant-descriptions-item-content {
  background-color: white !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.product-detail-dark .ant-descriptions-view {
  border-color: #e8e8e8 !important;
}

/* Fix hover styles for table buttons */
.view-button:hover,
.edit-button:hover,
.delete-button:hover {
  background-color: transparent !important;
}

/* Light theme for user stores table */
.user-stores-table .ant-table {
  background-color: white !important;
  color: #333 !important;
}

.user-stores-table .ant-table-thead > tr > th {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.user-stores-table .ant-table-tbody > tr > td {
  background-color: white !important;
  color: #333 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.user-stores-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

.user-stores-table .ant-empty-description {
  color: #333 !important;
}

/* Light theme for all forms */
.ant-form-item-label > label {
  color: #333 !important;
}

/* Select component styling */
.ant-select:not(.ant-select-disabled) .ant-select-selector,
.ant-select:not(.ant-select-disabled):hover .ant-select-selector,
.ant-select-focused:not(.ant-select-disabled) .ant-select-selector {
  background-color: white !important;
  border-color: #d9d9d9 !important;
  color: #333 !important;
  box-shadow: none !important;
}

.ant-select-selection-item {
  color: #333 !important;
}

.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-select-dropdown {
  background-color: white !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-select-item {
  color: #333 !important;
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #f5f5f5 !important;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #e6f7ff !important;
}

/* Switch component styling */
.ant-switch {
  background-color: rgba(0, 0, 0, 0.25) !important;
}

.ant-switch-checked {
  background-color: #1890ff !important;
}

/* Button hover states */
.ant-btn:not(.ant-btn-primary):not(.ant-btn-dangerous):hover {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #333 !important;
}

.ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.ant-btn-dangerous:hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
}

/* TextArea styling */
.ant-input-textarea-show-count::after {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Light theme for empty state */
.ant-empty-description {
  color: rgba(0, 0, 0, 0.65) !important;
}

/* Additional form element fixes for hover/focus states */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input:hover,
.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-affix-wrapper,
.ant-form-item-has-error .ant-input-affix-wrapper:hover,
.ant-form-item-has-error .ant-input-affix-wrapper:focus {
  background-color: white !important;
  border-color: #ff4d4f !important;
}

.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-input-number:hover,
.ant-form-item-has-error .ant-input-number:focus {
  background-color: white !important;
  border-color: #ff4d4f !important;
}

.ant-form-item-has-error .ant-select:not(.ant-select-disabled) .ant-select-selector,
.ant-form-item-has-error .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  background-color: white !important;
  border-color: #ff4d4f !important;
}

/* Fix for form validation error messages */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
}

/* Fix for form item tooltip hover */
.ant-form-item-tooltip {
  color: rgba(0, 0, 0, 0.65) !important;
}

/* Fix for disabled form elements */
.ant-input[disabled],
.ant-input-affix-wrapper-disabled,
.ant-input-number-disabled,
.ant-select-disabled .ant-select-selector {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
}
