import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import compression from "compression";
import router from './src/routes/route';
import {startCronJobs} from "./src/cron/cronJobs";
import { errorHandler } from './src/middleware/errorHandler';
import { setupSwagger } from './src/config/swagger';
import events from 'events';
import http from 'http';
import WebSocket, { WebSocketServer } from 'ws';
dotenv.config();

// Increase the maximum number of listeners to prevent memory leak warnings
events.defaultMaxListeners = 20;

// Initialize Express app
const app = express();
const port = process.env.PORT || 5005;

// Enable trust proxy for proper IP detection behind reverse proxies
app.set('trust proxy', 1);

// 🔥 Optimize JSON parsing with size limits to prevent large payload attacks
app.use(express.json({
  limit: '1mb',
  strict: true
}));

// 🔥 Optimize URL-encoded parsing
app.use(express.urlencoded({
  extended: true,
  limit: '1mb'
}));

// 🔥 Enhanced compression for better performance
app.use(compression({
  level: 6, // Balanced compression level (0-9, where 9 is max compression but slower)
  threshold: 1024, // Only compress responses larger than 1KB
  filter: (req, res) => {
    // Don't compress responses with this header
    if (req.headers['x-no-compression']) {
      return false;
    }
    // Use compression filter function from the middleware
    return compression.filter(req, res);
  }
}));

// 🔥 Optimized rate limiter with higher limits for API endpoints
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Increased limit for better performance under load
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests, please try again later.',
  skip: (req) => {
    // Skip rate limiting for health check endpoint
    return req.path === '/health';
  }
});

// CORS configuration
const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://nexapo.vercel.app'
  ],
  methods: ['GET', 'POST', 'OPTIONS'], // Added OPTIONS for preflight requests
  allowedHeaders: [
    'Authorization',
    'X-User-Role',
    'Content-Type',
    'Accept',
  ],
  credentials: true, // Allow credentials (cookies, authorization headers)
  maxAge: 86400, // Cache preflight response for 24 hours
};

// Apply middleware in optimal order (performance-sensitive first)
app.use(cors(corsOptions));
app.options('*', cors(corsOptions)); // Handle OPTIONS preflight requests
app.use(cookieParser());
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin resource sharing
}));
app.use(limiter);

// API routes
app.use('/api/v1', router);

// Setup Swagger API documentation
setupSwagger(app);

// Health check endpoint for monitoring
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Backend service is running'
  });
});

// Root endpoint
app.get('/', (_req, res) => {
  res.status(200).send('OK');
});

// 404 handler - must be before the error handler
app.use((req, res, _next) => {
  res.status(404).json({
    success: false,
    message: `Route not found: ${req.method} ${req.originalUrl}`
  });
});

// Global error handler - must be the last middleware
app.use(errorHandler);

// Process-level error handling
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION:', error);
  // Log to monitoring service if available
});

process.on('unhandledRejection', (reason, _promise) => {
  console.error('UNHANDLED REJECTION:', reason); 
  // Log to monitoring service if available
});

// Start cron jobs
startCronJobs();

// Start the server with error handling
const server = http.createServer(app);
server.listen(port, () => {
  console.log(`Server is running at https://nexapoapi.up.railway.app`);
});

// --- WebSocket Server Setup ---
const wss = new WebSocketServer({ server });
const userSockets: Map<number, WebSocket> = new Map(); // userId -> ws

wss.on('connection', (ws, req) => {
  let authedUserId: number | null = null;
  ws.on('message', (msg: WebSocket.RawData) => {
    try {
      const data = JSON.parse(msg.toString());
      if (data.type === 'auth' && typeof data.userId === 'number') {
        authedUserId = data.userId;
        userSockets.set(data.userId, ws);
        console.log(`[WebSocket] User ${data.userId} authenticated and connected.`);
      }
    } catch (e) {
      console.warn('[WebSocket] Failed to parse message:', msg);
    }
  });
  ws.on('close', () => {
    if (typeof authedUserId === 'number') {
      const userIdNum: number = authedUserId;
      userSockets.delete(userIdNum);
      console.log(`[WebSocket] User ${userIdNum} disconnected.`);
    }
  });
});

export function notifyUserPaymentStatus(userId: number, newStatus: 'paid' | 'pending' | 'overdue' | 'inactive') {
  const ws = userSockets.get(userId);
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({ type: 'paymentStatus', status: newStatus }));
    console.log(`[WebSocket] Sent paymentStatus '${newStatus}' to user ${userId}`);
  }
}

export default app;
