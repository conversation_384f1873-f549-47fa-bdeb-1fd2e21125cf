import { Request, Response, NextFunction } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createProduct,
  getAllProducts,
  getProductById,
  getProductByBarcode,
  updateProductById,
  deleteProductById,
} from "../services/productService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";
import { asyncHandler, ValidationError, NotFoundError } from "../middleware/errorHandler";

export const handleProductRequest = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const { mode, productId, page, limit, productsData, barcode } = req.body;
  const requester = req.user as DecodedToken;

  const validModes = ["createnew", "update", "delete", "retrieve", "barcode"];
  if (!validateMode(res, mode, validModes)) return;

  switch (mode) {
    case "createnew": {
      if (!Array.isArray(productsData) || productsData.length === 0) {
        throw new ValidationError("Provide at least one product.");
      }

      const newProducts = await createProduct(requester, productsData);
      return sendResponse(res, 201, true, "Products created successfully.", newProducts);
    }

    case "retrieve": {
      if (productId) {
        const product = await getProductById(requester, Number(productId));
        if (!product) {
          throw new NotFoundError("Product not found.");
        }
        return sendResponse(res, 200, true, "Product retrieved successfully.", product);
      } else {
        const pageNum = Number(page) || 1;
        const limitNum = Number(limit) || 10;
        const { search } = req.body;

        if (pageNum < 1 || limitNum < 1) {
          throw new ValidationError("Invalid pagination values.");
        }

        console.log("getAllProducts request:", {
          requester: { id: requester.id, role: requester.role },
          page: pageNum,
          limit: limitNum,
          search: search || 'none'
        });

        const products = await getAllProducts(requester, pageNum, limitNum, search);
        return sendResponse(res, 200, true, "Products retrieved successfully.", products);
      }
    }

    case "update": {
      if (!productId) {
        throw new ValidationError("Product ID is required for updating.");
      }

      const updatedProduct = await updateProductById(requester, Number(productId), req.body);
      return sendResponse(res, 200, true, "Product updated successfully.", updatedProduct);
    }

    case "delete": {
      // Check if we have productIds (array) or productId (single)
      const { productIds } = req.body;

      if (!productId && !productIds) {
        throw new ValidationError("Product ID(s) are required for deletion.");
      }

      // Use productIds if provided, otherwise use single productId
      const idsToDelete = productIds
        ? (Array.isArray(productIds) ? productIds : [productIds])
        : [Number(productId)];

      const result = await deleteProductById(requester, idsToDelete);

      const message = idsToDelete.length > 1
        ? `${idsToDelete.length} products deleted successfully.`
        : "Product deleted successfully.";

      return sendResponse(res, 200, true, message, result);
    }

    case "barcode": {
      if (!barcode || typeof barcode !== 'string') {
        throw new ValidationError("Barcode is required for barcode search.");
      }

      const product = await getProductByBarcode(requester, barcode.trim());

      if (!product) {
        return sendResponse(res, 404, false, "Product not found for the given barcode.", null);
      }

      return sendResponse(res, 200, true, "Product found by barcode.", product);
    }

    default:
      throw new ValidationError("Unexpected error occurred.");
  }
});
