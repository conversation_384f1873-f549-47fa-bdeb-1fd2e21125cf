(()=>{var A={};A.id=3230,A.ids=[3230],A.modules={10846:A=>{"use strict";A.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:A=>{"use strict";A.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:A=>{"use strict";A.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:A=>{"use strict";A.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:A=>{"use strict";A.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},10050:(A,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>b,routeModule:()=>s,serverHooks:()=>l,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>u});var r={};i.r(r),i.d(r,{GET:()=>F,dynamic:()=>V});var t=i(42706),n=i(28203),o=i(45994),f=i(41520);let a=Buffer.from("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","base64");function F(){return new f.NextResponse(a,{headers:{"Content-Type":"image/x-icon","Cache-Control":"public, max-age=0, must-revalidate"}})}let V="force-static",s=new t.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/favicon.ico/route",pathname:"/favicon.ico",filename:"favicon",bundlePath:"app/favicon.ico/route"},resolvedPagePath:"next-metadata-route-loader?filePath=E%3A%5CPROJECTS%5Cpos%5Cposfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:u,serverHooks:l}=s;function b(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:u})}},64446:(A,e,i)=>{var r;(()=>{var t={226:function(t,n){!function(o,f){"use strict";var a="function",F="undefined",V="object",s="string",x="major",u="model",l="name",b="type",d="vendor",c="version",v="architecture",X="console",w="mobile",B="tablet",U="smarttv",p="wearable",m="embedded",Q="Amazon",h="Apple",g="ASUS",P="BlackBerry",W="Browser",y="Chrome",j="Firefox",k="Google",O="Huawei",Z="Microsoft",Y="Motorola",z="Opera",q="Samsung",G="Sharp",R="Sony",H="Xiaomi",C="Zebra",N="Facebook",S="Chromium OS",T="Mac OS",D=function(A,e){var i={};for(var r in A)e[r]&&e[r].length%2==0?i[r]=e[r].concat(A[r]):i[r]=A[r];return i},J=function(A){for(var e={},i=0;i<A.length;i++)e[A[i].toUpperCase()]=A[i];return e},I=function(A,e){return typeof A===s&&-1!==E(e).indexOf(E(A))},E=function(A){return A.toLowerCase()},M=function(A,e){if(typeof A===s)return A=A.replace(/^\s\s*/,""),typeof e===F?A:A.substring(0,350)},_=function(A,e){for(var i,r,t,n,o,F,s=0;s<e.length&&!o;){var x=e[s],u=e[s+1];for(i=r=0;i<x.length&&!o&&x[i];)if(o=x[i++].exec(A))for(t=0;t<u.length;t++)F=o[++r],typeof(n=u[t])===V&&n.length>0?2===n.length?typeof n[1]==a?this[n[0]]=n[1].call(this,F):this[n[0]]=n[1]:3===n.length?typeof n[1]!==a||n[1].exec&&n[1].test?this[n[0]]=F?F.replace(n[1],n[2]):void 0:this[n[0]]=F?n[1].call(this,F,n[2]):void 0:4===n.length&&(this[n[0]]=F?n[3].call(this,F.replace(n[1],n[2])):void 0):this[n]=F||f;s+=2}},L=function(A,e){for(var i in e)if(typeof e[i]===V&&e[i].length>0){for(var r=0;r<e[i].length;r++)if(I(e[i][r],A))return"?"===i?f:i}else if(I(e[i],A))return"?"===i?f:i;return A},K={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},$={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[c,[l,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[c,[l,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[l,c],[/opios[\/ ]+([\w\.]+)/i],[c,[l,z+" Mini"]],[/\bopr\/([\w\.]+)/i],[c,[l,z]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[l,c],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[c,[l,"UC"+W]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[c,[l,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[c,[l,"WeChat"]],[/konqueror\/([\w\.]+)/i],[c,[l,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[c,[l,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[c,[l,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[l,/(.+)/,"$1 Secure "+W],c],[/\bfocus\/([\w\.]+)/i],[c,[l,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[c,[l,z+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[c,[l,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[c,[l,"Dolphin"]],[/coast\/([\w\.]+)/i],[c,[l,z+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[c,[l,"MIUI "+W]],[/fxios\/([-\w\.]+)/i],[c,[l,j]],[/\bqihu|(qi?ho?o?|360)browser/i],[[l,"360 "+W]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[l,/(.+)/,"$1 "+W],c],[/(comodo_dragon)\/([\w\.]+)/i],[[l,/_/g," "],c],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[l,c],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[l],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[l,N],c],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[l,c],[/\bgsa\/([\w\.]+) .*safari\//i],[c,[l,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[c,[l,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[c,[l,y+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[l,y+" WebView"],c],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[c,[l,"Android "+W]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[l,c],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[c,[l,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[c,l],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[l,[c,L,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[l,c],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[l,"Netscape"],c],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[c,[l,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[l,c],[/(cobalt)\/([\w\.]+)/i],[l,[c,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,E]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",E]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,E]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[d,q],[b,B]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[d,q],[b,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[d,h],[b,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[d,h],[b,B]],[/(macintosh);/i],[u,[d,h]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[d,G],[b,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[d,O],[b,B]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[d,O],[b,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[d,H],[b,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[d,H],[b,B]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[d,"OPPO"],[b,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[d,"Vivo"],[b,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[u,[d,"Realme"],[b,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[d,Y],[b,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[d,Y],[b,B]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[d,"LG"],[b,B]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[d,"LG"],[b,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[d,"Lenovo"],[b,B]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[d,"Nokia"],[b,w]],[/(pixel c)\b/i],[u,[d,k],[b,B]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[d,k],[b,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[d,R],[b,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[d,R],[b,B]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[d,"OnePlus"],[b,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[d,Q],[b,B]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[d,Q],[b,w]],[/(playbook);[-\w\),; ]+(rim)/i],[u,d,[b,B]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[d,P],[b,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[d,g],[b,B]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[d,g],[b,w]],[/(nexus 9)/i],[u,[d,"HTC"],[b,B]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[d,[u,/_/g," "],[b,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[d,"Acer"],[b,B]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[d,"Meizu"],[b,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[d,u,[b,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[d,u,[b,B]],[/(surface duo)/i],[u,[d,Z],[b,B]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[d,"Fairphone"],[b,w]],[/(u304aa)/i],[u,[d,"AT&T"],[b,w]],[/\bsie-(\w*)/i],[u,[d,"Siemens"],[b,w]],[/\b(rct\w+) b/i],[u,[d,"RCA"],[b,B]],[/\b(venue[\d ]{2,7}) b/i],[u,[d,"Dell"],[b,B]],[/\b(q(?:mv|ta)\w+) b/i],[u,[d,"Verizon"],[b,B]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[d,"Barnes & Noble"],[b,B]],[/\b(tm\d{3}\w+) b/i],[u,[d,"NuVision"],[b,B]],[/\b(k88) b/i],[u,[d,"ZTE"],[b,B]],[/\b(nx\d{3}j) b/i],[u,[d,"ZTE"],[b,w]],[/\b(gen\d{3}) b.+49h/i],[u,[d,"Swiss"],[b,w]],[/\b(zur\d{3}) b/i],[u,[d,"Swiss"],[b,B]],[/\b((zeki)?tb.*\b) b/i],[u,[d,"Zeki"],[b,B]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[d,"Dragon Touch"],u,[b,B]],[/\b(ns-?\w{0,9}) b/i],[u,[d,"Insignia"],[b,B]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[d,"NextBook"],[b,B]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[d,"Voice"],u,[b,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[d,"LvTel"],u,[b,w]],[/\b(ph-1) /i],[u,[d,"Essential"],[b,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[d,"Envizen"],[b,B]],[/\b(trio[-\w\. ]+) b/i],[u,[d,"MachSpeed"],[b,B]],[/\btu_(1491) b/i],[u,[d,"Rotor"],[b,B]],[/(shield[\w ]+) b/i],[u,[d,"Nvidia"],[b,B]],[/(sprint) (\w+)/i],[d,u,[b,w]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[d,Z],[b,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[d,C],[b,B]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[d,C],[b,w]],[/smart-tv.+(samsung)/i],[d,[b,U]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[d,q],[b,U]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[d,"LG"],[b,U]],[/(apple) ?tv/i],[d,[u,h+" TV"],[b,U]],[/crkey/i],[[u,y+"cast"],[d,k],[b,U]],[/droid.+aft(\w)( bui|\))/i],[u,[d,Q],[b,U]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[d,G],[b,U]],[/(bravia[\w ]+)( bui|\))/i],[u,[d,R],[b,U]],[/(mitv-\w{5}) bui/i],[u,[d,H],[b,U]],[/Hbbtv.*(technisat) (.*);/i],[d,u,[b,U]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[d,M],[u,M],[b,U]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[b,U]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[d,u,[b,X]],[/droid.+; (shield) bui/i],[u,[d,"Nvidia"],[b,X]],[/(playstation [345portablevi]+)/i],[u,[d,R],[b,X]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[d,Z],[b,X]],[/((pebble))app/i],[d,u,[b,p]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[d,h],[b,p]],[/droid.+; (glass) \d/i],[u,[d,k],[b,p]],[/droid.+; (wt63?0{2,3})\)/i],[u,[d,C],[b,p]],[/(quest( 2| pro)?)/i],[u,[d,N],[b,p]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[d,[b,m]],[/(aeobc)\b/i],[u,[d,Q],[b,m]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[u,[b,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[b,B]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[b,B]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[b,w]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[d,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[c,[l,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[c,[l,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[l,c],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[c,l]],os:[[/microsoft (windows) (vista|xp)/i],[l,c],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[l,[c,L,K]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[l,"Windows"],[c,L,K]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[c,/_/g,"."],[l,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[l,T],[c,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[c,l],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[l,c],[/\(bb(10);/i],[c,[l,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[c,[l,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[c,[l,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[c,[l,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[c,[l,"watchOS"]],[/crkey\/([\d\.]+)/i],[c,[l,y+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[l,S],c],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[l,c],[/(sunos) ?([\w\.\d]*)/i],[[l,"Solaris"],c],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[l,c]]},AA=function(A,e){if(typeof A===V&&(e=A,A=f),!(this instanceof AA))return new AA(A,e).getResult();var i=typeof o!==F&&o.navigator?o.navigator:f,r=A||(i&&i.userAgent?i.userAgent:""),t=i&&i.userAgentData?i.userAgentData:f,n=e?D($,e):$,X=i&&i.userAgent==r;return this.getBrowser=function(){var A,e={};return e[l]=f,e[c]=f,_.call(e,r,n.browser),e[x]=typeof(A=e[c])===s?A.replace(/[^\d\.]/g,"").split(".")[0]:f,X&&i&&i.brave&&typeof i.brave.isBrave==a&&(e[l]="Brave"),e},this.getCPU=function(){var A={};return A[v]=f,_.call(A,r,n.cpu),A},this.getDevice=function(){var A={};return A[d]=f,A[u]=f,A[b]=f,_.call(A,r,n.device),X&&!A[b]&&t&&t.mobile&&(A[b]=w),X&&"Macintosh"==A[u]&&i&&typeof i.standalone!==F&&i.maxTouchPoints&&i.maxTouchPoints>2&&(A[u]="iPad",A[b]=B),A},this.getEngine=function(){var A={};return A[l]=f,A[c]=f,_.call(A,r,n.engine),A},this.getOS=function(){var A={};return A[l]=f,A[c]=f,_.call(A,r,n.os),X&&!A[l]&&t&&"Unknown"!=t.platform&&(A[l]=t.platform.replace(/chrome os/i,S).replace(/macos/i,T)),A},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(A){return r=typeof A===s&&A.length>350?M(A,350):A,this},this.setUA(r),this};AA.VERSION="1.0.35",AA.BROWSER=J([l,c,x]),AA.CPU=J([v]),AA.DEVICE=J([u,d,b,X,w,U,B,p,m]),AA.ENGINE=AA.OS=J([l,c]),typeof n!==F?(t.exports&&(n=t.exports=AA),n.UAParser=AA):i.amdO?void 0!==(r=(function(){return AA}).call(e,i,e,A))&&(A.exports=r):typeof o!==F&&(o.UAParser=AA);var Ae=typeof o!==F&&(o.jQuery||o.Zepto);if(Ae&&!Ae.ua){var Ai=new AA;Ae.ua=Ai.getResult(),Ae.ua.get=function(){return Ai.getUA()},Ae.ua.set=function(A){Ai.setUA(A);var e=Ai.getResult();for(var i in e)Ae.ua[i]=e[i]}}}("object"==typeof window?window:this)}},n={};function o(A){var e=n[A];if(void 0!==e)return e.exports;var i=n[A]={exports:{}},r=!0;try{t[A].call(i.exports,i,i.exports,o),r=!1}finally{r&&delete n[A]}return i.exports}o.ab=__dirname+"/";var f=o(226);A.exports=f})()},46050:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"after",{enumerable:!0,get:function(){return t}});let r=i(29294);function t(A){let e=r.workAsyncStorage.getStore();if(!e)throw Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context");let{afterContext:i}=e;return i.after(A)}},95036:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){Object.keys(A).forEach(function(i){"default"===i||Object.prototype.hasOwnProperty.call(e,i)||Object.defineProperty(e,i,{enumerable:!0,get:function(){return A[i]}})})}(i(46050),e)},26807:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"connection",{enumerable:!0,get:function(){return F}});let r=i(29294),t=i(63033),n=i(10436),o=i(82312),f=i(60457),a=i(24982);function F(){let A=r.workAsyncStorage.getStore(),e=t.workUnitAsyncStorage.getStore();if(A){if(e&&"after"===e.phase&&!(0,a.isRequestAPICallableInsideAfter)())throw Error(`Route ${A.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(A.forceStatic)return Promise.resolve(void 0);if(e){if("cache"===e.type)throw Error(`Route ${A.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===e.type)throw Error(`Route ${A.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(A.dynamicShouldError)throw new o.StaticGenBailoutError(`Route ${A.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e){if("prerender"===e.type)return(0,f.makeHangingPromise)(e.renderSignal,"`connection()`");"prerender-ppr"===e.type?(0,n.postponeWithTracking)(A.route,"connection",e.dynamicTracking):"prerender-legacy"===e.type&&(0,n.throwToInterruptStaticGeneration)("connection",A,e)}(0,n.trackDynamicDataInDynamicRender)(A,e)}return Promise.resolve(void 0)}},42706:(A,e,i)=>{"use strict";A.exports=i(44870)},41520:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var i in e)Object.defineProperty(A,i,{enumerable:!0,get:e[i]})}(e,{ImageResponse:function(){return r.ImageResponse},NextRequest:function(){return t.NextRequest},NextResponse:function(){return n.NextResponse},URLPattern:function(){return f.URLPattern},after:function(){return a.after},connection:function(){return F.connection},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let r=i(14159),t=i(41639),n=i(44899),o=i(22215),f=i(31512),a=i(95036),F=i(26807)},14159:(A,e)=>{"use strict";function i(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageResponse",{enumerable:!0,get:function(){return i}})},44899:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NextResponse",{enumerable:!0,get:function(){return s}});let r=i(9181),t=i(49619),n=i(55225),o=i(20614),f=i(9181),a=Symbol("internal response"),F=new Set([301,302,303,307,308]);function V(A,e){var i;if(null==A?void 0:null==(i=A.request)?void 0:i.headers){if(!(A.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let i=[];for(let[r,t]of A.request.headers)e.set("x-middleware-request-"+r,t),i.push(r);e.set("x-middleware-override-headers",i.join(","))}}class s extends Response{constructor(A,e={}){super(A,e);let i=this.headers,F=new Proxy(new f.ResponseCookies(i),{get(A,t,n){switch(t){case"delete":case"set":return(...n)=>{let o=Reflect.apply(A[t],A,n),a=new Headers(i);return o instanceof f.ResponseCookies&&i.set("x-middleware-set-cookie",o.getAll().map(A=>(0,r.stringifyCookie)(A)).join(",")),V(e,a),o};default:return o.ReflectAdapter.get(A,t,n)}}});this[a]={cookies:F,url:e.url?new t.NextURL(e.url,{headers:(0,n.toNodeOutgoingHttpHeaders)(i),nextConfig:e.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[a].cookies}static json(A,e){let i=Response.json(A,e);return new s(i.body,i)}static redirect(A,e){let i="number"==typeof e?e:(null==e?void 0:e.status)??307;if(!F.has(i))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let r="object"==typeof e?e:{},t=new Headers(null==r?void 0:r.headers);return t.set("Location",(0,n.validateURL)(A)),new s(null,{...r,headers:t,status:i})}static rewrite(A,e){let i=new Headers(null==e?void 0:e.headers);return i.set("x-middleware-rewrite",(0,n.validateURL)(A)),V(e,i),new s(null,{...e,headers:i})}static next(A){let e=new Headers(null==A?void 0:A.headers);return e.set("x-middleware-next","1"),V(A,e),new s(null,{...A,headers:e})}}},31512:(A,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"URLPattern",{enumerable:!0,get:function(){return i}});let i="undefined"==typeof URLPattern?void 0:URLPattern},22215:(A,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var i in e)Object.defineProperty(A,i,{enumerable:!0,get:e[i]})}(e,{isBot:function(){return t},userAgent:function(){return o},userAgentFromString:function(){return n}});let r=function(A){return A&&A.__esModule?A:{default:A}}(i(64446));function t(A){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(A)}function n(A){return{...(0,r.default)(A),isBot:void 0!==A&&t(A)}}function o({headers:A}){return n(A.get("user-agent")||void 0)}}};var e=require("../../webpack-runtime.js");e.C(A);var i=A=>e(e.s=A),r=e.X(0,[638],()=>i(10050));module.exports=r})();