// services/stockAdjustmentApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define StockAdjustment types
export interface StockAdjustment {
  id: number;
  productId: number;
  quantityChange: number;
  reason: string;
  adjustedBy: number;
  createdBy: number;
  createdAt: string;
}

export interface PaginatedStockAdjustments {
  total: number;
  page: number;
  perPage: number;
  stockAdjustments: StockAdjustment[];
}

export interface CreateStockAdjustmentDto {
  productId: number;
  quantityChange: number;
  reason: string;
}

export interface UpdateStockAdjustmentDto {
  quantityChange?: number;
  reason?: string;
}

export const stockAdjustmentApi = createApi({
  reducerPath: 'stockAdjustmentApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['StockAdjustment'] as const,
  endpoints: (builder) => ({
    // Get all stock adjustments (paginated)
    getAllStockAdjustments: builder.query<ApiResponse<PaginatedStockAdjustments>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stockadjustment',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(),
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['StockAdjustment'],
    }),

    // Get stock adjustment by ID
    getStockAdjustmentById: builder.query<ApiResponse<StockAdjustment>, number>({
      query: (adjustmentId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stockadjustment',
          payloaddata: {
            mode: 'retrieve',
            adjustmentId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'StockAdjustment', id }],
    }),

    // Create new stock adjustment
    createStockAdjustment: builder.mutation<ApiResponse<StockAdjustment>, CreateStockAdjustmentDto>({
      query: (adjustmentData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stockadjustment',
          payloaddata: {
            mode: 'createnew',
            ...adjustmentData,
          },
          token,
        };
      },
      // Invalidate StockAdjustment tags to refresh data
      invalidatesTags: ['StockAdjustment'],
    }),

    // Update stock adjustment
    updateStockAdjustment: builder.mutation<ApiResponse<StockAdjustment>, { adjustmentId: number; data: UpdateStockAdjustmentDto }>({
      query: ({ adjustmentId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stockadjustment',
          payloaddata: {
            mode: 'update',
            adjustmentId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { adjustmentId }) => [
        { type: 'StockAdjustment', id: adjustmentId },
        'StockAdjustment',
      ],
    }),

    // Delete stock adjustment
    deleteStockAdjustment: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (adjustmentId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/stockadjustment',
          payloaddata: {
            mode: 'delete',
            adjustmentId,
          },
          token,
        };
      },
      invalidatesTags: ['StockAdjustment'],
    }),
  }),
});

export const {
  useGetAllStockAdjustmentsQuery,
  useGetStockAdjustmentByIdQuery,
  useCreateStockAdjustmentMutation,
  useUpdateStockAdjustmentMutation,
  useDeleteStockAdjustmentMutation,
} = stockAdjustmentApi;
