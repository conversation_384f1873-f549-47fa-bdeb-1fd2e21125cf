"use client";

import React, { useState, useEffect } from "react";
import { OverviewCard } from "./card";
import * as icons from "./icons";
import { compactFormat } from "@/lib/format-number";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

// Client-side version of OverviewCardsGroup
export function ClientOverviewCards() {
  const [data, setData] = useState<{
    views: { value: number; growthRate: number };
    profit: { value: number; growthRate: number };
    products: { value: number; growthRate: number };
    users: { value: number; growthRate: number };
  } | null>(null);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch data on the client side
    const fetchData = async () => {
      try {
        // Simulate API call with timeout
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data (same as in fetch.ts)
        const mockData = {
          views: {
            value: 3456,
            growthRate: 0.43,
          },
          profit: {
            value: 4220,
            growthRate: 4.35,
          },
          products: {
            value: 3456,
            growthRate: 2.59,
          },
          users: {
            value: 3456,
            growthRate: -0.95,
          },
        };

        setData(mockData);
      } catch (error) {
        console.error("Error fetching overview data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading || !data) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 min-h-[200px] items-center justify-center w-full overflow-hidden">
        <div className="col-span-full flex justify-center">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      </div>
    );
  }

  const { views, profit, products, users } = data;

  return (
    <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5 w-full overflow-hidden">
      <OverviewCard
        label="Total Views"
        data={{
          ...views,
          value: compactFormat(views.value),
        }}
        Icon={icons.Views}
      />

      <OverviewCard
        label="Total Profit"
        data={{
          ...profit,
          value: "$" + compactFormat(profit.value),
        }}
        Icon={icons.Profit}
      />

      <OverviewCard
        label="Total Products"
        data={{
          ...products,
          value: compactFormat(products.value),
        }}
        Icon={icons.Product}
      />

      <OverviewCard
        label="Total Users"
        data={{
          ...users,
          value: compactFormat(users.value),
        }}
        Icon={icons.Users}
      />
    </div>
  );
}
