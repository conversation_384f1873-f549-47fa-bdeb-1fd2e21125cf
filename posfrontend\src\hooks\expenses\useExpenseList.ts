import { useState, useEffect } from 'react';
import { useGetAllExpensesQuery } from '@/reduxRTK/services/expenseApi';

interface UseExpenseListProps {
  initialPage?: number;
  initialLimit?: number;
  initialSearch?: string;
  categoryFilter?: number;
  dateRange?: [string, string] | null;
}

export const useExpenseList = ({
  initialPage = 1,
  initialLimit = 10,
  initialSearch = '',
  categoryFilter,
  dateRange,
}: UseExpenseListProps = {}) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [search, setSearch] = useState(initialSearch);

  // Prepare query parameters
  const queryParams = {
    page,
    limit,
    search: search.trim(),
    categoryId: categoryFilter,
    startDate: dateRange?.[0],
    endDate: dateRange?.[1],
  };

  // Use the RTK Query hook
  const {
    data: expensesData,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useGetAllExpensesQuery(queryParams);

  const expenses = expensesData?.data?.expenses || [];
  const total = expensesData?.data?.total || 0;
  const totalPages = expensesData?.data?.totalPages || 0;

  // Reset page when search or filters change
  useEffect(() => {
    setPage(1);
  }, [search, categoryFilter, dateRange]);

  const handlePageChange = (newPage: number, newPageSize?: number) => {
    setPage(newPage);
    if (newPageSize && newPageSize !== limit) {
      setLimit(newPageSize);
    }
  };

  const handleSearch = (searchTerm: string) => {
    setSearch(searchTerm);
    setPage(1); // Reset to first page when searching
  };

  const resetFilters = () => {
    setSearch('');
    setPage(1);
  };

  return {
    // Data
    expenses,
    total,
    totalPages,
    
    // Loading states
    isLoading,
    isFetching,
    error,
    
    // Current state
    page,
    limit,
    search,
    
    // Actions
    handlePageChange,
    handleSearch,
    resetFilters,
    refetch,
    
    // Setters for direct control
    setPage,
    setLimit,
    setSearch,
  };
};
