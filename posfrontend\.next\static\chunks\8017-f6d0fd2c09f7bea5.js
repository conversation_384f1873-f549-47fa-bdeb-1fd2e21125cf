(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8017],{87181:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(85407),a=r(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var c=r(84021);let i=a.forwardRef(function(e,t){return a.createElement(c.A,(0,n.A)({},e,{ref:t,icon:o}))})},17084:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(85407),a=r(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zm-200 0H360v-72h304v72z"}}]},name:"delete",theme:"filled"};var c=r(84021);let i=a.forwardRef(function(e,t){return a.createElement(c.A,(0,n.A)({},e,{ref:t,icon:o}))})},27656:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(85407),a=r(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var c=r(84021);let i=a.forwardRef(function(e,t){return a.createElement(c.A,(0,n.A)({},e,{ref:t,icon:o}))})},33621:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(85407),a=r(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var c=r(84021);let i=a.forwardRef(function(e,t){return a.createElement(c.A,(0,n.A)({},e,{ref:t,icon:o}))})},72278:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(85407),a=r(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var c=r(84021);let i=a.forwardRef(function(e,t){return a.createElement(c.A,(0,n.A)({},e,{ref:t,icon:o}))})},92895:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(12115),a=r(4617),o=r.n(a),c=r(37801),i=r(15231),l=r(71054),s=r(43144),u=r(31049),d=r(30033),p=r(7926),f=r(30149);let m=n.createContext(null);var h=r(24631),v=r(83427),b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let g=n.forwardRef((e,t)=>{var r;let{prefixCls:a,className:g,rootClassName:y,children:x,indeterminate:C=!1,style:k,onMouseEnter:w,onMouseLeave:O,skipGroup:A=!1,disabled:E}=e,S=b(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:j,direction:R,checkbox:H}=n.useContext(u.QO),P=n.useContext(m),{isFormItemInput:I}=n.useContext(f.$W),z=n.useContext(d.A),L=null!==(r=(null==P?void 0:P.disabled)||E)&&void 0!==r?r:z,D=n.useRef(S.value),N=n.useRef(null),M=(0,i.K4)(t,N);n.useEffect(()=>{null==P||P.registerValue(S.value)},[]),n.useEffect(()=>{if(!A)return S.value!==D.current&&(null==P||P.cancelValue(D.current),null==P||P.registerValue(S.value),D.current=S.value),()=>null==P?void 0:P.cancelValue(S.value)},[S.value]),n.useEffect(()=>{var e;(null===(e=N.current)||void 0===e?void 0:e.input)&&(N.current.input.indeterminate=C)},[C]);let B=j("checkbox",a),T=(0,p.A)(B),[_,W,V]=(0,h.Ay)(B,T),F=Object.assign({},S);P&&!A&&(F.onChange=function(){S.onChange&&S.onChange.apply(S,arguments),P.toggleOption&&P.toggleOption({label:x,value:S.value})},F.name=P.name,F.checked=P.value.includes(S.value));let q=o()("".concat(B,"-wrapper"),{["".concat(B,"-rtl")]:"rtl"===R,["".concat(B,"-wrapper-checked")]:F.checked,["".concat(B,"-wrapper-disabled")]:L,["".concat(B,"-wrapper-in-form-item")]:I},null==H?void 0:H.className,g,y,V,T,W),$=o()({["".concat(B,"-indeterminate")]:C},s.D,W),[G,X]=(0,v.A)(F.onClick);return _(n.createElement(l.A,{component:"Checkbox",disabled:L},n.createElement("label",{className:q,style:Object.assign(Object.assign({},null==H?void 0:H.style),k),onMouseEnter:w,onMouseLeave:O,onClick:G},n.createElement(c.A,Object.assign({},F,{onClick:X,prefixCls:B,className:$,disabled:L,ref:M})),void 0!==x&&n.createElement("span",{className:"".concat(B,"-label")},x))))});var y=r(39014),x=r(70527),C=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let k=n.forwardRef((e,t)=>{let{defaultValue:r,children:a,options:c=[],prefixCls:i,className:l,rootClassName:s,style:d,onChange:f}=e,v=C(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:b,direction:k}=n.useContext(u.QO),[w,O]=n.useState(v.value||r||[]),[A,E]=n.useState([]);n.useEffect(()=>{"value"in v&&O(v.value||[])},[v.value]);let S=n.useMemo(()=>c.map(e=>"string"==typeof e||"number"==typeof e?{label:e,value:e}:e),[c]),j=b("checkbox",i),R="".concat(j,"-group"),H=(0,p.A)(j),[P,I,z]=(0,h.Ay)(j,H),L=(0,x.A)(v,["value","disabled"]),D=c.length?S.map(e=>n.createElement(g,{prefixCls:j,key:e.value.toString(),disabled:"disabled"in e?e.disabled:v.disabled,value:e.value,checked:w.includes(e.value),onChange:e.onChange,className:"".concat(R,"-item"),style:e.style,title:e.title,id:e.id,required:e.required},e.label)):a,N={toggleOption:e=>{let t=w.indexOf(e.value),r=(0,y.A)(w);-1===t?r.push(e.value):r.splice(t,1),"value"in v||O(r),null==f||f(r.filter(e=>A.includes(e)).sort((e,t)=>S.findIndex(t=>t.value===e)-S.findIndex(e=>e.value===t)))},value:w,disabled:v.disabled,name:v.name,registerValue:e=>{E(t=>[].concat((0,y.A)(t),[e]))},cancelValue:e=>{E(t=>t.filter(t=>t!==e))}},M=o()(R,{["".concat(R,"-rtl")]:"rtl"===k},l,s,z,H,I);return P(n.createElement("div",Object.assign({className:M,style:d},L,{ref:t}),n.createElement(m.Provider,{value:N},D)))});g.Group=k,g.__ANT_CHECKBOX=!0;let w=g},24631:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,gd:()=>l});var n=r(67548),a=r(70695),o=r(56204),c=r(1086);let i=e=>{let{checkboxCls:t}=e,r="".concat(t,"-wrapper");return[{["".concat(t,"-group")]:Object.assign(Object.assign({},(0,a.dF)(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,["> ".concat(e.antCls,"-row")]:{flex:1}}),[r]:Object.assign(Object.assign({},(0,a.dF)(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},["& + ".concat(r)]:{marginInlineStart:0},["&".concat(r,"-in-form-item")]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},(0,a.dF)(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",["".concat(t,"-input")]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,["&:focus-visible + ".concat(t,"-inner")]:Object.assign({},(0,a.jk)(e))},["".concat(t,"-inner")]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:"".concat((0,n.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:"all ".concat(e.motionDurationSlow),"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:"".concat((0,n.zA)(e.lineWidthBold)," solid ").concat(e.colorWhite),borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:"all ".concat(e.motionDurationFast," ").concat(e.motionEaseInBack,", opacity ").concat(e.motionDurationFast)}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{["\n        ".concat(r,":not(").concat(r,"-disabled),\n        ").concat(t,":not(").concat(t,"-disabled)\n      ")]:{["&:hover ".concat(t,"-inner")]:{borderColor:e.colorPrimary}},["".concat(r,":not(").concat(r,"-disabled)")]:{["&:hover ".concat(t,"-checked:not(").concat(t,"-disabled) ").concat(t,"-inner")]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},["&:hover ".concat(t,"-checked:not(").concat(t,"-disabled):after")]:{borderColor:e.colorPrimaryHover}}},{["".concat(t,"-checked")]:{["".concat(t,"-inner")]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseOutBack," ").concat(e.motionDurationFast)}}},["\n        ".concat(r,"-checked:not(").concat(r,"-disabled),\n        ").concat(t,"-checked:not(").concat(t,"-disabled)\n      ")]:{["&:hover ".concat(t,"-inner")]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{["".concat(t,"-inner")]:{backgroundColor:"".concat(e.colorBgContainer," !important"),borderColor:"".concat(e.colorBorder," !important"),"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},["&:hover ".concat(t,"-inner")]:{backgroundColor:"".concat(e.colorBgContainer," !important"),borderColor:"".concat(e.colorPrimary," !important")}}}},{["".concat(r,"-disabled")]:{cursor:"not-allowed"},["".concat(t,"-disabled")]:{["&, ".concat(t,"-input")]:{cursor:"not-allowed",pointerEvents:"none"},["".concat(t,"-inner")]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},["&".concat(t,"-indeterminate ").concat(t,"-inner::after")]:{background:e.colorTextDisabled}}}]};function l(e,t){return[i((0,o.oX)(t,{checkboxCls:".".concat(e),checkboxSize:t.controlInteractiveSize}))]}let s=(0,c.OF)("Checkbox",(e,t)=>{let{prefixCls:r}=t;return[l(r,e)]})},83427:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115),a=r(13379);function o(e){let t=n.useRef(null),r=()=>{a.A.cancel(t.current),t.current=null};return[()=>{r(),t.current=(0,a.A)(()=>{t.current=null})},n=>{t.current&&(n.stopPropagation(),r()),null==e||e(n)}]}},45551:(e,t)=>{"use strict";t.Y=function(e,t){return e.split(",").map(function(e){var t=(e=e.trim()).match(r),o=t[1],c=t[2],i=t[3]||"",l={};return l.inverse=!!o&&"not"===o.toLowerCase(),l.type=c?c.toLowerCase():"all",i=i.match(/\([^\)]+\)/g)||[],l.expressions=i.map(function(e){var t=e.match(n),r=t[1].toLowerCase().match(a);return{modifier:r[1],feature:r[2],value:t[2]}}),l}).some(function(e){var r=e.inverse,n="all"===e.type||t.type===e.type;if(n&&r||!(n||r))return!1;var a=e.expressions.every(function(e){var r=e.feature,n=e.modifier,a=e.value,o=t[r];if(!o)return!1;switch(r){case"orientation":case"scan":return o.toLowerCase()===a.toLowerCase();case"width":case"height":case"device-width":case"device-height":a=s(a),o=s(o);break;case"resolution":a=l(a),o=l(o);break;case"aspect-ratio":case"device-aspect-ratio":case"device-pixel-ratio":a=i(a),o=i(o);break;case"grid":case"color":case"color-index":case"monochrome":a=parseInt(a,10)||1,o=parseInt(o,10)||0}switch(n){case"min":return o>=a;case"max":return o<=a;default:return o===a}});return a&&!r||!a&&r})};var r=/(?:(only|not)?\s*([^\s\(\)]+)(?:\s*and)?\s*)?(.+)?/i,n=/\(\s*([^\s\:\)]+)\s*(?:\:\s*([^\s\)]+))?\s*\)/,a=/^(?:(min|max)-)?(.+)/,o=/(em|rem|px|cm|mm|in|pt|pc)?$/,c=/(dpi|dpcm|dppx)?$/;function i(e){var t,r=Number(e);return r||(r=(t=e.match(/^(\d+)\s*\/\s*(\d+)$/))[1]/t[2]),r}function l(e){var t=parseFloat(e);switch(String(e).match(c)[1]){case"dpcm":return t/2.54;case"dppx":return 96*t;default:return t}}function s(e){var t=parseFloat(e);switch(String(e).match(o)[1]){case"em":case"rem":return 16*t;case"cm":return 96*t/2.54;case"mm":return 96*t/2.54/10;case"in":return 96*t;case"pt":return 72*t;case"pc":return 72*t/12;default:return t}}},38740:(e,t,r)=>{"use strict";var n=r(45551).Y,a="undefined"!=typeof window?window.matchMedia:null;function o(e,t,r){var o,c=this;function i(e){c.matches=e.matches,c.media=e.media}a&&!r&&(o=a.call(window,e)),o?(this.matches=o.matches,this.media=o.media,o.addListener(i)):(this.matches=n(e,t),this.media=e),this.addListener=function(e){o&&o.addListener(e)},this.removeListener=function(e){o&&o.removeListener(e)},this.dispose=function(){o&&o.removeListener(i)}}e.exports=function(e,t,r){return new o(e,t,r)}},65192:(e,t,r)=>{"use strict";var n=r(80859);function a(){}function o(){}o.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,o,c){if(c!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:a};return r.PropTypes=r,r}},81996:(e,t,r)=>{e.exports=r(65192)()},80859:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37801:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(85407),a=r(85268),o=r(1568),c=r(59912),i=r(64406),l=r(4617),s=r.n(l),u=r(35015),d=r(12115),p=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"];let f=(0,d.forwardRef)(function(e,t){var r=e.prefixCls,l=void 0===r?"rc-checkbox":r,f=e.className,m=e.style,h=e.checked,v=e.disabled,b=e.defaultChecked,g=e.type,y=void 0===g?"checkbox":g,x=e.title,C=e.onChange,k=(0,i.A)(e,p),w=(0,d.useRef)(null),O=(0,d.useRef)(null),A=(0,u.A)(void 0!==b&&b,{value:h}),E=(0,c.A)(A,2),S=E[0],j=E[1];(0,d.useImperativeHandle)(t,function(){return{focus:function(e){var t;null===(t=w.current)||void 0===t||t.focus(e)},blur:function(){var e;null===(e=w.current)||void 0===e||e.blur()},input:w.current,nativeElement:O.current}});var R=s()(l,f,(0,o.A)((0,o.A)({},"".concat(l,"-checked"),S),"".concat(l,"-disabled"),v));return d.createElement("span",{className:R,title:x,style:m,ref:O},d.createElement("input",(0,n.A)({},k,{className:"".concat(l,"-input"),ref:w,onChange:function(t){v||("checked"in e||j(t.target.checked),null==C||C({target:(0,a.A)((0,a.A)({},e),{},{type:y,checked:t.target.checked}),stopPropagation:function(){t.stopPropagation()},preventDefault:function(){t.preventDefault()},nativeEvent:t.nativeEvent}))},disabled:v,checked:!!S,type:y})),d.createElement("span",{className:"".concat(l,"-inner")}))})},68190:(e,t,r)=>{"use strict";r.d(t,{Ub:()=>P});var n=r(12115),a=r(38740),o=r.n(a),c=/[A-Z]/g,i=/^ms-/,l={};function s(e){return"-"+e.toLowerCase()}let u=function(e){if(l.hasOwnProperty(e))return l[e];var t=e.replace(c,s);return l[e]=i.test(t)?"-"+t:t};var d=r(81996),p=r.n(d);let f=p().oneOfType([p().string,p().number]),m={all:p().bool,grid:p().bool,aural:p().bool,braille:p().bool,handheld:p().bool,print:p().bool,projection:p().bool,screen:p().bool,tty:p().bool,tv:p().bool,embossed:p().bool},{type:h,...v}={orientation:p().oneOf(["portrait","landscape"]),scan:p().oneOf(["progressive","interlace"]),aspectRatio:p().string,deviceAspectRatio:p().string,height:f,deviceHeight:f,width:f,deviceWidth:f,color:p().bool,colorIndex:p().bool,monochrome:p().bool,resolution:f,type:Object.keys(m)},b={minAspectRatio:p().string,maxAspectRatio:p().string,minDeviceAspectRatio:p().string,maxDeviceAspectRatio:p().string,minHeight:f,maxHeight:f,minDeviceHeight:f,maxDeviceHeight:f,minWidth:f,maxWidth:f,minDeviceWidth:f,maxDeviceWidth:f,minColor:p().number,maxColor:p().number,minColorIndex:p().number,maxColorIndex:p().number,minMonochrome:p().number,maxMonochrome:p().number,minResolution:f,maxResolution:f,...v};var g={all:{...m,...b}};let y=e=>`not ${e}`,x=(e,t)=>{let r=u(e);return("number"==typeof t&&(t=`${t}px`),!0===t)?r:!1===t?y(r):`(${r}: ${t})`},C=e=>e.join(" and "),k=e=>{let t=[];return Object.keys(g.all).forEach(r=>{let n=e[r];null!=n&&t.push(x(r,n))}),C(t)},w=(0,n.createContext)(void 0),O=e=>e.query||k(e),A=e=>{if(e)return Object.keys(e).reduce((t,r)=>(t[u(r)]=e[r],t),{})},E=()=>{let e=(0,n.useRef)(!1);return(0,n.useEffect)(()=>{e.current=!0},[]),e.current},S=e=>{let t=(0,n.useContext)(w),r=()=>A(e)||A(t),[a,o]=(0,n.useState)(r);return(0,n.useEffect)(()=>{let e=r();!function(e,t){if(e===t)return!0;if(!e||!t)return!1;let r=Object.keys(e),n=Object.keys(t),a=r.length;if(n.length!==a)return!1;for(let n=0;n<a;n++){let a=r[n];if(e[a]!==t[a]||!Object.prototype.hasOwnProperty.call(t,a))return!1}return!0}(a,e)&&o(e)},[e,t]),a},j=e=>{let t=()=>O(e),[r,a]=(0,n.useState)(t);return(0,n.useEffect)(()=>{let e=t();r!==e&&a(e)},[e]),r},R=(e,t)=>{let r=()=>o()(e,t||{},!!t),[a,c]=(0,n.useState)(r),i=E();return(0,n.useEffect)(()=>{if(i){let e=r();return c(e),()=>{e&&e.dispose()}}},[e,t]),a},H=e=>{let[t,r]=(0,n.useState)(e.matches);return(0,n.useEffect)(()=>{let t=e=>{r(e.matches)};return e.addListener(t),r(e.matches),()=>{e.removeListener(t)}},[e]),t},P=(e,t,r)=>{let a=S(t),o=j(e);if(!o)throw Error("Invalid or missing MediaQuery!");let c=R(o,a),i=H(c),l=E();return(0,n.useEffect)(()=>{l&&r&&r(i)},[i]),(0,n.useEffect)(()=>()=>{c&&c.dispose()},[]),i}}}]);