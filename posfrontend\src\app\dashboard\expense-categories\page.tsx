"use client";

import React, { useState } from "react";
import { Card, Button, Spin, Input, Space } from "antd";
import {
  TagOutlined,
  LoadingOutlined,
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGetAllExpenseCategoriesQuery, useDeleteExpenseCategoryMutation, type ExpenseCategory } from "@/reduxRTK/services/expenseCategoryApi";
import ExpenseCategoryTable from "@/components/Expenses/ExpenseCategoryTable";
import ExpensePagination from "@/components/Expenses/ExpensePagination";
import ExpenseCategoryFormPanel from "@/components/Expenses/ExpenseCategoryFormPanel";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { showMessage } from "@/utils/showMessage";
import { UserRole } from "@/types/user";

const ExpenseCategoriesPage: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const userRole = user?.role as UserRole;

  // State management
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [search, setSearch] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ExpenseCategory | null>(null);

  // Confirmation dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<number | null>(null);
  const [categoriesToDelete, setCategoriesToDelete] = useState<number[]>([]);

  // Check permissions
  const canAddCategory = userRole === "admin" || userRole === "superadmin";
  const canDeleteCategory = userRole === "admin" || userRole === "superadmin";

  // Prepare query parameters
  const queryParams = {
    page,
    limit,
    search: search.trim(),
  };

  // API hooks
  const { data: categoriesData, isLoading, error, refetch } = useGetAllExpenseCategoriesQuery(queryParams);
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteExpenseCategoryMutation();

  const categories = categoriesData?.data?.categories || [];
  const total = categoriesData?.data?.total || 0;

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle add category
  const handleAddCategory = () => {
    if (!canAddCategory) {
      showMessage.error("You don't have permission to add categories");
      return;
    }
    setEditingCategory(null);
    setIsAddPanelOpen(true);
  };

  // Handle edit category
  const handleEditCategory = (category: ExpenseCategory) => {
    if (category.isDefault) {
      showMessage.warning("System default categories cannot be edited");
      return;
    }
    setEditingCategory(category);
    setIsAddPanelOpen(true);
  };

  // Handle delete category
  const handleDeleteCategory = (categoryId: number) => {
    if (!canDeleteCategory) {
      showMessage.error("You don't have permission to delete categories");
      return;
    }

    const category = categories.find(c => c.id === categoryId);
    if (category?.isDefault) {
      showMessage.warning("System default categories cannot be deleted");
      return;
    }

    setCategoryToDelete(categoryId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete category
  const confirmDeleteCategory = async () => {
    if (categoryToDelete) {
      try {
        const result = await deleteCategory(categoryToDelete).unwrap();
        if (result.success) {
          showMessage.success("Category deleted successfully!");
          setSelectedCategories(prev => prev.filter(id => id !== categoryToDelete));
          setIsDeleteDialogOpen(false);
          setCategoryToDelete(null);
        } else {
          showMessage.error(result.message || "Failed to delete category");
        }
      } catch (error: any) {
        console.error("Error deleting category:", error);
        showMessage.error(error?.data?.message || "Failed to delete category");
      }
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setCategoryToDelete(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (categoryIds: number[]) => {
    if (!canDeleteCategory) {
      showMessage.error("You don't have permission to delete categories");
      return;
    }

    // Filter out default categories
    const deletableIds = categoryIds.filter(id => {
      const category = categories.find(c => c.id === id);
      return category && !category.isDefault;
    });

    if (deletableIds.length === 0) {
      showMessage.warning("No deletable categories selected");
      return;
    }

    setCategoriesToDelete(deletableIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    try {
      // Delete categories one by one (since backend doesn't have bulk delete)
      const deletePromises = categoriesToDelete.map(id => deleteCategory(id).unwrap());
      await Promise.all(deletePromises);

      showMessage.success(`${categoriesToDelete.length} category(s) deleted successfully!`);
      setSelectedCategories([]);
      setIsBulkDeleteDialogOpen(false);
      setCategoriesToDelete([]);
    } catch (error: any) {
      console.error("Error deleting categories:", error);
      showMessage.error("Failed to delete some categories");
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setCategoriesToDelete([]);
  };

  // Handle form success
  const handleFormSuccess = () => {
    refetch();
    setSelectedCategories([]);
  };

  if (error) {
    return (
      <div className="w-full p-2 sm:p-4">
        <Card className="w-full">
          <div className="text-center text-red-500 p-8">
            <p>Error loading expense categories. Please try again.</p>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Expense Categories</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          canAddCategory && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddCategory}
              size={isMobile ? "small" : "middle"}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMobile ? "" : "Add Category"}
            </Button>
          )
        }
      >
        {/* Search */}
        <div className="mb-4">
          <Input
            placeholder="Search categories..."
            prefix={<SearchOutlined />}
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
            className="max-w-md"
            allowClear
          />
        </div>

        {/* Content */}
        <div className="min-h-96">
          {isLoading ? (
            <div className="flex justify-center items-center h-96">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
            </div>
          ) : (
            <>
              {categories.length > 0 ? (
                <ExpenseCategoryTable
                  categories={categories}
                  loading={isLoading}
                  onEdit={handleEditCategory}
                  onDelete={handleDeleteCategory}
                  onBulkDelete={canDeleteCategory ? handleBulkDelete : undefined}
                  selectedCategories={selectedCategories}
                  onSelectionChange={setSelectedCategories}
                  isMobile={isMobile}
                />
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <TagOutlined className="text-4xl mb-4" />
                  {search ? (
                    <p>No categories found matching your search.</p>
                  ) : (
                    <p>No expense categories found. {canAddCategory && "Click 'Add Category' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {categories.length > 0 && (
                <ExpensePagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add/Edit Category Panel */}
      <ExpenseCategoryFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => {
          setIsAddPanelOpen(false);
          setEditingCategory(null);
        }}
        onSuccess={handleFormSuccess}
        category={editingCategory}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteCategory}
        title="Delete Category"
        message="Are you sure you want to delete this category? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Categories"
        message={`Are you sure you want to delete ${categoriesToDelete.length} categories? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />
    </div>
  );
};

export default ExpenseCategoriesPage;
