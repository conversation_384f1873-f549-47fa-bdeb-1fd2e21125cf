"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handlePurchaseRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const purchaseService_1 = require("../services/purchaseService");
const modeValidator_1 = require("../utils/modeValidator");
const handlePurchaseRequest = async (req, res) => {
    const { mode, purchaseId, page, limit, ...data } = req.body;
    const requester = req.user; // ✅ Extract requester from middleware
    // ✅ Validate mode
    const validModes = ["createnew", "update", "delete", "retrieve"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            // ✅ Create New Purchase
            case "createnew": {
                if (!data.productId || !data.quantity || !data.costPrice || !data.totalCost) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "All purchase details are required.");
                }
                const newPurchase = await (0, purchaseService_1.createPurchase)(requester, {
                    supplierId: data.supplierId,
                    productId: data.productId,
                    quantity: data.quantity,
                    costPrice: data.costPrice,
                    totalCost: data.totalCost,
                });
                return (0, responseHelper_1.sendResponse)(res, 201, true, "Purchase created successfully.", newPurchase);
            }
            // ✅ Retrieve Purchases (Single or All)
            case "retrieve": {
                if (purchaseId) {
                    // Fetch a single purchase by ID
                    const purchase = await (0, purchaseService_1.getPurchaseById)(requester, Number(purchaseId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Purchase retrieved successfully.", purchase);
                }
                else {
                    // Fetch all purchases with pagination
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    const purchases = await (0, purchaseService_1.getAllPurchases)(requester, pageNum, limitNum);
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Purchases retrieved successfully.", purchases);
                }
            }
            // ✅ Update Purchase
            case "update": {
                if (!purchaseId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Purchase ID is required for updating.");
                }
                const updatedPurchase = await (0, purchaseService_1.updatePurchaseById)(requester, Number(purchaseId), data);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Purchase updated successfully.", updatedPurchase);
            }
            // ✅ Delete Purchase (Single or Multiple)
            case "delete": {
                // Check if we have purchaseIds (array) or purchaseId (single)
                const { purchaseIds } = req.body;
                if (!purchaseId && !purchaseIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Purchase ID(s) are required for deletion.");
                }
                // Use purchaseIds if provided, otherwise use single purchaseId
                const idsToDelete = purchaseIds
                    ? (Array.isArray(purchaseIds) ? purchaseIds : [purchaseIds])
                    : [Number(purchaseId)];
                const result = await (0, purchaseService_1.deletePurchaseById)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} purchases deleted successfully.`
                    : "Purchase deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handlePurchaseRequest = handlePurchaseRequest;
