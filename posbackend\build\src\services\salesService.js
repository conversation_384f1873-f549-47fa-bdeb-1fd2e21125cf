"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSale = exports.updateSale = exports.getSaleById = exports.getSaleDetails = exports.getAllSales = exports.createSale = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
const schema_2 = require("../validation/schema");
const userStoreService_1 = require("./userStoreService");
/**
 * ✅ Create a new sale (Cashiers, Admins, Superadmins)
 */
const createSale = async (requester, bulkSaleData) => {
    // Authorization to create a sale
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "sales");
    // Validate sale data using Zod
    const parsedSaleData = schema_2.saleSchema.parse(bulkSaleData.saleData);
    const parsedReceiptData = schema_2.receiptSchema.parse({
        saleId: 1, // Placeholder, will be replaced after sale creation
        receiptUrl: bulkSaleData.saleData.receiptUrl,
    });
    // Get store information
    let storeId = bulkSaleData.saleData.storeId;
    // If no store ID provided, try to get the user's default store
    if (!storeId) {
        try {
            const defaultStore = await (0, userStoreService_1.getUserDefaultStore)(requester, requester.id);
            if (defaultStore) {
                storeId = defaultStore.id;
            }
        }
        catch (error) {
            console.error("Error getting default store:", error);
            // Continue without store ID if there's an error
        }
    }
    // Start a transaction to ensure atomicity
    return await db_1.postgresDb.transaction(async (trx) => {
        // Insert the sale data
        const insertedSales = await trx
            .insert(schema_1.sales)
            .values({
            totalAmount: parsedSaleData.totalAmount.toString(),
            paymentMethod: parsedSaleData.paymentMethod,
            createdBy: requester.id,
            storeId: storeId, // Include store ID if available
        })
            .returning({ saleId: schema_1.sales.id });
        if (insertedSales.length === 0) {
            throw new Error("Sale creation failed.");
        }
        const saleId = insertedSales[0].saleId;
        // Insert sale items in bulk
        const saleItemsData = parsedSaleData.items.map((item) => ({
            saleId: saleId, // Link the sale ID to the inserted sale
            productId: item.productId,
            quantity: item.quantity,
            price: item.price.toString(),
        }));
        await trx.insert(schema_1.salesItems).values(saleItemsData);
        // Stock update for each product in the sale
        for (const item of parsedSaleData.items) {
            // Validate stock before proceeding with the sale
            const stock = await trx
                .select({ stockQuantity: schema_1.products.stockQuantity })
                .from(schema_1.products)
                .where((0, drizzle_orm_1.eq)(schema_1.products.id, item.productId))
                .limit(1);
            if (!stock.length || stock[0].stockQuantity < item.quantity) {
                throw new Error(`Not enough stock for product ID ${item.productId}`);
            }
            // Update the product stock
            await trx
                .update(schema_1.products)
                .set({
                stockQuantity: (0, drizzle_orm_1.sql) `${schema_1.products.stockQuantity} - ${item.quantity}`,
            })
                .where((0, drizzle_orm_1.eq)(schema_1.products.id, item.productId));
        }
        // Validate and Insert the receipt into the receipts table
        await trx.insert(schema_1.receipts).values({
            saleId: saleId,
            receiptUrl: parsedReceiptData.receiptUrl, // Using validated URL
            createdBy: requester.id,
        });
        // Return the sale ID(s)
        return { sales: [{ saleId }] };
    });
};
exports.createSale = createSale;
/**
 * ✅ Get all sales (Anyone can view sales)
 */
const getAllSales = async (requester, page = 1, limit = 10) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "sales");
    const offset = (page - 1) * limit;
    const isSuperadmin = requester.role === "superadmin";
    const isCashier = requester.role === "cashier";
    // Different filter logic based on role
    let salesFilter;
    if (isSuperadmin) {
        // Superadmin sees all sales
        salesFilter = undefined;
    }
    else if (requester.role === "admin") {
        // Admin can only see their own sales and sales from users they created
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        const teamMemberIds = teamMembersResult.map((m) => m.memberId);
        if (teamMemberIds.length > 0) {
            // Admin can see their own sales and sales from users they created
            salesFilter = (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamMemberIds));
        }
        else {
            // If no team members found, just show their own sales
            salesFilter = (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id);
        }
    }
    else if (isCashier) {
        // Cashier can only see their own sales and sales from their admin and siblings
        // Find the cashier's creator (admin)
        const creatorResult = await db_1.postgresDb
            .select({ creatorId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        const creatorId = creatorResult[0]?.creatorId || undefined;
        if (creatorId) {
            // Get all users created by the same admin (siblings)
            const siblingsResult = await db_1.postgresDb
                .select({ siblingId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
            const siblingIds = siblingsResult.map((s) => s.siblingId);
            // Build the filter to include own sales, creator's sales, and siblings' sales
            salesFilter = (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, siblingIds));
        }
        else {
            // If no creator found, just show their own sales
            salesFilter = (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id);
        }
    }
    else {
        // Default case - just show own sales
        salesFilter = (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id);
    }
    const [salesData, totalSales] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.sales.id,
            totalAmount: schema_1.sales.totalAmount,
            paymentMethod: schema_1.sales.paymentMethod,
            transactionDate: schema_1.sales.transactionDate,
            createdBy: schema_1.sales.createdBy,
            createdByName: schema_1.users.name, // Include the creator's name
            storeId: schema_1.sales.storeId,
            storeName: schema_1.stores.name,
        })
            .from(schema_1.sales)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, schema_1.users.id))
            .leftJoin(schema_1.stores, (0, drizzle_orm_1.eq)(schema_1.sales.storeId, schema_1.stores.id))
            .where(salesFilter)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.sales.transactionDate))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb.select({ count: (0, drizzle_orm_1.count)() }).from(schema_1.sales).where(salesFilter),
    ]);
    // Get sale items for each sale
    const salesWithItems = await Promise.all(salesData.map(async (sale) => {
        const items = await (0, exports.getSaleDetails)(sale.id);
        return {
            ...sale,
            items // Include the items directly in each sale object
        };
    }));
    return {
        total: totalSales[0]?.count ?? 0,
        page,
        perPage: limit,
        sales: salesWithItems,
    };
};
exports.getAllSales = getAllSales;
// salesDetailsService.ts
const getSaleDetails = async (saleId) => {
    try {
        console.log(`Fetching sale items for saleId ${saleId}`);
        // Fetch sale items for a given saleId along with product details using a JOIN query
        const saleItemsWithDetails = await db_1.postgresDb
            .select({
            id: schema_1.salesItems.id,
            saleId: schema_1.salesItems.saleId,
            productId: schema_1.salesItems.productId,
            productName: schema_1.products.name,
            quantity: schema_1.salesItems.quantity,
            price: schema_1.salesItems.price,
        })
            .from(schema_1.salesItems)
            .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.salesItems.productId, schema_1.products.id)) // Using leftJoin to ensure we get items even if product is deleted
            .where((0, drizzle_orm_1.eq)(schema_1.salesItems.saleId, saleId)); // Filter by the saleId
        // If no items found, create a fallback item based on the sale total
        if (!saleItemsWithDetails || saleItemsWithDetails.length === 0) {
            console.log(`No sale items found for saleId ${saleId}, checking sale total`);
            // Get the sale to create a fallback item
            const dbSaleData = await db_1.postgresDb
                .select({
                id: schema_1.sales.id,
                totalAmount: schema_1.sales.totalAmount,
            })
                .from(schema_1.sales)
                .where((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId))
                .limit(1);
            let saleData = null;
            if (dbSaleData && dbSaleData.length > 0) {
                saleData = dbSaleData[0];
            }
            if (saleData) {
                const fallbackItems = [{
                        id: 0,
                        saleId: saleId,
                        productId: 0,
                        productName: "Sale Total (No Items Found)",
                        quantity: 1,
                        price: saleData?.totalAmount || '0',
                    }];
                return fallbackItems;
            }
        }
        // Map the results to ensure consistent property names
        const formattedItems = saleItemsWithDetails.map((item) => ({
            id: item.id,
            saleId: item.saleId,
            productId: item.productId,
            productName: item.productName || "Unknown Product",
            quantity: item.quantity,
            price: item.price,
        }));
        // Ensure we always return an array, even if empty
        return formattedItems;
    }
    catch (error) {
        console.error(`Error retrieving sale items for saleId ${saleId}:`, error);
        return []; // Return empty array on error
    }
};
exports.getSaleDetails = getSaleDetails;
/**
 * ✅ Get sale by ID (Anyone can view a sale)
 */
const getSaleById = async (requester, saleId) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "sales", saleId);
    const isSuperadmin = requester.role === "superadmin";
    const isCashier = requester.role === "cashier";
    // Different filter logic based on role
    let saleFilter;
    if (isSuperadmin) {
        // Superadmin can see any sale
        saleFilter = (0, drizzle_orm_1.eq)(schema_1.sales.id, saleId);
    }
    else if (requester.role === "admin") {
        // Admin can only see their own sales and sales from users they created
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        const teamMemberIds = teamMembersResult.map((m) => m.memberId);
        if (teamMemberIds.length > 0) {
            // Admin can see their own sales and sales from users they created
            saleFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamMemberIds)));
        }
        else {
            // If no team members found, just show their own sales
            saleFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
        }
    }
    else if (isCashier) {
        // Cashier can only see their own sales and sales from their admin and siblings
        // Find the cashier's creator (admin)
        const creatorResult = await db_1.postgresDb
            .select({ creatorId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        const creatorId = creatorResult[0]?.creatorId || undefined;
        if (creatorId) {
            // Get all users created by the same admin (siblings)
            const siblingsResult = await db_1.postgresDb
                .select({ siblingId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
            const siblingIds = siblingsResult.map((s) => s.siblingId);
            // Build the filter to include own sales, creator's sales, and siblings' sales
            saleFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, siblingIds)));
        }
        else {
            // If no creator found, just show their own sales
            saleFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
        }
    }
    else {
        // Default case - just show own sales
        saleFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
    }
    // Join with users and stores tables to get the creator's name and store info
    const saleData = await db_1.postgresDb
        .select({
        id: schema_1.sales.id,
        totalAmount: schema_1.sales.totalAmount,
        paymentMethod: schema_1.sales.paymentMethod,
        transactionDate: schema_1.sales.transactionDate,
        createdBy: schema_1.sales.createdBy,
        createdByName: schema_1.users.name, // Include the creator's name
        storeId: schema_1.sales.storeId,
        storeName: schema_1.stores.name,
        storeAddress: schema_1.stores.address,
        storeCity: schema_1.stores.city,
        storeState: schema_1.stores.state,
        storeCountry: schema_1.stores.country,
        storePhone: schema_1.stores.phone,
        storeEmail: schema_1.stores.email,
        storeLogo: schema_1.stores.logo,
    })
        .from(schema_1.sales)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, schema_1.users.id))
        .leftJoin(schema_1.stores, (0, drizzle_orm_1.eq)(schema_1.sales.storeId, schema_1.stores.id))
        .where(saleFilter)
        .limit(1);
    if (saleData.length === 0)
        throw new Error("Sale not found or unauthorized.");
    // Get the sale items for this sale (already cached in getSaleDetails)
    const saleItems = await (0, exports.getSaleDetails)(saleId);
    // Return the sale with its items
    const saleWithItems = {
        ...saleData[0],
        items: saleItems // Include the items directly in the sale object
    };
    return saleWithItems;
};
exports.getSaleById = getSaleById;
/**
 * ✅ Update sale (Only Admins & Superadmins)
 */
const updateSale = async (requester, saleId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "sales", saleId);
    const formattedUpdateData = {
        ...updateData,
        totalAmount: updateData.totalAmount
            ? updateData.totalAmount.toString()
            : undefined, // ✅ Convert number to string
    };
    const updatedSale = await db_1.postgresDb
        .update(schema_1.sales)
        .set(formattedUpdateData)
        .where((0, drizzle_orm_1.eq)(schema_1.sales.id, saleId))
        .returning();
    if (!updatedSale || updatedSale.length === 0) {
        throw new Error("Update failed: Sale not found.");
    }
    // No cache invalidation needed
    return { updatedSale: updatedSale[0] };
};
exports.updateSale = updateSale;
/**
 * ❌ Delete sale (Only Admins & Superadmins)
 */
const deleteSale = async (requester, ids) => {
    const saleIds = Array.isArray(ids) ? ids : [ids];
    // Check authorization for each sale
    for (const saleId of saleIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "sales", saleId);
    }
    if (requester.role === "cashier") {
        throw new Error("Unauthorized: Cashiers cannot delete sales.");
    }
    const isSuperadmin = requester.role === "superadmin";
    // Build the delete filter based on the user's role
    let deleteFilter;
    if (isSuperadmin) {
        // Superadmin can delete any sale
        deleteFilter = (0, drizzle_orm_1.inArray)(schema_1.sales.id, saleIds);
    }
    else if (requester.role === "admin") {
        // Admin can only delete their own sales and sales from users they created
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        const teamMemberIds = teamMembersResult.map((m) => m.memberId);
        if (teamMemberIds.length > 0) {
            // Admin can delete their own sales and sales from users they created
            deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.sales.id, saleIds), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.sales.createdBy, teamMemberIds)));
        }
        else {
            // If no team members found, just delete their own sales
            deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.sales.id, saleIds), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
        }
    }
    else {
        // Default case - just delete own sales (should not happen due to earlier check)
        deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.sales.id, saleIds), (0, drizzle_orm_1.eq)(schema_1.sales.createdBy, requester.id));
    }
    const deletedSales = await db_1.postgresDb
        .delete(schema_1.sales)
        .where(deleteFilter)
        .returning({ deletedId: schema_1.sales.id });
    if (!deletedSales || deletedSales.length === 0) {
        throw new Error("Delete failed: Sale(s) not found or unauthorized.");
    }
    // No cache invalidation needed
    return {
        deletedIds: deletedSales.map((sale) => sale.deletedId)
    };
};
exports.deleteSale = deleteSale;
