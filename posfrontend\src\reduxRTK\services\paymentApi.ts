// services/paymentApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { store } from '../store/store';
import {
  ApiResponse,
  Payment,
  PaginatedPayments,
  InitializePaymentDto,
  PaymentInitializationResponse
} from '@/types/payment';

export const paymentApi = createApi({
  reducerPath: 'paymentApi',
  baseQuery: customBaseQuery,
  tagTypes: ['Payment'],
  endpoints: (builder) => ({
    // Initialize a Paystack payment
    initializePayment: builder.mutation<ApiResponse<PaymentInitializationResponse>, InitializePaymentDto>({
      query: (paymentData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/payment',
          payloaddata: {
            mode: 'initialize',
            ...paymentData,
          },
          token,
        };
      },
      invalidatesTags: ['Payment'],
    }),



    // Get payment history (paginated)
    getPaymentHistory: builder.query<ApiResponse<PaginatedPayments>, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/payment',
          payloaddata: {
            mode: 'history', // FIXED: Changed from 'retrieve' to 'history'
            page,
            limit,
          },
          token,
        };
      },
      providesTags: ['Payment'],
    }),

    // Get payment by ID
    getPaymentById: builder.query<Payment, number>({
      query: (paymentId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/payment',
          payloaddata: {
            mode: 'retrieve',
            paymentId,
          },
          token,
        };
      },
      providesTags: ['Payment'],
    }),

    // Verify Paystack payment by reference
    verifyPaystackPayment: builder.mutation<ApiResponse<Payment>, { reference: string }>({
      query: ({ reference }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/payment',
          payloaddata: {
            mode: 'verify-paystack',
            reference,
          },
          token,
        };
      },
      invalidatesTags: ['Payment'],
    }),

    // Verify payment status by transaction ID
    verifyPayment: builder.mutation<ApiResponse<Payment>, { transactionId: string }>({
      query: ({ transactionId }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/payment',
          payloaddata: {
            mode: 'verify',
            transactionId,
          },
          token,
        };
      },
      invalidatesTags: ['Payment'],
    }),

    // Update user payment status (for admin/superadmin)
    updateUserPaymentStatus: builder.mutation<
      ApiResponse<{ success: boolean }>,
      { userId: number; paymentStatus: 'pending' | 'paid' | 'overdue' | 'inactive' }
    >({
      query: ({ userId, paymentStatus }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/users',
          payloaddata: {
            mode: 'update',
            userId,
            paymentStatus,
          },
          token,
        };
      },
      invalidatesTags: ['Payment'],
    }),
  }),
});

export const {
  useInitializePaymentMutation,
  useGetPaymentHistoryQuery,
  useGetPaymentByIdQuery,
  useVerifyPaystackPaymentMutation,
  useVerifyPaymentMutation,
  useUpdateUserPaymentStatusMutation,
} = paymentApi;
