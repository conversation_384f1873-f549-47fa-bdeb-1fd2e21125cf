"use client";

import { useDeleteUserMutation } from "@/reduxRTK/services/authApi";
import { showMessage } from "@/utils/showMessage";
import { useState } from "react";

export const useUserDelete = (onSuccess?: () => void) => {
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<number | null>(null);

  // RTK Query hook for deleting a user
  const [deleteUser, { isLoading }] = useDeleteUserMutation();

  const confirmDelete = (userId: number) => {
    setUserToDelete(userId);
    setIsConfirmOpen(true);
  };

  const cancelDelete = () => {
    setIsConfirmOpen(false);
    setUserToDelete(null);
  };

  const executeDelete = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(userToDelete).unwrap();
      showMessage("success", "User deleted successfully");

      if (onSuccess) {
        onSuccess();
      }

      setIsConfirmOpen(false);
      setUserToDelete(null);
    } catch (error: any) {
      showMessage("error", error.message || "Failed to delete user");
      console.error("Delete error:", error);
    }
  };

  return {
    confirmDelete,
    cancelDelete,
    executeDelete,
    isDeleting: isLoading,
    isConfirmOpen,
    userToDelete
  };
};
