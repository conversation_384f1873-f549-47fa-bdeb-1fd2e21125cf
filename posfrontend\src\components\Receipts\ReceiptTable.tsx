"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EyeOutlined,
  DeleteOutlined,
  PrinterOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  UserOutlined,
  CalendarOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Receipt } from "@/reduxRTK/services/receiptApi";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { formatDate } from "@/utils/formatDate";

interface ReceiptTableProps {
  receipts: Receipt[];
  loading: boolean;
  onViewReceipt: (receiptUrl: string) => void;
  onPrintReceipt: (receiptUrl: string) => void;
  onDelete?: (receiptId: number) => void;
  onBulkDelete?: (receiptIds: number[]) => void;
  isMobile?: boolean;
}

const ReceiptTable: React.FC<ReceiptTableProps> = ({
  receipts,
  loading,
  onViewReceipt,
  onPrintReceipt,
  onDelete,
  onBulkDelete,
  isMobile: propIsMobile = false
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const { user } = useSelector((state: RootState) => state.auth);
  
  // State for selected receipts
  const [selectedReceipts, setSelectedReceipts] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  
  // Check if user can delete receipts
  const canDeleteReceipt = user?.role === "admin" || user?.role === "superadmin";
  
  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);
    
    if (checked) {
      // Select all receipts that the user can delete
      const selectableReceiptIds = canDeleteReceipt 
        ? receipts.map(receipt => receipt.id)
        : [];
      setSelectedReceipts(selectableReceiptIds);
    } else {
      // Deselect all receipts
      setSelectedReceipts([]);
    }
  };
  
  // Handle individual checkbox change
  const handleCheckboxChange = (receiptId: number, checked: boolean) => {
    if (checked) {
      setSelectedReceipts(prev => [...prev, receiptId]);
    } else {
      setSelectedReceipts(prev => prev.filter(id => id !== receiptId));
    }
  };
  
  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedReceipts.length > 0 && onBulkDelete) {
      onBulkDelete(selectedReceipts);
      setSelectedReceipts([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No receipts selected',
        description: 'Please select at least one receipt to delete.',
      });
    }
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when receipts are selected */}
      {selectedReceipts.length > 0 && canDeleteReceipt && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedReceipts.length} {selectedReceipts.length === 1 ? 'receipt' : 'receipts'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}
      
      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns={canDeleteReceipt && onDelete ? "50px 120px 150px 120px 120px 150px" : "120px 150px 120px 120px 150px"}
          minWidth={canDeleteReceipt && onDelete ? "750px" : "700px"}
        >
          {/* Mobile Headers */}
          {canDeleteReceipt && onDelete && (
            <TableHeader className="text-center">
              <Checkbox
                checked={selectAll}
                onChange={handleSelectAllChange}
                disabled={receipts.length === 0}
              />
            </TableHeader>
          )}
          <TableHeader>
            <span className="flex items-center">
              <PrinterOutlined className="mr-1" />
              Receipt ID
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShoppingCartOutlined className="mr-1" />
              Sale ID
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShopOutlined className="mr-1" />
              Store
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <CalendarOutlined className="mr-1" />
              Date
            </span>
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {receipts.map((receipt) => (
            <TableRow
              key={receipt.id}
              selected={selectedReceipts.includes(receipt.id)}
            >
              {canDeleteReceipt && onDelete && (
                <TableCell className="text-center">
                  <Checkbox
                    checked={selectedReceipts.includes(receipt.id)}
                    onChange={(e) => handleCheckboxChange(receipt.id, e.target.checked)}
                  />
                </TableCell>
              )}
              <TableCell>
                <span className="font-medium">#{receipt.id}</span>
              </TableCell>
              <TableCell>
                <span className="text-blue-600">#{receipt.saleId}</span>
              </TableCell>
              <TableCell>
                <div className="max-w-[120px] overflow-hidden text-ellipsis">
                  {receipt.storeName}
                </div>
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {formatDate(receipt.createdAt)}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View Receipt">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onViewReceipt(receipt.receiptUrl)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  <Tooltip title="Print Receipt">
                    <Button
                      icon={<PrinterOutlined />}
                      onClick={() => onPrintReceipt(receipt.receiptUrl)}
                      type="text"
                      className="edit-button text-blue-500 hover:text-blue-400"
                      size="small"
                    />
                  </Tooltip>
                  {canDeleteReceipt && onDelete && (
                    <Tooltip title="Delete">
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(receipt.id)}
                        type="text"
                        className="delete-button text-red-500 hover:text-red-400"
                        size="small"
                      />
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                {canDeleteReceipt && onDelete && (
                  <th scope="col" className="w-10 px-3 py-3 text-center">
                    <Checkbox
                      checked={selectAll}
                      onChange={handleSelectAllChange}
                      disabled={receipts.length === 0}
                    />
                  </th>
                )}

                {/* Receipt ID Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <PrinterOutlined className="mr-1" />
                    Receipt ID
                  </span>
                </th>
            
                {/* Sale ID Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShoppingCartOutlined className="mr-1" />
                    Sale ID
                  </span>
                </th>

                {/* Store Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShopOutlined className="mr-1" />
                    Store
                  </span>
                </th>

                {/* Created By Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <UserOutlined className="mr-1" />
                    Created By
                  </span>
                </th>

                {/* Date Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <CalendarOutlined className="mr-1" />
                    Date
                  </span>
                </th>

                {/* Actions Column */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {receipts.map((receipt) => (
                <tr key={receipt.id} className={selectedReceipts.includes(receipt.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  {canDeleteReceipt && onDelete && (
                    <td className="px-3 py-4 whitespace-nowrap text-center">
                      <Checkbox
                        checked={selectedReceipts.includes(receipt.id)}
                        onChange={(e) => handleCheckboxChange(receipt.id, e.target.checked)}
                      />
                    </td>
                  )}

                  {/* Receipt ID Column */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      #{receipt.id}
                    </div>
                  </td>

                  {/* Sale ID Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    #{receipt.saleId}
                  </td>

                  {/* Store Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {receipt.storeName || "N/A"}
                  </td>

                  {/* Created By Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {receipt.createdBy || "N/A"}
                  </td>

                  {/* Date Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatDate(receipt.createdAt)}
                  </td>

                  {/* Actions Column */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View Receipt">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onViewReceipt(receipt.receiptUrl)}
                          type="text"
                          className="view-button text-green-500"
                          size="middle"
                        />
                      </Tooltip>
                      <Tooltip title="Print Receipt">
                        <Button
                          icon={<PrinterOutlined />}
                          onClick={() => onPrintReceipt(receipt.receiptUrl)}
                          type="text"
                          className="edit-button text-blue-500"
                          size="middle"
                        />
                      </Tooltip>
                      {canDeleteReceipt && onDelete && (
                        <Tooltip title="Delete">
                          <Button
                            icon={<DeleteOutlined />}
                            onClick={() => onDelete(receipt.id)}
                            type="text"
                            className="delete-button text-red-500"
                            size="middle"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ReceiptTable;
