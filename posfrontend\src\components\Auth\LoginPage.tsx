"use client";

import React from "react";
import SigninWithPassword from "@/components/Auth/SigninWithPassword";
import Image from "next/image";
import RedirectIfAuthenticated from "@/components/Auth/RedirectIfAuthenticated";

const LoginPage: React.FC = () => {
  return (
    <RedirectIfAuthenticated>
      <div className="w-screen h-screen flex items-center justify-center bg-white px-4">
        <div className="flex flex-col md:flex-row w-full max-w-5xl h-auto md:h-[90vh] rounded-[20px] shadow-lg overflow-hidden">
          {/* Left - Login Form */}
          <div className="w-full md:w-1/2 flex items-center justify-center bg-white p-6 md:p-10 md:border-r border-gray-200">
            <SigninWithPassword />
          </div>

          {/* Right - Illustration - Hidden on mobile */}
          <div className="hidden md:flex w-full md:w-1/2 items-center justify-center bg-[#f7f9fc] p-10">
            <Image
              src="/images/signin.webp"
              alt="Login Illustration"
              width={800}
              height={800}
              className="object-contain w-full h-full"
            />
          </div>
        </div>
      </div>
    </RedirectIfAuthenticated>
  );
};

export default LoginPage;
