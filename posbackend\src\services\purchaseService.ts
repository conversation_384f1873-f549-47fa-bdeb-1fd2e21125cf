import { and, eq, count, desc, inArray } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { purchases, products, suppliers, users } from "../db/schema";
import { purchaseSchema } from "../validation/schema";
import { authorizeAction } from "../utils/authorizeAction";
import { checkPaymentStatus } from "../utils/checkPaymentStatus";

/**
 * ✅ **Create a Purchase**
 */
export const createPurchase = async (
  requester: JwtPayload,
  purchaseData: {
    supplierId?: number;
    productId: number;
    quantity: number;
    costPrice: string;
    totalCost: string;
  }
) => {
  purchaseSchema.parse(purchaseData);
  await authorizeAction(requester, "create", "purchases");

  const createdBy = requester.id;

  // ✅ Ensure product exists
  const productExists = await postgresDb
    .select()
    .from(products)
    .where(eq(products.id, purchaseData.productId))
    .limit(1);

  if (productExists.length === 0) {
    throw new Error("Product not found.");
  }

  // ✅ Insert new purchase record
  const [newPurchase] = await postgresDb
    .insert(purchases)
    .values({
      ...purchaseData,
      purchasedBy: createdBy,
      createdBy,
    })
    .returning();

  if (!newPurchase) throw new Error("Purchase creation failed.");

  return { purchase: newPurchase };
};

/**
 * ✅ **Get All Purchases (Paginated)**
 */
export const getAllPurchases = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  const offset = (page - 1) * limit;
  await authorizeAction(requester, "getAll", "purchases");

  const isSuperadmin = requester.role === "superadmin";

  const [purchasesData, totalPurchases] = await Promise.all([
    postgresDb
      .select({
        id: purchases.id,
        supplier: suppliers.name,
        product: products.name,
        quantity: purchases.quantity,
        costPrice: purchases.costPrice,
        totalCost: purchases.totalCost,
        purchaseDate: purchases.purchaseDate,
        purchasedBy: users.name,
      })
      .from(purchases)
      .leftJoin(suppliers, eq(purchases.supplierId, suppliers.id))
      .leftJoin(products, eq(purchases.productId, products.id))
      .leftJoin(users, eq(purchases.purchasedBy, users.id))
      .where(isSuperadmin ? undefined : eq(purchases.createdBy, requester.id))
      .orderBy(desc(purchases.purchaseDate))
      .limit(limit)
      .offset(offset),
    postgresDb
      .select({ count: count() })
      .from(purchases)
      .where(isSuperadmin ? undefined : eq(purchases.createdBy, requester.id)),
  ]);

  return {
    total: totalPurchases[0].count,
    page,
    perPage: limit,
    purchases: purchasesData,
  };
};

/**
 * ✅ **Get Purchase by ID**
 */
export const getPurchaseById = async (requester: JwtPayload, id: number) => {
  await authorizeAction(requester, "getById", "purchases", id);

  const isSuperadmin = requester.role === "superadmin";

  const purchaseData = await postgresDb
    .select({
      id: purchases.id,
      supplier: suppliers.name,
      product: products.name,
      quantity: purchases.quantity,
      costPrice: purchases.costPrice,
      totalCost: purchases.totalCost,
      purchaseDate: purchases.purchaseDate,
      purchasedBy: users.name,
    })
    .from(purchases)
    .leftJoin(suppliers, eq(purchases.supplierId, suppliers.id))
    .leftJoin(products, eq(purchases.productId, products.id))
    .leftJoin(users, eq(purchases.purchasedBy, users.id))
    .where(
      isSuperadmin
        ? eq(purchases.id, id)
        : and(eq(purchases.id, id), eq(purchases.createdBy, requester.id))
    )
    .limit(1);

  if (purchaseData.length === 0)
    throw new Error("Purchase not found or unauthorized.");

  return purchaseData[0];
};

/**
 * ✅ **Update Purchase by ID**
 */
export const updatePurchaseById = async (
  requester: JwtPayload,
  purchaseId: number,
  updateData: Partial<{
    quantity: number;
    costPrice: string;
    totalCost: string;
  }>
) => {
  await authorizeAction(requester, "update", "purchases", purchaseId);

  const isSuperadmin = requester.role === "superadmin";

  // ✅ Ensure the purchase exists
  const existingPurchase = await postgresDb
    .select()
    .from(purchases)
    .where(
      isSuperadmin
        ? eq(purchases.id, purchaseId)
        : and(
            eq(purchases.id, purchaseId),
            eq(purchases.createdBy, requester.id)
          )
    )
    .limit(1);

  if (existingPurchase.length === 0) {
    throw new Error("Purchase not found or unauthorized.");
  }

  // ✅ Update the purchase record
  const updatedPurchase = await postgresDb
    .update(purchases)
    .set(updateData)
    .where(eq(purchases.id, purchaseId))
    .returning();

  if (!updatedPurchase || updatedPurchase.length === 0) {
    throw new Error("Update failed: Purchase not found.");
  }

  return { updatedPurchase: updatedPurchase[0] };
};

/**
 * ✅ **Delete Purchase by ID (Single or Multiple)**
 */
export const deletePurchaseById = async (requester: JwtPayload, ids: number | number[]) => {
  const purchaseIds = Array.isArray(ids) ? ids : [ids];
  const isSuperadmin = requester.role === "superadmin";

  // Check authorization for each purchase
  for (const id of purchaseIds) {
    await authorizeAction(requester, "delete", "purchases", id);

    // Ensure the purchase exists
    const existingPurchase = await postgresDb
      .select()
      .from(purchases)
      .where(
        isSuperadmin
          ? eq(purchases.id, id)
          : and(eq(purchases.id, id), eq(purchases.createdBy, requester.id))
      )
      .limit(1);

    if (existingPurchase.length === 0) {
      throw new Error(`Purchase with ID ${id} not found or unauthorized.`);
    }
  }

  // Delete the purchases
  const deletedPurchases = await postgresDb
    .delete(purchases)
    .where(
      isSuperadmin
        ? inArray(purchases.id, purchaseIds)
        : and(inArray(purchases.id, purchaseIds), eq(purchases.createdBy, requester.id))
    )
    .returning({ deletedId: purchases.id });

  if (!deletedPurchases || deletedPurchases.length === 0) {
    throw new Error("Delete failed: Purchase(s) not found.");
  }

  return {
    deletedIds: deletedPurchases.map((purchase: { deletedId: number }) => purchase.deletedId)
  };
};
