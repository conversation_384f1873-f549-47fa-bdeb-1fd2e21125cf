exports.id=3712,exports.ids=[3712],exports.modules={31127:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},99261:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},43231:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},42608:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(11855),o=n(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M723 620.5C666.8 571.6 593.4 542 513 542s-153.8 29.6-210.1 78.6a8.1 8.1 0 00-.8 11.2l36 42.9c2.9 3.4 8 3.8 11.4.9C393.1 637.2 450.3 614 513 614s119.9 23.2 163.5 61.5c3.4 2.9 8.5 2.5 11.4-.9l36-42.9c2.8-3.3 2.4-8.3-.9-11.2zm117.4-140.1C751.7 406.5 637.6 362 513 362s-238.7 44.5-327.5 118.4a8.05 8.05 0 00-1 11.3l36 42.9c2.8 3.4 7.9 3.8 11.2 1C308 472.2 406.1 434 513 434s205 38.2 281.2 101.6c3.4 2.8 8.4 2.4 11.2-1l36-42.9c2.8-3.4 2.4-8.5-1-11.3zm116.7-139C835.7 241.8 680.3 182 511 182c-168.2 0-322.6 59-443.7 157.4a8 8 0 00-1.1 11.4l36 42.9c2.8 3.3 7.8 3.8 11.1 1.1C222 306.7 360.3 254 511 254c151.8 0 291 53.5 400 142.7 3.4 2.8 8.4 2.3 11.2-1.1l36-42.9c2.9-3.4 2.4-8.5-1.1-11.3zM448 778a64 64 0 10128 0 64 64 0 10-128 0z"}}]},name:"wifi",theme:"outlined"};var a=n(78480);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:l}))})},61667:(e,t,n)=>{"use strict";n.d(t,{A:()=>ev});var r=n(58009),o=n(99261),l=n(56073),a=n.n(l),i=n(21776),c=n(86866),s=n(55977),u=n(61849),d=n(55681),f=n(80799),p=n(67725),m=n(27343),g=n(76155),b=n(70001),y=n(11855);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var v=n(78480),x=r.forwardRef(function(e,t){return r.createElement(v.A,(0,y.A)({},e,{ref:t,icon:h}))}),O=n(73924),E=n(2866),w=n(71073),j=n(47285),S=n(13662),C=n(7974),P=n(1439);let A=(e,t,n,r)=>{let{titleMarginBottom:o,fontWeightStrong:l}=r;return{marginBottom:o,color:n,fontWeight:l,fontSize:e,lineHeight:t}},k=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t[`
      h${n}&,
      div&-h${n},
      div&-h${n} > textarea,
      h${n}
    `]=A(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)}),t},I=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,j.Y1)(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},M=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:C.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),T=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:`calc(1em - ${(0,P.zA)(n)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},D=e=>({[`${e.componentCls}-copy-success`]:{[`
    &,
    &:hover,
    &:focus`]:{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),R=()=>({[`
  a&-ellipsis,
  span&-ellipsis
  `]:{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),B=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},[`
        div&,
        p
      `]:{marginBottom:"1em"}},k(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:n},[`
      div,
      ul,
      li,
      p,
      h1,
      h2,
      h3,
      h4,
      h5`]:{[`
        + h1,
        + h2,
        + h3,
        + h4,
        + h5
        `]:{marginTop:n}}}),M(e)),I(e)),{[`
        ${t}-expand,
        ${t}-collapse,
        ${t}-edit,
        ${t}-copy
      `]:Object.assign(Object.assign({},(0,j.Y1)(e)),{marginInlineStart:e.marginXXS})}),T(e)),D(e)),R()),{"&-rtl":{direction:"rtl"}})}},_=(0,S.OF)("Typography",e=>[B(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),$=e=>{let{prefixCls:t,"aria-label":n,className:o,style:l,direction:i,maxLength:c,autoSize:s=!0,value:u,onSave:d,onCancel:f,onEnd:p,component:m,enterIcon:g=r.createElement(x,null)}=e,b=r.useRef(null),y=r.useRef(!1),h=r.useRef(null),[v,j]=r.useState(u);r.useEffect(()=>{j(u)},[u]),r.useEffect(()=>{var e;if(null===(e=b.current)||void 0===e?void 0:e.resizableTextArea){let{textArea:e}=b.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let S=()=>{d(v.trim())},[C,P,A]=_(t),k=a()(t,`${t}-edit-content`,{[`${t}-rtl`]:"rtl"===i,[`${t}-${m}`]:!!m},o,P,A);return C(r.createElement("div",{className:k,style:l},r.createElement(w.A,{ref:b,maxLength:c,value:v,onChange:e=>{let{target:t}=e;j(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(h.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:r,metaKey:o,shiftKey:l}=e;h.current!==t||y.current||n||r||o||l||(t===O.A.ENTER?(S(),null==p||p()):t===O.A.ESC&&f())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{S()},"aria-label":n,rows:1,autoSize:s}),null!==g?(0,E.Ob)(g,{className:`${t}-edit-content-confirm`}):null))};var L=n(35121),z=n.n(L),N=n(25392);let H=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&null==e?[]:Array.isArray(e)?e:[e]},W=e=>{let{copyConfig:t,children:n}=e,[o,l]=r.useState(!1),[a,i]=r.useState(!1),c=r.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},u={};return t.format&&(u.format=t.format),r.useEffect(()=>s,[]),{copied:o,copyLoading:a,onClick:(0,N.A)(e=>(function(e,t,n,r){return new(n||(n=Promise))(function(o,l){function a(e){try{c(r.next(e))}catch(e){l(e)}}function i(e){try{c(r.throw(e))}catch(e){l(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,i)}c((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var r;null==e||e.preventDefault(),null==e||e.stopPropagation(),i(!0);try{let o="function"==typeof t.text?yield t.text():t.text;z()(o||H(n,!0).join("")||"",u),i(!1),l(!0),s(),c.current=setTimeout(()=>{l(!1)},3e3),null===(r=t.onCopy)||void 0===r||r.call(t,e)}catch(e){throw i(!1),e}}))}};function F(e,t){return r.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}let U=e=>{let t=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{t.current=e}),t.current},K=(e,t,n)=>(0,r.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,r.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]);var q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let V=r.forwardRef((e,t)=>{let{prefixCls:n,component:o="article",className:l,rootClassName:i,setContentRef:c,children:s,direction:u,style:d}=e,p=q(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:g,direction:b,className:y,style:h}=(0,m.TP)("typography"),v=c?(0,f.K4)(t,c):t,x=g("typography",n),[O,E,w]=_(x),j=a()(x,y,{[`${x}-rtl`]:"rtl"===(null!=u?u:b)},l,i,E,w),S=Object.assign(Object.assign({},h),d);return O(r.createElement(o,Object.assign({className:j,style:S,ref:v},p),s))});var X=n(31127);let Q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var Y=r.forwardRef(function(e,t){return r.createElement(v.A,(0,y.A)({},e,{ref:t,icon:Q}))}),Z=n(88752);function G(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function J(e,t,n){return!0===e||void 0===e?t:e||n&&t}let ee=e=>["string","number"].includes(typeof e),et=e=>{let{prefixCls:t,copied:n,locale:o,iconOnly:l,tooltips:i,icon:c,tabIndex:s,onCopy:u,loading:d}=e,f=G(i),p=G(c),{copied:m,copy:g}=null!=o?o:{},y=n?m:g,h=J(f[n?1:0],y),v="string"==typeof h?h:y;return r.createElement(b.A,{title:h},r.createElement("button",{type:"button",className:a()(`${t}-copy`,{[`${t}-copy-success`]:n,[`${t}-copy-icon-only`]:l}),onClick:u,"aria-label":v,tabIndex:s},n?J(p[1],r.createElement(X.A,null),!0):J(p[0],d?r.createElement(Z.A,null):r.createElement(Y,null),!0)))};var en=n(43984);let er=r.forwardRef((e,t)=>{let{style:n,children:o}=e,l=r.useRef(null);return r.useImperativeHandle(t,()=>({isExceed:()=>{let e=l.current;return e.scrollHeight>e.clientHeight},getHeight:()=>l.current.clientHeight})),r.createElement("span",{"aria-hidden":!0,ref:l,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},o)}),eo=e=>e.reduce((e,t)=>e+(ee(t)?String(t).length:1),0);function el(e,t){let n=0,r=[];for(let o=0;o<e.length;o+=1){if(n===t)return r;let l=e[o],a=n+(ee(l)?String(l).length:1);if(a>t){let e=t-n;return r.push(String(l).slice(0,e)),r}r.push(l),n=a}return e}let ea={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ei(e){let{enableMeasure:t,width:n,text:o,children:l,rows:a,expanded:i,miscDeps:u,onEllipsis:d}=e,f=r.useMemo(()=>(0,c.A)(o),[o]),p=r.useMemo(()=>eo(f),[o]),m=r.useMemo(()=>l(f,!1),[o]),[g,b]=r.useState(null),y=r.useRef(null),h=r.useRef(null),v=r.useRef(null),x=r.useRef(null),O=r.useRef(null),[E,w]=r.useState(!1),[j,S]=r.useState(0),[C,P]=r.useState(0),[A,k]=r.useState(null);(0,s.A)(()=>{t&&n&&p?S(1):S(0)},[n,o,a,t,f]),(0,s.A)(()=>{var e,t,n,r;if(1===j)S(2),k(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===j){let o=!!(null===(e=v.current)||void 0===e?void 0:e.isExceed());S(o?3:4),b(o?[0,p]:null),w(o),P(Math.max((null===(t=v.current)||void 0===t?void 0:t.getHeight())||0,(1===a?0:(null===(n=x.current)||void 0===n?void 0:n.getHeight())||0)+((null===(r=O.current)||void 0===r?void 0:r.getHeight())||0))+1),d(o)}},[j]);let I=g?Math.ceil((g[0]+g[1])/2):0;(0,s.A)(()=>{var e;let[t,n]=g||[0,0];if(t!==n){let r=((null===(e=y.current)||void 0===e?void 0:e.getHeight())||0)>C,o=I;n-t==1&&(o=r?t:n),b(r?[t,o]:[o,n])}},[g,I]);let M=r.useMemo(()=>{if(!t)return l(f,!1);if(3!==j||!g||g[0]!==g[1]){let e=l(f,!1);return[4,0].includes(j)?e:r.createElement("span",{style:Object.assign(Object.assign({},ea),{WebkitLineClamp:a})},e)}return l(i?f:el(f,g[0]),E)},[i,j,g,f].concat((0,en.A)(u))),T={width:n,margin:0,padding:0,whiteSpace:"nowrap"===A?"normal":"inherit"};return r.createElement(r.Fragment,null,M,2===j&&r.createElement(r.Fragment,null,r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},T),ea),{WebkitLineClamp:a}),ref:v},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},T),ea),{WebkitLineClamp:a-1}),ref:x},m),r.createElement(er,{style:Object.assign(Object.assign(Object.assign({},T),ea),{WebkitLineClamp:1}),ref:O},l([],!0))),3===j&&g&&g[0]!==g[1]&&r.createElement(er,{style:Object.assign(Object.assign({},T),{top:400}),ref:y},l(el(f,I),!0)),1===j&&r.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}let ec=e=>{let{enableEllipsis:t,isEllipsis:n,children:o,tooltipProps:l}=e;return(null==l?void 0:l.title)&&t?r.createElement(b.A,Object.assign({open:!!n&&void 0},l),o):o};var es=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eu=r.forwardRef((e,t)=>{var n;let{prefixCls:l,className:y,style:h,type:v,disabled:x,children:O,ellipsis:E,editable:w,copyable:j,component:S,title:C}=e,P=es(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:A,direction:k}=r.useContext(m.QO),[I]=(0,g.A)("Text"),M=r.useRef(null),T=r.useRef(null),D=A("typography",l),R=(0,d.A)(P,["mark","code","delete","underline","strong","keyboard","italic"]),[B,_]=F(w),[L,z]=(0,u.A)(!1,{value:_.editing}),{triggerType:N=["icon"]}=_,H=e=>{var t;e&&(null===(t=_.onStart)||void 0===t||t.call(_)),z(e)},q=U(L);(0,s.A)(()=>{var e;!L&&q&&(null===(e=T.current)||void 0===e||e.focus())},[L]);let X=e=>{null==e||e.preventDefault(),H(!0)},[Q,Y]=F(j),{copied:Z,copyLoading:G,onClick:J}=W({copyConfig:Y,children:O}),[en,er]=r.useState(!1),[eo,el]=r.useState(!1),[ea,eu]=r.useState(!1),[ed,ef]=r.useState(!1),[ep,em]=r.useState(!0),[eg,eb]=F(E,{expandable:!1,symbol:e=>e?null==I?void 0:I.collapse:null==I?void 0:I.expand}),[ey,eh]=(0,u.A)(eb.defaultExpanded||!1,{value:eb.expanded}),ev=eg&&(!ey||"collapsible"===eb.expandable),{rows:ex=1}=eb,eO=r.useMemo(()=>ev&&(void 0!==eb.suffix||eb.onEllipsis||eb.expandable||B||Q),[ev,eb,B,Q]);(0,s.A)(()=>{eg&&!eO&&(er((0,p.F)("webkitLineClamp")),el((0,p.F)("textOverflow")))},[eO,eg]);let[eE,ew]=r.useState(ev),ej=r.useMemo(()=>!eO&&(1===ex?eo:en),[eO,eo,en]);(0,s.A)(()=>{ew(ej&&ev)},[ej,ev]);let eS=ev&&(eE?ed:ea),eC=ev&&1===ex&&eE,eP=ev&&ex>1&&eE,eA=(e,t)=>{var n;eh(t.expanded),null===(n=eb.onExpand)||void 0===n||n.call(eb,e,t)},[ek,eI]=r.useState(0),eM=e=>{var t;eu(e),ea!==e&&(null===(t=eb.onEllipsis)||void 0===t||t.call(eb,e))};r.useEffect(()=>{let e=M.current;if(eg&&eE&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),r=t.getBoundingClientRect();return e.removeChild(t),n.left>r.left||r.right>n.right||n.top>r.top||r.bottom>n.bottom}(e);ed!==t&&ef(t)}},[eg,eE,O,eP,ep,ek]),r.useEffect(()=>{let e=M.current;if("undefined"==typeof IntersectionObserver||!e||!eE||!ev)return;let t=new IntersectionObserver(()=>{em(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[eE,ev]);let eT=K(eb.tooltip,_.text,O),eD=r.useMemo(()=>{if(eg&&!eE)return[_.text,O,C,eT.title].find(ee)},[eg,eE,C,eT.title,eS]);if(L)return r.createElement($,{value:null!==(n=_.text)&&void 0!==n?n:"string"==typeof O?O:"",onSave:e=>{var t;null===(t=_.onChange)||void 0===t||t.call(_,e),H(!1)},onCancel:()=>{var e;null===(e=_.onCancel)||void 0===e||e.call(_),H(!1)},onEnd:_.onEnd,prefixCls:D,className:y,style:h,direction:k,component:S,maxLength:_.maxLength,autoSize:_.autoSize,enterIcon:_.enterIcon});let eR=()=>{let{expandable:e,symbol:t}=eb;return e?r.createElement("button",{type:"button",key:"expand",className:`${D}-${ey?"collapse":"expand"}`,onClick:e=>eA(e,{expanded:!ey}),"aria-label":ey?I.collapse:null==I?void 0:I.expand},"function"==typeof t?t(ey):t):null},eB=()=>{if(!B)return;let{icon:e,tooltip:t,tabIndex:n}=_,l=(0,c.A)(t)[0]||(null==I?void 0:I.edit),a="string"==typeof l?l:"";return N.includes("icon")?r.createElement(b.A,{key:"edit",title:!1===t?"":l},r.createElement("button",{type:"button",ref:T,className:`${D}-edit`,onClick:X,"aria-label":a,tabIndex:n},e||r.createElement(o.A,{role:"button"}))):null},e_=()=>Q?r.createElement(et,Object.assign({key:"copy"},Y,{prefixCls:D,copied:Z,locale:I,onCopy:J,loading:G,iconOnly:null==O})):null,e$=e=>[e&&eR(),eB(),e_()],eL=e=>[e&&!ey&&r.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),eb.suffix,e$(e)];return r.createElement(i.A,{onResize:e=>{let{offsetWidth:t}=e;eI(t)},disabled:!ev},n=>r.createElement(ec,{tooltipProps:eT,enableEllipsis:ev,isEllipsis:eS},r.createElement(V,Object.assign({className:a()({[`${D}-${v}`]:v,[`${D}-disabled`]:x,[`${D}-ellipsis`]:eg,[`${D}-ellipsis-single-line`]:eC,[`${D}-ellipsis-multiple-line`]:eP},y),prefixCls:l,style:Object.assign(Object.assign({},h),{WebkitLineClamp:eP?ex:void 0}),component:S,ref:(0,f.K4)(n,M,t),direction:k,onClick:N.includes("text")?X:void 0,"aria-label":null==eD?void 0:eD.toString(),title:C},R),r.createElement(ei,{enableMeasure:ev&&!eE,text:O,rows:ex,width:ek,onEllipsis:eM,expanded:ey,miscDeps:[Z,ey,G,B,Q,I]},(t,n)=>(function(e,t){let{mark:n,code:o,underline:l,delete:a,strong:i,keyboard:c,italic:s}=e,u=t;function d(e,t){t&&(u=r.createElement(e,{},u))}return d("strong",i),d("u",l),d("del",a),d("code",o),d("mark",n),d("kbd",c),d("i",s),u})(e,r.createElement(r.Fragment,null,t.length>0&&n&&!ey&&eD?r.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eL(n)))))))});var ed=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ef=r.forwardRef((e,t)=>{var{ellipsis:n,rel:o}=e,l=ed(e,["ellipsis","rel"]);let a=Object.assign(Object.assign({},l),{rel:void 0===o&&"_blank"===l.target?"noopener noreferrer":o});return delete a.navigate,r.createElement(eu,Object.assign({},a,{ref:t,ellipsis:!!n,component:"a"}))}),ep=r.forwardRef((e,t)=>r.createElement(eu,Object.assign({ref:t},e,{component:"div"})));var em=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eg=r.forwardRef((e,t)=>{var{ellipsis:n}=e,o=em(e,["ellipsis"]);let l=r.useMemo(()=>n&&"object"==typeof n?(0,d.A)(n,["expandable","rows"]):n,[n]);return r.createElement(eu,Object.assign({ref:t},o,{ellipsis:l,component:"span"}))});var eb=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ey=[1,2,3,4,5],eh=r.forwardRef((e,t)=>{let{level:n=1}=e,o=eb(e,["level"]),l=ey.includes(n)?`h${n}`:"h1";return r.createElement(eu,Object.assign({ref:t},o,{component:l}))});V.Text=eg,V.Link=ef,V.Title=eh,V.Paragraph=ep;let ev=V},35121:(e,t,n)=>{"use strict";var r=n(40298),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,l,a,i,c,s,u,d,f=!1;t||(t={}),a=t.debug||!1;try{if(c=r(),s=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=o[t.format]||o.default;window.clipboardData.setData(r,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(d),s.selectNodeContents(d),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");f=!0}catch(r){a&&console.error("unable to copy using execCommand: ",r),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),f=!0}catch(r){a&&console.error("unable to copy using clipboardData: ",r),a&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",l=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=n.replace(/#{\s*key\s*}/g,l),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),d&&document.body.removeChild(d),c()}return f}},54380:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return l}});let r=n(74147),o=n(54887);function l(e,t){return(0,o.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28531:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return m}});let r=n(25488),o=n(45512),l=r._(n(58009)),a=n(35024),i=n(47829),c=n(59118),s=n(45267),u=n(73727),d=n(43438),f=n(54380);function p(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}n(76831);let m=l.default.forwardRef(function(e,t){let n,r;let{href:a,as:m,children:g,prefetch:b=null,passHref:y,replace:h,shallow:v,scroll:x,onClick:O,onMouseEnter:E,onTouchStart:w,legacyBehavior:j=!1,...S}=e;n=g,j&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let C=l.default.useContext(i.AppRouterContext),P=null===b?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:A,as:k}=l.default.useMemo(()=>{let e=p(a);return{href:e,as:m?p(m):e}},[a,m]),I=l.default.useRef(A),M=l.default.useRef(k);j&&(r=l.default.Children.only(n));let T=j?r&&"object"==typeof r&&r.ref:t,[D,R,B]=(0,c.useIntersection)({rootMargin:"200px"}),_=l.default.useCallback(e=>{(M.current!==k||I.current!==A)&&(B(),M.current=k,I.current=A),D(e)},[k,A,B,D]),$=(0,u.useMergedRef)(_,T);l.default.useEffect(()=>{},[k,A,R,!1!==b,C,P]);let L={ref:$,onClick(e){j||"function"!=typeof O||O(e),j&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),C&&!e.defaultPrevented&&function(e,t,n,r,o,a,i){let{nodeName:c}=e.currentTarget;"A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||(e.preventDefault(),l.default.startTransition(()=>{let e=null==i||i;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:a,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})}))}(e,C,A,k,h,v,x)},onMouseEnter(e){j||"function"!=typeof E||E(e),j&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e)},onTouchStart:function(e){j||"function"!=typeof w||w(e),j&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e)}};return(0,d.isAbsoluteUrl)(k)?L.href=k:j&&!y&&("a"!==r.type||"href"in r.props)||(L.href=(0,f.addBasePath)(k)),j?l.default.cloneElement(r,L):(0,o.jsx)("a",{...S,...L,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54887:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let r=n(35612),o=n(31546),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:l}=(0,o.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28903:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59118:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return c}});let r=n(58009),o=n(28903),l="function"==typeof IntersectionObserver,a=new Map,i=[];function c(e){let{rootRef:t,rootMargin:n,disabled:c}=e,s=c||!l,[u,d]=(0,r.useState)(!1),f=(0,r.useRef)(null),p=(0,r.useCallback)(e=>{f.current=e},[]);return(0,r.useEffect)(()=>{if(l){if(s||u)return;let e=f.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:l}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=i.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=a.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},i.push(n),a.set(n,t),t}(n);return l.set(e,t),o.observe(e),function(){if(l.delete(e),o.unobserve(e),0===l.size){o.disconnect(),a.delete(r);let e=i.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!u){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,n,t,u,f.current]),[p,u,(0,r.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74147:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let r=n(31546);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:o,hash:l}=(0,r.parsePath)(e);return""+t+n+o+l}},35024:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});let r=n(81063)._(n(33866)),o=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:n}=e,l=e.protocol||"",a=e.pathname||"",i=e.hash||"",c=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),c&&"object"==typeof c&&(c=String(r.urlQueryToSearchParams(c)));let u=e.search||c&&"?"+c||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||o.test(l))&&!1!==s?(s="//"+(s||""),a&&"/"!==a[0]&&(a="/"+a)):s||(s=""),i&&"#"!==i[0]&&(i="#"+i),u&&"?"!==u[0]&&(u="?"+u),""+l+s+(a=a.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+i}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return l(e)}},31546:(e,t)=>{"use strict";function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},33866:(e,t)=>{"use strict";function n(e){let t={};return e.forEach((e,n)=>{void 0===t[n]?t[n]=e:Array.isArray(t[n])?t[n].push(e):t[n]=[t[n],e]}),t}function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,o]=e;Array.isArray(o)?o.forEach(e=>t.append(n,r(e))):t.set(n,r(o))}),t}function l(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,n)=>e.append(n,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},35612:(e,t)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},43438:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return h},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return b},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return c},getLocationOrigin:function(){return a},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),l=0;l<r;l++)o[l]=arguments[l];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=a();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.');return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class h extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},67725:(e,t,n)=>{"use strict";n.d(t,{F:()=>a});var r=n(7822),o=function(e){if((0,r.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some(function(e){return e in n.style})}return!1},l=function(e,t){if(!o(e))return!1;var n=document.createElement("div"),r=n.style[e];return n.style[e]=t,n.style[e]!==r};function a(e,t){return Array.isArray(e)||void 0===t?o(e):l(e,t)}},40298:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},12439:(e,t,n)=>{"use strict";let r,o;n.d(t,{P2:()=>f});let l=(e,t)=>t.some(t=>e instanceof t),a=new WeakMap,i=new WeakMap,c=new WeakMap,s={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return a.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return u(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function u(e){var t;if(e instanceof IDBRequest)return function(e){let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("success",o),e.removeEventListener("error",l)},o=()=>{t(u(e.result)),r()},l=()=>{n(e.error),r()};e.addEventListener("success",o),e.addEventListener("error",l)});return c.set(t,e),t}(e);if(i.has(e))return i.get(e);let n="function"==typeof(t=e)?(o||(o=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(d(this),e),u(this.request)}:function(...e){return u(t.apply(d(this),e))}:(t instanceof IDBTransaction&&function(e){if(a.has(e))return;let t=new Promise((t,n)=>{let r=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",l),e.removeEventListener("abort",l)},o=()=>{t(),r()},l=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",o),e.addEventListener("error",l),e.addEventListener("abort",l)});a.set(e,t)}(t),l(t,r||(r=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,s):t;return n!==e&&(i.set(e,n),c.set(n,e)),n}let d=e=>c.get(e);function f(e,t,{blocked:n,upgrade:r,blocking:o,terminated:l}={}){let a=indexedDB.open(e,t),i=u(a);return r&&a.addEventListener("upgradeneeded",e=>{r(u(a.result),e.oldVersion,e.newVersion,u(a.transaction),e)}),n&&a.addEventListener("blocked",e=>n(e.oldVersion,e.newVersion,e)),i.then(e=>{l&&e.addEventListener("close",()=>l()),o&&e.addEventListener("versionchange",e=>o(e.oldVersion,e.newVersion,e))}).catch(()=>{}),i}let p=["get","getKey","getAll","getAllKeys","count"],m=["put","add","delete","clear"],g=new Map;function b(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(g.get(t))return g.get(t);let n=t.replace(/FromIndex$/,""),r=t!==n,o=m.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(o||p.includes(n)))return;let l=async function(e,...t){let l=this.transaction(e,o?"readwrite":"readonly"),a=l.store;return r&&(a=a.index(t.shift())),(await Promise.all([a[n](...t),o&&l.done]))[0]};return g.set(t,l),l}s=(e=>({...e,get:(t,n,r)=>b(t,n)||e.get(t,n,r),has:(t,n)=>!!b(t,n)||e.has(t,n)}))(s);let y=["continue","continuePrimaryKey","advance"],h={},v=new WeakMap,x=new WeakMap,O={get(e,t){if(!y.includes(t))return e[t];let n=h[t];return n||(n=h[t]=function(...e){v.set(this,x.get(this)[t](...e))}),n}};async function*E(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;let n=new Proxy(t,O);for(x.set(n,t),c.set(n,d(t));t;)yield n,t=await (v.get(n)||t.continue()),v.delete(n)}function w(e,t){return t===Symbol.asyncIterator&&l(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&l(e,[IDBIndex,IDBObjectStore])}s=(e=>({...e,get:(t,n,r)=>w(t,n)?E:e.get(t,n,r),has:(t,n)=>w(t,n)||e.has(t,n)}))(s)}};