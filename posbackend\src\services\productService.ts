import { postgresDb } from "../db/db";
import { products, users } from "../db/schema";
import { eq, and, count, desc, sql, inArray, or, like } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { productSchema } from "../validation/schema";
import { authorizeAction } from "../utils/authorizeAction";
// REMOVED: Cache import to ensure fresh data
// import cache from "../config/lruCache";

// ✅ **Bulk Create Products**
export const createProduct = async (
  requester: JwtPayload,
  productsData: Array<{
    name: string;
    categoryId: number;
    sku?: string;
    barcode?: string;
    price: number;
    cost: number;
    stockQuantity?: number;
    minStockLevel?: number;
    expiryDate?: Date | string | null;
    imageUrl?: string | null;
  }>
) => {
  if (!Array.isArray(productsData) || productsData.length === 0) {
    throw new Error("Invalid input: Provide at least one product.");
  }

  // Validate and transform product data
  const validatedProducts = productsData.map((product) => {
    const validatedProduct = productSchema.parse(product);

    return {
      ...validatedProduct,
      expiryDate:
        validatedProduct.expiryDate instanceof Date
          ? validatedProduct.expiryDate
          : validatedProduct.expiryDate
          ? new Date(validatedProduct.expiryDate)
          : null,
    };
  });

  // 🔹 Ensure the user is authorized for bulk creation
  await authorizeAction(requester, "create", "product");

  const createdBy = requester.id;

  // Format product data for insertion
  const formattedProducts = validatedProducts.map((product) => ({
    ...product,
    price: product.price.toString(),
    cost: product.cost.toString(),
    createdBy,
    createdAt: new Date(),
    imageUrl: product.imageUrl || null,
  }));

  // Bulk insert
  const newProducts = await postgresDb.insert(products).values(formattedProducts).returning();

  if (!newProducts || newProducts.length === 0) throw new Error("Product creation failed.");

  return { products: newProducts };
};


// ✅ **Get All Products (Without Caching)**
export const getAllProducts = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
) => {
  const offset = (page - 1) * limit;

  // 🔹 Get the authorized query based on user role
  const query = authorizeAction(requester, "getAll", "product");
  if (!query) throw new Error("Unauthorized: Unable to fetch products.");

  // Create search condition if search term is provided
  let searchCondition = undefined;
  if (searchTerm && searchTerm.trim() !== '') {
    // Use case-insensitive search with wildcards on both sides
    const searchPattern = `%${searchTerm.trim()}%`;

    // Create search condition for name, SKU, and barcode
    searchCondition = or(
      like(products.name, searchPattern),
      like(products.sku || '', searchPattern),
      like(products.barcode || '', searchPattern)
    );
  }

  // For cashiers, we need to get products created by their admin
  let productsQuery;
  let countQuery;
  let adminId: number | undefined;

  // Handle cashier flow
  if (requester.role === "cashier") {
    // Find the cashier's admin ID
    const adminResult = await postgresDb
      .select({ adminId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    adminId = adminResult[0]?.adminId || undefined;

    if (!adminId) {
      return { total: 0, page, perPage: limit, products: [] };
    }
  }

  // 🔥 Optimize query building with common fields
  const commonSelectFields = {
    id: products.id,
    name: products.name,
    categoryId: products.categoryId,
    sku: products.sku,
    barcode: products.barcode,
    price: products.price,
    cost: products.cost,
    stockQuantity: products.stockQuantity,
    minStockLevel: products.minStockLevel,
    expiryDate: products.expiryDate,
    createdBy: products.createdBy,
    createdAt: products.createdAt,
    imageUrl: products.imageUrl,
  };

  // Build queries based on role
  if (requester.role === "cashier" && adminId) {
    // Fetch products created by the cashier's admin
    const whereCondition = searchCondition
      ? and(eq(products.createdBy, adminId), searchCondition)
      : eq(products.createdBy, adminId);

    productsQuery = postgresDb
      .select(commonSelectFields)
      .from(products)
      .where(whereCondition)
      .orderBy(desc(products.createdAt))
      .limit(limit)
      .offset(offset);

    // Count total products for pagination
    countQuery = postgresDb
      .select({ count: count() })
      .from(products)
      .where(whereCondition);

  } else if (requester.role === "admin") {
    // Admin can only see products they created
    const whereCondition = searchCondition
      ? and(eq(products.createdBy, requester.id), searchCondition)
      : eq(products.createdBy, requester.id);

    productsQuery = postgresDb
      .select(commonSelectFields)
      .from(products)
      .where(whereCondition)
      .orderBy(desc(products.createdAt))
      .limit(limit)
      .offset(offset);

    // Count total products for pagination
    countQuery = postgresDb
      .select({ count: count() })
      .from(products)
      .where(whereCondition);

  } else {
    // Superadmin can see all products
    productsQuery = postgresDb
      .select(commonSelectFields)
      .from(products)
      .where(searchCondition || sql`1=1`)
      .orderBy(desc(products.createdAt))
      .limit(limit)
      .offset(offset);

    // Count total products for pagination
    countQuery = postgresDb
      .select({ count: count() })
      .from(products)
      .where(searchCondition || sql`1=1`);
  }

  // Execute queries in parallel
  const [productData, totalResult] = await Promise.all([
    productsQuery,
    countQuery
  ]);

  // Prepare the result
  const result = {
    total: totalResult[0]?.count || 0,
    page,
    perPage: limit,
    products: productData.length > 0 ? productData : [],
  };

  return result;
};
// ✅ **Get Product by ID (Without Caching)**
export const getProductById = async (requester: JwtPayload, id: number) => {

  // 🔹 Get the authorized query based on user role
  const query = authorizeAction(requester, "getById", "product", id);
  if (!query) throw new Error("Unauthorized or invalid request.");

  let productQuery;
  let adminId: number | undefined;

  if (requester.role === "cashier") {
    // Find the cashier's admin ID
    const adminResult = await postgresDb
      .select({ adminId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    adminId = adminResult[0]?.adminId || undefined;

    if (!adminId) {
      throw new Error("Product not found or you don't have permission to view it.");
    }

    // Fetch product created by the cashier's admin
    productQuery = postgresDb
      .select()
      .from(products)
      .where(and(
        eq(products.id, id),
        eq(products.createdBy, adminId)
      ))
      .limit(1);
  } else if (requester.role === "admin") {
    // Admin can only see products they created
    productQuery = postgresDb
      .select()
      .from(products)
      .where(and(
        eq(products.id, id),
        eq(products.createdBy, requester.id)
      ))
      .limit(1);
  } else {
    // Superadmin can see all products
    productQuery = postgresDb
      .select()
      .from(products)
      .where(eq(products.id, id))
      .limit(1);
  }

  // Execute query
  const productData = await productQuery;

  if (productData.length === 0) {
    throw new Error("Product not found or you don't have permission to view it.");
  }

  const product = productData[0];
  return product;
};

// ✅ **Get Product by Barcode (For Barcode Scanning)**
export const getProductByBarcode = async (requester: JwtPayload, barcode: string) => {
  // 🔹 Get the authorized query based on user role
  const query = authorizeAction(requester, "getAll", "product");
  if (!query) throw new Error("Unauthorized or invalid request.");

  let productQuery;
  let adminId: number | undefined;

  if (requester.role === "cashier") {
    // Find the cashier's admin ID
    const adminResult = await postgresDb
      .select({ adminId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    adminId = adminResult[0]?.adminId || undefined;

    if (!adminId) {
      throw new Error("Product not found or you don't have permission to view it.");
    }

    // Search for product by barcode or SKU created by the cashier's admin
    productQuery = postgresDb
      .select()
      .from(products)
      .where(and(
        eq(products.createdBy, adminId),
        or(
          eq(products.barcode, barcode),
          eq(products.sku, barcode)
        )
      ))
      .limit(1);
  } else if (requester.role === "admin") {
    // Admin can only see products they created
    productQuery = postgresDb
      .select()
      .from(products)
      .where(and(
        eq(products.createdBy, requester.id),
        or(
          eq(products.barcode, barcode),
          eq(products.sku, barcode)
        )
      ))
      .limit(1);
  } else {
    // Superadmin can see all products
    productQuery = postgresDb
      .select()
      .from(products)
      .where(or(
        eq(products.barcode, barcode),
        eq(products.sku, barcode)
      ))
      .limit(1);
  }

  // Execute query
  const productData = await productQuery;

  if (productData.length === 0) {
    return null; // Return null instead of throwing error for barcode scanning
  }

  const product = productData[0];
  return product;
};

// ✅ **Update Product**
export const updateProductById = async (
  requester: JwtPayload,
  productId: number,
  updateData: Partial<{
    name: string;
    categoryId: number;
    sku?: string;
    barcode?: string;
    price: number;
    cost: number;
    stockQuantity?: number;
    minStockLevel?: number;
    expiryDate?: Date;
    imageUrl?: string | null;
  }>
) => {
  // 🔹 Use authorization utility
  const query = await authorizeAction(requester, "update", "product", productId);
  if (!query) throw new Error("Unauthorized or invalid request.");

  // Convert `price` and `cost` to strings if they exist
  const sanitizedUpdateData = {
    ...updateData,
    price: updateData.price !== undefined ? updateData.price.toString() : undefined,
    cost: updateData.cost !== undefined ? updateData.cost.toString() : undefined,
    expiryDate: updateData.expiryDate ? new Date(updateData.expiryDate) : undefined,
    imageUrl: updateData.imageUrl !== undefined ? updateData.imageUrl : undefined,
  };

  const updatedProduct = await postgresDb
    .update(products)
    .set(sanitizedUpdateData)
    .where(eq(products.id, productId))
    .returning();

  if (!updatedProduct || updatedProduct.length === 0) {
    throw new Error("Update failed: Product not found.");
  }

  // No cache invalidation needed

  return {
    message: "Product updated successfully.",
    updatedProduct: updatedProduct[0],
  };
};
// ✅ **Bulk Delete Products**
export const deleteProductById = async (requester: JwtPayload, ids: number | number[]) => {
  const productIds = Array.isArray(ids) ? ids : [ids];

  // 🔹 Ensure authorization for each ID
  await Promise.all(productIds.map(id => authorizeAction(requester, "delete", "product", id)));

  const deletedProducts = await postgresDb
    .delete(products)
    .where(inArray(products.id, productIds))
    .returning({ deletedId: products.id });

  if (!deletedProducts || deletedProducts.length === 0) {
    throw new Error("Delete failed: Product(s) not found.");
  }

  // No cache invalidation needed

  return {
    deletedIds: deletedProducts.map((product: { deletedId: number }) => product.deletedId),
  };
};
