import swagger<PERSON>SD<PERSON> from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'POS System API',
      version: '1.0.0',
      description: `
        A comprehensive Point of Sale (POS) system API built with Node.js, Express, and PostgreSQL.

        ## Features
        - **Multi-tenant Architecture**: Support for multiple stores and users
        - **Role-based Access Control**: SuperAdmin, Admin, and Cashier roles
        - **Payment Integration**: Paystack payment gateway integration
        - **Inventory Management**: Products, categories, suppliers, and stock adjustments
        - **Sales Management**: Complete sales workflow with receipts
        - **Dashboard Analytics**: Real-time business insights

        ## Authentication
        This API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:
        \`Authorization: Bearer <your-jwt-token>\`

        ## Base URL
        \`http://localhost:5005/api/v1\`

        ## Currency
        All monetary values are in Ghanaian Cedis (GHS).
      `,
      contact: {
        name: 'POS System Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:5005/api/v1',
        description: 'Development server'
      },
      {
        url: 'https://your-production-domain.com/api/v1',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter your JWT token'
        }
      },
      schemas: {
        // Error Response Schema
        ErrorResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              example: 'Error message description'
            },
            error: {
              type: 'string',
              example: 'Detailed error information'
            }
          }
        },
        // Success Response Schema
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        },
        // User Schema
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1
            },
            name: {
              type: 'string',
              example: 'John Doe'
            },
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>'
            },
            phone: {
              type: 'string',
              example: '+233123456789'
            },
            role: {
              type: 'string',
              enum: ['superadmin', 'admin', 'cashier'],
              example: 'admin'
            },
            paymentStatus: {
              type: 'string',
              enum: ['pending', 'paid', 'overdue', 'inactive'],
              example: 'paid'
            },
            lastPaymentDate: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-15T10:30:00Z'
            },
            nextPaymentDue: {
              type: 'string',
              format: 'date-time',
              example: '2024-02-15T10:30:00Z'
            },
            createdBy: {
              type: 'integer',
              example: 1
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T10:30:00Z'
            }
          }
        },
        // Product Schema
        Product: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1
            },
            name: {
              type: 'string',
              example: 'Coca Cola 500ml'
            },
            categoryId: {
              type: 'integer',
              example: 1
            },
            sku: {
              type: 'string',
              example: 'CC500ML001'
            },
            barcode: {
              type: 'string',
              example: '1234567890123'
            },
            price: {
              type: 'string',
              example: '5.50'
            },
            cost: {
              type: 'string',
              example: '3.00'
            },
            stockQuantity: {
              type: 'integer',
              example: 100
            },
            minStockLevel: {
              type: 'integer',
              example: 10
            },
            expiryDate: {
              type: 'string',
              format: 'date-time',
              example: '2024-12-31T23:59:59Z'
            },
            createdBy: {
              type: 'integer',
              example: 1
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T10:30:00Z'
            }
          }
        },
        // Expense Category Schema
        ExpenseCategory: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1
            },
            name: {
              type: 'string',
              example: 'Office Supplies'
            },
            description: {
              type: 'string',
              example: 'Stationery, equipment, and office materials'
            },
            color: {
              type: 'string',
              example: '#3B82F6'
            },
            isDefault: {
              type: 'boolean',
              example: true
            },
            createdBy: {
              type: 'integer',
              example: 1
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T10:30:00Z'
            }
          }
        },
        // Expense Schema
        Expense: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1
            },
            title: {
              type: 'string',
              example: 'Office Rent Payment'
            },
            description: {
              type: 'string',
              example: 'Monthly rent for main office location'
            },
            amount: {
              type: 'string',
              example: '1500.00'
            },
            categoryId: {
              type: 'integer',
              example: 3
            },
            categoryName: {
              type: 'string',
              example: 'Rent'
            },
            categoryColor: {
              type: 'string',
              example: '#8B5CF6'
            },
            expenseDate: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-15T10:30:00Z'
            },
            paymentMethod: {
              type: 'string',
              enum: ['cash', 'card', 'mobile_money', 'bank_transfer', 'cheque'],
              example: 'bank_transfer'
            },
            receiptUrl: {
              type: 'string',
              example: 'https://example.com/receipts/receipt123.pdf'
            },
            vendor: {
              type: 'string',
              example: 'ABC Property Management'
            },
            isRecurring: {
              type: 'boolean',
              example: true
            },
            recurringFrequency: {
              type: 'string',
              enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
              example: 'monthly'
            },
            tags: {
              type: 'string',
              example: '["rent", "office", "monthly"]'
            },
            storeId: {
              type: 'integer',
              example: 1
            },
            createdBy: {
              type: 'integer',
              example: 1
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T10:30:00Z'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T10:30:00Z'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/docs/*.ts'
  ]
};

const specs = swaggerJSDoc(options);

export const setupSwagger = (app: Express): void => {
  // Swagger UI options
  const swaggerUiOptions = {
    explorer: true,
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #3b82f6; }
      .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; }
    `,
    customSiteTitle: 'POS System API Documentation',
    customfavIcon: '/favicon.ico'
  };

  // Serve Swagger UI
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerUiOptions));

  // Serve raw OpenAPI spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  console.log('📚 Swagger documentation available at: https://nexapoapi.up.railway.app/api-docs');
  console.log('📄 OpenAPI spec available at: https://nexapoapi.up.railway.app/api-docs.json');
};

export default specs;
