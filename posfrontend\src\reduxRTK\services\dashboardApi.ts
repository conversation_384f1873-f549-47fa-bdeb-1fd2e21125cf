// services/dashboardApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';
import dayjs from 'dayjs';

// Define dashboard statistics types
export interface StatItem {
  value: number;
  growthRate: number;
}

export interface SuperAdminStats {
  stores: StatItem;
  revenue: StatItem;
  admins: StatItem;
  users: StatItem;
}

export interface AdminStats {
  sales: StatItem;
  revenue: StatItem;
  products: StatItem;
  cashiers: StatItem;
  profit: StatItem;
  expenses: StatItem;
  cogs: StatItem;
  profitMargin: StatItem;
}

export interface CashierStats {
  todaySales: StatItem;
  todayRevenue: StatItem;
  totalProducts: StatItem;
  totalSales: StatItem;
}

// Union type for all possible dashboard stats
export type DashboardStats = SuperAdminStats | AdminStats | CashierStats;

export const dashboardApi = createApi({
  reducerPath: 'dashboardApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Dashboard'] as const,
  endpoints: (builder) => ({
    // Get dashboard statistics
    getDashboardStats: builder.query<ApiResponse<DashboardStats>, void>({
      query: (): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        // Calculate current month's start and end dates
        const startDate = dayjs().startOf('month').format('YYYY-MM-DD');
        const endDate = dayjs().endOf('month').format('YYYY-MM-DD');

        const timestamp = new Date().getTime();
        const randomId = Math.random().toString(36).substring(7);
        const sessionId = Date.now() + Math.random();

        return {
          urlpath: '/dashboard',
          payloaddata: {
            mode: 'stats',
            timestamp,
            randomId,
            sessionId,
            cacheBuster: `${timestamp}_${randomId}`,
            dateRange: {
              startDate,
              endDate,
            },
          },
          token,
        };
      },
      keepUnusedDataFor: 0, // Don't cache data at all
      providesTags: ['Dashboard'],
    }),
  }),
});

// Export hooks for usage in components
export const { useGetDashboardStatsQuery } = dashboardApi;
