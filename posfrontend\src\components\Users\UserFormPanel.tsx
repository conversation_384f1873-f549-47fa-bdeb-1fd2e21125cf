"use client";

import React, { useEffect } from "react";
import { Form, Input, Select, Button } from "antd";
import { User, CreateUserDto, UpdateUserDto, UserRole } from "@/types/user";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useUserCreate } from "@/hooks/users/useUserCreate";
import { useUserUpdate } from "@/hooks/users/useUserUpdate";
import { formatPhoneNumber, formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";
import "./user-panels.css";

const { Option } = Select;

interface UserFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  user?: User | null;
  currentUser?: User | null;
}

const UserFormPanel: React.FC<UserFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  user,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!user;

  // Custom hooks for creating and updating users
  const { createUser, isCreating } = useUserCreate(onSuccess);
  const { updateUser, isUpdating } = useUserUpdate(onSuccess);

  const isLoading = isCreating || isUpdating;

  // Reset form when panel opens/closes or user changes
  useEffect(() => {
    if (isOpen) {
      if (user) {
        // Format the phone number to local format (starting with 0)
        const formattedPhone = formatPhoneNumber(user.phone);

        // In edit mode, pre-fill the form with user data
        form.setFieldsValue({
          name: user.name,
          email: user.email,
          phone: formattedPhone,
          role: user.role,
          paymentStatus: user.paymentStatus,
        });
      } else {
        // In create mode, reset the form
        form.resetFields();
        // Set default role based on current user's role
        if (currentUser?.role === "admin") {
          // Admin can only create cashiers with paid status
          form.setFieldsValue({
            role: "cashier",
            paymentStatus: "paid"
          });
        }
      }
    }
  }, [isOpen, user, form, currentUser]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEditMode && user) {
        // Format the phone number to local format (starting with 0)
        const formattedPhone = formatPhoneNumber(values.phone);

        // Update existing user
        const updateData: UpdateUserDto = {
          name: values.name,
          email: values.email,
          phone: formattedPhone,
        };

        // Only include role if current user can change it
        if (currentUser?.role === "superadmin" ||
            (currentUser?.role === "admin" && values.role === "cashier")) {
          updateData.role = values.role;
        }

        // Only superadmin can update payment status
        if (currentUser?.role === "superadmin") {
          updateData.paymentStatus = values.paymentStatus;
        }

        await updateUser(user.id, updateData);
      } else {
        // Format the phone number to local format (starting with 0)
        const formattedPhone = formatPhoneNumber(values.phone);

        // Create new user
        const createData: CreateUserDto = {
          name: values.name,
          email: values.email,
          password: values.password,
          phone: formattedPhone,
        };

        // Only include role if current user can set it
        if (currentUser?.role === "superadmin") {
          createData.role = values.role;
        } else if (currentUser?.role === "admin") {
          createData.role = "cashier"; // Admins can only create cashiers
          createData.paymentStatus = "paid"; // Automatically set payment status to paid for cashiers created by admin
        }

        await createUser(createData);
      }

      onClose();
    } catch (error: any) {
      console.error("Form submission error:", error);
    }
  };

  // Determine which roles the current user can assign
  const getAvailableRoles = () => {
    if (currentUser?.role === "superadmin") {
      return ["superadmin", "admin", "cashier"];
    } else if (currentUser?.role === "admin") {
      return ["cashier"];
    }
    return [];
  };

  const availableRoles = getAvailableRoles();

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isLoading}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isLoading}
        onClick={handleSubmit}
      >
        {isEditMode ? "Update" : "Create"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={isEditMode ? "Edit User" : "Add New User"}
      width="450px" // This will be overridden on mobile by the SlidingPanel component
      footer={panelFooter}
    >
      {/* Form heading with icon */}
      <div className="mb-6 border-b border-gray-200 pb-4">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          {isEditMode ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Editing User: {user?.name}
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
              Create New User
            </>
          )}
        </h2>
        <p className="text-gray-600 mt-1">
          {isEditMode
            ? "Update user information using the form below"
            : "Fill in the details to create a new user account"}
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{ role: "cashier", paymentStatus: currentUser?.role === "admin" ? "paid" : "pending" }}
        className="text-gray-800 w-full"
        style={{ color: '#333' }}
      >
        {/* Basic Information Section */}
        <div className="mb-4">
          <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
            Basic Information
          </h3>

          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: "Please enter name" }]}
          >
            <Input placeholder="Enter full name" />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: "Please enter email" },
              { type: "email", message: "Please enter a valid email" },
            ]}
          >
            <Input placeholder="Enter email address" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Phone"
            rules={[
              { required: true, message: "Please enter phone number" },
              { min: 5, message: "Phone number must be at least 5 characters" },
              {
                pattern: /^[0-9+\-\s()]*$/,
                message: "Phone number can only contain digits, spaces, and the characters +, -, (, )"
              }
            ]}
            tooltip="Enter phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)"
          >
            <Input placeholder="e.g., ************" />
          </Form.Item>
        </div>

        {/* Authentication Section */}
        {!isEditMode && (
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Authentication
            </h3>
            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: "Please enter password" },
                { min: 6, message: "Password must be at least 6 characters" },
              ]}
            >
              <Input.Password placeholder="Enter password" />
            </Form.Item>
          </div>
        )}

        {/* Access Control Section */}
        {availableRoles.length > 0 && (
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Access Control
            </h3>
            <Form.Item
              name="role"
              label="Role"
              rules={[{ required: true, message: "Please select role" }]}
            >
              <Select placeholder="Select role">
                {availableRoles.map((role) => (
                  <Option key={role} value={role}>
                    {role === "superadmin"
                      ? "Super Admin"
                      : role === "admin"
                        ? "Admin"
                        : "Cashier"}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        )}

        {/* Payment Section - Only visible to superadmin */}
        {isEditMode && currentUser?.role === "superadmin" && (
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Payment Information
            </h3>
            <Form.Item
              name="paymentStatus"
              label="Payment Status"
              rules={[{ required: true, message: "Please select payment status" }]}
            >
              <Select placeholder="Select payment status">
                <Option value="pending">Pending</Option>
                <Option value="paid">Paid</Option>
                <Option value="overdue">Overdue</Option>
                <Option value="inactive">Inactive</Option>
              </Select>
            </Form.Item>
          </div>
        )}
      </Form>
    </SlidingPanel>
  );
};

export default UserFormPanel;
