"use client";

import { useCreatePurchaseMutation, CreatePurchaseDto } from "@/reduxRTK/services/purchaseApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const usePurchaseCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a purchase
  const [createPurchase, { isLoading: isSubmitting }] = useCreatePurchaseMutation();

  const createNewPurchase = async (purchaseData: CreatePurchaseDto) => {
    try {
      console.log("usePurchaseCreate - Starting purchase creation with data:", purchaseData);

      const result = await createPurchase(purchaseData).unwrap() as ApiResponse<any>;
      console.log("usePurchaseCreate - API response:", result);

      if (!result.success) {
        console.error("usePurchaseCreate - API returned error:", result.message);
        throw new Error(result.message || "Failed to create purchase");
      }

      showMessage("success", "Purchase created successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Create purchase error:", error);
      showMessage("error", error.message || "Failed to create purchase");
      throw error;
    }
  };

  return {
    createPurchase: createNewPurchase,
    isSubmitting,
  };
};
