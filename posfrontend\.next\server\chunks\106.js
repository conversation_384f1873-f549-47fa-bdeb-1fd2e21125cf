"use strict";exports.id=106,exports.ids=[106],exports.modules={45546:(e,t,a)=>{a.d(t,{default:()=>f});var s=a(45512),n=a(58009),i=a(92273),r=a(79334),l=a(24715);let o=({children:e})=>{let{user:t,accessToken:a}=(0,i.d4)(e=>e.auth),o=(0,r.useRouter)(),c=(0,r.usePathname)(),[d,u]=(0,n.useState)(!1),[h,m]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{u(!0),console.log("AuthGuard mounted on client",{isAuthenticated:!!t&&!!a,userRole:t?.role,pathname:c})},[a,c,t]),(0,n.useEffect)(()=>{!d||t&&a||h||(console.log("AuthGuard - Redirecting to login",{pathname:c}),m(!0),sessionStorage.setItem("redirectUrl",c),setTimeout(()=>{o.push("/")},100))},[t,a,o,c,d,h]),d&&t&&a)?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)(l.A,{fullScreen:!0})};var c=a(49198);let d=({children:e,allowedRoles:t,fallbackPath:a="/dashboard"})=>{let{user:o}=(0,i.d4)(e=>e.auth),d=(0,r.useRouter)();return((0,n.useEffect)(()=>{o&&!t.includes(o.role)&&d.push(a)},[o,t,d,a]),o)?t.includes(o.role)?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center",children:(0,s.jsx)(c.A,{message:"Access Denied",description:`You don't have permission to access this page. This area requires ${t.join(" or ")} role.`,type:"error",showIcon:!0})}):(0,s.jsx)(l.A,{fullScreen:!0})};var u=a(25510),h=a(16589),m=a.n(h);let p=({children:e})=>{let{user:t}=(0,i.d4)(e=>e.auth),a=(0,r.useRouter)(),o=(0,r.usePathname)(),{needsPayment:c,status:d,isActive:h,daysRemaining:p}=(0,u._)();if((0,n.useRef)(t?.paymentStatus),(0,i.wA)(),console.log("\uD83D\uDEE1️ PaymentGuard COMPONENT RENDERED - This confirms PaymentGuard is being called"),console.log("\uD83D\uDEE1️ PaymentGuard - Payment Status Check:",{userRole:t?.role,userPaymentStatus:t?.paymentStatus,needsPayment:c,status:d,isActive:h,daysRemaining:p,pathname:o,lastPaymentDate:t?.lastPaymentDate,nextPaymentDue:t?.nextPaymentDue,createdAt:t?.createdAt}),t?.createdAt&&t?.paymentStatus==="paid"&&!t?.lastPaymentDate){let e=m()().diff(m()(t.createdAt),"day");console.log("\uD83C\uDF81 PaymentGuard - FREE TRIAL USER DETECTED:",{email:t.email,daysSinceCreation:e,isInFreeTrial:e<=90,trialDaysRemaining:Math.max(0,90-e),paymentStatus:t.paymentStatus,nextPaymentDue:t.nextPaymentDue})}if(t?.paymentStatus==="pending"&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER DETECTED! Should redirect to payment page"),t?.paymentStatus,(0,n.useEffect)(()=>{t&&"superadmin"!==t.role&&"paid"!==t.paymentStatus&&a.replace("/payment")},[t,a]),(0,n.useEffect)(()=>{if(!t||"superadmin"===t.role)return;let e=o.includes("/profile")||o.includes("/dashboard/profile"),s=!0===window.__PROFILE_UPDATE_IN_PROGRESS,n=window.__LAST_PROFILE_UPDATE_PATH||"";if(e||s){console.log("PaymentGuard - Skipping payment check:",{isProfilePage:e,isProfileUpdateInProgress:s,pathname:o,lastProfileUpdatePath:n});return}let i="/payment"===o;console.log("PaymentGuard - Payment check status:",{pathname:o,isProfilePage:e,isExemptPath:i,needsPayment:c});let r=c&&!i&&"paid"!==t.paymentStatus||!i&&"paid"!==t.paymentStatus;console.log("\uD83D\uDD0D PaymentGuard - Redirect decision breakdown:",{needsPayment:c,isExemptPath:i,userPaymentStatus:t.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==t.paymentStatus,shouldRedirectToPayment:r,pathname:o}),"pending"===t.paymentStatus&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER REDIRECT CHECK:",{needsPayment:c,isExemptPath:i,userPaymentStatus:t.paymentStatus,shouldRedirectToPayment:r,"Will redirect?":r?"YES":"NO"}),r?(console.log("\uD83D\uDEA8 PaymentGuard - Redirecting to payment page from:",o),a.push("/payment")):"paid"===t.paymentStatus&&c?console.log("⚠️ PaymentGuard - User has paid status but needsPayment is true. This might indicate a cache issue."):"pending"!==t.paymentStatus||r||console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER NOT REDIRECTED! This is the bug!")},[t,c,a,o]),(0,n.useEffect)(()=>{t&&"superadmin"!==t.role&&t.paymentStatus},[t,a]),!t)return(0,s.jsx)(l.A,{tip:"Checking payment status...",fullScreen:!0});if("superadmin"===t.role)return(0,s.jsx)(s.Fragment,{children:e});let f=o.includes("/profile")||o.includes("/dashboard/profile"),x=!0===window.__PROFILE_UPDATE_IN_PROGRESS,g=window.__LAST_PROFILE_UPDATE_PATH||"";if(f||x)return console.log("PaymentGuard (render) - Skipping payment check:",{isProfilePage:f,isProfileUpdateInProgress:x,pathname:o,lastProfileUpdatePath:g}),(0,s.jsx)(s.Fragment,{children:e});let y="/payment"===o,v=c&&!y&&"paid"!==t.paymentStatus||!y&&"paid"!==t.paymentStatus;return(console.log("\uD83C\uDFA8 PaymentGuard (render) - Final render decision:",{needsPayment:c,isExemptPath:y,userPaymentStatus:t.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==t.paymentStatus,shouldShowPaymentLoading:v,pathname:o}),v)?(0,s.jsx)(l.A,{tip:"Checking payment status...",fullScreen:!0}):(0,s.jsx)(s.Fragment,{children:e})},f=({children:e,allowedRoles:t=["superadmin","admin","cashier"],checkPayment:a=!0,fallbackPath:n="/dashboard"})=>(0,s.jsx)(o,{children:a?(0,s.jsx)(p,{children:(0,s.jsx)(d,{allowedRoles:t,fallbackPath:n,children:e})}):(0,s.jsx)(d,{allowedRoles:t,fallbackPath:n,children:e})})},94682:(e,t,a)=>{a.d(t,{Y:()=>F});var s=a(45512),n=a(45103),i=a(28531),r=a.n(i),l=a(79334),o=a(60746);function c(e){return(0,s.jsxs)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{d:"M3.5625 6C3.5625 5.58579 3.89829 5.25 4.3125 5.25H20.3125C20.7267 5.25 21.0625 5.58579 21.0625 6C21.0625 6.41421 20.7267 6.75 20.3125 6.75L4.3125 6.75C3.89829 6.75 3.5625 6.41422 3.5625 6Z"}),(0,s.jsx)("path",{d:"M3.5625 18C3.5625 17.5858 3.89829 17.25 4.3125 17.25L20.3125 17.25C20.7267 17.25 21.0625 17.5858 21.0625 18C21.0625 18.4142 20.7267 18.75 20.3125 18.75L4.3125 18.75C3.89829 18.75 3.5625 18.4142 3.5625 18Z"}),(0,s.jsx)("path",{d:"M4.3125 11.25C3.89829 11.25 3.5625 11.5858 3.5625 12C3.5625 12.4142 3.89829 12.75 4.3125 12.75L20.3125 12.75C20.7267 12.75 21.0625 12.4142 21.0625 12C21.0625 11.5858 20.7267 11.25 20.3125 11.25L4.3125 11.25Z"})]})}function d(e){return(0,s.jsx)("svg",{width:22,height:22,viewBox:"0 0 22 22",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.551 7.728a.687.687 0 01.895 0l6.417 5.5a.687.687 0 11-.895 1.044l-5.97-5.117-5.969 5.117a.687.687 0 01-.894-1.044l6.416-5.5z"})})}var u=a(58009),h=a(44195);let m=(0,u.createContext)(null);function p(){let e=(0,u.useContext)(m);if(!e)throw Error("useDropdownContext must be used within a Dropdown");return e}function f({children:e,isOpen:t,setIsOpen:a}){function n(){a(!1)}return(0,u.useRef)(null),(0,s.jsx)(m.Provider,{value:{isOpen:t,handleOpen:function(){a(!0)},handleClose:n},children:(0,s.jsx)("div",{className:"relative",onKeyDown:e=>{"Escape"===e.key&&n()},children:e})})}function x({children:e,align:t="center",className:a}){let{isOpen:n,handleClose:i}=p(),r=(()=>{n&&i()},(0,u.useRef)(null));return n?(0,s.jsx)("div",{ref:r,role:"menu","aria-orientation":"vertical",className:(0,h.cn)("fade-in-0 zoom-in-95 pointer-events-auto absolute z-99 mt-2 min-w-[8rem] origin-top-right rounded-lg",{"animate-in right-0":"end"===t,"left-0":"start"===t,"left-1/2 -translate-x-1/2":"center"===t},a),children:e}):null}function g({children:e,className:t}){let{handleOpen:a,isOpen:n}=p();return(0,s.jsx)("button",{className:t,onClick:a,"aria-expanded":n,"aria-haspopup":"menu","data-state":n?"open":"closed",children:e})}function y(e){return(0,s.jsx)("svg",{width:20,height:20,viewBox:"0 0 18 18",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 .938a3.562 3.562 0 100 7.124A3.562 3.562 0 009 .938zM6.562 4.5a2.437 2.437 0 114.875 0 2.437 2.437 0 01-4.875 0zM9 9.188c-1.735 0-3.334.394-4.518 1.06-1.167.657-2.045 1.652-2.045 2.877v.076c0 .872-.001 1.965.958 2.747.472.384 1.132.657 2.025.838.894.181 2.06.276 3.58.276s2.685-.095 3.58-.276c.893-.18 1.553-.454 2.025-.838.96-.782.958-1.875.957-2.747v-.076c0-1.226-.877-2.22-2.044-2.877-1.184-.666-2.783-1.06-4.518-1.06zm-5.438 3.937c0-.639.467-1.331 1.471-1.896.987-.555 2.388-.916 3.967-.916 1.579 0 2.98.36 3.967.916 1.004.565 1.47 1.258 1.47 1.896 0 .98-.03 1.533-.542 1.95-.278.227-.743.448-1.538.609-.793.16-1.876.254-3.357.254-1.48 0-2.564-.094-3.357-.255-.795-.16-1.26-.381-1.538-.608-.512-.417-.543-.97-.543-1.95z"})})}function v(e){return(0,s.jsxs)("svg",{width:20,height:20,viewBox:"0 0 18 18",fill:"currentColor",...e,children:[(0,s.jsxs)("g",{clipPath:"url(#clip0_7095_11691)",children:[(0,s.jsx)("path",{d:"M11.209.938c-1.026 0-1.852 0-2.503.087-.675.09-1.243.285-1.695.736-.393.394-.592.878-.697 1.446-.101.553-.12 1.229-.125 2.04a.562.562 0 101.125.006c.005-.82.026-1.401.107-1.842.078-.426.203-.672.386-.854.207-.208.499-.343 1.05-.417.566-.076 1.317-.078 2.393-.078H12c1.077 0 1.828.002 2.394.078.55.074.842.21 1.05.417.207.207.342.499.416 1.05.077.566.078 1.316.078 2.393v6c0 1.077-.002 1.827-.078 2.394-.074.55-.209.842-.417 1.05-.207.207-.499.342-1.049.416-.566.076-1.317.078-2.394.078h-.75c-1.076 0-1.827-.002-2.394-.078-.55-.074-.842-.21-1.05-.417-.182-.182-.307-.428-.385-.854-.081-.44-.102-1.022-.107-1.842a.563.563 0 00-1.125.006c.004.811.024 1.487.125 2.04.105.568.304 1.052.697 1.446.452.451 1.02.645 1.695.736.65.087 1.477.087 2.503.087h.832c1.026 0 1.853 0 2.503-.087.675-.09 1.243-.285 1.695-.736.451-.452.645-1.02.736-1.695.088-.65.088-1.477.088-2.503V5.96c0-1.026 0-1.853-.088-2.503-.09-.675-.285-1.243-.736-1.695-.452-.451-1.02-.645-1.695-.736-.65-.088-1.477-.088-2.503-.087h-.832z"}),(0,s.jsx)("path",{d:"M11.25 8.438a.562.562 0 110 1.124H3.02l1.471 1.26a.563.563 0 01-.732.855l-2.625-2.25a.562.562 0 010-.854l2.625-2.25a.562.562 0 11.732.854l-1.47 1.26h8.229z"})]}),(0,s.jsx)("defs",{children:(0,s.jsx)("clipPath",{id:"clip0_7095_11691",children:(0,s.jsx)("rect",{width:18,height:18,rx:5})})})]})}var w=a(92273),b=a(97245),j=a(42211);let S=()=>{let e=(0,w.wA)(),[t,a]=(0,u.useState)({loading:!1,error:""}),[s]=(0,b.Qg)();return{logout:async t=>{a({loading:!0,error:""});try{let n=await s({email:t}).unwrap();n.success?(e((0,j.lM)()),window.location.href="/",a({loading:!1,error:""})):a({loading:!1,error:n.message})}catch(e){a({loading:!1,error:e?.data?.message||e.message||"Logout failed"})}},...t}},N=()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"h-6 w-6 text-gray-500",children:[(0,s.jsx)("circle",{cx:"12",cy:"8",r:"4"}),(0,s.jsx)("path",{d:"M4 20c0-4 4-4 8-4s8 4 8 4"})]});function E(){let[e,t]=(0,u.useState)(!1),a=(0,w.d4)(e=>e.auth.user),n=(0,l.useRouter)(),{logout:i}=S(),o=async()=>{a&&a.email&&(await i(a.email),n.replace("/"))};return(0,s.jsxs)(f,{isOpen:e,setIsOpen:t,children:[(0,s.jsxs)(g,{className:"rounded align-middle outline-none ring-primary ring-offset-2 focus-visible:ring-1",children:[(0,s.jsx)("span",{className:"sr-only",children:"My Account"}),(0,s.jsxs)("figure",{className:"flex items-center gap-3",children:[(0,s.jsx)(N,{}),(0,s.jsxs)("figcaption",{className:"flex items-center gap-1 font-medium text-dark max-[1024px]:sr-only",children:[(0,s.jsx)("span",{children:a?.name.split(" ")[0]||"John Smith"}),(0,s.jsx)(d,{"aria-hidden":!0,className:(0,h.cn)("rotate-180 transition-transform",e&&"rotate-0"),strokeWidth:1.5})]})]})]}),(0,s.jsxs)(x,{className:"border border-stroke bg-white shadow-md min-[230px]:min-w-[17.5rem]",align:"end",children:[(0,s.jsx)("h2",{className:"sr-only",children:"User information"}),(0,s.jsxs)("figure",{className:"flex items-center gap-2.5 px-5 py-3.5",children:[(0,s.jsx)(N,{}),(0,s.jsxs)("figcaption",{className:"space-y-1 text-base font-medium",children:[(0,s.jsx)("div",{className:"mb-2 leading-none text-dark",children:a?.name||"user"}),(0,s.jsx)("div",{className:"leading-none text-gray-6",children:a?.email||"<EMAIL>"})]})]}),(0,s.jsx)("hr",{className:"border-[#E8E8E8]"}),(0,s.jsx)("div",{className:"p-2 text-base text-[#4B5563] [&>*]:cursor-pointer",children:(0,s.jsxs)(r(),{href:"/dashboard/profile",onClick:e=>{e.preventDefault(),t(!1),n.push("/dashboard/profile")},className:"flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark",children:[(0,s.jsx)(y,{}),(0,s.jsx)("span",{className:"mr-auto text-base font-medium",children:"View profile"})]})}),(0,s.jsx)("hr",{className:"border-[#E8E8E8]"}),(0,s.jsx)("div",{className:"p-2 text-base text-[#4B5563]",children:(0,s.jsxs)("button",{className:"flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark",onClick:o,children:[(0,s.jsx)(v,{}),(0,s.jsx)("span",{className:"text-base font-medium",children:"Log out"})]})})]})]})}var A=a(61667),z=a(70001),P=a(26222),C=a(3117),R=a(88206),M=a(44599),k=a(62264),D=a(43231),I=a(75238),O=a(42608),T=a(30807),_=a(14396),L=a(37246);let{Text:V}=A.A,B=({className:e=""})=>{let[t,a]=(0,u.useState)(!0),[n,i]=(0,u.useState)(0),[r,l]=(0,u.useState)("idle"),[o,c]=(0,u.useState)(!1),[d,h]=(0,u.useState)(0),m=(0,u.useCallback)(async()=>{if(t&&"syncing"!==r)try{l("syncing"),await _.zy.forceSyncNow(),l("idle");let e=await L.V.getOfflineSales("pending"),t=await L.V.getOfflineSales("failed");i(e.length+t.length)}catch(e){console.error("Auto-sync failed:",e),l("error")}},[t,r]);(0,u.useEffect)(()=>{let e=()=>{a(!0),m()},t=()=>{a(!1)};return a(navigator.onLine),window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[m]),(0,u.useEffect)(()=>{let e=async()=>{try{let e=await L.V.getOfflineSales("pending"),t=await L.V.getOfflineSales("failed");i(e.length+t.length)}catch(e){console.error("Failed to get pending sales:",e)}};e();let t=setInterval(e,3e4);return()=>clearInterval(t)},[]);let p=async()=>{if(t){c(!0),h(0);try{l("syncing");let e=setInterval(()=>{h(e=>Math.min(e+10,90))},200);await _.zy.forceSyncNow(),clearInterval(e),h(100),l("idle");let t=await L.V.getOfflineSales("pending"),a=await L.V.getOfflineSales("failed");i(t.length+a.length),setTimeout(()=>{c(!1),h(0)},1e3)}catch(e){console.error("Manual sync failed:",e),l("error"),c(!1),h(0)}}},f=()=>t?"syncing"===r?"Syncing...":"error"===r?"Sync Error":n>0?`${n} Pending`:"Online & Synced":"Offline Mode";return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:`flex items-center space-x-2 ${e}`,children:(0,s.jsx)(z.A,{title:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Status:"})," ",f()]}),n>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Pending Sales:"})," ",n]}),!t&&(0,s.jsx)("div",{className:"text-yellow-300",children:"Sales will sync when connection returns"}),t&&n>0&&(0,s.jsx)("div",{className:"text-blue-300",children:"Click to sync now"})]}),children:(0,s.jsx)(P.A,{count:n>0?n:0,size:"small",offset:[0,0],children:(0,s.jsx)(C.Ay,{type:"text",icon:t?"syncing"===r?(0,s.jsx)(D.A,{spin:!0}):"error"===r?(0,s.jsx)(I.A,{}):(0,s.jsx)(O.A,{}):(0,s.jsx)(k.A,{}),onClick:t&&n>0?p:void 0,className:"flex items-center",style:{color:t?"syncing"===r?"#1890ff":"error"===r||n>0?"#faad14":"#52c41a":"#ff4d4f",cursor:t&&n>0?"pointer":"default"},disabled:"syncing"===r,children:(0,s.jsx)("span",{className:"ml-1 text-sm font-medium",children:f()})})})})}),(0,s.jsx)(R.A,{title:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(T.A,{className:"mr-2 text-blue-500"}),"Syncing Offline Sales"]}),open:o,footer:null,closable:!1,centered:!0,width:400,children:(0,s.jsxs)("div",{className:"py-4",children:[(0,s.jsx)(M.A,{percent:d,status:100===d?"success":"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(V,{type:"secondary",children:d<100?`Syncing ${n} offline sales...`:"Sync completed successfully!"})})]})})]})};function F(){let e=(0,l.useRouter)(),{toggleSidebar:t,isMobile:a,setIsOpen:i}=(0,o.V)();return(0,s.jsxs)("header",{className:"w-full flex items-center justify-between border-b border-stroke bg-white px-4 py-5 shadow-1 md:px-5 2xl:px-10",children:[a&&(0,s.jsxs)("button",{onClick:e=>{e.stopPropagation(),console.log("Toggle sidebar clicked from header"),i(!0)},className:"rounded-lg border px-1.5 py-1","aria-label":"Open sidebar menu",children:[(0,s.jsx)(c,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]}),a&&(0,s.jsx)(r(),{href:"/dashboard",className:"ml-2 mobile-logo max-[430px]:hidden min-[375px]:ml-4",onClick:t=>{window.location.pathname.includes("/dashboard/users")||(t.preventDefault(),e.push("/dashboard"))},children:(0,s.jsx)(n.default,{src:"/images/logo/logo-small.png",width:36,height:36,alt:"Company logo",role:"presentation",className:"object-contain"})}),(0,s.jsxs)("div",{className:"max-xl:hidden",children:[(0,s.jsx)("h4",{className:"mb-0.5 text-heading-5 font-bold text-dark",children:"Dashboard"}),(0,s.jsx)("p",{className:"font-medium",children:"POS System | Inventory Management"})]}),(0,s.jsxs)("div",{className:"flex flex-1 items-center justify-end gap-2 min-[375px]:gap-4",children:[(0,s.jsx)(B,{className:"shrink-0"}),(0,s.jsx)("div",{className:"shrink-0",children:(0,s.jsx)(E,{})})]})]})}},59262:(e,t,a)=>{a.d(t,{R:()=>f});var s=a(45512),n=a(15928),i=a(44195),r=a(28531),l=a.n(r),o=a(79334),c=a(58009),d=a(21441),u=a(35786),h=a(69435),m=a(60746),p=a(92273);function f(){let e=(0,o.usePathname)(),t=(0,o.useRouter)(),{setIsOpen:a,isOpen:r,toggleSidebar:f,isMobile:x}=(0,m.V)(),[g,y]=(0,c.useState)([]),{user:v}=(0,p.d4)(e=>e.auth),w=v?(0,d.D)(v.role):d.R,[b,j]=(0,c.useState)(e),S=e=>{y(t=>t.includes(e)?[]:[e])};return x&&r?(console.log("Mobile sidebar is open",{isMobile:x,isOpen:r}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 transition-opacity duration-300",onClick:()=>{console.log("Overlay clicked, closing sidebar with delay"),setTimeout(()=>{a(!1)},50)},"aria-hidden":"true"}),(0,s.jsx)("aside",{className:"fixed bottom-0 left-0 top-0 z-50 h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out","aria-label":"Mobile navigation",children:(0,s.jsxs)("div",{className:"flex h-full flex-col py-10 pl-[25px] pr-[7px]",children:[(0,s.jsxs)("div",{className:"relative pr-4.5",children:[(0,s.jsx)(l(),{href:"/dashboard",onClick:e=>{if(window.location.pathname.includes("/dashboard/users")){f();return}e.preventDefault(),f(),t.push("/dashboard")},className:"px-0 py-2.5 min-[850px]:py-0",children:(0,s.jsx)(n.g,{})}),(0,s.jsxs)("button",{onClick:e=>{e.stopPropagation(),console.log("Mobile back button clicked, closing sidebar with delay"),setTimeout(()=>{a(!1)},50)},className:"mobile-back-button absolute right-0 top-1/2 -translate-y-1/2 text-right","aria-label":"Close sidebar",children:[(0,s.jsx)("span",{className:"sr-only",children:"Close Menu"}),(0,s.jsx)(u.A6,{className:"ml-auto size-7"})]})]}),(0,s.jsx)("div",{className:"custom-scrollbar mt-6 flex-1 overflow-y-auto pr-3",children:w.map(t=>(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"mb-5 text-sm font-medium text-gray-600",children:t.label}),(0,s.jsx)("nav",{role:"navigation","aria-label":t.label,children:(0,s.jsx)("ul",{className:"space-y-2",children:t.items.map(t=>(0,s.jsx)("li",{children:t.items.length?(0,s.jsxs)("div",{children:[(0,s.jsxs)(h.D,{isActive:t.items.some(({url:t})=>t===e),onClick:()=>S(t.title),children:[(0,s.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,s.jsx)("span",{children:t.title}),(0,s.jsx)(u.rX,{className:(0,i.cn)("ml-auto rotate-180 transition-transform duration-200",g.includes(t.title)&&"rotate-0"),"aria-hidden":"true"})]}),g.includes(t.title)&&(0,s.jsx)("ul",{className:"ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2",role:"menu",children:t.items.map(t=>(0,s.jsx)("li",{role:"none",children:(0,s.jsx)(h.D,{as:"link",href:t.url,isActive:e===t.url,children:(0,s.jsx)("span",{children:t.title})})},t.title))})]}):(()=>{let a="url"in t&&t.url?t.url:"/"+t.title.toLowerCase().split(" ").join("-");return(0,s.jsxs)(h.D,{className:"flex items-center gap-3 py-3",as:"link",href:a,isActive:e===a,children:[(0,s.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,s.jsx)("span",{children:t.title})]})})()},t.title))})})]},t.label))})]})})]})):null}},21441:(e,t,a)=>{a.d(t,{D:()=>h,R:()=>m});var s=a(35786);let n=[{title:"Dashboard",url:"/dashboard",icon:s.fA,items:[]},{title:"Profile",url:"/dashboard/profile",icon:s.KJ,items:[]}];s.Sl;let i={title:"Inventory Menu",icon:s.Ez,items:[{title:"Categories",url:"/dashboard/categories"},{title:"Products",url:"/dashboard/products"},{title:"Suppliers",url:"/dashboard/suppliers"},{title:"Purchases",url:"/dashboard/purchases"}]},r={title:"Stores",url:"/dashboard/stores",icon:s.U1,items:[]},l={title:"Sales",url:"/dashboard/sales",icon:s.aJ,items:[]},o={title:"Receipts",url:"/dashboard/receipts",icon:s.ww,items:[]},c={title:"Expenses Menu",icon:s.sy,items:[{title:"Expense Categories",url:"/dashboard/expense-categories"},{title:"Expenses",url:"/dashboard/expenses"}]},d={title:"Users",url:"/dashboard/users",icon:s.KJ,items:[]},u={title:"Reports",icon:s.Bi,items:[{title:"Sales Reports",url:"/dashboard/reports/sales"},{title:"Inventory Reports",url:"/dashboard/reports/inventory"}]};s.Vv,s.Vv;let h=e=>{let t=[...n];return"superadmin"===e?t=[...t,d,r]:"admin"===e?t=[...t,i,l,o,c,u,d]:"cashier"===e&&(t=[...t,l,o]),[{label:"MAIN MENU",items:t}]},m=[{label:"MAIN MENU",items:[...n,i,l,o,c,d]}]},35786:(e,t,a)=>{a.d(t,{A6:()=>c,Bi:()=>f,Ez:()=>d,KJ:()=>l,Sl:()=>o,U1:()=>h,Vv:()=>r,aJ:()=>u,fA:()=>i,rX:()=>n,sy:()=>p,ww:()=>m});var s=a(45512);function n(e){return(0,s.jsx)("svg",{width:16,height:8,viewBox:"0 0 16 8",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.553.728a.687.687 0 01.895 0l6.416 5.5a.688.688 0 01-.895 1.044L8 2.155 2.03 7.272a.688.688 0 11-.894-1.044l6.417-5.5z"})})}function i(e){return(0,s.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{d:"M9 17.25a.75.75 0 000 1.5h6a.75.75 0 000-1.5H9z"}),(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1.25c-.725 0-1.387.2-2.11.537-.702.327-1.512.81-2.528 1.415l-1.456.867c-1.119.667-2.01 1.198-2.686 1.706C2.523 6.3 2 6.84 1.66 7.551c-.342.711-.434 1.456-.405 2.325.029.841.176 1.864.36 3.146l.293 2.032c.237 1.65.426 2.959.707 3.978.29 1.05.702 1.885 1.445 2.524.742.64 1.63.925 2.716 1.062 1.056.132 2.387.132 4.066.132h2.316c1.68 0 3.01 0 4.066-.132 1.086-.137 1.974-.422 2.716-1.061.743-.64 1.155-1.474 1.445-2.525.281-1.02.47-2.328.707-3.978l.292-2.032c.185-1.282.332-2.305.36-3.146.03-.87-.062-1.614-.403-2.325C22 6.84 21.477 6.3 20.78 5.775c-.675-.508-1.567-1.039-2.686-1.706l-1.456-.867c-1.016-.605-1.826-1.088-2.527-1.415-.724-.338-1.386-.537-2.111-.537zM8.096 4.511c1.057-.63 1.803-1.073 2.428-1.365.609-.284 1.047-.396 1.476-.396.43 0 .867.112 1.476.396.625.292 1.37.735 2.428 1.365l1.385.825c1.165.694 1.986 1.184 2.59 1.638.587.443.91.809 1.11 1.225.199.416.282.894.257 1.626-.026.75-.16 1.691-.352 3.026l-.28 1.937c-.246 1.714-.422 2.928-.675 3.845-.247.896-.545 1.415-.977 1.787-.433.373-.994.593-1.925.71-.951.119-2.188.12-3.93.12h-2.213c-1.743 0-2.98-.001-3.931-.12-.93-.117-1.492-.337-1.925-.71-.432-.372-.73-.891-.977-1.787-.253-.917-.43-2.131-.676-3.845l-.279-1.937c-.192-1.335-.326-2.277-.352-3.026-.025-.732.058-1.21.258-1.626.2-.416.521-.782 1.11-1.225.603-.454 1.424-.944 2.589-1.638l1.385-.825z"})]})}function r(e){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:[(0,s.jsx)("path",{d:"M17 14a1 1 0 100-2 1 1 0 000 2zM17 18a1 1 0 100-2 1 1 0 000 2zM13 13a1 1 0 11-2 0 1 1 0 012 0zM13 17a1 1 0 11-2 0 1 1 0 012 0zM7 14a1 1 0 100-2 1 1 0 000 2zM7 18a1 1 0 100-2 1 1 0 000 2z",fill:"currentColor"}),(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 1.75a.75.75 0 01.75.75v.763c.662-.013 1.391-.013 2.193-.013h4.113c.803 0 1.532 0 2.194.013V2.5a.75.75 0 011.5 0v.827c.26.02.506.045.739.076 1.172.158 2.121.49 2.87 1.238.748.749 1.08 1.698 1.238 2.87.153 1.14.153 2.595.153 4.433v2.112c0 1.838 0 3.294-.153 4.433-.158 1.172-.49 2.121-1.238 2.87-.749.748-1.698 1.08-2.87 1.238-1.14.153-2.595.153-4.433.153H9.944c-1.838 0-3.294 0-4.433-.153-1.172-.158-2.121-.49-2.87-1.238-.748-.749-1.08-1.698-1.238-2.87-.153-1.14-.153-2.595-.153-4.433v-2.112c0-1.838 0-3.294.153-4.433.158-1.172.49-2.121 1.238-2.87.749-.748 1.698-1.08 2.87-1.238.233-.031.48-.056.739-.076V2.5A.75.75 0 017 1.75zM5.71 4.89c-1.005.135-1.585.389-2.008.812-.423.423-.677 1.003-.812 2.009-.023.17-.042.35-.058.539h18.336c-.016-.19-.035-.369-.058-.54-.135-1.005-.389-1.585-.812-2.008-.423-.423-1.003-.677-2.009-.812-1.027-.138-2.382-.14-4.289-.14h-4c-1.907 0-3.261.002-4.29.14zM2.75 12c0-.854 0-1.597.013-2.25h18.474c.013.653.013 1.396.013 2.25v2c0 1.907-.002 3.262-.14 4.29-.135 1.005-.389 1.585-.812 2.008-.423.423-1.003.677-2.009.812-1.027.138-2.382.14-4.289.14h-4c-1.907 0-3.261-.002-4.29-.14-1.005-.135-1.585-.389-2.008-.812-.423-.423-.677-1.003-.812-2.009-.138-1.027-.14-2.382-.14-4.289v-2z",fill:"currentColor"})]})}function l(e){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1.25a4.75 4.75 0 100 9.5 4.75 4.75 0 000-9.5zM8.75 6a3.25 3.25 0 116.5 0 3.25 3.25 0 01-6.5 0zM12 12.25c-2.313 0-4.445.526-6.024 1.414C4.42 14.54 3.25 15.866 3.25 17.5v.102c-.001 1.162-.002 2.62 1.277 3.662.629.512 1.51.877 2.7 1.117 1.192.242 2.747.369 4.773.369s3.58-.127 4.774-.369c1.19-.24 2.07-.605 2.7-1.117 1.279-1.042 1.277-2.5 1.276-3.662V17.5c0-1.634-1.17-2.96-2.725-3.836-1.58-.888-3.711-1.414-6.025-1.414zM4.75 17.5c0-.851.622-1.775 1.961-2.528 1.316-.74 3.184-1.222 5.29-1.222 2.104 0 3.972.482 5.288 1.222 1.34.753 1.961 1.677 1.961 2.528 0 1.308-.04 2.044-.724 2.6-.37.302-.99.597-2.05.811-1.057.214-2.502.339-4.476.339-1.974 0-3.42-.125-4.476-.339-1.06-.214-1.68-.509-2.05-.81-.684-.557-.724-1.293-.724-2.601z",fill:"currentColor"})})}function o(e){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 7A.75.75 0 013 6.25h10a.75.75 0 010 1.5H3A.75.75 0 012.25 7zm14.25-.75a.75.75 0 01.684.442l4.5 10a.75.75 0 11-1.368.616l-1.437-3.194H14.12l-1.437 3.194a.75.75 0 11-1.368-.616l4.5-10a.75.75 0 01.684-.442zm-1.704 6.364h3.408L16.5 8.828l-1.704 3.786zM2.25 12a.75.75 0 01.75-.75h7a.75.75 0 010 1.5H3a.75.75 0 01-.75-.75zm0 5a.75.75 0 01.75-.75h5a.75.75 0 010 1.5H3a.75.75 0 01-.75-.75z",fill:"currentColor"})})}function c(e){return(0,s.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.89775 4.10225C8.11742 4.32192 8.11742 4.67808 7.89775 4.89775L4.358 8.4375H15C15.3107 8.4375 15.5625 8.68934 15.5625 9C15.5625 9.31066 15.3107 9.5625 15 9.5625H4.358L7.89775 13.1023C8.11742 13.3219 8.11742 13.6781 7.89775 13.8977C7.67808 14.1174 7.32192 14.1174 7.10225 13.8977L2.60225 9.39775C2.38258 9.17808 2.38258 8.82192 2.60225 8.60225L7.10225 4.10225C7.32192 3.88258 7.67808 3.88258 7.89775 4.10225Z",fill:""})})}function d(e){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.25 2.5a2.75 2.75 0 00-2.75 2.75v13.5a2.75 2.75 0 002.75 2.75h13.5a2.75 2.75 0 002.75-2.75V5.25a2.75 2.75 0 00-2.75-2.75H5.25zM3.5 5.25c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v13.5a1.75 1.75 0 01-1.75 1.75H5.25a1.75 1.75 0 01-1.75-1.75V5.25zm4.25 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75zm0 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75zm0 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75z"})})}function u(e){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 3a.75.75 0 01.75-.75h1.386c.76 0 1.413.537 1.563 1.282l.786 3.918h13.516a1.5 1.5 0 011.456 1.873l-1.5 6A1.5 1.5 0 0118.75 16.5h-10.5a1.5 1.5 0 01-1.456-1.127l-2.25-11.25a.25.25 0 00-.245-.214H3a.75.75 0 01-.75-.75zM4.5 8.95l1.944 9.72a.25.25 0 00.243.188h10.5a.25.25 0 00.243-.188l1.5-6a.25.25 0 00-.243-.312H4.5v-3.408z"}),(0,s.jsx)("path",{d:"M8.25 19.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM15.75 19.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"})]})}function h(e){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V18a3 3 0 01-3 3H5.25a3 3 0 01-3-3V5.25zm3-1.5a1.5 1.5 0 00-1.5 1.5v1.5h16.5v-1.5a1.5 1.5 0 00-1.5-1.5H5.25zM3.75 9v9a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5V9H3.75z"}),(0,s.jsx)("path",{d:"M6.75 12a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zM6.75 15a.75.75 0 01.75-.75h5.25a.75.75 0 010 1.5H7.5a.75.75 0 01-.75-.75z"})]})}function m(e){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.875 1.5a.75.75 0 00-.75.75v4.5c0 .414.336.75.75.75h8.25a.75.75 0 00.75-.75v-4.5a.75.75 0 00-.75-.75h-8.25zM9 3h6v2.25H9V3z"}),(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 7.5a.75.75 0 00-.75.75v7.5a.75.75 0 00.75.75h.75v3.75c0 .414.336.75.75.75h13.5a.75.75 0 00.75-.75V16.5h.75a.75.75 0 00.75-.75v-7.5a.75.75 0 00-.75-.75h-16.5zm15 9h-13.5v3h13.5v-3zm-15-7.5v6h16.5v-6h-16.5z"}),(0,s.jsx)("path",{d:"M6.75 12a.75.75 0 000 1.5h.75a.75.75 0 000-1.5h-.75z"})]})}function p(e){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v.816a3.836 3.836 0 00-1.72.756 2.25 2.25 0 000 3.18c.302.267.673.484 1.103.66.431.177.915.329 1.394.492.577.196 1.163.415 1.555.709a.75.75 0 010 1.06c-.392.294-.978.513-1.555.709-.479.163-.963.315-1.394.492-.43.176-.8.393-1.103.66a2.25 2.25 0 000 3.18 3.836 3.836 0 001.72.756V18a.75.75 0 001.5 0v-.816a3.836 3.836 0 001.72-.756 2.25 2.25 0 000-3.18c-.302-.267-.673-.484-1.103-.66-.431-.177-.915-.329-1.394-.492-.577-.196-1.163-.415-1.555-.709a.75.75 0 010-1.06c.392-.294.978-.513 1.555-.709.479-.163.963-.315 1.394-.492.43-.176.8-.393 1.103-.66a2.25 2.25 0 000-3.18 3.836 3.836 0 00-1.72-.756V6z"})})}function f(e){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3.75a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 3.75zM3 8.25a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 8.25zM3.75 11.25a.75.75 0 000 1.5h16.5a.75.75 0 000-1.5H3.75zM3 16.25a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75a.75.75 0 01-.75-.75zM3.75 19.25a.75.75 0 000 1.5h16.5a.75.75 0 000-1.5H3.75z"}),(0,s.jsx)("path",{d:"M6.75 6a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9A.75.75 0 016.75 6zM6.75 10.5a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zM7.5 14.25a.75.75 0 000 1.5h9a.75.75 0 000-1.5h-9z"})]})}},15144:(e,t,a)=>{a.d(t,{B:()=>f});var s=a(45512),n=a(15928),i=a(44195),r=a(28531),l=a.n(r),o=a(79334),c=a(58009),d=a(21441),u=a(35786),h=a(69435),m=a(60746),p=a(92273);function f(){let e=(0,o.usePathname)(),t=(0,o.useRouter)(),{setIsOpen:a,isOpen:r,isMobile:f,toggleSidebar:x}=(0,m.V)(),[g,y]=(0,c.useState)([]),{user:v}=(0,p.d4)(e=>e.auth),w=v?(0,d.D)(v.role):d.R,b=e=>{y(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,s.jsxs)(s.Fragment,{children:[f&&r&&(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 transition-opacity duration-300",onClick:()=>{console.log("Overlay clicked, closing sidebar"),a(!1)},"aria-hidden":"true"}),(0,s.jsx)("aside",{className:(0,i.cn)("h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out",f?"fixed left-0 top-0 bottom-0 z-50 transform":"z-40",r||!f?"translate-x-0":"-translate-x-full"),"aria-label":"Main navigation","aria-hidden":f&&!r,children:(0,s.jsxs)("div",{className:"flex h-full flex-col pt-3 pb-5 pl-[25px] pr-[7px]",children:[(0,s.jsxs)("div",{className:"relative sidebar-logo-container pr-4.5",children:[(0,s.jsx)(l(),{href:"/dashboard",onClick:e=>{if(window.location.pathname.includes("/dashboard/users")){f&&x();return}e.preventDefault(),f&&x(),t.push("/dashboard")},className:"sidebar-logo",children:(0,s.jsx)(n.g,{})}),f&&(0,s.jsxs)("button",{onClick:x,className:"absolute right-0 top-1/2 -translate-y-1/2 text-right",children:[(0,s.jsx)("span",{className:"sr-only",children:"Close Menu"}),(0,s.jsx)(u.A6,{className:"ml-auto size-7"})]})]}),(0,s.jsx)("div",{className:"custom-scrollbar flex-1 overflow-y-auto pr-3 sidebar-menu",children:w.map(t=>(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"mb-5 text-sm font-medium text-gray-600",children:t.label}),(0,s.jsx)("nav",{role:"navigation","aria-label":t.label,children:(0,s.jsx)("ul",{className:"space-y-2",children:t.items.map(t=>(0,s.jsx)("li",{children:t.items.length?(0,s.jsxs)("div",{children:[(0,s.jsxs)(h.D,{isActive:t.items.some(({url:t})=>e.startsWith(t)),onClick:()=>b(t.title),children:[(0,s.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,s.jsx)("span",{children:t.title}),(0,s.jsx)(u.rX,{className:(0,i.cn)("ml-auto rotate-180 transition-transform duration-200",g.includes(t.title)&&"rotate-0"),"aria-hidden":"true"})]}),g.includes(t.title)&&(0,s.jsx)("ul",{className:"ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2",role:"menu",children:t.items.map(t=>(0,s.jsx)("li",{role:"none",children:(0,s.jsx)(h.D,{as:"link",href:t.url,isActive:e===t.url,children:(0,s.jsx)("span",{children:t.title})})},t.title))})]}):(()=>{let a="url"in t&&t.url?t.url:"/"+t.title.toLowerCase().split(" ").join("-");return(0,s.jsxs)(h.D,{className:"flex items-center gap-3 py-3",as:"link",href:a,isActive:"/dashboard"===a?e===a:e===a||e.startsWith(a+"/"),children:[(0,s.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,s.jsx)("span",{children:t.title})]})})()},t.title))})})]},t.label))})]})})]})}},69435:(e,t,a)=>{a.d(t,{D:()=>u});var s=a(45512),n=a(44195),i=a(21643),r=a(28531),l=a.n(r),o=a(79334),c=a(60746);let d=(0,i.F)("rounded-lg px-3.5 font-medium text-gray-700 transition-all duration-200",{variants:{isActive:{true:"bg-[rgba(87,80,241,0.07)] text-primary hover:bg-[rgba(87,80,241,0.07)]",false:"hover:bg-gray-100 hover:text-gray-900"}},defaultVariants:{isActive:!1}});function u(e){let{toggleSidebar:t,isMobile:a}=(0,c.V)();return((0,o.useRouter)(),"link"===e.as)?(0,s.jsx)(l(),{href:e.href,onClick:()=>{a&&t()},className:(0,n.cn)(d({isActive:e.isActive,className:"relative block py-2"}),e.className),children:e.children}):(0,s.jsx)("button",{onClick:e.onClick,"aria-expanded":e.isActive,className:d({isActive:e.isActive,className:"flex w-full items-center gap-3 py-3"}),children:e.children})}},15928:(e,t,a)=>{a.d(t,{g:()=>i});var s=a(45512),n=a(45103);function i(){return(0,s.jsx)("div",{className:"flex items-center justify-center h-10 max-w-[12rem] mt-1",children:(0,s.jsx)(n.default,{src:"/images/logo.png",width:120,height:40,className:"object-contain",alt:"Company logo",role:"presentation",quality:100,priority:!0})})}},25510:(e,t,a)=>{a.d(t,{_:()=>l});var s=a(58009),n=a(92273),i=a(16589),r=a.n(i);let l=()=>{let e=(0,n.d4)(e=>e.auth.user),[t,a]=(0,s.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,s.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,s=!1,n=!0,i="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){s=!0,n=!1,i="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let i=r()(e.nextPaymentDue),l=r()();if(t=i.diff(l,"day"),a){let a=r()().diff(r()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:s,needsPayment:n})}}}else"pending"===e.paymentStatus?(s=!1,n=!0,i="pending"):"overdue"===e.paymentStatus?(s=!1,n=!0,i="overdue"):(s=!1,n=!0,i="inactive");a({isActive:s,daysRemaining:t,status:i,needsPayment:n})},[e]),t}},44195:(e,t,a)=>{a.d(t,{cn:()=>i});var s=a(82281),n=a(94805);function i(...e){return(0,n.QP)((0,s.$)(e))}},37246:(e,t,a)=>{a.d(t,{V:()=>i});var s=a(12439);class n{async init(){try{this.db=await (0,s.P2)(this.DB_NAME,this.DB_VERSION,{upgrade(e){let t=e.createObjectStore("offlineSales",{keyPath:"id"});t.createIndex("by-timestamp","timestamp"),t.createIndex("by-status","status");let a=e.createObjectStore("products",{keyPath:"id"});a.createIndex("by-name","name"),a.createIndex("by-barcode","barcode");let s=e.createObjectStore("syncQueue",{keyPath:"id"});s.createIndex("by-priority","priority"),s.createIndex("by-timestamp","timestamp"),e.objectStoreNames.contains("store")||e.createObjectStore("store")}}),console.log("✅ Offline storage initialized")}catch(e){throw console.error("❌ Failed to initialize offline storage:",e),e}}async saveOfflineSale(e){if(!this.db)throw Error("Database not initialized");let t=`offline_sale_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,a={...e,id:t,timestamp:new Date,status:"pending",syncAttempts:0};return await this.db.add("offlineSales",a),console.log("\uD83D\uDCBE Offline sale saved:",t),t}async getOfflineSales(e){if(!this.db)throw Error("Database not initialized");return e?await this.db.getAllFromIndex("offlineSales","by-status",e):await this.db.getAll("offlineSales")}async updateSaleStatus(e,t,a){if(!this.db)throw Error("Database not initialized");let s=await this.db.get("offlineSales",e);if(!s)throw Error("Sale not found");s.status=t,s.syncAttempts+=1,s.lastSyncAttempt=new Date,a&&(s.errorMessage=a),await this.db.put("offlineSales",s)}async deleteOfflineSale(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("offlineSales",e)}async cacheProducts(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction("products","readwrite"),a=t.objectStore("products");for(let t of(await a.clear(),e))await a.add({...t,lastUpdated:new Date});await t.done,console.log(`💾 Cached ${e.length} products for offline use`)}async getCachedProducts(){if(!this.db)throw Error("Database not initialized");return await this.db.getAll("products")}async searchProducts(e){if(!this.db)throw Error("Database not initialized");let t=await this.db.getAll("products"),a=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(a)||e.sku&&e.sku.toLowerCase().includes(a)||e.barcode&&e.barcode.toLowerCase().includes(a))}async getProductByBarcode(e){if(!this.db)throw Error("Database not initialized");return await this.db.getFromIndex("products","by-barcode",e)}async addToSyncQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:`sync_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,timestamp:new Date,retryCount:0};await this.db.add("syncQueue",t)}async getSyncQueue(){if(!this.db)throw Error("Database not initialized");return await this.db.getAllFromIndex("syncQueue","by-priority")}async removeSyncQueueItem(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("syncQueue",e)}async cacheStore(e){if(!this.db)throw Error("Database not initialized");await this.db.put("store",e,"current-store"),console.log("\uD83D\uDCBE Store details cached for offline use")}async getCachedStore(){if(!this.db)throw Error("Database not initialized");return await this.db.get("store","current-store")}async getStorageStats(){if(!this.db)throw Error("Database not initialized");let[e,t,a,s,n]=await Promise.all([this.db.getAllFromIndex("offlineSales","by-status","pending"),this.db.getAllFromIndex("offlineSales","by-status","synced"),this.db.getAllFromIndex("offlineSales","by-status","failed"),this.db.getAll("products"),this.db.getAll("syncQueue")]);return{pendingSales:e.length,syncedSales:t.length,failedSales:a.length,cachedProducts:s.length,queueItems:n.length}}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=this.db.transaction(["offlineSales","products","syncQueue"],"readwrite");await Promise.all([e.objectStore("offlineSales").clear(),e.objectStore("products").clear(),e.objectStore("syncQueue").clear()]),await e.done,console.log("\uD83D\uDDD1️ All offline data cleared")}constructor(){this.db=null,this.DB_NAME="NEXAPO_POS_OFFLINE",this.DB_VERSION=1}}let i=new n},14396:(e,t,a)=>{a.d(t,{zy:()=>l});var s=a(37246),n=a(9919),i=a(49792);class r{constructor(){this.syncInProgress=!1,this.syncInterval=null,this.SYNC_INTERVAL=3e4,this.MAX_RETRY_ATTEMPTS=3,this.setupNetworkListeners(),this.startPeriodicSync()}setupNetworkListeners(){}startPeriodicSync(){this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{navigator.onLine&&!this.syncInProgress&&this.syncOfflineSales()},this.SYNC_INTERVAL)}async syncOfflineSales(){if(this.syncInProgress){console.log("⏳ Sync already in progress, skipping...");return}if(!navigator.onLine){console.log("\uD83D\uDCF1 Offline - skipping sync");return}this.syncInProgress=!0,console.log("\uD83D\uDD04 Starting offline sales sync...");try{let e=await s.V.getOfflineSales("pending"),t=await s.V.getOfflineSales("failed"),a=[...e,...t.filter(e=>e.syncAttempts<this.MAX_RETRY_ATTEMPTS)];if(0===a.length){console.log("✅ No offline sales to sync");return}console.log(`🔄 Syncing ${a.length} offline sales...`);let n=0,r=0;for(let e of a)try{await this.syncSingleSale(e),n++}catch(t){console.error(`❌ Failed to sync sale ${e.id}:`,t),r++,await s.V.updateSaleStatus(e.id,"failed",t instanceof Error?t.message:"Unknown error")}n>0&&(0,i.r)("success",`✅ Synced ${n} offline sales`),r>0&&(0,i.r)("warning",`⚠️ ${r} sales failed to sync - will retry later`)}catch(e){console.error("❌ Sync process failed:",e),(0,i.r)("error","Sync failed - will retry automatically")}finally{this.syncInProgress=!1}}async syncSingleSale(e){let t=n.M.getState().auth,a=t?.accessToken;if(!a)throw Error("No authentication token available");let i={totalAmount:e.totalAmount,paymentMethod:e.paymentMethod,items:e.items.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:e.receiptUrl||"",storeId:e.storeId},r=await fetch(`${process.env.NEXT_PUBLIC_API_URL}/sales`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({mode:"createnew",saleData:i})});if(!r.ok)throw Error((await r.json().catch(()=>({}))).message||`HTTP ${r.status}: ${r.statusText}`);let l=await r.json();if(!l.success)throw Error(l.message||"Sale creation failed");await s.V.updateSaleStatus(e.id,"synced"),console.log(`✅ Successfully synced sale ${e.id}`),setTimeout(()=>{s.V.deleteOfflineSale(e.id).catch(console.error)},864e5)}async forceSyncNow(){if(!navigator.onLine){(0,i.r)("warning","No internet connection - cannot sync now");return}(0,i.r)("success","Starting manual sync..."),await this.syncOfflineSales()}async getSyncStatus(){let e=await s.V.getStorageStats();return{isOnline:navigator.onLine,syncInProgress:this.syncInProgress,pendingSales:e.pendingSales,failedSales:e.failedSales,lastSyncAttempt:new Date}}destroy(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null)}}let l=new r},49792:(e,t,a)=>{a.d(t,{r:()=>n});var s=a(22403);let n=(e,t)=>{"success"===e?s.oR.success(t):"error"===e?s.oR.error(t):"warning"===e&&(0,s.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};n.success=e=>n("success",e),n.error=e=>n("error",e),n.warning=e=>n("warning",e)},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var s=a(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};