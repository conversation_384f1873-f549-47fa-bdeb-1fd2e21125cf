1. **users** ✅ (Users need to exist before other entities reference them)
2. **categories** ✅ (Products require categories)
3. **products** ✅ (Products depend on categories)
4. **suppliers** ✅ (Suppliers provide products)
5. **purchases** ✅ (Tracks purchases from suppliers)
6. **stockAdjustments** ✅ (Tracks stock changes; occurs before sales because stock levels must be managed)
7. **sales** ✅ (Records transactions)
8. **salesItems** ✅ (Links sales to products)
9. **receipts** ✅ (Links receipts to sales)
10. **payments** (Tracks payments for subscriptions)

