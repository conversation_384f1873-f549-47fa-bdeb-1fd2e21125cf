import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/reduxRTK/store/store';
import { useGetPaymentHistoryQuery } from '@/reduxRTK/services/paymentApi';
import dayjs from 'dayjs';

interface SubscriptionInfo {
  planType: 'monthly' | 'quarterly' | 'annual';
  planLabel: string;
  subscriptionPeriod: number;
  amount: number;
}

export const useSubscriptionInfo = (): SubscriptionInfo => {
  const user = useSelector((state: RootState) => state.auth.user);
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo>({
    planType: 'monthly',
    planLabel: 'Monthly (₵64/month)',
    subscriptionPeriod: 1,
    amount: 64
  });

  // Fetch payment history to get the latest successful payment
  const { data: paymentHistory } = useGetPaymentHistoryQuery(
    { page: 1, limit: 5 },
    { skip: !user }
  );

  useEffect(() => {
    if (!user || !user.lastPaymentDate || !user.nextPaymentDue) {
      return;
    }

    // First try to get subscription period from payment history
    const latestSuccessfulPayment = paymentHistory?.data?.payments?.find(
      payment => payment.status === 'successful'
    );

    if (latestSuccessfulPayment?.subscriptionPeriod) {
      const period = latestSuccessfulPayment.subscriptionPeriod;
      let planInfo: SubscriptionInfo;

      switch (period) {
        case 12:
          planInfo = {
            planType: 'annual',
            planLabel: 'Annual (₵683.52/year)',
            subscriptionPeriod: 12,
            amount: 683.52
          };
          break;
        case 3:
          planInfo = {
            planType: 'quarterly',
            planLabel: 'Quarterly (₵182.40/quarter)',
            subscriptionPeriod: 3,
            amount: 182.40
          };
          break;
        default:
          planInfo = {
            planType: 'monthly',
            planLabel: 'Monthly (₵64/month)',
            subscriptionPeriod: 1,
            amount: 64
          };
      }

      setSubscriptionInfo(planInfo);
      return;
    }

    // Fallback: Calculate from dates
    const lastPayment = dayjs(user.lastPaymentDate);
    const nextDue = dayjs(user.nextPaymentDue);
    const daysDiff = nextDue.diff(lastPayment, 'day');

    console.log('Subscription calculation fallback:', {
      lastPayment: lastPayment.format('YYYY-MM-DD'),
      nextDue: nextDue.format('YYYY-MM-DD'),
      daysDiff
    });

    let planInfo: SubscriptionInfo;

    if (daysDiff >= 350) { // ~12 months (NEW PRICING)
      planInfo = {
        planType: 'annual',
        planLabel: 'Annual (₵360/year)',
        subscriptionPeriod: 12,
        amount: 360
      };
    } else if (daysDiff >= 80) { // ~3 months (NEW PRICING)
      planInfo = {
        planType: 'quarterly',
        planLabel: 'Quarterly (₵108/quarter)',
        subscriptionPeriod: 3,
        amount: 108
      };
    } else {
      planInfo = {
        planType: 'monthly',
        planLabel: 'Monthly (₵40/month)',
        subscriptionPeriod: 1,
        amount: 40
      };
    }

    setSubscriptionInfo(planInfo);
  }, [user, paymentHistory]);

  return subscriptionInfo;
};
