"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON>, <PERSON>, Button } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useVerifyPaystackPaymentMutation } from "@/reduxRTK/services/paymentApi";
import { useGetCurrentUserQuery, userApi } from "@/reduxRTK/services/authApi";
import { useDispatch, useSelector } from "react-redux";
import { setUser } from "@/reduxRTK/services/authSlice";
import { RootState } from "@/reduxRTK/store/store";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";
import {
  refreshUserDataAfterPayment,
  verifyPaymentStatusUpdate,
} from "@/utils/refreshUserData";

const PaymentCallbackPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  const { user, accessToken } = useSelector((state: RootState) => state.auth);
  const [verifyPaystackPayment] = useVerifyPaystackPaymentMutation();
  const { refetch: refetchCurrentUser } = useGetCurrentUserQuery(undefined, {
    skip: true,
  });
  const [status, setStatus] = useState<"loading" | "success" | "failed">(
    "loading",
  );
  const [message, setMessage] = useState<string>("");

  const verifyPayment = useCallback(async (reference: string) => {
    try {
      console.log("🔍 Verifying payment with reference:", reference);

      const response = await verifyPaystackPayment({ reference }).unwrap();

      if (response.success) {
        setStatus("success");
        setMessage(
          "Payment verified successfully! Your subscription is now active.",
        );
        showMessage(
          "success",
          "Payment successful! Your subscription is now active.",
        );

        console.log(
          "✅ Payment verification successful, updating user data...",
        );

        // Use the new utility function to refresh user data
        if (user && accessToken) {
          try {
            const refreshResult = await refreshUserDataAfterPayment({
              dispatch,
              accessToken,
              currentUser: user,
              maxRetries: 5,
              retryDelay: 2000,
            });

            if (refreshResult.success && refreshResult.user) {
              console.log("🎉 User data refresh successful!");

              // Verify the payment status is correct
              const isVerified = verifyPaymentStatusUpdate(refreshResult.user);
              if (isVerified) {
                console.log("✅ Payment status verification passed");
              } else {
                console.log(
                  "⚠️ Payment status verification failed, but proceeding anyway",
                );
              }
            } else {
              console.log("⚠️ User data refresh failed:", refreshResult.error);
            }

            // Navigate to dashboard after user data is updated
            try {
              // Force refresh user data using the global refresh function
              if (
                typeof window !== "undefined" &&
                (window as any).__FORCE_REFRESH_USER_DATA
              ) {
                await (window as any).__FORCE_REFRESH_USER_DATA();
              }

              // Clear only RTK Query cache, keep auth data
              localStorage.removeItem("user_cache");
              localStorage.removeItem("payment_status_cache");

              // Add a small delay to ensure state is updated
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Force a hard navigation to dashboard
              window.location.replace("/dashboard");
            } catch (e) {
              console.error("Error refreshing user data:", e);
              // Fallback navigation with replace
              window.location.replace("/dashboard");
            }
          } catch (error) {
            console.error("❌ Error in user data refresh:", error);
            // Fallback navigation
            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 1000);
          }
        } else {
          setTimeout(() => {
            window.location.href = "/dashboard";
          }, 1000);
        }
      } else {
        setStatus("failed");
        setMessage(response.message || "Payment verification failed.");
        console.error("❌ Payment verification failed:", response.message);
      }
    } catch (error: any) {
      console.error("❌ Payment verification error:", error);
      setStatus("failed");
      setMessage(
        error.data?.message || error.message || "Payment verification failed.",
      );
    }
  }, [verifyPaystackPayment, user, accessToken, dispatch]);

  useEffect(() => {
    const reference = searchParams?.get("reference");
    const trxref = searchParams?.get("trxref");

    // Use reference from URL params or trxref as fallback
    const paymentReference = reference || trxref;

    if (paymentReference) {
      verifyPayment(paymentReference);
    } else {
      setStatus("failed");
      setMessage("No payment reference found in the URL.");
    }
  }, [searchParams, verifyPayment]);

  const handleReturnToDashboard = async () => {
    // Ensure user payment status is updated before navigating
    if (user && accessToken && status === "success") {
      try {
        const refreshResult = await refreshUserDataAfterPayment({
          dispatch,
          accessToken,
          currentUser: user,
          maxRetries: 3,
          retryDelay: 1000,
        });

        // Navigate to dashboard
        window.location.href = "/dashboard";
      } catch (error) {
        console.error("Error in manual refresh:", error);
        // Fallback navigation
        window.location.href = "/dashboard";
      }
    } else {
      window.location.href = "/dashboard";
    }
  };

  const handleRetryPayment = () => {
    router.push("/payment");
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
        <div className="text-center">
          {status === "loading" && (
            <>
              <Spin
                indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
              />
              <h2 className="mb-2 mt-4 text-xl font-semibold">
                Verifying Payment
              </h2>
              <p className="text-gray-600">
                Please wait while we verify your payment...
              </p>
            </>
          )}

          {status === "success" && (
            <>
              <CheckCircleOutlined style={{ fontSize: 48, color: "#52c41a" }} />
              <h2 className="mb-2 mt-4 text-xl font-semibold text-green-600">
                Payment Successful!
              </h2>
              <p className="mb-4 text-gray-600">{message}</p>
              <Alert
                message="Subscription Activated"
                description="You will be redirected to your dashboard in a few seconds."
                type="success"
                showIcon
                className="mb-4"
              />
              <Button
                type="primary"
                onClick={handleReturnToDashboard}
                className="w-full"
              >
                Go to Dashboard
              </Button>
            </>
          )}

          {status === "failed" && (
            <>
              <CloseCircleOutlined style={{ fontSize: 48, color: "#ff4d4f" }} />
              <h2 className="mb-2 mt-4 text-xl font-semibold text-red-600">
                Payment Failed
              </h2>
              <p className="mb-4 text-gray-600">{message}</p>
              <Alert
                message="Payment Verification Failed"
                description="Please try again or contact support if the issue persists."
                type="error"
                showIcon
                className="mb-4"
              />
              <div className="space-y-2">
                <Button
                  type="primary"
                  onClick={handleRetryPayment}
                  className="w-full"
                >
                  Try Again
                </Button>
                <Button onClick={handleReturnToDashboard} className="w-full">
                  Return to Dashboard
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentCallbackPage;
