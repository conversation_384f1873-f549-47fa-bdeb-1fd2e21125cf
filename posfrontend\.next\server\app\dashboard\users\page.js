(()=>{var e={};e.id=9242,e.ids=[9242],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},92564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=s(70260),a=s(28203),l=s(25155),n=s.n(l),i=s(67292),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d=["",{children:["dashboard",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,54165)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\users\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/users/page",pathname:"/dashboard/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48219:(e,t,s)=>{Promise.resolve().then(s.bind(s,54165))},58835:(e,t,s)=>{Promise.resolve().then(s.bind(s,24017))},39193:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},53180:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},23847:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},67586:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},24648:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},45211:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),a=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var n=s(78480);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},24017:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>en});var r=s(45512),a=s(58009),l=s(6987),n=s(21419),i=s(3117);s(78058),s(5523);var c=s(88752),d=s(11855);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};var u=s(78480),x=a.forwardRef(function(e,t){return a.createElement(u.A,(0,d.A)({},e,{ref:t,icon:o}))}),m=s(60636),h=s(97245),p=s(78337);let g=()=>{let[e,t]=(0,a.useState)(1),[s,r]=(0,a.useState)(10),[l,n]=(0,a.useState)(""),i=(0,p.d)(l,500);(0,a.useEffect)(()=>{t(1)},[i]);let{data:c,isLoading:d,isFetching:o,refetch:u}=(0,h.VL)({page:e,limit:s,search:i},{refetchOnMountOrArgChange:!0});return console.log("User search state:",{searchTerm:l,debouncedSearchTerm:i,resultsCount:c?.data?.users?.length||0,totalResults:c?.data?.total||0}),{users:c?.data?.users||[],total:c?.data?.total||0,isLoading:d,isFetching:o,refetch:u,searchTerm:l,setSearchTerm:n,pagination:{page:e,pageSize:s,setPage:t,setPageSize:r}}};var f=s(49792);let j=e=>{let[t,s]=(0,a.useState)(!1),[r,l]=(0,a.useState)(null),[n,{isLoading:i}]=(0,h.HC)();return{confirmDelete:e=>{l(e),s(!0)},cancelDelete:()=>{s(!1),l(null)},executeDelete:async()=>{if(r)try{await n(r).unwrap(),(0,f.r)("success","User deleted successfully"),e&&e(),s(!1),l(null)}catch(e){(0,f.r)("error",e.message||"Failed to delete user"),console.error("Delete error:",e)}},isDeleting:i,isConfirmOpen:t,userToDelete:r}},y=e=>{let[t,{isLoading:s}]=(0,h.qx)();return{bulkDeleteUsers:async s=>{try{console.log("Bulk deleting users with IDs:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete users");return(0,f.r)("success",`${s.length} users deleted successfully`),e&&e(),r.data}catch(e){throw console.error("Bulk delete users error:",e),(0,f.r)("error",e.message||"Failed to delete users"),e}},isDeleting:s}};var b=s(48752),v=s(31111),w=s(77067),A=s(70001),N=s(60380),C=s(39193),k=s(45211),S=s(67586),P=s(25421),E=s(25834),z=s(99261),M=s(86977),D=s(63844),U=s(73542),I=s(16589),F=s.n(I),B=s(48228);let O=({users:e,isMobile:t,onView:s,onEdit:l,onDelete:n,onBulkDelete:c,canManageUser:d})=>{let o=(0,U.E)(),u=t||o,[x,m]=(0,a.useState)([]),[h,p]=(0,a.useState)(!1),g=t=>{let s=t.target.checked;p(s),s?m(e.filter(e=>d(e.role)).map(e=>e.id)):m([])},f=(e,t)=>{t?m(t=>[...t,e]):m(t=>t.filter(t=>t!==e))},j=e=>{switch(e){case"paid":return(0,r.jsx)(v.A,{className:"status-tag status-paid",icon:(0,r.jsx)(N.A,{}),color:"success",children:"Paid"});case"pending":return(0,r.jsx)(v.A,{className:"status-tag status-pending",icon:(0,r.jsx)(C.A,{}),color:"warning",children:"Pending"});case"overdue":return(0,r.jsx)(v.A,{className:"status-tag status-overdue",icon:(0,r.jsx)(k.A,{}),color:"error",children:"Overdue"});case"inactive":return(0,r.jsx)(v.A,{className:"status-tag status-inactive",icon:(0,r.jsx)(S.A,{}),color:"default",children:"Inactive"});default:return(0,r.jsx)(v.A,{className:"status-tag",color:"default",children:e})}},y=e=>{switch(e){case"superadmin":return(0,r.jsx)(v.A,{color:"purple",children:"Super Admin"});case"admin":return(0,r.jsx)(v.A,{color:"blue",children:"Admin"});case"cashier":return(0,r.jsx)(v.A,{color:"green",children:"Cashier"});default:return(0,r.jsx)(v.A,{color:"default",children:e})}};return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[x.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[x.length," ",1===x.length?"user":"users"," selected"]}),(0,r.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(P.A,{}),onClick:()=>{x.length>0&&c?(c(x),m([]),p(!1)):b.Ay.warning({message:"No users selected",description:"Please select at least one user to delete."})},className:"ml-2",children:"Delete Selected"})]}),u?(0,r.jsxs)(D.jB,{columns:"50px 200px 100px 100px 150px",minWidth:"700px",children:[(0,r.jsx)(D.A0,{className:"text-center",children:(0,r.jsx)(w.A,{checked:h,onChange:g,disabled:0===e.filter(e=>d(e.role)).length})}),(0,r.jsx)(D.A0,{sticky:u?void 0:"left",children:"Name"}),!u&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(D.A0,{children:"Email"}),(0,r.jsx)(D.A0,{children:"Phone"}),(0,r.jsx)(D.A0,{children:"Created At"})]}),(0,r.jsx)(D.A0,{children:"Role"}),(0,r.jsx)(D.A0,{children:"Status"}),(0,r.jsx)(D.A0,{sticky:u?void 0:"right",className:"text-right",children:"Actions"}),e.map(e=>(0,r.jsxs)(D.Hj,{selected:x.includes(e.id),children:[(0,r.jsx)(D.nA,{className:"text-center",children:d(e.role)&&(0,r.jsx)(w.A,{checked:x.includes(e.id),onChange:t=>f(e.id,t.target.checked)})}),(0,r.jsx)(D.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,r.jsx)(D.nA,{children:y(e.role)}),(0,r.jsx)(D.nA,{children:j(e.paymentStatus)}),(0,r.jsx)(D.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(E.A,{}),onClick:()=>s(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),d(e.role)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(z.A,{}),onClick:()=>l(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(M.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto w-full",children:(0,r.jsxs)("table",{className:"min-w-[900px] w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(w.A,{checked:h,onChange:g,disabled:0===e.filter(e=>d(e.role)).length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-white shadow-md px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Name"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell",children:"Email"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell",children:"Phone"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden xl:table-cell",children:"Created At"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Role"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-white shadow-md px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:x.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:d(e.role)&&(0,r.jsx)(w.A,{checked:x.includes(e.id),onChange:t=>f(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] truncate overflow-hidden text-ellipsis",children:e.name})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell",children:(0,r.jsx)("div",{className:"max-w-[200px] truncate overflow-hidden text-ellipsis",children:e.email})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell",children:(0,r.jsx)("div",{className:"max-w-[140px] truncate overflow-hidden text-ellipsis",children:(0,B.oB)(e.phone)})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800 hidden xl:table-cell",children:(0,r.jsx)("div",{className:"max-w-[130px] truncate overflow-hidden text-ellipsis",children:F()(e.createdAt).format("MMM D, YYYY")})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:y(e.role)}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:j(e.paymentStatus)}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(A.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(E.A,{}),onClick:()=>s(e.id),type:"text",className:"view-button text-green-700",size:"middle"})}),d(e.role)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(z.A,{}),onClick:()=>l(e),type:"text",className:"edit-button",size:"middle"})}),(0,r.jsx)(A.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(M.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button",danger:!0,size:"middle"})})]})]})})]},e.id))})]})})]})};var R=s(59022),L=s(60165);let q=({page:e,pageSize:t,total:s,setPage:a,isMobile:l})=>{let n=Math.ceil(s/t);return(0,r.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,s)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(R.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(t,s)=>{let l=s+1;return(0,r.jsx)("button",{onClick:()=>a(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:l},l)}),(0,r.jsxs)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=n?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(L.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>a(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,r.jsx)("button",{onClick:()=>a(e+1),disabled:e>=n,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var T=s(7325),_=s(41257),$=s(37764),V=s(98776);let H=e=>{let[t,{isLoading:s}]=(0,h.i0)();return{createUser:async s=>{try{let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to create user");return(0,f.r)("success","User created successfully"),e&&e(),r.data}catch(e){throw console.error("Create user error:",e),(0,f.r)("error",e.message||"Failed to create user"),e}},isCreating:s}},W=e=>{let[t,{isLoading:s}]=(0,h.H7)();return{updateUser:async(s,r)=>{try{let a=await t({userId:s,data:r}).unwrap();if(!a.success)throw Error(a.message||"Failed to update user");return(0,f.r)("success","User updated successfully"),e&&e(),a.data}catch(e){throw console.error("Update user error:",e),(0,f.r)("error",e.message||"Failed to update user"),e}},isUpdating:s}};s(61248);let{Option:Y}=T.A,G=({isOpen:e,onClose:t,onSuccess:s,user:l,currentUser:n})=>{let[c]=_.A.useForm(),d=!!l,{createUser:o,isCreating:u}=H(s),{updateUser:x,isUpdating:m}=W(s),h=u||m;(0,a.useEffect)(()=>{if(e){if(l){let e=(0,B.n4)(l.phone);c.setFieldsValue({name:l.name,email:l.email,phone:e,role:l.role,paymentStatus:l.paymentStatus})}else c.resetFields(),n?.role==="admin"&&c.setFieldsValue({role:"cashier",paymentStatus:"paid"})}},[e,l,c,n]);let p=async()=>{try{let e=await c.validateFields();if(d&&l){let t=(0,B.n4)(e.phone),s={name:e.name,email:e.email,phone:t};(n?.role==="superadmin"||n?.role==="admin"&&"cashier"===e.role)&&(s.role=e.role),n?.role==="superadmin"&&(s.paymentStatus=e.paymentStatus),await x(l.id,s)}else{let t=(0,B.n4)(e.phone),s={name:e.name,email:e.email,password:e.password,phone:t};n?.role==="superadmin"?s.role=e.role:n?.role==="admin"&&(s.role="cashier",s.paymentStatus="paid"),await o(s)}t()}catch(e){console.error("Form submission error:",e)}},g=n?.role==="superadmin"?["superadmin","admin","cashier"]:n?.role==="admin"?["cashier"]:[],f=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.Ay,{onClick:t,disabled:h,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(i.Ay,{type:"primary",loading:h,onClick:p,children:d?"Update":"Create"})]});return(0,r.jsxs)(V.A,{isOpen:e,onClose:t,title:d?"Edit User":"Add New User",width:"450px",footer:f,children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing User: ",l?.name]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),"Create New User"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:d?"Update user information using the form below":"Fill in the details to create a new user account"})]}),(0,r.jsxs)(_.A,{form:c,layout:"vertical",initialValues:{role:"cashier",paymentStatus:n?.role==="admin"?"paid":"pending"},className:"text-gray-800 w-full",style:{color:"#333"},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,r.jsx)(_.A.Item,{name:"name",label:"Name",rules:[{required:!0,message:"Please enter name"}],children:(0,r.jsx)($.A,{placeholder:"Enter full name"})}),(0,r.jsx)(_.A.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Please enter email"},{type:"email",message:"Please enter a valid email"}],children:(0,r.jsx)($.A,{placeholder:"Enter email address"})}),(0,r.jsx)(_.A.Item,{name:"phone",label:"Phone",rules:[{required:!0,message:"Please enter phone number"},{min:5,message:"Phone number must be at least 5 characters"},{pattern:/^[0-9+\-\s()]*$/,message:"Phone number can only contain digits, spaces, and the characters +, -, (, )"}],tooltip:"Enter phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)",children:(0,r.jsx)($.A,{placeholder:"e.g., ************"})})]}),!d&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Authentication"}),(0,r.jsx)(_.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter password"},{min:6,message:"Password must be at least 6 characters"}],children:(0,r.jsx)($.A.Password,{placeholder:"Enter password"})})]}),g.length>0&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Access Control"}),(0,r.jsx)(_.A.Item,{name:"role",label:"Role",rules:[{required:!0,message:"Please select role"}],children:(0,r.jsx)(T.A,{placeholder:"Select role",children:g.map(e=>(0,r.jsx)(Y,{value:e,children:"superadmin"===e?"Super Admin":"admin"===e?"Admin":"Cashier"},e))})})]}),d&&n?.role==="superadmin"&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Payment Information"}),(0,r.jsx)(_.A.Item,{name:"paymentStatus",label:"Payment Status",rules:[{required:!0,message:"Please select payment status"}],children:(0,r.jsxs)(T.A,{placeholder:"Select payment status",children:[(0,r.jsx)(Y,{value:"pending",children:"Pending"}),(0,r.jsx)(Y,{value:"paid",children:"Paid"}),(0,r.jsx)(Y,{value:"overdue",children:"Overdue"}),(0,r.jsx)(Y,{value:"inactive",children:"Inactive"})]})})]})]})]})};var J=s(12869),K=s(24648),X=s(53180),Q=s(23847),Z=s(81045);let ee=e=>{let{data:t,isLoading:s}=(0,h.$f)(e||0,{skip:!e});return{user:t?.data,isLoading:s}},et=({isOpen:e,onClose:t,userId:s})=>{let{user:a,isLoading:l}=ee(s),d=e=>{if(!e)return"N/A";try{return F()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}},o=(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"})});return(0,r.jsx)(V.A,{isOpen:e,onClose:t,title:"User Details",width:"500px",footer:o,children:l?(0,r.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"User Profile: ",a.name,(0,r.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${"superadmin"===a.role?"bg-purple-100 text-purple-800":"admin"===a.role?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:"superadmin"===a.role?"Super Admin":"admin"===a.role?"Admin":"Cashier"})]}),(0,r.jsxs)("p",{className:"text-gray-600 mt-1 flex items-center",children:["Complete user information and account details",(0,r.jsx)("span",{className:`ml-2 text-xs px-2 py-1 rounded-full ${"paid"===a.paymentStatus?"bg-green-100 text-green-800":"pending"===a.paymentStatus?"bg-yellow-100 text-yellow-800":"overdue"===a.paymentStatus?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:"paid"===a.paymentStatus?"Paid":"pending"===a.paymentStatus?"Pending":"overdue"===a.paymentStatus?"Overdue":"Inactive"})]})]}),(0,r.jsxs)(J.A,{bordered:!0,column:1,className:"user-detail-dark",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,r.jsx)(J.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(K.A,{})," Name"]}),children:a.name}),(0,r.jsx)(J.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(X.A,{})," Email"]}),children:a.email}),(0,r.jsx)(J.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(Q.A,{})," Phone"]}),children:(0,B.oB)(a.phone)}),(0,r.jsx)(J.A.Item,{label:"Role",children:(e=>{switch(e){case"superadmin":return(0,r.jsx)(v.A,{color:"purple",children:"Super Admin"});case"admin":return(0,r.jsx)(v.A,{color:"blue",children:"Admin"});case"cashier":return(0,r.jsx)(v.A,{color:"green",children:"Cashier"});default:return(0,r.jsx)(v.A,{color:"default",children:e})}})(a.role)}),(0,r.jsx)(J.A.Item,{label:"Payment Status",children:(e=>{switch(e){case"paid":return(0,r.jsx)(v.A,{icon:(0,r.jsx)(N.A,{}),color:"success",children:"Paid"});case"pending":return(0,r.jsx)(v.A,{icon:(0,r.jsx)(C.A,{}),color:"warning",children:"Pending"});case"overdue":return(0,r.jsx)(v.A,{icon:(0,r.jsx)(k.A,{}),color:"error",children:"Overdue"});case"inactive":return(0,r.jsx)(v.A,{icon:(0,r.jsx)(S.A,{}),color:"default",children:"Inactive"});default:return(0,r.jsx)(v.A,{color:"default",children:e})}})(a.paymentStatus)}),(0,r.jsx)(J.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(Z.A,{})," Created At"]}),children:d(a.createdAt)}),(0,r.jsx)(J.A.Item,{label:"Last Payment Date",children:d(a.lastPaymentDate)}),(0,r.jsx)(J.A.Item,{label:"Next Payment Due",children:d(a.nextPaymentDue)})]})]}):(0,r.jsx)("div",{className:"text-center py-8 text-gray-800",children:"User not found"})})};var es=s(58733);s(86078);let er=({searchTerm:e,setSearchTerm:t,isMobile:s=!1})=>(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)($.A,{placeholder:"Search by name, email, or phone...",prefix:(0,r.jsx)(es.A,{className:"text-gray-400"}),value:e,onChange:e=>{let s=e.target.value;console.log("Search input changed:",s),t(s)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-400 focus:border-blue-400",style:{width:s?"100%":"300px",height:"36px",backgroundColor:"#ffffff",color:"#333333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var ea=s(51531);let el=()=>{let[e,t]=(0,a.useState)({width:0,height:0});return(0,a.useEffect)(()=>{},[]),e},en=()=>{let{user:e}=(0,m.A)(),{width:t}=el(),s=t<640,[d,o]=(0,a.useState)(!1),[u,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(null),[w,A]=(0,a.useState)(null),{users:N,total:C,isLoading:k,isFetching:S,refetch:P,searchTerm:E,setSearchTerm:z,pagination:{page:M,pageSize:D,setPage:U}}=g(),{confirmDelete:I,cancelDelete:F,executeDelete:B,isDeleting:R,isConfirmOpen:L,userToDelete:T}=j(P),{bulkDeleteUsers:_,isDeleting:$}=y(()=>{H(!1),P()}),[V,H]=(0,a.useState)(!1),[W,Y]=(0,a.useState)([]),J=e?.role==="superadmin"||e?.role==="admin",K=async()=>{if(console.log("confirmBulkDelete called with users:",W),W.length>0)try{await _(W)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return((0,a.useEffect)(()=>{console.log("Users page - isAddPanelOpen changed:",d)},[d]),k)?(0,r.jsx)("div",{className:"p-2 sm:p-4 w-full",children:(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"User Management"}),styles:{body:{padding:"16px",minHeight:"200px",backgroundColor:"#ffffff"},header:{padding:s?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,r.jsx)("div",{className:"flex justify-center items-center h-60",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})})})}):(0,r.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"User Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:s?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:J&&(0,r.jsx)(i.Ay,{type:"primary",icon:(0,r.jsx)(x,{}),onClick:()=>{console.log("Add user button clicked"),v(null),setTimeout(()=>{o(!0)},0)},size:s?"small":"middle",children:s?"":"Add User"}),children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden",children:[(0,r.jsx)(er,{searchTerm:E,setSearchTerm:z,isMobile:s}),S?(0,r.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[N.length>0?(0,r.jsx)(O,{users:N,isMobile:s,onView:e=>{A(e),f(!0)},onEdit:e=>{v(e),h(!0)},onDelete:I,onBulkDelete:e=>{console.log("handleBulkDelete called with userIds:",e),Y(e),H(!0)},canManageUser:t=>e?.role==="superadmin"||e?.role==="admin"&&"cashier"===t}):(0,r.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-700",children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No users found matching your search criteria."}),(0,r.jsx)(i.Ay,{type:"primary",onClick:()=>z(""),className:"mt-4 bg-blue-500 hover:bg-blue-600",children:"Clear Search"})]}):(0,r.jsxs)("p",{children:["No users found. ",J&&"Click 'Add User' to create one."]})}),N.length>0&&(0,r.jsx)(q,{page:M,pageSize:D,total:C,setPage:U,isMobile:s})]})]})}),(0,r.jsx)(G,{isOpen:d,onClose:()=>o(!1),onSuccess:()=>{o(!1),P()},currentUser:e}),(0,r.jsx)(G,{isOpen:u,onClose:()=>h(!1),onSuccess:()=>{h(!1),P()},user:b,currentUser:e}),(0,r.jsx)(et,{isOpen:p,onClose:()=>{f(!1),A(null)},userId:w}),(0,r.jsx)(ea.A,{isOpen:L,onClose:F,onConfirm:B,title:"Delete User",message:"Are you sure you want to delete this user? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:R,type:"danger"}),(0,r.jsx)(ea.A,{isOpen:V,onClose:()=>{H(!1),Y([])},onConfirm:K,title:"Delete Multiple Users",message:`Are you sure you want to delete ${W.length} users? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:$,type:"danger"})]})}},78337:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(58009);function a(e,t){let[s,a]=(0,r.useState)(e);return s}},48228:(e,t,s)=>{"use strict";s.d(t,{n4:()=>r,oB:()=>a});let r=e=>{if(!e)return"";let t=e.replace(/\D/g,"");return e.startsWith("0")?e:t.startsWith("233")||e.startsWith("+233")?"0"+t.substring(3):e},a=e=>{let t=r(e);if(!t)return"";let s=t.replace(/\D/g,"");return 10===s.length&&s.startsWith("0")?`${s.substring(0,3)} ${s.substring(3,7)} ${s.substring(7)}`:t}},54165:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\users\\page.tsx","default")},78058:()=>{},5523:()=>{},61248:()=>{},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,1785,5482,106,4286,6165],()=>s(92564));module.exports=r})();