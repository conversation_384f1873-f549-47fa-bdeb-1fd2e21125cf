// services/userStoreApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';
import { Store, UserStoreAssociation } from '@/types/store';

export const userStoreApi = createApi({
  reducerPath: 'userStoreApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['UserStore', 'Store'] as const,
  endpoints: (builder) => ({
    // Get all stores for a user
    getUserStores: builder.query<ApiResponse<Store[]>, number>({
      query: (userId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'getUserStores',
            userId,
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['UserStore', 'Store'],
    }),

    // Get default store for a user
    getUserDefaultStore: builder.query<ApiResponse<Store | null>, number>({
      query: (userId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'getDefaultStore',
            userId,
          },
          token,
        };
      },
      providesTags: ['UserStore', 'Store'],
    }),

    // Associate user with store
    associateUserWithStore: builder.mutation<ApiResponse<UserStoreAssociation>, { userId: number; storeId: number; isDefault?: boolean }>({
      query: ({ userId, storeId, isDefault = false }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'associate',
            userId,
            storeId,
            isDefault,
          },
          token,
        };
      },
      invalidatesTags: ['UserStore', 'Store'],
    }),

    // Set default store for user
    setUserDefaultStore: builder.mutation<ApiResponse<UserStoreAssociation>, { userId: number; storeId: number }>({
      query: ({ userId, storeId }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'setDefaultStore',
            userId,
            storeId,
          },
          token,
        };
      },
      invalidatesTags: ['UserStore', 'Store'],
    }),

    // Remove user-store association
    removeUserFromStore: builder.mutation<ApiResponse<{ deletedId: number }>, { userId: number; storeId: number }>({
      query: ({ userId, storeId }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'removeAssociation',
            userId,
            storeId,
          },
          token,
        };
      },
      invalidatesTags: ['UserStore', 'Store'],
    }),
    // Create a store for a user
    createUserStore: builder.mutation<ApiResponse<Store>, { data: any }>({
      query: ({ data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'createStore', // Using the mode that backend now supports
            ...data,
          },
          token,
        };
      },
      invalidatesTags: ['UserStore', 'Store'],
    }),

    // Update a store for a user
    updateUserStore: builder.mutation<ApiResponse<Store>, { storeId: number; data: any }>({
      query: ({ storeId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/user-stores',
          payloaddata: {
            mode: 'updateStore', // Using the mode that backend now supports
            storeId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: ['UserStore', 'Store'],
    }),
  }),
});

export const {
  useGetUserStoresQuery,
  useGetUserDefaultStoreQuery,
  useAssociateUserWithStoreMutation,
  useSetUserDefaultStoreMutation,
  useRemoveUserFromStoreMutation,
  useCreateUserStoreMutation,
  useUpdateUserStoreMutation,
} = userStoreApi;
