exports.id=4286,exports.ids=[4286],exports.modules={29800:(e,s,t)=>{Promise.resolve().then(t.bind(t,3453))},16648:(e,s,t)=>{Promise.resolve().then(t.bind(t,57176))},57176:(e,s,t)=>{"use strict";t.d(s,{default:()=>X});var a=t(45512),n=t(58009),l=t.n(n),r=t(15144),o=t(59262),i=t(94682),c=t(92273),d=t(49198),m=t(3117),u=t(79334),p=t(25510);let x=()=>{let e=(0,c.d4)(e=>e.auth.user),s=(0,u.useRouter)(),[t,l]=(0,n.useState)(!0),{daysRemaining:r,needsPayment:o,status:i}=(0,p._)();return e&&t&&o&&"superadmin"!==e.role?(0,a.jsx)(d.A,{message:`Payment Reminder: ${"overdue"===i||null!==r&&r<0?"Your subscription payment is overdue. Please make a payment to continue using the system.":0===r?"Your subscription payment is due today. Please make a payment to avoid service interruption.":null!==r?`Your subscription payment is due in ${r} day${r>1?"s":""}.`:"pending"===i?"Your payment is pending verification. If you've already made a payment, please wait for confirmation.":"Your subscription is inactive. Please make a payment to activate your account."}`,type:"overdue"===i||null!==r&&r<0?"error":null!==r&&r<=3?"warning":"info",showIcon:!0,action:(0,a.jsx)(m.Ay,{size:"small",type:"primary",onClick:()=>{s.push("/payment")},children:"Make Payment"}),closable:!0,onClose:()=>l(!1),className:"mb-4"}):null};var h=t(45546),y=t(24715),f=t(60746),j=t(61667),g=t(6987),v=t(88206),b=t(9334),w=t(27069),N=t(94762),A=t(97071),S=t(45905),P=t(60380),k=t(42608),C=t(72501);let{Title:E,Text:I,Paragraph:O}=j.A,R=()=>{let[e,s]=(0,n.useState)(null),[t,l]=(0,n.useState)(!1),[r,o]=(0,n.useState)(!1),[i,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{(()=>{let e=window.matchMedia("(display-mode: standalone)").matches,s=!0===window.navigator.standalone;o(e||s)})();let e=e=>{e.preventDefault(),s(e),r||setTimeout(()=>{l(!0)},1e4)},t=()=>{o(!0),l(!1),s(null)};return window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",t),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",t)}},[r]);let d=async()=>{if(e)try{await e.prompt();let{outcome:t}=await e.userChoice;"accepted"===t?console.log("PWA installation accepted"):console.log("PWA installation dismissed"),s(null),l(!1),c(!1)}catch(e){console.error("Error during PWA installation:",e)}};return r||sessionStorage.getItem("pwa-install-dismissed")?null:(0,a.jsxs)(a.Fragment,{children:[t&&e&&(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,a.jsxs)(g.A,{className:"shadow-lg border-blue-500 border-2",style:{maxWidth:300},actions:[(0,a.jsx)(m.Ay,{type:"primary",icon:(0,a.jsx)(w.A,{}),onClick:()=>{c(!0)},className:"w-full",children:"Install App"},"install")],children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(N.A,{className:"text-2xl text-blue-500 mb-2"}),(0,a.jsx)(E,{level:5,className:"mb-1",children:"Install NEXAPO POS"}),(0,a.jsx)(I,{type:"secondary",className:"text-sm",children:"Get faster access and work offline"})]}),(0,a.jsx)(m.Ay,{type:"text",icon:(0,a.jsx)(A.A,{}),onClick:()=>{l(!1),sessionStorage.setItem("pwa-install-dismissed","true")},className:"absolute top-2 right-2 p-1",size:"small"})]})}),(0,a.jsx)(v.A,{title:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"mr-2 text-blue-500"}),"Install NEXAPO POS App"]}),open:i,onCancel:()=>c(!1),footer:[(0,a.jsx)(m.Ay,{onClick:()=>c(!1),children:"Maybe Later"},"cancel"),(0,a.jsx)(m.Ay,{type:"primary",icon:(0,a.jsx)(w.A,{}),onClick:d,disabled:!e,children:"Install Now"},"install")],width:500,children:(0,a.jsxs)("div",{className:"py-4",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(S.A,{className:"text-3xl text-blue-500"})}),(0,a.jsx)(E,{level:4,className:"mb-2",children:"Get the full NEXAPO POS experience"}),(0,a.jsx)(I,{type:"secondary",children:"Install our app for faster access and enhanced offline capabilities"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(P.A,{className:"text-green-500 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(I,{strong:!0,children:"Lightning Fast"}),(0,a.jsx)("br",{}),(0,a.jsx)(I,{type:"secondary",className:"text-sm",children:"Instant loading and smooth performance"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(k.A,{className:"text-green-500 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(I,{strong:!0,children:"Works Offline"}),(0,a.jsx)("br",{}),(0,a.jsx)(I,{type:"secondary",className:"text-sm",children:"Continue making sales even without internet"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(N.A,{className:"text-green-500 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(I,{strong:!0,children:"Native App Experience"}),(0,a.jsx)("br",{}),(0,a.jsx)(I,{type:"secondary",className:"text-sm",children:"Full-screen mode with app-like navigation"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(C.A,{className:"text-green-500 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(I,{strong:!0,children:"Desktop & Mobile"}),(0,a.jsx)("br",{}),(0,a.jsx)(I,{type:"secondary",className:"text-sm",children:"Works perfectly on all your devices"})]})]})]}),(0,a.jsx)(b.A,{}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)(I,{strong:!0,className:"text-blue-700",children:"\uD83D\uDCA1 Pro Tip:"}),(0,a.jsx)("br",{}),(0,a.jsx)(I,{className:"text-blue-600 text-sm",children:"After installation, you can access NEXAPO POS directly from your home screen or desktop, just like any other app!"})]})]})})]})};var L=t(48752),D=t(56403);let M=()=>{let[e,s]=(0,n.useState)(!1),[t,l]=(0,n.useState)(null),r=(0,n.useCallback)(e=>{t?.waiting&&t.waiting.postMessage({type:"SKIP_WAITING"}),L.Ay.destroy(e)},[t]),o=(0,n.useCallback)(e=>{s(!1),L.Ay.destroy(e)},[]),i=(0,n.useCallback)(()=>{let e="pwa-update";L.Ay.info({key:e,message:"App Update Available",description:"A new version of NEXAPO POS is available. Update now for the latest features and improvements.",duration:0,placement:"topRight",btn:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(m.Ay,{type:"primary",size:"small",icon:(0,a.jsx)(D.A,{}),onClick:()=>r(e),children:"Update Now"}),(0,a.jsx)(m.Ay,{size:"small",icon:(0,a.jsx)(A.A,{}),onClick:()=>o(e),children:"Later"})]}),onClose:()=>o(e)})},[o,r]);return(0,n.useEffect)(()=>{},[i]),null};var W=t(97245),z=t(42211),T=t(3971);let X=({children:e})=>{let[s,t]=(0,n.useState)(!1),{setIsOpen:d,isMobile:m}=(0,f.V)(),p=(0,c.wA)(),j=(0,c.d4)(e=>e.auth.user),g=(0,u.useRouter)(),v=(0,c.d4)(e=>e.auth.accessToken),b=(0,u.usePathname)();return((0,T.X)(),l().useEffect(()=>{j&&p(W.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0})).then(e=>{if(e?.data?.success&&e.data.data){let s=e.data.data;v?(console.log("[Sync] Updating Redux state with fresh user and token:",s,v),p((0,z.gV)({user:s,accessToken:v}))):console.warn("[Sync] No access token found in Redux state; not updating user.")}else{let s="";console.log("[Sync] Error message:",s="string"==typeof e?.error?e.error.toLowerCase():e?.error?.message?.toLowerCase?.()||e?.error?.data?.message?.toLowerCase?.()||e?.error?.data?.error?.toLowerCase?.()||""),s.includes("payment required")&&(j&&"paid"===j.paymentStatus&&v?(console.log("[Sync] Setting user paymentStatus to 'pending' with token:",v),p((0,z.gV)({user:{...j,paymentStatus:"pending"},accessToken:v}))):console.warn("[Sync] No access token found in Redux state; not updating user to pending."),console.log("[Sync] Redirecting to /payment"),g.replace("/payment"))}})},[p,j,g,v]),l().useEffect(()=>{j&&"superadmin"!==j.role&&"paid"!==j.paymentStatus&&"/payment"!==b&&(console.log("[Watcher] Payment status changed, redirecting to /payment"),g.replace("/payment"))},[j,g,b]),(0,n.useEffect)(()=>{t(!0),console.log("ProtectedDashboardContent mounted on client",{isMobile:m})},[m]),(0,n.useEffect)(()=>()=>{d(!1),console.log("ProtectedDashboardContent unmounted - closing sidebar")},[d]),s)?(0,a.jsxs)(h.default,{children:[(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)(o.R,{}),(0,a.jsx)("div",{className:"fixed left-0 top-0 h-full z-20 hidden lg:block",children:(0,a.jsx)(r.B,{})}),(0,a.jsxs)("div",{className:"w-full lg:ml-[290px] bg-white flex flex-col h-screen min-w-0",children:[(0,a.jsx)("div",{className:"sticky top-0 z-30 w-full bg-white border-b border-gray-200",children:(0,a.jsx)(i.Y,{})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto min-w-0",children:(0,a.jsxs)("main",{className:"isolate mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10",children:[(0,a.jsx)(x,{}),e]})})]})]}),(0,a.jsx)(R,{}),(0,a.jsx)(M,{})]}):(0,a.jsx)(y.A,{tip:"Loading dashboard..."})}},3971:(e,s,t)=>{"use strict";t.d(s,{X:()=>l});var a=t(58009),n=t(92273);function l(){(0,n.wA)();let e=(0,n.d4)(e=>e.auth.user),s=(0,n.d4)(e=>e.auth.accessToken);(0,a.useRef)(e),(0,a.useRef)(s)}t(42211),t(97245)},18606:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r,metadata:()=>l});var a=t(62740),n=t(3453);let l={title:{template:"%s | Nexapo POS | Inventory Management System",default:"Nexapo POS | Inventory Management System"},description:"Nexapo POS | Inventory Management System"};function r({children:e}){return(0,a.jsx)(n.default,{children:e})}t(37504),t(68207)},3453:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Dashboard\\\\ProtectedDashboardContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\ProtectedDashboardContent.tsx","default")},37504:()=>{},68207:()=>{}};