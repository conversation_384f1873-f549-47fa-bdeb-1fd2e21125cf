"use client";

import React, { useState, useMemo } from "react";
import { Card, Button, Spin, Select, Row, Col, Statistic, Tag, message } from "antd";
import {
  DatabaseOutlined,
  LoadingOutlined,
  DownloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGetAllProductsQuery } from "@/reduxRTK/services/productApi";
import { useGetAllCategoriesQuery } from "@/reduxRTK/services/categoryApi";
import dayjs from "dayjs";

const { Option } = Select;

const InventoryReportsPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // State management
  const [reportType, setReportType] = useState<string>("stock-levels");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [reportGenerated, setReportGenerated] = useState(true); // Auto-generate on load

  // API calls for real data
  const { data: productsData, isLoading: productsLoading, refetch: refetchProducts } = useGetAllProductsQuery({
    page: 1,
    limit: 1000, // Get all products for analysis
    search: ''
  });
  const { data: categoriesData, isLoading: categoriesLoading } = useGetAllCategoriesQuery({
    page: 1,
    limit: 100,
    search: ''
  });

  const isLoading = productsLoading || categoriesLoading;

  // Calculate real inventory statistics
  const { inventoryStats, inventoryData } = useMemo(() => {
    const products = productsData?.data?.products || [];
    const categories = (categoriesData as any)?.data?.categories || [];

    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Products Data:", productsData);
      console.log("🔍 Products Array:", products);
      console.log("🔍 Categories Data:", categoriesData);
      console.log("🔍 Categories Array:", categories);
    }

    // Create category lookup
    const categoryLookup = categories.reduce((acc: { [key: number]: string }, cat: any) => {
      acc[cat.id] = cat.name;
      return acc;
    }, {} as { [key: number]: string });

    // Filter products by category if specified
    const filteredProducts = categoryFilter === "all"
      ? products
      : products.filter(product => {
          const categoryName = categoryLookup[product.categoryId];
          return categoryName?.toLowerCase() === categoryFilter.toLowerCase();
        });

    // Calculate statistics
    const totalProducts = filteredProducts.length;
    const lowStockItems = filteredProducts.filter(product =>
      product.stockQuantity > 0 &&
      product.minStockLevel &&
      product.stockQuantity <= product.minStockLevel
    ).length;
    const outOfStockItems = filteredProducts.filter(product => product.stockQuantity === 0).length;
    const totalValue = filteredProducts.reduce((sum, product) =>
      sum + (product.stockQuantity * parseFloat(product.price)), 0
    );

    // Prepare inventory data for table
    const inventoryTableData = filteredProducts.map(product => {
      const minStock = product.minStockLevel || 5; // Default min stock if not set
      let status = 'In Stock';

      if (product.stockQuantity === 0) {
        status = 'Out of Stock';
      } else if (product.stockQuantity <= minStock) {
        status = 'Low Stock';
      }

      return {
        key: product.id.toString(),
        id: product.id,
        name: product.name,
        category: categoryLookup[product.categoryId] || 'Unknown',
        currentStock: product.stockQuantity,
        minStock: minStock,
        status,
        value: product.stockQuantity * parseFloat(product.price),
        price: parseFloat(product.price),
        sku: product.sku || 'N/A'
      };
    });

    return {
      inventoryStats: {
        totalProducts,
        lowStockItems,
        outOfStockItems,
        totalValue
      },
      inventoryData: inventoryTableData
    };
  }, [productsData, categoriesData, categoryFilter]);



  const handleGenerateReport = () => {
    setReportGenerated(true);
    refetchProducts();
    message.success('Inventory report generated successfully!');
  };

  const handleExportReport = () => {
    if (!reportGenerated) {
      message.warning('Please generate a report first');
      return;
    }

    // Create CSV content
    const csvContent = generateInventoryCSV();

    // Generate professional filename
    const reportTypeNames = {
      'stock-levels': 'Stock-Levels',
      'low-stock': 'Low-Stock-Alert',
      'valuation': 'Inventory-Valuation',
      'movement': 'Stock-Movement'
    };

    const categoryStr = categoryFilter === 'all' ? 'All-Categories' : categoryFilter.replace(/\s+/g, '-');
    const filename = `NEXAPO_${reportTypeNames[reportType as keyof typeof reportTypeNames]}_${categoryStr}_${dayjs().format('YYYY-MM-DD_HH-mm')}.csv`;

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('Professional inventory report exported successfully!');
  };

  const generateInventoryCSV = () => {
    const currentDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const reportTypeNames = {
      'stock-levels': 'Stock Levels Report',
      'low-stock': 'Low Stock Alert Report',
      'valuation': 'Inventory Valuation Report',
      'movement': 'Stock Movement Report'
    };

    const categoryDisplayName = categoryFilter === 'all' ? 'All Categories' :
      (categoriesData as any)?.data?.categories?.find((cat: any) => cat.name.toLowerCase() === categoryFilter.toLowerCase())?.name || categoryFilter;

    // Professional header section
    const headerSection = [
      'NEXAPO INVENTORY REPORT',
      '',
      `Report Type,${reportTypeNames[reportType as keyof typeof reportTypeNames]}`,
      `Category Filter,${categoryDisplayName}`,
      `Generated On,${currentDate}`,
      `Generated By,${(currentUser as any)?.firstName || 'Admin'} ${(currentUser as any)?.lastName || 'User'}`,
      '',
      'REPORT DATA',
      ''
    ];

    // Summary statistics section
    const summarySection = [
      'INVENTORY SUMMARY',
      '',
      'Metric,Value,Description',
      `Total Products,${inventoryStats.totalProducts},Total number of products in inventory`,
      `Low Stock Items,${inventoryStats.lowStockItems},Products below minimum stock level`,
      `Out of Stock Items,${inventoryStats.outOfStockItems},Products with zero stock`,
      `Total Inventory Value,GHS ${inventoryStats.totalValue.toFixed(2)},Total value of all inventory`,
      ''
    ];

    // Detailed inventory data
    const detailSection = [
      'DETAILED INVENTORY DATA',
      '',
      'SKU,Product Name,Category,Current Stock,Min Stock,Unit Price (GHS),Status,Total Value (GHS)',
      ...inventoryData.map(item => [
        item.sku || 'N/A',
        `"${item.name}"`, // Quotes for names with commas
        `"${item.category}"`,
        item.currentStock.toString(),
        item.minStock.toString(),
        `GHS ${item.price.toFixed(2)}`,
        item.status,
        `GHS ${item.value.toFixed(2)}`
      ].join(',')),
      '',
      'SUMMARY STATISTICS',
      `Total Products Analyzed,${inventoryData.length}`,
      `Products In Stock,${inventoryData.filter(item => item.status === 'In Stock').length}`,
      `Products Low Stock,${inventoryData.filter(item => item.status === 'Low Stock').length}`,
      `Products Out of Stock,${inventoryData.filter(item => item.status === 'Out of Stock').length}`,
      `Total Inventory Value,GHS ${inventoryStats.totalValue.toFixed(2)}`
    ];

    return [...headerSection, ...summarySection, ...detailSection].join('\n');
  };

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={
          <div className="flex items-center gap-2">
            <DatabaseOutlined className="text-blue-600" />
            <span className="text-gray-800">Inventory Reports</span>
          </div>
        }
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExportReport}
            size={isMobile ? "small" : "middle"}
            className="bg-green-600 hover:bg-green-700"
          >
            {isMobile ? "" : "Export"}
          </Button>
        }
      >
        <div className="w-full space-y-6">
          {/* Filters Section */}
          <Card
            title="Report Filters"
            size="small"
            className="bg-gray-50"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Report Type</label>
                  <Select
                    value={reportType}
                    onChange={setReportType}
                    className="w-full"
                  >
                    <Option value="stock-levels">Stock Levels</Option>
                    <Option value="low-stock">Low Stock Alert</Option>
                    <Option value="valuation">Inventory Valuation</Option>
                    <Option value="movement">Stock Movement</Option>
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Category</label>
                  <Select
                    value={categoryFilter}
                    onChange={setCategoryFilter}
                    className="w-full"
                  >
                    <Option value="all">All Categories</Option>
                    {(categoriesData as any)?.data?.categories?.map((category: any) => (
                      <Option key={category.id} value={category.name.toLowerCase()}>
                        {category.name}
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={24} md={8}>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">&nbsp;</label>
                  <Button
                    type="primary"
                    onClick={handleGenerateReport}
                    loading={isLoading}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    icon={<DatabaseOutlined />}
                  >
                    Generate Report
                  </Button>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Statistics Cards */}
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Total Products"
                  value={inventoryStats.totalProducts}
                  valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Low Stock Items"
                  value={inventoryStats.lowStockItems}
                  valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
                  suffix={<WarningOutlined />}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Out of Stock"
                  value={inventoryStats.outOfStockItems}
                  valueStyle={{ color: '#f5222d', fontSize: '18px' }}
                  suffix={<WarningOutlined />}
                  className="w-full"
                />
              </Card>
            </Col>
            <Col xs={12} sm={12} md={6}>
              <Card className="text-center h-24 flex items-center justify-center">
                <Statistic
                  title="Total Value"
                  value={inventoryStats.totalValue}
                  precision={2}
                  prefix="₵"
                  valueStyle={{ color: '#3f8600', fontSize: '18px' }}
                  className="w-full"
                />
              </Card>
            </Col>
          </Row>



          {/* Inventory Table */}
          <Card title="Inventory Details">
            {isLoading ? (
              <div className="flex h-60 items-center justify-center">
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{ fontSize: 24, color: "#1890ff" }}
                      spin
                    />
                  }
                />
              </div>
            ) : reportGenerated ? (
              inventoryData && inventoryData.length > 0 ? (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full min-w-[800px]">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">SKU</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Product Name</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Category</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Current Stock</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Min Stock</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Unit Price</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Status</th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b">Total Value</th>
                        </tr>
                      </thead>
                      <tbody>
                        {inventoryData.map((item, index) => (
                          <tr key={item.key} className="border-b hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm text-gray-900">{item.sku}</td>
                            <td className="px-4 py-3 text-sm text-gray-900">{item.name}</td>
                            <td className="px-4 py-3 text-sm text-gray-900">{item.category}</td>
                            <td className={`px-4 py-3 text-sm font-semibold ${
                              item.currentStock === 0 ? 'text-red-600' :
                              item.currentStock <= item.minStock ? 'text-orange-600' :
                              'text-green-600'
                            }`}>
                              {item.currentStock}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900">{item.minStock}</td>
                            <td className="px-4 py-3 text-sm text-gray-900">₵{item.price.toFixed(2)}</td>
                            <td className="px-4 py-3 text-sm">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                item.status === 'Out of Stock' ? 'bg-red-100 text-red-800' :
                                item.status === 'Low Stock' ? 'bg-orange-100 text-orange-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {item.status === 'Out of Stock' && <WarningOutlined className="mr-1" />}
                                {item.status === 'Low Stock' && <WarningOutlined className="mr-1" />}
                                {item.status === 'In Stock' && <CheckCircleOutlined className="mr-1" />}
                                {item.status}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-sm font-semibold text-blue-600">₵{item.value.toFixed(2)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="px-4 py-3 bg-gray-50 border-t text-sm text-gray-600">
                    Showing {inventoryData.length} products
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-10">
                  <DatabaseOutlined className="text-4xl mb-4" />
                  <p>No products found for the selected criteria.</p>
                  <p className="text-sm mt-2">
                    {categoryFilter !== "all"
                      ? "Try selecting 'All Categories' or check if products exist in this category."
                      : "No products have been added to the system yet."
                    }
                  </p>
                </div>
              )
            ) : (
              <div className="text-center text-gray-500 py-20">
                <DatabaseOutlined className="text-4xl mb-4" />
                <p>Click &quot;Generate Report&quot; to view inventory details</p>
                <p className="text-sm mt-2">Product data will be displayed here</p>
              </div>
            )}
          </Card>

          {/* Coming Soon Notice */}
          <Card className="bg-blue-50 border-blue-200">
            <div className="text-center text-blue-700">
              <h3 className="font-semibold mb-2">📦 Advanced Inventory Analytics Coming Soon</h3>
              <p className="text-sm">
                Stock movement tracking, reorder suggestions, and ABC analysis will be available in future updates.
              </p>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default InventoryReportsPage;
