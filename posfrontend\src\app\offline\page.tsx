"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Result, Card, Typography, Space, Alert, Divider } from 'antd';
import {
  WifiOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  DatabaseOutlined,
  SyncOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import { offlineStorage } from '@/utils/offlineStorage';

const { Title, Paragraph, Text } = Typography;

const OfflinePage: React.FC = () => {
  const [offlineData, setOfflineData] = useState({
    products: 0,
    pendingSales: 0,
    lastSync: null as Date | null
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadOfflineData = async () => {
      try {
        const products = await offlineStorage.getCachedProducts();
        const pendingSales = await offlineStorage.getOfflineSales('pending');

        setOfflineData({
          products: products.length,
          pendingSales: pendingSales.length,
          lastSync: products.length > 0 ? products[0].lastUpdated : null
        });
      } catch (error) {
        console.error('Failed to load offline data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadOfflineData();
  }, []);

  const handleRetry = () => {
    window.location.reload();
  };

  const getDataFreshness = () => {
    if (!offlineData.lastSync) return 'No cached data';

    const now = new Date();
    const diffHours = Math.floor((now.getTime() - offlineData.lastSync.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) return 'Very fresh (< 1 hour)';
    if (diffHours < 24) return `Fresh (${diffHours} hours ago)`;
    return `${Math.floor(diffHours / 24)} days ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full text-center shadow-lg">
        <Result
          icon={<WifiOutlined className="text-gray-400" style={{ fontSize: '72px' }} />}
          title={
            <Title level={2} className="text-gray-800">
              You&apos;re Offline
            </Title>
          }
          subTitle={
            <div className="space-y-4">
              <Paragraph className="text-gray-600">
                No internet connection detected. Don&apos;t worry - NEXAPO POS works great offline!
              </Paragraph>

              {/* Offline Capabilities */}
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <Title level={4} className="text-green-700 mb-3 flex items-center justify-center">
                  <CheckCircleOutlined className="mr-2" />
                  Available Offline
                </Title>
                <div className="grid grid-cols-2 gap-2 text-left text-green-600">
                  <div>• Make sales</div>
                  <div>• Generate receipts</div>
                  <div>• View products</div>
                  <div>• Search inventory</div>
                  <div>• Barcode scanning</div>
                  <div>• Sales history</div>
                </div>
              </div>

              {/* Offline Data Status */}
              {!isLoading && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <Title level={5} className="text-blue-700 mb-3 flex items-center justify-center">
                    <DatabaseOutlined className="mr-2" />
                    Cached Data Status
                  </Title>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Text>Products cached:</Text>
                      <Text strong className="text-blue-600">{offlineData.products}</Text>
                    </div>
                    <div className="flex justify-between items-center">
                      <Text>Pending sales:</Text>
                      <Text strong className="text-orange-600">{offlineData.pendingSales}</Text>
                    </div>
                    <div className="flex justify-between items-center">
                      <Text>Data freshness:</Text>
                      <Text strong className="text-blue-600">{getDataFreshness()}</Text>
                    </div>
                  </div>
                </div>
              )}

              {/* Sync Alert */}
              {offlineData.pendingSales > 0 && (
                <Alert
                  message="Pending Sales"
                  description={`You have ${offlineData.pendingSales} sales waiting to sync when connection returns.`}
                  type="warning"
                  showIcon
                  icon={<SyncOutlined />}
                />
              )}
            </div>
          }
          extra={
            <Space direction="vertical" className="w-full" size="middle">
              <Link href="/dashboard/sales/offline-pos">
                <Button
                  type="primary"
                  icon={<ShoppingCartOutlined />}
                  size="large"
                  className="w-full bg-green-600 hover:bg-green-700 border-green-600"
                >
                  Continue with Offline POS
                </Button>
              </Link>

              <Divider className="my-2">or</Divider>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRetry}
                  size="large"
                  className="w-full"
                >
                  Retry Connection
                </Button>

                <Link href="/dashboard">
                  <Button size="large" className="w-full">
                    Go to Dashboard
                  </Button>
                </Link>
              </div>

              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <Text type="secondary" className="text-xs">
                  💡 Your offline sales will automatically sync when internet connection is restored
                </Text>
              </div>
            </Space>
          }
        />
      </Card>
    </div>
  );
};

export default OfflinePage;
