// Comprehensive Offline Sync Manager for ALL NEXAPO POS Features
import { comprehensiveOfflineStorage, OfflineAction } from './comprehensiveOfflineStorage';
import { showMessage } from './showMessage';

interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: string[];
}

class ComprehensiveOfflineSyncManager {
  private syncInProgress = false;
  private syncQueue: OfflineAction[] = [];
  private readonly API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

  constructor() {
    // Auto-sync when online
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.autoSync();
      });

      // Periodic sync every 5 minutes when online
      setInterval(() => {
        if (navigator.onLine && !this.syncInProgress) {
          this.autoSync();
        }
      }, 5 * 60 * 1000);
    }
  }

  private async autoSync(): Promise<void> {
    if (!navigator.onLine || this.syncInProgress) return;
    
    try {
      showMessage('warning', 'Working offline - data will sync when connection returns');
      await this.syncAllPendingActions();
    } catch (error) {
      console.error('Auto-sync failed:', error);
    }
  }

  async syncAllPendingActions(): Promise<SyncResult> {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    if (!navigator.onLine) {
      throw new Error('No internet connection');
    }

    this.syncInProgress = true;
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      failedCount: 0,
      errors: []
    };

    try {
      // Get all pending actions sorted by priority
      const pendingActions = await comprehensiveOfflineStorage.getPendingActions();
      this.syncQueue = pendingActions.sort((a, b) => a.priority - b.priority);

      console.log(`🔄 Starting sync of ${this.syncQueue.length} pending actions`);

      for (const action of this.syncQueue) {
        try {
          await this.syncSingleAction(action);
          result.syncedCount++;
        } catch (error) {
          console.error(`Failed to sync action ${action.id}:`, error);
          result.failedCount++;
          result.errors.push(`${action.type} ${action.entity}: ${error}`);
          
          // Update action status to failed
          await comprehensiveOfflineStorage.updateActionStatus(
            action.id, 
            'failed', 
            error instanceof Error ? error.message : String(error)
          );
        }
      }

      if (result.failedCount === 0) {
        showMessage('success', `Successfully synced ${result.syncedCount} items`);
      } else {
        showMessage('warning', `Synced ${result.syncedCount} items, ${result.failedCount} failed`);
        result.success = false;
      }

    } catch (error) {
      console.error('Sync process failed:', error);
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : String(error));
      showMessage('error', 'Sync failed. Will retry automatically.');
    } finally {
      this.syncInProgress = false;
    }

    return result;
  }

  private async syncSingleAction(action: OfflineAction): Promise<void> {
    // Update action status to syncing
    await comprehensiveOfflineStorage.updateActionStatus(action.id, 'syncing');

    const endpoint = this.getEndpointForEntity(action.entity, action.type);
    const method = this.getHttpMethod(action.type);
    
    const response = await this.makeApiCall(endpoint, method, action.data);

    if (response.success) {
      // Mark action as synced
      await comprehensiveOfflineStorage.updateActionStatus(action.id, 'synced');
      
      // Remove the action from offline storage
      await comprehensiveOfflineStorage.deleteEntity('offlineActions', action.id);
      
      console.log(`✅ Synced ${action.type} ${action.entity} (${action.id})`);
    } else {
      throw new Error(response.message || 'Sync failed');
    }
  }

  private getEndpointForEntity(entity: string, actionType: string): string {
    const baseEndpoints: { [key: string]: string } = {
      'product': '/products',
      'category': '/categories',
      'supplier': '/suppliers',
      'customer': '/customers',
      'user': '/users',
      'store': '/stores',
      'sale': '/sales',
      'purchase': '/purchases',
      'expense': '/expenses',
      'expenseCategory': '/expense-categories',
      'stockAdjustment': '/stock-adjustments',
      'receipt': '/receipts'
    };

    const baseEndpoint = baseEndpoints[entity];
    if (!baseEndpoint) {
      throw new Error(`Unknown entity: ${entity}`);
    }

    return `${this.API_BASE_URL}${baseEndpoint}`;
  }

  private getHttpMethod(actionType: string): string {
    switch (actionType) {
      case 'CREATE': return 'POST';
      case 'UPDATE': return 'PUT';
      case 'DELETE': return 'DELETE';
      default: throw new Error(`Unknown action type: ${actionType}`);
    }
  }

  private async makeApiCall(endpoint: string, method: string, data: any): Promise<any> {
    const token = localStorage.getItem('accessToken');
    
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    };

    if (method !== 'DELETE' && data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  // Specific sync methods for different entities
  async syncOfflineEntity(
    entity: string, 
    actionType: 'CREATE' | 'UPDATE' | 'DELETE', 
    data: any, 
    priority: number = 5
  ): Promise<string> {
    return await comprehensiveOfflineStorage.addOfflineAction({
      type: actionType,
      entity,
      entityId: data.id || Date.now(),
      data,
      priority,
      status: 'pending'
    });
  }

  // Convenience methods for common operations
  async createOfflineSale(saleData: any): Promise<string> {
    return await this.syncOfflineEntity('sale', 'CREATE', saleData, 1); // High priority
  }

  async createOfflineExpense(expenseData: any): Promise<string> {
    return await this.syncOfflineEntity('expense', 'CREATE', expenseData, 2);
  }

  async createOfflinePurchase(purchaseData: any): Promise<string> {
    return await this.syncOfflineEntity('purchase', 'CREATE', purchaseData, 2);
  }

  async updateOfflineProduct(productData: any): Promise<string> {
    return await this.syncOfflineEntity('product', 'UPDATE', productData, 3);
  }

  async createOfflineStockAdjustment(adjustmentData: any): Promise<string> {
    return await this.syncOfflineEntity('stockAdjustment', 'CREATE', adjustmentData, 2);
  }

  async deleteOfflineEntity(entity: string, entityId: string | number): Promise<string> {
    return await this.syncOfflineEntity(entity, 'DELETE', { id: entityId }, 4);
  }

  // Data caching methods
  async cacheAllData(): Promise<void> {
    if (!navigator.onLine) {
      console.log('⚠️ Cannot cache data while offline');
      return;
    }

    try {
      console.log('📥 Starting comprehensive data caching...');

      // Cache all entities in parallel
      await Promise.all([
        this.cacheProducts(),
        this.cacheCategories(),
        this.cacheSuppliers(),
        this.cacheCustomers(),
        this.cacheUsers(),
        this.cacheStores(),
        this.cacheExpenseCategories(),
        this.cacheSettings()
      ]);

      console.log('✅ All data cached successfully');
      showMessage('success', 'Data cached for offline use');
    } catch (error) {
      console.error('❌ Failed to cache data:', error);
      showMessage('error', 'Failed to cache data for offline use');
    }
  }

  private async cacheProducts(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/products`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('products', response.data);
    }
  }

  private async cacheCategories(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/categories`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('categories', response.data);
    }
  }

  private async cacheSuppliers(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/suppliers`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('suppliers', response.data);
    }
  }

  private async cacheCustomers(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/customers`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('customers', response.data);
    }
  }

  private async cacheUsers(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/users`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('users', response.data);
    }
  }

  private async cacheStores(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/stores`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('stores', response.data);
    }
  }

  private async cacheExpenseCategories(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/expense-categories`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('expenseCategories', response.data);
    }
  }

  private async cacheSettings(): Promise<void> {
    const response = await this.makeApiCall(`${this.API_BASE_URL}/settings`, 'GET', null);
    if (response.success && response.data) {
      await comprehensiveOfflineStorage.bulkSaveEntities('settings', response.data);
    }
  }

  // Force sync method for manual triggers
  async forceSyncNow(): Promise<SyncResult> {
    console.log('🔄 Force sync triggered');
    showMessage('success', 'Starting manual sync...');
    return await this.syncAllPendingActions();
  }

  // Get sync status
  async getSyncStatus(): Promise<{
    pendingActions: number;
    lastSyncAttempt?: Date;
    isOnline: boolean;
    isSyncing: boolean;
  }> {
    const stats = await comprehensiveOfflineStorage.getStorageStats();
    
    return {
      pendingActions: stats.pendingActions || 0,
      lastSyncAttempt: undefined, // Could be stored in settings
      isOnline: navigator.onLine,
      isSyncing: this.syncInProgress
    };
  }

  // Clear all offline data
  async clearAllOfflineData(): Promise<void> {
    // Implementation would clear all stores
    console.log('🗑️ Clearing all offline data...');
    // This would need to be implemented in comprehensiveOfflineStorage
  }
}

// Create singleton instance
export const comprehensiveOfflineSync = new ComprehensiveOfflineSyncManager();

export default comprehensiveOfflineSync;
