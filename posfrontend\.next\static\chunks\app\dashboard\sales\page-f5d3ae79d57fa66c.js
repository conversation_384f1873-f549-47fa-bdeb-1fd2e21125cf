(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2562],{18053:(e,t,a)=>{Promise.resolve().then(a.bind(a,47090))},81910:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var l=a(85407),r=a(12115);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};var n=a(84021);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,l.A)({},e,{ref:t,icon:s}))})},40794:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var l=a(85407),r=a(12115);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var n=a(84021);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,l.A)({},e,{ref:t,icon:s}))})},45100:(e,t,a)=>{"use strict";a.d(t,{A:()=>B});var l=a(12115),r=a(4617),s=a.n(r),n=a(70527),o=a(28673),i=a(64766),c=a(58292),d=a(71054),x=a(31049),g=a(67548),m=a(10815),h=a(70695),u=a(56204),A=a(1086);let p=e=>{let{paddingXXS:t,lineWidth:a,tagPaddingHorizontal:l,componentCls:r,calc:s}=e,n=s(l).sub(a).equal(),o=s(t).sub(a).equal();return{[r]:Object.assign(Object.assign({},(0,h.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:n,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,g.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(r,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(r,"-close-icon")]:{marginInlineStart:o,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(r,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(r,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:n}}),["".concat(r,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:a,calc:l}=e,r=e.fontSizeSM;return(0,u.oX)(e,{tagFontSize:r,tagLineHeight:(0,g.zA)(l(e.lineHeightSM).mul(r).equal()),tagIconSize:l(a).sub(l(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),f=(0,A.OF)("Tag",e=>p(y(e)),b);var j=function(e,t){var a={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(a[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(a[l[r]]=e[l[r]]);return a};let v=l.forwardRef((e,t)=>{let{prefixCls:a,style:r,className:n,checked:o,onChange:i,onClick:c}=e,d=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:m}=l.useContext(x.QO),h=g("tag",a),[u,A,p]=f(h),y=s()(h,"".concat(h,"-checkable"),{["".concat(h,"-checkable-checked")]:o},null==m?void 0:m.className,n,A,p);return u(l.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==m?void 0:m.style),className:y,onClick:e=>{null==i||i(!o),null==c||c(e)}})))});var N=a(46258);let w=e=>(0,N.A)(e,(t,a)=>{let{textColor:l,lightBorderColor:r,lightColor:s,darkColor:n}=a;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:l,background:s,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),E=(0,A.bf)(["Tag","preset"],e=>w(y(e)),b),C=(e,t,a)=>{let l=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(a);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(a)],background:e["color".concat(l,"Bg")],borderColor:e["color".concat(l,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},Q=(0,A.bf)(["Tag","status"],e=>{let t=y(e);return[C(t,"success","Success"),C(t,"processing","Info"),C(t,"error","Error"),C(t,"warning","Warning")]},b);var k=function(e,t){var a={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(a[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(a[l[r]]=e[l[r]]);return a};let S=l.forwardRef((e,t)=>{let{prefixCls:a,className:r,rootClassName:g,style:m,children:h,icon:u,color:A,onClose:p,bordered:y=!0,visible:b}=e,j=k(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:v,direction:N,tag:w}=l.useContext(x.QO),[C,S]=l.useState(!0),B=(0,n.A)(j,["closeIcon","closable"]);l.useEffect(()=>{void 0!==b&&S(b)},[b]);let I=(0,o.nP)(A),z=(0,o.ZZ)(A),M=I||z,D=Object.assign(Object.assign({backgroundColor:A&&!M?A:void 0},null==w?void 0:w.style),m),J=v("tag",a),[P,T,F]=f(J),H=s()(J,null==w?void 0:w.className,{["".concat(J,"-").concat(A)]:M,["".concat(J,"-has-color")]:A&&!M,["".concat(J,"-hidden")]:!C,["".concat(J,"-rtl")]:"rtl"===N,["".concat(J,"-borderless")]:!y},r,g,T,F),R=e=>{e.stopPropagation(),null==p||p(e),e.defaultPrevented||S(!1)},[,Y]=(0,i.A)((0,i.d)(e),(0,i.d)(w),{closable:!1,closeIconRender:e=>{let t=l.createElement("span",{className:"".concat(J,"-close-icon"),onClick:R},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var a;null===(a=null==e?void 0:e.onClick)||void 0===a||a.call(e,t),R(t)},className:s()(null==e?void 0:e.className,"".concat(J,"-close-icon"))}))}}),K="function"==typeof j.onClick||h&&"a"===h.type,O=u||null,U=O?l.createElement(l.Fragment,null,O,h&&l.createElement("span",null,h)):h,L=l.createElement("span",Object.assign({},B,{ref:t,className:H,style:D}),U,Y,I&&l.createElement(E,{key:"preset",prefixCls:J}),z&&l.createElement(Q,{key:"status",prefixCls:J}));return P(K?l.createElement(d.A,{component:"Tag"},L):L)});S.CheckableTag=v;let B=S},47090:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>X});var l=a(95155),r=a(12115),s=a(71349),n=a(43316),o=a(72093),i=a(5099),c=a(16419),d=a(39279),x=a(5413),g=a(36060),m=a(30555),h=a(21633);let u=()=>{var e,t,a,l,s;let[n,o]=(0,r.useState)(1),[i,c]=(0,r.useState)(10),[d,x]=(0,r.useState)(""),[g,m]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=setTimeout(()=>{m(d)},500);return()=>{clearTimeout(e)}},[d]);let{data:u,isLoading:A,isFetching:p,refetch:y}=(0,h.JZ)({page:n,limit:i,search:g},{refetchOnMountOrArgChange:!0});if(console.log("Sales data from API:",u),console.log("Total sales:",(null==u?void 0:null===(e=u.data)||void 0===e?void 0:e.total)||0),console.log("Sales array:",(null==u?void 0:null===(t=u.data)||void 0===t?void 0:t.sales)||[]),(null==u?void 0:null===(a=u.data)||void 0===a?void 0:a.sales)&&u.data.sales.length>0){let e=u.data.sales[0];console.log("First sale:",e),console.log("First sale has items?","items"in e),"items"in e&&console.log("First sale items:",e.items)}return{sales:(null==u?void 0:null===(l=u.data)||void 0===l?void 0:l.sales)||[],total:(null==u?void 0:null===(s=u.data)||void 0===s?void 0:s.total)||0,page:n,limit:i,isLoading:A||p,refetch:y,searchTerm:d,setSearchTerm:x,handlePageChange:(e,t)=>{o(e),t&&c(t)}}};var A=a(75912);let p=e=>{let[t,{isLoading:a}]=(0,h.lz)();return{deleteSale:async a=>{try{let l=await t(a).unwrap();if(l.success)return(0,A.r)("success","Sale deleted successfully"),e&&e(),!0;return(0,A.r)("error",l.message||"Failed to delete sale"),!1}catch(e){var l;return(0,A.r)("error",(null===(l=e.data)||void 0===l?void 0:l.message)||"An error occurred while deleting the sale"),!1}},isDeleting:a}},y=e=>{let[t,{isLoading:a}]=(0,h.Kn)();return{bulkDeleteSales:async a=>{try{console.log("Bulk deleting sales with IDs:",a);let l=await t(a).unwrap();if(!l.success)throw Error(l.message||"Failed to delete sales");return(0,A.r)("success","".concat(a.length," sales deleted successfully")),e&&e(),l.data}catch(e){throw console.error("Bulk delete sales error:",e),(0,A.r)("error",e.message||"Failed to delete sales"),e}},isDeleting:a}};var b=a(80766),f=a(92895),j=a(6457),v=a(17084),N=a(40794),w=a(87181),E=a(80519),C=a(27656),Q=a(60102),k=a(91256),S=a(83391),B=a(21455),I=a.n(B);a(23411);let z=e=>{let{sales:t,loading:a,onViewSale:s,onDelete:o,onBulkDelete:c,isMobile:d=!1}=e,x=(0,k.E)(),{user:g}=(0,S.d4)(e=>e.auth),[m,h]=(0,r.useState)([]),[u,A]=(0,r.useState)(!1),p=(null==g?void 0:g.role)==="admin"||(null==g?void 0:g.role)==="superadmin",y=e=>{let a=e.target.checked;A(a),a?h(p?t.map(e=>e.id):[]):h([])},B=(e,t)=>{t?h(t=>[...t,e]):h(t=>t.filter(t=>t!==e))},z=e=>new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS"}).format(parseFloat(e)),M=e=>I()(e).format("MMM D, YYYY HH:mm"),D=e=>{o(e)},J=t.length>0;return(0,l.jsxs)("div",{className:"overflow-hidden bg-white",children:[m.length>0&&p&&(0,l.jsxs)("div",{className:"flex items-center justify-between border-b bg-gray-100 p-2",children:[(0,l.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[m.length," ",1===m.length?"sale":"sales"," selected"]}),(0,l.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,l.jsx)(v.A,{}),onClick:()=>{m.length>0&&c?(c(m),h([]),A(!1)):b.Ay.warning({message:"No sales selected",description:"Please select at least one sale to delete."})},className:"ml-2",children:"Delete Selected"})]}),!J&&t.length>0&&(0,l.jsxs)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:[(0,l.jsx)("p",{children:'You don"t have access to view these sales.'}),(0,l.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"You can only view sales created by you or your team."})]}),J&&(d||x?(0,l.jsxs)(Q.jB,{columns:p?"50px 80px 120px 120px 120px 150px":"80px 120px 120px 120px 150px",minWidth:p?"700px":"650px",children:[p&&(0,l.jsx)(Q.A0,{className:"text-center",children:(0,l.jsx)(f.A,{checked:u,onChange:y,disabled:0===t.length})}),(0,l.jsx)(Q.A0,{children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(i.A,{className:"mr-1"}),"ID"]})}),(0,l.jsx)(Q.A0,{children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(N.A,{className:"mr-1"}),"Amount"]})}),(0,l.jsx)(Q.A0,{children:"Payment"}),(0,l.jsx)(Q.A0,{children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(w.A,{className:"mr-1"}),"Date"]})}),(0,l.jsx)(Q.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,l.jsxs)(Q.Hj,{selected:m.includes(e.id),children:[p&&(0,l.jsx)(Q.nA,{className:"text-center",children:(0,l.jsx)(f.A,{checked:m.includes(e.id),onChange:t=>B(e.id,t.target.checked)})}),(0,l.jsx)(Q.nA,{children:(0,l.jsxs)("span",{className:"font-medium",children:["#",e.id]})}),(0,l.jsx)(Q.nA,{children:(0,l.jsx)("span",{className:"font-medium text-green-600",children:z(e.totalAmount)})}),(0,l.jsx)(Q.nA,{children:(0,l.jsx)("span",{className:"inline-flex rounded-full px-2 text-xs font-semibold leading-5 ".concat("cash"===e.paymentMethod?"bg-green-800 text-green-100":"card"===e.paymentMethod?"bg-blue-800 text-blue-100":"bg-purple-800 text-purple-100"),children:e.paymentMethod.replace("_"," ").toUpperCase()})}),(0,l.jsx)(Q.nA,{children:(0,l.jsx)("span",{className:"text-sm",children:I()(e.transactionDate).format("MMM D, YYYY")})}),(0,l.jsx)(Q.nA,{className:"text-right",children:(0,l.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,l.jsx)(j.A,{title:"View Details",children:(0,l.jsx)(n.Ay,{icon:(0,l.jsx)(E.A,{}),onClick:()=>s(e),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),((null==g?void 0:g.role)==="admin"||(null==g?void 0:g.role)==="superadmin")&&(0,l.jsx)(j.A,{title:"Delete",children:(0,l.jsx)(n.Ay,{icon:(0,l.jsx)(C.A,{}),onClick:()=>D(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,l.jsx)("thead",{className:"bg-gray-50",children:(0,l.jsxs)("tr",{children:[p&&(0,l.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,l.jsx)(f.A,{checked:u,onChange:y,disabled:0===t.length})}),(0,l.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(i.A,{className:"mr-1"}),"ID"]})}),(0,l.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(N.A,{className:"mr-1"}),"Total Amount"]})}),(0,l.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(N.A,{className:"mr-1"}),"Payment Method"]})}),(0,l.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)(w.A,{className:"mr-1"}),"Date"]})}),(0,l.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700",children:"Actions"})]})}),(0,l.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:t.map(e=>(0,l.jsxs)("tr",{className:m.includes(e.id)?"bg-blue-50":"",children:[p&&(0,l.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-center",children:(0,l.jsx)(f.A,{checked:m.includes(e.id),onChange:t=>B(e.id,t.target.checked)})}),(0,l.jsxs)("td",{className:"sticky left-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-gray-800",children:["#",e.id]}),(0,l.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-gray-800",children:z(e.totalAmount)}),(0,l.jsx)("td",{className:"whitespace-nowrap px-3 py-4",children:(0,l.jsx)("span",{className:"inline-flex rounded-full px-2 text-xs font-semibold leading-5 ".concat("cash"===e.paymentMethod?"bg-green-800 text-green-100":"card"===e.paymentMethod?"bg-blue-800 text-blue-100":"bg-purple-800 text-purple-100"),children:e.paymentMethod.replace("_"," ").toUpperCase()})}),(0,l.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-gray-800",children:M(e.transactionDate)}),(0,l.jsx)("td",{className:"sticky right-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-right text-sm font-medium",children:(0,l.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,l.jsx)(j.A,{title:"View Details",children:(0,l.jsx)(n.Ay,{icon:(0,l.jsx)(E.A,{}),onClick:()=>s(e),type:"text",className:"view-button text-green-500",size:"middle"})}),((null==g?void 0:g.role)==="admin"||(null==g?void 0:g.role)==="superadmin")&&(0,l.jsx)(j.A,{title:"Delete",children:(0,l.jsx)(n.Ay,{icon:(0,l.jsx)(C.A,{}),onClick:()=>D(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})}))]})};var M=a(33621),D=a(44549);let J=e=>{let{current:t,pageSize:a,total:r,onChange:s,isMobile:n=!1}=e,o=Math.ceil(r/a);return(0,l.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,l.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,l.jsx)("div",{children:(0,l.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,l.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*a+1})," to"," ",(0,l.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*a,r)})," of"," ",(0,l.jsx)("span",{className:"font-medium text-gray-900",children:r})," results"]})}),(0,l.jsx)("div",{children:(0,l.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,l.jsxs)("button",{onClick:()=>s(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,l.jsx)("span",{className:"sr-only",children:"Previous"}),(0,l.jsx)(M.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,o)},(e,a)=>{let r=a+1;return(0,l.jsx)("button",{onClick:()=>s(r),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===r?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:r},r)}),(0,l.jsxs)("button",{onClick:()=>s(t+1),disabled:t>=o,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=o?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,l.jsx)("span",{className:"sr-only",children:"Next"}),(0,l.jsx)(D.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,l.jsx)("button",{onClick:()=>s(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,l.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",o]}),(0,l.jsx)("button",{onClick:()=>s(t+1),disabled:t>=o,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=o?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})};var P=a(9124),T=a(67649),F=a(45100),H=a(7974),R=a(53096),Y=a(81910),K=a(55750),O=a(53452),U=a(24988);let L=e=>{var t,a;let{sale:s,isOpen:x,onClose:g}=e,{data:m,isLoading:u}=(0,h.Vk)((null==s?void 0:s.id)||0,{skip:!s||!x}),A=r.useMemo(()=>{var e,t,a;return(null==m?void 0:null===(e=m.data)||void 0===e?void 0:e.saleItems)&&Array.isArray(m.data.saleItems)&&m.data.saleItems.length>0?m.data.saleItems:(null==m?void 0:null===(a=m.data)||void 0===a?void 0:null===(t=a.sale)||void 0===t?void 0:t.items)&&Array.isArray(m.data.sale.items)&&m.data.sale.items.length>0?m.data.sale.items:[]},[m]),p=e=>{var t,a,l,r,n;if("createdByName"===e){if(null==m?void 0:null===(l=m.data)||void 0===l?void 0:null===(a=l.sale)||void 0===a?void 0:a.createdByName)return m.data.sale.createdByName;if(null==s?void 0:s.createdByName)return s.createdByName;let e=(null==m?void 0:null===(n=m.data)||void 0===n?void 0:null===(r=n.sale)||void 0===r?void 0:r.createdBy)||(null==s?void 0:s.createdBy);return e?"User ID: ".concat(e):null}return"items"===e?A:(null==m?void 0:null===(t=m.data)||void 0===t?void 0:t.sale)&&e in m.data.sale?m.data.sale[e]:s&&e in s?s[e]:null},y=(0,l.jsx)("div",{className:"flex justify-end space-x-2",children:(0,l.jsx)(n.Ay,{onClick:g,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"})});return(0,l.jsx)(U.A,{title:"Sale Details",isOpen:x,onClose:g,width:"500px",footer:y,children:u?(0,l.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,l.jsx)(o.A,{indicator:(0,l.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(null==m?void 0:m.data)?(0,l.jsxs)("div",{className:"p-4 sales-form",children:[(0,l.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,l.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,l.jsx)(i.A,{className:"mr-2"}),"Sale #",(null==m?void 0:null===(a=m.data)||void 0===a?void 0:null===(t=a.sale)||void 0===t?void 0:t.id)||(null==s?void 0:s.id)||"N/A"]}),(0,l.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete sale information and details"})]}),(0,l.jsxs)(T.A,{bordered:!0,column:1,className:"sale-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(i.A,{})," Sale ID"]}),children:String(p("id"))||"N/A"}),(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(N.A,{})," Total Amount"]}),children:(e=>{if(null==e)return"GHS 0.00";let t="string"==typeof e?parseFloat(e):e;return"GHS ".concat(t.toFixed(2))})("string"==typeof p("totalAmount")||"number"==typeof p("totalAmount")?String(p("totalAmount")):"0")}),(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(Y.A,{})," Payment Method"]}),children:p("paymentMethod")?(0,l.jsx)(F.A,{color:(e=>{switch(e){case"cash":return"green";case"card":return"blue";case"mobile_money":return"purple";default:return"default"}})(p("paymentMethod")),children:(e=>(null==e?void 0:e.replace("_"," ").toUpperCase())||"N/A")(p("paymentMethod"))}):"N/A"}),(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(w.A,{})," Transaction Date"]}),children:(e=>e?I()(e).format("MMM D, YYYY HH:mm"):"N/A")(p("transactionDate")||"")}),(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(K.A,{})," Created By"]}),children:String(p("createdByName"))||"N/A"}),(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(d.A,{})," Store"]}),children:String(p("storeName")||"N/A")}),p("receiptUrl")&&(0,l.jsx)(T.A.Item,{label:(0,l.jsxs)("span",{children:[(0,l.jsx)(O.A,{})," Receipt"]}),children:(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)(H.A,{src:p("receiptUrl"),alt:"Receipt",width:200,className:"border border-gray-700 rounded-md",fallback:"data:image/png;base64,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"}),(0,l.jsx)(n.Ay,{type:"link",href:p("receiptUrl"),target:"_blank",className:"mt-2",children:"View Full Receipt"})]})})]}),(0,l.jsxs)("div",{className:"mt-8 bg-white p-4 rounded-lg border border-gray-200",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold mb-4 text-gray-800 flex items-center",children:[(0,l.jsx)(i.A,{className:"mr-2"})," Sale Items"]}),(0,l.jsx)("div",{className:"overflow-x-auto rounded-md border border-gray-200 shadow-lg",children:(0,l.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,l.jsx)("thead",{className:"bg-gray-50",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider",children:"PRODUCT"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider w-24",children:"QUANTITY"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32",children:"PRICE"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32",children:"SUBTOT"})]})}),(0,l.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.length>0?A.map((e,t)=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800",children:e.productName||"Unknown Product"}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-center",children:e.quantity||0}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right",children:["GHS ",(e.price?parseFloat(e.price):0).toFixed(2)]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right",children:["GHS ",((e.price?parseFloat(e.price):0)*(e.quantity||0)).toFixed(2)]})]},"sale-item-".concat(t))):(0,l.jsx)("tr",{children:(0,l.jsx)("td",{colSpan:4,className:"px-6 py-8 text-center text-sm text-gray-500",children:"No items found for this sale"})})}),(0,l.jsx)("tfoot",{className:"bg-gray-100",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800",children:"Total"}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-center",children:A.length>0?A.reduce((e,t)=>e+(t.quantity||0),0):0}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-right"}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600 text-right",children:["GHS ",A.length>0?A.reduce((e,t)=>e+(t.price?parseFloat(t.price):0)*(t.quantity||0),0).toFixed(2):("string"==typeof p("totalAmount")?parseFloat(p("totalAmount")):parseFloat(String(p("totalAmount")||"0"))).toFixed(2)]})]})})]})})]})]}):(0,l.jsx)("div",{className:"p-4 bg-white h-full flex items-center justify-center",children:(0,l.jsx)(R.A,{description:"Sale details not found",image:R.A.PRESENTED_IMAGE_SIMPLE})})})};var G=a(41657);a(66202);let Z=e=>{let{searchTerm:t,setSearchTerm:a,isMobile:r=!1}=e;return(0,l.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,l.jsx)(G.A,{placeholder:"Search sales...",prefix:(0,l.jsx)(x.A,{className:"text-gray-500"}),value:t,onChange:e=>{a(e.target.value)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:r?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,l.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),t&&(0,l.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',t,'"']})]})};var q=a(12467),W=a(93968);let X=()=>{var e,t,a;let{user:h}=(0,g.A)(),A=(0,m.a)(),[b,f]=(0,r.useState)(!1),[j,v]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[E,C]=(0,r.useState)(null),[Q,k]=(0,r.useState)(!1),{sales:S,total:B,page:I,limit:M,isLoading:D,refetch:T,searchTerm:F,setSearchTerm:H,handlePageChange:R}=u(),{deleteSale:Y,isDeleting:K}=p(()=>{k(!1),T()}),{bulkDeleteSales:O,isDeleting:U}=y(()=>{X(!1),T()}),[G,X]=(0,r.useState)(!1),[V,_]=(0,r.useState)([]),{data:$,isLoading:ee}=(0,W.r3)({page:1,limit:1}),et=(null!==(a=null==$?void 0:null===(t=$.data)||void 0===t?void 0:null===(e=t.products)||void 0===e?void 0:e.length)&&void 0!==a?a:0)>0,ea=async()=>{E&&await Y(E)},el=async()=>{if(console.log("confirmBulkDelete called with sales:",V),V.length>0)try{await O(V)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,l.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,l.jsx)(s.A,{title:(0,l.jsx)("span",{className:"text-gray-800",children:"Sales Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:A?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:et&&(0,l.jsx)(n.Ay,{type:"primary",icon:(0,l.jsx)(i.A,{}),onClick:()=>{f(!0)},size:A?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:A?"":"New Sale"}),children:(0,l.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,l.jsx)(Z,{searchTerm:F,setSearchTerm:H,isMobile:A}),D?(0,l.jsx)("div",{className:"flex h-60 items-center justify-center bg-gray-50",children:(0,l.jsx)(o.A,{indicator:(0,l.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,l.jsx)(l.Fragment,{children:et?(0,l.jsxs)(l.Fragment,{children:[S.length>0?(0,l.jsx)(z,{sales:S,loading:!1,onViewSale:e=>{w(e),v(!0)},onDelete:e=>{C(e),k(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with saleIds:",e),_(e),X(!0)},isMobile:A}):(0,l.jsx)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:F?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("p",{children:"No sales found matching your search criteria."}),(0,l.jsx)(n.Ay,{type:"primary",onClick:()=>H(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",icon:(0,l.jsx)(x.A,{}),children:"Clear Search"})]}):(0,l.jsx)("p",{children:'No sales found. Click "New Sale" to create one.'})}),S.length>0&&(0,l.jsx)(J,{current:I,pageSize:M,total:B,onChange:R,isMobile:A})]}):(0,l.jsxs)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:[(0,l.jsx)(d.A,{className:"text-4xl mb-4"}),(0,l.jsx)("p",{children:"No products found. Please add products to start selling."})]})})]})}),(0,l.jsx)(P.A,{isOpen:b,onClose:()=>f(!1),onSuccess:()=>{f(!1),T()}}),(0,l.jsx)(L,{sale:N,isOpen:j,onClose:()=>{v(!1),w(null)}}),(0,l.jsx)(q.A,{isOpen:Q,onClose:()=>{k(!1),C(null)},onConfirm:ea,title:"Delete Sale",message:"Are you sure you want to delete this sale? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:K,type:"danger"}),(0,l.jsx)(q.A,{isOpen:G,onClose:()=>{X(!1),_([])},onConfirm:el,title:"Delete Multiple Sales",message:"Are you sure you want to delete ".concat(V.length," sales? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:U,type:"danger"})]})}},12467:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var l=a(95155);a(12115);var r=a(46102),s=a(43316),n=a(75218);let o=e=>{let{isOpen:t,onClose:a,onConfirm:o,title:i,message:c,confirmText:d="Confirm",cancelText:x="Cancel",isLoading:g=!1,type:m="danger"}=e;return(0,l.jsx)(r.A,{title:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(n.A,{style:{color:"danger"===m?"#ff4d4f":"warning"===m?"#faad14":"#1890ff",marginRight:8}}),(0,l.jsx)("span",{children:i})]}),open:t,onCancel:a,footer:[(0,l.jsx)(s.Ay,{onClick:a,disabled:g,children:x},"cancel"),(0,l.jsx)(s.Ay,{type:"danger"===m?"primary":"default",danger:"danger"===m,onClick:o,loading:g,children:d},"confirm")],maskClosable:!g,closable:!g,centered:!0,children:(0,l.jsx)("p",{className:"my-4",children:c})})}},60102:(e,t,a)=>{"use strict";a.d(t,{A0:()=>n,Hj:()=>i,jB:()=>s,nA:()=>o});var l=a(95155);a(12115);var r=a(21567);let s=e=>{let{children:t,columns:a,className:s,minWidth:n="800px"}=e,o=window.innerWidth<768;return(0,l.jsx)("div",{className:(0,r.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",s),children:(0,l.jsx)("div",{className:(0,r.cn)("gap-0",o?"grid":"block"),style:o?{gridTemplateColumns:a,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:a,sticky:s}=e,n=window.innerWidth<768;return(0,l.jsx)("div",{className:(0,r.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",s&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[s],a),children:t})},o=e=>{let{children:t,className:a,sticky:s}=e,n=window.innerWidth<768;return(0,l.jsx)("div",{className:(0,r.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",s&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[s],a),children:t})},i=e=>{let{children:t,className:a,selected:s=!1,onClick:n}=e;return(0,l.jsx)("div",{className:(0,r.cn)("contents",s&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",a),onClick:n,children:t})}},30555:(e,t,a)=>{"use strict";a.d(t,{a:()=>r});var l=a(12115);function r(){let[e,t]=(0,l.useState)();return(0,l.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),a=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",a),()=>e.removeEventListener("change",a)},[]),!!e}},91256:(e,t,a)=>{"use strict";a.d(t,{E:()=>r});var l=a(12115);let r=()=>{let[e,t]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var l=a(43463),r=a(69795);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,l.$)(t))}},66202:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[3247,8059,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,5565,5211,3252,821,921,8441,1517,7358],()=>t(18053)),_N_E=e.O()}]);