[{"E:\\PROJECTS\\pos\\posfrontend\\src\\api\\apicaller.ts": "1", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\fetch.ts": "2", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx": "3", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\card.tsx": "4", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\client.tsx": "5", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\icons.tsx": "6", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\index.tsx": "7", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\role-based.tsx": "8", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\skeleton.tsx": "9", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx": "10", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expense-categories\\page.tsx": "11", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expenses\\page.tsx": "12", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx": "13", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\metadata.ts": "14", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\payment\\page.tsx": "15", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx": "16", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\layout.tsx": "17", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\page.tsx": "18", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\providers.tsx": "19", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\purchases\\page.tsx": "20", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\receipts\\page.tsx": "21", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\inventory\\page.tsx": "22", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\page.tsx": "23", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx": "24", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\offline-pos\\page.tsx": "25", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx": "26", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\stores\\page.tsx": "27", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\suppliers\\page.tsx": "28", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\users\\page.tsx": "29", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx": "30", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\metadata.ts": "31", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\offline\\page.tsx": "32", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\page.tsx": "33", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\callback\\page.tsx": "34", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\layout.tsx": "35", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\page.tsx": "36", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\layout.tsx": "37", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\page.tsx": "38", "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\providers.tsx": "39", "E:\\PROJECTS\\pos\\posfrontend\\src\\assets\\icons.tsx": "40", "E:\\PROJECTS\\pos\\posfrontend\\src\\assets\\logos\\index.ts": "41", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\AuthGuard.tsx": "42", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\LoginPage.tsx": "43", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\PaymentGuard.tsx": "44", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\ProtectedRoute.tsx": "45", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\RedirectIfAuthenticated.tsx": "46", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\RoleGuard.tsx": "47", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\Signin\\index.tsx": "48", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\SigninWithPassword.tsx": "49", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\BarcodeScanner\\BarcodeScanner.tsx": "50", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Breadcrumbs\\Breadcrumb.tsx": "51", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryDetailPanel.tsx": "52", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryFormPanel.tsx": "53", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryPagination.tsx": "54", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategorySearch.tsx": "55", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryTable.tsx": "56", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\chart.tsx": "57", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\client.tsx": "58", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\index.tsx": "59", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\chart.tsx": "60", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\client.tsx": "61", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\index.tsx": "62", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\OfflineIndicator.tsx": "63", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\PWAInstallPrompt.tsx": "64", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\PWAUpdateNotification.tsx": "65", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\AdminDashboard.tsx": "66", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\AdminSubscriptionsTable.tsx": "67", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\CashierDashboard.tsx": "68", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminProductsChart.tsx": "69", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminRevenueChart.tsx": "70", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminSalesChart.tsx": "71", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\PaymentStatusChart.tsx": "72", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\ProfitLossChart.tsx": "73", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\RevenueChart.tsx": "74", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\SubscriptionDistributionChart.tsx": "75", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\UserGrowthChart.tsx": "76", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\DashboardContent.tsx": "77", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\PaymentStatusWidget.tsx": "78", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\ProtectedDashboardContent.tsx": "79", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\RoleBasedDashboard.tsx": "80", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\SimpleDashboard.tsx": "81", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\SuperAdminDashboard.tsx": "82", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseCategoryFormPanel.tsx": "83", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseCategoryTable.tsx": "84", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseFormPanel.tsx": "85", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpensePagination.tsx": "86", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseTable.tsx": "87", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\icons.tsx": "88", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\index.tsx": "89", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\notification\\icons.tsx": "90", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\notification\\index.tsx": "91", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\theme-toggle\\icons.tsx": "92", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\theme-toggle\\index.tsx": "93", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\user-info\\icons.tsx": "94", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\user-info\\index.tsx": "95", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\showcase-section.tsx": "96", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\data\\index.ts": "97", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\data\\ui-elements-list.ts": "98", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\icons.tsx": "99", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\index.tsx": "100", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\menu-item.tsx": "101", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\MobileSidebar.tsx": "102", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx": "103", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\types.ts": "104", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\logo.tsx": "105", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentForm.tsx": "106", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentHistory.tsx": "107", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentNotification.tsx": "108", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentStatus.tsx": "109", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaystackPaymentForm.tsx": "110", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\period-picker.tsx": "111", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ImageUploadDropzone.tsx": "112", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductDetailPanel.tsx": "113", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductFormPanel.tsx": "114", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductPagination.tsx": "115", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductSearch.tsx": "116", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductTable.new.tsx": "117", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductTable.tsx": "118", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ChangePasswordForm.tsx": "119", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ProfileAvatar.tsx": "120", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ProfileForm.tsx": "121", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\StoreForm.tsx": "122", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseDetailPanel.tsx": "123", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseFormPanel.tsx": "124", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchasePagination.tsx": "125", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseSearch.tsx": "126", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseTable.tsx": "127", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\PWA\\PWAInstallPrompt.tsx": "128", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Receipts\\ReceiptTable.tsx": "129", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\OfflinePOSPanel.tsx": "130", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\POSPanel.tsx": "131", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesDetailsPanel.tsx": "132", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesFormPanel.tsx": "133", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesPagination.tsx": "134", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesSearch.tsx": "135", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesTable.tsx": "136", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\StockAdjustments\\StockAdjustmentFormPanel.tsx": "137", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoreDetailsPanel.tsx": "138", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoreFormPanel.tsx": "139", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresPagination.tsx": "140", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresSearch.tsx": "141", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresTable.tsx": "142", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\UserStorePanel.tsx": "143", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierDetailPanel.tsx": "144", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierFormPanel.tsx": "145", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierPagination.tsx": "146", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierSearch.tsx": "147", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierTable.tsx": "148", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\ConfirmationDialog.tsx": "149", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\dropdown.tsx": "150", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\LoadingSpinner.tsx": "151", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\ResponsiveTable.tsx": "152", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\skeleton.tsx": "153", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\SlidingPanel.tsx": "154", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\table.tsx": "155", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\alert\\icons.tsx": "156", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\alert\\index.tsx": "157", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\button.tsx": "158", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserDetail.tsx": "159", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserDetailPanel.tsx": "160", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserForm.tsx": "161", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserFormPanel.tsx": "162", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserPagination.tsx": "163", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserSearch.tsx": "164", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserTable.tsx": "165", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryBulkDelete.ts": "166", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryCreate.ts": "167", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryDelete.ts": "168", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryDetail.ts": "169", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryList.ts": "170", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryUpdate.ts": "171", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseBulkDelete.ts": "172", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseDelete.ts": "173", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseList.ts": "174", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\logoutUser.ts": "175", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductBulkDelete.ts": "176", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductCreate.ts": "177", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductDelete.ts": "178", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductDetail.ts": "179", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductList.ts": "180", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductUpdate.ts": "181", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseBulkDelete.ts": "182", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseCreate.ts": "183", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseDelete.ts": "184", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseList.ts": "185", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseUpdate.ts": "186", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\receipts\\useReceiptBulkDelete.ts": "187", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\receipts\\useReceiptDelete.ts": "188", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleBulkDelete.ts": "189", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleCreate.ts": "190", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleDelete.ts": "191", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSalesList.ts": "192", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentCreate.ts": "193", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentDelete.ts": "194", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentList.ts": "195", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stores\\useStoreBulkDelete.ts": "196", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stores\\useStoreDelete.ts": "197", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierBulkDelete.ts": "198", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierCreate.ts": "199", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierDelete.ts": "200", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierList.ts": "201", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierUpdate.ts": "202", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\use-click-outside.ts": "203", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\use-mobile.ts": "204", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useAuth.ts": "205", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useBarcodeScanner.ts": "206", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useCheckPaymentStatus.ts": "207", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useComprehensiveOffline.ts": "208", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useDebounce.ts": "209", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useLoginUser.ts": "210", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useOfflineData.ts": "211", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useOfflinePOS.ts": "212", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\usePayment.ts": "213", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\usePaymentWebSocket.ts": "214", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useResponsiveTable.ts": "215", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserBulkDelete.ts": "216", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserCreate.ts": "217", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserDelete.ts": "218", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserDetail.ts": "219", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserList.ts": "220", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserUpdate.ts": "221", "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useSubscriptionInfo.ts": "222", "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\format-message-time.ts": "223", "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\format-number.ts": "224", "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\utils.ts": "225", "E:\\PROJECTS\\pos\\posfrontend\\src\\provider\\Provider.tsx": "226", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\customBaseQuery.ts": "227", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\authApi.ts": "228", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\authSlice.ts": "229", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\categoryApi.ts": "230", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\dashboardApi.ts": "231", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\expenseApi.ts": "232", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\expenseCategoryApi.ts": "233", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\paymentApi.ts": "234", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\productApi.ts": "235", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\purchaseApi.ts": "236", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\receiptApi.ts": "237", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\salesApi.ts": "238", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\stockAdjustmentApi.ts": "239", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\storeApi.ts": "240", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\supplierApi.ts": "241", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\userStoreApi.ts": "242", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\storage.ts": "243", "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\store\\store.ts": "244", "E:\\PROJECTS\\pos\\posfrontend\\src\\services\\charts.services.ts": "245", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\api.ts": "246", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\icon-props.ts": "247", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\jspdf-autotable.d.ts": "248", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\payment.ts": "249", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\set-state-action-type.ts": "250", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\store.ts": "251", "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\user.ts": "252", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\cloudinaryUtils.ts": "253", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\comprehensiveOfflineStorage.ts": "254", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\comprehensiveOfflineSync.ts": "255", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\directUserRefresh.ts": "256", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\forceUserRefresh.ts": "257", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatDate.ts": "258", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatPhoneNumber.ts": "259", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatUtils.ts": "260", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\indexedDB.ts": "261", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\offlineStorage.ts": "262", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\offlineSync.ts": "263", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\refreshUserData.ts": "264", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\showMessage.ts": "265", "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\timeframe-extractor.ts": "266", "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\DailySalesChart.tsx": "267"}, {"size": 5137, "mtime": 1750371226041, "results": "268", "hashOfConfig": "269"}, {"size": 669, "mtime": 1748458393441, "results": "270", "hashOfConfig": "269"}, {"size": 1001, "mtime": 1748300783648, "results": "271", "hashOfConfig": "269"}, {"size": 1957, "mtime": 1748276404827, "results": "272", "hashOfConfig": "269"}, {"size": 2809, "mtime": 1748276352699, "results": "273", "hashOfConfig": "269"}, {"size": 3343, "mtime": 1748276352729, "results": "274", "hashOfConfig": "269"}, {"size": 1131, "mtime": 1748276352760, "results": "275", "hashOfConfig": "269"}, {"size": 11650, "mtime": 1748461236580, "results": "276", "hashOfConfig": "269"}, {"size": 732, "mtime": 1748276352823, "results": "277", "hashOfConfig": "269"}, {"size": 11938, "mtime": 1750281130849, "results": "278", "hashOfConfig": "269"}, {"size": 10954, "mtime": 1750281130849, "results": "279", "hashOfConfig": "269"}, {"size": 16444, "mtime": 1750281130849, "results": "280", "hashOfConfig": "269"}, {"size": 512, "mtime": 1748276353007, "results": "281", "hashOfConfig": "269"}, {"size": 273, "mtime": 1748276353056, "results": "282", "hashOfConfig": "269"}, {"size": 584, "mtime": 1750288253516, "results": "283", "hashOfConfig": "269"}, {"size": 13291, "mtime": 1750281130849, "results": "284", "hashOfConfig": "269"}, {"size": 381, "mtime": 1748276353120, "results": "285", "hashOfConfig": "269"}, {"size": 6537, "mtime": 1750288294222, "results": "286", "hashOfConfig": "269"}, {"size": 290, "mtime": 1748276353206, "results": "287", "hashOfConfig": "269"}, {"size": 9838, "mtime": 1750281130865, "results": "288", "hashOfConfig": "269"}, {"size": 21199, "mtime": 1750281130865, "results": "289", "hashOfConfig": "269"}, {"size": 26566, "mtime": 1750442528645, "results": "290", "hashOfConfig": "269"}, {"size": 6966, "mtime": 1750281130865, "results": "291", "hashOfConfig": "269"}, {"size": 40476, "mtime": 1750442540770, "results": "292", "hashOfConfig": "269"}, {"size": 283, "mtime": 1750281130881, "results": "293", "hashOfConfig": "269"}, {"size": 9427, "mtime": 1750444912989, "results": "294", "hashOfConfig": "269"}, {"size": 8318, "mtime": 1750281130881, "results": "295", "hashOfConfig": "269"}, {"size": 9830, "mtime": 1750281130881, "results": "296", "hashOfConfig": "269"}, {"size": 10756, "mtime": 1750281130881, "results": "297", "hashOfConfig": "269"}, {"size": 4600, "mtime": 1750281130881, "results": "298", "hashOfConfig": "269"}, {"size": 221, "mtime": 1748276353594, "results": "299", "hashOfConfig": "269"}, {"size": 6389, "mtime": 1750281130899, "results": "300", "hashOfConfig": "269"}, {"size": 184, "mtime": 1750281130901, "results": "301", "hashOfConfig": "269"}, {"size": 8921, "mtime": 1750289170643, "results": "302", "hashOfConfig": "269"}, {"size": 391, "mtime": 1750288879470, "results": "303", "hashOfConfig": "269"}, {"size": 6400, "mtime": 1750405627581, "results": "304", "hashOfConfig": "269"}, {"size": 2322, "mtime": 1748276353653, "results": "305", "hashOfConfig": "269"}, {"size": 548, "mtime": 1750281130907, "results": "306", "hashOfConfig": "269"}, {"size": 405, "mtime": 1748276353723, "results": "307", "hashOfConfig": "269"}, {"size": 28069, "mtime": 1748276353949, "results": "308", "hashOfConfig": "269"}, {"size": 341, "mtime": 1748276354123, "results": "309", "hashOfConfig": "269"}, {"size": 1911, "mtime": 1748753322526, "results": "310", "hashOfConfig": "269"}, {"size": 1232, "mtime": 1749813218471, "results": "311", "hashOfConfig": "269"}, {"size": 8676, "mtime": 1750349141766, "results": "312", "hashOfConfig": "269"}, {"size": 1458, "mtime": 1748276354386, "results": "313", "hashOfConfig": "269"}, {"size": 1347, "mtime": 1748276354417, "results": "314", "hashOfConfig": "269"}, {"size": 1957, "mtime": 1748276354445, "results": "315", "hashOfConfig": "269"}, {"size": 181, "mtime": 1748276354471, "results": "316", "hashOfConfig": "269"}, {"size": 3687, "mtime": 1748276354512, "results": "317", "hashOfConfig": "269"}, {"size": 6534, "mtime": 1749749469270, "results": "318", "hashOfConfig": "269"}, {"size": 692, "mtime": 1748276354574, "results": "319", "hashOfConfig": "269"}, {"size": 4599, "mtime": 1750281130907, "results": "320", "hashOfConfig": "269"}, {"size": 4955, "mtime": 1749155982861, "results": "321", "hashOfConfig": "269"}, {"size": 4170, "mtime": 1748276354671, "results": "322", "hashOfConfig": "269"}, {"size": 1493, "mtime": 1748499664692, "results": "323", "hashOfConfig": "269"}, {"size": 12405, "mtime": 1750281130913, "results": "324", "hashOfConfig": "269"}, {"size": 1855, "mtime": 1748276354852, "results": "325", "hashOfConfig": "269"}, {"size": 3362, "mtime": 1748276405204, "results": "326", "hashOfConfig": "269"}, {"size": 1792, "mtime": 1748276354907, "results": "327", "hashOfConfig": "269"}, {"size": 2434, "mtime": 1748276405223, "results": "328", "hashOfConfig": "269"}, {"size": 5246, "mtime": 1748276405242, "results": "329", "hashOfConfig": "269"}, {"size": 1008, "mtime": 1748276354997, "results": "330", "hashOfConfig": "269"}, {"size": 7152, "mtime": 1749761916734, "results": "331", "hashOfConfig": "269"}, {"size": 7832, "mtime": 1749763516203, "results": "332", "hashOfConfig": "269"}, {"size": 3070, "mtime": 1750370832415, "results": "333", "hashOfConfig": "269"}, {"size": 18260, "mtime": 1750967585564, "results": "334", "hashOfConfig": "269"}, {"size": 8092, "mtime": 1749678125924, "results": "335", "hashOfConfig": "269"}, {"size": 10503, "mtime": 1750440485822, "results": "336", "hashOfConfig": "269"}, {"size": 7803, "mtime": 1748370082831, "results": "337", "hashOfConfig": "269"}, {"size": 3266, "mtime": 1748458762099, "results": "338", "hashOfConfig": "269"}, {"size": 4233, "mtime": 1750967604961, "results": "339", "hashOfConfig": "269"}, {"size": 3146, "mtime": 1748364613934, "results": "340", "hashOfConfig": "269"}, {"size": 3149, "mtime": 1750437923661, "results": "341", "hashOfConfig": "269"}, {"size": 3869, "mtime": 1748459265745, "results": "342", "hashOfConfig": "269"}, {"size": 3754, "mtime": 1748365668856, "results": "343", "hashOfConfig": "269"}, {"size": 3894, "mtime": 1748364573398, "results": "344", "hashOfConfig": "269"}, {"size": 1998, "mtime": 1748362811689, "results": "345", "hashOfConfig": "269"}, {"size": 6094, "mtime": 1750288286620, "results": "346", "hashOfConfig": "269"}, {"size": 5790, "mtime": 1750375702941, "results": "347", "hashOfConfig": "269"}, {"size": 1975, "mtime": 1748493880625, "results": "348", "hashOfConfig": "269"}, {"size": 1202, "mtime": 1748276355139, "results": "349", "hashOfConfig": "269"}, {"size": 5383, "mtime": 1748460485777, "results": "350", "hashOfConfig": "269"}, {"size": 8782, "mtime": 1748754173341, "results": "351", "hashOfConfig": "269"}, {"size": 12593, "mtime": 1749154248604, "results": "352", "hashOfConfig": "269"}, {"size": 13573, "mtime": 1749812057127, "results": "353", "hashOfConfig": "269"}, {"size": 4179, "mtime": 1748707155463, "results": "354", "hashOfConfig": "269"}, {"size": 18710, "mtime": 1750229592190, "results": "355", "hashOfConfig": "269"}, {"size": 906, "mtime": 1748276355189, "results": "356", "hashOfConfig": "269"}, {"size": 2699, "mtime": 1749758968459, "results": "357", "hashOfConfig": "269"}, {"size": 966, "mtime": 1748276355252, "results": "358", "hashOfConfig": "269"}, {"size": 3980, "mtime": 1748276355295, "results": "359", "hashOfConfig": "269"}, {"size": 1660, "mtime": 1748276355327, "results": "360", "hashOfConfig": "269"}, {"size": 630, "mtime": 1748276355355, "results": "361", "hashOfConfig": "269"}, {"size": 6255, "mtime": 1748276355423, "results": "362", "hashOfConfig": "269"}, {"size": 4308, "mtime": 1750228600439, "results": "363", "hashOfConfig": "269"}, {"size": 551, "mtime": 1748276355473, "results": "364", "hashOfConfig": "269"}, {"size": 4728, "mtime": 1750288672079, "results": "365", "hashOfConfig": "269"}, {"size": 281, "mtime": 1748276355585, "results": "366", "hashOfConfig": "269"}, {"size": 18769, "mtime": 1748707738322, "results": "367", "hashOfConfig": "269"}, {"size": 9074, "mtime": 1750445111633, "results": "368", "hashOfConfig": "269"}, {"size": 1801, "mtime": 1748318887453, "results": "369", "hashOfConfig": "269"}, {"size": 9109, "mtime": 1749942405126, "results": "370", "hashOfConfig": "269"}, {"size": 2913, "mtime": 1748276355707, "results": "371", "hashOfConfig": "269"}, {"size": 304, "mtime": 1748707819734, "results": "372", "hashOfConfig": "269"}, {"size": 388, "mtime": 1748276357155, "results": "373", "hashOfConfig": "269"}, {"size": 326, "mtime": 1748276405303, "results": "374", "hashOfConfig": "269"}, {"size": 6488, "mtime": 1749447106472, "results": "375", "hashOfConfig": "269"}, {"size": 2328, "mtime": 1750288279099, "results": "376", "hashOfConfig": "269"}, {"size": 7550, "mtime": 1750288270796, "results": "377", "hashOfConfig": "269"}, {"size": 12011, "mtime": 1750357678316, "results": "378", "hashOfConfig": "269"}, {"size": 3651, "mtime": 1748276357182, "results": "379", "hashOfConfig": "269"}, {"size": 4231, "mtime": 1749829089975, "results": "380", "hashOfConfig": "269"}, {"size": 5505, "mtime": 1749156155383, "results": "381", "hashOfConfig": "269"}, {"size": 14188, "mtime": 1750238794902, "results": "382", "hashOfConfig": "269"}, {"size": 4164, "mtime": 1748276355926, "results": "383", "hashOfConfig": "269"}, {"size": 1490, "mtime": 1748499697937, "results": "384", "hashOfConfig": "269"}, {"size": 16906, "mtime": 1749749685115, "results": "385", "hashOfConfig": "269"}, {"size": 16462, "mtime": 1749747622749, "results": "386", "hashOfConfig": "269"}, {"size": 6504, "mtime": 1748461415725, "results": "387", "hashOfConfig": "269"}, {"size": 3998, "mtime": 1748276356063, "results": "388", "hashOfConfig": "269"}, {"size": 10725, "mtime": 1748276356091, "results": "389", "hashOfConfig": "269"}, {"size": 7158, "mtime": 1748276356121, "results": "390", "hashOfConfig": "269"}, {"size": 6582, "mtime": 1749156310588, "results": "391", "hashOfConfig": "269"}, {"size": 10193, "mtime": 1750239035187, "results": "392", "hashOfConfig": "269"}, {"size": 4170, "mtime": 1748276356205, "results": "393", "hashOfConfig": "269"}, {"size": 1493, "mtime": 1748499718520, "results": "394", "hashOfConfig": "269"}, {"size": 15359, "mtime": 1749157034035, "results": "395", "hashOfConfig": "269"}, {"size": 5097, "mtime": 1749774997844, "results": "396", "hashOfConfig": "269"}, {"size": 13980, "mtime": 1749154071155, "results": "397", "hashOfConfig": "269"}, {"size": 13906, "mtime": 1749944785694, "results": "398", "hashOfConfig": "269"}, {"size": 11669, "mtime": 1749747759096, "results": "399", "hashOfConfig": "269"}, {"size": 15460, "mtime": 1749161609235, "results": "400", "hashOfConfig": "269"}, {"size": 50281, "mtime": 1750238103822, "results": "401", "hashOfConfig": "269"}, {"size": 4128, "mtime": 1749161609274, "results": "402", "hashOfConfig": "269"}, {"size": 1406, "mtime": 1749161609363, "results": "403", "hashOfConfig": "269"}, {"size": 15097, "mtime": 1749272843463, "results": "404", "hashOfConfig": "269"}, {"size": 7797, "mtime": 1750444266717, "results": "405", "hashOfConfig": "269"}, {"size": 6819, "mtime": 1749157476767, "results": "406", "hashOfConfig": "269"}, {"size": 8606, "mtime": 1749814152309, "results": "407", "hashOfConfig": "269"}, {"size": 4047, "mtime": 1748276356626, "results": "408", "hashOfConfig": "269"}, {"size": 1423, "mtime": 1748499789143, "results": "409", "hashOfConfig": "269"}, {"size": 16922, "mtime": 1750281130918, "results": "410", "hashOfConfig": "269"}, {"size": 8992, "mtime": 1748494205231, "results": "411", "hashOfConfig": "269"}, {"size": 6026, "mtime": 1749156775295, "results": "412", "hashOfConfig": "269"}, {"size": 6861, "mtime": 1749156792937, "results": "413", "hashOfConfig": "269"}, {"size": 4170, "mtime": 1748276356819, "results": "414", "hashOfConfig": "269"}, {"size": 1495, "mtime": 1748499832005, "results": "415", "hashOfConfig": "269"}, {"size": 15031, "mtime": 1749152557337, "results": "416", "hashOfConfig": "269"}, {"size": 1793, "mtime": 1750281130918, "results": "417", "hashOfConfig": "269"}, {"size": 3382, "mtime": 1748276357443, "results": "418", "hashOfConfig": "269"}, {"size": 1074, "mtime": 1748276357349, "results": "419", "hashOfConfig": "269"}, {"size": 5276, "mtime": 1749151955348, "results": "420", "hashOfConfig": "269"}, {"size": 280, "mtime": 1748276357478, "results": "421", "hashOfConfig": "269"}, {"size": 4519, "mtime": 1749812757614, "results": "422", "hashOfConfig": "269"}, {"size": 1885, "mtime": 1748276357507, "results": "423", "hashOfConfig": "269"}, {"size": 2102, "mtime": 1748276357226, "results": "424", "hashOfConfig": "269"}, {"size": 1723, "mtime": 1748276357256, "results": "425", "hashOfConfig": "269"}, {"size": 1473, "mtime": 1748276357284, "results": "426", "hashOfConfig": "269"}, {"size": 4015, "mtime": 1748276356935, "results": "427", "hashOfConfig": "269"}, {"size": 6155, "mtime": 1749156831476, "results": "428", "hashOfConfig": "269"}, {"size": 7231, "mtime": 1748276356995, "results": "429", "hashOfConfig": "269"}, {"size": 10386, "mtime": 1749156860163, "results": "430", "hashOfConfig": "269"}, {"size": 4057, "mtime": 1748276357054, "results": "431", "hashOfConfig": "269"}, {"size": 1475, "mtime": 1748499853679, "results": "432", "hashOfConfig": "269"}, {"size": 14495, "mtime": 1750375482610, "results": "433", "hashOfConfig": "269"}, {"size": 1195, "mtime": 1748276405602, "results": "434", "hashOfConfig": "269"}, {"size": 1655, "mtime": 1748276359420, "results": "435", "hashOfConfig": "269"}, {"size": 1059, "mtime": 1748276359466, "results": "436", "hashOfConfig": "269"}, {"size": 575, "mtime": 1748461455635, "results": "437", "hashOfConfig": "269"}, {"size": 2004, "mtime": 1748461553136, "results": "438", "hashOfConfig": "269"}, {"size": 1165, "mtime": 1748276359578, "results": "439", "hashOfConfig": "269"}, {"size": 1696, "mtime": 1748677117084, "results": "440", "hashOfConfig": "269"}, {"size": 1246, "mtime": 1748677107593, "results": "441", "hashOfConfig": "269"}, {"size": 2024, "mtime": 1748677097249, "results": "442", "hashOfConfig": "269"}, {"size": 1195, "mtime": 1748276405623, "results": "443", "hashOfConfig": "269"}, {"size": 1163, "mtime": 1748276405637, "results": "444", "hashOfConfig": "269"}, {"size": 1882, "mtime": 1748276405662, "results": "445", "hashOfConfig": "269"}, {"size": 1043, "mtime": 1748276359655, "results": "446", "hashOfConfig": "269"}, {"size": 513, "mtime": 1748276359688, "results": "447", "hashOfConfig": "269"}, {"size": 1782, "mtime": 1748276405667, "results": "448", "hashOfConfig": "269"}, {"size": 1580, "mtime": 1748276405691, "results": "449", "hashOfConfig": "269"}, {"size": 1182, "mtime": 1748276405713, "results": "450", "hashOfConfig": "269"}, {"size": 1338, "mtime": 1748276359765, "results": "451", "hashOfConfig": "269"}, {"size": 1059, "mtime": 1748276359797, "results": "452", "hashOfConfig": "269"}, {"size": 1640, "mtime": 1748276359825, "results": "453", "hashOfConfig": "269"}, {"size": 1165, "mtime": 1748276359839, "results": "454", "hashOfConfig": "269"}, {"size": 1163, "mtime": 1748276405727, "results": "455", "hashOfConfig": "269"}, {"size": 1093, "mtime": 1748276405746, "results": "456", "hashOfConfig": "269"}, {"size": 1107, "mtime": 1748276405763, "results": "457", "hashOfConfig": "269"}, {"size": 1214, "mtime": 1748276359890, "results": "458", "hashOfConfig": "269"}, {"size": 937, "mtime": 1748276359918, "results": "459", "hashOfConfig": "269"}, {"size": 1836, "mtime": 1748276359957, "results": "460", "hashOfConfig": "269"}, {"size": 1730, "mtime": 1748276405786, "results": "461", "hashOfConfig": "269"}, {"size": 1166, "mtime": 1748276360009, "results": "462", "hashOfConfig": "269"}, {"size": 1547, "mtime": 1748276360038, "results": "463", "hashOfConfig": "269"}, {"size": 1125, "mtime": 1748276405794, "results": "464", "hashOfConfig": "269"}, {"size": 1010, "mtime": 1748276360069, "results": "465", "hashOfConfig": "269"}, {"size": 1182, "mtime": 1748276405814, "results": "466", "hashOfConfig": "269"}, {"size": 1338, "mtime": 1748276360106, "results": "467", "hashOfConfig": "269"}, {"size": 1059, "mtime": 1748276360140, "results": "468", "hashOfConfig": "269"}, {"size": 2033, "mtime": 1748276360172, "results": "469", "hashOfConfig": "269"}, {"size": 1165, "mtime": 1748276360193, "results": "470", "hashOfConfig": "269"}, {"size": 514, "mtime": 1748276360222, "results": "471", "hashOfConfig": "269"}, {"size": 566, "mtime": 1748276360257, "results": "472", "hashOfConfig": "269"}, {"size": 6481, "mtime": 1748276360291, "results": "473", "hashOfConfig": "269"}, {"size": 2797, "mtime": 1749317177861, "results": "474", "hashOfConfig": "269"}, {"size": 3250, "mtime": 1749681344218, "results": "475", "hashOfConfig": "269"}, {"size": 12467, "mtime": 1749760098316, "results": "476", "hashOfConfig": "269"}, {"size": 871, "mtime": 1748276360352, "results": "477", "hashOfConfig": "269"}, {"size": 3021, "mtime": 1748276405830, "results": "478", "hashOfConfig": "269"}, {"size": 2540, "mtime": 1750281130918, "results": "479", "hashOfConfig": "269"}, {"size": 11421, "mtime": 1749944768200, "results": "480", "hashOfConfig": "269"}, {"size": 2981, "mtime": 1748276405839, "results": "481", "hashOfConfig": "269"}, {"size": 2805, "mtime": 1750405274753, "results": "482", "hashOfConfig": "269"}, {"size": 647, "mtime": 1749152059663, "results": "483", "hashOfConfig": "269"}, {"size": 1106, "mtime": 1748276405865, "results": "484", "hashOfConfig": "269"}, {"size": 1014, "mtime": 1748276360408, "results": "485", "hashOfConfig": "269"}, {"size": 1262, "mtime": 1748276360446, "results": "486", "hashOfConfig": "269"}, {"size": 523, "mtime": 1748276360483, "results": "487", "hashOfConfig": "269"}, {"size": 1517, "mtime": 1748276360510, "results": "488", "hashOfConfig": "269"}, {"size": 1080, "mtime": 1748276360543, "results": "489", "hashOfConfig": "269"}, {"size": 3177, "mtime": 1749805197565, "results": "490", "hashOfConfig": "269"}, {"size": 1252, "mtime": 1748276360608, "results": "491", "hashOfConfig": "269"}, {"size": 351, "mtime": 1748276360630, "results": "492", "hashOfConfig": "269"}, {"size": 166, "mtime": 1748276360659, "results": "493", "hashOfConfig": "269"}, {"size": 3148, "mtime": 1748464215469, "results": "494", "hashOfConfig": "269"}, {"size": 3023, "mtime": 1748276405902, "results": "495", "hashOfConfig": "269"}, {"size": 5816, "mtime": 1748285390781, "results": "496", "hashOfConfig": "269"}, {"size": 783, "mtime": 1748276405990, "results": "497", "hashOfConfig": "269"}, {"size": 7443, "mtime": 1748461787757, "results": "498", "hashOfConfig": "269"}, {"size": 2622, "mtime": 1750437551018, "results": "499", "hashOfConfig": "269"}, {"size": 6976, "mtime": 1748676563871, "results": "500", "hashOfConfig": "269"}, {"size": 5482, "mtime": 1748676580151, "results": "501", "hashOfConfig": "269"}, {"size": 6240, "mtime": 1749447114012, "results": "502", "hashOfConfig": "269"}, {"size": 8250, "mtime": 1749590533528, "results": "503", "hashOfConfig": "269"}, {"size": 6770, "mtime": 1748276406102, "results": "504", "hashOfConfig": "269"}, {"size": 4578, "mtime": 1748276406125, "results": "505", "hashOfConfig": "269"}, {"size": 7013, "mtime": 1748276406157, "results": "506", "hashOfConfig": "269"}, {"size": 6153, "mtime": 1748461889284, "results": "507", "hashOfConfig": "269"}, {"size": 6078, "mtime": 1748276406210, "results": "508", "hashOfConfig": "269"}, {"size": 7020, "mtime": 1748276406216, "results": "509", "hashOfConfig": "269"}, {"size": 7334, "mtime": 1748276360918, "results": "510", "hashOfConfig": "269"}, {"size": 634, "mtime": 1748276360948, "results": "511", "hashOfConfig": "269"}, {"size": 3107, "mtime": 1748676608918, "results": "512", "hashOfConfig": "269"}, {"size": 3061, "mtime": 1748417755400, "results": "513", "hashOfConfig": "269"}, {"size": 838, "mtime": 1748457453410, "results": "514", "hashOfConfig": "269"}, {"size": 55, "mtime": 1748276361070, "results": "515", "hashOfConfig": "269"}, {"size": 463, "mtime": 1750229592149, "results": "516", "hashOfConfig": "269"}, {"size": 1230, "mtime": 1748320675999, "results": "517", "hashOfConfig": "269"}, {"size": 121, "mtime": 1748276361104, "results": "518", "hashOfConfig": "269"}, {"size": 1197, "mtime": 1748276361130, "results": "519", "hashOfConfig": "269"}, {"size": 1172, "mtime": 1748461709979, "results": "520", "hashOfConfig": "269"}, {"size": 8457, "mtime": 1750237480471, "results": "521", "hashOfConfig": "269"}, {"size": 13302, "mtime": 1749759903570, "results": "522", "hashOfConfig": "269"}, {"size": 11260, "mtime": 1749759974969, "results": "523", "hashOfConfig": "269"}, {"size": 4672, "mtime": 1748464280984, "results": "524", "hashOfConfig": "269"}, {"size": 2315, "mtime": 1748464331928, "results": "525", "hashOfConfig": "269"}, {"size": 521, "mtime": 1748276361207, "results": "526", "hashOfConfig": "269"}, {"size": 2647, "mtime": 1748276361234, "results": "527", "hashOfConfig": "269"}, {"size": 1830, "mtime": 1748276406304, "results": "528", "hashOfConfig": "269"}, {"size": 4454, "mtime": 1750281130929, "results": "529", "hashOfConfig": "269"}, {"size": 8753, "mtime": 1749944585786, "results": "530", "hashOfConfig": "269"}, {"size": 6414, "mtime": 1749751088113, "results": "531", "hashOfConfig": "269"}, {"size": 5995, "mtime": 1748464368167, "results": "532", "hashOfConfig": "269"}, {"size": 734, "mtime": 1749751649027, "results": "533", "hashOfConfig": "269"}, {"size": 229, "mtime": 1748276361286, "results": "534", "hashOfConfig": "269"}, {"size": 4422, "mtime": 1750967554572, "results": "535", "hashOfConfig": "269"}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vcywfp", {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\PROJECTS\\pos\\posfrontend\\src\\api\\apicaller.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\fetch.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\card.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\client.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\role-based.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\_components\\overview-cards\\skeleton.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expense-categories\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\expenses\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\metadata.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\payment\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\layout.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\profile\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\providers.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\purchases\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\receipts\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\inventory\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\offline-pos\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\stores\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\suppliers\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\users\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\metadata.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\offline\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\callback\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\layout.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\layout.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\page.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\app\\providers.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\assets\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\assets\\logos\\index.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\AuthGuard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\LoginPage.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\PaymentGuard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\RedirectIfAuthenticated.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\RoleGuard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\Signin\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Auth\\SigninWithPassword.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\BarcodeScanner\\BarcodeScanner.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Breadcrumbs\\Breadcrumb.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryDetailPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategorySearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Categories\\CategoryTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\chart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\client.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\payments-overview\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\chart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\client.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Charts\\weeks-profit\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\OfflineIndicator.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\PWAInstallPrompt.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Common\\PWAUpdateNotification.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\AdminDashboard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\AdminSubscriptionsTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\CashierDashboard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminProductsChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminRevenueChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\AdminSalesChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\PaymentStatusChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\ProfitLossChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\RevenueChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\SubscriptionDistributionChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\UserGrowthChart.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\DashboardContent.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\PaymentStatusWidget.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\ProtectedDashboardContent.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\RoleBasedDashboard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\SimpleDashboard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\SuperAdminDashboard.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseCategoryFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseCategoryTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpensePagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Expenses\\ExpenseTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\notification\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\notification\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\theme-toggle\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\theme-toggle\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\user-info\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\header\\user-info\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\showcase-section.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\data\\index.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\data\\ui-elements-list.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\menu-item.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\MobileSidebar.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\types.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\logo.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentHistory.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentNotification.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaymentStatus.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Payment\\PaystackPaymentForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\period-picker.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ImageUploadDropzone.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductDetailPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductTable.new.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Products\\ProductTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ChangePasswordForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ProfileAvatar.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\ProfileForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Profile\\StoreForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseDetailPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchasePagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Purchases\\PurchaseTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\PWA\\PWAInstallPrompt.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Receipts\\ReceiptTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\OfflinePOSPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\POSPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesDetailsPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Sales\\SalesTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\StockAdjustments\\StockAdjustmentFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoreDetailsPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoreFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\StoresTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Stores\\UserStorePanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierDetailPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Suppliers\\SupplierTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\ConfirmationDialog.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\dropdown.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\ResponsiveTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\skeleton.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\SlidingPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui\\table.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\alert\\icons.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\alert\\index.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\ui-elements\\button.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserDetail.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserDetailPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserForm.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserFormPanel.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserPagination.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserSearch.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Users\\UserTable.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryDetail.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\categories\\useCategoryUpdate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\expenses\\useExpenseList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\logoutUser.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductDetail.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\products\\useProductUpdate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\purchases\\usePurchaseUpdate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\receipts\\useReceiptBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\receipts\\useReceiptDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSaleDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\sales\\useSalesList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stockAdjustments\\useStockAdjustmentList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stores\\useStoreBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\stores\\useStoreDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\suppliers\\useSupplierUpdate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\use-click-outside.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\use-mobile.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useAuth.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useBarcodeScanner.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useCheckPaymentStatus.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useComprehensiveOffline.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useDebounce.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useLoginUser.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useOfflineData.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useOfflinePOS.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\usePayment.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\usePaymentWebSocket.ts", ["1337"], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useResponsiveTable.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserBulkDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserCreate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserDelete.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserDetail.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserList.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\users\\useUserUpdate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\hooks\\useSubscriptionInfo.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\format-message-time.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\format-number.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\lib\\utils.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\provider\\Provider.tsx", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\customBaseQuery.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\authApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\authSlice.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\categoryApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\dashboardApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\expenseApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\expenseCategoryApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\paymentApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\productApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\purchaseApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\receiptApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\salesApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\stockAdjustmentApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\storeApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\supplierApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\services\\userStoreApi.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\storage.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\reduxRTK\\store\\store.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\services\\charts.services.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\api.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\icon-props.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\jspdf-autotable.d.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\payment.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\set-state-action-type.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\store.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\types\\user.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\cloudinaryUtils.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\comprehensiveOfflineStorage.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\comprehensiveOfflineSync.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\directUserRefresh.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\forceUserRefresh.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatDate.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatPhoneNumber.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\formatUtils.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\indexedDB.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\offlineStorage.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\offlineSync.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\refreshUserData.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\showMessage.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\utils\\timeframe-extractor.ts", [], [], "E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\Charts\\DailySalesChart.tsx", [], [], {"ruleId": "1338", "severity": 1, "message": "1339", "line": 70, "column": 6, "nodeType": "1340", "endLine": 70, "endColumn": 26, "suggestions": "1341"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", "ArrayExpression", ["1342"], {"desc": "1343", "fix": "1344"}, "Update the dependencies array to be: [user.id, dispatch, user]", {"range": "1345", "text": "1346"}, [2779, 2799], "[user.id, dispatch, user]"]