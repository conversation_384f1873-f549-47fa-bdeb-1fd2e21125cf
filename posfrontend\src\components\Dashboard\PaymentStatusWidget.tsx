"use client";
import React from "react";
import { Progress } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter } from "next/navigation";
import { useCheckPaymentStatus } from "@/hooks/useCheckPaymentStatus";
import dayjs from "dayjs";

const PaymentStatusWidget: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();
  const { isActive, daysRemaining, needsPayment, status } = useCheckPaymentStatus();

  // Enhanced debug log (always log for debugging payment issues)
  console.log("💳 PaymentStatusWidget - Status:", {
    userRole: user?.role,
    userPaymentStatus: user?.paymentStatus,
    isActive,
    daysRemaining,
    needsPayment,
    status,
    nextPaymentDue: user?.nextPaymentDue,
    lastPaymentDate: user?.lastPaymentDate
  });

  // Special debug for pending users
  if (user?.paymentStatus === 'pending') {
    console.log("🚨 PaymentStatusWidget - PENDING USER detected but showing as active! This is the UI bug!");
  }

  // Don't show payment widget for superadmin or if no user
  if (!user || user.role === 'superadmin') {
    return null;
  }

  const handleManageSubscription = () => {
    router.push("/payment");
  };

  // Calculate subscription progress
  const calculateProgress = () => {
    if (!user.nextPaymentDue || !user.lastPaymentDate) {
      return 0;
    }

    const startDate = dayjs(user.lastPaymentDate);
    const endDate = dayjs(user.nextPaymentDue);
    const today = dayjs();

    // Total subscription period in days
    const totalDays = endDate.diff(startDate, 'day');
    if (totalDays <= 0) return 0;

    // Days elapsed since start
    const daysElapsed = today.diff(startDate, 'day');
    if (daysElapsed < 0) return 0;

    // Calculate percentage
    const percentage = Math.min(100, Math.round((daysElapsed / totalDays) * 100));
    return percentage;
  };

  const getProgressStatus = () => {
    // Use actual payment status instead of just isActive
    if (user.paymentStatus === 'pending') return "warning";
    if (user.paymentStatus === 'overdue' || user.paymentStatus === 'inactive') return "exception";
    if (user.paymentStatus === 'paid' && !isActive) return "exception";
    if (daysRemaining !== null && daysRemaining <= 7) return "warning";
    return "success";
  };

  const getProgressColor = () => {
    // Use actual payment status instead of just isActive
    if (user.paymentStatus === 'pending') return "#faad14"; // Yellow for pending
    if (user.paymentStatus === 'overdue' || user.paymentStatus === 'inactive') return "#f5222d"; // Red for overdue/inactive
    if (user.paymentStatus === 'paid' && !isActive) return "#f5222d";
    if (daysRemaining !== null && daysRemaining <= 7) return "#faad14";
    return "#52c41a"; // Green for active
  };

  return (
    <div className="p-5">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800">Subscription Status</h2>
        <button
          onClick={handleManageSubscription}
          className="text-blue-600 hover:text-blue-700 text-sm"
        >
          Manage
        </button>
      </div>
      <div className="flex flex-col space-y-4">
        <Progress
          type="circle"
          percent={calculateProgress()}
          status={getProgressStatus() as any}
          strokeColor={getProgressColor()}
          className="mx-auto"
          trailColor="#f0f0f0"
        />

        <div className="text-center mt-4">
          {/* Use actual user payment status instead of just isActive */}
          {user.paymentStatus === 'paid' && isActive ? (
            <div>
              <p className="text-green-500 font-medium">
                Your subscription is active
              </p>
              <p className="text-gray-800 text-sm mt-1">
                Monthly Plan (GH₵40/month)
              </p>
              {daysRemaining !== null && (
                <span className="block text-sm mt-1 text-gray-500">
                  {daysRemaining > 0
                    ? `Renews in ${daysRemaining} day${daysRemaining > 1 ? 's' : ''}`
                    : "Renewal due today"}
                </span>
              )}
            </div>
          ) : user.paymentStatus === 'pending' ? (
            <div>
              <p className="text-yellow-500 font-medium">
                Your payment is pending verification
              </p>
              <p className="text-gray-800 text-sm mt-1">
                Monthly Plan (GH₵64/month)
              </p>
              <span className="block text-sm mt-1 text-gray-500">
                Please wait while we verify your payment
              </span>
            </div>
          ) : user.paymentStatus === 'overdue' ? (
            <div>
              <p className="text-red-500 font-medium">
                Your payment is overdue
              </p>
              <p className="text-gray-800 text-sm mt-1">
                Monthly Plan (GH₵64/month)
              </p>
              <span className="block text-sm mt-1 text-gray-500">
                Please make a payment to continue using the system
              </span>
            </div>
          ) : (
            <div>
              <p className="text-red-500 font-medium">
                Your subscription is inactive
              </p>
              <p className="text-gray-800 text-sm mt-1">
                Monthly Plan (GH₵64/month)
              </p>
              <span className="block text-sm mt-1 text-gray-500">
                Make a payment to continue using the system
              </span>
            </div>
          )}
        </div>

        {needsPayment && (
          <button
            onClick={handleManageSubscription}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors"
          >
            Manage Subscription
          </button>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusWidget;
