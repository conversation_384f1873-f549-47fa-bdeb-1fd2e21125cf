"use client";

import { SearchIcon } from "@/assets/icons";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSidebarContext } from "../sidebar/sidebar-context";
import { MenuIcon } from "./icons";
import { Notification } from "./notification";
import { ThemeToggleSwitch } from "./theme-toggle";
import { UserInfo } from "./user-info";
import OfflineIndicator from "@/components/Common/OfflineIndicator";

export function Header() {
  const router = useRouter();
  const { toggleSidebar, isMobile, setIsOpen } = useSidebarContext();

  return (
    <header className="w-full flex items-center justify-between border-b border-stroke bg-white px-4 py-5 shadow-1 md:px-5 2xl:px-10">
      {/* Only show toggle button on mobile */}
      {isMobile && (
        <button
          onClick={(e) => {
            // Prevent event bubbling
            e.stopPropagation();
            console.log("Toggle sidebar clicked from header");

            // Use direct state manipulation for more reliability
            setIsOpen(true);
          }}
          className="rounded-lg border px-1.5 py-1"
          aria-label="Open sidebar menu"
        >
          <MenuIcon />
          <span className="sr-only">Toggle Sidebar</span>
        </button>
      )}

      {isMobile && (
        <Link
          href={"/dashboard"}
          className="ml-2 mobile-logo max-[430px]:hidden min-[375px]:ml-4"
          onClick={(e) => {
            // Check if we're already on a page with a different layout
            const isOnUsersPage = window.location.pathname.includes("/dashboard/users");

            // If we're on a page with a different layout, allow full page navigation
            if (isOnUsersPage) {
              return; // Allow default navigation
            }

            // Otherwise use client-side navigation
            e.preventDefault();
            router.push("/dashboard");
          }}
        >
          <Image
            src={"/images/logo/logo-small.png"}
            width={36}
            height={36}
            alt="Company logo"
            role="presentation"
            className="object-contain"
          />
        </Link>
      )}

      <div className="max-xl:hidden">
        <h4 className="mb-0.5 text-heading-5 font-bold text-dark">
          Dashboard
        </h4>
        <p className="font-medium">POS System | Inventory Management</p>
      </div>

      <div className="flex flex-1 items-center justify-end gap-2 min-[375px]:gap-4">
        <OfflineIndicator className="shrink-0" />
        <div className="shrink-0">
          <UserInfo />
        </div>
      </div>
    </header>
  );
}
