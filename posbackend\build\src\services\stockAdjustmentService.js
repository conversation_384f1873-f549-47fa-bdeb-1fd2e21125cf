"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteStockAdjustmentById = exports.updateStockAdjustmentById = exports.getStockAdjustmentById = exports.getAllStockAdjustments = exports.createStockAdjustment = void 0;
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const schema_2 = require("../validation/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
const drizzle_orm_1 = require("drizzle-orm");
// ✅ **Create Stock Adjustment**
const createStockAdjustment = async (requester, adjustmentData) => {
    // ✅ Validate Input
    schema_2.stockAdjustmentSchema.parse(adjustmentData);
    // ❌ Explicitly prevent cashiers from adjusting stock
    if (requester.role === "cashier") {
        throw new Error("Unauthorized: Cashiers cannot perform stock adjustments.");
    }
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "stockAdjustments");
    const createdBy = requester.id;
    // ✅ Fetch the current product stock
    const product = await db_1.postgresDb
        .select({
        id: schema_1.products.id,
        stockQuantity: schema_1.products.stockQuantity,
    })
        .from(schema_1.products)
        .where((0, drizzle_orm_1.eq)(schema_1.products.id, adjustmentData.productId))
        .limit(1);
    if (!product.length) {
        throw new Error("Product not found.");
    }
    const currentStock = product[0].stockQuantity || 0;
    const newStock = currentStock + adjustmentData.quantityChange;
    // ✅ Ensure stock does not go negative
    if (newStock < 0) {
        throw new Error("Stock adjustment failed: Insufficient stock.");
    }
    // ✅ Proceed with stock adjustment
    const [newAdjustment] = await db_1.postgresDb
        .insert(schema_1.stockAdjustments)
        .values({
        ...adjustmentData,
        adjustedBy: createdBy,
        createdBy,
        createdAt: new Date(),
    })
        .returning();
    if (!newAdjustment)
        throw new Error("Stock adjustment creation failed.");
    // ✅ Update product stock in the database - Using a direct SQL query for maximum reliability
    console.log(`Updating product ${adjustmentData.productId} stock from ${currentStock} to ${newStock}`);
    try {
        // First attempt with drizzle ORM
        const updatedProduct = await db_1.postgresDb
            .update(schema_1.products)
            .set({ stockQuantity: newStock })
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, adjustmentData.productId))
            .returning();
        console.log(`Product update result: ${JSON.stringify(updatedProduct)}`);
        // Verify the update was successful by directly querying the database
        const verifyProduct = await db_1.postgresDb
            .select({ stockQuantity: schema_1.products.stockQuantity })
            .from(schema_1.products)
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, adjustmentData.productId))
            .limit(1);
        console.log(`Verification query result: Current stock is now ${verifyProduct[0]?.stockQuantity}`);
        if (verifyProduct[0]?.stockQuantity !== newStock) {
            console.error(`Stock update verification failed! Expected ${newStock}, got ${verifyProduct[0]?.stockQuantity}`);
            // If verification failed, try a direct SQL update as a fallback
            console.log("Attempting direct SQL update as fallback");
            await db_1.postgresDb.execute((0, drizzle_orm_1.sql) `UPDATE products SET "stockQuantity" = ${newStock} WHERE id = ${adjustmentData.productId}`);
            // Verify again
            const reverifyProduct = await db_1.postgresDb
                .select({ stockQuantity: schema_1.products.stockQuantity })
                .from(schema_1.products)
                .where((0, drizzle_orm_1.eq)(schema_1.products.id, adjustmentData.productId))
                .limit(1);
            console.log(`Re-verification result: Current stock is now ${reverifyProduct[0]?.stockQuantity}`);
        }
    }
    catch (error) {
        console.error("Error updating product stock:", error);
        throw new Error(`Failed to update product stock: ${error?.message || String(error)}`);
    }
    // Log the current stock level after adjustment for verification
    console.log(`Product ${adjustmentData.productId} stock is now ${newStock} after adjustment`);
    return { stockAdjustment: newAdjustment };
};
exports.createStockAdjustment = createStockAdjustment;
// ✅ **Get All Stock Adjustments**
const getAllStockAdjustments = async (requester, page = 1, limit = 10) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "stockAdjustments");
    const offset = (page - 1) * limit;
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Fetch all stock adjustments (Superadmin) or only the user's
    const [adjustmentData, totalAdjustments] = await Promise.all([
        db_1.postgresDb
            .select()
            .from(schema_1.stockAdjustments)
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.createdBy, requester.id))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.stockAdjustments.createdAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stockAdjustments)
            .where(isSuperadmin ? undefined : (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.createdBy, requester.id)),
    ]);
    return {
        total: totalAdjustments[0].count,
        page,
        perPage: limit,
        stockAdjustments: adjustmentData,
    };
};
exports.getAllStockAdjustments = getAllStockAdjustments;
// ✅ **Get Stock Adjustment by ID**
const getStockAdjustmentById = async (requester, id) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "stockAdjustments", id);
    const isSuperadmin = requester.role === "superadmin";
    const adjustmentData = await db_1.postgresDb
        .select()
        .from(schema_1.stockAdjustments)
        .where(isSuperadmin
        ? (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, id)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, id), (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.createdBy, requester.id)))
        .limit(1);
    if (adjustmentData.length === 0)
        throw new Error("Stock adjustment not found or unauthorized.");
    return adjustmentData[0];
};
exports.getStockAdjustmentById = getStockAdjustmentById;
// ✅ **Update Stock Adjustment**
const updateStockAdjustmentById = async (requester, adjustmentId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "stockAdjustments", adjustmentId);
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Ensure the stock adjustment exists
    const existingAdjustment = await db_1.postgresDb
        .select()
        .from(schema_1.stockAdjustments)
        .where(isSuperadmin
        ? (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, adjustmentId)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, adjustmentId), (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.createdBy, requester.id)))
        .limit(1);
    if (existingAdjustment.length === 0) {
        throw new Error("Stock adjustment not found or unauthorized.");
    }
    // ✅ Update the stock adjustment record
    const updatedAdjustment = await db_1.postgresDb
        .update(schema_1.stockAdjustments)
        .set(updateData)
        .where((0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, adjustmentId))
        .returning();
    if (!updatedAdjustment || updatedAdjustment.length === 0) {
        throw new Error("Update failed: Stock adjustment not found.");
    }
    return { updatedStockAdjustment: updatedAdjustment[0] };
};
exports.updateStockAdjustmentById = updateStockAdjustmentById;
// ✅ **Delete Stock Adjustment**
const deleteStockAdjustmentById = async (requester, id) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "delete", "stockAdjustments", id);
    const isSuperadmin = requester.role === "superadmin";
    // ✅ Ensure the stock adjustment exists
    const existingAdjustment = await db_1.postgresDb
        .select()
        .from(schema_1.stockAdjustments)
        .where(isSuperadmin
        ? (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, id)
        : (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, id), (0, drizzle_orm_1.eq)(schema_1.stockAdjustments.createdBy, requester.id)))
        .limit(1);
    if (existingAdjustment.length === 0) {
        throw new Error("Stock adjustment not found or unauthorized.");
    }
    // ✅ Delete the stock adjustment
    const deletedAdjustment = await db_1.postgresDb
        .delete(schema_1.stockAdjustments)
        .where((0, drizzle_orm_1.eq)(schema_1.stockAdjustments.id, id))
        .returning({ deletedId: schema_1.stockAdjustments.id });
    if (!deletedAdjustment || deletedAdjustment.length === 0) {
        throw new Error("Delete failed: Stock adjustment not found.");
    }
    return { deletedId: deletedAdjustment[0].deletedId };
};
exports.deleteStockAdjustmentById = deleteStockAdjustmentById;
