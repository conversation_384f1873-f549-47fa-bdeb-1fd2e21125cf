<!DOCTYPE html><html lang="en" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/f17064efdca3d6bc.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/7530bd4109138eb6.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-0d97f1fde9f9bfef.js"/><script src="/_next/static/chunks/4bd1b696-49189eafdf628e8c.js" async=""></script><script src="/_next/static/chunks/1517-3583efc2438bbf0f.js" async=""></script><script src="/_next/static/chunks/main-app-98303296995d0400.js" async=""></script><script src="/_next/static/chunks/6754-f7e0c476dd0463b0.js" async=""></script><script src="/_next/static/chunks/1961-cd84205e3c077583.js" async=""></script><script src="/_next/static/chunks/4831-f505eefffe995c13.js" async=""></script><script src="/_next/static/chunks/2093-1824d9d9df58bf0a.js" async=""></script><script src="/_next/static/chunks/5037-71816486b8690fde.js" async=""></script><script src="/_next/static/chunks/8481-575e580e537e4fda.js" async=""></script><script src="/_next/static/chunks/821-32d8295a11b78f8d.js" async=""></script><script src="/_next/static/chunks/app/layout-9a0d06194ce600e1.js" async=""></script><script src="/_next/static/chunks/2261-897e568a99ebc792.js" async=""></script><script src="/_next/static/chunks/3316-8dde992b4acd8d21.js" async=""></script><script src="/_next/static/chunks/9135-95334d94807c45a4.js" async=""></script><script src="/_next/static/chunks/1388-af1ce0ec5a6f66ed.js" async=""></script><script src="/_next/static/chunks/9907-b7f231675fa73123.js" async=""></script><script src="/_next/static/chunks/3288-6262c1edfe41f4bf.js" async=""></script><script src="/_next/static/chunks/2204-62b09a56e5d0daf9.js" async=""></script><script src="/_next/static/chunks/1349-7200226dd41fabc4.js" async=""></script><script src="/_next/static/chunks/2336-e3541a3498b56ec7.js" async=""></script><script src="/_next/static/chunks/2270-ee7eec5f1acfaa57.js" async=""></script><script src="/_next/static/chunks/9873-97ea5af42d381fbd.js" async=""></script><script src="/_next/static/chunks/app/offline/page-b85fd9a42a3d26f1.js" async=""></script><link rel="manifest" href="/manifest.json"/><meta name="theme-color" content="#2563eb"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="NEXAPO POS"/><link rel="apple-touch-icon" href="/images/logo.png"/><title>NEXAPO POS | Business Management System</title><meta name="description" content="Complete business management platform with offline POS capabilities"/><link rel="manifest" href="/manifest.json"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="NEXAPO POS"/><meta name="application-name" content="NEXAPO POS"/><meta name="msapplication-TileColor" content="#2563eb"/><meta name="msapplication-tap-highlight" content="no"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-title" content="NEXAPO POS"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><link rel="icon" href="/favicon.ico" sizes="any"/><link rel="icon" href="/images/logo.png" sizes="192x192" type="image/png"/><link rel="icon" href="/images/logo.png" sizes="512x512" type="image/png"/><link rel="apple-touch-icon" href="/images/logo.png" sizes="180x180" type="image/png"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="h-screen w-screen overflow-x-hidden"><script>((e,t,r,n,o,i,a,s)=>{let u=document.documentElement,c=["light","dark"];function l(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?o.map(e=>i[e]||e):o;r?(u.classList.remove(...n),u.classList.add(i&&i[t]?i[t]:t)):u.setAttribute(e,t)}),s&&c.includes(t)&&(u.style.colorScheme=t)}if(n)l(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;l(n)}catch(e){}})("class","theme","light","light",["light","dark"],null,false,true)</script><div class="fixed inset-0 z-50 flex items-center justify-center bg-white/80"><div class="ant-spin ant-spin-lg ant-spin-spinning css-1d4w9r2" aria-live="polite" aria-busy="true"><span role="img" aria-label="loading" style="font-size:24px" class="anticon anticon-loading anticon-spin ant-spin-dot"><svg viewBox="0 0 1024 1024" focusable="false" data-icon="loading" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg></span></div></div><script>
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', async function() {
                  try {
                    // Unregister any existing service workers first
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for(let registration of registrations) {
                      await registration.unregister();
                    }

                    // Register the new service worker
                    const swPath = '/sw.js';
                    const registration = await navigator.serviceWorker.register(swPath);
                    console.log('Service Worker registration successful with scope: ', registration.scope);

                    // Handle updates
                    registration.addEventListener('updatefound', () => {
                      const newWorker = registration.installing;
                      newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                          console.log('New service worker installed');
                        }
                      });
                    });
                  } catch (error) {
                    console.error('Service Worker registration failed: ', error);
                  }
                });

                // Handle offline/online events
                window.addEventListener('online', function() {
                  console.log('Back online');
                  window.location.reload();
                });

                window.addEventListener('offline', function() {
                  console.log('Gone offline');
                });
              }
            </script><script src="/_next/static/chunks/webpack-0d97f1fde9f9bfef.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[78534,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"1961\",\"static/chunks/1961-cd84205e3c077583.js\",\"4831\",\"static/chunks/4831-f505eefffe995c13.js\",\"2093\",\"static/chunks/2093-1824d9d9df58bf0a.js\",\"5037\",\"static/chunks/5037-71816486b8690fde.js\",\"8481\",\"static/chunks/8481-575e580e537e4fda.js\",\"821\",\"static/chunks/821-32d8295a11b78f8d.js\",\"7177\",\"static/chunks/app/layout-9a0d06194ce600e1.js\"],\"Providers\"]\n3:I[6349,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"1961\",\"static/chunks/1961-cd84205e3c077583.js\",\"4831\",\"static/chunks/4831-f505eefffe995c13.js\",\"2093\",\"static/chunks/2093-1824d9d9df58bf0a.js\",\"5037\",\"static/chunks/5037-71816486b8690fde.js\",\"8481\",\"static/chunks/8481-575e580e537e4fda.js\",\"821\",\"static/chunks/821-32d8295a11b78f8d.js\",\"7177\",\"static/chunks/app/layout-9a0d06194ce600e1.js\"],\"default\"]\n4:I[14638,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"1961\",\"static/chunks/1961-cd84205e3c077583.js\",\"4831\",\"static/chunks/4831-f505eefffe995c13.js\",\"2093\",\"static/chunks/2093-1824d9d9df58bf0a.js\",\"5037\",\"static/chunks/5037-71816486b8690fde.js\",\"8481\",\"static/chunks/8481-575e580e537e4fda.js\",\"821\",\"static/chunks/821-32d8295a11b78f8d.js\",\"7177\",\"static/chunks/app/layout-9a0d06194ce600e1.js\"],\"SidebarProvider\"]\n5:I[91325,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"1961\",\"static/chunks/1961-cd84205e3c077583.js\",\"4831\",\"static/chunks/4831-f505eefffe995c13.js\",\"2093\",\"static/chunks/2093-1824d9d9df58bf0a.js\",\"5037\",\"static/chunks/5037-71816486b8690fde.js\",\"8481\",\"static/chunks/8481-575e580e537e4fda.js\",\"821\",\"static/chunks/821-32d8295a11b78f8d.js\",\"7177\",\"static/chunks/app/layout-9a0d06194ce600e1.js\"],\"\"]\n6:I[15244,[],\"\"]\n7:I[43866,[],\"\"]\n8:I[55037,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"1961\",\"static/chunks/1961-cd84205e3c077583.js\",\"4831\",\"static/chunks/4831-f505eefffe995c13.js\",\"2093\",\"static/chunks/2093-1824d9d9df58bf0a.js\",\"5037\",\"static/chunks/5037-71816486b8690fde.js\",\"8481\",\"static/chunks/8481-575e580e537e4fda.js\",\"821\",\"static/chunks/821-32d8295a11b78f8d.js\",\"7"])</script><script>self.__next_f.push([1,"177\",\"static/chunks/app/layout-9a0d06194ce600e1.js\"],\"Toaster\"]\na:I[57033,[],\"ClientPageRoot\"]\nb:I[68037,[\"6754\",\"static/chunks/6754-f7e0c476dd0463b0.js\",\"2261\",\"static/chunks/2261-897e568a99ebc792.js\",\"3316\",\"static/chunks/3316-8dde992b4acd8d21.js\",\"9135\",\"static/chunks/9135-95334d94807c45a4.js\",\"1388\",\"static/chunks/1388-af1ce0ec5a6f66ed.js\",\"9907\",\"static/chunks/9907-b7f231675fa73123.js\",\"3288\",\"static/chunks/3288-6262c1edfe41f4bf.js\",\"2204\",\"static/chunks/2204-62b09a56e5d0daf9.js\",\"1349\",\"static/chunks/1349-7200226dd41fabc4.js\",\"2336\",\"static/chunks/2336-e3541a3498b56ec7.js\",\"2270\",\"static/chunks/2270-ee7eec5f1acfaa57.js\",\"9873\",\"static/chunks/9873-97ea5af42d381fbd.js\",\"9734\",\"static/chunks/app/offline/page-b85fd9a42a3d26f1.js\"],\"default\"]\ne:I[86213,[],\"OutletBoundary\"]\n10:I[86213,[],\"MetadataBoundary\"]\n12:I[86213,[],\"ViewportBoundary\"]\n14:I[34835,[],\"\"]\n:HL[\"/_next/static/css/f17064efdca3d6bc.css\",\"style\"]\n:HL[\"/_next/static/css/7530bd4109138eb6.css\",\"style\"]\n9:T6f0,\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', async function() {\n                  try {\n                    // Unregister any existing service workers first\n                    const registrations = await navigator.serviceWorker.getRegistrations();\n                    for(let registration of registrations) {\n                      await registration.unregister();\n                    }\n\n                    // Register the new service worker\n                    const swPath = '/sw.js';\n                    const registration = await navigator.serviceWorker.register(swPath);\n                    console.log('Service Worker registration successful with scope: ', registration.scope);\n\n                    // Handle updates\n                    registration.addEventListener('updatefound', () =\u003e {\n                      const newWorker = registration.installing;\n                      newWorker.addEventListener('statechange', () =\u003e {\n                        if (newWorker.state === 'installed' \u0026\u0026 navigator.s"])</script><script>self.__next_f.push([1,"erviceWorker.controller) {\n                          console.log('New service worker installed');\n                        }\n                      });\n                    });\n                  } catch (error) {\n                    console.error('Service Worker registration failed: ', error);\n                  }\n                });\n\n                // Handle offline/online events\n                window.addEventListener('online', function() {\n                  console.log('Back online');\n                  window.location.reload();\n                });\n\n                window.addEventListener('offline', function() {\n                  console.log('Gone offline');\n                });\n              }\n            "])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"8R18q_GZ7Vmiaqp6tG1ol\",\"p\":\"\",\"c\":[\"\",\"offline\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"offline\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/f17064efdca3d6bc.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7530bd4109138eb6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"className\":\"h-full\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/manifest.json\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#2563eb\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-title\",\"content\":\"NEXAPO POS\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"href\":\"/images/logo.png\"}]]}],[\"$\",\"body\",null,{\"suppressHydrationWarning\":true,\"className\":\"h-screen w-screen overflow-x-hidden\",\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[[\"$\",\"$L5\",null,{\"color\":\"#5750F1\",\"showSpinner\":true}],[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[],[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L8\",null,{\"position\":\"top-center\",\"reverseOrder\":false}]]}]}]}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"$9\"}}]]}]]}]]}],{\"children\":[\"offline\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"offline\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$La\",null,{\"Component\":\"$b\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@c\",\"$@d\"]}],null,[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"zRwC2tr96n1dG7B74M9xL\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],null]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"c:{}\nd:{}\n"])</script><script>self.__next_f.push([1,"13:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"1\",{\"children\":\"NEXAPO POS | Business Management System\"}],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"Complete business management platform with offline POS capabilities\"}],[\"$\",\"link\",\"3\",{\"rel\":\"manifest\",\"href\":\"/manifest.json\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"meta\",\"4\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"6\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",\"7\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"NEXAPO POS\"}],[\"$\",\"meta\",\"8\",{\"name\":\"application-name\",\"content\":\"NEXAPO POS\"}],[\"$\",\"meta\",\"9\",{\"name\":\"msapplication-TileColor\",\"content\":\"#2563eb\"}],[\"$\",\"meta\",\"10\",{\"name\":\"msapplication-tap-highlight\",\"content\":\"no\"}],[\"$\",\"meta\",\"11\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"12\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"NEXAPO POS\"}],[\"$\",\"meta\",\"13\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"link\",\"14\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"15\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"sizes\":\"any\"}],[\"$\",\"link\",\"16\",{\"rel\":\"icon\",\"href\":\"/images/logo.png\",\"sizes\":\"192x192\",\"type\":\"image/png\"}],[\"$\",\"link\",\"17\",{\"rel\":\"icon\",\"href\":\"/images/logo.png\",\"sizes\":\"512x512\",\"type\":\"image/png\"}],[\"$\",\"link\",\"18\",{\"rel\":\"apple-touch-icon\",\"href\":\"/images/logo.png\",\"sizes\":\"180x180\",\"type\":\"image/png\"}]]\n"])</script><script>self.__next_f.push([1,"f:null\n"])</script></body></html>