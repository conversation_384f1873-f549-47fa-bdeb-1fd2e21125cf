"use client";

import React, { useEffect } from "react";
import { Form, Input, Button } from "antd";
import { Category } from "@/reduxRTK/services/categoryApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useCategoryCreate } from "@/hooks/categories/useCategoryCreate";
import { useCategoryUpdate } from "@/hooks/categories/useCategoryUpdate";
import "./category-panels.css";

const { TextArea } = Input;

interface CategoryFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  category?: Category | null;
  currentUser?: any;
}

const CategoryFormPanel: React.FC<CategoryFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  category,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!category;

  // Custom hooks for creating and updating categories
  const { createCategory, isCreating } = useCategoryCreate(onSuccess);
  const { updateCategory, isUpdating } = useCategoryUpdate(onSuccess);

  const isLoading = isCreating || isUpdating;

  // Reset form when panel opens/closes or category changes
  useEffect(() => {
    console.log("CategoryFormPanel - isOpen changed:", isOpen);
    if (isOpen) {
      console.log("CategoryFormPanel - Panel is open, category:", category);
      if (category) {
        // In edit mode, pre-fill the form with category data
        form.setFieldsValue({
          name: category.name,
          description: category.description || "",
        });
      } else {
        // In create mode, reset the form
        form.resetFields();
      }
    }
  }, [isOpen, category, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEditMode && category) {
        // Update existing category
        await updateCategory(category.id, values);
      } else {
        // Create new category
        await createCategory(values);
      }

      onClose();
    } catch (error: any) {
      console.error("Form submission error:", error);
    }
  };

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isLoading}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isLoading}
        onClick={handleSubmit}
      >
        {isEditMode ? "Update" : "Create"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={isEditMode ? "Edit Category" : "Add New Category"}
      width="450px" // This will be overridden on mobile by the SlidingPanel component
      footer={panelFooter}
    >
      {/* Form heading with icon */}
      <div className="mb-6 border-b border-gray-200 pb-4">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          {isEditMode ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Editing Category: {category?.name}
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create New Category
            </>
          )}
        </h2>
        <p className="text-gray-600 mt-1">
          {isEditMode
            ? "Update category information using the form below"
            : "Fill in the details to create a new category"}
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        className="text-gray-800 w-full"
        style={{ color: '#333' }}
      >
        <div className="mb-4">
          <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
            Category Information
          </h3>

          <Form.Item
            name="name"
            label="Category Name"
            rules={[{ required: true, message: "Please enter category name" }]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea
              placeholder="Enter category description (optional)"
              rows={4}
            />
          </Form.Item>
        </div>
      </Form>
    </SlidingPanel>
  );
};

export default CategoryFormPanel;
