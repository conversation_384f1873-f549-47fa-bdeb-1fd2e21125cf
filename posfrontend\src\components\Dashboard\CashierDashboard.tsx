"use client";

import React, { useState } from 'react';
import { useGetDashboardStatsQuery } from '@/reduxRTK/services/dashboardApi';
import { useGetAllSalesQuery } from '@/reduxRTK/services/salesApi';
import { useGetAllProductsQuery } from '@/reduxRTK/services/productApi';
import { Spin, Card, Row, Col, Statistic, Button } from 'antd';
import { LoadingOutlined, DollarOutlined, ShoppingCartOutlined, ProductOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '@/reduxRTK/store/store';
import { useRouter } from 'next/navigation';
import AdminSalesChart from './Charts/AdminSalesChart';
import AdminRevenueChart from './Charts/AdminRevenueChart';
import SalesFormPanel from '@/components/Sales/SalesFormPanel';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
dayjs.extend(isBetween);

interface CashierDashboardProps {
  className?: string;
}

const CashierDashboard: React.FC<CashierDashboardProps> = ({ className = "" }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();

  // State for sales panel
  const [isCreateSalePanelOpen, setIsCreateSalePanelOpen] = useState(false);

  // Fetch dashboard stats
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useGetDashboardStatsQuery();

  // Fetch sales data for charts (filtered by cashier)
  const { data: salesData, isLoading: salesLoading } = useGetAllSalesQuery({
    page: 1,
    limit: 1000,
    search: ''
  });

  // Fetch products data
  const { data: productsData, isLoading: productsLoading } = useGetAllProductsQuery({
    page: 1,
    limit: 1000,
    search: ''
  });

  const stats = dashboardData?.data;
  const allSales = salesData?.data?.sales || [];
  const products = productsData?.data?.products || [];

  // Filter sales by current cashier
  const mySales = allSales.filter(sale => sale.createdBy === user?.id);

  if (dashboardLoading || salesLoading || productsLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
      </div>
    );
  }

  if (dashboardError) {
    return (
      <div className="text-center text-red-500 p-8">
        <p>Error loading dashboard data. Please try again.</p>
      </div>
    );
  }

  // Calculate cashier-specific metrics for the current month
  const startOfMonth = dayjs().startOf('month');
  const endOfMonth = dayjs().endOf('month');
  const monthlySales = mySales.filter(sale => dayjs(sale.transactionDate).isBetween(startOfMonth, endOfMonth, null, '[]'));
  const monthlyRevenue = monthlySales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount || '0'), 0);
  const monthlySalesCount = monthlySales.length;
  const monthlyAverageOrder = monthlySalesCount > 0 ? monthlyRevenue / monthlySalesCount : 0;

  // Calculate today's revenue
  const today = dayjs();
  const todaySales = mySales.filter(sale => dayjs(sale.transactionDate).isSame(today, 'day'));
  const todayRevenue = todaySales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount || '0'), 0);

  // Helper to format large numbers (e.g., 49250000 -> 49.3K)
  function formatLargeNumber(num: number, isMoney = false): string {
    let prefix = isMoney ? '₵' : '';
    if (num >= 1_000_000_000) return `${prefix}${(num / 1_000_000_000).toFixed(1)}B`;
    if (num >= 1_000_000) return `${prefix}${(num / 1_000_000).toFixed(1)}M`;
    if (num >= 1_000) return `${prefix}${(num / 1_000).toFixed(1)}k`;
    return isMoney ? `₵${num.toLocaleString()}` : num.toLocaleString();
  }
  function ValueWithTooltip({ value, isMoney = false }: { value: number, isMoney?: boolean }) {
    const formatted = formatLargeNumber(value, isMoney);
    let explanation = '';
    if (formatted.includes('k')) explanation = 'k = thousand';
    if (formatted.includes('M')) explanation = 'M = million';
    if (formatted.includes('B')) explanation = 'B = billion';
    return explanation ? (
      <span title={explanation}>{formatted}</span>
    ) : (
      <>{formatted}</>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back, {user?.name}!</h1>
            <p className="text-gray-600">Ready to serve customers and process sales</p>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <Row gutter={[16, 16]} className="mb-6" justify="center" align="middle">
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full flex flex-col justify-center w-full min-h-[170px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full">
              <Statistic
                title="Today's Revenue"
                value={todayRevenue}
                valueRender={() => <ValueWithTooltip value={todayRevenue} isMoney />}
                precision={2}
                valueStyle={{ color: '#3f8600', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Earnings for today</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full flex flex-col justify-center w-full min-h-[170px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full">
              <Statistic
                title="Revenue (This Month)"
                value={monthlyRevenue}
                valueRender={() => <ValueWithTooltip value={monthlyRevenue} isMoney />}
                precision={2}
                valueStyle={{ color: '#3f8600', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Earnings for this month</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full flex flex-col justify-center w-full min-h-[170px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full">
              <Statistic
                title="Sales (This Month)"
                value={monthlySalesCount}
                valueRender={() => <ValueWithTooltip value={monthlySalesCount} />}
                precision={0}
                valueStyle={{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Transactions this month</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full flex flex-col justify-center w-full min-h-[170px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full">
              <Statistic
                title="Average Order (This Month)"
                value={monthlyAverageOrder}
                valueRender={() => <ValueWithTooltip value={monthlyAverageOrder} isMoney />}
                precision={2}
                valueStyle={{ color: '#722ed1', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Per transaction (monthly)</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 1 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="My Sales Performance" className="shadow-md h-96">
            <AdminSalesChart sales={mySales} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="My Revenue Trends" className="shadow-md h-96">
            <AdminRevenueChart sales={mySales} />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24}>
          <Card title="Quick Actions" className="shadow-md">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                type="primary"
                size="large"
                className="h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700 border-green-600"
                onClick={() => setIsCreateSalePanelOpen(true)}
              >
                <PlusOutlined className="text-xl mb-2" />
                New Sale
              </Button>
              <Button
                size="large"
                className="h-20 flex flex-col items-center justify-center hover:bg-blue-50 border-blue-300"
                onClick={() => router.push('/dashboard/sales')}
              >
                <ShoppingCartOutlined className="text-xl mb-2" />
                View Sales
              </Button>
              <Button
                size="large"
                className="h-20 flex flex-col items-center justify-center hover:bg-purple-50 border-purple-300"
                onClick={() => router.push('/dashboard/products')}
              >
                <ProductOutlined className="text-xl mb-2" />
                Products
              </Button>
              <Button
                size="large"
                className="h-20 flex flex-col items-center justify-center hover:bg-gray-50 border-gray-300"
                onClick={() => router.push('/dashboard/profile')}
              >
                <UserOutlined className="text-xl mb-2" />
                Profile
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Sales Form Panel */}
      <SalesFormPanel
        isOpen={isCreateSalePanelOpen}
        onClose={() => setIsCreateSalePanelOpen(false)}
        onSuccess={() => {
          // Close the panel and optionally refresh data
          setIsCreateSalePanelOpen(false);
          // You could add a refetch here if needed
        }}
      />
    </div>
  );
};

export default CashierDashboard;
