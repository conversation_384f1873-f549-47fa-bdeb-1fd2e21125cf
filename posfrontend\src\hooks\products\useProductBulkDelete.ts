import { useBulkDeleteProductsMutation } from "@/reduxRTK/services/productApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useProductBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting products
  const [bulkDeleteProducts, { isLoading }] = useBulkDeleteProductsMutation();

  const deleteProducts = async (productIds: number[]) => {
    try {
      console.log("Bulk deleting products with IDs:", productIds);
      
      const result = await bulkDeleteProducts(productIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete products");
      }

      showMessage("success", `${productIds.length} products deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete products error:", error);
      showMessage("error", error.message || "Failed to delete products");
      throw error;
    }
  };

  return {
    bulkDeleteProducts: deleteProducts,
    isDeleting: isLoading
  };
};
