(()=>{var e={};e.id=2959,e.ids=[2959],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},61902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o});var s=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let o=["",{children:["dashboard",{children:["receipts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56444)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\receipts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\receipts\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/receipts/page",pathname:"/dashboard/receipts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},72188:(e,t,r)=>{Promise.resolve().then(r.bind(r,56444))},81916:(e,t,r)=>{Promise.resolve().then(r.bind(r,6755))},81045:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var n=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:i}))})},49195:(e,t)=>{"use strict";t.Y=function(e,t){return e.split(",").map(function(e){var t=(e=e.trim()).match(r),i=t[1],n=t[2],l=t[3]||"",c={};return c.inverse=!!i&&"not"===i.toLowerCase(),c.type=n?n.toLowerCase():"all",l=l.match(/\([^\)]+\)/g)||[],c.expressions=l.map(function(e){var t=e.match(s),r=t[1].toLowerCase().match(a);return{modifier:r[1],feature:r[2],value:t[2]}}),c}).some(function(e){var r=e.inverse,s="all"===e.type||t.type===e.type;if(s&&r||!(s||r))return!1;var a=e.expressions.every(function(e){var r=e.feature,s=e.modifier,a=e.value,i=t[r];if(!i)return!1;switch(r){case"orientation":case"scan":return i.toLowerCase()===a.toLowerCase();case"width":case"height":case"device-width":case"device-height":a=o(a),i=o(i);break;case"resolution":a=c(a),i=c(i);break;case"aspect-ratio":case"device-aspect-ratio":case"device-pixel-ratio":a=l(a),i=l(i);break;case"grid":case"color":case"color-index":case"monochrome":a=parseInt(a,10)||1,i=parseInt(i,10)||0}switch(s){case"min":return i>=a;case"max":return i<=a;default:return i===a}});return a&&!r||!a&&r})};var r=/(?:(only|not)?\s*([^\s\(\)]+)(?:\s*and)?\s*)?(.+)?/i,s=/\(\s*([^\s\:\)]+)\s*(?:\:\s*([^\s\)]+))?\s*\)/,a=/^(?:(min|max)-)?(.+)/,i=/(em|rem|px|cm|mm|in|pt|pc)?$/,n=/(dpi|dpcm|dppx)?$/;function l(e){var t,r=Number(e);return r||(r=(t=e.match(/^(\d+)\s*\/\s*(\d+)$/))[1]/t[2]),r}function c(e){var t=parseFloat(e);switch(String(e).match(n)[1]){case"dpcm":return t/2.54;case"dppx":return 96*t;default:return t}}function o(e){var t=parseFloat(e);switch(String(e).match(i)[1]){case"em":case"rem":return 16*t;case"cm":return 96*t/2.54;case"mm":return 96*t/2.54/10;case"in":return 96*t;case"pt":return 72*t;case"pc":return 72*t/12;default:return t}}},70894:(e,t,r)=>{"use strict";var s=r(49195).Y,a="undefined"!=typeof window?window.matchMedia:null;function i(e,t,r){var i,n=this;function l(e){n.matches=e.matches,n.media=e.media}a&&!r&&(i=a.call(window,e)),i?(this.matches=i.matches,this.media=i.media,i.addListener(l)):(this.matches=s(e,t),this.media=e),this.addListener=function(e){i&&i.addListener(e)},this.removeListener=function(e){i&&i.removeListener(e)},this.dispose=function(){i&&i.removeListener(l)}}e.exports=function(e,t,r){return new i(e,t,r)}},6755:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ep});var s=r(45512),a=r(58009),i=r(6987),n=r(37764),l=r(3117),c=r(21419),o=r(79203),d=r(58733),x=r(56403),m=r(88752),p=r(59022),h=r(60165),u=r(97071),g=r(91054),f=r(56335),y=r(70894),b=r.n(y),j=/[A-Z]/g,v=/^ms-/,w={};function N(e){return"-"+e.toLowerCase()}let A=function(e){if(w.hasOwnProperty(e))return w[e];var t=e.replace(j,N);return w[e]=v.test(t)?"-"+t:t};var k=r(28200),C=r.n(k);let R=C().oneOfType([C().string,C().number]),E={all:C().bool,grid:C().bool,aural:C().bool,braille:C().bool,handheld:C().bool,print:C().bool,projection:C().bool,screen:C().bool,tty:C().bool,tv:C().bool,embossed:C().bool},{type:P,...D}={orientation:C().oneOf(["portrait","landscape"]),scan:C().oneOf(["progressive","interlace"]),aspectRatio:C().string,deviceAspectRatio:C().string,height:R,deviceHeight:R,width:R,deviceWidth:R,color:C().bool,colorIndex:C().bool,monochrome:C().bool,resolution:R,type:Object.keys(E)},S={minAspectRatio:C().string,maxAspectRatio:C().string,minDeviceAspectRatio:C().string,maxDeviceAspectRatio:C().string,minHeight:R,maxHeight:R,minDeviceHeight:R,maxDeviceHeight:R,minWidth:R,maxWidth:R,minDeviceWidth:R,maxDeviceWidth:R,minColor:C().number,maxColor:C().number,minColorIndex:C().number,maxColorIndex:C().number,minMonochrome:C().number,maxMonochrome:C().number,minResolution:R,maxResolution:R,...D};var z={all:{...E,...S}};let M=e=>`not ${e}`,O=(e,t)=>{let r=A(e);return("number"==typeof t&&(t=`${t}px`),!0===t)?r:!1===t?M(r):`(${r}: ${t})`},L=e=>e.join(" and "),I=e=>{let t=[];return Object.keys(z.all).forEach(r=>{let s=e[r];null!=s&&t.push(O(r,s))}),L(t)},T=(0,a.createContext)(void 0),$=e=>e.query||I(e),_=e=>{if(e)return Object.keys(e).reduce((t,r)=>(t[A(r)]=e[r],t),{})},W=()=>{let e=(0,a.useRef)(!1);return(0,a.useEffect)(()=>{e.current=!0},[]),e.current},B=e=>{let t=(0,a.useContext)(T),r=()=>_(e)||_(t),[s,i]=(0,a.useState)(r);return(0,a.useEffect)(()=>{let e=r();!function(e,t){if(e===t)return!0;if(!e||!t)return!1;let r=Object.keys(e),s=Object.keys(t),a=r.length;if(s.length!==a)return!1;for(let s=0;s<a;s++){let a=r[s];if(e[a]!==t[a]||!Object.prototype.hasOwnProperty.call(t,a))return!1}return!0}(s,e)&&i(e)},[e,t]),s},H=e=>{let t=()=>$(e),[r,s]=(0,a.useState)(t);return(0,a.useEffect)(()=>{let e=t();r!==e&&s(e)},[e]),r},q=(e,t)=>{let r=()=>b()(e,t||{},!!t),[s,i]=(0,a.useState)(r),n=W();return(0,a.useEffect)(()=>{if(n){let e=r();return i(e),()=>{e&&e.dispose()}}},[e,t]),s},F=e=>{let[t,r]=(0,a.useState)(e.matches);return(0,a.useEffect)(()=>{let t=e=>{r(e.matches)};return e.addListener(t),r(e.matches),()=>{e.removeListener(t)}},[e]),t},V=(e,t,r)=>{let s=B(t),i=H(e);if(!i)throw Error("Invalid or missing MediaQuery!");let n=q(i,s),l=F(n),c=W();return(0,a.useEffect)(()=>{c&&r&&r(l)},[l]),(0,a.useEffect)(()=>()=>{n&&n.dispose()},[]),l};var Y=r(48752),G=r(77067),J=r(70001),U=r(25421),K=r(45905),Q=r(63440),X=r(81045),Z=r(25834),ee=r(86977),et=r(24648),er=r(63844),es=r(73542),ea=r(92273),ei=r(16589),en=r.n(ei);let el=(e,t="MMM D, YYYY h:mm A")=>{if(!e)return"N/A";let r=en()(e);return r.isValid()?r.format(t):"N/A"},ec=({receipts:e,loading:t,onViewReceipt:r,onPrintReceipt:i,onDelete:n,onBulkDelete:c,isMobile:o=!1})=>{let d=(0,es.E)(),{user:x}=(0,ea.d4)(e=>e.auth),[m,p]=(0,a.useState)([]),[h,u]=(0,a.useState)(!1),f=x?.role==="admin"||x?.role==="superadmin",y=t=>{let r=t.target.checked;u(r),r?p(f?e.map(e=>e.id):[]):p([])},b=(e,t)=>{t?p(t=>[...t,e]):p(t=>t.filter(t=>t!==e))};return(0,s.jsxs)("div",{className:"overflow-hidden bg-white",children:[m.length>0&&f&&(0,s.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[m.length," ",1===m.length?"receipt":"receipts"," selected"]}),(0,s.jsx)(l.Ay,{type:"primary",danger:!0,icon:(0,s.jsx)(U.A,{}),onClick:()=>{m.length>0&&c?(c(m),p([]),u(!1)):Y.Ay.warning({message:"No receipts selected",description:"Please select at least one receipt to delete."})},className:"ml-2",children:"Delete Selected"})]}),o||d?(0,s.jsxs)(er.jB,{columns:f&&n?"50px 120px 150px 120px 120px 150px":"120px 150px 120px 120px 150px",minWidth:f&&n?"750px":"700px",children:[f&&n&&(0,s.jsx)(er.A0,{className:"text-center",children:(0,s.jsx)(G.A,{checked:h,onChange:y,disabled:0===e.length})}),(0,s.jsx)(er.A0,{children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"mr-1"}),"Receipt ID"]})}),(0,s.jsx)(er.A0,{children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(K.A,{className:"mr-1"}),"Sale ID"]})}),(0,s.jsx)(er.A0,{children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(Q.A,{className:"mr-1"}),"Store"]})}),(0,s.jsx)(er.A0,{children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(X.A,{className:"mr-1"}),"Date"]})}),(0,s.jsx)(er.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,s.jsxs)(er.Hj,{selected:m.includes(e.id),children:[f&&n&&(0,s.jsx)(er.nA,{className:"text-center",children:(0,s.jsx)(G.A,{checked:m.includes(e.id),onChange:t=>b(e.id,t.target.checked)})}),(0,s.jsx)(er.nA,{children:(0,s.jsxs)("span",{className:"font-medium",children:["#",e.id]})}),(0,s.jsx)(er.nA,{children:(0,s.jsxs)("span",{className:"text-blue-600",children:["#",e.saleId]})}),(0,s.jsx)(er.nA,{children:(0,s.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.storeName})}),(0,s.jsx)(er.nA,{children:(0,s.jsx)("span",{className:"text-sm",children:el(e.createdAt)})}),(0,s.jsx)(er.nA,{className:"text-right",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,s.jsx)(J.A,{title:"View Receipt",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(Z.A,{}),onClick:()=>r(e.receiptUrl),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),(0,s.jsx)(J.A,{title:"Print Receipt",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(g.A,{}),onClick:()=>i(e.receiptUrl),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),f&&n&&(0,s.jsx)(J.A,{title:"Delete",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(ee.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[f&&n&&(0,s.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,s.jsx)(G.A,{checked:h,onChange:y,disabled:0===e.length})}),(0,s.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"mr-1"}),"Receipt ID"]})}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(K.A,{className:"mr-1"}),"Sale ID"]})}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(Q.A,{className:"mr-1"}),"Store"]})}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(et.A,{className:"mr-1"}),"Created By"]})}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(X.A,{className:"mr-1"}),"Date"]})}),(0,s.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("tr",{className:m.includes(e.id)?"bg-blue-50":"",children:[f&&n&&(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:(0,s.jsx)(G.A,{checked:m.includes(e.id),onChange:t=>b(e.id,t.target.checked)})}),(0,s.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,s.jsxs)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:["#",e.id]})}),(0,s.jsxs)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:["#",e.saleId]}),(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.storeName||"N/A"}),(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.createdBy||"N/A"}),(0,s.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:el(e.createdAt)}),(0,s.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,s.jsx)(J.A,{title:"View Receipt",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(Z.A,{}),onClick:()=>r(e.receiptUrl),type:"text",className:"view-button text-green-500",size:"middle"})}),(0,s.jsx)(J.A,{title:"Print Receipt",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(g.A,{}),onClick:()=>i(e.receiptUrl),type:"text",className:"edit-button text-blue-500",size:"middle"})}),f&&n&&(0,s.jsx)(J.A,{title:"Delete",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(ee.A,{}),onClick:()=>n(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})})]})};var eo=r(49792);let ed=e=>{let[t,{isLoading:r}]=(0,f.iB)();return{deleteReceipt:async r=>{try{console.log("Deleting receipt with ID:",r);let s=await t(r).unwrap();if(!s.success)throw Error(s.message||"Failed to delete receipt");return(0,eo.r)("success","Receipt deleted successfully"),e&&e(),s.data}catch(e){throw console.error("Delete receipt error:",e),(0,eo.r)("error",e.message||"Failed to delete receipt"),e}},isDeleting:r}},ex=e=>{let[t,{isLoading:r}]=(0,f.rN)();return{bulkDeleteReceipts:async r=>{try{console.log("Bulk deleting receipts with IDs:",r);let s=await t(r).unwrap();if(!s.success)throw Error(s.message||"Failed to delete receipts");return(0,eo.r)("success",`${r.length} receipts deleted successfully`),e&&e(),s.data}catch(e){throw console.error("Bulk delete receipts error:",e),(0,eo.r)("error",e.message||"Failed to delete receipts"),e}},isDeleting:r}};var em=r(51531);function ep(){let[e,t]=(0,a.useState)(1),[r]=(0,a.useState)(10),[y,b]=(0,a.useState)(""),[j,v]=(0,a.useState)(null),[w,N]=(0,a.useState)(!1),A=V({maxWidth:768}),[k,C]=(0,a.useState)(!1),[R,E]=(0,a.useState)(null),[P,D]=(0,a.useState)(!1),[S,z]=(0,a.useState)([]),{data:M,isLoading:O,refetch:L}=(0,f.sK)({page:e,limit:r,search:y}),{deleteReceipt:I,isDeleting:T}=ed(()=>{C(!1),L()}),{bulkDeleteReceipts:$,isDeleting:_}=ex(()=>{D(!1),L()}),W=async()=>{N(!0),await L(),N(!1)},B=e=>{t(e)},H=e=>{let t=document.createElement("iframe");t.style.display="none",document.body.appendChild(t),t.onload=()=>{t.contentWindow&&(t.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${e}" alt="Receipt" />
            </body>
          </html>
        `),t.contentWindow.document.close(),setTimeout(()=>{t.contentWindow&&(t.contentWindow.focus(),t.contentWindow.print(),setTimeout(()=>{document.body.removeChild(t)},1e3))},500))},t.src="about:blank"},q=()=>{v(null)},F=async()=>{if(R)try{await I(R)}catch(e){console.error("Error deleting receipt:",e)}},Y=async()=>{if(console.log("confirmBulkDelete called with receipts:",S),S.length>0)try{await $(S)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,s.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,s.jsx)(i.A,{title:(0,s.jsx)("span",{className:"text-gray-800",children:"Receipt Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:A?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,s.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,s.jsxs)("div",{className:"sticky top-0 z-10 mb-4 flex items-center justify-between border-b border-gray-200 bg-gray-50 px-3 py-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(n.A,{placeholder:"Search receipts...",prefix:(0,s.jsx)(d.A,{className:"text-gray-400"}),value:y,onChange:e=>{b(e.target.value)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:A?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,s.jsx)("span",{className:"text-gray-600",children:"\xd7"})}}),y&&(0,s.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',y,'"']})]}),(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(x.A,{spin:w}),onClick:W,loading:O&&!w,className:"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",title:"Refresh receipts",children:!A&&"Refresh"})]}),O?(0,s.jsx)("div",{className:"flex h-60 items-center justify-center bg-white",children:(0,s.jsx)(c.A,{indicator:(0,s.jsx)(m.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,s.jsxs)(s.Fragment,{children:[M?.data?.receipts&&M.data.receipts.length>0?(0,s.jsx)(ec,{receipts:M.data.receipts,loading:O,onViewReceipt:e=>{v(e)},onPrintReceipt:H,onDelete:e=>{E(e),C(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with receiptIds:",e),z(e),D(!0)},isMobile:A}):(0,s.jsx)("div",{className:"flex h-60 flex-col items-center justify-center rounded-md border border-gray-200 bg-white text-gray-600",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:"No receipts found matching your search criteria."}),(0,s.jsx)(l.Ay,{type:"primary",onClick:()=>b(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,s.jsx)("p",{children:"No receipts found."})}),M?.data?.receipts&&M.data.receipts.length>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-gray-50 px-4 py-3 sm:px-6",children:[(0,s.jsxs)("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*r+1})," ","to"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*r,M.data.total)})," ","of"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:M.data.total})," ","receipts"]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("nav",{className:"relative z-0 inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[(0,s.jsxs)("button",{onClick:()=>B(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ${1===e?"cursor-not-allowed text-gray-400":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Previous"}),(0,s.jsx)(p.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,Math.ceil(M.data.total/r))},(t,r)=>{let a=r+1;return(0,s.jsx)("button",{onClick:()=>B(a),className:`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${e===a?"z-10 border-blue-500 bg-blue-50 text-blue-600":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`,children:a},a)}),(0,s.jsxs)("button",{onClick:()=>B(e+1),disabled:e>=Math.ceil(M.data.total/r),className:`relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ${e>=Math.ceil(M.data.total/r)?"cursor-not-allowed text-gray-400":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Next"}),(0,s.jsx)(h.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,s.jsxs)("div",{className:"flex w-full items-center justify-between sm:hidden",children:[(0,s.jsx)("button",{onClick:()=>B(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ${1===e?"cursor-not-allowed bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"}`,children:"Previous"}),(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of"," ",Math.ceil(M.data.total/r)]}),(0,s.jsx)("button",{onClick:()=>B(e+1),disabled:e>=Math.ceil(M.data.total/r),className:`relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ${e>=Math.ceil(M.data.total/r)?"cursor-not-allowed bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"}`,children:"Next"})]})]})]})]})}),(0,s.jsx)(em.A,{isOpen:k,onClose:()=>{C(!1),E(null)},onConfirm:F,title:"Delete Receipt",message:"Are you sure you want to delete this receipt? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:T,type:"danger"}),(0,s.jsx)(em.A,{isOpen:P,onClose:()=>{D(!1),z([])},onConfirm:Y,title:"Delete Multiple Receipts",message:`Are you sure you want to delete ${S.length} receipts? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:_,type:"danger"}),j&&(0,s.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black opacity-50 transition-opacity duration-300",onClick:q}),(0,s.jsxs)("div",{className:"absolute bottom-0 right-0 top-0 flex translate-x-0 transform flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out",style:{width:A?"100vw":"500px"},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 bg-gray-50 px-4 py-3",children:[(0,s.jsx)("h2",{className:"truncate text-lg font-medium text-gray-800",children:"Receipt Preview"}),(0,s.jsx)(l.Ay,{type:"text",icon:(0,s.jsx)(u.A,{style:{color:"#333"}}),onClick:q,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto bg-white p-4",children:[(0,s.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,s.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,s.jsx)(g.A,{className:"mr-2"}),"Receipt Details"]}),(0,s.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"View and print your receipt"})]}),(0,s.jsx)("div",{className:"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-4",children:(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(o.A,{src:j,alt:"Receipt",className:"max-w-full",preview:!1})})})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 bg-gray-50 px-4 py-3",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(l.Ay,{onClick:q,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),(0,s.jsx)(l.Ay,{type:"primary",icon:(0,s.jsx)(g.A,{}),onClick:()=>H(j),children:"Print"})]})})]})]})]})}r(64807)},51531:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(45512);r(58009);var a=r(88206),i=r(3117),n=r(75238);let l=({isOpen:e,onClose:t,onConfirm:r,title:l,message:c,confirmText:o="Confirm",cancelText:d="Cancel",isLoading:x=!1,type:m="danger"})=>(0,s.jsx)(a.A,{title:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{style:{color:"danger"===m?"#ff4d4f":"warning"===m?"#faad14":"#1890ff",marginRight:8}}),(0,s.jsx)("span",{children:l})]}),open:e,onCancel:t,footer:[(0,s.jsx)(i.Ay,{onClick:t,disabled:x,children:d},"cancel"),(0,s.jsx)(i.Ay,{type:"danger"===m?"primary":"default",danger:"danger"===m,onClick:r,loading:x,children:o},"confirm")],maskClosable:!x,closable:!x,centered:!0,children:(0,s.jsx)("p",{className:"my-4",children:c})})},63844:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,Hj:()=>c,jB:()=>i,nA:()=>l});var s=r(45512);r(58009);var a=r(44195);let i=({children:e,columns:t,className:r,minWidth:i="800px"})=>(0,s.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",r),children:(0,s.jsx)("div",{className:(0,a.cn)("gap-0","block"),style:{},children:e})}),n=({children:e,className:t,sticky:r})=>(0,s.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",r&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[r],t),children:e}),l=({children:e,className:t,sticky:r})=>(0,s.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",r&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[r],t),children:e}),c=({children:e,className:t,selected:r=!1,onClick:i})=>(0,s.jsx)("div",{className:(0,a.cn)("contents",r&&"bg-blue-50",i&&"cursor-pointer hover:bg-gray-50",t),onClick:i,children:e})},73542:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(58009);let a=()=>{let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},56444:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\receipts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\receipts\\page.tsx","default")},64807:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,5050,984,5482,106,4286],()=>r(61902));module.exports=s})();