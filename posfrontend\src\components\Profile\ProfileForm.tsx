"use client";

import React, { useState } from "react";
import { Form, Input, Button } from "antd";
import { User } from "@/types/user";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { setUser } from "@/reduxRTK/services/authSlice";
import { useUpdateUserMutation } from "@/reduxRTK/services/authApi";
import { showMessage } from "@/utils/showMessage";
import { formatPhoneNumber, formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";

// Define the API response type
interface ApiResponse {
  success: boolean;
  message: string;
  data?: {
    updatedUser: User;
  };
}

interface ProfileFormProps {
  user: User;
  onProfileUpdated?: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ user, onProfileUpdated }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const [updateUser] = useUpdateUserMutation();

  // Get the current access token from Redux store at the component level
  // This is the correct way to use hooks - at the top level of the component
  const currentAccessToken = useSelector((state: RootState) => state.auth.accessToken) || '';

  // Initialize form with user data
  React.useEffect(() => {
    // Log the raw user object for debugging
    console.log("ProfileForm - Raw user object:", user);
    console.log("ProfileForm - Raw user object (stringified):", JSON.stringify(user, null, 2));

    // Format the phone number to local format (starting with 0)
    const phoneValue = formatPhoneNumber(user.phone);
    const displayPhoneValue = formatPhoneNumberForDisplay(user.phone);

    console.log("ProfileForm - Phone value:", {
      originalPhone: user.phone,
      phoneType: typeof user.phone,
      formattedPhone: phoneValue,
      displayPhone: displayPhoneValue
    });

    form.setFieldsValue({
      name: user.name,
      email: user.email,
      phone: phoneValue,
    });

    // Log form values for debugging
    console.log("ProfileForm - Form values set:", {
      name: user.name,
      email: user.email,
      phone: phoneValue,
    });

    // Force a refresh of the form values
    setTimeout(() => {
      form.setFieldsValue({
        name: user.name,
        email: user.email,
        phone: phoneValue,
      });
      console.log("ProfileForm - Form values refreshed:", {
        name: user.name,
        email: user.email,
        phone: phoneValue
      });
    }, 500);
  }, [form, user]);

  const onFinish = async (values: any) => {
    setLoading(true);

    // Set a flag to indicate we're updating the profile
    // This will prevent any redirects during the update
    (window as any).__PROFILE_UPDATE_IN_PROGRESS = true;
    console.log("ProfileForm - Profile update in progress flag set");

    try {
      // Format the phone number to local format (starting with 0)
      const phoneValue = formatPhoneNumber(values.phone);

      // Log the values being sent to the API
      console.log("ProfileForm - Updating user with values:", {
        userId: user.id,
        name: values.name,
        originalPhone: values.phone,
        formattedPhone: phoneValue,
        phoneType: typeof phoneValue
      });

      // Make API call to update user profile
      const response = await updateUser({
        userId: user.id,
        data: {
          name: values.name,
          phone: phoneValue
        }
      });

      // Log the response for debugging
      console.log("ProfileForm - Update response:", response);

      // Handle successful response
      if ('data' in response) {
        const result = response.data as ApiResponse;

        if (result.success) {
          // Extract the updated user data from the response
          const updatedUser = result.data?.updatedUser;

          if (updatedUser) {
            // Log the updated user data from the backend
            console.log("ProfileForm - Updated user data from backend:", updatedUser);
            console.log("ProfileForm - Updated user data (stringified):", JSON.stringify(updatedUser, null, 2));

            // Ensure all fields are properly defined
            const phoneValue = updatedUser.phone || user.phone || "";
            const createdAtValue = user.createdAt || updatedUser.createdAt || "";
            const lastPaymentDateValue = user.lastPaymentDate || updatedUser.lastPaymentDate || undefined;
            const nextPaymentDueValue = user.nextPaymentDue || updatedUser.nextPaymentDue || null;
            const createdByValue = user.createdBy || updatedUser.createdBy || undefined;

            console.log("ProfileForm - Critical fields:", {
              updatedUserPhone: updatedUser.phone,
              userPhone: user.phone,
              finalPhone: phoneValue,
              updatedUserCreatedAt: updatedUser.createdAt,
              userCreatedAt: user.createdAt,
              finalCreatedAt: createdAtValue,
              updatedUserLastPaymentDate: updatedUser.lastPaymentDate,
              userLastPaymentDate: user.lastPaymentDate,
              finalLastPaymentDate: lastPaymentDateValue,
              updatedUserNextPaymentDue: updatedUser.nextPaymentDue,
              userNextPaymentDue: user.nextPaymentDue,
              finalNextPaymentDue: nextPaymentDueValue,
              updatedUserCreatedBy: updatedUser.createdBy,
              userCreatedBy: user.createdBy,
              finalCreatedBy: createdByValue
            });

            // Update user in Redux store with the actual data from backend
            // Preserve the original payment status to prevent redirect
            const updatedUserData = {
              ...user,
              name: updatedUser.name,
              phone: phoneValue,
              createdAt: createdAtValue,
              lastPaymentDate: lastPaymentDateValue,
              nextPaymentDue: nextPaymentDueValue,
              createdBy: createdByValue,
              // Keep original payment status to prevent redirect
              paymentStatus: user.paymentStatus
            };

            console.log("ProfileForm - Updating Redux store with:", updatedUserData);
            console.log("ProfileForm - Updating Redux store with (stringified):", JSON.stringify(updatedUserData, null, 2));

            // Log the token we're using (from the top-level hook)
            console.log("ProfileForm - Using access token from Redux:",
              currentAccessToken ? "Token exists (not showing for security)" : "No token found");

            // Update user in Redux without changing the token
            dispatch(setUser({
              user: updatedUserData,
              accessToken: currentAccessToken
            }));

            // Show success message using toast instead of modal
            showMessage("success", result.message || "Profile updated successfully");

            // Format the phone number for the form
            const formattedPhone = formatPhoneNumber(updatedUser.phone);

            // Update form values with the latest data
            form.setFieldsValue({
              name: updatedUser.name,
              email: updatedUser.email,
              phone: formattedPhone
            });

            console.log("ProfileForm - Form values updated after save:", {
              name: updatedUser.name,
              email: updatedUser.email,
              originalPhone: updatedUser.phone,
              formattedPhone
            });

            // Call the onProfileUpdated callback if provided
            if (onProfileUpdated) {
              onProfileUpdated();
            }
          }
        } else {
          // Handle unsuccessful response with success: false
          showMessage("error", result.message || "Failed to update profile");
        }
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);

      // Extract error message from the error response if available
      const errorMessage = error.data?.message ||
                          error.message ||
                          "Failed to update profile. Please try again.";

      showMessage("error", errorMessage);
    } finally {
      setLoading(false);

      // Clear the profile update flag after a short delay
      // This ensures all state updates have been processed
      setTimeout(() => {
        (window as any).__PROFILE_UPDATE_IN_PROGRESS = false;
        console.log("ProfileForm - Profile update in progress flag cleared");
      }, 1000);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="text-gray-800"
    >
      <div className="grid grid-cols-1 gap-4">
        <Form.Item
          name="name"
          label={<span className="text-gray-700">Full Name</span>}
          rules={[{ required: true, message: "Please enter your name" }]}
        >
          <Input className="bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400" />
        </Form.Item>

        <Form.Item
          name="email"
          label={<span className="text-gray-700">Email Address</span>}
          rules={[
            { required: true, message: "Please enter your email" },
            { type: "email", message: "Please enter a valid email" },
          ]}
        >
          <Input
            className="bg-gray-100 border-gray-300 text-gray-600"
            disabled={true}
            title="Email cannot be changed"
          />
        </Form.Item>

        <Form.Item
          name="phone"
          label={<span className="text-gray-700">Phone Number</span>}
          rules={[
            { required: true, message: "Please enter your phone number" },
            { min: 5, message: "Phone number must be at least 5 digits" },
            { max: 20, message: "Phone number must be at most 20 digits" },
            {
              pattern: /^[0-9+\-\s()]*$/,
              message: "Phone number can only contain digits, spaces, and the characters +, -, (, )"
            }
          ]}
          tooltip="Enter your phone number starting with 0 (e.g., ************) or with country code (e.g., +233 20 123 4567)"
        >
          <Input
            className="bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"
            placeholder="e.g., ************"
          />
        </Form.Item>
      </div>

      <Form.Item className="mt-4">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          className="bg-blue-600 hover:bg-blue-700 border-none text-white"
        >
          Save Changes
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ProfileForm;
