"use client";

import { useUpdateSupplierMutation, UpdateSupplierDto } from "@/reduxRTK/services/supplierApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useSupplierUpdate = (onSuccess?: () => void) => {
  // RTK Query hook for updating a supplier
  const [updateSupplier, { isLoading }] = useUpdateSupplierMutation();

  const updateExistingSupplier = async (supplierId: number, supplierData: UpdateSupplierDto) => {
    try {
      const result = await updateSupplier({
        supplierId,
        data: supplierData
      }).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to update supplier");
      }

      showMessage("success", "Supplier updated successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Update supplier error:", error);
      showMessage("error", error.message || "Failed to update supplier");
      throw error;
    }
  };

  return {
    updateSupplier: updateExistingSupplier,
    isUpdating: isLoading
  };
};
