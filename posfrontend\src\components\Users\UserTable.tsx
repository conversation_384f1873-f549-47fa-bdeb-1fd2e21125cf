"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ip, Space, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,
  DeleteFilled,
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { User, PaymentStatus, UserRole } from "@/types/user";
import dayjs from "dayjs";
import { formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";

interface UserTableProps {
  users: User[];
  isMobile: boolean;
  onView: (userId: number) => void;
  onEdit: (user: User) => void;
  onDelete: (userId: number) => void;
  onBulkDelete?: (userIds: number[]) => void;
  canManageUser: (role: UserRole) => boolean;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  isMobile: propIsMobile,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  canManageUser,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;
  // State for selected users
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all users that the user can manage
      const selectableUserIds = users
        .filter(user => canManageUser(user.role))
        .map(user => user.id);
      setSelectedUsers(selectableUserIds);
    } else {
      // Deselect all users
      setSelectedUsers([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (userId: number, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedUsers.length > 0 && onBulkDelete) {
      onBulkDelete(selectedUsers);
      setSelectedUsers([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No users selected',
        description: 'Please select at least one user to delete.',
      });
    }
  };

  // Helper functions for rendering tags
  const getPaymentStatusTag = (status: PaymentStatus) => {
    switch (status) {
      case "paid":
        return <Tag className="status-tag status-paid" icon={<CheckCircleOutlined />} color="success">Paid</Tag>;
      case "pending":
        return <Tag className="status-tag status-pending" icon={<ClockCircleOutlined />} color="warning">Pending</Tag>;
      case "overdue":
        return <Tag className="status-tag status-overdue" icon={<WarningOutlined />} color="error">Overdue</Tag>;
      case "inactive":
        return <Tag className="status-tag status-inactive" icon={<StopOutlined />} color="default">Inactive</Tag>;
      default:
        return <Tag className="status-tag" color="default">{status}</Tag>;
    }
  };

  const getRoleTag = (role: UserRole) => {
    switch (role) {
      case "superadmin":
        return <Tag color="purple">Super Admin</Tag>;
      case "admin":
        return <Tag color="blue">Admin</Tag>;
      case "cashier":
        return <Tag color="green">Cashier</Tag>;
      default:
        return <Tag color="default">{role}</Tag>;
    }
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when users are selected */}
      {selectedUsers.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedUsers.length} {selectedUsers.length === 1 ? 'user' : 'users'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 100px 100px 150px"
          minWidth="700px"
        >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={users.filter(user => canManageUser(user.role)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          Name
        </TableHeader>
        {!isMobile && (
          <>
            <TableHeader>
              Email
            </TableHeader>
            <TableHeader>
              Phone
            </TableHeader>
            <TableHeader>
              Created At
            </TableHeader>
          </>
        )}
        <TableHeader>
          Role
        </TableHeader>
        <TableHeader>
          Status
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
          {/* Mobile Rows */}
          {users.map((user) => (
            <TableRow
              key={user.id}
              selected={selectedUsers.includes(user.id)}
            >
              <TableCell className="text-center">
                {canManageUser(user.role) && (
                  <Checkbox
                    checked={selectedUsers.includes(user.id)}
                    onChange={(e) => handleCheckboxChange(user.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {user.name}
                </div>
              </TableCell>
              <TableCell>
                {getRoleTag(user.role)}
              </TableCell>
              <TableCell>
                {getPaymentStatusTag(user.paymentStatus)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(user.id)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  {canManageUser(user.role) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(user)}
                          type="text"
                          className="edit-button text-blue-500 hover:text-blue-400"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(user.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto w-full">
          <table className="min-w-[900px] w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={users.filter(user => canManageUser(user.role)).length === 0}
                  />
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-white shadow-md px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Name
                </th>

                {/* Email Column - Hide on screens < lg */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell">
                  Email
                </th>

                {/* Phone Column - Hide on screens < lg */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden lg:table-cell">
                  Phone
                </th>

                {/* Created At Column - Hide on screens < xl */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider hidden xl:table-cell">
                  Created At
                </th>

                {/* Role Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Role
                </th>

                {/* Status Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Status
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-white shadow-md px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className={selectedUsers.includes(user.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canManageUser(user.role) && (
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onChange={(e) => handleCheckboxChange(user.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] truncate overflow-hidden text-ellipsis">
                      {user.name}
                    </div>
                  </td>

                  {/* Email Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell">
                    <div className="max-w-[200px] truncate overflow-hidden text-ellipsis">
                      {user.email}
                    </div>
                  </td>

                  {/* Phone Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800 hidden lg:table-cell">
                    <div className="max-w-[140px] truncate overflow-hidden text-ellipsis">
                      {formatPhoneNumberForDisplay(user.phone)}
                    </div>
                  </td>

                  {/* Created At Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800 hidden xl:table-cell">
                    <div className="max-w-[130px] truncate overflow-hidden text-ellipsis">
                      {dayjs(user.createdAt).format("MMM D, YYYY")}
                    </div>
                  </td>

                  {/* Role Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {getRoleTag(user.role)}
                  </td>

                  {/* Payment Status Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {getPaymentStatusTag(user.paymentStatus)}
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white shadow-md px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(user.id)}
                          type="text"
                          className="view-button text-green-700"
                          size="middle"
                        />
                      </Tooltip>
                      {canManageUser(user.role) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(user)}
                              type="text"
                              className="edit-button"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(user.id)}
                              type="text"
                              className="delete-button"
                              danger
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default UserTable;
