(()=>{"use strict";self.fallback=async e=>{switch(e.destination){case"document":return caches.match("/offline.html",{ignoreSearch:!0});case"image":return caches.match("/images/offline-fallback.png",{ignoreSearch:!0});case"audio":return caches.match("/audio/offline-fallback.mp3",{ignoreSearch:!0});case"video":return caches.match("/video/offline-fallback.mp4",{ignoreSearch:!0});case"font":return caches.match("/fonts/offline-fallback.woff2",{ignoreSearch:!0});default:return Response.error()}}})();