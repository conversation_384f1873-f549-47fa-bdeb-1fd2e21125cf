"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Spin } from "antd";
import { ShoppingOutlined, LoadingOutlined } from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { usePurchaseList } from "@/hooks/purchases/usePurchaseList";
import { usePurchaseDelete } from "@/hooks/purchases/usePurchaseDelete";
import { usePurchaseBulkDelete } from "@/hooks/purchases/usePurchaseBulkDelete";
import PurchaseTable from "@/components/Purchases/PurchaseTable";
import PurchasePagination from "@/components/Purchases/PurchasePagination";
import PurchaseFormPanel from "@/components/Purchases/PurchaseFormPanel";
import PurchaseDetailPanel from "@/components/Purchases/PurchaseDetailPanel";
import PurchaseSearch from "@/components/Purchases/PurchaseSearch";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { Purchase } from "@/reduxRTK/services/purchaseApi";
import { UserRole } from "@/types/user";

const PurchasesPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();

  // UI state
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(null);
  const [selectedPurchaseId, setSelectedPurchaseId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Purchase list state and handlers
  const {
    purchases,
    total,
    page,
    limit,
    isLoading,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
  } = usePurchaseList();

  // Debug output
  console.log("PurchasesPage - Current user:", currentUser);
  console.log("PurchasesPage - Purchases:", purchases);
  console.log("PurchasesPage - Total:", total);

  // Delete purchase handler
  const { deletePurchase, isDeleting } = usePurchaseDelete(() => {
    setIsDeleteDialogOpen(false);
    refetch();
  });

  // Bulk delete purchase handler
  const { bulkDeletePurchases, isDeleting: isBulkDeleting } = usePurchaseBulkDelete(() => {
    setIsBulkDeleteDialogOpen(false);
    refetch();
  });

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [purchasesToDelete, setPurchasesToDelete] = useState<number[]>([]);

  // Check if user can add purchases (only admin can add purchases)
  const canAddPurchase = currentUser?.role === "admin";

  // Handle add purchase button click
  const handleAddPurchase = () => {
    setIsAddPanelOpen(true);
  };

  // Handle view purchase button click
  const handleViewPurchase = (purchaseId: number) => {
    setSelectedPurchaseId(purchaseId);
    setIsDetailPanelOpen(true);
  };

  // Handle edit purchase button click
  const handleEditPurchase = (purchase: Purchase) => {
    setSelectedPurchase(purchase);
    setIsEditPanelOpen(true);
  };

  // Handle edit from detail panel
  const handleEditFromDetail = (purchaseId: number) => {
    const purchase = purchases.find(p => p.id === purchaseId) || null;
    if (purchase) {
      setSelectedPurchase(purchase);
      setIsDetailPanelOpen(false);
      setIsEditPanelOpen(true);
    }
  };

  // Handle delete purchase button click
  const handleDeletePurchase = (purchaseId: number) => {
    setSelectedPurchaseId(purchaseId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete purchase
  const confirmDeletePurchase = async () => {
    if (selectedPurchaseId) {
      await deletePurchase(selectedPurchaseId);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedPurchaseId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (purchaseIds: number[]) => {
    console.log("handleBulkDelete called with purchaseIds:", purchaseIds);
    setPurchasesToDelete(purchaseIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with purchases:", purchasesToDelete);

    if (purchasesToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple purchases
        await bulkDeletePurchases(purchasesToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setPurchasesToDelete([]);
  };

  return (
    <div className="p-2 sm:p-4 w-full">
      <Card
        title={<span className="text-gray-800">Purchase Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: { padding: '12px', overflow: 'hidden', backgroundColor: '#ffffff' },
          header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
        }}
        extra={
          canAddPurchase && (
            <Button
              type="primary"
              icon={<ShoppingOutlined />}
              onClick={handleAddPurchase}
              size={isMobile ? "small" : "middle"}
            >
              {isMobile ? "" : "Add Purchase"}
            </Button>
          )
        }
      >
        <div className="w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200">
          {/* Search Component - Always visible */}
          <PurchaseSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex justify-center items-center h-60 bg-gray-50">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
            </div>
          ) : (
            <>
              {/* Purchase Table Component */}
              {purchases.length > 0 ? (
                <PurchaseTable
                  purchases={purchases}
                  loading={false}
                  onView={handleViewPurchase}
                  onEdit={handleEditPurchase}
                  onDelete={handleDeletePurchase}
                  onBulkDelete={handleBulkDelete}
                  isMobile={isMobile}
                />
              ) : (
                <div className="flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800">
                  {searchTerm ? (
                    <>
                      <p>No purchases found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm('')}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No purchases found. {canAddPurchase && "Click 'Add Purchase' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {purchases.length > 0 && (
                <PurchasePagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add Purchase Panel */}
      <PurchaseFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => setIsAddPanelOpen(false)}
        onSuccess={() => {
          setIsAddPanelOpen(false);
          refetch();
        }}
        currentUser={currentUser}
      />

      {/* Edit Purchase Panel */}
      <PurchaseFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);
          refetch();
        }}
        purchase={selectedPurchase}
        currentUser={currentUser}
      />

      {/* View Purchase Details Panel */}
      <PurchaseDetailPanel
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false);
          setSelectedPurchaseId(null);
        }}
        purchaseId={selectedPurchaseId}
        onEdit={handleEditFromDetail}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeletePurchase}
        title="Delete Purchase"
        message="Are you sure you want to delete this purchase? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Purchases"
        message={`Are you sure you want to delete ${purchasesToDelete.length} purchases? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default PurchasesPage;
