import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createExpense,
  getAllExpenses,
  getExpenseById,
  updateExpenseById,
  deleteExpenseById,
  getExpenseStats,
} from "../services/expenseService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleExpenseRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, expenseId, page, limit, search, categoryId, startDate, endDate, ...data } = req.body;
  const requester = req.user as DecodedToken;

  const validModes = ["createnew", "retrieve", "update", "delete", "stats"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      case "createnew": {
        if (!data.title || !data.amount || !data.paymentMethod) {
          return sendResponse(res, 400, false, "Title, amount, and payment method are required.");
        }

        // Use the destructured categoryId, not data.categoryId
        const processedCategoryId = categoryId ? parseInt(categoryId.toString()) : undefined;

        const newExpense = await createExpense(requester, {
          title: data.title,
          description: data.description,
          amount: parseFloat(data.amount),
          categoryId: processedCategoryId,
          expenseDate: data.expenseDate ? new Date(data.expenseDate) : undefined,
          paymentMethod: data.paymentMethod,
          receiptUrl: data.receiptUrl,
          vendor: data.vendor,
          isRecurring: data.isRecurring || false,
          recurringFrequency: data.recurringFrequency,
          tags: data.tags,
          storeId: data.storeId ? parseInt(data.storeId) : undefined,
        });

        return sendResponse(res, 201, true, "Expense created successfully.", newExpense);
      }

      case "retrieve": {
        if (expenseId) {
          const expense = await getExpenseById(requester, Number(expenseId));
          return sendResponse(res, 200, true, "Expense retrieved successfully.", expense);
        } else {
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const filters = {
            search: search || '',
            categoryId: categoryId ? Number(categoryId) : undefined,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
          };

          const expensesData = await getAllExpenses(
            requester,
            pageNum,
            limitNum,
            filters.search,
            filters.categoryId,
            filters.startDate,
            filters.endDate
          );

          return sendResponse(res, 200, true, "Expenses retrieved successfully.", expensesData);
        }
      }

      case "update": {
        if (!expenseId) {
          return sendResponse(res, 400, false, "Expense ID is required for updating.");
        }

        const updateData: any = {};
        if (data.title) updateData.title = data.title;
        if (data.description !== undefined) updateData.description = data.description;
        if (data.amount) updateData.amount = parseFloat(data.amount);
        if (data.categoryId) updateData.categoryId = parseInt(data.categoryId);
        if (data.expenseDate) updateData.expenseDate = new Date(data.expenseDate);
        if (data.paymentMethod) updateData.paymentMethod = data.paymentMethod;
        if (data.receiptUrl !== undefined) updateData.receiptUrl = data.receiptUrl;
        if (data.vendor !== undefined) updateData.vendor = data.vendor;
        if (data.isRecurring !== undefined) updateData.isRecurring = data.isRecurring;
        if (data.recurringFrequency !== undefined) updateData.recurringFrequency = data.recurringFrequency;
        if (data.tags !== undefined) updateData.tags = data.tags;
        if (data.storeId) updateData.storeId = parseInt(data.storeId);

        const updatedExpense = await updateExpenseById(requester, Number(expenseId), updateData);
        return sendResponse(res, 200, true, "Expense updated successfully.", updatedExpense);
      }

      case "delete": {
        const { expenseIds } = req.body;

        if (!expenseId && !expenseIds) {
          return sendResponse(res, 400, false, "Expense ID(s) are required for deletion.");
        }

        const idsToDelete = expenseIds
          ? (Array.isArray(expenseIds) ? expenseIds : [expenseIds])
          : [Number(expenseId)];

        const result = await deleteExpenseById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} expenses deleted successfully.`
          : "Expense deleted successfully.";

        return sendResponse(res, 200, true, message, result);
      }

      case "stats": {
        const filters = {
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
        };

        const stats = await getExpenseStats(requester, filters.startDate, filters.endDate);
        return sendResponse(res, 200, true, "Expense statistics retrieved successfully.", stats);
      }

      default:
        return sendResponse(res, 400, false, "Invalid mode.");
    }
  } catch (error: any) {
    console.error("Error in expense controller:", error);
    return sendResponse(res, 500, false, error.message || "Error processing expense request");
  }
};
