import { postgresDb } from "../db/db";
import { suppliers, userSuppliers } from "../db/schema";
import { eq, count, desc, and, or, sql, inArray, ne } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { authorizeAction } from "../utils/authorizeAction";
import { supplierSchema } from "../validation/schema";

// ✅ **Create Supplier**
export const createSupplier = async (
  requester: JwtPayload,
  supplierData: {
    name: string;
    contactPerson?: string;
    phone: string;
    email?: string;
    address?: string;
  }
) => {
  supplierSchema.parse(supplierData);
  await authorizeAction(requester, "create", "supplier");

  const createdBy = requester.id;

  // 🔹 Check if the supplier already exists by phone
  const existingSupplier = await postgresDb
    .select()
    .from(suppliers)
    .where(eq(suppliers.phone, supplierData.phone))
    .limit(1);

  // 🔹 If email is provided, also check if it already exists
  if (supplierData.email && supplierData.email.trim() !== '') {
    try {
      const existingSupplierByEmail = await postgresDb
        .select()
        .from(suppliers)
        .where(eq(suppliers.email, supplierData.email))
        .limit(1);

      if (existingSupplierByEmail.length > 0) {
        throw new Error("A supplier with this email already exists.");
      }
    } catch (error) {
      console.error("Error checking for duplicate supplier email:", error);
      // Continue with creation even if there's an error checking for duplicates
    }
  }

  if (existingSupplier.length > 0) {
    const supplier = existingSupplier[0];

    // 🔹 Check if the requester is already linked to this supplier
    const userSupplierLink = await postgresDb
      .select()
      .from(userSuppliers)
      .where(
        and(
          eq(userSuppliers.userId, createdBy),
          eq(userSuppliers.supplierId, supplier.id)
        )
      )
      .limit(1);

    if (userSupplierLink.length > 0) {
      throw new Error("You are already linked to this supplier.");
    }

    // 🔹 If supplier exists but user is not linked, create a new link
    await postgresDb.insert(userSuppliers).values({
      userId: createdBy,
      supplierId: supplier.id,
      createdBy,
      createdAt: new Date(),
    });

    return {
      message:
        "Supplier already exists. You have been linked to this supplier.",
      supplier,
    };
  }

  // 🔹 Create a new supplier if it does not exist
  const [newSupplier] = await postgresDb
    .insert(suppliers)
    .values({
      ...supplierData,
      createdBy,
      createdAt: new Date(),
    })
    .returning();

  if (!newSupplier) throw new Error("Supplier creation failed.");

  // 🔹 Link the new supplier to the creator
  await postgresDb.insert(userSuppliers).values({
    userId: createdBy,
    supplierId: newSupplier.id,
    createdBy,
    createdAt: new Date(),
  });

  return {
    message: "Supplier created successfully.",
    supplier: newSupplier,
  };
};

// ✅ **Get All Suppliers**
export const getAllSuppliers = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  await authorizeAction(requester, "getAll", "supplier");

  const offset = (page - 1) * limit;

  // ✅ Count total suppliers for pagination
  let totalQuery;

  if (requester.role === "superadmin") {
    totalQuery = postgresDb.select({ count: count() }).from(suppliers); // ✅ Count all suppliers
  } else {
    totalQuery = postgresDb
      .select({ count: count() })
      .from(userSuppliers)
      .where(eq(userSuppliers.userId, requester.id));
  }

  const totalResult = await totalQuery;
  const total = totalResult[0]?.count ?? 0; // ✅ Avoid "Property 'total' does not exist" error

  // ✅ Fetch suppliers linked to the user (or all if superadmin)
  const supplierQuery = postgresDb
    .select({
      id: suppliers.id,
      name: suppliers.name,
      contactPerson: suppliers.contactPerson,
      phone: suppliers.phone,
      email: suppliers.email,
      address: suppliers.address,
      createdAt: suppliers.createdAt,
    })
    .from(suppliers)
    .innerJoin(userSuppliers, eq(suppliers.id, userSuppliers.supplierId))
    .orderBy(desc(suppliers.createdAt))
    .limit(limit)
    .offset(offset);

  if (requester.role !== "superadmin") {
    supplierQuery.where(eq(userSuppliers.userId, requester.id));
  }

  const supplierData = await supplierQuery;

  return {
    total,
    page,
    perPage: limit,
    suppliers: supplierData,
  };
};

// ✅ **Check Supplier Ownership (Utility Function)**
const isSupplierLinkedToUser = async (userId: number, supplierId: number) => {
  const result = await postgresDb
    .select({
      exists: sql<boolean>`EXISTS (
        SELECT 1 FROM ${userSuppliers}
        WHERE ${userSuppliers.userId} = ${userId}
        AND ${userSuppliers.supplierId} = ${supplierId}
      )`,
    })
    .from(userSuppliers);

  return result[0]?.exists ?? false; // ✅ Ensures it returns a boolean
};


// ✅ **Get Supplier by ID**
export const getSupplierById = async (requester: JwtPayload, id: number) => {
  await authorizeAction(requester, "getById", "supplier", id);

  // 🔹 Check if the supplier is linked to the user
  const linked = await isSupplierLinkedToUser(requester.id, id);
  if (!linked) throw new Error("Unauthorized: Supplier not linked to you.");

  const supplierData = await postgresDb
    .select()
    .from(suppliers)
    .where(eq(suppliers.id, id))
    .limit(1);

  if (supplierData.length === 0) throw new Error("Supplier not found.");

  return supplierData[0];
};


// ✅ **Update Supplier**
export const updateSupplierById = async (
  requester: JwtPayload,
  supplierId: number,
  updateData: Partial<{
    name: string;
    contactPerson?: string;
    phone?: string;
    email?: string;
    address?: string;
  }>
) => {
  await authorizeAction(requester, "update", "supplier", supplierId);

  const userId = requester.id;

  // 🔹 Check if the supplier is already linked to the user
  const linkedSupplier = await postgresDb
    .select()
    .from(userSuppliers)
    .where(
      and(eq(userSuppliers.userId, userId), eq(userSuppliers.supplierId, supplierId))
    )
    .limit(1);

  if (linkedSupplier.length === 0) {
    throw new Error("Unauthorized: Supplier not linked to you.");
  }

  // 🔹 Build the conditions for checking existing suppliers
  const conditions: any[] = [];

  // Add phone condition if phone is being updated
  if (updateData.phone) {
    conditions.push(eq(suppliers.phone, updateData.phone));
  }

  // Add email condition if email is being updated and is not empty
  if (updateData.email && updateData.email.trim() !== '') {
    conditions.push(eq(suppliers.email, updateData.email));
  }

  // Initialize existingSupplier with the correct type
  let existingSupplier: any[] = [];

  // Only check for duplicates if we have conditions to check
  if (conditions.length > 0) {
    try {
      // Check if the updated phone or email already exists in another supplier
      existingSupplier = await postgresDb
        .select()
        .from(suppliers)
        .where(
          and(
            or(...conditions),
            // Exclude the current supplier
            ne(suppliers.id, supplierId)
          )
        )
        .limit(1);
    } catch (error) {
      console.error("Error checking for duplicate suppliers:", error);
      // If there's an error, assume no duplicates
      existingSupplier = [];
    }
  }

  if (existingSupplier.length > 0) {
    const newSupplier = existingSupplier[0];

    // 🔹 Check if user is already linked to the existing supplier
    const existingLink = await postgresDb
      .select()
      .from(userSuppliers)
      .where(
        and(eq(userSuppliers.userId, userId), eq(userSuppliers.supplierId, newSupplier.id))
      )
      .limit(1);

    if (existingLink.length > 0) {
      return {
        message: "You are already linked to this supplier.",
        supplier: newSupplier,
      };
    }

    // 🔹 Link the user to the existing supplier instead of updating
    await postgresDb.insert(userSuppliers).values({
      userId,
      supplierId: newSupplier.id,
      createdBy: userId,
      createdAt: new Date(),
    });

    return {
      message: "Supplier already exists. You have been linked to this supplier.",
      supplier: newSupplier,
    };
  }

  // 🔹 Update the supplier if no duplicate was found
  const updatedSupplier = await postgresDb
    .update(suppliers)
    .set({
      ...updateData,
    })
    .where(eq(suppliers.id, supplierId))
    .returning();

  if (!updatedSupplier || updatedSupplier.length === 0) {
    throw new Error("Update failed: Supplier not found.");
  }

  return {
    message: "Supplier updated successfully.",
    supplier: updatedSupplier[0],
  };
};



// ✅ **Delete Suppliers (Single or Multiple)**
export const deleteSupplierById = async (requester: JwtPayload, ids: number | number[]) => {
  const supplierIds = Array.isArray(ids) ? ids : [ids];

  // Check authorization and link for each supplier
  for (const id of supplierIds) {
    await authorizeAction(requester, "delete", "supplier", id);

    // 🔹 Check if the supplier is linked to the user
    const linked = await isSupplierLinkedToUser(requester.id, id);
    if (!linked) throw new Error(`Unauthorized: Supplier with ID ${id} not linked to you.`);
  }

  const deletedSuppliers = await postgresDb
    .delete(suppliers)
    .where(inArray(suppliers.id, supplierIds))
    .returning({ deletedId: suppliers.id });

  if (!deletedSuppliers || deletedSuppliers.length === 0) {
    throw new Error("Delete failed: Suppliers not found.");
  }

  return {
    message: supplierIds.length > 1 ? "Suppliers deleted successfully" : "Supplier deleted successfully",
    deletedIds: deletedSuppliers.map(supplier => supplier.deletedId),
  };
};

