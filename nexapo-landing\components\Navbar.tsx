'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface NavbarProps {
  currentPage?: string
}

export default function Navbar({ currentPage = 'home' }: NavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState('')

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)

      // Update active section based on scroll position
      if (currentPage === 'home') {
        const sections = ['features', 'how-it-works', 'industries', 'pricing']
        const currentSection = sections.find(section => {
          const element = document.getElementById(section)
          if (element) {
            const rect = element.getBoundingClientRect()
            return rect.top <= 100 && rect.bottom >= 100
          }
          return false
        })
        setActiveSection(currentSection || '')
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [currentPage])

  const scrollToSection = (sectionId: string) => {
    if (currentPage !== 'home') {
      window.location.href = `/#${sectionId}`
      return
    }
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveSection(sectionId)
    }
    setIsMenuOpen(false)
  }

  const getMenuItemClass = (section: string, isMobile: boolean = false) => {
    const baseClass = "font-medium transition-all duration-200"
    const isActiveSection = activeSection === section ||
        (currentPage === 'about' && section === 'about') ||
        (currentPage === 'contact' && section === 'contact')

    if (isActiveSection) {
      if (isMobile) {
        return `${baseClass} text-blue-600 font-semibold bg-blue-50 border-l-4 border-blue-600`
      }
      return `${baseClass} text-blue-600 font-semibold relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-blue-600`
    }

    if (isMobile) {
      return `${baseClass} text-gray-700 hover:text-blue-600 hover:bg-gray-50`
    }
    return `${baseClass} text-gray-700 hover:text-blue-600`
  }

  return (
    <nav className={`fixed w-full z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white shadow-lg py-3' : 'bg-white/95 backdrop-blur-sm shadow-sm py-4'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/images/logo.png"
              alt="NEXAPO Logo"
              width={80}
              height={80}
              className="rounded-lg"
            />
          </Link>

          {/* Desktop Menu */}
          <div className="hidden lg:flex items-center space-x-6">
            <button
              onClick={() => scrollToSection('features')}
              className={getMenuItemClass('features')}
            >
              Features
            </button>
            <button
              onClick={() => scrollToSection('how-it-works')}
              className={getMenuItemClass('how-it-works')}
            >
              How It Works
            </button>
            <button
              onClick={() => scrollToSection('industries')}
              className={getMenuItemClass('industries')}
            >
              Industries
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className={getMenuItemClass('pricing')}
            >
              Pricing
            </button>
            <Link
              href="/about"
              className={getMenuItemClass('about')}
            >
              About
            </Link>
            <Link
              href="/contact"
              className={getMenuItemClass('contact')}
            >
              Contact
            </Link>
            <Link
              href="/contact"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:scale-105"
            >
              Request Demo
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`lg:hidden text-2xl transition-all duration-200 p-2 rounded-lg ${
              isMenuOpen
                ? 'text-blue-600 bg-blue-50 rotate-90'
                : 'text-gray-900 hover:text-blue-600 hover:bg-gray-50'
            }`}
          >
            {isMenuOpen ? '✕' : '☰'}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden mt-4 pb-4 border-t border-gray-200 bg-white rounded-lg shadow-lg animate-fade-in-down">
            <div className="flex flex-col space-y-2 pt-4 px-2">
              <button
                onClick={() => scrollToSection('features')}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('features', true)}`}
              >
                <span className="text-lg">⚡</span>
                Features
              </button>
              <button
                onClick={() => scrollToSection('how-it-works')}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('how-it-works', true)}`}
              >
                <span className="text-lg">🔧</span>
                How It Works
              </button>
              <button
                onClick={() => scrollToSection('industries')}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('industries', true)}`}
              >
                <span className="text-lg">🏢</span>
                Industries
              </button>
              <button
                onClick={() => scrollToSection('pricing')}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('pricing', true)}`}
              >
                <span className="text-lg">💰</span>
                Pricing
              </button>
              <Link
                href="/about"
                onClick={() => setIsMenuOpen(false)}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('about', true)}`}
              >
                <span className="text-lg">ℹ️</span>
                About
              </Link>
              <Link
                href="/contact"
                onClick={() => setIsMenuOpen(false)}
                className={`text-left py-3 px-4 rounded-lg flex items-center gap-3 ${getMenuItemClass('contact', true)}`}
              >
                <span className="text-lg">📞</span>
                Contact
              </Link>
              <Link
                href="/contact"
                onClick={() => setIsMenuOpen(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg w-full text-center mt-4 transition-all hover:scale-105"
              >
                Request Demo
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
