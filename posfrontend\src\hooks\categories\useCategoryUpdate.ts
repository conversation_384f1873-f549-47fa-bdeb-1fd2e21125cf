"use client";

import { useUpdateCategoryMutation, UpdateCategoryDto } from "@/reduxRTK/services/categoryApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useCategoryUpdate = (onSuccess?: () => void) => {
  // RTK Query hook for updating a category
  const [updateCategory, { isLoading }] = useUpdateCategoryMutation();

  const updateExistingCategory = async (categoryId: number, categoryData: UpdateCategoryDto) => {
    try {
      const result = await updateCategory({
        categoryId,
        data: categoryData
      }).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to update category");
      }

      showMessage("success", "Category updated successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Update category error:", error);
      showMessage("error", error.message || "Failed to update category");
      throw error;
    }
  };

  return {
    updateCategory: updateExistingCategory,
    isUpdating: isLoading
  };
};
