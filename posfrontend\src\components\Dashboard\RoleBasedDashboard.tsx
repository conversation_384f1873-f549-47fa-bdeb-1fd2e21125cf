"use client";

import React from "react";
import { useAuth } from "@/hooks/useAuth";
import {
  SuperAdminOverviewCards,
  AdminOverviewCards,
  CashierOverviewCards,
} from "@/app/dashboard/(home)/_components/overview-cards/role-based";
import { ClientPaymentsOverview } from "@/components/Charts/payments-overview/client";
import { ClientWeeksProfit } from "@/components/Charts/weeks-profit/client";
import PaymentStatusWidget from "@/components/Dashboard/PaymentStatusWidget";
import AdminSubscriptionsTable from "@/components/Dashboard/AdminSubscriptionsTable";
import SuperAdminDashboard from "@/components/Dashboard/SuperAdminDashboard";
import AdminDashboard from "@/components/Dashboard/AdminDashboard";
import CashierDashboard from "@/components/Dashboard/CashierDashboard";

interface RoleBasedDashboardProps {
  extractTimeFrame?: (sectionKey: string) => string | undefined;
}

/**
 * RoleBasedDashboard component that displays different dashboard based on user role
 */
const RoleBasedDashboard: React.FC<RoleBasedDashboardProps> = ({
  extractTimeFrame,
}) => {
  const { user, isSuperAdmin, isAdmin, isCashier } = useAuth();

  // If no user, return null - parent component will handle this case
  if (!user) {
    return null;
  }

  // Render the appropriate dashboard based on user role
  if (isSuperAdmin()) {
    return <SuperAdminDashboard />;
  }

  if (isAdmin()) {
    return <AdminDashboard />;
  }

  if (isCashier()) {
    return <CashierDashboard />;
  }

  // Fallback for unknown roles
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-5 shadow-md">
      <h2 className="text-xl font-bold text-gray-800">Welcome, {user.name}!</h2>
      <p className="text-gray-600">
        Your role ({user.role}) doesn&quot;t have a specific dashboard view.
      </p>
      <p className="mt-2 text-gray-600">
        Please contact your administrator for assistance.
      </p>
    </div>
  );
};

export default RoleBasedDashboard;
