"use client";

import { useUpdatePurchaseMutation, UpdatePurchaseDto } from "@/reduxRTK/services/purchaseApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const usePurchaseUpdate = (onSuccess?: () => void) => {
  // RTK Query hook for updating a purchase
  const [updatePurchase, { isLoading }] = useUpdatePurchaseMutation();

  const updateExistingPurchase = async (purchaseId: number, purchaseData: UpdatePurchaseDto) => {
    try {
      const result = await updatePurchase({
        purchaseId,
        data: purchaseData
      }).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to update purchase");
      }

      showMessage("success", "Purchase updated successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Update purchase error:", error);
      showMessage("error", error.message || "Failed to update purchase");
      throw error;
    }
  };

  return {
    updatePurchase: updateExistingPurchase,
    isUpdating: isLoading
  };
};
