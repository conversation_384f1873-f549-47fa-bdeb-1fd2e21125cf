(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9734],{52796:(e,t,a)=>{Promise.resolve().then(a.bind(a,68037))},68037:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>D});var s=a(95155),i=a(12115),r=a(73937),n=a(71349),l=a(62075),o=a(20148),c=a(68773),d=a(43316),h=a(9365),u=a(75365),f=a(1227),b=a(19397),g=a(76170),y=a(5099),m=a(72278),w=a(48173),x=a.n(w),S=a(88908);let{Title:p,Paragraph:j,Text:N}=r.A,D=()=>{let[e,t]=(0,i.useState)({products:0,pendingSales:0,lastSync:null}),[a,r]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{(async()=>{try{let e=await S.V.getCachedProducts(),a=await S.V.getOfflineSales("pending");t({products:e.length,pendingSales:a.length,lastSync:e.length>0?e[0].lastUpdated:null})}catch(e){console.error("Failed to load offline data:",e)}finally{r(!1)}})()},[]),(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsx)(n.A,{className:"max-w-lg w-full text-center shadow-lg",children:(0,s.jsx)(l.Ay,{icon:(0,s.jsx)(u.A,{className:"text-gray-400",style:{fontSize:"72px"}}),title:(0,s.jsx)(p,{level:2,className:"text-gray-800",children:"You're Offline"}),subTitle:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(j,{className:"text-gray-600",children:"No internet connection detected. Don't worry - NEXAPO POS works great offline!"}),(0,s.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,s.jsxs)(p,{level:4,className:"text-green-700 mb-3 flex items-center justify-center",children:[(0,s.jsx)(f.A,{className:"mr-2"}),"Available Offline"]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-left text-green-600",children:[(0,s.jsx)("div",{children:"• Make sales"}),(0,s.jsx)("div",{children:"• Generate receipts"}),(0,s.jsx)("div",{children:"• View products"}),(0,s.jsx)("div",{children:"• Search inventory"}),(0,s.jsx)("div",{children:"• Barcode scanning"}),(0,s.jsx)("div",{children:"• Sales history"})]})]}),!a&&(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,s.jsxs)(p,{level:5,className:"text-blue-700 mb-3 flex items-center justify-center",children:[(0,s.jsx)(b.A,{className:"mr-2"}),"Cached Data Status"]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(N,{children:"Products cached:"}),(0,s.jsx)(N,{strong:!0,className:"text-blue-600",children:e.products})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(N,{children:"Pending sales:"}),(0,s.jsx)(N,{strong:!0,className:"text-orange-600",children:e.pendingSales})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(N,{children:"Data freshness:"}),(0,s.jsx)(N,{strong:!0,className:"text-blue-600",children:(()=>{if(!e.lastSync)return"No cached data";let t=Math.floor((new Date().getTime()-e.lastSync.getTime())/36e5);return t<1?"Very fresh (< 1 hour)":t<24?"Fresh (".concat(t," hours ago)"):"".concat(Math.floor(t/24)," days ago")})()})]})]})]}),e.pendingSales>0&&(0,s.jsx)(o.A,{message:"Pending Sales",description:"You have ".concat(e.pendingSales," sales waiting to sync when connection returns."),type:"warning",showIcon:!0,icon:(0,s.jsx)(g.A,{})})]}),extra:(0,s.jsxs)(c.A,{direction:"vertical",className:"w-full",size:"middle",children:[(0,s.jsx)(x(),{href:"/dashboard/sales/offline-pos",children:(0,s.jsx)(d.Ay,{type:"primary",icon:(0,s.jsx)(y.A,{}),size:"large",className:"w-full bg-green-600 hover:bg-green-700 border-green-600",children:"Continue with Offline POS"})}),(0,s.jsx)(h.A,{className:"my-2",children:"or"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)(d.Ay,{icon:(0,s.jsx)(m.A,{}),onClick:()=>{window.location.reload()},size:"large",className:"w-full",children:"Retry Connection"}),(0,s.jsx)(x(),{href:"/dashboard",children:(0,s.jsx)(d.Ay,{size:"large",className:"w-full",children:"Go to Dashboard"})})]}),(0,s.jsx)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:(0,s.jsx)(N,{type:"secondary",className:"text-xs",children:"\uD83D\uDCA1 Your offline sales will automatically sync when internet connection is restored"})})]})})})})}},88908:(e,t,a)=>{"use strict";a.d(t,{V:()=>r});var s=a(10733);class i{async init(){try{this.db=await (0,s.P2)(this.DB_NAME,this.DB_VERSION,{upgrade(e){let t=e.createObjectStore("offlineSales",{keyPath:"id"});t.createIndex("by-timestamp","timestamp"),t.createIndex("by-status","status");let a=e.createObjectStore("products",{keyPath:"id"});a.createIndex("by-name","name"),a.createIndex("by-barcode","barcode");let s=e.createObjectStore("syncQueue",{keyPath:"id"});s.createIndex("by-priority","priority"),s.createIndex("by-timestamp","timestamp"),e.objectStoreNames.contains("store")||e.createObjectStore("store")}}),console.log("✅ Offline storage initialized")}catch(e){throw console.error("❌ Failed to initialize offline storage:",e),e}}async saveOfflineSale(e){if(!this.db)throw Error("Database not initialized");let t="offline_sale_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),a={...e,id:t,timestamp:new Date,status:"pending",syncAttempts:0};return await this.db.add("offlineSales",a),console.log("\uD83D\uDCBE Offline sale saved:",t),t}async getOfflineSales(e){if(!this.db)throw Error("Database not initialized");return e?await this.db.getAllFromIndex("offlineSales","by-status",e):await this.db.getAll("offlineSales")}async updateSaleStatus(e,t,a){if(!this.db)throw Error("Database not initialized");let s=await this.db.get("offlineSales",e);if(!s)throw Error("Sale not found");s.status=t,s.syncAttempts+=1,s.lastSyncAttempt=new Date,a&&(s.errorMessage=a),await this.db.put("offlineSales",s)}async deleteOfflineSale(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("offlineSales",e)}async cacheProducts(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction("products","readwrite"),a=t.objectStore("products");for(let t of(await a.clear(),e))await a.add({...t,lastUpdated:new Date});await t.done,console.log("\uD83D\uDCBE Cached ".concat(e.length," products for offline use"))}async getCachedProducts(){if(!this.db)throw Error("Database not initialized");return await this.db.getAll("products")}async searchProducts(e){if(!this.db)throw Error("Database not initialized");let t=await this.db.getAll("products"),a=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(a)||e.sku&&e.sku.toLowerCase().includes(a)||e.barcode&&e.barcode.toLowerCase().includes(a))}async getProductByBarcode(e){if(!this.db)throw Error("Database not initialized");return await this.db.getFromIndex("products","by-barcode",e)}async addToSyncQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"sync_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:new Date,retryCount:0};await this.db.add("syncQueue",t)}async getSyncQueue(){if(!this.db)throw Error("Database not initialized");return await this.db.getAllFromIndex("syncQueue","by-priority")}async removeSyncQueueItem(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("syncQueue",e)}async cacheStore(e){if(!this.db)throw Error("Database not initialized");await this.db.put("store",e,"current-store"),console.log("\uD83D\uDCBE Store details cached for offline use")}async getCachedStore(){if(!this.db)throw Error("Database not initialized");return await this.db.get("store","current-store")}async getStorageStats(){if(!this.db)throw Error("Database not initialized");let[e,t,a,s,i]=await Promise.all([this.db.getAllFromIndex("offlineSales","by-status","pending"),this.db.getAllFromIndex("offlineSales","by-status","synced"),this.db.getAllFromIndex("offlineSales","by-status","failed"),this.db.getAll("products"),this.db.getAll("syncQueue")]);return{pendingSales:e.length,syncedSales:t.length,failedSales:a.length,cachedProducts:s.length,queueItems:i.length}}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=this.db.transaction(["offlineSales","products","syncQueue"],"readwrite");await Promise.all([e.objectStore("offlineSales").clear(),e.objectStore("products").clear(),e.objectStore("syncQueue").clear()]),await e.done,console.log("\uD83D\uDDD1️ All offline data cleared")}constructor(){this.db=null,this.DB_NAME="NEXAPO_POS_OFFLINE",this.DB_VERSION=1}}let r=new i;r.init().catch(console.error)}},e=>{var t=t=>e(e.s=t);e.O(0,[6754,2261,3316,9135,1388,9907,3288,2204,1349,2336,2270,9873,8441,1517,7358],()=>t(52796)),_N_E=e.O()}]);