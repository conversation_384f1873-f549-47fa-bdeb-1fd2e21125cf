import type { PropsWithChildren } from "react";
import ProtectedDashboardContent from "@/components/Dashboard/ProtectedDashboardContent";
import { metadata } from "./metadata";
import "./fix-sidebar.css"; // Import the CSS fix for sidebar issues
import "./logo-fix.css"; // Import the CSS fix for logo positioning

export { metadata };

export default function DashboardLayout({ children }: PropsWithChildren) {
  return (
    <ProtectedDashboardContent>
      {children}
    </ProtectedDashboardContent>
  );
}
