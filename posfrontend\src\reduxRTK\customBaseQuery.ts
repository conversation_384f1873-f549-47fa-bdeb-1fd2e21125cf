import { BaseQueryFn } from "@reduxjs/toolkit/query";
import { apiCallerWithAuth, ApiCaller } from "@/api/apicaller";

// Custom Base Query Function to handle both authenticated and non-authenticated requests
export const customBaseQuery: BaseQueryFn<
  { urlpath: string; payloaddata: { [key: string]: any }; token?: string },
  unknown,
  { success: boolean; message: string; data?: any }
> = async (args) => {
  const { urlpath, payloaddata, token } = args;

  try {
    // Handle login request separately (no token required)
    if (urlpath === "/login" || urlpath === "/logout") {
      const result = await ApiCaller(urlpath, payloaddata);
      if (result.success) {
        return { data: result }; // Return data directly if successful
      } else {
        return { error: result }; // Return error if login fails
      }
    }

    // Handle other authenticated requests (requires token)
    if (token) {
      console.log("Making authenticated request:", { urlpath, payloaddata, hasToken: !!token });
      try {
        // Ensure token is not empty
        if (!token.trim()) {
          console.error("Empty token provided for authenticated request");
          return {
            error: {
              success: false,
              message: "Authentication token is empty. Please log in again."
            }
          };
        }

        const result = await apiCallerWithAuth(payloaddata, urlpath, token);
        console.log("Authenticated request result:", { success: result.success, urlpath });

        if (result.success) {
          return { data: result }; // Return data if successful
        } else {
          console.error("API error:", { urlpath, error: result.message });
          return { error: result }; // Return error if the request fails
        }
      } catch (error: any) {
        console.error("API call exception:", { urlpath, error });
        return {
          error: {
            success: false,
            message: `Error in API call: ${error.message || "Unknown error"}`
          }
        };
      }
    }

    // If no conditions match, return an error
    console.error("Invalid API call - No token provided or invalid request structure", {
      urlpath,
      hasToken: !!token,
      payloaddata
    });

    // Provide a more helpful error message based on the situation
    let errorMessage = "Authentication error";

    if (!token) {
      errorMessage = "No authentication token provided. Please log in again.";
    } else if (!urlpath) {
      errorMessage = "Invalid API call - No URL path provided.";
    } else {
      errorMessage = "Invalid API call - Please check your request structure.";
    }

    return { error: { success: false, message: errorMessage } };
  } catch (error) {
    console.error("API call failed:", error);
    return {
      error: {
        success: false,
        message: "An error occurred during the API call",
      },
    };
  }
};
