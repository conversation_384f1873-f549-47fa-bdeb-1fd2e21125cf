{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/categories", "regex": "^/dashboard/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/categories(?:/)?$"}, {"page": "/dashboard/expense-categories", "regex": "^/dashboard/expense\\-categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/expense\\-categories(?:/)?$"}, {"page": "/dashboard/expenses", "regex": "^/dashboard/expenses(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/expenses(?:/)?$"}, {"page": "/dashboard/payment", "regex": "^/dashboard/payment(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/payment(?:/)?$"}, {"page": "/dashboard/products", "regex": "^/dashboard/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/products(?:/)?$"}, {"page": "/dashboard/profile", "regex": "^/dashboard/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/profile(?:/)?$"}, {"page": "/dashboard/purchases", "regex": "^/dashboard/purchases(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/purchases(?:/)?$"}, {"page": "/dashboard/receipts", "regex": "^/dashboard/receipts(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/receipts(?:/)?$"}, {"page": "/dashboard/reports", "regex": "^/dashboard/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports(?:/)?$"}, {"page": "/dashboard/reports/inventory", "regex": "^/dashboard/reports/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports/inventory(?:/)?$"}, {"page": "/dashboard/reports/sales", "regex": "^/dashboard/reports/sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/reports/sales(?:/)?$"}, {"page": "/dashboard/sales", "regex": "^/dashboard/sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/sales(?:/)?$"}, {"page": "/dashboard/sales/offline-pos", "regex": "^/dashboard/sales/offline\\-pos(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/sales/offline\\-pos(?:/)?$"}, {"page": "/dashboard/stores", "regex": "^/dashboard/stores(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/stores(?:/)?$"}, {"page": "/dashboard/suppliers", "regex": "^/dashboard/suppliers(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/suppliers(?:/)?$"}, {"page": "/dashboard/users", "regex": "^/dashboard/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/offline", "regex": "^/offline(?:/)?$", "routeKeys": {}, "namedRegex": "^/offline(?:/)?$"}, {"page": "/payment", "regex": "^/payment(?:/)?$", "routeKeys": {}, "namedRegex": "^/payment(?:/)?$"}, {"page": "/payment/callback", "regex": "^/payment/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/payment/callback(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}