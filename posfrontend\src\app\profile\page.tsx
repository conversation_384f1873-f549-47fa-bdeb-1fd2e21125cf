"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function ProfileRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new profile page under the dashboard layout
    router.replace("/dashboard/profile");
  }, [router]);

  return (
    <div className="flex justify-center items-center h-screen">
      <LoadingSpinner tip="Redirecting to profile..." />
    </div>
  );
}
