import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setUser } from '@/reduxRTK/services/authSlice';
import { RootState } from '@/reduxRTK/store/store';
import { userApi } from '@/reduxRTK/services/authApi';

export function usePaymentWebSocket() {
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  // Use refs to always have the latest user and accessToken
  const userRef = useRef(user);
  const accessTokenRef = useRef(accessToken);

  useEffect(() => {
    userRef.current = user;
    accessTokenRef.current = accessToken;
  }, [user, accessToken]);

  useEffect(() => {
    if (!user || !user.id) return;
    // Use the production Railway backend WebSocket URL directly
    const wsUrl = 'wss://nexapoapi.up.railway.app';
    const ws = new WebSocket(wsUrl);
    ws.onopen = () => {
      ws.send(JSON.stringify({ type: 'auth', userId: user.id }));
      console.log('[WebSocket] Connected and authenticated as user', user.id, 'URL:', wsUrl);
      // Immediately fetch current user data to sync Redux state
      (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, { forceRefetch: true }))
        .then((result: any) => {
          if (result?.data?.success && result.data.data && accessTokenRef.current) {
            dispatch(setUser({ user: result.data.data, accessToken: accessTokenRef.current }));
          }
        });
    };
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'paymentStatus') {
        if (userRef.current?.role === 'superadmin') return;
        console.log('[WebSocket] Received paymentStatus update:', data.status);
        if (accessTokenRef.current && userRef.current && typeof userRef.current.id === 'number') {
          // Only include defined properties, and ensure id is present
          const { id, name, email, phone, role, createdAt, lastPaymentDate, nextPaymentDue, createdBy } = userRef.current;
          dispatch(setUser({
            user: {
              id,
              name,
              email,
              phone,
              role,
              createdAt,
              lastPaymentDate,
              nextPaymentDue,
              createdBy,
              paymentStatus: data.status
            },
            accessToken: accessTokenRef.current
          }));
        }
      }
    };
    ws.onerror = (event) => {
      console.error('[WebSocket] Error:', event);
    };
    ws.onclose = () => {
      console.log('[WebSocket] Connection closed');
    };
    return () => ws.close();
  }, [user?.id, dispatch]);
} 