"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface AdminRevenueChartProps {
  sales: any[];
}

const AdminRevenueChart: React.FC<AdminRevenueChartProps> = ({ sales }) => {
  const chartData = useMemo(() => {
    console.log('📊 Revenue Chart - Sales found:', sales.length);

    // Create last 12 months array
    const last12Months: Array<{
      monthKey: string;
      monthLabel: string;
      revenue: number;
      salesCount: number;
    }> = [];
    for (let i = 11; i >= 0; i--) {
      const month = dayjs().subtract(i, 'month');
      last12Months.push({
        monthKey: month.format('YYYY-MM'),
        monthLabel: month.format('MMM YYYY'),
        revenue: 0,
        salesCount: 0
      });
    }

    // Group sales by month
    sales.forEach(sale => {
      if (sale.transactionDate) {
        const saleMonth = dayjs(sale.transactionDate).format('YYYY-MM');
        const monthData = last12Months.find(m => m.monthKey === saleMonth);
        if (monthData) {
          monthData.revenue += parseFloat(sale.totalAmount || '0');
          monthData.salesCount++;
        }
      }
    });

    const chartData = last12Months.map(month => ({
      x: month.monthLabel,
      y: month.revenue
    }));

    console.log('📈 Revenue chart data:', chartData);
    return chartData;
  }, [sales]);

  const options: ApexOptions = {
    chart: {
      type: 'area',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#10B981'],
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.1,
        stops: [0, 90, 100]
      }
    },
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return `₵${value.toFixed(2)}`;
        }
      }
    },
    xaxis: {
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '12px',
        }
      }
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          return `₵${value.toFixed(0)}`;
        },
        style: {
          fontSize: '12px',
        }
      }
    }
  };

  const series = [{
    name: 'Revenue',
    data: chartData
  }];

  if (chartData.length === 0 || chartData.every(point => point.y === 0)) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No revenue data available</p>
          <p className="text-sm">Chart will appear when sales are made</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="area"
        height={250}
      />
    </div>
  );
};

export default AdminRevenueChart;
