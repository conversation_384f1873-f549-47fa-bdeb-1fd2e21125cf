// Mock data for overview cards
export async function getOverviewData() {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  return {
    views: {
      value: 3456,
      change: 0.43,
      changeType: "increase" as const,
      growthRate: 0.43,
    },
    profit: {
      value: 45678,
      change: 4.35,
      changeType: "increase" as const,
      growthRate: 4.35,
    },
    products: {
      value: 2450,
      change: 2.59,
      changeType: "increase" as const,
      growthRate: 2.59,
    },
    users: {
      value: 3456,
      change: 0.95,
      changeType: "decrease" as const,
      growthRate: -0.95,
    },
  };
}
