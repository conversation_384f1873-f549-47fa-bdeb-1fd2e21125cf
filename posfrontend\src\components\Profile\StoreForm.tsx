"use client";

import React, { useState, useEffect } from "react";
import { Form, Input, Button, Spin } from "antd";
import { ShopOutlined, SaveOutlined } from "@ant-design/icons";
import { User } from "@/types/user";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { showMessage } from "@/utils/showMessage";
import {
  useGetUserDefaultStoreQuery,
  useCreateUserStoreMutation,
  useUpdateUserStoreMutation
} from "@/reduxRTK/services/userStoreApi";

interface StoreFormProps {
  user: User;
  onStoreUpdated?: () => void;
}

const StoreForm: React.FC<StoreFormProps> = ({ user, onStoreUpdated }) => {
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get user's default store
  const { data: storeData, isLoading: isLoadingStore, refetch } = useGetUserDefaultStoreQuery(user.id);

  // Mutations for creating/updating store
  const [createUserStore] = useCreateUserStoreMutation();
  const [updateUserStore] = useUpdateUserStoreMutation();

  // Populate form with store data when available
  useEffect(() => {
    if (storeData?.data) {
      const store = storeData.data;
      form.setFieldsValue({
        name: store.name,
        address: store.address,
        city: store.city,
        state: store.state,
        country: store.country,
        phone: store.phone,
        email: store.email,
        website: store.website,
        taxId: store.taxId,
      });
    } else {
      form.resetFields();
    }
  }, [storeData, form]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const values = await form.validateFields();

      // Determine if we're creating or updating
      if (storeData?.data) {
        // Update existing store
        const response = await updateUserStore({
          storeId: storeData.data.id,
          data: values
        }).unwrap();

        if (response.success) {
          showMessage("success", "Store information updated successfully");
          if (onStoreUpdated) onStoreUpdated();
          refetch();
        } else {
          showMessage("error", response.message || "Failed to update store information");
        }
      } else {
        // Create new store
        const response = await createUserStore({
          data: {
            ...values,
            userId: user.id
          }
        }).unwrap();

        if (response.success) {
          showMessage("success", "Store information saved successfully");
          if (onStoreUpdated) onStoreUpdated();
          refetch();
        } else {
          showMessage("error", response.message || "Failed to save store information");
        }
      }
    } catch (error: any) {
      console.error("Error submitting store form:", error);
      showMessage("error", error.data?.message || "An error occurred while saving store information");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingStore) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin />
      </div>
    );
  }

  return (
    <div className="text-gray-800">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2 flex items-center">
          <ShopOutlined className="mr-2" />
          <span>Store Information</span>
        </h3>
        <p className="text-gray-600">
          This information will appear on receipts and invoices
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="text-gray-800"
      >
        <Form.Item
          name="name"
          label={<span className="text-gray-700">Store Name <span className="text-red-500">*</span></span>}
          rules={[{ required: true, message: "Please enter your store name" }]}
        >
          <Input
            placeholder="Enter store name"
            prefix={<ShopOutlined className="site-form-item-icon" />}
            className="bg-white border-gray-300 text-gray-800"
          />
        </Form.Item>

        <Form.Item
          name="address"
          label={<span className="text-gray-700">Address</span>}
        >
          <Input.TextArea
            placeholder="Enter store address"
            className="bg-white border-gray-300 text-gray-800"
            rows={3}
          />
        </Form.Item>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="city"
            label={<span className="text-gray-700">City</span>}
          >
            <Input
              placeholder="Enter city"
              className="bg-white border-gray-300 text-gray-800"
            />
          </Form.Item>

          <Form.Item
            name="state"
            label={<span className="text-gray-700">State/Region</span>}
          >
            <Input
              placeholder="Enter state or region"
              className="bg-white border-gray-300 text-gray-800"
            />
          </Form.Item>
        </div>

        <Form.Item
          name="country"
          label={<span className="text-gray-700">Country</span>}
        >
          <Input
            placeholder="Enter country"
            className="bg-white border-gray-300 text-gray-800"
          />
        </Form.Item>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="phone"
            label={<span className="text-gray-700">Phone</span>}
          >
            <Input
              placeholder="Enter store phone"
              className="bg-white border-gray-300 text-gray-800"
            />
          </Form.Item>

          <Form.Item
            name="email"
            label={<span className="text-gray-700">Email</span>}
          >
            <Input
              placeholder="Enter store email"
              className="bg-white border-gray-300 text-gray-800"
              type="email"
            />
          </Form.Item>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="website"
            label={<span className="text-gray-700">Website</span>}
          >
            <Input
              placeholder="Enter website URL"
              className="bg-white border-gray-300 text-gray-800"
            />
          </Form.Item>

          <Form.Item
            name="taxId"
            label={<span className="text-gray-700">Tax ID</span>}
          >
            <Input
              placeholder="Enter tax ID"
              className="bg-white border-gray-300 text-gray-800"
            />
          </Form.Item>
        </div>

        <Form.Item className="mt-6">
          <Button
            type="primary"
            htmlType="submit"
            loading={isSubmitting}
            icon={<SaveOutlined />}
            className="bg-blue-600 hover:bg-blue-700 border-none text-white"
            block
          >
            {storeData?.data ? "Update Store Information" : "Save Store Information"}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default StoreForm;
