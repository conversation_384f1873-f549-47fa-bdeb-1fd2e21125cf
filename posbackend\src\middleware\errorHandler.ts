import { Request, Response, NextFunction } from 'express';
import { sendResponse } from '../utils/responseHelper';

// Custom error class for API errors
export class ApiError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(statusCode: number, message: string, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    Error.captureStackTrace(this, this.constructor);
  }
}

// Database connection error handler
export class DatabaseError extends ApiError {
  constructor(message: string) {
    super(503, message || 'Database connection error', true);
  }
}

// Network error handler
export class NetworkError extends ApiError {
  constructor(message: string) {
    super(503, message || 'Network error', true);
  }
}

// Validation error
export class ValidationError extends ApiError {
  constructor(message: string) {
    super(400, message || 'Validation error', true);
  }
}

// Not found error
export class NotFoundError extends ApiError {
  constructor(message: string) {
    super(404, message || 'Resource not found', true);
  }
}

// Authentication error
export class AuthenticationError extends ApiError {
  constructor(message: string) {
    super(401, message || 'Authentication error', true);
  }
}

// Authorization error
export class AuthorizationError extends ApiError {
  constructor(message: string) {
    super(403, message || 'Authorization error', true);
  }
}

// Global error handler middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('Error caught by global handler:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    body: req.body,
  });

  // Handle specific error types
  if (err instanceof ApiError) {
    return sendResponse(
      res,
      err.statusCode,
      false,
      err.message
    );
  }

  // Handle PostgreSQL specific errors
  if (err.name === 'PostgresError' || err.message.includes('database') || err.message.includes('sql')) {
    return sendResponse(
      res,
      503,
      false,
      'Database error occurred. Please try again later.'
    );
  }

  // Handle network errors
  if (
    err.name === 'FetchError' || 
    err.message.includes('network') || 
    err.message.includes('ECONNREFUSED') ||
    err.message.includes('ETIMEDOUT') ||
    err.message.includes('ENOTFOUND')
  ) {
    return sendResponse(
      res,
      503,
      false,
      'Network error occurred. Please check your connection and try again.'
    );
  }

  // Handle JSON parsing errors
  if (err instanceof SyntaxError && 'body' in err) {
    return sendResponse(
      res,
      400,
      false,
      'Invalid JSON in request body'
    );
  }

  // Handle validation errors (e.g., from Zod)
  if (err.name === 'ZodError') {
    return sendResponse(
      res,
      400,
      false,
      `Validation error: ${err.message}`
    );
  }

  // Default error handler for unhandled errors
  return sendResponse(
    res,
    500,
    false,
    process.env.NODE_ENV === 'production'
      ? 'An unexpected error occurred. Please try again later.'
      : `Internal Server Error: ${err.message}`
  );
};

// Async handler to catch errors in async route handlers
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
