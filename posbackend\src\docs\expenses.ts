/**
 * @swagger
 * tags:
 *   - name: Expenses
 *     description: Expense management operations
 *   - name: Expense Categories
 *     description: Expense category management operations
 */

/**
 * @swagger
 * /expenses:
 *   post:
 *     summary: Manage expenses
 *     description: Create, retrieve, update, delete expenses, or get expense statistics
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [createnew]
 *                     example: createnew
 *                   title:
 *                     type: string
 *                     example: "Office Rent Payment"
 *                   description:
 *                     type: string
 *                     example: "Monthly rent for main office location"
 *                   amount:
 *                     type: number
 *                     example: 1500.00
 *                   categoryId:
 *                     type: integer
 *                     example: 3
 *                   expenseDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2024-01-15T10:30:00Z"
 *                   paymentMethod:
 *                     type: string
 *                     enum: [cash, card, mobile_money, bank_transfer, cheque]
 *                     example: bank_transfer
 *                   receiptUrl:
 *                     type: string
 *                     example: "https://example.com/receipts/receipt123.pdf"
 *                   vendor:
 *                     type: string
 *                     example: "ABC Property Management"
 *                   isRecurring:
 *                     type: boolean
 *                     example: true
 *                   recurringFrequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, yearly]
 *                     example: monthly
 *                   tags:
 *                     type: string
 *                     example: '["rent", "office", "monthly"]'
 *                   storeId:
 *                     type: integer
 *                     example: 1
 *                 required: [mode, title, amount, paymentMethod]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   expenseId:
 *                     type: integer
 *                     example: 1
 *                   page:
 *                     type: integer
 *                     example: 1
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                   categoryId:
 *                     type: integer
 *                     example: 3
 *                   startDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2024-01-01T00:00:00Z"
 *                   endDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2024-01-31T23:59:59Z"
 *                 required: [mode]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [update]
 *                     example: update
 *                   expenseId:
 *                     type: integer
 *                     example: 1
 *                   title:
 *                     type: string
 *                     example: "Updated Office Rent Payment"
 *                   description:
 *                     type: string
 *                     example: "Updated monthly rent for main office location"
 *                   amount:
 *                     type: number
 *                     example: 1600.00
 *                   categoryId:
 *                     type: integer
 *                     example: 3
 *                   paymentMethod:
 *                     type: string
 *                     enum: [cash, card, mobile_money, bank_transfer, cheque]
 *                     example: bank_transfer
 *                 required: [mode, expenseId]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [delete]
 *                     example: delete
 *                   expenseId:
 *                     type: integer
 *                     example: 1
 *                   expenseIds:
 *                     type: array
 *                     items:
 *                       type: integer
 *                     example: [1, 2, 3]
 *                 required: [mode]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [stats]
 *                     example: stats
 *                   startDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2024-01-01T00:00:00Z"
 *                   endDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2024-01-31T23:59:59Z"
 *                 required: [mode]
 *     responses:
 *       200:
 *         description: Expense operation successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       oneOf:
 *                         - $ref: '#/components/schemas/Expense'
 *                         - type: object
 *                           properties:
 *                             expenses:
 *                               type: array
 *                               items:
 *                                 $ref: '#/components/schemas/Expense'
 *                             pagination:
 *                               type: object
 *                               properties:
 *                                 currentPage:
 *                                   type: integer
 *                                 totalPages:
 *                                   type: integer
 *                                 totalCount:
 *                                   type: integer
 *                                 hasNextPage:
 *                                   type: boolean
 *                                 hasPreviousPage:
 *                                   type: boolean
 *                         - type: object
 *                           properties:
 *                             totalAmount:
 *                               type: number
 *                             totalCount:
 *                               type: integer
 *                             byCategory:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   categoryId:
 *                                     type: integer
 *                                   categoryName:
 *                                     type: string
 *                                   categoryColor:
 *                                     type: string
 *                                   totalAmount:
 *                                     type: number
 *                                   count:
 *                                     type: integer
 *       201:
 *         description: Expense created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Expense'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Expense not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /expense-categories:
 *   post:
 *     summary: Manage expense categories
 *     description: Create, retrieve, update, or delete expense categories
 *     tags: [Expense Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [createnew]
 *                     example: createnew
 *                   name:
 *                     type: string
 *                     example: "Marketing"
 *                   description:
 *                     type: string
 *                     example: "Advertising, promotions, and marketing expenses"
 *                   color:
 *                     type: string
 *                     example: "#10B981"
 *                 required: [mode, name]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   categoryId:
 *                     type: integer
 *                     example: 1
 *                   page:
 *                     type: integer
 *                     example: 1
 *                   limit:
 *                     type: integer
 *                     example: 50
 *                 required: [mode]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [update]
 *                     example: update
 *                   categoryId:
 *                     type: integer
 *                     example: 1
 *                   name:
 *                     type: string
 *                     example: "Updated Marketing"
 *                   description:
 *                     type: string
 *                     example: "Updated advertising and marketing expenses"
 *                   color:
 *                     type: string
 *                     example: "#059669"
 *                 required: [mode, categoryId]
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [delete]
 *                     example: delete
 *                   categoryId:
 *                     type: integer
 *                     example: 1
 *                   categoryIds:
 *                     type: array
 *                     items:
 *                       type: integer
 *                     example: [1, 2, 3]
 *                 required: [mode]
 *     responses:
 *       200:
 *         description: Expense category operation successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       oneOf:
 *                         - $ref: '#/components/schemas/ExpenseCategory'
 *                         - type: object
 *                           properties:
 *                             categories:
 *                               type: array
 *                               items:
 *                                 $ref: '#/components/schemas/ExpenseCategory'
 *                             pagination:
 *                               type: object
 *                               properties:
 *                                 currentPage:
 *                                   type: integer
 *                                 totalPages:
 *                                   type: integer
 *                                 totalCount:
 *                                   type: integer
 *                                 hasNextPage:
 *                                   type: boolean
 *                                 hasPreviousPage:
 *                                   type: boolean
 *       201:
 *         description: Expense category created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/ExpenseCategory'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Expense category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
