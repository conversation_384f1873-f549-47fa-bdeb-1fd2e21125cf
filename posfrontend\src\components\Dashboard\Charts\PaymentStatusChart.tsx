"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface PaymentStatusChartProps {
  users: any[];
}

const PaymentStatusChart: React.FC<PaymentStatusChartProps> = ({ users }) => {
  const chartData = useMemo(() => {
    // Filter admin users and count by payment status
    const adminUsers = users.filter(user => user.role === 'admin');
    
    let paid = 0;
    let overdue = 0;
    let pending = 0;
    let inactive = 0;

    adminUsers.forEach(user => {
      switch (user.paymentStatus) {
        case 'paid':
          paid++;
          break;
        case 'overdue':
          overdue++;
          break;
        case 'pending':
          pending++;
          break;
        default:
          inactive++;
          break;
      }
    });

    return {
      labels: ['Active (Paid)', 'Overdue', 'Pending', 'Inactive'],
      series: [paid, overdue, pending, inactive],
      total: paid + overdue + pending + inactive
    };
  }, [users]);

  const options: ApexOptions = {
    chart: {
      type: 'bar',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#10B981', '#EF4444', '#F59E0B', '#6B7280'],
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '60%',
        borderRadius: 4,
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '12px',
        fontWeight: 600,
        colors: ['#fff']
      }
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    legend: {
      show: false,
    },
    tooltip: {
      y: {
        formatter: function(value, { dataPointIndex }) {
          const status = chartData.labels[dataPointIndex];
          const percentage = ((value / chartData.total) * 100).toFixed(1);
          return `${value} admins (${percentage}%)`;
        }
      }
    },
    xaxis: {
      categories: chartData.labels,
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '12px',
        },
        rotate: -45,
      }
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          return Math.round(value).toString();
        },
        style: {
          fontSize: '12px',
        }
      }
    }
  };

  const series = [{
    name: 'Admin Count',
    data: chartData.series
  }];

  if (chartData.total === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No payment status data available</p>
          <p className="text-sm">Chart will appear when admin users are registered</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="bar"
        height={250}
      />
    </div>
  );
};

export default PaymentStatusChart;
