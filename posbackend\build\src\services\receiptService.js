"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteReceiptById = exports.getReceiptBySaleId = exports.getAllReceipts = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
/**
 * ✅ **Get All Receipts (Paginated)**
 */
const getAllReceipts = async (requester, page = 1, limit = 10) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "receipts");
    const offset = (page - 1) * limit;
    const isSuperadmin = requester.role === "superadmin";
    const isCashier = requester.role === "cashier";
    // Different filter logic based on role
    let receiptsFilter;
    if (isSuperadmin) {
        // Superadmin sees all receipts
        receiptsFilter = undefined;
    }
    else if (requester.role === "admin") {
        // Admin can only see their own receipts and receipts from users they created
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        const teamMemberIds = teamMembersResult.map((m) => m.memberId);
        if (teamMemberIds.length > 0) {
            // Admin can see their own receipts and receipts from users they created
            receiptsFilter = (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, teamMemberIds));
        }
        else {
            // If no team members found, just show their own receipts
            receiptsFilter = (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id);
        }
    }
    else if (isCashier) {
        // Cashier can only see their own receipts and receipts from their admin and siblings
        // Find the cashier's creator (admin)
        const creatorResult = await db_1.postgresDb
            .select({ creatorId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        const creatorId = creatorResult[0]?.creatorId || undefined;
        if (creatorId) {
            // Get all users created by the same admin (siblings)
            const siblingsResult = await db_1.postgresDb
                .select({ siblingId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
            const siblingIds = siblingsResult.map((s) => s.siblingId);
            // Build the filter to include own receipts, creator's receipts, and siblings' receipts
            receiptsFilter = (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, siblingIds));
        }
        else {
            // If no creator found, just show their own receipts
            receiptsFilter = (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id);
        }
    }
    else {
        // Default case - just show own receipts
        receiptsFilter = (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id);
    }
    const [receiptsData, totalReceipts] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.receipts.id,
            saleId: schema_1.receipts.saleId,
            receiptUrl: schema_1.receipts.receiptUrl,
            createdBy: schema_1.users.name,
            createdAt: schema_1.receipts.createdAt,
            // Store information
            storeName: schema_1.stores.name,
        })
            .from(schema_1.receipts)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, schema_1.users.id))
            .leftJoin(schema_1.sales, (0, drizzle_orm_1.eq)(schema_1.receipts.saleId, schema_1.sales.id))
            .leftJoin(schema_1.stores, (0, drizzle_orm_1.eq)(schema_1.sales.storeId, schema_1.stores.id))
            .where(receiptsFilter)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.receipts.createdAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.receipts)
            .where(receiptsFilter),
    ]);
    return {
        total: totalReceipts[0].count,
        page,
        perPage: limit,
        receipts: receiptsData,
    };
};
exports.getAllReceipts = getAllReceipts;
/**
 * ✅ **Get Receipt by Sale ID**
 */
const getReceiptBySaleId = async (requester, saleId) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "receipts", saleId);
    const isSuperadmin = requester.role === "superadmin";
    const isCashier = requester.role === "cashier";
    // Different filter logic based on role
    let receiptFilter;
    if (isSuperadmin) {
        // Superadmin can see any receipt
        receiptFilter = (0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId);
    }
    else if (requester.role === "admin") {
        // Admin can only see their own receipts and receipts from users they created
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        const teamMemberIds = teamMembersResult.map((m) => m.memberId);
        if (teamMemberIds.length > 0) {
            // Admin can see their own receipts and receipts from users they created
            receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, teamMemberIds)));
        }
        else {
            // If no team members found, just show their own receipts
            receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
        }
    }
    else if (isCashier) {
        // Cashier can only see their own receipts and receipts from their admin and siblings
        // Find the cashier's creator (admin)
        const creatorResult = await db_1.postgresDb
            .select({ creatorId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        const creatorId = creatorResult[0]?.creatorId || undefined;
        if (creatorId) {
            // Get all users created by the same admin (siblings)
            const siblingsResult = await db_1.postgresDb
                .select({ siblingId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
            const siblingIds = siblingsResult.map((s) => s.siblingId);
            // Build the filter to include own receipts, creator's receipts, and siblings' receipts
            receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, siblingIds)));
        }
        else {
            // If no creator found, just show their own receipts
            receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
        }
    }
    else {
        // Default case - just show own receipts
        receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.saleId, saleId), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
    }
    const receiptData = await db_1.postgresDb
        .select({
        id: schema_1.receipts.id,
        saleId: schema_1.receipts.saleId,
        receiptUrl: schema_1.receipts.receiptUrl,
        createdBy: schema_1.users.name,
        createdAt: schema_1.receipts.createdAt,
        // Store information
        storeName: schema_1.stores.name,
        storeAddress: schema_1.stores.address,
        storeCity: schema_1.stores.city,
        storeState: schema_1.stores.state,
        storeCountry: schema_1.stores.country,
        storePhone: schema_1.stores.phone,
        storeEmail: schema_1.stores.email,
        storeLogo: schema_1.stores.logo,
    })
        .from(schema_1.receipts)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, schema_1.users.id))
        .leftJoin(schema_1.sales, (0, drizzle_orm_1.eq)(schema_1.receipts.saleId, schema_1.sales.id))
        .leftJoin(schema_1.stores, (0, drizzle_orm_1.eq)(schema_1.sales.storeId, schema_1.stores.id))
        .where(receiptFilter)
        .limit(1);
    if (receiptData.length === 0)
        throw new Error("Receipt not found or unauthorized.");
    return receiptData[0];
};
exports.getReceiptBySaleId = getReceiptBySaleId;
/**
 * ✅ **Delete Receipt by ID (Single or Multiple)**
 */
const deleteReceiptById = async (requester, ids) => {
    const receiptIds = Array.isArray(ids) ? ids : [ids];
    // Check authorization for each receipt
    for (const id of receiptIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "receipts", id);
    }
    const isSuperadmin = requester.role === "superadmin";
    const isCashier = requester.role === "cashier";
    // For each receipt, ensure it exists and user has permission
    for (const id of receiptIds) {
        // Different filter logic based on role
        let receiptFilter;
        if (isSuperadmin) {
            // Superadmin can delete any receipt
            receiptFilter = (0, drizzle_orm_1.eq)(schema_1.receipts.id, id);
        }
        else if (isCashier || requester.role === "admin") {
            // For both cashiers and admins, we need to find their team
            // First, find the creator of this user (their admin)
            const creatorResult = await db_1.postgresDb
                .select({ creatorId: schema_1.users.createdBy })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
                .limit(1);
            const creatorId = creatorResult[0]?.creatorId || undefined;
            // For admins, get all users they created
            let teamMemberIds = [];
            if (requester.role === "admin") {
                const teamMembersResult = await db_1.postgresDb
                    .select({ memberId: schema_1.users.id })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
                teamMemberIds = teamMembersResult.map((m) => m.memberId);
            }
            // Build the filter based on the user's role and relationships
            if (requester.role === "admin" && teamMemberIds.length > 0) {
                // Admin can delete their own receipts and receipts from users they created
                receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.id, id), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, teamMemberIds)));
            }
            else if (creatorId) {
                // If this user has a creator (admin), they can delete their own receipts,
                // their creator's receipts, and receipts from other users created by the same admin
                // First, get all users created by the same admin (siblings)
                const siblingsResult = await db_1.postgresDb
                    .select({ siblingId: schema_1.users.id })
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
                const siblingIds = siblingsResult.map((s) => s.siblingId);
                // Build the filter to include own receipts, creator's receipts, and siblings' receipts
                receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.id, id), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, siblingIds)));
            }
            else {
                // If no relationships found, just show their own receipts
                receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.id, id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
            }
        }
        else {
            // Default case - just show own receipts
            receiptFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.receipts.id, id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
        }
        const existingReceipt = await db_1.postgresDb
            .select()
            .from(schema_1.receipts)
            .where(receiptFilter)
            .limit(1);
        if (existingReceipt.length === 0) {
            throw new Error(`Receipt with ID ${id} not found or unauthorized.`);
        }
    }
    // Build the delete filter based on the user's role
    let deleteFilter;
    if (isSuperadmin) {
        // Superadmin can delete any receipt
        deleteFilter = (0, drizzle_orm_1.inArray)(schema_1.receipts.id, receiptIds);
    }
    else if (isCashier || requester.role === "admin") {
        // For both cashiers and admins, we need to find their team
        // First, find the creator of this user (their admin)
        const creatorResult = await db_1.postgresDb
            .select({ creatorId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        const creatorId = creatorResult[0]?.creatorId || undefined;
        // For admins, get all users they created
        let teamMemberIds = [];
        if (requester.role === "admin") {
            const teamMembersResult = await db_1.postgresDb
                .select({ memberId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
            teamMemberIds = teamMembersResult.map((m) => m.memberId);
        }
        // Build the filter based on the user's role and relationships
        if (requester.role === "admin" && teamMemberIds.length > 0) {
            // Admin can delete their own receipts and receipts from users they created
            deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.receipts.id, receiptIds), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, teamMemberIds)));
        }
        else if (creatorId) {
            // If this user has a creator (admin), they can delete their own receipts,
            // their creator's receipts, and receipts from other users created by the same admin
            // First, get all users created by the same admin (siblings)
            const siblingsResult = await db_1.postgresDb
                .select({ siblingId: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, creatorId));
            const siblingIds = siblingsResult.map((s) => s.siblingId);
            // Build the filter to include own receipts, creator's receipts, and siblings' receipts
            deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.receipts.id, receiptIds), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, creatorId), (0, drizzle_orm_1.inArray)(schema_1.receipts.createdBy, siblingIds)));
        }
        else {
            // If no relationships found, just delete their own receipts
            deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.receipts.id, receiptIds), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
        }
    }
    else {
        // Default case - just delete own receipts
        deleteFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.receipts.id, receiptIds), (0, drizzle_orm_1.eq)(schema_1.receipts.createdBy, requester.id));
    }
    // Delete the receipts
    const deletedReceipts = await db_1.postgresDb
        .delete(schema_1.receipts)
        .where(deleteFilter)
        .returning({ deletedId: schema_1.receipts.id });
    if (!deletedReceipts || deletedReceipts.length === 0) {
        throw new Error("Delete failed: Receipt(s) not found.");
    }
    return {
        deletedIds: deletedReceipts.map((receipt) => receipt.deletedId)
    };
};
exports.deleteReceiptById = deleteReceiptById;
