import { and, eq, desc, sql, count, inArray, or } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { expenseCategories, users } from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";
import { expenseCategorySchema } from "../validation/schema";

/**
 * ✅ Create a new expense category
 */
export const createExpenseCategory = async (
  requester: JwtPayload,
  categoryData: {
    name: string;
    description?: string;
    color?: string;
    isDefault?: boolean;
  }
) => {
  // Validate category data
  expenseCategorySchema.parse(categoryData);
  await authorizeAction(requester, "create", "expense_categories");

  const createdBy = requester.id;

  // Check if category name already exists for this user
  const existingCategory = await postgresDb
    .select()
    .from(expenseCategories)
    .where(
      and(
        eq(expenseCategories.name, categoryData.name),
        eq(expenseCategories.createdBy, createdBy)
      )
    )
    .limit(1);

  if (existingCategory.length > 0) {
    throw new Error("Category with this name already exists.");
  }

  // Insert new category
  const [newCategory] = await postgresDb
    .insert(expenseCategories)
    .values({
      ...categoryData,
      createdBy,
    })
    .returning();

  return newCategory;
};

/**
 * ✅ Get all expense categories
 */
export const getAllExpenseCategories = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 50,
  search: string = ''
) => {
  await authorizeAction(requester, "getAll", "expense_categories");

  // Build team member IDs for admin users
  let teamMemberIds: number[] = [];
  if (requester.role === "admin") {
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);
  }

  // Include the requester's ID in the team IDs, plus default categories
  const teamIds = requester.role === "admin"
    ? [requester.id, ...teamMemberIds]
    : [requester.id];

  // Get categories created by user/team OR default categories
  let whereConditions = or(
    inArray(expenseCategories.createdBy, teamIds),
    eq(expenseCategories.isDefault, true)
  );

  // Add search condition if provided
  if (search && search.trim()) {
    const searchTerm = `%${search.trim()}%`;
    whereConditions = and(
      whereConditions,
      or(
        sql`${expenseCategories.name} ILIKE ${searchTerm}`,
        sql`${expenseCategories.description} ILIKE ${searchTerm}`
      )
    );
  }

  // Get total count
  const totalCountResult = await postgresDb
    .select({ count: count() })
    .from(expenseCategories)
    .where(whereConditions);

  const totalCount = totalCountResult[0]?.count || 0;

  // Get categories
  const categoriesResult = await postgresDb
    .select()
    .from(expenseCategories)
    .where(whereConditions)
    .orderBy(desc(expenseCategories.isDefault), expenseCategories.name)
    .limit(limit)
    .offset((page - 1) * limit);

  return {
    categories: categoriesResult,
    total: totalCount,
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
  };
};

/**
 * ✅ Get expense category by ID
 */
export const getExpenseCategoryById = async (
  requester: JwtPayload,
  categoryId: number
) => {
  await authorizeAction(requester, "getById", "expense_categories", categoryId);

  const categoryResult = await postgresDb
    .select()
    .from(expenseCategories)
    .where(eq(expenseCategories.id, categoryId))
    .limit(1);

  if (categoryResult.length === 0) {
    throw new Error("Expense category not found.");
  }

  return categoryResult[0];
};

/**
 * ✅ Update expense category by ID
 */
export const updateExpenseCategoryById = async (
  requester: JwtPayload,
  categoryId: number,
  updateData: Partial<{
    name: string;
    description: string;
    color: string;
  }>
) => {
  await authorizeAction(requester, "update", "expense_categories", categoryId);

  // Check if it's a default category (cannot be updated)
  const category = await postgresDb
    .select()
    .from(expenseCategories)
    .where(eq(expenseCategories.id, categoryId))
    .limit(1);

  if (category.length === 0) {
    throw new Error("Category not found.");
  }

  if (category[0].isDefault) {
    throw new Error("Default categories cannot be modified.");
  }

  // Check if new name conflicts with existing categories
  if (updateData.name) {
    const existingCategory = await postgresDb
      .select()
      .from(expenseCategories)
      .where(
        and(
          eq(expenseCategories.name, updateData.name),
          eq(expenseCategories.createdBy, requester.id),
          sql`${expenseCategories.id} != ${categoryId}`
        )
      )
      .limit(1);

    if (existingCategory.length > 0) {
      throw new Error("Category with this name already exists.");
    }
  }

  const [updatedCategory] = await postgresDb
    .update(expenseCategories)
    .set(updateData)
    .where(eq(expenseCategories.id, categoryId))
    .returning();

  if (!updatedCategory) {
    throw new Error("Category not found or update failed.");
  }

  return updatedCategory;
};

/**
 * ✅ Delete expense category(s) by ID(s)
 */
export const deleteExpenseCategoryById = async (
  requester: JwtPayload,
  categoryIds: number[]
) => {
  // Validate each category before deletion
  for (const categoryId of categoryIds) {
    await authorizeAction(requester, "delete", "expense_categories", categoryId);

    // Check if it's a default category (cannot be deleted)
    const category = await postgresDb
      .select()
      .from(expenseCategories)
      .where(eq(expenseCategories.id, categoryId))
      .limit(1);

    if (category.length > 0 && category[0].isDefault) {
      throw new Error(`Default category "${category[0].name}" cannot be deleted.`);
    }
  }

  const deletedCategories = await postgresDb
    .delete(expenseCategories)
    .where(
      and(
        inArray(expenseCategories.id, categoryIds),
        eq(expenseCategories.isDefault, false) // Extra safety check
      )
    )
    .returning({ id: expenseCategories.id, name: expenseCategories.name });

  if (deletedCategories.length === 0) {
    throw new Error("No categories found for deletion or all are default categories.");
  }

  return {
    deletedCount: deletedCategories.length,
    deletedCategories,
  };
};

/**
 * ✅ Initialize default expense categories for a new user
 */
export const initializeDefaultExpenseCategories = async (userId: number) => {
  const defaultCategories = [
    { name: "Office Supplies", description: "Stationery, equipment, and office materials", color: "#3B82F6", isDefault: true },
    { name: "Utilities", description: "Electricity, water, internet, phone bills", color: "#EF4444", isDefault: true },
    { name: "Rent", description: "Office or store rent payments", color: "#8B5CF6", isDefault: true },
    { name: "Marketing", description: "Advertising, promotions, and marketing expenses", color: "#10B981", isDefault: true },
    { name: "Transportation", description: "Fuel, vehicle maintenance, delivery costs", color: "#F59E0B", isDefault: true },
    { name: "Professional Services", description: "Legal, accounting, consulting fees", color: "#6366F1", isDefault: true },
    { name: "Insurance", description: "Business insurance premiums", color: "#EC4899", isDefault: true },
    { name: "Maintenance", description: "Equipment and facility maintenance", color: "#14B8A6", isDefault: true },
    { name: "Training", description: "Staff training and development", color: "#F97316", isDefault: true },
    { name: "Miscellaneous", description: "Other business expenses", color: "#6B7280", isDefault: true },
  ];

  try {
    await postgresDb
      .insert(expenseCategories)
      .values(
        defaultCategories.map(cat => ({
          ...cat,
          createdBy: userId,
        }))
      );
  } catch (error) {
    // If categories already exist, ignore the error
    console.log("Default expense categories may already exist for user:", userId);
  }
};
