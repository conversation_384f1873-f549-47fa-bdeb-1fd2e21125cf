(()=>{var e={};e.id=8407,e.ids=[8407],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},16572:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(70260),r=s(28203),n=s(25155),l=s.n(n),i=s(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["payment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38996)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,33439)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/payment/page",pathname:"/payment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4067:(e,t,s)=>{Promise.resolve().then(s.bind(s,38996))},18139:(e,t,s)=>{Promise.resolve().then(s.bind(s,8115))},39193:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),r=s(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var l=s(78480);let i=r.forwardRef(function(e,t){return r.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},67586:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),r=s(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var l=s(78480);let i=r.forwardRef(function(e,t){return r.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},45211:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),r=s(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var l=s(78480);let i=r.forwardRef(function(e,t){return r.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},8115:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(45512),r=s(58009),n=s(6987),l=s(41257),i=s(26222),o=s(49198),c=s(9334),d=s(37764),u=s(3117),m=s(22127);s(49792);var x=s(79334),p=s(92273),h=s(9269);s(97245),s(42211);let y={monthly:{price:40,discount:0,label:"Monthly",description:"Basic subscription plan",period:1},quarterly:{price:108,discount:10,label:"Quarterly",description:"Save 10% with quarterly billing",period:3},annual:{price:360,discount:25,label:"Annual",description:"Save 25% with annual billing",period:12}},g=({onSuccess:e})=>{let[t]=l.A.useForm(),[s,{isLoading:g}]=(0,h.nm)(),[f,{isLoading:b}]=(0,h.Ex)(),[j,v]=(0,r.useState)(null),[N,w]=(0,r.useState)("monthly"),[P,A]=(0,r.useState)(null);(0,x.useRouter)(),(0,p.wA)(),(0,p.d4)(e=>e.auth.accessToken);let k=(0,p.d4)(e=>e.auth.user);(0,r.useEffect)(()=>{let e=y[N];t.setFieldsValue({amount:Math.round(e.price),email:k?.email||""})},[N,t,k]);let C=async e=>{try{v(null);let t=y[N];console.log("\uD83D\uDD04 Initializing payment with subscription period:",{selectedPlan:N,planDetails:t,subscriptionPeriod:t.period,amount:e.amount});let a=await s({amount:e.amount,email:e.email,callbackUrl:`${window.location.origin}/payment/callback`,subscriptionPeriod:t.period}).unwrap();a.success&&a.data?(A({authorizationUrl:a.data.authorizationUrl,reference:a.data.reference,email:e.email,amount:e.amount,key:"pk_live_b6fdd41efeeabee8a2aa28c1fc89445f9d8f2eca"}),console.log("Payment initialized successfully:",{authorizationUrl:a.data.authorizationUrl,reference:a.data.reference,amount:e.amount})):v(a.message||"Failed to initialize payment")}catch(e){v(e.data?.message||e.message||"An error occurred during payment initialization.")}},D=e=>{let t=y[e],s=N===e;return(0,a.jsxs)(n.A,{className:`bg-white border-2 transition-all duration-200 h-full ${s?"border-blue-500 shadow-lg":"border-gray-200"}`,styles:{body:{padding:"16px",height:"100%",display:"flex",flexDirection:"column"}},onClick:()=>w(e),children:["annual"===e&&(0,a.jsx)(i.A.Ribbon,{text:"Best Value",color:"blue",className:"absolute top-0 right-0",children:(0,a.jsx)("div",{className:"h-6"})}),(0,a.jsxs)("div",{className:"flex flex-col h-full flex-grow",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:t.label}),s&&(0,a.jsx)(m.A,{className:"text-blue-500 text-xl"})]}),(0,a.jsx)("div",{className:"text-gray-600 text-sm mb-3",children:t.description}),(0,a.jsxs)("div",{className:"mt-auto",children:[(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsxs)("span",{className:"text-2xl font-bold text-gray-800",children:["GH₵",Math.round(t.price)]}),t.discount>0&&(0,a.jsxs)("span",{className:"ml-2 text-sm text-green-500",children:["Save ",t.discount,"%"]})]}),(0,a.jsx)("div",{className:"text-gray-500 text-xs mt-1",children:"monthly"===e?"per month":"quarterly"===e?"every 3 months":"per year"})]})]})]})};return(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6 text-gray-800 pl-2",children:"Choose a Subscription Plan"}),j&&(0,a.jsx)(o.A,{message:"Payment Error",description:j,type:"error",showIcon:!0,className:"mb-4",closable:!0,onClose:()=>v(null)}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:Object.keys(y).map(e=>(0,a.jsx)("div",{className:"col-span-1 h-full",children:(0,a.jsx)("div",{className:"h-full",children:D(e)})},e))}),(0,a.jsx)(c.A,{className:"border-gray-200 my-6"}),(0,a.jsx)("h3",{className:"text-xl font-bold mb-4 text-gray-800 pl-2",children:"Payment Details"}),(0,a.jsx)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg mx-2",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:[(0,a.jsx)("strong",{children:"\uD83D\uDD12 Secure Payment:"})," All payments are processed securely through Paystack. You can pay with cards, or mobile money."]})}),(0,a.jsxs)(l.A,{form:t,layout:"vertical",onFinish:C,initialValues:{amount:y.monthly.price,email:k?.email||"",plan:"monthly"},className:"px-2",children:[(0,a.jsx)(l.A.Item,{name:"plan",hidden:!0,children:(0,a.jsx)(d.A,{type:"hidden",value:N})}),(0,a.jsx)(l.A.Item,{label:(0,a.jsx)("span",{className:"text-gray-700",children:"Amount (GH₵)"}),name:"amount",children:(0,a.jsx)(d.A,{className:"bg-white border-gray-300 text-gray-800",disabled:!0,prefix:(0,a.jsx)("span",{className:"text-gray-600",children:"GH₵"}),suffix:(0,a.jsx)("span",{className:"text-gray-600",children:"GHS"}),style:{color:"#333333"}})}),(0,a.jsx)(l.A.Item,{label:(0,a.jsx)("span",{className:"text-gray-700",children:"Email Address"}),name:"email",rules:[{required:!0,message:"Please enter your email address"},{type:"email",message:"Please enter a valid email address"}],children:(0,a.jsx)(d.A,{placeholder:"<EMAIL>",className:"bg-white border-gray-300 text-gray-800",style:{color:"#333333"}})}),(0,a.jsx)(l.A.Item,{className:"mt-6",children:P?(0,a.jsx)(u.Ay,{type:"primary",onClick:()=>{if(!P||!P.authorizationUrl){v("Payment not initialized. Please try again.");return}console.log("Redirecting to Paystack checkout:",P.authorizationUrl),window.location.href=P.authorizationUrl},className:"w-full bg-green-500 hover:bg-green-600 h-12 text-base font-medium",loading:b,children:b?"Processing Payment...":"Pay with Paystack"}):(0,a.jsx)(u.Ay,{type:"primary",htmlType:"submit",className:"w-full bg-blue-500 hover:bg-blue-600 h-12 text-base font-medium",loading:g,children:g?"Initializing...":"Initialize Payment"})})]}),b&&(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsx)(o.A,{message:"Verifying Payment",description:"Please wait while we verify your payment...",type:"info",showIcon:!0})})]})};var f=s(46902),b=s(3971),j=s(45103);function v(){return(0,p.d4)(e=>e.auth.user),(0,x.useRouter)(),null}function N(){return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center border border-gray-200",children:[(0,a.jsx)(j.default,{src:"/images/access.jpg",alt:"Admin Payment Required",className:"mx-auto mb-6 w-32 h-32 object-contain",width:128,height:128}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Access Restricted"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-6",children:["Your admin needs to make a payment for you to access the dashboard.",(0,a.jsx)("br",{}),"Please contact your admin to renew the subscription."]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 text-sm px-4 py-2 rounded-full font-semibold",children:"Waiting for admin payment"})})]})})}function w(){let e=(0,p.d4)(e=>e.auth.user);return(0,x.useRouter)(),(0,b.X)(),(0,p.wA)(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v,{}),e?"cashier"===e.role&&"paid"!==e.paymentStatus?(0,a.jsx)(N,{}):"admin"===e.role&&"paid"!==e.paymentStatus?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Subscription Payment"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Choose your subscription plan and complete the payment"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(g,{})}),(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)(f.A,{showPayButton:!1}),(0,a.jsxs)(n.A,{title:(0,a.jsx)("span",{className:"text-gray-800 pl-2",children:"Payment Instructions"}),className:"mt-6 shadow-md bg-white border border-gray-200",styles:{header:{backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"},body:{backgroundColor:"#ffffff"}},children:[(0,a.jsxs)("ol",{className:"list-decimal pl-5 space-y-2 text-gray-700",children:[(0,a.jsxs)("li",{children:["Select your preferred subscription plan (Monthly: ",(0,a.jsx)("strong",{className:"text-gray-900",children:"₵40"}),", Quarterly: ",(0,a.jsx)("strong",{className:"text-gray-900",children:"₵108"}),", or Annual: ",(0,a.jsx)("strong",{className:"text-gray-900",children:"₵360"}),")"]}),(0,a.jsx)("li",{children:"Enter your email address for payment confirmation"}),(0,a.jsxs)("li",{children:["Click ",(0,a.jsx)("strong",{className:"text-gray-900",children:'"Initialize Payment"'})," to set up your Paystack payment"]}),(0,a.jsxs)("li",{children:["Click ",(0,a.jsx)("strong",{className:"text-gray-900",children:'"Pay Now"'})," to complete payment securely via Paystack"]}),(0,a.jsx)("li",{children:"Your subscription will be activated automatically after successful payment"})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-blue-800 mb-2",children:"\uD83D\uDCB3 Accepted Payment Methods (via Paystack):"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Cards:"})," Visa, Mastercard, Verve (local & international)"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Mobile Money:"})," MTN, Vodafone, AirtelTigo"]})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-green-800 mb-2",children:"\uD83D\uDCA1 Payment Status Not Updated?"}),(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["If your payment was successful but your status still shows as pending, click the ",(0,a.jsx)("strong",{children:"refresh button (\uD83D\uDD04)"})," in the Subscription Status card above to update your status."]})]}),(0,a.jsxs)("p",{className:"mt-4 text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"\uD83D\uDD12 Secure Payment:"})," All payments are processed securely through Paystack, Ghana's leading payment gateway. Your subscription will be activated immediately after verification. For any issues, please contact support."]})]})]})]})]})}):null:null]})}},46902:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var a=s(45512),r=s(58009),n=s(92273),l=s(49198),i=s(53950),o=s(6987),c=s(3117),d=s(26222),u=s(60380),m=s(39193),x=s(45211),p=s(67586),h=s(56403),y=s(79334),g=s(16589),f=s.n(g),b=s(25510),j=s(79188);let v=({showPayButton:e=!0})=>{let t=(0,n.d4)(e=>e.auth.user),s=(0,n.d4)(e=>e.auth.accessToken),g=(0,n.wA)(),v=(0,y.useRouter)(),{needsPayment:N,status:w,daysRemaining:P}=(0,b._)(),[A,k]=(0,r.useState)(!1);if(!t)return(0,a.jsx)(l.A,{message:"Authentication Required",description:"Please log in to view your payment status.",type:"warning",showIcon:!0});let C=()=>{switch(t.paymentStatus){case"paid":return"success";case"pending":return"warning";case"overdue":return"error";default:return"default"}},D=e=>e?f()(e).format("MMM D, YYYY"):"N/A",S=async()=>{if(!t||!s){i.Ay.error("Unable to refresh: User not authenticated");return}k(!0),console.log("\uD83D\uDD04 Manual payment status refresh triggered");try{(await (0,j.hB)({dispatch:g,accessToken:s,currentUser:t,maxRetries:3,retryDelay:1e3})).success?(i.Ay.success("Payment status refreshed successfully! Reloading page..."),console.log("✅ Manual refresh successful"),setTimeout(()=>{window.location.reload()},1500)):(i.Ay.warning("Refresh completed but status may not have changed"),console.log("⚠️ Manual refresh completed with warnings"))}catch(e){console.error("❌ Manual refresh failed:",e),i.Ay.error("Failed to refresh payment status. Please try again.")}finally{k(!1)}};return(0,a.jsx)(o.A,{title:(0,a.jsxs)("div",{className:"flex items-center justify-between pl-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(()=>{switch(t.paymentStatus){case"paid":return(0,a.jsx)(u.A,{className:"text-green-500 text-xl"});case"pending":return(0,a.jsx)(m.A,{className:"text-yellow-500 text-xl"});case"overdue":return(0,a.jsx)(x.A,{className:"text-red-500 text-xl"});case"inactive":return(0,a.jsx)(p.A,{className:"text-gray-500 text-xl"});default:return null}})(),(0,a.jsx)("span",{className:"ml-2 text-gray-800",children:"Subscription Status"})]}),(0,a.jsx)(c.Ay,{type:"text",icon:(0,a.jsx)(h.A,{spin:A}),onClick:S,loading:A,size:"small",className:"text-gray-600 hover:text-blue-600",title:"Refresh payment status"})]}),className:"w-full shadow-md bg-white border border-gray-200",styles:{header:{backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"},body:{backgroundColor:"#ffffff"}},children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4 px-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,a.jsx)(d.A,{status:C(),text:t.paymentStatus.toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Plan:"}),(0,a.jsx)("span",{className:"text-gray-800",children:(()=>{if(!t?.lastPaymentDate||!t?.nextPaymentDue)return"Monthly (₵40/month)";let e=f()(t.lastPaymentDate),s=f()(t.nextPaymentDue),a=s.diff(e,"day"),r=s.diff(e,"month",!0);return a>=350||r>=11?(console.log("✅ Detected: Annual subscription"),"Annual (₵360/year)"):a>=80||r>=2.5?(console.log("✅ Detected: Quarterly subscription"),"Quarterly (₵108/3 months)"):(console.log("✅ Detected: Monthly subscription"),"Monthly (₵40/month)")})()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Last Payment:"}),(0,a.jsx)("span",{className:"text-gray-800",children:D(t.lastPaymentDate)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next Payment Due:"}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-gray-800",children:D(t.nextPaymentDue)}),t.nextPaymentDue&&"paid"===t.paymentStatus&&(0,a.jsx)("div",{className:"text-xs text-green-600",children:(()=>{let e=f()(t.nextPaymentDue).diff(f()(),"day");return e>0?`${e} days remaining`:0===e?"Due today":`${Math.abs(e)} days overdue`})()})]})]}),(0,a.jsx)(l.A,{message:(()=>{switch(t.paymentStatus){case"paid":return"Your subscription is active";case"pending":return"Your payment is pending verification";case"overdue":return"Your payment is overdue";case"inactive":return"Your subscription is inactive";default:return"Unknown status"}})(),type:C(),showIcon:!0,className:"mt-2"}),e&&N&&(0,a.jsx)(c.Ay,{type:"primary",onClick:()=>{v.push("/payment")},className:"w-full mt-4 bg-blue-600 hover:bg-blue-700 h-10 font-medium",children:"Manage Subscription"})]})})}},3971:(e,t,s)=>{"use strict";s.d(t,{X:()=>n});var a=s(58009),r=s(92273);function n(){(0,r.wA)();let e=(0,r.d4)(e=>e.auth.user),t=(0,r.d4)(e=>e.auth.accessToken);(0,a.useRef)(e),(0,a.useRef)(t)}s(42211),s(97245)},38996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\payment\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\payment\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,7624,7175,7764,1257,3950,5482,8042],()=>s(16572));module.exports=a})();