import { Dispatch } from '@reduxjs/toolkit';
import { userApi } from '@/reduxRTK/services/authApi';
import { setUser } from '@/reduxRTK/services/authSlice';

/**
 * Force refresh user data from backend to get latest payment status
 * This bypasses any caching and gets fresh data
 */
export const forceRefreshUserData = async (
  dispatch: Dispatch,
  accessToken: string,
  userId: number
): Promise<{ success: boolean; user?: any; error?: string }> => {
  try {
    console.log('🔄 Force refreshing user data from backend for user:', userId);

    // Invalidate all user-related cache first
    dispatch(userApi.util.invalidateTags(['User']));

    // Wait a moment for cache invalidation
    await new Promise(resolve => setTimeout(resolve, 500));

    // Force a fresh API call to get current user data
    const result = await (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, {
      forceRefetch: true // This forces a fresh API call
    }));

    if ('data' in result && result.data?.success && result.data.data) {
      const freshUser = result.data.data;

      console.log('✅ Fresh user data retrieved:', {
        userId: freshUser.id,
        paymentStatus: freshUser.paymentStatus,
        lastPaymentDate: freshUser.lastPaymentDate,
        nextPaymentDue: freshUser.nextPaymentDue
      });

      // Update Redux state with fresh data
      dispatch(setUser({ user: freshUser, accessToken }));

      return { success: true, user: freshUser };
    } else {
      console.error('❌ Failed to get fresh user data:', result);
      return { success: false, error: 'Failed to fetch user data' };
    }
  } catch (error: any) {
    console.error('❌ Error force refreshing user data:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check if user data is stale and needs refresh
 */
export const isUserDataStale = (user: any): boolean => {
  if (!user) return true;

  // Check if user data was fetched more than 5 minutes ago
  const lastFetch = user._lastFetch || 0;
  const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);

  return lastFetch < fiveMinutesAgo;
};

/**
 * Add timestamp to user data to track when it was fetched
 */
export const addFetchTimestamp = (user: any): any => {
  return {
    ...user,
    _lastFetch: Date.now()
  };
};
