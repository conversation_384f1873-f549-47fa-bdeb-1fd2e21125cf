import "@/css/satoshi.css";
import "@/css/style.css";
import "flatpickr/dist/flatpickr.min.css";
import "./global-spinner.css";

import NextTopLoader from "nextjs-toploader";
import type { PropsWithChildren } from "react";
import { Providers } from "./providers";
import ClientProvider from "@/provider/Provider";
import { Toaster } from "react-hot-toast";
import { SidebarProvider } from "@/components/Layouts/sidebar/sidebar-context";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "NEXAPO POS | Business Management System",
  description: "Complete business management platform with offline POS capabilities",
  manifest: "/manifest.json",
  themeColor: "#2563eb",
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "NEXAPO POS",
  },
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/images/logo.png", sizes: "192x192", type: "image/png" },
      { url: "/images/logo.png", sizes: "512x512", type: "image/png" }
    ],
    apple: [
      { url: "/images/logo.png", sizes: "180x180", type: "image/png" }
    ],
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "NEXAPO POS",
    "application-name": "NEXAPO POS",
    "msapplication-TileColor": "#2563eb",
    "msapplication-tap-highlight": "no",
  },
};

export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full">
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="NEXAPO POS" />
        <link rel="apple-touch-icon" href="/images/logo.png" />
      </head>
      <body suppressHydrationWarning={true} className="h-screen w-screen overflow-x-hidden">
        <Providers>
          <ClientProvider>
            <SidebarProvider>
              <NextTopLoader color="#5750F1" showSpinner={true} />
              {children}
              <Toaster position="top-center" reverseOrder={false} />
            </SidebarProvider>
          </ClientProvider>
        </Providers>

        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', async function() {
                  try {
                    // Unregister any existing service workers first
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for(let registration of registrations) {
                      await registration.unregister();
                    }

                    // Register the new service worker
                    const swPath = '/sw.js';
                    const registration = await navigator.serviceWorker.register(swPath);
                    console.log('Service Worker registration successful with scope: ', registration.scope);

                    // Handle updates
                    registration.addEventListener('updatefound', () => {
                      const newWorker = registration.installing;
                      newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                          console.log('New service worker installed');
                        }
                      });
                    });
                  } catch (error) {
                    console.error('Service Worker registration failed: ', error);
                  }
                });

                // Handle offline/online events
                window.addEventListener('online', function() {
                  console.log('Back online');
                  window.location.reload();
                });

                window.addEventListener('offline', function() {
                  console.log('Gone offline');
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
