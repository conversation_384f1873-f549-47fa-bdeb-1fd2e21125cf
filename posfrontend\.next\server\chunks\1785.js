"use strict";exports.id=1785,exports.ids=[1785],exports.modules={81045:(e,t,l)=>{l.d(t,{A:()=>s});var n=l(11855),a=l(58009);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var i=l(78480);let s=a.forwardRef(function(e,t){return a.createElement(i.A,(0,n.A)({},e,{ref:t,icon:o}))})},12869:(e,t,l)=>{l.d(t,{A:()=>A});var n=l(58009),a=l.n(n),o=l(56073),i=l.n(o),s=l(83893),r=l(27343),c=l(43089),d=l(52271);let b={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},m=a().createContext({});var g=l(86866),p=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let u=e=>(0,g.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var O=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let y=(e,t)=>{let[l,a]=(0,n.useMemo)(()=>(function(e,t){let l=[],n=[],a=!1,o=0;return e.filter(e=>e).forEach(e=>{let{filled:i}=e,s=O(e,["filled"]);if(i){n.push(s),l.push(n),n=[],o=0;return}let r=t-o;(o+=e.span||1)>=t?(o>t?(a=!0,n.push(Object.assign(Object.assign({},s),{span:r}))):n.push(s),l.push(n),n=[],o=0):n.push(s)}),n.length>0&&l.push(n),[l=l.map(e=>{let l=e.reduce((e,t)=>e+(t.span||1),0);if(l<t){let n=e[e.length-1];n.span=t-(l-(n.span||1))}return e}),a]})(t,e),[t,e]);return l},v=e=>{let{itemPrefixCls:t,component:l,span:a,className:o,style:s,labelStyle:r,contentStyle:c,bordered:d,label:b,content:g,colon:p,type:u,styles:O}=e,{classNames:y}=n.useContext(m);return d?n.createElement(l,{className:i()({[`${t}-item-label`]:"label"===u,[`${t}-item-content`]:"content"===u,[`${null==y?void 0:y.label}`]:"label"===u,[`${null==y?void 0:y.content}`]:"content"===u},o),style:s,colSpan:a},null!=b&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==O?void 0:O.label)},b),null!=g&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==O?void 0:O.content)},g)):n.createElement(l,{className:i()(`${t}-item`,o),style:s,colSpan:a},n.createElement("div",{className:`${t}-item-container`},(b||0===b)&&n.createElement("span",{className:i()(`${t}-item-label`,null==y?void 0:y.label,{[`${t}-item-no-colon`]:!p}),style:Object.assign(Object.assign({},r),null==O?void 0:O.label)},b),(g||0===g)&&n.createElement("span",{className:i()(`${t}-item-content`,null==y?void 0:y.content),style:Object.assign(Object.assign({},c),null==O?void 0:O.content)},g)))};function f(e,t,l){let{colon:a,prefixCls:o,bordered:i}=t,{component:s,type:r,showLabel:c,showContent:d,labelStyle:b,contentStyle:m,styles:g}=l;return e.map((e,t)=>{let{label:l,children:p,prefixCls:u=o,className:O,style:y,labelStyle:f,contentStyle:j,span:$=1,key:h,styles:x}=e;return"string"==typeof s?n.createElement(v,{key:`${r}-${h||t}`,className:O,style:y,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),f),null==x?void 0:x.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),j),null==x?void 0:x.content)},span:$,colon:a,component:s,itemPrefixCls:u,bordered:i,label:c?l:null,content:d?p:null,type:r}):[n.createElement(v,{key:`label-${h||t}`,className:O,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),y),f),null==x?void 0:x.label),span:1,colon:a,component:s[0],itemPrefixCls:u,bordered:i,label:l,type:"label"}),n.createElement(v,{key:`content-${h||t}`,className:O,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),y),j),null==x?void 0:x.content),span:2*$-1,component:s[1],itemPrefixCls:u,bordered:i,content:p,type:"content"})]})}let j=e=>{let t=n.useContext(m),{prefixCls:l,vertical:a,row:o,index:i,bordered:s}=e;return a?n.createElement(n.Fragment,null,n.createElement("tr",{key:`label-${i}`,className:`${l}-row`},f(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),n.createElement("tr",{key:`content-${i}`,className:`${l}-row`},f(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):n.createElement("tr",{key:i,className:`${l}-row`},f(o,e,Object.assign({component:s?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var $=l(1439),h=l(47285),x=l(13662),S=l(10941);let w=e=>{let{componentCls:t,labelBg:l}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.padding)} ${(0,$.zA)(e.paddingLG)}`,borderInlineEnd:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:l,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.paddingSM)} ${(0,$.zA)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.paddingXS)} ${(0,$.zA)(e.padding)}`}}}}}},E=e=>{let{componentCls:t,extraColor:l,itemPaddingBottom:n,itemPaddingEnd:a,colonMarginRight:o,colonMarginLeft:i,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,h.dF)(e)),w(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},h.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:a},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,$.zA)(i)} ${(0,$.zA)(o)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},z=(0,x.OF)("Descriptions",e=>E((0,S.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var N=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let C=e=>{let{prefixCls:t,title:l,extra:a,column:o,colon:g=!0,bordered:O,layout:v,children:f,className:$,rootClassName:h,style:x,size:S,labelStyle:w,contentStyle:E,styles:C,items:A,classNames:P}=e,k=N(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:B,direction:M,className:I,style:H,classNames:L,styles:T}=(0,r.TP)("descriptions"),W=B("descriptions",t),X=(0,d.A)(),G=n.useMemo(()=>{var e;return"number"==typeof o?o:null!==(e=(0,s.ko)(X,Object.assign(Object.assign({},b),o)))&&void 0!==e?e:3},[X,o]),F=function(e,t,l){let a=n.useMemo(()=>t||u(l),[t,l]);return n.useMemo(()=>a.map(t=>{var{span:l}=t,n=p(t,["span"]);return"filled"===l?Object.assign(Object.assign({},n),{filled:!0}):Object.assign(Object.assign({},n),{span:"number"==typeof l?l:(0,s.ko)(e,l)})}),[a,e])}(X,A,f),R=(0,c.A)(S),V=y(G,F),[D,q,J]=z(W),K=n.useMemo(()=>({labelStyle:w,contentStyle:E,styles:{content:Object.assign(Object.assign({},T.content),null==C?void 0:C.content),label:Object.assign(Object.assign({},T.label),null==C?void 0:C.label)},classNames:{label:i()(L.label,null==P?void 0:P.label),content:i()(L.content,null==P?void 0:P.content)}}),[w,E,C,P,L,T]);return D(n.createElement(m.Provider,{value:K},n.createElement("div",Object.assign({className:i()(W,I,L.root,null==P?void 0:P.root,{[`${W}-${R}`]:R&&"default"!==R,[`${W}-bordered`]:!!O,[`${W}-rtl`]:"rtl"===M},$,h,q,J),style:Object.assign(Object.assign(Object.assign(Object.assign({},H),T.root),null==C?void 0:C.root),x)},k),(l||a)&&n.createElement("div",{className:i()(`${W}-header`,L.header,null==P?void 0:P.header),style:Object.assign(Object.assign({},T.header),null==C?void 0:C.header)},l&&n.createElement("div",{className:i()(`${W}-title`,L.title,null==P?void 0:P.title),style:Object.assign(Object.assign({},T.title),null==C?void 0:C.title)},l),a&&n.createElement("div",{className:i()(`${W}-extra`,L.extra,null==P?void 0:P.extra),style:Object.assign(Object.assign({},T.extra),null==C?void 0:C.extra)},a)),n.createElement("div",{className:`${W}-view`},n.createElement("table",null,n.createElement("tbody",null,V.map((e,t)=>n.createElement(j,{key:t,index:t,colon:g,prefixCls:W,vertical:"vertical"===v,bordered:O,row:e}))))))))};C.Item=e=>{let{children:t}=e;return t};let A=C}};