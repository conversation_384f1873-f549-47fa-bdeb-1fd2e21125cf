"use client";

import React, { useState, useEffect } from "react";
import { Card, Button, Spin } from "antd";
import "./users-tailwind.css";
import "./users.css";
import { UserAddOutlined, LoadingOutlined } from "@ant-design/icons";
import { User, UserRole } from "@/types/user";
import { useAuth } from "@/hooks/useAuth";
import { useUserList } from "@/hooks/users/useUserList";
import { useUserDelete } from "@/hooks/users/useUserDelete";
import { useUserBulkDelete } from "@/hooks/users/useUserBulkDelete";
import UserTable from "@/components/Users/<USER>";
import UserPagination from "@/components/Users/<USER>";
import UserFormPanel from "@/components/Users/<USER>";
import UserDetailPanel from "@/components/Users/<USER>";
import UserSearch from "@/components/Users/<USER>";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { showMessage } from "@/utils/showMessage";

// Custom hook for responsive design
const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    // Handler to call on window resize
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);

      // Call handler right away so state gets updated with initial window size
      handleResize();

      // Remove event listener on cleanup
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []); // Empty array ensures that effect is only run on mount and unmount

  return windowSize;
};

const UsersPage = () => {
  const { user: currentUser } = useAuth();
  const { width } = useWindowSize();
  const isMobile = width < 640;

  // State for panels
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // Use custom hooks
  const {
    users,
    total,
    isLoading,
    isFetching,
    refetch,
    searchTerm,
    setSearchTerm,
    pagination: { page, pageSize, setPage }
  } = useUserList();

  const {
    confirmDelete,
    cancelDelete,
    executeDelete,
    isDeleting,
    isConfirmOpen,
    userToDelete
  } = useUserDelete(refetch);

  // Bulk delete user handler
  const { bulkDeleteUsers, isDeleting: isBulkDeleting } = useUserBulkDelete(() => {
    setIsBulkDeleteDialogOpen(false);
    refetch();
  });

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [usersToDelete, setUsersToDelete] = useState<number[]>([]);

  // Check if current user is superadmin or admin
  const canManageUsers = currentUser?.role === "superadmin" || currentUser?.role === "admin";

  // Superadmin can manage all users, admin can only manage cashiers
  const canManageUser = (userRole: UserRole) => {
    if (currentUser?.role === "superadmin") return true;
    if (currentUser?.role === "admin" && userRole === "cashier") return true;
    return false;
  };

  const handleAddUser = () => {
    console.log("Add user button clicked");
    setSelectedUser(null);
    // Use a timeout to ensure state updates properly
    setTimeout(() => {
      setIsAddPanelOpen(true);
    }, 0);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditPanelOpen(true);
  };

  const handleViewUser = (userId: number) => {
    setSelectedUserId(userId);
    setIsDetailPanelOpen(true);
  };

  // Handle bulk delete
  const handleBulkDelete = (userIds: number[]) => {
    console.log("handleBulkDelete called with userIds:", userIds);
    setUsersToDelete(userIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with users:", usersToDelete);

    if (usersToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple users
        await bulkDeleteUsers(usersToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setUsersToDelete([]);
  };

  // Monitor isAddPanelOpen changes
  useEffect(() => {
    console.log("Users page - isAddPanelOpen changed:", isAddPanelOpen);
  }, [isAddPanelOpen]);

  // Show welcome message on component mount
  // useEffect(() => {
  //   showMessage("success", "Welcome to User Management");
  // }, []);



  if (isLoading) {
    return (
      <div className="p-2 sm:p-4 w-full">
        <Card
          title={<span className="text-gray-800">User Management</span>}
          styles={{
            body: { padding: '16px', minHeight: '200px', backgroundColor: '#ffffff' },
            header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
          }}
        >
          <div className="flex justify-center items-center h-60">
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
          </div>
        </Card>
      </div>
    );
  }

  // We'll handle empty state within the main layout to keep the search visible

  return (
    <div className="p-2 sm:p-4 w-full">
      <Card
        title={<span className="text-gray-800">User Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: { padding: '12px', overflow: 'hidden', backgroundColor: '#ffffff' },
          header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
        }}
        extra={
          canManageUsers && (
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={handleAddUser}
              size={isMobile ? "small" : "middle"}
            >
              {isMobile ? "" : "Add User"}
            </Button>
          )
        }
      >
        <div className="w-full bg-white rounded-md shadow-sm overflow-hidden">
          {/* Search Component - Always visible */}
          <UserSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isFetching ? (
            <div className="flex justify-center items-center h-60 bg-gray-50">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
            </div>
          ) : (
            <>
              {/* User Table Component */}
              {users.length > 0 ? (
                <UserTable
                  users={users}
                  isMobile={isMobile}
                  onView={handleViewUser}
                  onEdit={handleEditUser}
                  onDelete={confirmDelete}
                  onBulkDelete={handleBulkDelete}
                  canManageUser={canManageUser}
                />
              ) : (
                <div className="flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-700">
                  {searchTerm ? (
                    <>
                      <p>No users found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm('')}
                        className="mt-4 bg-blue-500 hover:bg-blue-600"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No users found. {canManageUsers && "Click 'Add User' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {users.length > 0 && (
                <UserPagination
                  page={page}
                  pageSize={pageSize}
                  total={total}
                  setPage={setPage}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add User Panel */}
      <UserFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => setIsAddPanelOpen(false)}
        onSuccess={() => {
          setIsAddPanelOpen(false);
          refetch();
        }}
        currentUser={currentUser}
      />

      {/* Edit User Panel */}
      <UserFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);
          refetch();
        }}
        user={selectedUser}
        currentUser={currentUser}
      />

      {/* View User Details Panel */}
      <UserDetailPanel
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false);
          setSelectedUserId(null);
        }}
        userId={selectedUserId}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isConfirmOpen}
        onClose={cancelDelete}
        onConfirm={executeDelete}
        title="Delete User"
        message="Are you sure you want to delete this user? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Users"
        message={`Are you sure you want to delete ${usersToDelete.length} users? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default UsersPage;












