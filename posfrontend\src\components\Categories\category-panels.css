/* Custom styles for category panels */

/* Light theme for descriptions */
.category-detail-light {
  color: #333;
  background-color: #ffffff;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.category-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

.category-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

/* Light theme for form items */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Light theme for select */
.ant-select-selector {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85) !important;
}

.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Light theme for input */
.ant-input, .ant-input-password {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-input-password-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Dark theme for tags */
.ant-tag {
  border-color: transparent !important;
}

/* Fix for loading spinner */
.ant-spin-text,
.ant-spin-dot + div,
.ant-spin + div {
  display: none !important;
}

/* Center all spinners */
.ant-spin-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 200px !important;
}

/* Fix for z-index */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .ant-form-item {
    margin-bottom: 12px !important;
  }

  .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .ant-input, .ant-input-password, .ant-select {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    padding: 8px !important;
    font-size: 14px !important;
  }

  /* Ensure form inputs don't overflow */
  .ant-form-item-control-input {
    width: 100% !important;
    max-width: 100% !important;
  }
}
