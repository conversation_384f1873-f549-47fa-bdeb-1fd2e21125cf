(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{4180:(e,t,s)=>{Promise.resolve().then(s.bind(s,30307))},30307:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var r=s(95155),a=s(12115),l=s(71349),i=s(41657),n=s(43316),c=s(72093),d=s(7974),o=s(5413),x=s(72278),m=s(16419),h=s(33621),p=s(44549),g=s(79624),y=s(53452),u=s(47141),b=s(68190),j=s(80766),f=s(92895),w=s(6457),N=s(17084),v=s(5099),A=s(39279),k=s(87181),C=s(80519),D=s(27656),R=s(55750),z=s(60102),S=s(91256),E=s(83391),M=s(21455),P=s.n(M);let B=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"MMM D, YYYY h:mm A";if(!e)return"N/A";let s=P()(e);return s.isValid()?s.format(t):"N/A"},I=e=>{let{receipts:t,loading:s,onViewReceipt:l,onPrintReceipt:i,onDelete:c,onBulkDelete:d,isMobile:o=!1}=e,x=(0,S.E)(),{user:m}=(0,E.d4)(e=>e.auth),[h,p]=(0,a.useState)([]),[g,u]=(0,a.useState)(!1),b=(null==m?void 0:m.role)==="admin"||(null==m?void 0:m.role)==="superadmin",M=e=>{let s=e.target.checked;u(s),s?p(b?t.map(e=>e.id):[]):p([])},P=(e,t)=>{t?p(t=>[...t,e]):p(t=>t.filter(t=>t!==e))};return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[h.length>0&&b&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[h.length," ",1===h.length?"receipt":"receipts"," selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(N.A,{}),onClick:()=>{h.length>0&&d?(d(h),p([]),u(!1)):j.Ay.warning({message:"No receipts selected",description:"Please select at least one receipt to delete."})},className:"ml-2",children:"Delete Selected"})]}),o||x?(0,r.jsxs)(z.jB,{columns:b&&c?"50px 120px 150px 120px 120px 150px":"120px 150px 120px 120px 150px",minWidth:b&&c?"750px":"700px",children:[b&&c&&(0,r.jsx)(z.A0,{className:"text-center",children:(0,r.jsx)(f.A,{checked:g,onChange:M,disabled:0===t.length})}),(0,r.jsx)(z.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"mr-1"}),"Receipt ID"]})}),(0,r.jsx)(z.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"mr-1"}),"Sale ID"]})}),(0,r.jsx)(z.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Store"]})}),(0,r.jsx)(z.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)(z.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,r.jsxs)(z.Hj,{selected:h.includes(e.id),children:[b&&c&&(0,r.jsx)(z.nA,{className:"text-center",children:(0,r.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>P(e.id,t.target.checked)})}),(0,r.jsx)(z.nA,{children:(0,r.jsxs)("span",{className:"font-medium",children:["#",e.id]})}),(0,r.jsx)(z.nA,{children:(0,r.jsxs)("span",{className:"text-blue-600",children:["#",e.saleId]})}),(0,r.jsx)(z.nA,{children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.storeName})}),(0,r.jsx)(z.nA,{children:(0,r.jsx)("span",{className:"text-sm",children:B(e.createdAt)})}),(0,r.jsx)(z.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(w.A,{title:"View Receipt",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>l(e.receiptUrl),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),(0,r.jsx)(w.A,{title:"Print Receipt",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(y.A,{}),onClick:()=>i(e.receiptUrl),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),b&&c&&(0,r.jsx)(w.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(D.A,{}),onClick:()=>c(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[b&&c&&(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(f.A,{checked:g,onChange:M,disabled:0===t.length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"mr-1"}),"Receipt ID"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"mr-1"}),"Sale ID"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Store"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(R.A,{className:"mr-1"}),"Created By"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("tr",{className:h.includes(e.id)?"bg-blue-50":"",children:[b&&c&&(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:(0,r.jsx)(f.A,{checked:h.includes(e.id),onChange:t=>P(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsxs)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:["#",e.id]})}),(0,r.jsxs)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:["#",e.saleId]}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.storeName||"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.createdBy||"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:B(e.createdAt)}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(w.A,{title:"View Receipt",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>l(e.receiptUrl),type:"text",className:"view-button text-green-500",size:"middle"})}),(0,r.jsx)(w.A,{title:"Print Receipt",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(y.A,{}),onClick:()=>i(e.receiptUrl),type:"text",className:"edit-button text-blue-500",size:"middle"})}),b&&c&&(0,r.jsx)(w.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(D.A,{}),onClick:()=>c(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})})]})};var W=s(75912);let T=e=>{let[t,{isLoading:s}]=(0,u.iB)();return{deleteReceipt:async s=>{try{console.log("Deleting receipt with ID:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete receipt");return(0,W.r)("success","Receipt deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete receipt error:",e),(0,W.r)("error",e.message||"Failed to delete receipt"),e}},isDeleting:s}},F=e=>{let[t,{isLoading:s}]=(0,u.rN)();return{bulkDeleteReceipts:async s=>{try{console.log("Bulk deleting receipts with IDs:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete receipts");return(0,W.r)("success","".concat(s.length," receipts deleted successfully")),e&&e(),r.data}catch(e){throw console.error("Bulk delete receipts error:",e),(0,W.r)("error",e.message||"Failed to delete receipts"),e}},isDeleting:s}};var _=s(12467);function O(){var e,t;let[s,j]=(0,a.useState)(1),[f]=(0,a.useState)(10),[w,N]=(0,a.useState)(""),[v,A]=(0,a.useState)(null),[k,C]=(0,a.useState)(!1),D=(0,b.Ub)({maxWidth:768}),[R,z]=(0,a.useState)(!1),[S,E]=(0,a.useState)(null),[M,P]=(0,a.useState)(!1),[B,W]=(0,a.useState)([]),{data:O,isLoading:U,refetch:V}=(0,u.sK)({page:s,limit:f,search:w}),{deleteReceipt:Y,isDeleting:L}=T(()=>{z(!1),V()}),{bulkDeleteReceipts:H,isDeleting:K}=F(()=>{P(!1),V()});(0,a.useEffect)(()=>{let e=setInterval(()=>{V()},1e4);return()=>clearInterval(e)},[V]);let Q=async()=>{C(!0),await V(),C(!1)},$=e=>{j(e)},q=e=>{let t=document.createElement("iframe");t.style.display="none",document.body.appendChild(t),t.onload=()=>{t.contentWindow&&(t.contentWindow.document.write('\n          <!DOCTYPE html>\n          <html>\n            <head>\n              <title>Print Receipt</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 0;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                }\n                @media print {\n                  body {\n                    margin: 0;\n                    padding: 0;\n                  }\n                  img {\n                    width: 100%;\n                    height: auto;\n                  }\n                }\n              </style>\n            </head>\n            <body>\n              <img src="'.concat(e,'" alt="Receipt" />\n            </body>\n          </html>\n        ')),t.contentWindow.document.close(),setTimeout(()=>{t.contentWindow&&(t.contentWindow.focus(),t.contentWindow.print(),setTimeout(()=>{document.body.removeChild(t)},1e3))},500))},t.src="about:blank"},G=()=>{A(null)},J=async()=>{if(S)try{await Y(S)}catch(e){console.error("Error deleting receipt:",e)}},X=async()=>{if(console.log("confirmBulkDelete called with receipts:",B),B.length>0)try{await H(B)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Receipt Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:D?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},children:(0,r.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 flex items-center justify-between border-b border-gray-200 bg-gray-50 px-3 py-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(i.A,{placeholder:"Search receipts...",prefix:(0,r.jsx)(o.A,{className:"text-gray-400"}),value:w,onChange:e=>{N(e.target.value)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:D?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-600",children:"\xd7"})}}),w&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',w,'"']})]}),(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(x.A,{spin:k}),onClick:Q,loading:U&&!k,className:"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",title:"Refresh receipts",children:!D&&"Refresh"})]}),U?(0,r.jsx)("div",{className:"flex h-60 items-center justify-center bg-white",children:(0,r.jsx)(c.A,{indicator:(0,r.jsx)(m.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[(null==O?void 0:null===(e=O.data)||void 0===e?void 0:e.receipts)&&O.data.receipts.length>0?(0,r.jsx)(I,{receipts:O.data.receipts,loading:U,onViewReceipt:e=>{A(e)},onPrintReceipt:q,onDelete:e=>{E(e),z(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with receiptIds:",e),W(e),P(!0)},isMobile:D}):(0,r.jsx)("div",{className:"flex h-60 flex-col items-center justify-center rounded-md border border-gray-200 bg-white text-gray-600",children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No receipts found matching your search criteria."}),(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>N(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,r.jsx)("p",{children:"No receipts found."})}),(null==O?void 0:null===(t=O.data)||void 0===t?void 0:t.receipts)&&O.data.receipts.length>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-gray-50 px-4 py-3 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(s-1)*f+1})," ","to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(s*f,O.data.total)})," ","of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:O.data.total})," ","receipts"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>$(Math.max(1,s-1)),disabled:1===s,className:"relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ".concat(1===s?"cursor-not-allowed text-gray-400":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(h.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,Math.ceil(O.data.total/f))},(e,t)=>{let a=t+1;return(0,r.jsx)("button",{onClick:()=>$(a),className:"relative inline-flex items-center border px-4 py-2 text-sm font-medium ".concat(s===a?"z-10 border-blue-500 bg-blue-50 text-blue-600":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"),children:a},a)}),(0,r.jsxs)("button",{onClick:()=>$(s+1),disabled:s>=Math.ceil(O.data.total/f),className:"relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ".concat(s>=Math.ceil(O.data.total/f)?"cursor-not-allowed text-gray-400":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(p.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex w-full items-center justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>$(Math.max(1,s-1)),disabled:1===s,className:"relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ".concat(1===s?"cursor-not-allowed bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"),children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",s," of"," ",Math.ceil(O.data.total/f)]}),(0,r.jsx)("button",{onClick:()=>$(s+1),disabled:s>=Math.ceil(O.data.total/f),className:"relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ".concat(s>=Math.ceil(O.data.total/f)?"cursor-not-allowed bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"),children:"Next"})]})]})]})]})}),(0,r.jsx)(_.A,{isOpen:R,onClose:()=>{z(!1),E(null)},onConfirm:J,title:"Delete Receipt",message:"Are you sure you want to delete this receipt? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:L,type:"danger"}),(0,r.jsx)(_.A,{isOpen:M,onClose:()=>{P(!1),W([])},onConfirm:X,title:"Delete Multiple Receipts",message:"Are you sure you want to delete ".concat(B.length," receipts? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:K,type:"danger"}),v&&(0,r.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black opacity-50 transition-opacity duration-300",onClick:G}),(0,r.jsxs)("div",{className:"absolute bottom-0 right-0 top-0 flex translate-x-0 transform flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out",style:{width:D?"100vw":"500px"},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 bg-gray-50 px-4 py-3",children:[(0,r.jsx)("h2",{className:"truncate text-lg font-medium text-gray-800",children:"Receipt Preview"}),(0,r.jsx)(n.Ay,{type:"text",icon:(0,r.jsx)(g.A,{style:{color:"#333"}}),onClick:G,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto bg-white p-4",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(y.A,{className:"mr-2"}),"Receipt Details"]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"View and print your receipt"})]}),(0,r.jsx)("div",{className:"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-4",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(d.A,{src:v,alt:"Receipt",className:"max-w-full",preview:!1})})})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 bg-gray-50 px-4 py-3",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:G,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(y.A,{}),onClick:()=>q(v),children:"Print"})]})})]})]})]})}s(4039)},12467:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(95155);s(12115);var a=s(46102),l=s(43316),i=s(75218);let n=e=>{let{isOpen:t,onClose:s,onConfirm:n,title:c,message:d,confirmText:o="Confirm",cancelText:x="Cancel",isLoading:m=!1,type:h="danger"}=e;return(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.A,{style:{color:"danger"===h?"#ff4d4f":"warning"===h?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:c})]}),open:t,onCancel:s,footer:[(0,r.jsx)(l.Ay,{onClick:s,disabled:m,children:x},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===h?"primary":"default",danger:"danger"===h,onClick:n,loading:m,children:o},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:d})})}},60102:(e,t,s)=>{"use strict";s.d(t,{A0:()=>i,Hj:()=>c,jB:()=>l,nA:()=>n});var r=s(95155);s(12115);var a=s(21567);let l=e=>{let{children:t,columns:s,className:l,minWidth:i="800px"}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0",n?"grid":"block"),style:n?{gridTemplateColumns:s,minWidth:i,width:"max-content"}:{},children:t})})},i=e=>{let{children:t,className:s,sticky:l}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:i?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:i?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],s),children:t})},n=e=>{let{children:t,className:s,sticky:l}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:i?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:i?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],s),children:t})},c=e=>{let{children:t,className:s,selected:l=!1,onClick:i}=e;return(0,r.jsx)("div",{className:(0,a.cn)("contents",l&&"bg-blue-50",i&&"cursor-pointer hover:bg-gray-50",s),onClick:i,children:t})}},91256:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(12115);let a=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(43463),a=s(69795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},75912:(e,t,s)=>{"use strict";s.d(t,{r:()=>a});var r=s(55037);let a=(e,t)=>{"success"===e?r.oR.success(t):"error"===e?r.oR.error(t):"warning"===e&&(0,r.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)},4039:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[5006,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,6102,2910,766,3252,8017,821,8441,1517,7358],()=>t(4180)),_N_E=e.O()}]);