'use client';

import React from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/reduxRTK/store/store';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { userApi } from '@/reduxRTK/services/authApi';
import { setUser } from '@/reduxRTK/services/authSlice';

interface ClientProviderProps {
  children: React.ReactNode;
}

const ClientProvider: React.FC<ClientProviderProps> = ({ children }) => {
  const [isReady, setIsReady] = React.useState(false);

  React.useEffect(() => {
    console.log("ClientProvider mounted");
    setIsReady(true);

    // Set up global refresh function for user data
    (window as any).__FORCE_REFRESH_USER_DATA = async () => {
      console.log('🔄 Global force refresh user data triggered');

      try {
        // Get current auth state
        const state = store.getState();
        const { user, accessToken } = state.auth;

        if (!user || !accessToken) {
          console.log('⚠️ No user or access token for refresh');
          return;
        }

        // Clear cache and fetch fresh data
        store.dispatch(userApi.util.invalidateTags(['User']));
        store.dispatch(userApi.util.resetApiState());

        // Wait a moment for cache clearing
        await new Promise(resolve => setTimeout(resolve, 500));

        // Fetch fresh user data
        const result = await (store.dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, {
          forceRefetch: true
        }));

        if ('data' in result && result.data?.success && result.data.data) {
          const freshUser = result.data.data;
          console.log('✅ Global refresh: Fresh user data fetched:', {
            id: freshUser.id,
            paymentStatus: freshUser.paymentStatus
          });

          // Update Redux state
          store.dispatch(setUser({ user: freshUser, accessToken }));

          console.log('✅ Global refresh: Redux state updated');
        } else {
          console.log('❌ Global refresh: Failed to fetch fresh user data');
        }
      } catch (error) {
        console.error('❌ Global refresh error:', error);
      }
    };

    console.log('✅ Global refresh function set up');
  }, []);

  return (
    <Provider store={store}>
      <PersistGate
        loading={
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80">
            <Spin size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
          </div>
        }
        persistor={persistor}
        onBeforeLift={() => {
          console.log("PersistGate - Before lift");
        }}
      >
        <AntdRegistry>
          {isReady ? children : <LoadingSpinner fullScreen />}
        </AntdRegistry>
      </PersistGate>
    </Provider>
  );
};

export default ClientProvider;
