"use client";

import React, { useEffect, useMemo } from "react";
import {
  Form,
  Input,
  Button,
  Space,
  notification,
  ColorPicker,
  Switch,
} from "antd";
import SlidingPanel from "@/components/ui/SlidingPanel";
import {
  TagOutlined,
  CloseOutlined,
  SaveOutlined,
  LoadingOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { useCreateExpenseCategoryMutation, useUpdateExpenseCategoryMutation, type ExpenseCategory } from "@/reduxRTK/services/expenseCategoryApi";
import { showMessage } from "@/utils/showMessage";

const { TextArea } = Input;

interface ExpenseCategoryFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  category?: ExpenseCategory | null; // For editing
}

const ExpenseCategoryFormPanel: React.FC<ExpenseCategoryFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  category,
}) => {
  const [form] = Form.useForm();
  const isEditing = !!category;

  // API hooks
  const [createCategory, { isLoading: isCreating }] = useCreateExpenseCategoryMutation();
  const [updateCategory, { isLoading: isUpdating }] = useUpdateExpenseCategoryMutation();

  const isSubmitting = isCreating || isUpdating;

  // Default colors for categories
  const defaultColors = useMemo(() => [
    "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6",
    "#EC4899", "#6366F1", "#14B8A6", "#F97316", "#6B7280"
  ], []);

  // Reset form when panel opens/closes or category changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && category) {
        form.setFieldsValue({
          name: category.name,
          description: category.description,
          color: category.color,
          isDefault: category.isDefault,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          color: defaultColors[Math.floor(Math.random() * defaultColors.length)],
          isDefault: false,
        });
      }
    }
  }, [isOpen, isEditing, category, form, defaultColors]);

  const handleSubmit = async (values: any) => {
    try {
      const categoryData = {
        name: values.name,
        description: values.description,
        color: typeof values.color === 'string' ? values.color : values.color?.toHexString?.() || '#6B7280',
        isDefault: values.isDefault || false,
      };

      let result;
      if (isEditing && category) {
        result = await updateCategory({
          categoryId: category.id,
          categoryData,
        }).unwrap();
      } else {
        result = await createCategory(categoryData).unwrap();
      }

      if (result.success) {
        showMessage.success(
          isEditing ? "Category updated successfully!" : "Category created successfully!"
        );
        form.resetFields();
        onSuccess?.();
        onClose();
      } else {
        showMessage.error(result.message || "Failed to save category");
      }
    } catch (error: any) {
      console.error("Error saving category:", error);
      showMessage.error(
        error?.data?.message ||
        error?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} category`
      );
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // Panel title
  const panelTitle = isEditing ? "Edit Expense Category" : "Add New Expense Category";

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={handleClose}
        disabled={isSubmitting}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        onClick={() => form.submit()}
        loading={isSubmitting}
        icon={isSubmitting ? <LoadingOutlined /> : <SaveOutlined />}
      >
        {isEditing ? "Update" : "Create"} Category
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={handleClose}
      title={panelTitle}
      width="500px"
      footer={panelFooter}
    >
      {/* Form heading with icon */}
      <div className="mb-6 border-b border-gray-200 pb-4 pt-10">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          {isEditing ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Editing Category: {category?.name}
            </>
          ) : (
            <>
              <TagOutlined className="mr-2" />
              Add New Expense Category
            </>
          )}
        </h2>
        <p className="text-gray-600 mt-1">
          {isEditing ? "Update category information" : "Create a new expense category for better organization"}
        </p>
      </div>

      {/* Required fields indicator */}
      <div className="mb-4 text-sm text-gray-600">
        <span className="text-red-500 mr-1">*</span> indicates required fields
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="expense-category-form"
        requiredMark={true}
      >
        {/* Basic Information */}
        <div className="mb-6">
          <h3 className="text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2">
            Category Information
          </h3>

          <Form.Item
            name="name"
            label={<span className="flex items-center"><TagOutlined className="mr-1" /> Category Name <span className="text-red-500 ml-1">*</span></span>}
            rules={[
              { required: true, message: "Please enter category name" },
              { max: 100, message: "Category name must be at most 100 characters" }
            ]}
            tooltip="A descriptive name for this expense category"
          >
            <Input
              placeholder="e.g., Office Supplies, Utilities, Marketing"
              maxLength={100}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={<span className="flex items-center"><FileTextOutlined className="mr-1" /> Description</span>}
            tooltip="Optional description to help identify this category"
          >
            <TextArea
              placeholder="Brief description of what expenses belong to this category..."
              rows={3}
              maxLength={500}
            />
          </Form.Item>
        </div>

        {/* Appearance */}
        <div className="mb-6">
          <h3 className="text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2">
            Appearance
          </h3>

          <Form.Item
            name="color"
            label="Category Color"
            tooltip="Choose a color to help identify this category visually"
          >
            <ColorPicker
              showText
              size="large"
              presets={[
                {
                  label: 'Recommended',
                  colors: defaultColors,
                },
              ]}
            />
          </Form.Item>

          {/* Quick Color Selection */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">Quick Colors:</div>
            <div className="flex flex-wrap gap-2">
              {defaultColors.map((color, index) => (
                <button
                  key={index}
                  type="button"
                  className="w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-500 transition-colors"
                  style={{ backgroundColor: color }}
                  onClick={() => form.setFieldValue('color', color)}
                  title={`Set color to ${color}`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Advanced Options */}
        {isEditing && category?.isDefault && (
          <div className="mb-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="text-yellow-600 font-medium">
                  System Default Category
                </div>
              </div>
              <div className="text-yellow-700 text-sm mt-1">
                This is a system default category. Some properties cannot be modified.
              </div>
            </div>
          </div>
        )}
      </Form>
    </SlidingPanel>
  );
};

export default ExpenseCategoryFormPanel;
