"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExpenseStats = exports.deleteExpenseById = exports.updateExpenseById = exports.getExpenseById = exports.getAllExpenses = exports.createExpense = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
const schema_2 = require("../validation/schema");
/**
 * ✅ Create a new expense
 */
const createExpense = async (requester, expenseData) => {
    // Validate expense data
    schema_2.expenseSchema.parse(expenseData);
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "expenses");
    const createdBy = requester.id;
    // Insert new expense record
    const [newExpense] = await db_1.postgresDb
        .insert(schema_1.expenses)
        .values({
        ...expenseData,
        amount: expenseData.amount.toString(),
        createdBy,
    })
        .returning();
    return newExpense;
};
exports.createExpense = createExpense;
/**
 * ✅ Get all expenses with pagination and filtering
 */
const getAllExpenses = async (requester, page = 1, limit = 10, search = '', categoryId, startDate, endDate) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "expenses");
    // Build team member IDs for admin users
    let teamMemberIds = [];
    if (requester.role === "admin") {
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        teamMemberIds = teamMembersResult.map((m) => m.memberId);
    }
    // Include the requester's ID in the team IDs
    const teamIds = requester.role === "admin"
        ? [requester.id, ...teamMemberIds]
        : [requester.id];
    // Build where conditions
    const whereConditions = [(0, drizzle_orm_1.inArray)(schema_1.expenses.createdBy, teamIds)];
    if (search && search.trim()) {
        const searchTerm = `%${search.trim()}%`;
        whereConditions.push((0, drizzle_orm_1.sql) `(
        ${schema_1.expenses.title} ILIKE ${searchTerm} OR
        ${schema_1.expenses.description} ILIKE ${searchTerm} OR
        ${schema_1.expenses.vendor} ILIKE ${searchTerm}
      )`);
    }
    if (categoryId) {
        whereConditions.push((0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, categoryId));
    }
    if (startDate) {
        whereConditions.push((0, drizzle_orm_1.gte)(schema_1.expenses.expenseDate, startDate));
    }
    if (endDate) {
        whereConditions.push((0, drizzle_orm_1.lte)(schema_1.expenses.expenseDate, endDate));
    }
    // Get total count
    const totalCountResult = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.expenses)
        .where((0, drizzle_orm_1.and)(...whereConditions));
    const totalCount = totalCountResult[0]?.count || 0;
    // Get expenses with category information
    const expensesResult = await db_1.postgresDb
        .select({
        id: schema_1.expenses.id,
        title: schema_1.expenses.title,
        description: schema_1.expenses.description,
        amount: schema_1.expenses.amount,
        categoryId: schema_1.expenses.categoryId,
        categoryName: schema_1.expenseCategories.name,
        categoryColor: schema_1.expenseCategories.color,
        expenseDate: schema_1.expenses.expenseDate,
        paymentMethod: schema_1.expenses.paymentMethod,
        receiptUrl: schema_1.expenses.receiptUrl,
        vendor: schema_1.expenses.vendor,
        isRecurring: schema_1.expenses.isRecurring,
        recurringFrequency: schema_1.expenses.recurringFrequency,
        tags: schema_1.expenses.tags,
        storeId: schema_1.expenses.storeId,
        createdBy: schema_1.expenses.createdBy,
        createdAt: schema_1.expenses.createdAt,
        updatedAt: schema_1.expenses.updatedAt,
    })
        .from(schema_1.expenses)
        .leftJoin(schema_1.expenseCategories, (0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, schema_1.expenseCategories.id))
        .where((0, drizzle_orm_1.and)(...whereConditions))
        .orderBy((0, drizzle_orm_1.desc)(schema_1.expenses.expenseDate))
        .limit(limit)
        .offset((page - 1) * limit);
    return {
        expenses: expensesResult.map(expense => ({
            ...expense,
            category: expense.categoryName ? {
                id: expense.categoryId,
                name: expense.categoryName,
                color: expense.categoryColor || '#6B7280'
            } : undefined
        })),
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
    };
};
exports.getAllExpenses = getAllExpenses;
/**
 * ✅ Get expense by ID
 */
const getExpenseById = async (requester, expenseId) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "expenses", expenseId);
    const expenseResult = await db_1.postgresDb
        .select({
        id: schema_1.expenses.id,
        title: schema_1.expenses.title,
        description: schema_1.expenses.description,
        amount: schema_1.expenses.amount,
        categoryId: schema_1.expenses.categoryId,
        categoryName: schema_1.expenseCategories.name,
        categoryColor: schema_1.expenseCategories.color,
        expenseDate: schema_1.expenses.expenseDate,
        paymentMethod: schema_1.expenses.paymentMethod,
        receiptUrl: schema_1.expenses.receiptUrl,
        vendor: schema_1.expenses.vendor,
        isRecurring: schema_1.expenses.isRecurring,
        recurringFrequency: schema_1.expenses.recurringFrequency,
        tags: schema_1.expenses.tags,
        storeId: schema_1.expenses.storeId,
        createdBy: schema_1.expenses.createdBy,
        createdAt: schema_1.expenses.createdAt,
        updatedAt: schema_1.expenses.updatedAt,
    })
        .from(schema_1.expenses)
        .leftJoin(schema_1.expenseCategories, (0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, schema_1.expenseCategories.id))
        .where((0, drizzle_orm_1.eq)(schema_1.expenses.id, expenseId))
        .limit(1);
    if (expenseResult.length === 0) {
        throw new Error("Expense not found.");
    }
    return expenseResult[0];
};
exports.getExpenseById = getExpenseById;
/**
 * ✅ Update expense by ID
 */
const updateExpenseById = async (requester, expenseId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "expenses", expenseId);
    // Convert amount to string if provided
    const processedUpdateData = {
        ...updateData,
        ...(updateData.amount && { amount: updateData.amount.toString() }),
        updatedAt: new Date(),
    };
    const [updatedExpense] = await db_1.postgresDb
        .update(schema_1.expenses)
        .set({
        ...processedUpdateData,
        amount: typeof updateData.amount === 'number' ? updateData.amount.toString() : updateData.amount,
    })
        .where((0, drizzle_orm_1.eq)(schema_1.expenses.id, expenseId))
        .returning();
    if (!updatedExpense) {
        throw new Error("Expense not found or update failed.");
    }
    return updatedExpense;
};
exports.updateExpenseById = updateExpenseById;
/**
 * ✅ Delete expense(s) by ID(s)
 */
const deleteExpenseById = async (requester, expenseIds) => {
    // Validate each expense before deletion
    for (const expenseId of expenseIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "expenses", expenseId);
    }
    const deletedExpenses = await db_1.postgresDb
        .delete(schema_1.expenses)
        .where((0, drizzle_orm_1.inArray)(schema_1.expenses.id, expenseIds))
        .returning({ id: schema_1.expenses.id });
    if (deletedExpenses.length === 0) {
        throw new Error("No expenses found for deletion.");
    }
    return {
        deletedCount: deletedExpenses.length,
        deletedIds: deletedExpenses.map(e => e.id),
    };
};
exports.deleteExpenseById = deleteExpenseById;
/**
 * ✅ Get expense statistics for dashboard
 */
const getExpenseStats = async (requester, startDate, endDate) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "expenses");
    // Build team member IDs for admin users
    let teamMemberIds = [];
    if (requester.role === "admin") {
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        teamMemberIds = teamMembersResult.map((m) => m.memberId);
    }
    const teamIds = requester.role === "admin"
        ? [requester.id, ...teamMemberIds]
        : [requester.id];
    // Build where conditions
    const whereConditions = [(0, drizzle_orm_1.inArray)(schema_1.expenses.createdBy, teamIds)];
    if (startDate) {
        whereConditions.push((0, drizzle_orm_1.gte)(schema_1.expenses.expenseDate, startDate));
    }
    if (endDate) {
        whereConditions.push((0, drizzle_orm_1.lte)(schema_1.expenses.expenseDate, endDate));
    }
    // Get total expenses and amount
    const totalResult = await db_1.postgresDb
        .select({
        totalAmount: (0, drizzle_orm_1.sql) `COALESCE(SUM(CAST(${schema_1.expenses.amount} AS DECIMAL)), 0)`,
        totalCount: (0, drizzle_orm_1.count)(),
    })
        .from(schema_1.expenses)
        .where((0, drizzle_orm_1.and)(...whereConditions));
    // Get expenses by category
    const categoryResult = await db_1.postgresDb
        .select({
        categoryId: schema_1.expenses.categoryId,
        categoryName: schema_1.expenseCategories.name,
        categoryColor: schema_1.expenseCategories.color,
        totalAmount: (0, drizzle_orm_1.sql) `COALESCE(SUM(CAST(${schema_1.expenses.amount} AS DECIMAL)), 0)`,
        count: (0, drizzle_orm_1.count)(),
    })
        .from(schema_1.expenses)
        .leftJoin(schema_1.expenseCategories, (0, drizzle_orm_1.eq)(schema_1.expenses.categoryId, schema_1.expenseCategories.id))
        .where((0, drizzle_orm_1.and)(...whereConditions))
        .groupBy(schema_1.expenses.categoryId, schema_1.expenseCategories.name, schema_1.expenseCategories.color);
    return {
        totalAmount: parseFloat(totalResult[0]?.totalAmount || "0"),
        totalCount: totalResult[0]?.totalCount || 0,
        byCategory: categoryResult.map(cat => ({
            categoryId: cat.categoryId,
            categoryName: cat.categoryName || "Uncategorized",
            categoryColor: cat.categoryColor || "#6B7280",
            totalAmount: parseFloat(cat.totalAmount || "0"),
            count: cat.count,
        })),
    };
};
exports.getExpenseStats = getExpenseStats;
