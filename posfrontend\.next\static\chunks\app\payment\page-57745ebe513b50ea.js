(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8407],{62533:(e,a,s)=>{Promise.resolve().then(s.bind(s,89220))},89220:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>k});var t=s(95155),r=s(12115),l=s(71349),n=s(83414),i=s(97838),o=s(20148),c=s(9365),d=s(41657),u=s(43316),m=s(4951);s(75912);var h=s(76046),x=s(83391),y=s(78767),p=s(63065);s(7875);let g={monthly:{price:40,discount:0,label:"Monthly",description:"Basic subscription plan",period:1},quarterly:{price:108,discount:10,label:"Quarterly",description:"Save 10% with quarterly billing",period:3},annual:{price:360,discount:25,label:"Annual",description:"Save 25% with annual billing",period:12}},b=e=>{let{onSuccess:a}=e,[s]=n.A.useForm(),[p,{isLoading:b}]=(0,y.nm)(),[f,{isLoading:j}]=(0,y.Ex)(),[N,v]=(0,r.useState)(null),[w,k]=(0,r.useState)("monthly"),[P,A]=(0,r.useState)(null);(0,h.useRouter)(),(0,x.wA)(),(0,x.d4)(e=>e.auth.accessToken);let S=(0,x.d4)(e=>e.auth.user);(0,r.useEffect)(()=>{let e=g[w];s.setFieldsValue({amount:Math.round(e.price),email:(null==S?void 0:S.email)||""})},[w,s,S]);let D=async e=>{try{v(null);let a=g[w];console.log("\uD83D\uDD04 Initializing payment with subscription period:",{selectedPlan:w,planDetails:a,subscriptionPeriod:a.period,amount:e.amount});let s=await p({amount:e.amount,email:e.email,callbackUrl:"".concat(window.location.origin,"/payment/callback"),subscriptionPeriod:a.period}).unwrap();s.success&&s.data?(A({authorizationUrl:s.data.authorizationUrl,reference:s.data.reference,email:e.email,amount:e.amount,key:"pk_live_b6fdd41efeeabee8a2aa28c1fc89445f9d8f2eca"}),console.log("Payment initialized successfully:",{authorizationUrl:s.data.authorizationUrl,reference:s.data.reference,amount:e.amount})):v(s.message||"Failed to initialize payment")}catch(e){var a;v((null===(a=e.data)||void 0===a?void 0:a.message)||e.message||"An error occurred during payment initialization.")}},C=e=>{let a=g[e],s=w===e;return(0,t.jsxs)(l.A,{className:"bg-white border-2 transition-all duration-200 h-full ".concat(s?"border-blue-500 shadow-lg":"border-gray-200"),styles:{body:{padding:"16px",height:"100%",display:"flex",flexDirection:"column"}},onClick:()=>k(e),children:["annual"===e&&(0,t.jsx)(i.A.Ribbon,{text:"Best Value",color:"blue",className:"absolute top-0 right-0",children:(0,t.jsx)("div",{className:"h-6"})}),(0,t.jsxs)("div",{className:"flex flex-col h-full flex-grow",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:a.label}),s&&(0,t.jsx)(m.A,{className:"text-blue-500 text-xl"})]}),(0,t.jsx)("div",{className:"text-gray-600 text-sm mb-3",children:a.description}),(0,t.jsxs)("div",{className:"mt-auto",children:[(0,t.jsxs)("div",{className:"flex items-baseline",children:[(0,t.jsxs)("span",{className:"text-2xl font-bold text-gray-800",children:["GH₵",Math.round(a.price)]}),a.discount>0&&(0,t.jsxs)("span",{className:"ml-2 text-sm text-green-500",children:["Save ",a.discount,"%"]})]}),(0,t.jsx)("div",{className:"text-gray-500 text-xs mt-1",children:"monthly"===e?"per month":"quarterly"===e?"every 3 months":"per year"})]})]})]})};return(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6 text-gray-800 pl-2",children:"Choose a Subscription Plan"}),N&&(0,t.jsx)(o.A,{message:"Payment Error",description:N,type:"error",showIcon:!0,className:"mb-4",closable:!0,onClose:()=>v(null)}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:Object.keys(g).map(e=>(0,t.jsx)("div",{className:"col-span-1 h-full",children:(0,t.jsx)("div",{className:"h-full",children:C(e)})},e))}),(0,t.jsx)(c.A,{className:"border-gray-200 my-6"}),(0,t.jsx)("h3",{className:"text-xl font-bold mb-4 text-gray-800 pl-2",children:"Payment Details"}),(0,t.jsx)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg mx-2",children:(0,t.jsxs)("p",{className:"text-sm text-green-700",children:[(0,t.jsx)("strong",{children:"\uD83D\uDD12 Secure Payment:"})," All payments are processed securely through Paystack. You can pay with cards, or mobile money."]})}),(0,t.jsxs)(n.A,{form:s,layout:"vertical",onFinish:D,initialValues:{amount:g.monthly.price,email:(null==S?void 0:S.email)||"",plan:"monthly"},className:"px-2",children:[(0,t.jsx)(n.A.Item,{name:"plan",hidden:!0,children:(0,t.jsx)(d.A,{type:"hidden",value:w})}),(0,t.jsx)(n.A.Item,{label:(0,t.jsx)("span",{className:"text-gray-700",children:"Amount (GH₵)"}),name:"amount",children:(0,t.jsx)(d.A,{className:"bg-white border-gray-300 text-gray-800",disabled:!0,prefix:(0,t.jsx)("span",{className:"text-gray-600",children:"GH₵"}),suffix:(0,t.jsx)("span",{className:"text-gray-600",children:"GHS"}),style:{color:"#333333"}})}),(0,t.jsx)(n.A.Item,{label:(0,t.jsx)("span",{className:"text-gray-700",children:"Email Address"}),name:"email",rules:[{required:!0,message:"Please enter your email address"},{type:"email",message:"Please enter a valid email address"}],children:(0,t.jsx)(d.A,{placeholder:"<EMAIL>",className:"bg-white border-gray-300 text-gray-800",style:{color:"#333333"}})}),(0,t.jsx)(n.A.Item,{className:"mt-6",children:P?(0,t.jsx)(u.Ay,{type:"primary",onClick:()=>{if(!P||!P.authorizationUrl){v("Payment not initialized. Please try again.");return}console.log("Redirecting to Paystack checkout:",P.authorizationUrl),window.location.href=P.authorizationUrl},className:"w-full bg-green-500 hover:bg-green-600 h-12 text-base font-medium",loading:j,children:j?"Processing Payment...":"Pay with Paystack"}):(0,t.jsx)(u.Ay,{type:"primary",htmlType:"submit",className:"w-full bg-blue-500 hover:bg-blue-600 h-12 text-base font-medium",loading:b,children:b?"Initializing...":"Initialize Payment"})})]}),j&&(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsx)(o.A,{message:"Verifying Payment",description:"Please wait while we verify your payment...",type:"info",showIcon:!0})})]})};var f=s(22882),j=s(67259),N=s(5565);function v(){let e=(0,x.d4)(e=>e.auth.user),a=(0,h.useRouter)();return(0,r.useEffect)(()=>{e&&"paid"===e.paymentStatus&&a.replace("/dashboard")},[e,a]),null}function w(){return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center border border-gray-200",children:[(0,t.jsx)(N.default,{src:"/images/access.jpg",alt:"Admin Payment Required",className:"mx-auto mb-6 w-32 h-32 object-contain",width:128,height:128}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Access Restricted"}),(0,t.jsxs)("p",{className:"text-gray-600 mb-6",children:["Your admin needs to make a payment for you to access the dashboard.",(0,t.jsx)("br",{}),"Please contact your admin to renew the subscription."]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 text-sm px-4 py-2 rounded-full font-semibold",children:"Waiting for admin payment"})})]})})}function k(){let e=(0,x.d4)(e=>e.auth.user);return(0,h.useRouter)(),(0,j.X)(),!function(e){let a=(0,x.wA)();(0,r.useEffect)(()=>{if(!e||"cashier"!==e.role||"paid"===e.paymentStatus)return;let s=setInterval(()=>{a(p.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}))},5e3);return()=>clearInterval(s)},[e,a])}(e),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v,{}),e?"cashier"===e.role&&"paid"!==e.paymentStatus?(0,t.jsx)(w,{}):"admin"===e.role&&"paid"!==e.paymentStatus?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Subscription Payment"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Choose your subscription plan and complete the payment"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(b,{})}),(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsx)(f.A,{showPayButton:!1}),(0,t.jsxs)(l.A,{title:(0,t.jsx)("span",{className:"text-gray-800 pl-2",children:"Payment Instructions"}),className:"mt-6 shadow-md bg-white border border-gray-200",styles:{header:{backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"},body:{backgroundColor:"#ffffff"}},children:[(0,t.jsxs)("ol",{className:"list-decimal pl-5 space-y-2 text-gray-700",children:[(0,t.jsxs)("li",{children:["Select your preferred subscription plan (Monthly: ",(0,t.jsx)("strong",{className:"text-gray-900",children:"₵40"}),", Quarterly: ",(0,t.jsx)("strong",{className:"text-gray-900",children:"₵108"}),", or Annual: ",(0,t.jsx)("strong",{className:"text-gray-900",children:"₵360"}),")"]}),(0,t.jsx)("li",{children:"Enter your email address for payment confirmation"}),(0,t.jsxs)("li",{children:["Click ",(0,t.jsx)("strong",{className:"text-gray-900",children:'"Initialize Payment"'})," to set up your Paystack payment"]}),(0,t.jsxs)("li",{children:["Click ",(0,t.jsx)("strong",{className:"text-gray-900",children:'"Pay Now"'})," to complete payment securely via Paystack"]}),(0,t.jsx)("li",{children:"Your subscription will be activated automatically after successful payment"})]}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-blue-800 mb-2",children:"\uD83D\uDCB3 Accepted Payment Methods (via Paystack):"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Cards:"})," Visa, Mastercard, Verve (local & international)"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Mobile Money:"})," MTN, Vodafone, AirtelTigo"]})]})]}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-green-800 mb-2",children:"\uD83D\uDCA1 Payment Status Not Updated?"}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:["If your payment was successful but your status still shows as pending, click the ",(0,t.jsx)("strong",{children:"refresh button (\uD83D\uDD04)"})," in the Subscription Status card above to update your status."]})]}),(0,t.jsxs)("p",{className:"mt-4 text-sm text-gray-600",children:[(0,t.jsx)("strong",{children:"\uD83D\uDD12 Secure Payment:"})," All payments are processed securely through Paystack, Ghana's leading payment gateway. Your subscription will be activated immediately after verification. For any issues, please contact support."]})]})]})]})]})}):null:null]})}},67259:(e,a,s)=>{"use strict";s.d(a,{X:()=>i});var t=s(12115),r=s(83391),l=s(7875),n=s(63065);function i(){let e=(0,r.wA)(),a=(0,r.d4)(e=>e.auth.user),s=(0,r.d4)(e=>e.auth.accessToken),i=(0,t.useRef)(a),o=(0,t.useRef)(s);(0,t.useEffect)(()=>{i.current=a,o.current=s},[a,s]),(0,t.useEffect)(()=>{if(!a||!a.id)return;let s="wss://nexapoapi.up.railway.app",t=new WebSocket(s);return t.onopen=()=>{t.send(JSON.stringify({type:"auth",userId:a.id})),console.log("[WebSocket] Connected and authenticated as user",a.id,"URL:",s),e(n.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0})).then(a=>{var s;(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.success)&&a.data.data&&o.current&&e((0,l.gV)({user:a.data.data,accessToken:o.current}))})},t.onmessage=a=>{let s=JSON.parse(a.data);if("paymentStatus"===s.type){var t;if((null===(t=i.current)||void 0===t?void 0:t.role)==="superadmin")return;if(console.log("[WebSocket] Received paymentStatus update:",s.status),o.current&&i.current&&"number"==typeof i.current.id){let{id:a,name:t,email:r,phone:n,role:c,createdAt:d,lastPaymentDate:u,nextPaymentDue:m,createdBy:h}=i.current;e((0,l.gV)({user:{id:a,name:t,email:r,phone:n,role:c,createdAt:d,lastPaymentDate:u,nextPaymentDue:m,createdBy:h,paymentStatus:s.status},accessToken:o.current}))}}},t.onerror=e=>{console.error("[WebSocket] Error:",e)},t.onclose=()=>{console.log("[WebSocket] Connection closed")},()=>t.close()},[null==a?void 0:a.id,e])}}},e=>{var a=a=>e(e.s=a);e.O(0,[6754,1961,2261,4831,3316,9135,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,5565,8408,7114,7359,821,2671,8441,1517,7358],()=>a(62533)),_N_E=e.O()}]);