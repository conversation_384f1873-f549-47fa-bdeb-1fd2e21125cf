(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{38570:(e,t,a)=>{Promise.resolve().then(a.bind(a,5812))},90954:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(85407),s=a(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var c=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(c.A,(0,r.A)({},e,{ref:t,icon:n}))})},7162:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(85407),s=a(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var c=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(c.A,(0,r.A)({},e,{ref:t,icon:n}))})},15955:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(12115).createContext)(void 0)},11870:(e,t,a)=>{"use strict";a.d(t,{L3:()=>d,i4:()=>u,xV:()=>m});var r=a(67548),s=a(1086),n=a(56204);let c=e=>{let{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},l=(e,t)=>{let{prefixCls:a,componentCls:r,gridColumns:s}=e,n={};for(let e=s;e>=0;e--)0===e?(n["".concat(r).concat(t,"-").concat(e)]={display:"none"},n["".concat(r,"-push-").concat(e)]={insetInlineStart:"auto"},n["".concat(r,"-pull-").concat(e)]={insetInlineEnd:"auto"},n["".concat(r).concat(t,"-push-").concat(e)]={insetInlineStart:"auto"},n["".concat(r).concat(t,"-pull-").concat(e)]={insetInlineEnd:"auto"},n["".concat(r).concat(t,"-offset-").concat(e)]={marginInlineStart:0},n["".concat(r).concat(t,"-order-").concat(e)]={order:0}):(n["".concat(r).concat(t,"-").concat(e)]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:"0 0 ".concat(e/s*100,"%"),maxWidth:"".concat(e/s*100,"%")}],n["".concat(r).concat(t,"-push-").concat(e)]={insetInlineStart:"".concat(e/s*100,"%")},n["".concat(r).concat(t,"-pull-").concat(e)]={insetInlineEnd:"".concat(e/s*100,"%")},n["".concat(r).concat(t,"-offset-").concat(e)]={marginInlineStart:"".concat(e/s*100,"%")},n["".concat(r).concat(t,"-order-").concat(e)]={order:e});return n["".concat(r).concat(t,"-flex")]={flex:"var(--".concat(a).concat(t,"-flex)")},n},o=(e,t)=>l(e,t),i=(e,t,a)=>({["@media (min-width: ".concat((0,r.zA)(t),")")]:Object.assign({},o(e,a))}),d=(0,s.OF)("Grid",e=>{let{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},()=>({})),u=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),m=(0,s.OF)("Grid",e=>{let t=(0,n.oX)(e,{gridColumns:24}),a=u(t);return delete a.xs,[c(t),o(t,""),o(t,"-xs"),Object.keys(a).map(e=>i(t,a[e],"-".concat(e))).reduce((e,t)=>Object.assign(Object.assign({},e),t),{})]},()=>({}))},76046:(e,t,a)=>{"use strict";var r=a(66658);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}}),a.o(r,"useServerInsertedHTML")&&a.d(t,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})},5812:(e,t,a)=>{"use strict";a.d(t,{default:()=>w});var r=a(95155),s=a(12115),n=a(5565),c=a(83414),l=a(41657),o=a(43316),i=a(7162),d=a(90954),u=a(16419),m=a(83391),g=a(7875),h=a(63065);let f=()=>{let e=(0,m.wA)(),[t,a]=(0,s.useState)({loading:!1,error:"",data:null}),[r]=(0,h.h8)();return{login:async(t,s)=>{var n,c,l;a(e=>({...e,loading:!0,error:""}));try{let l=await r({email:t,password:s}).unwrap();if(l.success&&(null===(n=l.data)||void 0===n?void 0:n.user)&&(null===(c=l.data)||void 0===c?void 0:c.accessToken)){let{user:t,accessToken:r}=l.data;console.log("useLoginUser - User data from login response:",t),console.log("useLoginUser - User data (stringified):",JSON.stringify(t,null,2)),console.log("useLoginUser - Critical fields:",{phone:t.phone,phoneType:typeof t.phone,createdAt:t.createdAt,createdAtType:typeof t.createdAt,lastPaymentDate:t.lastPaymentDate,nextPaymentDue:t.nextPaymentDue});let s={...t,phone:t.phone||"",createdAt:t.createdAt||"",lastPaymentDate:t.lastPaymentDate||null,nextPaymentDue:t.nextPaymentDue||null,createdBy:t.createdBy||null};return console.log("useLoginUser - Updated user data:",s),e((0,g.gV)({user:s,accessToken:r})),a({loading:!1,error:"",data:{user:s,accessToken:r}}),{success:!0,message:l.message,data:{user:s,accessToken:r}}}{let e=l.message||"Login failed";return a({loading:!1,error:e,data:null}),{success:!1,message:e,data:null}}}catch(t){let e=(null==t?void 0:null===(l=t.data)||void 0===l?void 0:l.message)||t.message||"Something went wrong";return a({loading:!1,error:e,data:null}),{success:!1,message:e,data:null}}},...t}};var x=a(75912),p=a(76046);function y(){let[e,t]=(0,s.useState)(!1),{login:a}=f(),m=(0,p.useRouter)(),g=async e=>{t(!0);let r=await a(e.email,e.password);(null==r?void 0:r.success)?(t(!1),(0,x.r)("success",(null==r?void 0:r.message)||"Login successful"),m.replace("/dashboard")):(t(!1),(null==r?void 0:r.message)==="getaddrinfo ENOTFOUND ep-icy-resonance-a5p24f0y-pooler.us-east-2.aws.neon.tech"?(0,x.r)("error","Error in network connection. Check your connection"):(0,x.r)("error",null==r?void 0:r.message))};return(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)("div",{className:"mb-8 flex justify-center",children:(0,r.jsx)(n.default,{src:"/images/logo.png",alt:"Logo",width:150,height:64,priority:!0})}),(0,r.jsx)("h2",{className:"text-gray-800 text-2xl font-bold mb-6 text-center",children:"Sign In"}),(0,r.jsxs)(c.A,{name:"signin",onFinish:g,layout:"vertical",className:"text-gray-800",validateMessages:{required:"${label} is required"},children:[(0,r.jsx)(c.A.Item,{name:"email",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Email Address"}),rules:[{required:!0,message:"Please enter your email"}],children:(0,r.jsx)(l.A,{size:"large",prefix:(0,r.jsx)(i.A,{className:"text-gray-500"}),placeholder:"Email Address",className:"rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"})}),(0,r.jsx)(c.A.Item,{name:"password",label:(0,r.jsx)("span",{className:"text-gray-700",children:"Password"}),rules:[{required:!0,message:"Please enter your password"}],children:(0,r.jsx)(l.A.Password,{size:"large",prefix:(0,r.jsx)(d.A,{className:"text-gray-500"}),placeholder:"Password",className:"rounded-md bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400 placeholder-gray-400"})}),(0,r.jsx)(c.A.Item,{className:"mb-4",children:(0,r.jsx)(o.Ay,{type:"primary",htmlType:"submit",size:"large",className:"w-full rounded-md bg-blue-500 text-white hover:bg-blue-600 border border-blue-500",children:e?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(u.A,{style:{color:"white",marginRight:"8px"},spin:!0}),(0,r.jsx)("span",{children:"Logging in..."})]}):"Log in"})})]})]})}a(44647);let v=e=>{let{children:t,redirectTo:a="/dashboard"}=e,{user:n,accessToken:c}=(0,m.d4)(e=>e.auth),l=(0,p.useRouter)();return(0,s.useEffect)(()=>{if(n&&c){let e=sessionStorage.getItem("redirectUrl");e?(sessionStorage.removeItem("redirectUrl"),l.push(e)):l.push(a)}},[n,c,l,a]),(0,r.jsx)(r.Fragment,{children:t})},w=()=>(0,r.jsx)(v,{children:(0,r.jsx)("div",{className:"w-screen h-screen flex items-center justify-center bg-white px-4",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row w-full max-w-5xl h-auto md:h-[90vh] rounded-[20px] shadow-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center bg-white p-6 md:p-10 md:border-r border-gray-200",children:(0,r.jsx)(y,{})}),(0,r.jsx)("div",{className:"hidden md:flex w-full md:w-1/2 items-center justify-center bg-[#f7f9fc] p-10",children:(0,r.jsx)(n.default,{src:"/images/signin.webp",alt:"Login Illustration",width:800,height:800,className:"object-contain w-full h-full"})})]})})})},75912:(e,t,a)=>{"use strict";a.d(t,{r:()=>s});var r=a(55037);let s=(e,t)=>{"success"===e?r.oR.success(t):"error"===e?r.oR.error(t):"warning"===e&&(0,r.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};s.success=e=>s("success",e),s.error=e=>s("error",e),s.warning=e=>s("warning",e)},44647:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[6590,6754,1961,2261,4831,3316,9135,1388,5037,2204,2336,1657,3414,5565,821,8441,1517,7358],()=>t(38570)),_N_E=e.O()}]);