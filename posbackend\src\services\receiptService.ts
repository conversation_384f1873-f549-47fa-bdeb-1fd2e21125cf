import { and, eq, count, desc, inArray, or } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { receipts, users, sales, stores } from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";

/**
 * ✅ **Get All Receipts (Paginated)**
 */
export const getAllReceipts = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  await authorizeAction(requester, "getAll", "receipts");

  const offset = (page - 1) * limit;
  const isSuperadmin = requester.role === "superadmin";
  const isCashier = requester.role === "cashier";

  // Different filter logic based on role
  let receiptsFilter;

  if (isSuperadmin) {
    // Superadmin sees all receipts
    receiptsFilter = undefined;
  } else if (requester.role === "admin") {
    // Admin can only see their own receipts and receipts from users they created
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

    if (teamMemberIds.length > 0) {
      // Admin can see their own receipts and receipts from users they created
      receiptsFilter = or(
        eq(receipts.createdBy, requester.id),
        inArray(receipts.createdBy, teamMemberIds)
      );
    } else {
      // If no team members found, just show their own receipts
      receiptsFilter = eq(receipts.createdBy, requester.id);
    }
  } else if (isCashier) {
    // Cashier can only see their own receipts and receipts from their admin and siblings

    // Find the cashier's creator (admin)
    const creatorResult = await postgresDb
      .select({ creatorId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    const creatorId = creatorResult[0]?.creatorId || undefined;

    if (creatorId) {
      // Get all users created by the same admin (siblings)
      const siblingsResult = await postgresDb
        .select({ siblingId: users.id })
        .from(users)
        .where(eq(users.createdBy, creatorId));

      const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

      // Build the filter to include own receipts, creator's receipts, and siblings' receipts
      receiptsFilter = or(
        eq(receipts.createdBy, requester.id),
        eq(receipts.createdBy, creatorId),
        inArray(receipts.createdBy, siblingIds)
      );
    } else {
      // If no creator found, just show their own receipts
      receiptsFilter = eq(receipts.createdBy, requester.id);
    }
  } else {
    // Default case - just show own receipts
    receiptsFilter = eq(receipts.createdBy, requester.id);
  }

  const [receiptsData, totalReceipts] = await Promise.all([
    postgresDb
      .select({
        id: receipts.id,
        saleId: receipts.saleId,
        receiptUrl: receipts.receiptUrl,
        createdBy: users.name,
        createdAt: receipts.createdAt,
        // Store information
        storeName: stores.name,
      })
      .from(receipts)
      .leftJoin(users, eq(receipts.createdBy, users.id))
      .leftJoin(sales, eq(receipts.saleId, sales.id))
      .leftJoin(stores, eq(sales.storeId, stores.id))
      .where(receiptsFilter)
      .orderBy(desc(receipts.createdAt))
      .limit(limit)
      .offset(offset),
    postgresDb
      .select({ count: count() })
      .from(receipts)
      .where(receiptsFilter),
  ]);

  return {
    total: totalReceipts[0].count,
    page,
    perPage: limit,
    receipts: receiptsData,
  };
};

/**
 * ✅ **Get Receipt by Sale ID**
 */
export const getReceiptBySaleId = async (
  requester: JwtPayload,
  saleId: number
) => {
  await authorizeAction(requester, "getById", "receipts", saleId);

  const isSuperadmin = requester.role === "superadmin";
  const isCashier = requester.role === "cashier";

  // Different filter logic based on role
  let receiptFilter;

  if (isSuperadmin) {
    // Superadmin can see any receipt
    receiptFilter = eq(receipts.saleId, saleId);
  } else if (requester.role === "admin") {
    // Admin can only see their own receipts and receipts from users they created
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

    if (teamMemberIds.length > 0) {
      // Admin can see their own receipts and receipts from users they created
      receiptFilter = and(
        eq(receipts.saleId, saleId),
        or(
          eq(receipts.createdBy, requester.id),
          inArray(receipts.createdBy, teamMemberIds)
        )
      );
    } else {
      // If no team members found, just show their own receipts
      receiptFilter = and(
        eq(receipts.saleId, saleId),
        eq(receipts.createdBy, requester.id)
      );
    }
  } else if (isCashier) {
    // Cashier can only see their own receipts and receipts from their admin and siblings

    // Find the cashier's creator (admin)
    const creatorResult = await postgresDb
      .select({ creatorId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    const creatorId = creatorResult[0]?.creatorId || undefined;

    if (creatorId) {
      // Get all users created by the same admin (siblings)
      const siblingsResult = await postgresDb
        .select({ siblingId: users.id })
        .from(users)
        .where(eq(users.createdBy, creatorId));

      const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

      // Build the filter to include own receipts, creator's receipts, and siblings' receipts
      receiptFilter = and(
        eq(receipts.saleId, saleId),
        or(
          eq(receipts.createdBy, requester.id),
          eq(receipts.createdBy, creatorId),
          inArray(receipts.createdBy, siblingIds)
        )
      );
    } else {
      // If no creator found, just show their own receipts
      receiptFilter = and(
        eq(receipts.saleId, saleId),
        eq(receipts.createdBy, requester.id)
      );
    }
  } else {
    // Default case - just show own receipts
    receiptFilter = and(
      eq(receipts.saleId, saleId),
      eq(receipts.createdBy, requester.id)
    );
  }

  const receiptData = await postgresDb
    .select({
      id: receipts.id,
      saleId: receipts.saleId,
      receiptUrl: receipts.receiptUrl,
      createdBy: users.name,
      createdAt: receipts.createdAt,
      // Store information
      storeName: stores.name,
      storeAddress: stores.address,
      storeCity: stores.city,
      storeState: stores.state,
      storeCountry: stores.country,
      storePhone: stores.phone,
      storeEmail: stores.email,
      storeLogo: stores.logo,
    })
    .from(receipts)
    .leftJoin(users, eq(receipts.createdBy, users.id))
    .leftJoin(sales, eq(receipts.saleId, sales.id))
    .leftJoin(stores, eq(sales.storeId, stores.id))
    .where(receiptFilter)
    .limit(1);

  if (receiptData.length === 0)
    throw new Error("Receipt not found or unauthorized.");

  return receiptData[0];
};

/**
 * ✅ **Delete Receipt by ID (Single or Multiple)**
 */
export const deleteReceiptById = async (requester: JwtPayload, ids: number | number[]) => {
  const receiptIds = Array.isArray(ids) ? ids : [ids];

  // Check authorization for each receipt
  for (const id of receiptIds) {
    await authorizeAction(requester, "delete", "receipts", id);
  }

  const isSuperadmin = requester.role === "superadmin";
  const isCashier = requester.role === "cashier";

  // For each receipt, ensure it exists and user has permission
  for (const id of receiptIds) {
    // Different filter logic based on role
    let receiptFilter;

    if (isSuperadmin) {
      // Superadmin can delete any receipt
      receiptFilter = eq(receipts.id, id);
    } else if (isCashier || requester.role === "admin") {
      // For both cashiers and admins, we need to find their team

      // First, find the creator of this user (their admin)
      const creatorResult = await postgresDb
        .select({ creatorId: users.createdBy })
        .from(users)
        .where(eq(users.id, requester.id))
        .limit(1);

      const creatorId = creatorResult[0]?.creatorId || undefined;

      // For admins, get all users they created
      let teamMemberIds: number[] = [];

      if (requester.role === "admin") {
        const teamMembersResult = await postgresDb
          .select({ memberId: users.id })
          .from(users)
          .where(eq(users.createdBy, requester.id));

        teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);
      }

      // Build the filter based on the user's role and relationships
      if (requester.role === "admin" && teamMemberIds.length > 0) {
        // Admin can delete their own receipts and receipts from users they created
        receiptFilter = and(
          eq(receipts.id, id),
          or(
            eq(receipts.createdBy, requester.id),
            inArray(receipts.createdBy, teamMemberIds)
          )
        );
      } else if (creatorId) {
        // If this user has a creator (admin), they can delete their own receipts,
        // their creator's receipts, and receipts from other users created by the same admin

        // First, get all users created by the same admin (siblings)
        const siblingsResult = await postgresDb
          .select({ siblingId: users.id })
          .from(users)
          .where(eq(users.createdBy, creatorId));

        const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

        // Build the filter to include own receipts, creator's receipts, and siblings' receipts
        receiptFilter = and(
          eq(receipts.id, id),
          or(
            eq(receipts.createdBy, requester.id),
            eq(receipts.createdBy, creatorId),
            inArray(receipts.createdBy, siblingIds)
          )
        );
      } else {
        // If no relationships found, just show their own receipts
        receiptFilter = and(
          eq(receipts.id, id),
          eq(receipts.createdBy, requester.id)
        );
      }
    } else {
      // Default case - just show own receipts
      receiptFilter = and(
        eq(receipts.id, id),
        eq(receipts.createdBy, requester.id)
      );
    }

    const existingReceipt = await postgresDb
      .select()
      .from(receipts)
      .where(receiptFilter)
      .limit(1);

    if (existingReceipt.length === 0) {
      throw new Error(`Receipt with ID ${id} not found or unauthorized.`);
    }
  }

  // Build the delete filter based on the user's role
  let deleteFilter;

  if (isSuperadmin) {
    // Superadmin can delete any receipt
    deleteFilter = inArray(receipts.id, receiptIds);
  } else if (isCashier || requester.role === "admin") {
    // For both cashiers and admins, we need to find their team

    // First, find the creator of this user (their admin)
    const creatorResult = await postgresDb
      .select({ creatorId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    const creatorId = creatorResult[0]?.creatorId || undefined;

    // For admins, get all users they created
    let teamMemberIds: number[] = [];

    if (requester.role === "admin") {
      const teamMembersResult = await postgresDb
        .select({ memberId: users.id })
        .from(users)
        .where(eq(users.createdBy, requester.id));

      teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);
    }

    // Build the filter based on the user's role and relationships
    if (requester.role === "admin" && teamMemberIds.length > 0) {
      // Admin can delete their own receipts and receipts from users they created
      deleteFilter = and(
        inArray(receipts.id, receiptIds),
        or(
          eq(receipts.createdBy, requester.id),
          inArray(receipts.createdBy, teamMemberIds)
        )
      );
    } else if (creatorId) {
      // If this user has a creator (admin), they can delete their own receipts,
      // their creator's receipts, and receipts from other users created by the same admin

      // First, get all users created by the same admin (siblings)
      const siblingsResult = await postgresDb
        .select({ siblingId: users.id })
        .from(users)
        .where(eq(users.createdBy, creatorId));

      const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

      // Build the filter to include own receipts, creator's receipts, and siblings' receipts
      deleteFilter = and(
        inArray(receipts.id, receiptIds),
        or(
          eq(receipts.createdBy, requester.id),
          eq(receipts.createdBy, creatorId),
          inArray(receipts.createdBy, siblingIds)
        )
      );
    } else {
      // If no relationships found, just delete their own receipts
      deleteFilter = and(
        inArray(receipts.id, receiptIds),
        eq(receipts.createdBy, requester.id)
      );
    }
  } else {
    // Default case - just delete own receipts
    deleteFilter = and(
      inArray(receipts.id, receiptIds),
      eq(receipts.createdBy, requester.id)
    );
  }

  // Delete the receipts
  const deletedReceipts = await postgresDb
    .delete(receipts)
    .where(deleteFilter)
    .returning({ deletedId: receipts.id });

  if (!deletedReceipts || deletedReceipts.length === 0) {
    throw new Error("Delete failed: Receipt(s) not found.");
  }

  return {
    deletedIds: deletedReceipts.map((receipt: { deletedId: number }) => receipt.deletedId)
  };
};
