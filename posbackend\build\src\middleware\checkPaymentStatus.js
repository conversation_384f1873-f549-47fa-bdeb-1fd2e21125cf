"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPaymentStatus = void 0;
const authService_1 = require("../services/authService"); // Ensure this function fetches the user from the DB
const errorHandler_1 = require("./errorHandler");
exports.checkPaymentStatus = (0, errorHandler_1.asyncHandler)(async (req, res, next) => {
    const requester = req.user;
    if (!requester) {
        throw new errorHandler_1.AuthenticationError("Unauthorized: User not authenticated.");
    }
    // Superadmins bypass payment checks
    if (requester.role === "superadmin") {
        return next();
    }
    // Fetch the authenticated user's payment status
    const user = await (0, authService_1.getUserById)(requester, requester.id);
    if (!user) {
        throw new errorHandler_1.NotFoundError("User not found.");
    }
    console.log("Checking Payment Status for:", user.id, user.paymentStatus);
    // Check if payment status requires payment
    if (user.paymentStatus && ["pending", "overdue", "inactive"].includes(user.paymentStatus.toLowerCase())) {
        throw new errorHandler_1.AuthorizationError("Access denied: Payment required to continue using the app.");
    }
    // Payment status is valid, proceed to the next middleware
    next();
});
