"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logoutUser = exports.updateUserById = exports.getAllUsers = exports.deleteUserById = exports.getUserById = exports.loginUser = exports.registerUser = void 0;
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const schema_2 = require("../validation/schema");
const authUtils_1 = require("../utils/authUtils");
const authorizeAction_1 = require("../utils/authorizeAction");
const dayjs_1 = __importDefault(require("dayjs"));
const updatePaymentStatus_1 = require("../utils/updatePaymentStatus");
const userStoreService_1 = require("./userStoreService");
const expenseCategoryService_1 = require("./expenseCategoryService");
// REMOVED: All caching to ensure fresh data
// import cache from "../config/lruCache";
// const userCache = new Map<string, UserRecord>();
const registerUser = async (requester, userData) => {
    console.log("Received Payment Status:", userData.paymentStatus);
    // 🔹 Default payment status based on requester role and user role
    let paymentStatus = userData.paymentStatus ?? "pending";
    // If an admin is creating a user (which will be a cashier), set status to "paid"
    if (requester.role === "admin" && !userData.paymentStatus) {
        paymentStatus = "paid";
    }
    // If a superadmin is creating an admin user, they get 3-month free trial with "paid" status
    if (requester.role === "superadmin" && (userData.role === "admin" || !userData.role)) {
        paymentStatus = "paid"; // Set to paid so they get the 3-month trial
    }
    if (!["pending", "paid", "overdue", "inactive"].includes(paymentStatus)) {
        throw new Error("Invalid payment status.");
    }
    // 🔹 Validate input using Zod schema
    schema_2.userSchema.parse({ ...userData, paymentStatus });
    // 🔹 Check authorization
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "user");
    // 🔹 Check if email or phone already exists
    const existingUser = await db_1.postgresDb
        .select()
        .from(schema_1.users)
        .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.users.email, userData.email), (0, drizzle_orm_1.eq)(schema_1.users.phone, userData.phone)))
        .limit(1);
    console.log("Existing User Found:", existingUser);
    if (existingUser.length > 0) {
        if (existingUser[0].email === userData.email) {
            throw new Error("Email is already in use.");
        }
        if (existingUser[0].phone === userData.phone) {
            throw new Error("Phone number is already in use.");
        }
    }
    // 🔹 Hash password before inserting
    const hashedPassword = await (0, authUtils_1.hashPassword)(userData.password);
    const createdAt = new Date();
    // Set next payment due based on role and payment status
    let nextPaymentDue = null;
    if (paymentStatus === "paid") {
        // If admin is creating a cashier, use admin's payment schedule
        if (requester.role === "admin") {
            // Get admin's subscription period from their latest payment
            const adminLatestPayment = await db_1.postgresDb
                .select({
                subscriptionPeriod: schema_1.payments.subscriptionPeriod,
                nextPaymentDue: schema_1.users.nextPaymentDue
            })
                .from(schema_1.payments)
                .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.payments.userId, schema_1.users.id))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.userId, requester.id), (0, drizzle_orm_1.eq)(schema_1.payments.status, "successful")))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.payments.paidAt))
                .limit(1);
            if (adminLatestPayment.length > 0) {
                // Use admin's subscription period
                const subscriptionPeriod = adminLatestPayment[0].subscriptionPeriod || 1;
                nextPaymentDue = (0, dayjs_1.default)(createdAt).add(subscriptionPeriod, "month").toDate();
                console.log(`📅 Cashier inherits admin's ${subscriptionPeriod}-month subscription period`);
            }
            else {
                // Fallback to 1 month if no payment history found
                nextPaymentDue = (0, dayjs_1.default)(createdAt).add(1, "month").toDate();
                console.log(`⚠️ No admin payment history found, defaulting to 1-month subscription`);
            }
        }
        else if (requester.role === "superadmin" && (userData.role === "admin" || !userData.role)) {
            // New admin users get 3-month free trial
            nextPaymentDue = (0, dayjs_1.default)(createdAt).add(3, "month").toDate();
            console.log(`🎉 New admin user gets 3-month free trial. Payment due: ${nextPaymentDue}`);
        }
        else {
            // Default case
            nextPaymentDue = (0, dayjs_1.default)(createdAt).add(1, "month").toDate();
        }
    }
    try {
        // Start a transaction to ensure atomicity
        return await db_1.postgresDb.transaction(async (trx) => {
            // 🔹 Insert new user into the database
            const [newUser] = await trx
                .insert(schema_1.users)
                .values({
                name: userData.name,
                email: userData.email,
                phone: userData.phone,
                role: userData.role || "cashier",
                passwordHash: hashedPassword,
                createdBy: requester.id,
                paymentStatus,
                lastPaymentDate: createdAt,
                nextPaymentDue,
                createdAt,
            })
                .returning();
            // If an admin is creating a cashier, auto-associate with admin's default store
            if (requester.role === "admin" && newUser.role === "cashier") {
                try {
                    // Get admin's default store
                    const adminDefaultStore = await (0, userStoreService_1.getUserDefaultStore)(requester, requester.id);
                    if (adminDefaultStore) {
                        // Associate the new cashier with the admin's default store
                        await trx
                            .insert(schema_1.userStores)
                            .values({
                            userId: newUser.id,
                            storeId: adminDefaultStore.id,
                            isDefault: true, // Make it the default store for the cashier
                            createdBy: requester.id,
                        });
                        console.log(`Cashier (ID: ${newUser.id}) auto-associated with admin's store (ID: ${adminDefaultStore.id})`);
                    }
                }
                catch (storeError) {
                    console.error("Error associating cashier with admin's store:", storeError);
                    // Continue even if store association fails - don't block user creation
                }
            }
            // Initialize default expense categories for new admin users
            if (newUser.role === "admin") {
                try {
                    await (0, expenseCategoryService_1.initializeDefaultExpenseCategories)(newUser.id);
                    console.log(`Default expense categories initialized for admin (ID: ${newUser.id})`);
                }
                catch (expenseError) {
                    console.error("Error initializing default expense categories:", expenseError);
                    // Continue even if expense category initialization fails - don't block user creation
                }
            }
            // 🔹 Remove password before returning user data
            const safeUser = (0, authUtils_1.omitPassword)(newUser);
            console.log("Registered User (No Password):", safeUser);
            // 🔹 Generate authentication token
            const accessToken = (0, authUtils_1.generateToken)(safeUser.id, safeUser.role);
            return { user: safeUser, accessToken };
        });
    }
    catch (error) {
        console.error("Database Error:", error);
        if (error.message.includes("duplicate key value violates unique constraint")) {
            if (error.message.includes("users_email_unique")) {
                throw new Error("Email is already in use.");
            }
            if (error.message.includes("users_phone_unique")) {
                throw new Error("Phone number is already in use.");
            }
        }
        throw error;
    }
};
exports.registerUser = registerUser;
// ✅ **Login User** - NO CACHING for fresh data
const loginUser = async (email, password) => {
    schema_2.loginSchema.parse({ email, password });
    console.log(`🔍 Login attempt for user: ${email} - Fetching fresh data from database`);
    // ALWAYS fetch fresh data from database - NO CACHING
    const userData = await db_1.postgresDb
        .select({
        id: schema_1.users.id,
        email: schema_1.users.email,
        passwordHash: schema_1.users.passwordHash,
        role: schema_1.users.role,
        paymentStatus: schema_1.users.paymentStatus,
        name: schema_1.users.name,
        phone: schema_1.users.phone,
        createdAt: schema_1.users.createdAt,
        lastPaymentDate: schema_1.users.lastPaymentDate,
        nextPaymentDue: schema_1.users.nextPaymentDue,
        createdBy: schema_1.users.createdBy
    })
        .from(schema_1.users)
        .where((0, drizzle_orm_1.eq)(schema_1.users.email, email))
        .limit(1);
    if (userData.length === 0)
        throw new Error("User not found.");
    const userRecord = userData[0];
    console.log(`✅ Fresh user data fetched for ${email}:`, {
        id: userRecord.id,
        paymentStatus: userRecord.paymentStatus,
        lastPaymentDate: userRecord.lastPaymentDate,
        nextPaymentDue: userRecord.nextPaymentDue
    });
    // Verify password
    const isPasswordValid = await (0, authUtils_1.comparePassword)(password, userRecord.passwordHash);
    if (!isPasswordValid) {
        // Log failed login attempt
        console.warn(`Failed login attempt for user: ${email}`);
        throw new Error("Invalid credentials.");
    }
    // Create safe user object without password
    const safeUser = (0, authUtils_1.omitPassword)(userRecord);
    // Generate token
    const accessToken = (0, authUtils_1.generateToken)(safeUser.id, safeUser.role);
    // REMOVED: No caching to ensure fresh data on every request
    // cache.set(userByIdCacheKey, userRecord, 1800000);
    console.log(`✅ Login successful for user ${email} with fresh payment status: ${userRecord.paymentStatus}`);
    return { user: safeUser, accessToken };
};
exports.loginUser = loginUser;
// ✅ **Get User by ID - NO CACHING for fresh data**
const getUserById = async (requester, id) => {
    try {
        console.log(`🔍 Getting user by ID: ${id} - Fetching fresh data from database`);
        // Check authorization first
        if (requester.role === "cashier" && id !== requester.id) {
            throw new Error("Unauthorized: Cashiers can only access their own details.");
        }
        // REMOVED: No caching to ensure fresh data
        // const cacheKey = `user:id:${id}:requester:${requester.id}:${requester.role}`;
        // const cachedUser = cache.get(cacheKey);
        // If not in cache, build the appropriate query
        let query;
        if (requester.role === "cashier") {
            // Cashier can only fetch their own details
            query = db_1.postgresDb.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id));
        }
        else if (requester.role === "admin") {
            // Admin can fetch their own details or users they created
            if (id === requester.id) {
                // Admin fetching their own details
                query = db_1.postgresDb.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id));
            }
            else {
                // Admin fetching details of a user they created
                query = db_1.postgresDb
                    .select()
                    .from(schema_1.users)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.id, id), (0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id)));
            }
        }
        else if (requester.role === "superadmin") {
            // Superadmin has full access
            query = db_1.postgresDb.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.id, id));
        }
        else {
            throw new Error("Unauthorized: Invalid role.");
        }
        // Execute the query
        const userData = await query;
        if (userData.length === 0) {
            throw new Error("User not found or access denied");
        }
        // Create safe user object without password
        const safeUser = (0, authUtils_1.omitPassword)(userData[0]);
        // REMOVED: No caching to ensure fresh data
        // cache.set(cacheKey, safeUser, 600000);
        console.log(`✅ Fresh user data fetched for ID ${id}:`, {
            id: safeUser.id,
            paymentStatus: safeUser.paymentStatus,
            lastPaymentDate: safeUser.lastPaymentDate,
            nextPaymentDue: safeUser.nextPaymentDue
        });
        return safeUser;
    }
    catch (error) {
        console.error("Error in getUserById:", error);
        throw error;
    }
};
exports.getUserById = getUserById;
// ✅ **Delete Users by ID (Single or Multiple)**
const deleteUserById = async (requester, ids) => {
    const userIds = Array.isArray(ids) ? ids : [ids];
    // Check authorization for each user
    for (const id of userIds) {
        // ✅ Authorize the action (ensures Admins can only delete users they created)
        const query = (0, authorizeAction_1.authorizeAction)(requester, "delete", "user", id);
        const userToDelete = await query;
        if (userToDelete.length === 0) {
            throw new Error(`User with ID ${id} not found or access denied`);
        }
    }
    // 🚨 **Perform the DELETE operation**
    const deletedUsers = await db_1.postgresDb
        .delete(schema_1.users)
        .where((0, drizzle_orm_1.inArray)(schema_1.users.id, userIds))
        .returning({ deletedId: schema_1.users.id });
    if (!deletedUsers || deletedUsers.length === 0) {
        throw new Error("Delete failed: Users not found.");
    }
    return {
        deletedIds: deletedUsers.map(user => user.deletedId),
    };
};
exports.deleteUserById = deleteUserById;
// ✅ **Get All Users**
const getAllUsers = async (requester, page = 1, limit = 10, searchTerm) => {
    const offset = (page - 1) * limit;
    // We'll use direct role-based filtering instead of authorizeAction
    // This is because authorizeAction returns a query, but we need to handle it differently
    // for getAllUsers
    let query;
    let countQuery;
    // Apply role-based filtering
    if (requester.role === "superadmin") {
        // Superadmin can see all users
        query = db_1.postgresDb.select().from(schema_1.users);
        countQuery = db_1.postgresDb.select({ count: (0, drizzle_orm_1.count)() }).from(schema_1.users);
    }
    else if (requester.role === "admin") {
        // Admin can only see users they created
        query = db_1.postgresDb
            .select()
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        countQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
    }
    else {
        // Cashier can only see themselves
        query = db_1.postgresDb
            .select()
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id));
        countQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id));
    }
    // Apply search filter if provided
    if (searchTerm && searchTerm.trim() !== '') {
        // Use case-insensitive search with wildcards on both sides
        const searchPattern = `%${searchTerm.trim()}%`;
        // Log the exact search pattern for debugging
        console.log(`Search pattern: "${searchPattern}"`);
        const searchCondition = (0, drizzle_orm_1.or)((0, drizzle_orm_1.like)(schema_1.users.name, searchPattern), (0, drizzle_orm_1.like)(schema_1.users.email, searchPattern), (0, drizzle_orm_1.like)(schema_1.users.phone, searchPattern));
        // For role-based queries, we need to modify the existing conditions
        if (requester.role === "superadmin") {
            // Superadmin - just add search condition
            query = db_1.postgresDb
                .select()
                .from(schema_1.users)
                .where(searchCondition);
            countQuery = db_1.postgresDb
                .select({ count: (0, drizzle_orm_1.count)() })
                .from(schema_1.users)
                .where(searchCondition);
        }
        else if (requester.role === "admin") {
            // Admin - combine with createdBy condition
            query = db_1.postgresDb
                .select()
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id), searchCondition));
            countQuery = db_1.postgresDb
                .select({ count: (0, drizzle_orm_1.count)() })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id), searchCondition));
        }
        else {
            // Cashier - combine with id condition
            query = db_1.postgresDb
                .select()
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id), searchCondition));
            countQuery = db_1.postgresDb
                .select({ count: (0, drizzle_orm_1.count)() })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id), searchCondition));
        }
        console.log(`Searching users with term: "${searchTerm}"`);
    }
    // Apply pagination and ordering
    query = query.orderBy((0, drizzle_orm_1.desc)(schema_1.users.createdAt)).limit(limit).offset(offset);
    try {
        // Execute both queries
        const [userData, totalUsers] = await Promise.all([query, countQuery]);
        // Log the results for debugging
        console.log(`getAllUsers - Found ${userData.length} users for ${requester.role} (ID: ${requester.id})`);
        return {
            total: totalUsers[0]?.count || 0,
            page,
            perPage: limit,
            users: userData.map(authUtils_1.omitPassword),
        };
    }
    catch (error) {
        console.error("Error in getAllUsers:", error);
        throw error;
    }
};
exports.getAllUsers = getAllUsers;
// ✅ **Update User**
const updateUserById = async (requester, userId, updateData) => {
    // Handle password change if requested
    if (updateData.passwordChange && updateData.currentPassword && updateData.newPassword) {
        // Get the user's current password hash
        const [userToUpdate] = await db_1.postgresDb
            .select({ passwordHash: schema_1.users.passwordHash })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId));
        if (!userToUpdate) {
            throw new Error("User not found.");
        }
        // Verify current password
        const isPasswordValid = await (0, authUtils_1.comparePassword)(updateData.currentPassword, userToUpdate.passwordHash);
        if (!isPasswordValid) {
            throw new Error("Current password is incorrect.");
        }
        // Hash the new password
        const newPasswordHash = await (0, authUtils_1.hashPassword)(updateData.newPassword);
        // Update the password
        const [updatedUser] = await db_1.postgresDb
            .update(schema_1.users)
            .set({ passwordHash: newPasswordHash })
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .returning();
        if (!updatedUser) {
            throw new Error("Failed to update password.");
        }
        return { success: true, message: "Password updated successfully." };
    }
    // Regular user update (not password change)
    // 🔒 **Cashiers cannot update users**
    if (requester.role === "cashier" && userId !== requester.id) {
        throw new Error("Unauthorized: Cashiers can only update their own profile.");
    }
    // 🔒 **Admins can only update users they created**
    if (requester.role === "admin" && userId !== requester.id) {
        const [userToUpdate] = await db_1.postgresDb
            .select({ createdBy: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId));
        if (!userToUpdate || userToUpdate.createdBy !== requester.id) {
            throw new Error("Unauthorized: Admins can only update users they created.");
        }
        // 🚫 **Admins cannot update paymentStatus**
        if (updateData.paymentStatus) {
            throw new Error("Unauthorized: Admins cannot update payment status.");
        }
    }
    // If a superadmin is updating payment status, propagate to all users created by this user
    if (requester.role === "superadmin" && updateData.paymentStatus) {
        // Get the user's role to check if they're an admin
        const [userToUpdate] = await db_1.postgresDb
            .select({ role: schema_1.users.role })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId));
        // Only propagate payment status changes for admin users
        if (userToUpdate && userToUpdate.role === "admin") {
            await (0, updatePaymentStatus_1.updatePaymentStatus)(userId, updateData.paymentStatus);
            console.log(`Payment status updated to ${updateData.paymentStatus} for admin ID ${userId} and all their cashiers`);
        }
    }
    // Remove password-related and undefined values from updateData
    const { currentPassword, newPassword, passwordChange, ...restData } = updateData;
    const filteredUpdateData = Object.fromEntries(Object.entries(restData).filter(([_, value]) => value !== undefined));
    if (Object.keys(filteredUpdateData).length === 0) {
        throw new Error("No valid fields provided for update.");
    }
    // Check for duplicate email or phone before updating (only if they're actually changing)
    if (filteredUpdateData.email || filteredUpdateData.phone) {
        console.log(`🔍 Checking for duplicates - User ID: ${userId}`);
        console.log('Update data:', filteredUpdateData);
        // First get the current user data to compare
        const [currentUser] = await db_1.postgresDb
            .select({ email: schema_1.users.email, phone: schema_1.users.phone })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .limit(1);
        if (!currentUser) {
            throw new Error("User not found.");
        }
        console.log('Current user data:', currentUser);
        const conditions = [];
        // Only check for email duplicates if the email is actually changing
        if (filteredUpdateData.email && filteredUpdateData.email !== currentUser.email) {
            console.log(`📧 Email is changing: ${currentUser.email} → ${filteredUpdateData.email}`);
            conditions.push((0, drizzle_orm_1.eq)(schema_1.users.email, filteredUpdateData.email));
        }
        else if (filteredUpdateData.email) {
            console.log(`📧 Email not changing: ${filteredUpdateData.email} (same as current)`);
        }
        // Only check for phone duplicates if the phone is actually changing
        if (filteredUpdateData.phone && filteredUpdateData.phone !== currentUser.phone) {
            console.log(`📱 Phone is changing: ${currentUser.phone} → ${filteredUpdateData.phone}`);
            conditions.push((0, drizzle_orm_1.eq)(schema_1.users.phone, filteredUpdateData.phone));
        }
        else if (filteredUpdateData.phone) {
            console.log(`📱 Phone not changing: ${filteredUpdateData.phone} (same as current)`);
        }
        // Only run the duplicate check if there are conditions to check
        if (conditions.length > 0) {
            console.log(`🔍 Running duplicate check with ${conditions.length} condition(s)`);
            const existingUser = await db_1.postgresDb
                .select()
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.or)(...conditions), (0, drizzle_orm_1.ne)(schema_1.users.id, userId) // Exclude the current user
            ))
                .limit(1);
            console.log('Existing user found:', existingUser);
            if (existingUser.length > 0) {
                if (filteredUpdateData.email &&
                    filteredUpdateData.email !== currentUser.email &&
                    existingUser[0].email === filteredUpdateData.email) {
                    console.log('❌ Email conflict detected');
                    throw new Error("Email is already in use by another user.");
                }
                if (filteredUpdateData.phone &&
                    filteredUpdateData.phone !== currentUser.phone &&
                    existingUser[0].phone === filteredUpdateData.phone) {
                    console.log('❌ Phone conflict detected');
                    throw new Error("Phone number is already in use by another user.");
                }
            }
        }
        else {
            console.log('✅ No duplicate check needed - no values are changing');
        }
    }
    // Update user data
    const [updatedUser] = await db_1.postgresDb
        .update(schema_1.users)
        .set(filteredUpdateData)
        .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
        .returning();
    if (!updatedUser)
        throw new Error("User not found or update failed.");
    return { updatedUser };
};
exports.updateUserById = updateUserById;
const logoutUser = async (email) => {
    // REMOVED: No cache to clear
    // userCache.delete(email);
    console.log(`✅ User ${email} logged out - no cache to clear`);
};
exports.logoutUser = logoutUser;
