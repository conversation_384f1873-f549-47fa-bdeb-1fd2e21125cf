"use client";

import React, { useState, useEffect } from "react";
import {
  Button,
  Spin,
  Empty,
  Image,
  Tooltip,
  Card,
  Input,
  notification,
} from "antd";
import {
  PrinterOutlined,
  SearchOutlined,
  LoadingOutlined,
  EyeOutlined,
  ShoppingOutlined,
  UserOutlined,
  CalendarOutlined,
  ShopOutlined,
  CloseOutlined,
  ReloadOutlined,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { useGetAllReceiptsQuery } from "@/reduxRTK/services/receiptApi";
import { formatDate } from "@/utils/formatDate";
import { showMessage } from "@/utils/showMessage";
import { useMediaQuery } from "react-responsive";
import ReceiptTable from "@/components/Receipts/ReceiptTable";
import { useReceiptDelete } from "@/hooks/receipts/useReceiptDelete";
import { useReceiptBulkDelete } from "@/hooks/receipts/useReceiptBulkDelete";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import "./receipts.css";

export default function ReceiptsPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReceiptUrl, setSelectedReceiptUrl] = useState<string | null>(
    null,
  );
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isMobile = useMediaQuery({ maxWidth: 768 });

  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedReceiptId, setSelectedReceiptId] = useState<number | null>(
    null,
  );

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [receiptsToDelete, setReceiptsToDelete] = useState<number[]>([]);

  // Fetch receipts
  const {
    data: receiptsData,
    isLoading,
    refetch,
  } = useGetAllReceiptsQuery({
    page,
    limit,
    search: searchTerm,
  });

  // Delete receipt handler
  const { deleteReceipt, isDeleting } = useReceiptDelete(() => {
    setIsDeleteDialogOpen(false);
    refetch();
  });

  // Bulk delete receipt handler
  const { bulkDeleteReceipts, isDeleting: isBulkDeleting } =
    useReceiptBulkDelete(() => {
      setIsBulkDeleteDialogOpen(false);
      refetch();
    });

  // Auto-refresh receipts data every 10 seconds
  useEffect(() => {
    const intervalId = setInterval(() => {
      refetch();
    }, 10000); // 10 seconds

    return () => clearInterval(intervalId);
  }, [refetch]);

  // Manual refresh function
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  };

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle print receipt - directly trigger print dialog
  const handlePrintReceipt = (receiptUrl: string) => {
    // Create a hidden iframe to load the image
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Set up the iframe content with the image and print CSS
    iframe.onload = () => {
      if (iframe.contentWindow) {
        // Write the HTML content to the iframe
        iframe.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${receiptUrl}" alt="Receipt" />
            </body>
          </html>
        `);

        // Close the document
        iframe.contentWindow.document.close();

        // Wait for image to load before printing
        setTimeout(() => {
          if (iframe.contentWindow) {
            // Print the iframe content
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // Remove the iframe after printing (or after a timeout)
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          }
        }, 500);
      }
    };

    // Set the iframe source to trigger the onload event
    iframe.src = "about:blank";
  };

  // Handle view receipt
  const handleViewReceipt = (receiptUrl: string) => {
    setSelectedReceiptUrl(receiptUrl);
  };

  // Close receipt preview
  const handleClosePreview = () => {
    setSelectedReceiptUrl(null);
  };

  // Handle delete receipt
  const handleDeleteReceipt = (receiptId: number) => {
    setSelectedReceiptId(receiptId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete receipt
  const confirmDeleteReceipt = async () => {
    if (selectedReceiptId) {
      try {
        await deleteReceipt(selectedReceiptId);
      } catch (error) {
        console.error("Error deleting receipt:", error);
      }
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedReceiptId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (receiptIds: number[]) => {
    console.log("handleBulkDelete called with receiptIds:", receiptIds);
    setReceiptsToDelete(receiptIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with receipts:", receiptsToDelete);

    if (receiptsToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple receipts
        await bulkDeleteReceipts(receiptsToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setReceiptsToDelete([]);
  };

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Receipt Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
      >
        <div className="w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm">
          {/* Search Component - Always visible */}
          <div className="sticky top-0 z-10 mb-4 flex items-center justify-between border-b border-gray-200 bg-gray-50 px-3 py-3">
            <div>
              <Input
                placeholder="Search receipts..."
                prefix={<SearchOutlined className="text-gray-400" />}
                value={searchTerm}
                onChange={handleSearchChange}
                className="border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500"
                style={{
                  width: isMobile ? "100%" : "300px",
                  height: "36px",
                  backgroundColor: "white",
                  color: "#333",
                }}
                allowClear={{
                  clearIcon: <span className="text-gray-600">×</span>,
                }}
              />
              {searchTerm && (
                <div className="ml-1 mt-1 text-xs text-gray-600">
                  Searching for: &quot;{searchTerm}&quot;
                </div>
              )}
            </div>
            <Button
              icon={<ReloadOutlined spin={isRefreshing} />}
              onClick={handleRefresh}
              loading={isLoading && !isRefreshing}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              title="Refresh receipts"
            >
              {!isMobile && "Refresh"}
            </Button>
          </div>

          {isLoading ? (
            <div className="flex h-60 items-center justify-center bg-white">
              <Spin
                indicator={
                  <LoadingOutlined
                    style={{ fontSize: 24, color: "#1890ff" }}
                    spin
                  />
                }
              />
            </div>
          ) : (
            <>
              {/* Receipt Table Component */}
              {receiptsData?.data?.receipts &&
              receiptsData.data.receipts.length > 0 ? (
                <ReceiptTable
                  receipts={receiptsData.data.receipts}
                  loading={isLoading}
                  onViewReceipt={handleViewReceipt}
                  onPrintReceipt={handlePrintReceipt}
                  onDelete={handleDeleteReceipt}
                  onBulkDelete={handleBulkDelete}
                  isMobile={isMobile}
                />
              ) : (
                <div className="flex h-60 flex-col items-center justify-center rounded-md border border-gray-200 bg-white text-gray-600">
                  {searchTerm ? (
                    <>
                      <p>No receipts found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm("")}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No receipts found.</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {receiptsData?.data?.receipts &&
                receiptsData.data.receipts.length > 0 && (
                  <div className="flex items-center justify-between border-t border-gray-200 bg-gray-50 px-4 py-3 sm:px-6">
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Showing{" "}
                          <span className="font-medium text-gray-900">
                            {(page - 1) * limit + 1}
                          </span>{" "}
                          to{" "}
                          <span className="font-medium text-gray-900">
                            {Math.min(page * limit, receiptsData.data.total)}
                          </span>{" "}
                          of{" "}
                          <span className="font-medium text-gray-900">
                            {receiptsData.data.total}
                          </span>{" "}
                          receipts
                        </p>
                      </div>
                      <div>
                        <nav
                          className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm"
                          aria-label="Pagination"
                        >
                          <button
                            onClick={() =>
                              handlePageChange(Math.max(1, page - 1))
                            }
                            disabled={page === 1}
                            className={`relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ${page === 1 ? "cursor-not-allowed text-gray-400" : "text-gray-700 hover:bg-gray-50"}`}
                          >
                            <span className="sr-only">Previous</span>
                            <LeftOutlined
                              className="h-5 w-5"
                              aria-hidden="true"
                            />
                          </button>

                          {/* Page numbers */}
                          {Array.from(
                            {
                              length: Math.min(
                                5,
                                Math.ceil(receiptsData.data.total / limit),
                              ),
                            },
                            (_, i) => {
                              const pageNum = i + 1;
                              return (
                                <button
                                  key={pageNum}
                                  onClick={() => handlePageChange(pageNum)}
                                  className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${
                                    page === pageNum
                                      ? "z-10 border-blue-500 bg-blue-50 text-blue-600"
                                      : "border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                                  }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            },
                          )}

                          <button
                            onClick={() => handlePageChange(page + 1)}
                            disabled={
                              page >= Math.ceil(receiptsData.data.total / limit)
                            }
                            className={`relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium ${
                              page >= Math.ceil(receiptsData.data.total / limit)
                                ? "cursor-not-allowed text-gray-400"
                                : "text-gray-700 hover:bg-gray-50"
                            }`}
                          >
                            <span className="sr-only">Next</span>
                            <RightOutlined
                              className="h-5 w-5"
                              aria-hidden="true"
                            />
                          </button>
                        </nav>
                      </div>
                    </div>

                    {/* Mobile pagination */}
                    <div className="flex w-full items-center justify-between sm:hidden">
                      <button
                        onClick={() => handlePageChange(Math.max(1, page - 1))}
                        disabled={page === 1}
                        className={`relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ${
                          page === 1
                            ? "cursor-not-allowed bg-gray-100 text-gray-400"
                            : "bg-white text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        Previous
                      </button>
                      <div className="text-sm text-gray-700">
                        Page {page} of{" "}
                        {Math.ceil(receiptsData.data.total / limit)}
                      </div>
                      <button
                        onClick={() => handlePageChange(page + 1)}
                        disabled={
                          page >= Math.ceil(receiptsData.data.total / limit)
                        }
                        className={`relative inline-flex items-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium ${
                          page >= Math.ceil(receiptsData.data.total / limit)
                            ? "cursor-not-allowed bg-gray-100 text-gray-400"
                            : "bg-white text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
            </>
          )}
        </div>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteReceipt}
        title="Delete Receipt"
        message="Are you sure you want to delete this receipt? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Receipts"
        message={`Are you sure you want to delete ${receiptsToDelete.length} receipts? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />

      {/* Receipt Preview Panel - Using SlidingPanel like Product Panel */}
      {selectedReceiptUrl && (
        <div className="fixed inset-0 z-[1000] overflow-hidden">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black opacity-50 transition-opacity duration-300"
            onClick={handleClosePreview}
          />

          {/* Panel */}
          <div
            className="absolute bottom-0 right-0 top-0 flex translate-x-0 transform flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out"
            style={{ width: isMobile ? "100vw" : "500px" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between border-b border-gray-200 bg-gray-50 px-4 py-3">
              <h2 className="truncate text-lg font-medium text-gray-800">
                Receipt Preview
              </h2>
              <Button
                type="text"
                icon={<CloseOutlined style={{ color: "#333" }} />}
                onClick={handleClosePreview}
                aria-label="Close panel"
                style={{
                  color: "#333",
                  borderColor: "transparent",
                  background: "transparent",
                }}
              />
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto bg-white p-4">
              {/* Receipt detail heading with icon */}
              <div className="mb-6 border-b border-gray-200 pb-4">
                <h2 className="flex items-center text-xl font-bold text-gray-800">
                  <PrinterOutlined className="mr-2" />
                  Receipt Details
                </h2>
                <p className="mt-1 flex items-center text-gray-600">
                  View and print your receipt
                </p>
              </div>

              <div className="mb-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
                <div className="flex justify-center">
                  <Image
                    src={selectedReceiptUrl}
                    alt="Receipt"
                    className="max-w-full"
                    preview={false}
                  />
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="border-t border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex justify-end space-x-2">
                <Button
                  onClick={handleClosePreview}
                  className="text-gray-700 hover:text-gray-900"
                  style={{ borderColor: "#d9d9d9", background: "#f5f5f5" }}
                >
                  Close
                </Button>
                <Button
                  type="primary"
                  icon={<PrinterOutlined />}
                  onClick={() => handlePrintReceipt(selectedReceiptUrl)}
                >
                  Print
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
