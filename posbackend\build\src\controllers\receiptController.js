"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleReceiptRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const receiptService_1 = require("../services/receiptService");
const modeValidator_1 = require("../utils/modeValidator");
/**
 * ✅ **Handle Receipt Requests**
 */
const handleReceiptRequest = async (req, res) => {
    const { mode, page, limit, saleId, id } = req.body;
    const requester = req.user; // ✅ Extract requester from middleware
    // ✅ Validate mode
    const validModes = ["retrieve", "retrieveBySaleId", "delete"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            /**
             * ✅ **Retrieve All Receipts (Paginated)**
             */
            case "retrieve": {
                const pageNum = Number(page) || 1;
                const limitNum = Number(limit) || 10;
                if (pageNum < 1 || limitNum < 1) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                }
                const receipts = await (0, receiptService_1.getAllReceipts)(requester, pageNum, limitNum);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Receipts retrieved successfully.", receipts);
            }
            /**
             * ✅ **Retrieve a Receipt by Sale ID**
             */
            case "retrieveBySaleId": {
                if (!saleId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "saleId is required.");
                }
                const receipt = await (0, receiptService_1.getReceiptBySaleId)(requester, Number(saleId));
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Receipt retrieved successfully.", receipt);
            }
            /**
             * ✅ **Delete a Receipt (Single or Multiple)**
             */
            case "delete": {
                // Check if we have receiptIds (array) or id (single)
                const { receiptIds } = req.body;
                if (!id && !receiptIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Receipt ID(s) are required for deletion.");
                }
                // Use receiptIds if provided, otherwise use single id
                const idsToDelete = receiptIds
                    ? (Array.isArray(receiptIds) ? receiptIds : [receiptIds])
                    : [Number(id)];
                const result = await (0, receiptService_1.deleteReceiptById)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} receipts deleted successfully.`
                    : "Receipt deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handleReceiptRequest = handleReceiptRequest;
