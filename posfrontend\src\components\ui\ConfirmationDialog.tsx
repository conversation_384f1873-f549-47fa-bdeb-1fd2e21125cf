"use client";

import React from "react";
import { Modal, Button } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  type?: "danger" | "warning" | "info";
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  isLoading = false,
  type = "danger",
}) => {
  // Determine button type based on dialog type
  const buttonType = type === "danger" ? "primary" : type === "warning" ? "default" : "default";
  const buttonDanger = type === "danger";
  
  // Icon color based on type
  const iconColor = type === "danger" ? "#ff4d4f" : type === "warning" ? "#faad14" : "#1890ff";

  return (
    <Modal
      title={
        <div className="flex items-center">
          <ExclamationCircleOutlined style={{ color: iconColor, marginRight: 8 }} />
          <span>{title}</span>
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={isLoading}>
          {cancelText}
        </Button>,
        <Button
          key="confirm"
          type={buttonType}
          danger={buttonDanger}
          onClick={onConfirm}
          loading={isLoading}
        >
          {confirmText}
        </Button>,
      ]}
      maskClosable={!isLoading}
      closable={!isLoading}
      centered
    >
      <p className="my-4">{message}</p>
    </Modal>
  );
};

export default ConfirmationDialog;
