import { useBulkDeleteStoresMutation } from "@/reduxRTK/services/storeApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useStoreBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting stores
  const [bulkDeleteStores, { isLoading }] = useBulkDeleteStoresMutation();

  const deleteStores = async (storeIds: number[]) => {
    try {
      console.log("Bulk deleting stores with IDs:", storeIds);
      
      const result = await bulkDeleteStores(storeIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete stores");
      }

      showMessage("success", `${storeIds.length} stores deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete stores error:", error);
      showMessage("error", error.message || "Failed to delete stores");
      throw error;
    }
  };

  return {
    bulkDeleteStores: deleteStores,
    isDeleting: isLoading
  };
};
