"use client";

import React, { useState } from "react";
import { <PERSON>ton, Tooltip, Checkbox, Tag, notification, Space } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  DollarOutlined,
  CalendarOutlined,
  UserOutlined,
  TagOutlined,
  CreditCardOutlined,
  ReloadOutlined,
  DeleteFilled,
  PrinterOutlined,
  DownloadOutlined
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Expense } from "@/reduxRTK/services/expenseApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

interface ExpenseTableProps {
  expenses: Expense[];
  loading?: boolean;
  onEdit?: (expense: Expense) => void;
  onDelete?: (expenseId: number) => void;
  onBulkDelete?: (expenseIds: number[]) => void;
  onView?: (expense: Expense) => void;
  selectedExpenses?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  isMobile?: boolean;
}

const ExpenseTable: React.FC<ExpenseTableProps> = ({
  expenses,
  loading = false,
  onEdit,
  onDelete,
  onBulkDelete,
  onView,
  selectedExpenses = [],
  onSelectionChange,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Check permissions
  const canEdit = userRole === "admin" || userRole === "superadmin";
  const canDelete = userRole === "admin" || userRole === "superadmin";

  // Handle individual checkbox change
  const handleCheckboxChange = (expenseId: number, checked: boolean) => {
    if (!onSelectionChange) return;

    const newSelection = checked
      ? [...selectedExpenses, expenseId]
      : selectedExpenses.filter(id => id !== expenseId);

    onSelectionChange(newSelection);
  };

  // Handle select all checkbox
  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (!onSelectionChange) return;

    if (e.target.checked) {
      const allIds = expenses.map(expense => expense.id);
      onSelectionChange(allIds);
    } else {
      onSelectionChange([]);
    }
  };

  // Check if all expenses are selected
  const isAllSelected = expenses.length > 0 && selectedExpenses.length === expenses.length;
  const isIndeterminate = selectedExpenses.length > 0 && selectedExpenses.length < expenses.length;

  // Format payment method for display
  const formatPaymentMethod = (method: string) => {
    const methods: { [key: string]: { label: string; color: string } } = {
      cash: { label: 'Cash', color: 'green' },
      card: { label: 'Card', color: 'blue' },
      mobile_money: { label: 'Mobile Money', color: 'purple' },
      bank_transfer: { label: 'Bank Transfer', color: 'orange' },
      cheque: { label: 'Cheque', color: 'cyan' },
    };

    const methodInfo = methods[method] || { label: method, color: 'default' };
    return <Tag color={methodInfo.color}>{methodInfo.label}</Tag>;
  };

  // Format amount with currency
  const formatAmount = (amount: string) => {
    return `₵${parseFloat(amount).toFixed(2)}`;
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      notification.error({ message: 'Please allow popups to print expenses' });
      return;
    }

    const printContent = `
      <html>
        <head>
          <title>Expenses Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print {
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Expenses Report</h2>
            <p class="date">Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Title</th>
                <th>Amount</th>
                <th>Category</th>
                <th>Date</th>
                <th>Payment Method</th>
                <th>Vendor</th>
              </tr>
            </thead>
            <tbody>
              ${expenses.map(expense => `
                <tr>
                  <td>${expense.title}</td>
                  <td>${formatAmount(expense.amount)}</td>
                  <td>${expense.category?.name || 'No category'}</td>
                  <td>${dayjs(expense.expenseDate).format('MMM DD, YYYY')}</td>
                  <td>${expense.paymentMethod}</td>
                  <td>${expense.vendor || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  const handleExportPDF = () => {
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(16);
    doc.text('Expenses Report', 14, 15);
    doc.setFontSize(10);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY h:mm A')}`, 14, 22);

    // Prepare table data
    const tableData = expenses.map(expense => [
      expense.title,
      formatAmount(expense.amount),
      expense.category?.name || 'No category',
      dayjs(expense.expenseDate).format('MMM DD, YYYY'),
      expense.paymentMethod,
      expense.vendor || '-'
    ]);

    // Add table
    autoTable(doc, {
      head: [['Title', 'Amount', 'Category', 'Date', 'Payment Method', 'Vendor']],
      body: tableData,
      startY: 30,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [41, 128, 185] }
    });

    // Save the PDF
    doc.save('expenses-report.pdf');
  };

  // Add print and export buttons to the table header
  const tableTitle = (
    <div className="flex justify-between items-center">
      <span>Expenses</span>
      <Space>
        <Button
          icon={<PrinterOutlined />}
          onClick={handlePrint}
          title="Print Expenses"
        >
          Print
        </Button>
        <Button
          icon={<DownloadOutlined />}
          onClick={handleExportPDF}
          title="Export to PDF"
        >
          Export
        </Button>
      </Space>
    </div>
  );

  return (
    <div>
      {/* Bulk Actions */}
      {selectedExpenses.length > 0 && canDelete && onBulkDelete && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-red-700">
              {selectedExpenses.length} expense(s) selected
            </span>
            <Button
              type="primary"
              danger
              icon={<DeleteFilled />}
              onClick={() => onBulkDelete(selectedExpenses)}
              size={isMobile ? "small" : "middle"}
            >
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns={onSelectionChange ? "50px 200px 120px 120px 120px 150px" : "200px 120px 120px 120px 150px"}
          minWidth={onSelectionChange ? "800px" : "750px"}
        >
          {/* Mobile Headers */}
          {onSelectionChange && (
            <TableHeader className="text-center">
              <Checkbox
                indeterminate={isIndeterminate}
                checked={isAllSelected}
                onChange={handleSelectAll}
              />
            </TableHeader>
          )}
          <TableHeader>
            <span className="flex items-center">
              <DollarOutlined className="mr-1" />
              Expense
            </span>
          </TableHeader>
          <TableHeader>
            Amount
          </TableHeader>
          <TableHeader>
            Category
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <CalendarOutlined className="mr-1" />
              Date
            </span>
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {expenses.map((expense) => (
            <TableRow
              key={expense.id}
              selected={selectedExpenses.includes(expense.id)}
            >
              {onSelectionChange && (
                <TableCell className="text-center">
                  <Checkbox
                    checked={selectedExpenses.includes(expense.id)}
                    onChange={(e) => handleCheckboxChange(expense.id, e.target.checked)}
                  />
                </TableCell>
              )}
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis">
                  <div className="font-medium">{expense.title}</div>
                  {expense.isRecurring && (
                    <Tag color="blue" className="mt-1 text-xs">
                      <ReloadOutlined className="mr-1" />
                      {expense.recurringFrequency}
                    </Tag>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <span className="font-semibold text-green-600">
                  {formatAmount(expense.amount)}
                </span>
              </TableCell>
              <TableCell>
                {expense.category ? (
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-1"
                      style={{ backgroundColor: expense.category.color }}
                    />
                    <span className="text-sm truncate max-w-[80px]">{expense.category.name}</span>
                  </div>
                ) : (
                  <span className="text-sm text-gray-400">No category</span>
                )}
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {dayjs(expense.expenseDate).format("MMM DD, YYYY")}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  {onView && (
                    <Tooltip title="View Details">
                      <Button
                        type="text"
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => onView(expense)}
                        className="text-green-500 hover:text-green-400"
                      />
                    </Tooltip>
                  )}
                  {canEdit && onEdit && (
                    <Tooltip title="Edit">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => onEdit(expense)}
                        className="text-blue-500 hover:text-blue-400"
                      />
                    </Tooltip>
                  )}
                  {canDelete && onDelete && (
                    <Tooltip title="Delete">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(expense.id)}
                        className="text-red-500 hover:text-red-400"
                      />
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <thead className="bg-gray-50">
              <tr>
                {onSelectionChange && (
                  <th className="px-4 py-3 text-left">
                    <Checkbox
                      indeterminate={isIndeterminate}
                      checked={isAllSelected}
                      onChange={handleSelectAll}
                    />
                  </th>
                )}
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expense
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>

            <tbody className="bg-white divide-y divide-gray-200">
              {expenses.map((expense) => (
                <tr key={expense.id} className="hover:bg-gray-50">
                  {onSelectionChange && (
                    <td className="px-4 py-4 whitespace-nowrap">
                      <Checkbox
                        checked={selectedExpenses.includes(expense.id)}
                        onChange={(e) => handleCheckboxChange(expense.id, e.target.checked)}
                      />
                    </td>
                  )}
                  <td className="px-4 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{expense.title}</div>
                      {expense.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {expense.description}
                        </div>
                      )}
                      {expense.isRecurring && (
                        <Tag color="blue" className="mt-1 text-xs">
                          <ReloadOutlined className="mr-1" />
                          {expense.recurringFrequency}
                        </Tag>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-lg font-semibold text-green-600">
                      {formatAmount(expense.amount)}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {expense.category ? (
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: expense.category.color }}
                        />
                        <span className="text-sm text-gray-900">{expense.category.name}</span>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">No category</span>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {dayjs(expense.expenseDate).format("MMM DD, YYYY")}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {formatPaymentMethod(expense.paymentMethod)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {expense.vendor || '-'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {onView && (
                        <Tooltip title="View Details">
                          <Button
                            type="text"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => onView(expense)}
                            className="text-green-500"
                          />
                        </Tooltip>
                      )}
                      {canEdit && onEdit && (
                        <Tooltip title="Edit">
                          <Button
                            type="text"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => onEdit(expense)}
                            className="text-blue-500"
                          />
                        </Tooltip>
                      )}
                      {canDelete && onDelete && (
                        <Tooltip title="Delete">
                          <Button
                            type="text"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => onDelete(expense.id)}
                            className="text-red-500"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ExpenseTable;
