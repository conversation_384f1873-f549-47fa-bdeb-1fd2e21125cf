(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1330],{60741:(e,t,s)=>{Promise.resolve().then(s.bind(s,54321))},40794:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var n=s(84021);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},34802:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"}}]},name:"shopping",theme:"outlined"};var n=s(84021);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},55750:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var n=s(84021);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,r.A)({},e,{ref:t,icon:l}))})},54321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>er});var r=s(95155),a=s(12115),l=s(71349),n=s(43316),i=s(72093),c=s(34802),o=s(16419),d=s(36060),u=s(30555),h=s(77418),x=s(9273);let m=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,[l,n]=(0,a.useState)(s),[i,c]=(0,a.useState)(r),[o,d]=(0,a.useState)(""),u=(0,x.d)(o,500);(0,a.useEffect)(()=>{n(1)},[u]);let{data:m,error:p,isLoading:g,refetch:y}=(0,h.hZ)({page:l,limit:i,search:u}),f=(null==m?void 0:null===(e=m.data)||void 0===e?void 0:e.purchases)||[],j=(null==m?void 0:null===(t=m.data)||void 0===t?void 0:t.total)||0;return console.log("Purchases from API:",f),console.log("Total purchases:",j),{purchases:f,total:j,page:l,limit:i,isLoading:g,error:p,refetch:y,searchTerm:o,setSearchTerm:d,handlePageChange:e=>{n(e)},handleLimitChange:e=>{c(e),n(1)}}};var p=s(75912);let g=e=>{let[t,{isLoading:s}]=(0,h.PY)();return{deletePurchase:async s=>{try{let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete purchase");return(0,p.r)("success","Purchase deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete purchase error:",e),(0,p.r)("error",e.message||"Failed to delete purchase"),e}},isDeleting:s}},y=e=>{let[t,{isLoading:s}]=(0,h.oq)();return{bulkDeletePurchases:async s=>{try{console.log("Bulk deleting purchases with IDs:",s);let r=await t(s).unwrap();if(!r.success)throw Error(r.message||"Failed to delete purchases");return(0,p.r)("success","".concat(s.length," purchases deleted successfully")),e&&e(),r.data}catch(e){throw console.error("Bulk delete purchases error:",e),(0,p.r)("error",e.message||"Failed to delete purchases"),e}},isDeleting:s}};var f=s(80766),j=s(92895),v=s(6457),b=s(17084),w=s(55750),A=s(87181),N=s(80519),P=s(86260),C=s(27656),S=s(40794),k=s(60102),D=s(91256),E=s(21455),F=s.n(E),z=s(83391);s(49694);let I=e=>{let{purchases:t,loading:s,onView:l,onEdit:i,onDelete:o,onBulkDelete:d,isMobile:u=!1}=e,h=(0,D.E)(),x=(0,z.d4)(e=>e.auth.user),m=null==x?void 0:x.role,[p,g]=(0,a.useState)([]),[y,E]=(0,a.useState)(!1),I=e=>{let s=e.target.checked;E(s),s?g(t.filter(e=>B(e)).map(e=>e.id)):g([])},T=(e,t)=>{t?g(t=>[...t,e]):g(t=>t.filter(t=>t!==e))},M=e=>F()(e).format("MMM D, YYYY"),R=e=>new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS",minimumFractionDigits:2}).format(Number(e)),B=e=>"admin"===m;return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[p.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[p.length," ",1===p.length?"purchase":"purchases"," selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(b.A,{}),onClick:()=>{p.length>0&&d?(d(p),g([]),E(!1)):f.Ay.warning({message:"No purchases selected",description:"Please select at least one purchase to delete."})},className:"ml-2",children:"Delete Selected"})]}),u||h?(0,r.jsxs)(k.jB,{columns:"50px 200px 120px 120px 120px 150px",minWidth:"800px",children:[(0,r.jsx)(k.A0,{className:"text-center",children:(0,r.jsx)(j.A,{checked:y,onChange:I,disabled:0===t.filter(e=>B(e)).length})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Product"]})}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Supplier"]})}),(0,r.jsx)(k.A0,{children:"Quantity"}),(0,r.jsx)(k.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)(k.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,r.jsxs)(k.Hj,{selected:p.includes(e.id),children:[(0,r.jsx)(k.nA,{className:"text-center",children:B(e)&&(0,r.jsx)(j.A,{checked:p.includes(e.id),onChange:t=>T(e.id,t.target.checked)})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.product||"N/A"})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis text-gray-600",children:e.supplier||"N/A"})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("span",{className:"font-medium",children:e.quantity})}),(0,r.jsx)(k.nA,{children:(0,r.jsx)("span",{className:"text-sm",children:M(e.purchaseDate)})}),(0,r.jsx)(k.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(v.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(N.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),B(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>i(e),type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(v.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(j.A,{checked:y,onChange:I,disabled:0===t.filter(e=>B(e)).length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Product"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"}),"Supplier"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"}),"Quantity"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Cost Price"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"}),"Total Cost"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"mr-1"}),"Date"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("tr",{className:p.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:B(e)&&(0,r.jsx)(j.A,{checked:p.includes(e.id),onChange:t=>T(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.product||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.supplier||"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-500 text-white",children:e.quantity})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:R(e.costPrice)}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:R(e.totalCost)}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:M(e.purchaseDate)}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(v.A,{title:"View",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(N.A,{}),onClick:()=>l(e.id),type:"text",className:"view-button text-green-500",size:"middle"})}),B(e)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(P.A,{}),onClick:()=>i(e),type:"text",className:"edit-button text-blue-500",size:"middle"})}),(0,r.jsx)(v.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(C.A,{}),onClick:()=>o(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})]})})]},e.id))})]})})]})};var T=s(33621),M=s(44549);let R=e=>{let{current:t,pageSize:s,total:a,onChange:l,isMobile:n=!1}=e,i=Math.ceil(a/s);return(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*s+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*s,a)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:a})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(T.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,i)},(e,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>l(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:a},a)}),(0,r.jsxs)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=i?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(M.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>l(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",i]}),(0,r.jsx)("button",{onClick:()=>l(t+1),disabled:t>=i,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=i?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})};var B=s(21614),H=s(83414),O=s(18198),V=s(6564),_=s(93968),L=s(24988);let U=e=>{let[t,{isLoading:s}]=(0,h.o3)();return{createPurchase:async s=>{try{console.log("usePurchaseCreate - Starting purchase creation with data:",s);let r=await t(s).unwrap();if(console.log("usePurchaseCreate - API response:",r),!r.success)throw console.error("usePurchaseCreate - API returned error:",r.message),Error(r.message||"Failed to create purchase");return(0,p.r)("success","Purchase created successfully"),e&&e(),r.data}catch(e){throw console.error("Create purchase error:",e),(0,p.r)("error",e.message||"Failed to create purchase"),e}},isSubmitting:s}},q=e=>{let[t,{isLoading:s}]=(0,h.xc)();return{updatePurchase:async(s,r)=>{try{let a=await t({purchaseId:s,data:r}).unwrap();if(!a.success)throw Error(a.message||"Failed to update purchase");return(0,p.r)("success","Purchase updated successfully"),e&&e(),a.data}catch(e){throw console.error("Update purchase error:",e),(0,p.r)("error",e.message||"Failed to update purchase"),e}},isUpdating:s}};var G=s(85407);let Y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 394c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H400V152c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v166H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v236H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h168v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h228v166c0 4.4 3.6 8 8 8h64c4.4 0 8-3.6 8-8V706h164c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8H708V394h164zM628 630H400V394h228v236z"}}]},name:"number",theme:"outlined"};var W=s(84021),Q=a.forwardRef(function(e,t){return a.createElement(W.A,(0,G.A)({},e,{ref:t,icon:Y}))});let{Option:$}=B.A,J=e=>{let{isOpen:t,onClose:s,onSuccess:l,purchase:i,currentUser:o}=e,[d]=H.A.useForm(),u=!!i,[h,x]=(0,a.useState)("0"),{createPurchase:m,isSubmitting:p}=U(l),{updatePurchase:g,isUpdating:y}=q(l),{data:f,refetch:j}=(0,V.w$)({},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),v=(0,a.useMemo)(()=>{var e;return(null==f?void 0:null===(e=f.data)||void 0===e?void 0:e.suppliers)||[]},[f]),{data:b,refetch:A}=(0,_.r3)({page:1,limit:1e3,search:""},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),N=(0,a.useMemo)(()=>{var e;return(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.products)||[]},[b]),P=()=>{let e=((d.getFieldValue("quantity")||0)*(d.getFieldValue("costPrice")||0)).toFixed(2);x(e),d.setFieldsValue({totalCost:e})};(0,a.useEffect)(()=>{t&&(console.log("\uD83D\uDED2 Purchase panel opened - fetching fresh data"),A(),j(),d.resetFields(),i||x("0.00"))},[d,t,A,j,i]),(0,a.useEffect)(()=>{if(t&&i&&N.length>0&&v.length>0){let e=N.find(e=>e.name===i.product),t=v.find(e=>e.name===i.supplier);d.setFieldsValue({productId:null==e?void 0:e.id,supplierId:null==t?void 0:t.id,quantity:i.quantity,costPrice:i.costPrice,totalCost:i.totalCost}),x(i.totalCost)}},[t,i,N,v,d]);let C=async e=>{try{var t,s;let r={...e,costPrice:(null===(t=e.costPrice)||void 0===t?void 0:t.toString())||"0",totalCost:(null===(s=e.totalCost)||void 0===s?void 0:s.toString())||"0"};console.log("Submitting purchase with formatted values:",r),u&&i?await g(i.id,r):await m(r)}catch(e){console.error("Failed to save purchase:",e)}},k=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,disabled:p||y,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsx)(n.Ay,{type:"primary",loading:p||y,onClick:()=>d.submit(),children:u?"Update":"Save"})]});return(0,r.jsx)(L.A,{isOpen:t,onClose:s,title:u?"Edit Purchase":"Add Purchase",width:"500px",footer:k,children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:u?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Edit Purchase"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Add New Purchase"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:u?"Update purchase information":"Fill in the details to add a new purchase"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(H.A,{form:d,layout:"vertical",onFinish:C,className:"purchase-form",requiredMark:!0,onValuesChange:(e,t)=>{("quantity"in t||"costPrice"in t)&&P()},children:[(0,r.jsx)(H.A.Item,{name:"productId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-1"})," Product"]}),rules:[{required:!0,message:"Please select a product"}],tooltip:"Select the product you are purchasing",children:(0,r.jsx)(B.A,{placeholder:"Select a product",showSearch:!0,optionFilterProp:"children",children:N.map(e=>(0,r.jsx)($,{value:e.id,children:e.name},e.id))})}),(0,r.jsx)(H.A.Item,{name:"supplierId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"mr-1"})," Supplier"]}),tooltip:"Select the supplier (optional)",children:(0,r.jsx)(B.A,{placeholder:"Select a supplier (optional)",allowClear:!0,showSearch:!0,optionFilterProp:"children",children:v.map(e=>(0,r.jsx)($,{value:e.id,children:e.name},e.id))})}),(0,r.jsx)(H.A.Item,{name:"quantity",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(Q,{className:"mr-1"})," Quantity"]}),rules:[{required:!0,message:"Please enter quantity"},{type:"number",min:1,message:"Quantity must be at least 1"}],tooltip:"The quantity of products purchased",children:(0,r.jsx)(O.A,{min:1,style:{width:"100%"},placeholder:"Enter quantity"})}),(0,r.jsx)(H.A.Item,{name:"costPrice",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Cost Price Per Unit (GHS)"]}),rules:[{required:!0,message:"Please enter cost price"},{type:"number",min:.01,message:"Cost price must be greater than 0"}],tooltip:"The cost price per unit",children:(0,r.jsx)(O.A,{min:.01,step:.01,style:{width:"100%"},placeholder:"Enter cost price",formatter:e=>"GHS ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>parseFloat(e.replace(/GHS\s?|(,*)/g,""))})}),(0,r.jsx)(H.A.Item,{name:"totalCost",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(S.A,{className:"mr-1"})," Total Cost (GHS)"]}),tooltip:"The total cost (calculated automatically)",children:(0,r.jsx)(O.A,{disabled:!0,style:{width:"100%"},value:h,formatter:e=>"GHS ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/GHS\s?|(,*)/g,"")})})]})]})})};var Z=s(67649);let K=e=>{let{isOpen:t,onClose:s,purchaseId:a,onEdit:l}=e,d=(0,z.d4)(e=>e.auth.user),u=null==d?void 0:d.role,{data:x,isLoading:m,error:p}=(0,h.Pt)(a||0,{skip:!t||!a}),g=null==x?void 0:x.data,y=!!g;console.log("Purchase detail - User ID:",null==d?void 0:d.id),console.log("Purchase detail - Purchase:",g),console.log("Purchase detail - Can view purchase:",y);let f=e=>e?new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS",minimumFractionDigits:2}).format(Number(e)):"N/A",j="admin"===u&&!!g,v=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:s,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"}),l&&g&&j&&(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>l(g.id),children:"Edit"})]});return(0,r.jsx)(L.A,{isOpen:t,onClose:s,title:"Purchase Details",width:"500px",footer:v,children:m?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):g&&y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(c.A,{className:"mr-2"}),"Purchase Details"]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"Complete purchase information and details"})]}),(0,r.jsxs)(Z.A,{bordered:!0,column:1,className:"purchase-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Purchase ID"]}),children:g.id}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Product"]}),children:g.product}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(w.A,{})," Supplier"]}),children:g.supplier||"N/A"}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(c.A,{})," Quantity"]}),children:g.quantity}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Cost Price"]}),children:f(g.costPrice)}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(S.A,{})," Total Cost"]}),children:f(g.totalCost)}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(A.A,{})," Purchase Date"]}),children:(e=>{if(!e)return"N/A";try{return F()(e).format("MMM D, YYYY")}catch(e){return"Invalid date"}})(g.purchaseDate)}),(0,r.jsx)(Z.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(w.A,{})," Purchased By"]}),children:g.purchasedBy||"N/A"})]})]}):g&&!y?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-red-500",children:'You don"t have permission to view this purchase.'})}):(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)("p",{className:"text-gray-800",children:"No purchase data available"})})})};var X=s(41657),ee=s(5413);s(66202);let et=e=>{let{searchTerm:t,setSearchTerm:s,isMobile:a=!1}=e;return(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(X.A,{placeholder:"Search by product or supplier...",prefix:(0,r.jsx)(ee.A,{className:"text-gray-500"}),value:t,onChange:e=>{let t=e.target.value;console.log("Purchase search input changed:",t),s(t)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:a?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),t&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',t,'"']})]})};var es=s(12467);let er=()=>{let{user:e}=(0,d.A)(),t=(0,u.a)(),[s,h]=(0,a.useState)(!1),[x,p]=(0,a.useState)(!1),[f,j]=(0,a.useState)(!1),[v,b]=(0,a.useState)(null),[w,A]=(0,a.useState)(null),[N,P]=(0,a.useState)(!1),{purchases:C,total:S,page:k,limit:D,isLoading:E,refetch:F,searchTerm:z,setSearchTerm:T,handlePageChange:M}=m();console.log("PurchasesPage - Current user:",e),console.log("PurchasesPage - Purchases:",C),console.log("PurchasesPage - Total:",S);let{deletePurchase:B,isDeleting:H}=g(()=>{P(!1),F()}),{bulkDeletePurchases:O,isDeleting:V}=y(()=>{L(!1),F()}),[_,L]=(0,a.useState)(!1),[U,q]=(0,a.useState)([]),G=(null==e?void 0:e.role)==="admin",Y=async()=>{w&&await B(w)},W=async()=>{if(console.log("confirmBulkDelete called with purchases:",U),U.length>0)try{await O(U)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"p-2 sm:p-4 w-full",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Purchase Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:G&&(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(c.A,{}),onClick:()=>{h(!0)},size:t?"small":"middle",children:t?"":"Add Purchase"}),children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200",children:[(0,r.jsx)(et,{searchTerm:z,setSearchTerm:T,isMobile:t}),E?(0,r.jsx)("div",{className:"flex justify-center items-center h-60 bg-gray-50",children:(0,r.jsx)(i.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[C.length>0?(0,r.jsx)(I,{purchases:C,loading:!1,onView:e=>{A(e),j(!0)},onEdit:e=>{b(e),p(!0)},onDelete:e=>{A(e),P(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with purchaseIds:",e),q(e),L(!0)},isMobile:t}):(0,r.jsx)("div",{className:"flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800",children:z?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No purchases found matching your search criteria."}),(0,r.jsx)(n.Ay,{type:"primary",onClick:()=>T(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]}):(0,r.jsxs)("p",{children:["No purchases found. ",G&&"Click 'Add Purchase' to create one."]})}),C.length>0&&(0,r.jsx)(R,{current:k,pageSize:D,total:S,onChange:M,isMobile:t})]})]})}),(0,r.jsx)(J,{isOpen:s,onClose:()=>h(!1),onSuccess:()=>{h(!1),F()},currentUser:e}),(0,r.jsx)(J,{isOpen:x,onClose:()=>p(!1),onSuccess:()=>{p(!1),F()},purchase:v,currentUser:e}),(0,r.jsx)(K,{isOpen:f,onClose:()=>{j(!1),A(null)},purchaseId:w,onEdit:e=>{let t=C.find(t=>t.id===e)||null;t&&(b(t),j(!1),p(!0))}}),(0,r.jsx)(es.A,{isOpen:N,onClose:()=>{P(!1),A(null)},onConfirm:Y,title:"Delete Purchase",message:"Are you sure you want to delete this purchase? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:H,type:"danger"}),(0,r.jsx)(es.A,{isOpen:_,onClose:()=>{L(!1),q([])},onConfirm:W,title:"Delete Multiple Purchases",message:"Are you sure you want to delete ".concat(U.length," purchases? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:V,type:"danger"})]})}},12467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155);s(12115);var a=s(46102),l=s(43316),n=s(75218);let i=e=>{let{isOpen:t,onClose:s,onConfirm:i,title:c,message:o,confirmText:d="Confirm",cancelText:u="Cancel",isLoading:h=!1,type:x="danger"}=e;return(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{style:{color:"danger"===x?"#ff4d4f":"warning"===x?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:c})]}),open:t,onCancel:s,footer:[(0,r.jsx)(l.Ay,{onClick:s,disabled:h,children:u},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===x?"primary":"default",danger:"danger"===x,onClick:i,loading:h,children:d},"confirm")],maskClosable:!h,closable:!h,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:o})})}},60102:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,Hj:()=>c,jB:()=>l,nA:()=>i});var r=s(95155);s(12115);var a=s(21567);let l=e=>{let{children:t,columns:s,className:l,minWidth:n="800px"}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:s,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],s),children:t})},i=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],s),children:t})},c=e=>{let{children:t,className:s,selected:l=!1,onClick:n}=e;return(0,r.jsx)("div",{className:(0,a.cn)("contents",l&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",s),onClick:n,children:t})}},24988:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155),a=s(12115),l=s(43316),n=s(79624);let i=e=>{let{isOpen:t,onClose:s,title:i,children:c,width:o="400px",footer:d,fullWidth:u=!1}=e,[h,x]=(0,a.useState)(!1),[m,p]=(0,a.useState)(!1),[g,y]=(0,a.useState)(window.innerWidth);if((0,a.useEffect)(()=>{let e=()=>{y(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)p(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{x(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{x(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{p(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!m)return null;let f="Point of Sale"===i||u||"100vw"===o;return(0,r.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(f?"sales-panel-container":""),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(h?"opacity-50":"opacity-0"),onClick:s}),(0,r.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(h?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||u||"100vw"===o||g<640?"100vw":g<1024?"500px":"string"==typeof o&&o.includes("px")&&parseInt(o)>600?"600px":o},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(n.A,{style:{color:"#333"}}),onClick:s,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:c}),d&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:d})]})]})}},30555:(e,t,s)=>{"use strict";s.d(t,{a:()=>a});var r=s(12115);function a(){let[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},36060:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(83391),a=s(70854),l=s(63065),n=s(7875);let i=()=>{let e=(0,r.wA)(),{user:t,accessToken:s}=(0,r.d4)(e=>e.auth),i=(0,a._)(),{refetch:c}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let o=!!t&&!!s,d=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let r=await c();console.log("useAuth - Refetch result:",r);let a=r.data;if((null==a?void 0:a.success)&&(null==a?void 0:a.data)){console.log("useAuth - API response data:",a.data);let r=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=a.data.phone||t.phone||"",i=a.data.createdAt||t.createdAt||"",c=a.data.lastPaymentDate||t.lastPaymentDate||void 0,o=a.data.nextPaymentDue||t.nextPaymentDue||null,d=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:c,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:o,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...a.data,phone:l,createdAt:i,lastPaymentDate:c,nextPaymentDue:o,createdBy:d,paymentStatus:r};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:u,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==a?void 0:a.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:o,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:d}}},70854:(e,t,s)=>{"use strict";s.d(t,{_:()=>i});var r=s(12115),a=s(83391),l=s(21455),n=s.n(l);let i=()=>{let e=(0,a.d4)(e=>e.auth.user),[t,s]=(0,r.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,r.useEffect)(()=>{if(!e){s({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,r=!1,a=!0,l="inactive";if("superadmin"===e.role){s({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){r=!0,a=!1,l="active";let s=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),i=n()();if(t=l.diff(i,"day"),s){let s=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:s,daysRemaining:t,trialDaysUsed:s,trialDaysRemaining:t,isActive:r,needsPayment:a})}}}else"pending"===e.paymentStatus?(r=!1,a=!0,l="pending"):"overdue"===e.paymentStatus?(r=!1,a=!0,l="overdue"):(r=!1,a=!0,l="inactive");s({isActive:r,daysRemaining:t,status:l,needsPayment:a})},[e]),t}},9273:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(12115);function a(e,t){let[s,a]=(0,r.useState)(e);return(0,r.useEffect)(()=>{console.log("Debouncing value:",e);let s=setTimeout(()=>{console.log("Debounce timer completed, setting value:",e),a(e)},t);return()=>{clearTimeout(s)}},[e,t]),s}},91256:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(12115);let a=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(43463),a=s(69795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},75912:(e,t,s)=>{"use strict";s.d(t,{r:()=>a});var r=s(55037);let a=(e,t)=>{"success"===e?r.oR.success(t):"error"===e?r.oR.error(t):"warning"===e&&(0,r.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)},49694:()=>{},66202:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[1559,8059,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,5211,1637,821,8441,1517,7358],()=>t(60741)),_N_E=e.O()}]);