import { useState } from 'react';
import { useDeleteExpenseMutation } from '@/reduxRTK/services/expenseApi';
import { showMessage } from '@/utils/showMessage';

export const useExpenseBulkDelete = () => {
  const [deleteExpense] = useDeleteExpenseMutation();
  const [isLoading, setIsLoading] = useState(false);

  const handleBulkDelete = async (expenseIds: number[]): Promise<boolean> => {
    if (expenseIds.length === 0) {
      showMessage.warning('No expenses selected for deletion');
      return false;
    }

    try {
      setIsLoading(true);
      
      // Delete expenses one by one since backend doesn't have bulk delete
      const deletePromises = expenseIds.map(id => deleteExpense(id).unwrap());
      const results = await Promise.allSettled(deletePromises);
      
      // Count successful and failed deletions
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;
      
      if (successful > 0) {
        if (failed === 0) {
          showMessage.success(`${successful} expense(s) deleted successfully!`);
        } else {
          showMessage.warning(
            `${successful} expense(s) deleted successfully, ${failed} failed to delete`
          );
        }
        return true;
      } else {
        showMessage.error('Failed to delete expenses');
        return false;
      }
    } catch (error: any) {
      console.error('Error in bulk delete:', error);
      showMessage.error('Failed to delete expenses');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    handleBulkDelete,
    isLoading,
  };
};
