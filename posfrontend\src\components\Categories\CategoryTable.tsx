"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Tooltip, Tag, Checkbox, notification } from "antd";
import { EditOutlined, EyeOutlined, DeleteOutlined, DeleteFilled } from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Category } from "@/reduxRTK/services/categoryApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface CategoryTableProps {
  categories: Category[];
  loading: boolean;
  onView: (categoryId: number) => void;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: number) => void;
  onBulkDelete?: (categoryIds: number[]) => void;
  isMobile?: boolean;
}

const CategoryTable: React.FC<CategoryTableProps> = ({
  categories,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected categories
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: any) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all categories that the user can delete
      const selectableCategoryIds = categories
        .filter(category => canEditDelete(category))
        .map(category => category.id);
      setSelectedCategories(selectableCategoryIds);
    } else {
      // Deselect all categories
      setSelectedCategories([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (categoryId: number, checked: boolean) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId]);
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCategories.length > 0 && onBulkDelete) {
      onBulkDelete(selectedCategories);
      setSelectedCategories([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No categories selected',
        description: 'Please select at least one category to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Check if user can edit/delete (superadmin can edit all, others only their own)
  const canEditDelete = (category: Category) => {
    if (userRole === "superadmin") return true;
    if (userRole === "admin" && user?.id === category.createdBy) return true;
    return false;
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when categories are selected */}
      {selectedCategories.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedCategories.length} {selectedCategories.length === 1 ? 'category' : 'categories'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 120px 150px"
          minWidth="700px"
        >
          {/* Mobile Headers */}
          <TableHeader className="text-center">
            <Checkbox
              checked={selectAll}
              onChange={handleSelectAllChange}
              disabled={categories.filter(category => canEditDelete(category)).length === 0}
            />
          </TableHeader>
          <TableHeader>
            Name
          </TableHeader>
          <TableHeader>
            Created At
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {categories.map((category) => (
            <TableRow
              key={category.id}
              selected={selectedCategories.includes(category.id)}
            >
              <TableCell className="text-center">
                {canEditDelete(category) && (
                  <Checkbox
                    checked={selectedCategories.includes(category.id)}
                    onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {category.name}
                </div>
              </TableCell>
              <TableCell>
                <div className="max-w-[110px] overflow-hidden text-ellipsis">
                  {formatDate(category.createdAt)}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(category.id)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  {canEditDelete(category) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(category)}
                          type="text"
                          className="edit-button text-blue-500 hover:text-blue-400"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(category.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={categories.filter(category => canEditDelete(category)).length === 0}
                  />
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Name
                </th>

                {/* Description Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Description
                </th>

                {/* Created At Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Created At
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map((category) => (
                <tr key={category.id} className={selectedCategories.includes(category.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canEditDelete(category) && (
                      <Checkbox
                        checked={selectedCategories.includes(category.id)}
                        onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {category.name}
                    </div>
                  </td>

                  {/* Description Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[200px] overflow-hidden text-ellipsis">
                      {category.description || "No description"}
                    </div>
                  </td>

                  {/* Created At Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[130px] overflow-hidden text-ellipsis">
                      {formatDate(category.createdAt)}
                    </div>
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(category.id)}
                          type="text"
                          className="view-button text-blue-600"
                          size="middle"
                        />
                      </Tooltip>
                      {canEditDelete(category) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(category)}
                              type="text"
                              className="edit-button"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(category.id)}
                              type="text"
                              className="delete-button"
                              danger
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

    </div>
  );
};

export default CategoryTable;
