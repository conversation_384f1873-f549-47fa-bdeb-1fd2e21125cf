"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9907],{79624:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(85407),a=n(12115);let r={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var i=n(84021);let c=a.forwardRef(function(e,t){return a.createElement(i.A,(0,o.A)({},e,{ref:t,icon:r}))})},38536:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(85407),a=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};var i=n(84021);let c=a.forwardRef(function(e,t){return a.createElement(i.A,(0,o.A)({},e,{ref:t,icon:r}))})},96030:(e,t,n)=>{n.d(t,{A:()=>c});var o=n(85407),a=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};var i=n(84021);let c=a.forwardRef(function(e,t){return a.createElement(i.A,(0,o.A)({},e,{ref:t,icon:r}))})},46777:(e,t,n)=>{n.d(t,{YU:()=>l,_j:()=>d,nP:()=>c,ox:()=>r,vR:()=>i});var o=n(67548),a=n(49698);let r=new o.Mo("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),i=new o.Mo("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),c=new o.Mo("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),l=new o.Mo("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),u=new o.Mo("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),s={"slide-up":{inKeyframes:r,outKeyframes:i},"slide-down":{inKeyframes:c,outKeyframes:l},"slide-left":{inKeyframes:u,outKeyframes:new o.Mo("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}})},"slide-right":{inKeyframes:new o.Mo("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),outKeyframes:new o.Mo("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}})}},d=(e,t)=>{let{antCls:n}=e,o="".concat(n,"-").concat(t),{inKeyframes:r,outKeyframes:i}=s[t];return[(0,a.b)(o,r,i,e.motionDurationMid),{["\n      ".concat(o,"-enter,\n      ").concat(o,"-appear\n    ")]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},["".concat(o,"-leave")]:{animationTimingFunction:e.motionEaseInQuint}}]}},99907:(e,t,n)=>{n.d(t,{A:()=>eC});var o=n(12115),a=n(79624),r=n(38536),i=n(96030),c=n(4617),l=n.n(c),u=n(85407),s=n(1568),d=n(85268),f=n(59912),v=n(21855),p=n(64406),m=n(35015),b=n(8324);let h=(0,o.createContext)(null);var g=n(39014),y=n(30377),A=n(97262),k=n(15231),w=n(13379);let C=function(e){var t=e.activeTabOffset,n=e.horizontal,a=e.rtl,r=e.indicator,i=void 0===r?{}:r,c=i.size,l=i.align,u=void 0===l?"center":l,s=(0,o.useState)(),d=(0,f.A)(s,2),v=d[0],p=d[1],m=(0,o.useRef)(),b=o.useCallback(function(e){return"function"==typeof c?c(e):"number"==typeof c?c:e},[c]);function h(){w.A.cancel(m.current)}return(0,o.useEffect)(function(){var e={};if(t){if(n){e.width=b(t.width);var o=a?"right":"left";"start"===u&&(e[o]=t[o]),"center"===u&&(e[o]=t[o]+t.width/2,e.transform=a?"translateX(50%)":"translateX(-50%)"),"end"===u&&(e[o]=t[o]+t.width,e.transform="translateX(-100%)")}else e.height=b(t.height),"start"===u&&(e.top=t.top),"center"===u&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===u&&(e.top=t.top+t.height,e.transform="translateY(-100%)")}return h(),m.current=(0,w.A)(function(){p(e)}),h},[t,n,a,u,b]),{style:v}};var E={width:0,height:0,left:0,top:0};function x(e,t){var n=o.useRef(e),a=o.useState({}),r=(0,f.A)(a,2)[1];return[n.current,function(e){var o="function"==typeof e?e(n.current):e;o!==n.current&&t(o,n.current),n.current=o,r({})}]}var S=n(66105);function R(e){var t=(0,o.useState)(0),n=(0,f.A)(t,2),a=n[0],r=n[1],i=(0,o.useRef)(0),c=(0,o.useRef)();return c.current=e,(0,S.o)(function(){var e;null===(e=c.current)||void 0===e||e.call(c)},[a]),function(){i.current===a&&(i.current+=1,r(i.current))}}var _={width:0,height:0,left:0,top:0,right:0};function M(e){var t;return e instanceof Map?(t={},e.forEach(function(e,n){t[n]=e})):t=e,JSON.stringify(t)}function I(e){return String(e).replace(/"/g,"TABS_DQ")}function P(e,t,n,o){return!!n&&!o&&!1!==e&&(void 0!==e||!1!==t&&null!==t)}var N=o.forwardRef(function(e,t){var n=e.prefixCls,a=e.editable,r=e.locale,i=e.style;return a&&!1!==a.showAdd?o.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){a.onEdit("add",{event:e})}},a.addIcon||"+"):null}),O=o.forwardRef(function(e,t){var n,a=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var c={};return"object"!==(0,v.A)(i)||o.isValidElement(i)?c.right=i:c=i,"right"===a&&(n=c.right),"left"===a&&(n=c.left),n?o.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null}),z=n(41763),T=n(88881),L=n(23672),K=o.forwardRef(function(e,t){var n=e.prefixCls,a=e.id,r=e.tabs,i=e.locale,c=e.mobile,d=e.more,v=void 0===d?{}:d,p=e.style,m=e.className,b=e.editable,h=e.tabBarGutter,g=e.rtl,y=e.removeAriaLabel,A=e.onTabClick,k=e.getPopupContainer,w=e.popupClassName,C=(0,o.useState)(!1),E=(0,f.A)(C,2),x=E[0],S=E[1],R=(0,o.useState)(null),_=(0,f.A)(R,2),M=_[0],I=_[1],O=v.icon,K="".concat(a,"-more-popup"),D="".concat(n,"-dropdown"),B=null!==M?"".concat(K,"-").concat(M):null,j=null==i?void 0:i.dropdownAriaLabel,X=o.createElement(T.Ay,{onClick:function(e){A(e.key,e.domEvent),S(!1)},prefixCls:"".concat(D,"-menu"),id:K,tabIndex:-1,role:"listbox","aria-activedescendant":B,selectedKeys:[M],"aria-label":void 0!==j?j:"expanded dropdown"},r.map(function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,c=e.label,l=P(t,r,b,n);return o.createElement(T.Dr,{key:i,id:"".concat(K,"-").concat(i),role:"option","aria-controls":a&&"".concat(a,"-panel-").concat(i),disabled:n},o.createElement("span",null,c),l&&o.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(D,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),e.preventDefault(),e.stopPropagation(),b.onEdit("remove",{key:i,event:e})}},r||b.removeIcon||"\xd7"))}));function W(e){for(var t=r.filter(function(e){return!e.disabled}),n=t.findIndex(function(e){return e.key===M})||0,o=t.length,a=0;a<o;a+=1){var i=t[n=(n+e+o)%o];if(!i.disabled){I(i.key);return}}}(0,o.useEffect)(function(){var e=document.getElementById(B);e&&e.scrollIntoView&&e.scrollIntoView(!1)},[M]),(0,o.useEffect)(function(){x||I(null)},[x]);var G=(0,s.A)({},g?"marginRight":"marginLeft",h);r.length||(G.visibility="hidden",G.order=1);var F=l()((0,s.A)({},"".concat(D,"-rtl"),g)),H=c?null:o.createElement(z.A,(0,u.A)({prefixCls:D,overlay:X,visible:!!r.length&&x,onVisibleChange:S,overlayClassName:l()(F,w),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:k},v),o.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:G,"aria-haspopup":"listbox","aria-controls":K,id:"".concat(a,"-more"),"aria-expanded":x,onKeyDown:function(e){var t=e.which;if(!x){[L.A.DOWN,L.A.SPACE,L.A.ENTER].includes(t)&&(S(!0),e.preventDefault());return}switch(t){case L.A.UP:W(-1),e.preventDefault();break;case L.A.DOWN:W(1),e.preventDefault();break;case L.A.ESC:S(!1);break;case L.A.SPACE:case L.A.ENTER:null!==M&&A(M,e)}}},void 0===O?"More":O));return o.createElement("div",{className:l()("".concat(n,"-nav-operations"),m),style:p,ref:t},H,o.createElement(N,{prefixCls:n,locale:i,editable:b}))});let D=o.memo(K,function(e,t){return t.tabMoving}),B=function(e){var t=e.prefixCls,n=e.id,a=e.active,r=e.focus,i=e.tab,c=i.key,u=i.label,d=i.disabled,f=i.closeIcon,v=i.icon,p=e.closable,m=e.renderWrapper,b=e.removeAriaLabel,h=e.editable,g=e.onClick,y=e.onFocus,A=e.onBlur,k=e.onKeyDown,w=e.onMouseDown,C=e.onMouseUp,E=e.style,x=e.tabCount,S=e.currentPosition,R="".concat(t,"-tab"),_=P(p,f,h,d);function M(e){d||g(e)}var N=o.useMemo(function(){return v&&"string"==typeof u?o.createElement("span",null,u):u},[u,v]),O=o.useRef(null);o.useEffect(function(){r&&O.current&&O.current.focus()},[r]);var z=o.createElement("div",{key:c,"data-node-key":I(c),className:l()(R,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(R,"-with-remove"),_),"".concat(R,"-active"),a),"".concat(R,"-disabled"),d),"".concat(R,"-focus"),r)),style:E,onClick:M},o.createElement("div",{ref:O,role:"tab","aria-selected":a,id:n&&"".concat(n,"-tab-").concat(c),className:"".concat(R,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(c),"aria-disabled":d,tabIndex:d?null:a?0:-1,onClick:function(e){e.stopPropagation(),M(e)},onKeyDown:k,onMouseDown:w,onMouseUp:C,onFocus:y,onBlur:A},r&&o.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(S," of ").concat(x)),v&&o.createElement("span",{className:"".concat(R,"-icon")},v),u&&N),_&&o.createElement("button",{type:"button",role:"tab","aria-label":b||"remove",tabIndex:a?0:-1,className:"".concat(R,"-remove"),onClick:function(e){e.stopPropagation(),e.preventDefault(),e.stopPropagation(),h.onEdit("remove",{key:c,event:e})}},f||h.removeIcon||"\xd7"));return m?m(z):z};var j=function(e,t){var n=e.offsetWidth,o=e.offsetHeight,a=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),c=i.width,l=i.height,u=i.left,s=i.top;return 1>Math.abs(c-n)?[c,l,u-t.left,s-t.top]:[n,o,r,a]},X=function(e){var t=e.current||{},n=t.offsetWidth,o=void 0===n?0:n,a=t.offsetHeight;if(e.current){var r=e.current.getBoundingClientRect(),i=r.width,c=r.height;if(1>Math.abs(i-o))return[i,c]}return[o,void 0===a?0:a]},W=function(e,t){return e[t?0:1]},G=o.forwardRef(function(e,t){var n,a,r,i,c,v,p,m,b,w,S,z,T,L,K,G,F,H,V,q,Y,U,Q,J,Z,$,ee,et,en,eo,ea,er,ei,ec,el,eu,es,ed,ef,ev=e.className,ep=e.style,em=e.id,eb=e.animated,eh=e.activeKey,eg=e.rtl,ey=e.extra,eA=e.editable,ek=e.locale,ew=e.tabPosition,eC=e.tabBarGutter,eE=e.children,ex=e.onTabClick,eS=e.onTabScroll,eR=e.indicator,e_=o.useContext(h),eM=e_.prefixCls,eI=e_.tabs,eP=(0,o.useRef)(null),eN=(0,o.useRef)(null),eO=(0,o.useRef)(null),ez=(0,o.useRef)(null),eT=(0,o.useRef)(null),eL=(0,o.useRef)(null),eK=(0,o.useRef)(null),eD="top"===ew||"bottom"===ew,eB=x(0,function(e,t){eD&&eS&&eS({direction:e>t?"left":"right"})}),ej=(0,f.A)(eB,2),eX=ej[0],eW=ej[1],eG=x(0,function(e,t){!eD&&eS&&eS({direction:e>t?"top":"bottom"})}),eF=(0,f.A)(eG,2),eH=eF[0],eV=eF[1],eq=(0,o.useState)([0,0]),eY=(0,f.A)(eq,2),eU=eY[0],eQ=eY[1],eJ=(0,o.useState)([0,0]),eZ=(0,f.A)(eJ,2),e$=eZ[0],e0=eZ[1],e1=(0,o.useState)([0,0]),e2=(0,f.A)(e1,2),e6=e2[0],e5=e2[1],e8=(0,o.useState)([0,0]),e7=(0,f.A)(e8,2),e4=e7[0],e9=e7[1],e3=(n=new Map,a=(0,o.useRef)([]),r=(0,o.useState)({}),i=(0,f.A)(r,2)[1],c=(0,o.useRef)("function"==typeof n?n():n),v=R(function(){var e=c.current;a.current.forEach(function(t){e=t(e)}),a.current=[],c.current=e,i({})}),[c.current,function(e){a.current.push(e),v()}]),te=(0,f.A)(e3,2),tt=te[0],tn=te[1],to=(p=e$[0],(0,o.useMemo)(function(){for(var e=new Map,t=tt.get(null===(a=eI[0])||void 0===a?void 0:a.key)||E,n=t.left+t.width,o=0;o<eI.length;o+=1){var a,r,i=eI[o].key,c=tt.get(i);c||(c=tt.get(null===(r=eI[o-1])||void 0===r?void 0:r.key)||E);var l=e.get(i)||(0,d.A)({},c);l.right=n-l.left-l.width,e.set(i,l)}return e},[eI.map(function(e){return e.key}).join("_"),tt,p])),ta=W(eU,eD),tr=W(e$,eD),ti=W(e6,eD),tc=W(e4,eD),tl=Math.floor(ta)<Math.floor(tr+ti),tu=tl?ta-tc:ta-ti,ts="".concat(eM,"-nav-operations-hidden"),td=0,tf=0;function tv(e){return e<td?td:e>tf?tf:e}eD&&eg?(td=0,tf=Math.max(0,tr-tu)):(td=Math.min(0,tu-tr),tf=0);var tp=(0,o.useRef)(null),tm=(0,o.useState)(),tb=(0,f.A)(tm,2),th=tb[0],tg=tb[1];function ty(){tg(Date.now())}function tA(){tp.current&&clearTimeout(tp.current)}m=function(e,t){function n(e,t){e(function(e){return tv(e+t)})}return!!tl&&(eD?n(eW,e):n(eV,t),tA(),ty(),!0)},b=(0,o.useState)(),S=(w=(0,f.A)(b,2))[0],z=w[1],T=(0,o.useState)(0),K=(L=(0,f.A)(T,2))[0],G=L[1],F=(0,o.useState)(0),V=(H=(0,f.A)(F,2))[0],q=H[1],Y=(0,o.useState)(),Q=(U=(0,f.A)(Y,2))[0],J=U[1],Z=(0,o.useRef)(),$=(0,o.useRef)(),(ee=(0,o.useRef)(null)).current={onTouchStart:function(e){var t=e.touches[0];z({x:t.screenX,y:t.screenY}),window.clearInterval(Z.current)},onTouchMove:function(e){if(S){var t=e.touches[0],n=t.screenX,o=t.screenY;z({x:n,y:o});var a=n-S.x,r=o-S.y;m(a,r);var i=Date.now();G(i),q(i-K),J({x:a,y:r})}},onTouchEnd:function(){if(S&&(z(null),J(null),Q)){var e=Q.x/V,t=Q.y/V;if(!(.1>Math.max(Math.abs(e),Math.abs(t)))){var n=e,o=t;Z.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(o)){window.clearInterval(Z.current);return}n*=.9046104802746175,o*=.9046104802746175,m(20*n,20*o)},20)}}},onWheel:function(e){var t=e.deltaX,n=e.deltaY,o=0,a=Math.abs(t),r=Math.abs(n);a===r?o="x"===$.current?t:n:a>r?(o=t,$.current="x"):(o=n,$.current="y"),m(-o,-o)&&e.preventDefault()}},o.useEffect(function(){function e(e){ee.current.onTouchMove(e)}function t(e){ee.current.onTouchEnd(e)}return document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",t,{passive:!0}),ez.current.addEventListener("touchstart",function(e){ee.current.onTouchStart(e)},{passive:!0}),ez.current.addEventListener("wheel",function(e){ee.current.onWheel(e)},{passive:!1}),function(){document.removeEventListener("touchmove",e),document.removeEventListener("touchend",t)}},[]),(0,o.useEffect)(function(){return tA(),th&&(tp.current=setTimeout(function(){tg(0)},100)),tA},[th]);var tk=(et=eD?eX:eH,ei=(en=(0,d.A)((0,d.A)({},e),{},{tabs:eI})).tabs,ec=en.tabPosition,el=en.rtl,["top","bottom"].includes(ec)?(eo="width",ea=el?"right":"left",er=Math.abs(et)):(eo="height",ea="top",er=-et),(0,o.useMemo)(function(){if(!ei.length)return[0,0];for(var e=ei.length,t=e,n=0;n<e;n+=1){var o=to.get(ei[n].key)||_;if(Math.floor(o[ea]+o[eo])>Math.floor(er+tu)){t=n-1;break}}for(var a=0,r=e-1;r>=0;r-=1)if((to.get(ei[r].key)||_)[ea]<er){a=r+1;break}return a>=t?[0,0]:[a,t]},[to,tu,tr,ti,tc,er,ec,ei.map(function(e){return e.key}).join("_"),el])),tw=(0,f.A)(tk,2),tC=tw[0],tE=tw[1],tx=(0,A.A)(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eh,t=to.get(e)||{width:0,height:0,left:0,right:0,top:0};if(eD){var n=eX;eg?t.right<eX?n=t.right:t.right+t.width>eX+tu&&(n=t.right+t.width-tu):t.left<-eX?n=-t.left:t.left+t.width>-eX+tu&&(n=-(t.left+t.width-tu)),eV(0),eW(tv(n))}else{var o=eH;t.top<-eH?o=-t.top:t.top+t.height>-eH+tu&&(o=-(t.top+t.height-tu)),eW(0),eV(tv(o))}}),tS=(0,o.useState)(),tR=(0,f.A)(tS,2),t_=tR[0],tM=tR[1],tI=(0,o.useState)(!1),tP=(0,f.A)(tI,2),tN=tP[0],tO=tP[1],tz=eI.filter(function(e){return!e.disabled}).map(function(e){return e.key}),tT=function(e){var t=tz.indexOf(t_||eh),n=tz.length;tM(tz[(t+e+n)%n])},tL=function(e){var t=e.code,n=eg&&eD,o=tz[0],a=tz[tz.length-1];switch(t){case"ArrowLeft":eD&&tT(n?1:-1);break;case"ArrowRight":eD&&tT(n?-1:1);break;case"ArrowUp":e.preventDefault(),eD||tT(-1);break;case"ArrowDown":e.preventDefault(),eD||tT(1);break;case"Home":e.preventDefault(),tM(o);break;case"End":e.preventDefault(),tM(a);break;case"Enter":case"Space":e.preventDefault(),ex(eh,e);break;case"Backspace":case"Delete":var r=tz.indexOf(t_),i=eI.find(function(e){return e.key===t_});P(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,eA,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),eA.onEdit("remove",{key:t_,event:e}),r===tz.length-1?tT(-1):tT(1))}},tK={};eD?tK[eg?"marginRight":"marginLeft"]=eC:tK.marginTop=eC;var tD=eI.map(function(e,t){var n=e.key;return o.createElement(B,{id:em,prefixCls:eM,key:n,tab:e,style:0===t?void 0:tK,closable:e.closable,editable:eA,active:n===eh,focus:n===t_,renderWrapper:eE,removeAriaLabel:null==ek?void 0:ek.removeAriaLabel,tabCount:tz.length,currentPosition:t+1,onClick:function(e){ex(n,e)},onKeyDown:tL,onFocus:function(){tN||tM(n),tx(n),ty(),ez.current&&(eg||(ez.current.scrollLeft=0),ez.current.scrollTop=0)},onBlur:function(){tM(void 0)},onMouseDown:function(){tO(!0)},onMouseUp:function(){tO(!1)}})}),tB=function(){return tn(function(){var e,t=new Map,n=null===(e=eT.current)||void 0===e?void 0:e.getBoundingClientRect();return eI.forEach(function(e){var o,a=e.key,r=null===(o=eT.current)||void 0===o?void 0:o.querySelector('[data-node-key="'.concat(I(a),'"]'));if(r){var i=j(r,n),c=(0,f.A)(i,4),l=c[0],u=c[1],s=c[2],d=c[3];t.set(a,{width:l,height:u,left:s,top:d})}}),t})};(0,o.useEffect)(function(){tB()},[eI.map(function(e){return e.key}).join("_")]);var tj=R(function(){var e=X(eP),t=X(eN),n=X(eO);eQ([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var o=X(eK);e5(o),e9(X(eL));var a=X(eT);e0([a[0]-o[0],a[1]-o[1]]),tB()}),tX=eI.slice(0,tC),tW=eI.slice(tE+1),tG=[].concat((0,g.A)(tX),(0,g.A)(tW)),tF=to.get(eh),tH=C({activeTabOffset:tF,horizontal:eD,indicator:eR,rtl:eg}).style;(0,o.useEffect)(function(){tx()},[eh,td,tf,M(tF),M(to),eD]),(0,o.useEffect)(function(){tj()},[eg]);var tV=!!tG.length,tq="".concat(eM,"-nav-wrap");return eD?eg?(es=eX>0,eu=eX!==tf):(eu=eX<0,es=eX!==td):(ed=eH<0,ef=eH!==td),o.createElement(y.A,{onResize:tj},o.createElement("div",{ref:(0,k.xK)(t,eP),role:"tablist","aria-orientation":eD?"horizontal":"vertical",className:l()("".concat(eM,"-nav"),ev),style:ep,onKeyDown:function(){ty()}},o.createElement(O,{ref:eN,position:"left",extra:ey,prefixCls:eM}),o.createElement(y.A,{onResize:tj},o.createElement("div",{className:l()(tq,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(tq,"-ping-left"),eu),"".concat(tq,"-ping-right"),es),"".concat(tq,"-ping-top"),ed),"".concat(tq,"-ping-bottom"),ef)),ref:ez},o.createElement(y.A,{onResize:tj},o.createElement("div",{ref:eT,className:"".concat(eM,"-nav-list"),style:{transform:"translate(".concat(eX,"px, ").concat(eH,"px)"),transition:th?"none":void 0}},tD,o.createElement(N,{ref:eK,prefixCls:eM,locale:ek,editable:eA,style:(0,d.A)((0,d.A)({},0===tD.length?void 0:tK),{},{visibility:tV?"hidden":null})}),o.createElement("div",{className:l()("".concat(eM,"-ink-bar"),(0,s.A)({},"".concat(eM,"-ink-bar-animated"),eb.inkBar)),style:tH}))))),o.createElement(D,(0,u.A)({},e,{removeAriaLabel:null==ek?void 0:ek.removeAriaLabel,ref:eL,prefixCls:eM,tabs:tG,className:!tV&&ts,tabMoving:!!th})),o.createElement(O,{ref:eO,position:"right",extra:ey,prefixCls:eM})))}),F=o.forwardRef(function(e,t){var n=e.prefixCls,a=e.className,r=e.style,i=e.id,c=e.active,u=e.tabKey,s=e.children;return o.createElement("div",{id:i&&"".concat(i,"-panel-").concat(u),role:"tabpanel",tabIndex:c?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(u),"aria-hidden":!c,style:r,className:l()(n,c&&"".concat(n,"-active"),a),ref:t},s)}),H=["renderTabBar"],V=["label","key"];let q=function(e){var t=e.renderTabBar,n=(0,p.A)(e,H),a=o.useContext(h).tabs;return t?t((0,d.A)((0,d.A)({},n),{},{panes:a.map(function(e){var t=e.label,n=e.key,a=(0,p.A)(e,V);return o.createElement(F,(0,u.A)({tab:t,key:n,tabKey:n},a))})}),G):o.createElement(G,n)};var Y=n(72261),U=["key","forceRender","style","className","destroyInactiveTabPane"];let Q=function(e){var t=e.id,n=e.activeKey,a=e.animated,r=e.tabPosition,i=e.destroyInactiveTabPane,c=o.useContext(h),f=c.prefixCls,v=c.tabs,m=a.tabPane,b="".concat(f,"-tabpane");return o.createElement("div",{className:l()("".concat(f,"-content-holder"))},o.createElement("div",{className:l()("".concat(f,"-content"),"".concat(f,"-content-").concat(r),(0,s.A)({},"".concat(f,"-content-animated"),m))},v.map(function(e){var r=e.key,c=e.forceRender,s=e.style,f=e.className,v=e.destroyInactiveTabPane,h=(0,p.A)(e,U),g=r===n;return o.createElement(Y.Ay,(0,u.A)({key:r,visible:g,forceRender:c,removeOnLeave:!!(i||v),leavedClassName:"".concat(b,"-hidden")},a.tabPaneMotion),function(e,n){var a=e.style,i=e.className;return o.createElement(F,(0,u.A)({},h,{prefixCls:b,id:t,tabKey:r,animated:m,active:g,style:(0,d.A)((0,d.A)({},s),a),className:l()(f,i),ref:n}))})})))};n(30754);var J=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],Z=0,$=o.forwardRef(function(e,t){var n=e.id,a=e.prefixCls,r=void 0===a?"rc-tabs":a,i=e.className,c=e.items,g=e.direction,y=e.activeKey,A=e.defaultActiveKey,k=e.editable,w=e.animated,C=e.tabPosition,E=void 0===C?"top":C,x=e.tabBarGutter,S=e.tabBarStyle,R=e.tabBarExtraContent,_=e.locale,M=e.more,I=e.destroyInactiveTabPane,P=e.renderTabBar,N=e.onChange,O=e.onTabClick,z=e.onTabScroll,T=e.getPopupContainer,L=e.popupClassName,K=e.indicator,D=(0,p.A)(e,J),B=o.useMemo(function(){return(c||[]).filter(function(e){return e&&"object"===(0,v.A)(e)&&"key"in e})},[c]),j="rtl"===g,X=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,d.A)({inkBar:!0},"object"===(0,v.A)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(w),W=(0,o.useState)(!1),G=(0,f.A)(W,2),F=G[0],H=G[1];(0,o.useEffect)(function(){H((0,b.A)())},[]);var V=(0,m.A)(function(){var e;return null===(e=B[0])||void 0===e?void 0:e.key},{value:y,defaultValue:A}),Y=(0,f.A)(V,2),U=Y[0],$=Y[1],ee=(0,o.useState)(function(){return B.findIndex(function(e){return e.key===U})}),et=(0,f.A)(ee,2),en=et[0],eo=et[1];(0,o.useEffect)(function(){var e,t=B.findIndex(function(e){return e.key===U});-1===t&&(t=Math.max(0,Math.min(en,B.length-1)),$(null===(e=B[t])||void 0===e?void 0:e.key)),eo(t)},[B.map(function(e){return e.key}).join("_"),U,en]);var ea=(0,m.A)(null,{value:n}),er=(0,f.A)(ea,2),ei=er[0],ec=er[1];(0,o.useEffect)(function(){n||(ec("rc-tabs-".concat(Z)),Z+=1)},[]);var el={id:ei,activeKey:U,animated:X,tabPosition:E,rtl:j,mobile:F},eu=(0,d.A)((0,d.A)({},el),{},{editable:k,locale:_,more:M,tabBarGutter:x,onTabClick:function(e,t){null==O||O(e,t);var n=e!==U;$(e),n&&(null==N||N(e))},onTabScroll:z,extra:R,style:S,panes:null,getPopupContainer:T,popupClassName:L,indicator:K});return o.createElement(h.Provider,{value:{tabs:B,prefixCls:r}},o.createElement("div",(0,u.A)({ref:t,id:n,className:l()(r,"".concat(r,"-").concat(E),(0,s.A)((0,s.A)((0,s.A)({},"".concat(r,"-mobile"),F),"".concat(r,"-editable"),k),"".concat(r,"-rtl"),j),i)},D),o.createElement(q,(0,u.A)({},eu,{renderTabBar:P})),o.createElement(Q,(0,u.A)({destroyInactiveTabPane:I},el,{animated:X}))))}),ee=n(31049),et=n(7926),en=n(27651),eo=n(19635);let ea={motionAppear:!1,motionEnter:!0,motionLeave:!0};var er=n(63588),ei=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n},ec=n(67548),el=n(70695),eu=n(1086),es=n(56204),ed=n(46777);let ef=e=>{let{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{["".concat(t,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,ed._j)(e,"slide-up"),(0,ed._j)(e,"slide-down")]]},ev=e=>{let{componentCls:t,tabsCardPadding:n,cardBg:o,cardGutter:a,colorBorderSecondary:r,itemSelectedColor:i}=e;return{["".concat(t,"-card")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab")]:{margin:0,padding:n,background:o,border:"".concat((0,ec.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(r),transition:"all ".concat(e.motionDurationSlow," ").concat(e.motionEaseInOut)},["".concat(t,"-tab-active")]:{color:i,background:e.colorBgContainer},["".concat(t,"-tab-focus")]:Object.assign({},(0,el.jk)(e,-3)),["".concat(t,"-ink-bar")]:{visibility:"hidden"},["& ".concat(t,"-tab").concat(t,"-tab-focus ").concat(t,"-tab-btn")]:{outline:"none"}},["&".concat(t,"-top, &").concat(t,"-bottom")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab + ").concat(t,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ec.zA)(a)}}}},["&".concat(t,"-top")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab")]:{borderRadius:"".concat((0,ec.zA)(e.borderRadiusLG)," ").concat((0,ec.zA)(e.borderRadiusLG)," 0 0")},["".concat(t,"-tab-active")]:{borderBottomColor:e.colorBgContainer}}},["&".concat(t,"-bottom")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab")]:{borderRadius:"0 0 ".concat((0,ec.zA)(e.borderRadiusLG)," ").concat((0,ec.zA)(e.borderRadiusLG))},["".concat(t,"-tab-active")]:{borderTopColor:e.colorBgContainer}}},["&".concat(t,"-left, &").concat(t,"-right")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab + ").concat(t,"-tab")]:{marginTop:(0,ec.zA)(a)}}},["&".concat(t,"-left")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ec.zA)(e.borderRadiusLG)," 0 0 ").concat((0,ec.zA)(e.borderRadiusLG))}},["".concat(t,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},["&".concat(t,"-right")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ec.zA)(e.borderRadiusLG)," ").concat((0,ec.zA)(e.borderRadiusLG)," 0")}},["".concat(t,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ep=e=>{let{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:o}=e;return{["".concat(t,"-dropdown")]:Object.assign(Object.assign({},(0,el.dF)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(t,"-dropdown-menu")]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:"".concat((0,ec.zA)(o)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},el.L9),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:"".concat((0,ec.zA)(e.paddingXXS)," ").concat((0,ec.zA)(e.paddingSM)),color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:"all ".concat(e.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},em=e=>{let{componentCls:t,margin:n,colorBorderSecondary:o,horizontalMargin:a,verticalItemPadding:r,verticalItemMargin:i,calc:c}=e;return{["".concat(t,"-top, ").concat(t,"-bottom")]:{flexDirection:"column",["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{margin:a,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ec.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(o),content:"''"},["".concat(t,"-ink-bar")]:{height:e.lineWidthBold,"&-animated":{transition:"width ".concat(e.motionDurationSlow,", left ").concat(e.motionDurationSlow,",\n            right ").concat(e.motionDurationSlow)}},["".concat(t,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},["&".concat(t,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(t,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(t,"-top")]:{["> ".concat(t,"-nav,\n        > div > ").concat(t,"-nav")]:{"&::before":{bottom:0},["".concat(t,"-ink-bar")]:{bottom:0}}},["".concat(t,"-bottom")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(t,"-ink-bar")]:{top:0}},["> ".concat(t,"-content-holder, > div > ").concat(t,"-content-holder")]:{order:0}},["".concat(t,"-left, ").concat(t,"-right")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{flexDirection:"column",minWidth:c(e.controlHeight).mul(1.25).equal(),["".concat(t,"-tab")]:{padding:r,textAlign:"center"},["".concat(t,"-tab + ").concat(t,"-tab")]:{margin:i},["".concat(t,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},["&".concat(t,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(t,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(t,"-ink-bar")]:{width:e.lineWidthBold,"&-animated":{transition:"height ".concat(e.motionDurationSlow,", top ").concat(e.motionDurationSlow)}},["".concat(t,"-nav-list, ").concat(t,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(t,"-left")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(t,"-content-holder, > div > ").concat(t,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ec.zA)(c(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ec.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder)},["> ".concat(t,"-content > ").concat(t,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},["".concat(t,"-right")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{order:1,["".concat(t,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(t,"-content-holder, > div > ").concat(t,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:c(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ec.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder)},["> ".concat(t,"-content > ").concat(t,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},eb=e=>{let{componentCls:t,cardPaddingSM:n,cardPaddingLG:o,horizontalItemPaddingSM:a,horizontalItemPaddingLG:r}=e;return{[t]:{"&-small":{["> ".concat(t,"-nav")]:{["".concat(t,"-tab")]:{padding:a,fontSize:e.titleFontSizeSM}}},"&-large":{["> ".concat(t,"-nav")]:{["".concat(t,"-tab")]:{padding:r,fontSize:e.titleFontSizeLG}}}},["".concat(t,"-card")]:{["&".concat(t,"-small")]:{["> ".concat(t,"-nav")]:{["".concat(t,"-tab")]:{padding:n}},["&".concat(t,"-bottom")]:{["> ".concat(t,"-nav ").concat(t,"-tab")]:{borderRadius:"0 0 ".concat((0,ec.zA)(e.borderRadius)," ").concat((0,ec.zA)(e.borderRadius))}},["&".concat(t,"-top")]:{["> ".concat(t,"-nav ").concat(t,"-tab")]:{borderRadius:"".concat((0,ec.zA)(e.borderRadius)," ").concat((0,ec.zA)(e.borderRadius)," 0 0")}},["&".concat(t,"-right")]:{["> ".concat(t,"-nav ").concat(t,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ec.zA)(e.borderRadius)," ").concat((0,ec.zA)(e.borderRadius)," 0")}}},["&".concat(t,"-left")]:{["> ".concat(t,"-nav ").concat(t,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ec.zA)(e.borderRadius)," 0 0 ").concat((0,ec.zA)(e.borderRadius))}}}},["&".concat(t,"-large")]:{["> ".concat(t,"-nav")]:{["".concat(t,"-tab")]:{padding:o}}}}}},eh=e=>{let{componentCls:t,itemActiveColor:n,itemHoverColor:o,iconCls:a,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:c,itemColor:l}=e,u="".concat(t,"-tab");return{[u]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(e.motionDurationSlow),["".concat(u,"-icon:not(:last-child)")]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(e.motionDurationSlow),"&:hover":{color:e.colorTextHeading}},(0,el.K8)(e)),"&:hover":{color:o},["&".concat(u,"-active ").concat(u,"-btn")]:{color:c,textShadow:e.tabsActiveTextShadow},["&".concat(u,"-focus ").concat(u,"-btn")]:Object.assign({},(0,el.jk)(e)),["&".concat(u,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed"},["&".concat(u,"-disabled ").concat(u,"-btn, &").concat(u,"-disabled ").concat(t,"-remove")]:{"&:focus, &:active":{color:e.colorTextDisabled}},["& ".concat(u,"-remove ").concat(a)]:{margin:0},["".concat(a,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},["".concat(u," + ").concat(u)]:{margin:{_skip_check_:!0,value:r}}}},eg=e=>{let{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:o,cardGutter:a,calc:r}=e;return{["".concat(t,"-rtl")]:{direction:"rtl",["".concat(t,"-nav")]:{["".concat(t,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(t,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[o]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ec.zA)(e.marginSM)}},["".concat(t,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ec.zA)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ec.zA)(r(e.marginXXS).mul(-1).equal())},[o]:{margin:0}}}},["&".concat(t,"-left")]:{["> ".concat(t,"-nav")]:{order:1},["> ".concat(t,"-content-holder")]:{order:0}},["&".concat(t,"-right")]:{["> ".concat(t,"-nav")]:{order:0},["> ".concat(t,"-content-holder")]:{order:1}},["&".concat(t,"-card").concat(t,"-top, &").concat(t,"-card").concat(t,"-bottom")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-tab + ").concat(t,"-tab")]:{marginRight:{_skip_check_:!0,value:a},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(t,"-dropdown-rtl")]:{direction:"rtl"},["".concat(t,"-menu-item")]:{["".concat(t,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ey=e=>{let{componentCls:t,tabsCardPadding:n,cardHeight:o,cardGutter:a,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:c}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,el.dF)(e)),{display:"flex",["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(t,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(e.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(t,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(e.motionDurationSlow)},["".concat(t,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(t,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(t,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(t,"-nav-add")]:Object.assign({minWidth:o,marginLeft:{_skip_check_:!0,value:a},padding:(0,ec.zA)(e.paddingXS),background:"transparent",border:"".concat((0,ec.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(c),borderRadius:"".concat((0,ec.zA)(e.borderRadiusLG)," ").concat((0,ec.zA)(e.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:e.colorText,transition:"all ".concat(e.motionDurationSlow," ").concat(e.motionEaseInOut),"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,el.K8)(e,-3))},["".concat(t,"-extra-content")]:{flex:"none"},["".concat(t,"-ink-bar")]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),eh(e)),{["".concat(t,"-content")]:{position:"relative",width:"100%"},["".concat(t,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(t,"-tabpane")]:Object.assign(Object.assign({},(0,el.K8)(e)),{"&-hidden":{display:"none"}})}),["".concat(t,"-centered")]:{["> ".concat(t,"-nav, > div > ").concat(t,"-nav")]:{["".concat(t,"-nav-wrap")]:{["&:not([class*='".concat(t,"-nav-wrap-ping']) > ").concat(t,"-nav-list")]:{margin:"auto"}}}}}},eA=(0,eu.OF)("Tabs",e=>{let t=(0,es.oX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ec.zA)(e.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ec.zA)(e.horizontalItemGutter))});return[eb(t),eg(t),em(t),ep(t),ev(t),ey(t),ef(t)]},e=>{let t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:"".concat((t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth,"px ").concat(e.padding,"px"),cardPaddingSM:"".concat(1.5*e.paddingXXS,"px ").concat(e.padding,"px"),cardPaddingLG:"".concat(e.paddingXS,"px ").concat(e.padding,"px ").concat(1.5*e.paddingXXS,"px"),titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:"0 0 ".concat(e.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(e.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(e.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(e.padding,"px 0"),verticalItemPadding:"".concat(e.paddingXS,"px ").concat(e.paddingLG,"px"),verticalItemMargin:"".concat(e.margin,"px 0 0 0"),itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}});var ek=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let ew=e=>{var t,n,c,u,s,d,f,v,p,m,b;let h;let{type:g,className:y,rootClassName:A,size:k,onEdit:w,hideAdd:C,centered:E,addIcon:x,removeIcon:S,moreIcon:R,more:_,popupClassName:M,children:I,items:P,animated:N,style:O,indicatorSize:z,indicator:T}=e,L=ek(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:K}=L,{direction:D,tabs:B,getPrefixCls:j,getPopupContainer:X}=o.useContext(ee.QO),W=j("tabs",K),G=(0,et.A)(W),[F,H,V]=eA(W,G);"editable-card"===g&&(h={onEdit:(e,t)=>{let{key:n,event:o}=t;null==w||w("add"===e?o:n,e)},removeIcon:null!==(t=null!=S?S:null==B?void 0:B.removeIcon)&&void 0!==t?t:o.createElement(a.A,null),addIcon:(null!=x?x:null==B?void 0:B.addIcon)||o.createElement(i.A,null),showAdd:!0!==C});let q=j(),Y=(0,en.A)(k),U=function(e,t){return e||(0,er.A)(t).map(e=>{if(o.isValidElement(e)){let{key:t,props:n}=e,o=n||{},{tab:a}=o,r=ei(o,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:a})}return null}).filter(e=>e)}(P,I),Q=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(t.tabPaneMotion=Object.assign(Object.assign({},ea),{motionName:(0,eo.b)(e,"switch")})),t}(W,N),J=Object.assign(Object.assign({},null==B?void 0:B.style),O),Z={align:null!==(n=null==T?void 0:T.align)&&void 0!==n?n:null===(c=null==B?void 0:B.indicator)||void 0===c?void 0:c.align,size:null!==(f=null!==(s=null!==(u=null==T?void 0:T.size)&&void 0!==u?u:z)&&void 0!==s?s:null===(d=null==B?void 0:B.indicator)||void 0===d?void 0:d.size)&&void 0!==f?f:null==B?void 0:B.indicatorSize};return F(o.createElement($,Object.assign({direction:D,getPopupContainer:X},L,{items:U,className:l()({["".concat(W,"-").concat(Y)]:Y,["".concat(W,"-card")]:["card","editable-card"].includes(g),["".concat(W,"-editable-card")]:"editable-card"===g,["".concat(W,"-centered")]:E},null==B?void 0:B.className,y,A,H,V,G),popupClassName:l()(M,H,V,G),style:J,editable:h,more:Object.assign({icon:null!==(b=null!==(m=null!==(p=null===(v=null==B?void 0:B.more)||void 0===v?void 0:v.icon)&&void 0!==p?p:null==B?void 0:B.moreIcon)&&void 0!==m?m:R)&&void 0!==b?b:o.createElement(r.A,null),transitionName:"".concat(q,"-slide-up")},_),prefixCls:W,animated:Q,indicator:Z})))};ew.TabPane=()=>null;let eC=ew},41763:(e,t,n)=>{n.d(t,{A:()=>k});var o=n(85407),a=n(1568),r=n(59912),i=n(64406),c=n(99121),l=n(4617),u=n.n(l),s=n(15231),d=n(12115),f=n(23672),v=n(13379),p=f.A.ESC,m=f.A.TAB,b=(0,d.forwardRef)(function(e,t){var n=e.overlay,o=e.arrow,a=e.prefixCls,r=(0,d.useMemo)(function(){return"function"==typeof n?n():n},[n]),i=(0,s.K4)(t,(0,s.A9)(r));return d.createElement(d.Fragment,null,o&&d.createElement("div",{className:"".concat(a,"-arrow")}),d.cloneElement(r,{ref:(0,s.f3)(r)?i:void 0}))}),h={adjustX:1,adjustY:1},g=[0,0];let y={topLeft:{points:["bl","tl"],overflow:h,offset:[0,-4],targetOffset:g},top:{points:["bc","tc"],overflow:h,offset:[0,-4],targetOffset:g},topRight:{points:["br","tr"],overflow:h,offset:[0,-4],targetOffset:g},bottomLeft:{points:["tl","bl"],overflow:h,offset:[0,4],targetOffset:g},bottom:{points:["tc","bc"],overflow:h,offset:[0,4],targetOffset:g},bottomRight:{points:["tr","br"],overflow:h,offset:[0,4],targetOffset:g}};var A=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];let k=d.forwardRef(function(e,t){var n,l,f,h,g,k,w,C,E,x,S,R,_,M,I=e.arrow,P=void 0!==I&&I,N=e.prefixCls,O=void 0===N?"rc-dropdown":N,z=e.transitionName,T=e.animation,L=e.align,K=e.placement,D=e.placements,B=e.getPopupContainer,j=e.showAction,X=e.hideAction,W=e.overlayClassName,G=e.overlayStyle,F=e.visible,H=e.trigger,V=void 0===H?["hover"]:H,q=e.autoFocus,Y=e.overlay,U=e.children,Q=e.onVisibleChange,J=(0,i.A)(e,A),Z=d.useState(),$=(0,r.A)(Z,2),ee=$[0],et=$[1],en="visible"in e?F:ee,eo=d.useRef(null),ea=d.useRef(null),er=d.useRef(null);d.useImperativeHandle(t,function(){return eo.current});var ei=function(e){et(e),null==Q||Q(e)};l=(n={visible:en,triggerRef:er,onVisibleChange:ei,autoFocus:q,overlayRef:ea}).visible,f=n.triggerRef,h=n.onVisibleChange,g=n.autoFocus,k=n.overlayRef,w=d.useRef(!1),C=function(){if(l){var e,t;null===(e=f.current)||void 0===e||null===(t=e.focus)||void 0===t||t.call(e),null==h||h(!1)}},E=function(){var e;return null!==(e=k.current)&&void 0!==e&&!!e.focus&&(k.current.focus(),w.current=!0,!0)},x=function(e){switch(e.keyCode){case p:C();break;case m:var t=!1;w.current||(t=E()),t?e.preventDefault():C()}},d.useEffect(function(){return l?(window.addEventListener("keydown",x),g&&(0,v.A)(E,3),function(){window.removeEventListener("keydown",x),w.current=!1}):function(){w.current=!1}},[l]);var ec=function(){return d.createElement(b,{ref:ea,overlay:Y,prefixCls:O,arrow:P})},el=d.cloneElement(U,{className:u()(null===(M=U.props)||void 0===M?void 0:M.className,en&&(void 0!==(S=e.openClassName)?S:"".concat(O,"-open"))),ref:(0,s.f3)(U)?(0,s.K4)(er,(0,s.A9)(U)):void 0}),eu=X;return eu||-1===V.indexOf("contextMenu")||(eu=["click"]),d.createElement(c.A,(0,o.A)({builtinPlacements:void 0===D?y:D},J,{prefixCls:O,ref:eo,popupClassName:u()(W,(0,a.A)({},"".concat(O,"-show-arrow"),P)),popupStyle:G,action:V,showAction:j,hideAction:eu,popupPlacement:void 0===K?"bottomLeft":K,popupAlign:L,popupTransitionName:z,popupAnimation:T,popupVisible:en,stretch:(R=e.minOverlayWidthMatchTrigger,_=e.alignPoint,"minOverlayWidthMatchTrigger"in e?R:!_)?"minWidth":"",popup:"function"==typeof Y?ec:ec(),onPopupVisibleChange:ei,onPopupClick:function(t){var n=e.onOverlayClick;et(!1),n&&n(t)},getPopupContainer:B}),el)})},88881:(e,t,n)=>{n.d(t,{cG:()=>eO,q7:()=>ep,te:()=>eL,Dr:()=>ep,g8:()=>eP,Ay:()=>eW,Wj:()=>R});var o=n(85407),a=n(1568),r=n(85268),i=n(39014),c=n(59912),l=n(64406),u=n(4617),s=n.n(u),d=n(89585),f=n(35015),v=n(85646),p=n(30754),m=n(12115),b=n(47650),h=m.createContext(null);function g(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function y(e){return g(m.useContext(h),e)}var A=n(58676),k=["children","locked"],w=m.createContext(null);function C(e){var t=e.children,n=e.locked,o=(0,l.A)(e,k),a=m.useContext(w),i=(0,A.A)(function(){var e;return e=(0,r.A)({},a),Object.keys(o).forEach(function(t){var n=o[t];void 0!==n&&(e[t]=n)}),e},[a,o],function(e,t){return!n&&(e[0]!==t[0]||!(0,v.A)(e[1],t[1],!0))});return m.createElement(w.Provider,{value:i},t)}var E=m.createContext(null);function x(){return m.useContext(E)}var S=m.createContext([]);function R(e){var t=m.useContext(S);return m.useMemo(function(){return void 0!==e?[].concat((0,i.A)(t),[e]):t},[t,e])}var _=m.createContext(null),M=m.createContext({}),I=n(87543);function P(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,I.A)(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||"a"===n&&!!e.getAttribute("href"),a=e.getAttribute("tabindex"),r=Number(a),i=null;return a&&!Number.isNaN(r)?i=r:o&&null===i&&(i=0),o&&e.disabled&&(i=null),null!==i&&(i>=0||t&&i<0)}return!1}var N=n(23672),O=n(13379),z=N.A.LEFT,T=N.A.RIGHT,L=N.A.UP,K=N.A.DOWN,D=N.A.ENTER,B=N.A.ESC,j=N.A.HOME,X=N.A.END,W=[L,K,z,T];function G(e,t){return(function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(0,i.A)(e.querySelectorAll("*")).filter(function(e){return P(e,t)});return P(e,t)&&n.unshift(e),n})(e,!0).filter(function(e){return t.has(e)})}function F(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var a=G(e,t),r=a.length,i=a.findIndex(function(e){return n===e});return o<0?-1===i?i=r-1:i-=1:o>0&&(i+=1),a[i=(i+r)%r]}var H=function(e,t){var n=new Set,o=new Map,a=new Map;return e.forEach(function(e){var r=document.querySelector("[data-menu-id='".concat(g(t,e),"']"));r&&(n.add(r),a.set(r,e),o.set(e,r))}),{elements:n,key2element:o,element2key:a}},V="__RC_UTIL_PATH_SPLIT__",q=function(e){return e.join(V)},Y="rc-menu-more";function U(e){var t=m.useRef(e);t.current=e;var n=m.useCallback(function(){for(var e,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(o))},[]);return e?n:void 0}var Q=Math.random().toFixed(5).toString().slice(2),J=0,Z=n(25514),$=n(98566),ee=n(52106),et=n(61361),en=n(70527),eo=n(15231);function ea(e,t,n,o){var a=m.useContext(w),r=a.activeKey,i=a.onActive,c=a.onInactive,l={active:r===e};return t||(l.onMouseEnter=function(t){null==n||n({key:e,domEvent:t}),i(e)},l.onMouseLeave=function(t){null==o||o({key:e,domEvent:t}),c(e)}),l}function er(e){var t=m.useContext(w),n=t.mode,o=t.rtl,a=t.inlineIndent;return"inline"!==n?null:o?{paddingRight:e*a}:{paddingLeft:e*a}}function ei(e){var t,n=e.icon,o=e.props,a=e.children;return null===n||!1===n?null:("function"==typeof n?t=m.createElement(n,(0,r.A)({},o)):"boolean"!=typeof n&&(t=n),t||a||null)}var ec=["item"];function el(e){var t=e.item,n=(0,l.A)(e,ec);return Object.defineProperty(n,"item",{get:function(){return(0,p.Ay)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var eu=["title","attribute","elementRef"],es=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],ed=["active"],ef=function(e){(0,ee.A)(n,e);var t=(0,et.A)(n);function n(){return(0,Z.A)(this,n),t.apply(this,arguments)}return(0,$.A)(n,[{key:"render",value:function(){var e=this.props,t=e.title,n=e.attribute,a=e.elementRef,r=(0,l.A)(e,eu),i=(0,en.A)(r,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,p.Ay)(!n,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),m.createElement(d.A.Item,(0,o.A)({},n,{title:"string"==typeof t?t:void 0},i,{ref:a}))}}]),n}(m.Component),ev=m.forwardRef(function(e,t){var n=e.style,c=e.className,u=e.eventKey,d=(e.warnKey,e.disabled),f=e.itemIcon,v=e.children,p=e.role,b=e.onMouseEnter,h=e.onMouseLeave,g=e.onClick,A=e.onKeyDown,k=e.onFocus,C=(0,l.A)(e,es),E=y(u),x=m.useContext(w),S=x.prefixCls,_=x.onItemClick,I=x.disabled,P=x.overflowDisabled,O=x.itemIcon,z=x.selectedKeys,T=x.onActive,L=m.useContext(M)._internalRenderMenuItem,K="".concat(S,"-item"),D=m.useRef(),B=m.useRef(),j=I||d,X=(0,eo.xK)(t,B),W=R(u),G=function(e){return{key:u,keyPath:(0,i.A)(W).reverse(),item:D.current,domEvent:e}},F=ea(u,j,b,h),H=F.active,V=(0,l.A)(F,ed),q=z.includes(u),Y=er(W.length),U={};"option"===e.role&&(U["aria-selected"]=q);var Q=m.createElement(ef,(0,o.A)({ref:D,elementRef:X,role:null===p?"none":p||"menuitem",tabIndex:d?null:-1,"data-menu-id":P&&E?null:E},(0,en.A)(C,["extra"]),V,U,{component:"li","aria-disabled":d,style:(0,r.A)((0,r.A)({},Y),n),className:s()(K,(0,a.A)((0,a.A)((0,a.A)({},"".concat(K,"-active"),H),"".concat(K,"-selected"),q),"".concat(K,"-disabled"),j),c),onClick:function(e){if(!j){var t=G(e);null==g||g(el(t)),_(t)}},onKeyDown:function(e){if(null==A||A(e),e.which===N.A.ENTER){var t=G(e);null==g||g(el(t)),_(t)}},onFocus:function(e){T(u),null==k||k(e)}}),v,m.createElement(ei,{props:(0,r.A)((0,r.A)({},e),{},{isSelected:q}),icon:f||O}));return L&&(Q=L(Q,e,{selected:q})),Q});let ep=m.forwardRef(function(e,t){var n=e.eventKey,a=x(),r=R(n);return(m.useEffect(function(){if(a)return a.registerPath(n,r),function(){a.unregisterPath(n,r)}},[r]),a)?null:m.createElement(ev,(0,o.A)({},e,{ref:t}))});var em=["className","children"],eb=m.forwardRef(function(e,t){var n=e.className,a=e.children,r=(0,l.A)(e,em),i=m.useContext(w),c=i.prefixCls,u=i.mode,d=i.rtl;return m.createElement("ul",(0,o.A)({className:s()(c,d&&"".concat(c,"-rtl"),"".concat(c,"-sub"),"".concat(c,"-").concat("inline"===u?"inline":"vertical"),n),role:"menu"},r,{"data-menu-list":!0,ref:t}),a)});eb.displayName="SubMenuList";var eh=n(63588);function eg(e,t){return(0,eh.A)(e).map(function(e,n){if(m.isValidElement(e)){var o,a,r=e.key,c=null!==(o=null===(a=e.props)||void 0===a?void 0:a.eventKey)&&void 0!==o?o:r;null==c&&(c="tmp_key-".concat([].concat((0,i.A)(t),[n]).join("-")));var l={key:c,eventKey:c};return m.cloneElement(e,l)}return e})}var ey=n(99121),eA={adjustX:1,adjustY:1},ek={topLeft:{points:["bl","tl"],overflow:eA},topRight:{points:["br","tr"],overflow:eA},bottomLeft:{points:["tl","bl"],overflow:eA},bottomRight:{points:["tr","br"],overflow:eA},leftTop:{points:["tr","tl"],overflow:eA},leftBottom:{points:["br","bl"],overflow:eA},rightTop:{points:["tl","tr"],overflow:eA},rightBottom:{points:["bl","br"],overflow:eA}},ew={topLeft:{points:["bl","tl"],overflow:eA},topRight:{points:["br","tr"],overflow:eA},bottomLeft:{points:["tl","bl"],overflow:eA},bottomRight:{points:["tr","br"],overflow:eA},rightTop:{points:["tr","tl"],overflow:eA},rightBottom:{points:["br","bl"],overflow:eA},leftTop:{points:["tl","tr"],overflow:eA},leftBottom:{points:["bl","br"],overflow:eA}};function eC(e,t,n){return t||(n?n[e]||n.other:void 0)}var eE={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ex(e){var t=e.prefixCls,n=e.visible,o=e.children,i=e.popup,l=e.popupStyle,u=e.popupClassName,d=e.popupOffset,f=e.disabled,v=e.mode,p=e.onVisibleChange,b=m.useContext(w),h=b.getPopupContainer,g=b.rtl,y=b.subMenuOpenDelay,A=b.subMenuCloseDelay,k=b.builtinPlacements,C=b.triggerSubMenuAction,E=b.forceSubMenuRender,x=b.rootClassName,S=b.motion,R=b.defaultMotions,_=m.useState(!1),M=(0,c.A)(_,2),I=M[0],P=M[1],N=g?(0,r.A)((0,r.A)({},ew),k):(0,r.A)((0,r.A)({},ek),k),z=eE[v],T=eC(v,S,R),L=m.useRef(T);"inline"!==v&&(L.current=T);var K=(0,r.A)((0,r.A)({},L.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),D=m.useRef();return m.useEffect(function(){return D.current=(0,O.A)(function(){P(n)}),function(){O.A.cancel(D.current)}},[n]),m.createElement(ey.A,{prefixCls:t,popupClassName:s()("".concat(t,"-popup"),(0,a.A)({},"".concat(t,"-rtl"),g),u,x),stretch:"horizontal"===v?"minWidth":null,getPopupContainer:h,builtinPlacements:N,popupPlacement:z,popupVisible:I,popup:i,popupStyle:l,popupAlign:d&&{offset:d},action:f?[]:[C],mouseEnterDelay:y,mouseLeaveDelay:A,onPopupVisibleChange:p,forceRender:E,popupMotion:K,fresh:!0},o)}var eS=n(72261);function eR(e){var t=e.id,n=e.open,a=e.keyPath,i=e.children,l="inline",u=m.useContext(w),s=u.prefixCls,d=u.forceSubMenuRender,f=u.motion,v=u.defaultMotions,p=u.mode,b=m.useRef(!1);b.current=p===l;var h=m.useState(!b.current),g=(0,c.A)(h,2),y=g[0],A=g[1],k=!!b.current&&n;m.useEffect(function(){b.current&&A(!1)},[p]);var E=(0,r.A)({},eC(l,f,v));a.length>1&&(E.motionAppear=!1);var x=E.onVisibleChanged;return(E.onVisibleChanged=function(e){return b.current||e||A(!0),null==x?void 0:x(e)},y)?null:m.createElement(C,{mode:l,locked:!b.current},m.createElement(eS.Ay,(0,o.A)({visible:k},E,{forceRender:d,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(e){var n=e.className,o=e.style;return m.createElement(eb,{id:t,className:n,style:o},i)}))}var e_=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],eM=["active"],eI=m.forwardRef(function(e,t){var n=e.style,i=e.className,u=e.title,f=e.eventKey,v=(e.warnKey,e.disabled),p=e.internalPopupClose,b=e.children,h=e.itemIcon,g=e.expandIcon,A=e.popupClassName,k=e.popupOffset,E=e.popupStyle,x=e.onClick,S=e.onMouseEnter,I=e.onMouseLeave,P=e.onTitleClick,N=e.onTitleMouseEnter,O=e.onTitleMouseLeave,z=(0,l.A)(e,e_),T=y(f),L=m.useContext(w),K=L.prefixCls,D=L.mode,B=L.openKeys,j=L.disabled,X=L.overflowDisabled,W=L.activeKey,G=L.selectedKeys,F=L.itemIcon,H=L.expandIcon,V=L.onItemClick,q=L.onOpenChange,Y=L.onActive,Q=m.useContext(M)._internalRenderSubMenuItem,J=m.useContext(_).isSubPathKey,Z=R(),$="".concat(K,"-submenu"),ee=j||v,et=m.useRef(),en=m.useRef(),eo=null!=g?g:H,ec=B.includes(f),eu=!X&&ec,es=J(G,f),ed=ea(f,ee,N,O),ef=ed.active,ev=(0,l.A)(ed,eM),ep=m.useState(!1),em=(0,c.A)(ep,2),eh=em[0],eg=em[1],ey=function(e){ee||eg(e)},eA=m.useMemo(function(){return ef||"inline"!==D&&(eh||J([W],f))},[D,ef,W,eh,f,J]),ek=er(Z.length),ew=U(function(e){null==x||x(el(e)),V(e)}),eC=T&&"".concat(T,"-popup"),eE=m.useMemo(function(){return m.createElement(ei,{icon:"horizontal"!==D?eo:void 0,props:(0,r.A)((0,r.A)({},e),{},{isOpen:eu,isSubMenu:!0})},m.createElement("i",{className:"".concat($,"-arrow")}))},[D,eo,e,eu,$]),eS=m.createElement("div",(0,o.A)({role:"menuitem",style:ek,className:"".concat($,"-title"),tabIndex:ee?null:-1,ref:et,title:"string"==typeof u?u:null,"data-menu-id":X&&T?null:T,"aria-expanded":eu,"aria-haspopup":!0,"aria-controls":eC,"aria-disabled":ee,onClick:function(e){ee||(null==P||P({key:f,domEvent:e}),"inline"===D&&q(f,!ec))},onFocus:function(){Y(f)}},ev),u,eE),eI=m.useRef(D);if("inline"!==D&&Z.length>1?eI.current="vertical":eI.current=D,!X){var eP=eI.current;eS=m.createElement(ex,{mode:eP,prefixCls:$,visible:!p&&eu&&"inline"!==D,popupClassName:A,popupOffset:k,popupStyle:E,popup:m.createElement(C,{mode:"horizontal"===eP?"vertical":eP},m.createElement(eb,{id:eC,ref:en},b)),disabled:ee,onVisibleChange:function(e){"inline"!==D&&q(f,e)}},eS)}var eN=m.createElement(d.A.Item,(0,o.A)({ref:t,role:"none"},z,{component:"li",style:n,className:s()($,"".concat($,"-").concat(D),i,(0,a.A)((0,a.A)((0,a.A)((0,a.A)({},"".concat($,"-open"),eu),"".concat($,"-active"),eA),"".concat($,"-selected"),es),"".concat($,"-disabled"),ee)),onMouseEnter:function(e){ey(!0),null==S||S({key:f,domEvent:e})},onMouseLeave:function(e){ey(!1),null==I||I({key:f,domEvent:e})}}),eS,!X&&m.createElement(eR,{id:eC,open:eu,keyPath:Z},b));return Q&&(eN=Q(eN,e,{selected:es,active:eA,open:eu,disabled:ee})),m.createElement(C,{onItemClick:ew,mode:"horizontal"===D?"vertical":D,itemIcon:null!=h?h:F,expandIcon:eo},eN)});let eP=m.forwardRef(function(e,t){var n,a=e.eventKey,r=e.children,i=R(a),c=eg(r,i),l=x();return m.useEffect(function(){if(l)return l.registerPath(a,i),function(){l.unregisterPath(a,i)}},[i]),n=l?c:m.createElement(eI,(0,o.A)({ref:t},e),c),m.createElement(S.Provider,{value:i},n)});var eN=n(21855);function eO(e){var t=e.className,n=e.style,o=m.useContext(w).prefixCls;return x()?null:m.createElement("li",{role:"separator",className:s()("".concat(o,"-item-divider"),t),style:n})}var ez=["className","title","eventKey","children"],eT=m.forwardRef(function(e,t){var n=e.className,a=e.title,r=(e.eventKey,e.children),i=(0,l.A)(e,ez),c=m.useContext(w).prefixCls,u="".concat(c,"-item-group");return m.createElement("li",(0,o.A)({ref:t,role:"presentation"},i,{onClick:function(e){return e.stopPropagation()},className:s()(u,n)}),m.createElement("div",{role:"presentation",className:"".concat(u,"-title"),title:"string"==typeof a?a:void 0},a),m.createElement("ul",{role:"group",className:"".concat(u,"-list")},r))});let eL=m.forwardRef(function(e,t){var n=e.eventKey,a=eg(e.children,R(n));return x()?a:m.createElement(eT,(0,o.A)({ref:t},(0,en.A)(e,["warnKey"])),a)});var eK=["label","children","key","type","extra"];function eD(e,t,n,a,i){var c=e,u=(0,r.A)({divider:eO,item:ep,group:eL,submenu:eP},a);return t&&(c=function e(t,n,a){var r=n.item,i=n.group,c=n.submenu,u=n.divider;return(t||[]).map(function(t,s){if(t&&"object"===(0,eN.A)(t)){var d=t.label,f=t.children,v=t.key,p=t.type,b=t.extra,h=(0,l.A)(t,eK),g=null!=v?v:"tmp-".concat(s);return f||"group"===p?"group"===p?m.createElement(i,(0,o.A)({key:g},h,{title:d}),e(f,n,a)):m.createElement(c,(0,o.A)({key:g},h,{title:d}),e(f,n,a)):"divider"===p?m.createElement(u,(0,o.A)({key:g},h)):m.createElement(r,(0,o.A)({key:g},h,{extra:b}),d,(!!b||0===b)&&m.createElement("span",{className:"".concat(a,"-item-extra")},b))}return null}).filter(function(e){return e})}(t,u,i)),eg(c,n)}var eB=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],ej=[],eX=m.forwardRef(function(e,t){var n,u,p,g,y,A,k,w,x,S,R,I,P,N,Z,$,ee,et,en,eo,ea,er,ei,ec,eu,es,ed=e.prefixCls,ef=void 0===ed?"rc-menu":ed,ev=e.rootClassName,em=e.style,eb=e.className,eh=e.tabIndex,eg=e.items,ey=e.children,eA=e.direction,ek=e.id,ew=e.mode,eC=void 0===ew?"vertical":ew,eE=e.inlineCollapsed,ex=e.disabled,eS=e.disabledOverflow,eR=e.subMenuOpenDelay,e_=e.subMenuCloseDelay,eM=e.forceSubMenuRender,eI=e.defaultOpenKeys,eN=e.openKeys,eO=e.activeKey,ez=e.defaultActiveFirst,eT=e.selectable,eL=void 0===eT||eT,eK=e.multiple,eX=void 0!==eK&&eK,eW=e.defaultSelectedKeys,eG=e.selectedKeys,eF=e.onSelect,eH=e.onDeselect,eV=e.inlineIndent,eq=e.motion,eY=e.defaultMotions,eU=e.triggerSubMenuAction,eQ=e.builtinPlacements,eJ=e.itemIcon,eZ=e.expandIcon,e$=e.overflowedIndicator,e0=void 0===e$?"...":e$,e1=e.overflowedIndicatorPopupClassName,e2=e.getPopupContainer,e6=e.onClick,e5=e.onOpenChange,e8=e.onKeyDown,e7=(e.openAnimation,e.openTransitionName,e._internalRenderMenuItem),e4=e._internalRenderSubMenuItem,e9=e._internalComponents,e3=(0,l.A)(e,eB),te=m.useMemo(function(){return[eD(ey,eg,ej,e9,ef),eD(ey,eg,ej,{},ef)]},[ey,eg,e9]),tt=(0,c.A)(te,2),tn=tt[0],to=tt[1],ta=m.useState(!1),tr=(0,c.A)(ta,2),ti=tr[0],tc=tr[1],tl=m.useRef(),tu=(n=(0,f.A)(ek,{value:ek}),p=(u=(0,c.A)(n,2))[0],g=u[1],m.useEffect(function(){J+=1;var e="".concat(Q,"-").concat(J);g("rc-menu-uuid-".concat(e))},[]),p),ts="rtl"===eA,td=(0,f.A)(eI,{value:eN,postState:function(e){return e||ej}}),tf=(0,c.A)(td,2),tv=tf[0],tp=tf[1],tm=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(){tp(e),null==e5||e5(e)}t?(0,b.flushSync)(n):n()},tb=m.useState(tv),th=(0,c.A)(tb,2),tg=th[0],ty=th[1],tA=m.useRef(!1),tk=m.useMemo(function(){return("inline"===eC||"vertical"===eC)&&eE?["vertical",eE]:[eC,!1]},[eC,eE]),tw=(0,c.A)(tk,2),tC=tw[0],tE=tw[1],tx="inline"===tC,tS=m.useState(tC),tR=(0,c.A)(tS,2),t_=tR[0],tM=tR[1],tI=m.useState(tE),tP=(0,c.A)(tI,2),tN=tP[0],tO=tP[1];m.useEffect(function(){tM(tC),tO(tE),tA.current&&(tx?tp(tg):tm(ej))},[tC,tE]);var tz=m.useState(0),tT=(0,c.A)(tz,2),tL=tT[0],tK=tT[1],tD=tL>=tn.length-1||"horizontal"!==t_||eS;m.useEffect(function(){tx&&ty(tv)},[tv]),m.useEffect(function(){return tA.current=!0,function(){tA.current=!1}},[]);var tB=(y=m.useState({}),A=(0,c.A)(y,2)[1],k=(0,m.useRef)(new Map),w=(0,m.useRef)(new Map),x=m.useState([]),R=(S=(0,c.A)(x,2))[0],I=S[1],P=(0,m.useRef)(0),N=(0,m.useRef)(!1),Z=function(){N.current||A({})},$=(0,m.useCallback)(function(e,t){var n,o=q(t);w.current.set(o,e),k.current.set(e,o),P.current+=1;var a=P.current;n=function(){a===P.current&&Z()},Promise.resolve().then(n)},[]),ee=(0,m.useCallback)(function(e,t){var n=q(t);w.current.delete(n),k.current.delete(e)},[]),et=(0,m.useCallback)(function(e){I(e)},[]),en=(0,m.useCallback)(function(e,t){var n=(k.current.get(e)||"").split(V);return t&&R.includes(n[0])&&n.unshift(Y),n},[R]),eo=(0,m.useCallback)(function(e,t){return e.filter(function(e){return void 0!==e}).some(function(e){return en(e,!0).includes(t)})},[en]),ea=(0,m.useCallback)(function(e){var t="".concat(k.current.get(e)).concat(V),n=new Set;return(0,i.A)(w.current.keys()).forEach(function(e){e.startsWith(t)&&n.add(w.current.get(e))}),n},[]),m.useEffect(function(){return function(){N.current=!0}},[]),{registerPath:$,unregisterPath:ee,refreshOverflowKeys:et,isSubPathKey:eo,getKeyPath:en,getKeys:function(){var e=(0,i.A)(k.current.keys());return R.length&&e.push(Y),e},getSubPathKeys:ea}),tj=tB.registerPath,tX=tB.unregisterPath,tW=tB.refreshOverflowKeys,tG=tB.isSubPathKey,tF=tB.getKeyPath,tH=tB.getKeys,tV=tB.getSubPathKeys,tq=m.useMemo(function(){return{registerPath:tj,unregisterPath:tX}},[tj,tX]),tY=m.useMemo(function(){return{isSubPathKey:tG}},[tG]);m.useEffect(function(){tW(tD?ej:tn.slice(tL+1).map(function(e){return e.key}))},[tL,tD]);var tU=(0,f.A)(eO||ez&&(null===(es=tn[0])||void 0===es?void 0:es.key),{value:eO}),tQ=(0,c.A)(tU,2),tJ=tQ[0],tZ=tQ[1],t$=U(function(e){tZ(e)}),t0=U(function(){tZ(void 0)});(0,m.useImperativeHandle)(t,function(){return{list:tl.current,focus:function(e){var t,n,o=H(tH(),tu),a=o.elements,r=o.key2element,i=o.element2key,c=G(tl.current,a),l=null!=tJ?tJ:c[0]?i.get(c[0]):null===(t=tn.find(function(e){return!e.props.disabled}))||void 0===t?void 0:t.key,u=r.get(l);l&&u&&(null==u||null===(n=u.focus)||void 0===n||n.call(u,e))}}});var t1=(0,f.A)(eW||[],{value:eG,postState:function(e){return Array.isArray(e)?e:null==e?ej:[e]}}),t2=(0,c.A)(t1,2),t6=t2[0],t5=t2[1],t8=function(e){if(eL){var t,n=e.key,o=t6.includes(n);t5(t=eX?o?t6.filter(function(e){return e!==n}):[].concat((0,i.A)(t6),[n]):[n]);var a=(0,r.A)((0,r.A)({},e),{},{selectedKeys:t});o?null==eH||eH(a):null==eF||eF(a)}!eX&&tv.length&&"inline"!==t_&&tm(ej)},t7=U(function(e){null==e6||e6(el(e)),t8(e)}),t4=U(function(e,t){var n=tv.filter(function(t){return t!==e});if(t)n.push(e);else if("inline"!==t_){var o=tV(e);n=n.filter(function(e){return!o.has(e)})}(0,v.A)(tv,n,!0)||tm(n,!0)}),t9=(er=function(e,t){var n=null!=t?t:!tv.includes(e);t4(e,n)},ei=m.useRef(),(ec=m.useRef()).current=tJ,eu=function(){O.A.cancel(ei.current)},m.useEffect(function(){return function(){eu()}},[]),function(e){var t=e.which;if([].concat(W,[D,B,j,X]).includes(t)){var n=tH(),o=H(n,tu),r=o,i=r.elements,c=r.key2element,l=r.element2key,u=function(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}(c.get(tJ),i),s=l.get(u),d=function(e,t,n,o){var r,i="prev",c="next",l="children",u="parent";if("inline"===e&&o===D)return{inlineTrigger:!0};var s=(0,a.A)((0,a.A)({},L,i),K,c),d=(0,a.A)((0,a.A)((0,a.A)((0,a.A)({},z,n?c:i),T,n?i:c),K,l),D,l),f=(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({},L,i),K,c),D,l),B,u),z,n?l:u),T,n?u:l);switch(null===(r=({inline:s,horizontal:d,vertical:f,inlineSub:s,horizontalSub:f,verticalSub:f})["".concat(e).concat(t?"":"Sub")])||void 0===r?void 0:r[o]){case i:return{offset:-1,sibling:!0};case c:return{offset:1,sibling:!0};case u:return{offset:-1,sibling:!1};case l:return{offset:1,sibling:!1};default:return null}}(t_,1===tF(s,!0).length,ts,t);if(!d&&t!==j&&t!==X)return;(W.includes(t)||[j,X].includes(t))&&e.preventDefault();var f=function(e){if(e){var t=e,n=e.querySelector("a");null!=n&&n.getAttribute("href")&&(t=n);var o=l.get(e);tZ(o),eu(),ei.current=(0,O.A)(function(){ec.current===o&&t.focus()})}};if([j,X].includes(t)||d.sibling||!u){var v,p=G(v=u&&"inline"!==t_?function(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}(u):tl.current,i);f(t===j?p[0]:t===X?p[p.length-1]:F(v,i,u,d.offset))}else if(d.inlineTrigger)er(s);else if(d.offset>0)er(s,!0),eu(),ei.current=(0,O.A)(function(){o=H(n,tu);var e=u.getAttribute("aria-controls");f(F(document.getElementById(e),o.elements))},5);else if(d.offset<0){var m=tF(s,!0),b=m[m.length-2],h=c.get(b);er(b,!1),f(h)}}null==e8||e8(e)});m.useEffect(function(){tc(!0)},[]);var t3=m.useMemo(function(){return{_internalRenderMenuItem:e7,_internalRenderSubMenuItem:e4}},[e7,e4]),ne="horizontal"!==t_||eS?tn:tn.map(function(e,t){return m.createElement(C,{key:e.key,overflowDisabled:t>tL},e)}),nt=m.createElement(d.A,(0,o.A)({id:ek,ref:tl,prefixCls:"".concat(ef,"-overflow"),component:"ul",itemComponent:ep,className:s()(ef,"".concat(ef,"-root"),"".concat(ef,"-").concat(t_),eb,(0,a.A)((0,a.A)({},"".concat(ef,"-inline-collapsed"),tN),"".concat(ef,"-rtl"),ts),ev),dir:eA,style:em,role:"menu",tabIndex:void 0===eh?0:eh,data:ne,renderRawItem:function(e){return e},renderRawRest:function(e){var t=e.length,n=t?tn.slice(-t):null;return m.createElement(eP,{eventKey:Y,title:e0,disabled:tD,internalPopupClose:0===t,popupClassName:e1},n)},maxCount:"horizontal"!==t_||eS?d.A.INVALIDATE:d.A.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){tK(e)},onKeyDown:t9},e3));return m.createElement(M.Provider,{value:t3},m.createElement(h.Provider,{value:tu},m.createElement(C,{prefixCls:ef,rootClassName:ev,mode:t_,openKeys:tv,rtl:ts,disabled:ex,motion:ti?eq:null,defaultMotions:ti?eY:null,activeKey:tJ,onActive:t$,onInactive:t0,selectedKeys:t6,inlineIndent:void 0===eV?24:eV,subMenuOpenDelay:void 0===eR?.1:eR,subMenuCloseDelay:void 0===e_?.1:e_,forceSubMenuRender:eM,builtinPlacements:eQ,triggerSubMenuAction:void 0===eU?"hover":eU,getPopupContainer:e2,itemIcon:eJ,expandIcon:eZ,onItemClick:t7,onOpenChange:t4},m.createElement(_.Provider,{value:tY},nt),m.createElement("div",{style:{display:"none"},"aria-hidden":!0},m.createElement(E.Provider,{value:tq},to)))))});eX.Item=ep,eX.SubMenu=eP,eX.ItemGroup=eL,eX.Divider=eO;let eW=eX},89585:(e,t,n)=>{n.d(t,{A:()=>M});var o=n(85407),a=n(85268),r=n(59912),i=n(64406),c=n(12115),l=n(4617),u=n.n(l),s=n(30377),d=n(66105),f=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],v=void 0,p=c.forwardRef(function(e,t){var n,r=e.prefixCls,l=e.invalidate,d=e.item,p=e.renderItem,m=e.responsive,b=e.responsiveDisabled,h=e.registerSize,g=e.itemKey,y=e.className,A=e.style,k=e.children,w=e.display,C=e.order,E=e.component,x=(0,i.A)(e,f),S=m&&!w;c.useEffect(function(){return function(){h(g,null)}},[]);var R=p&&d!==v?p(d,{index:C}):k;l||(n={opacity:S?0:1,height:S?0:v,overflowY:S?"hidden":v,order:m?C:v,pointerEvents:S?"none":v,position:S?"absolute":v});var _={};S&&(_["aria-hidden"]=!0);var M=c.createElement(void 0===E?"div":E,(0,o.A)({className:u()(!l&&r,y),style:(0,a.A)((0,a.A)({},n),A)},_,x,{ref:t}),R);return m&&(M=c.createElement(s.A,{onResize:function(e){h(g,e.offsetWidth)},disabled:b},M)),M});p.displayName="Item";var m=n(97262),b=n(47650),h=n(13379);function g(e,t){var n=c.useState(t),o=(0,r.A)(n,2),a=o[0],i=o[1];return[a,(0,m.A)(function(t){e(function(){i(t)})})]}var y=c.createContext(null),A=["component"],k=["className"],w=["className"],C=c.forwardRef(function(e,t){var n=c.useContext(y);if(!n){var a=e.component,r=(0,i.A)(e,A);return c.createElement(void 0===a?"div":a,(0,o.A)({},r,{ref:t}))}var l=n.className,s=(0,i.A)(n,k),d=e.className,f=(0,i.A)(e,w);return c.createElement(y.Provider,{value:null},c.createElement(p,(0,o.A)({ref:t,className:u()(l,d)},s,f)))});C.displayName="RawItem";var E=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],x="responsive",S="invalidate";function R(e){return"+ ".concat(e.length," ...")}var _=c.forwardRef(function(e,t){var n,l=e.prefixCls,f=void 0===l?"rc-overflow":l,v=e.data,m=void 0===v?[]:v,A=e.renderItem,k=e.renderRawItem,w=e.itemKey,C=e.itemWidth,_=void 0===C?10:C,M=e.ssr,I=e.style,P=e.className,N=e.maxCount,O=e.renderRest,z=e.renderRawRest,T=e.suffix,L=e.component,K=e.itemComponent,D=e.onVisibleChange,B=(0,i.A)(e,E),j="full"===M,X=(n=c.useRef(null),function(e){n.current||(n.current=[],function(e){if("undefined"==typeof MessageChannel)(0,h.A)(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}(function(){(0,b.unstable_batchedUpdates)(function(){n.current.forEach(function(e){e()}),n.current=null})})),n.current.push(e)}),W=g(X,null),G=(0,r.A)(W,2),F=G[0],H=G[1],V=F||0,q=g(X,new Map),Y=(0,r.A)(q,2),U=Y[0],Q=Y[1],J=g(X,0),Z=(0,r.A)(J,2),$=Z[0],ee=Z[1],et=g(X,0),en=(0,r.A)(et,2),eo=en[0],ea=en[1],er=g(X,0),ei=(0,r.A)(er,2),ec=ei[0],el=ei[1],eu=(0,c.useState)(null),es=(0,r.A)(eu,2),ed=es[0],ef=es[1],ev=(0,c.useState)(null),ep=(0,r.A)(ev,2),em=ep[0],eb=ep[1],eh=c.useMemo(function(){return null===em&&j?Number.MAX_SAFE_INTEGER:em||0},[em,F]),eg=(0,c.useState)(!1),ey=(0,r.A)(eg,2),eA=ey[0],ek=ey[1],ew="".concat(f,"-item"),eC=Math.max($,eo),eE=N===x,ex=m.length&&eE,eS=N===S,eR=ex||"number"==typeof N&&m.length>N,e_=(0,c.useMemo)(function(){var e=m;return ex?e=null===F&&j?m:m.slice(0,Math.min(m.length,V/_)):"number"==typeof N&&(e=m.slice(0,N)),e},[m,_,F,N,ex]),eM=(0,c.useMemo)(function(){return ex?m.slice(eh+1):m.slice(e_.length)},[m,e_,ex,eh]),eI=(0,c.useCallback)(function(e,t){var n;return"function"==typeof w?w(e):null!==(n=w&&(null==e?void 0:e[w]))&&void 0!==n?n:t},[w]),eP=(0,c.useCallback)(A||function(e){return e},[A]);function eN(e,t,n){(em!==e||void 0!==t&&t!==ed)&&(eb(e),n||(ek(e<m.length-1),null==D||D(e)),void 0!==t&&ef(t))}function eO(e,t){Q(function(n){var o=new Map(n);return null===t?o.delete(e):o.set(e,t),o})}function ez(e){return U.get(eI(e_[e],e))}(0,d.A)(function(){if(V&&"number"==typeof eC&&e_){var e=ec,t=e_.length,n=t-1;if(!t){eN(0,null);return}for(var o=0;o<t;o+=1){var a=ez(o);if(j&&(a=a||0),void 0===a){eN(o-1,void 0,!0);break}if(e+=a,0===n&&e<=V||o===n-1&&e+ez(n)<=V){eN(n,null);break}if(e+eC>V){eN(o-1,e-a-ec+eo);break}}T&&ez(0)+ec>V&&ef(null)}},[V,U,eo,ec,eI,e_]);var eT=eA&&!!eM.length,eL={};null!==ed&&ex&&(eL={position:"absolute",left:ed,top:0});var eK={prefixCls:ew,responsive:ex,component:K,invalidate:eS},eD=k?function(e,t){var n=eI(e,t);return c.createElement(y.Provider,{key:n,value:(0,a.A)((0,a.A)({},eK),{},{order:t,item:e,itemKey:n,registerSize:eO,display:t<=eh})},k(e,t))}:function(e,t){var n=eI(e,t);return c.createElement(p,(0,o.A)({},eK,{order:t,key:n,item:e,renderItem:eP,itemKey:n,registerSize:eO,display:t<=eh}))},eB={order:eT?eh:Number.MAX_SAFE_INTEGER,className:"".concat(ew,"-rest"),registerSize:function(e,t){ea(t),ee(eo)},display:eT},ej=O||R,eX=z?c.createElement(y.Provider,{value:(0,a.A)((0,a.A)({},eK),eB)},z(eM)):c.createElement(p,(0,o.A)({},eK,eB),"function"==typeof ej?ej(eM):ej),eW=c.createElement(void 0===L?"div":L,(0,o.A)({className:u()(!eS&&f,P),style:I,ref:t},B),e_.map(eD),eR?eX:null,T&&c.createElement(p,(0,o.A)({},eK,{responsive:eE,responsiveDisabled:!ex,order:eh,className:"".concat(ew,"-suffix"),registerSize:function(e,t){el(t)},display:!0,style:eL}),T));return eE?c.createElement(s.A,{onResize:function(e,t){H(t.clientWidth)},disabled:!ex},eW):eW});_.displayName="Overflow",_.Item=C,_.RESPONSIVE=x,_.INVALIDATE=S;let M=_}}]);