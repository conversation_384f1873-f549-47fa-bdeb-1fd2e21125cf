(()=>{var e={};e.id=2562,e.ids=[2562],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},92356:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(70260),r=s(28203),l=s(25155),n=s.n(l),i=s(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["dashboard",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,67373)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/sales/page",pathname:"/dashboard/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},50288:(e,t,s)=>{Promise.resolve().then(s.bind(s,67373))},15944:(e,t,s)=>{Promise.resolve().then(s.bind(s,77929))},51592:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),r=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};var n=s(78480);let i=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},73021:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),r=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var n=s(78480);let i=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},77929:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>W});var a=s(45512),r=s(58009),l=s.n(r),n=s(6987),i=s(3117),o=s(21419),d=s(45905),c=s(88752),x=s(63440),m=s(58733),h=s(60636),g=s(765),A=s(10499);let p=()=>{let[e,t]=(0,r.useState)(1),[s,a]=(0,r.useState)(10),[l,n]=(0,r.useState)(""),[i,o]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=setTimeout(()=>{o(l)},500);return()=>{clearTimeout(e)}},[l]);let{data:d,isLoading:c,isFetching:x,refetch:m}=(0,A.JZ)({page:e,limit:s,search:i},{refetchOnMountOrArgChange:!0});if(console.log("Sales data from API:",d),console.log("Total sales:",d?.data?.total||0),console.log("Sales array:",d?.data?.sales||[]),d?.data?.sales&&d.data.sales.length>0){let e=d.data.sales[0];console.log("First sale:",e),console.log("First sale has items?","items"in e),"items"in e&&console.log("First sale items:",e.items)}return{sales:d?.data?.sales||[],total:d?.data?.total||0,page:e,limit:s,isLoading:c||x,refetch:m,searchTerm:l,setSearchTerm:n,handlePageChange:(e,s)=>{t(e),s&&a(s)}}};var u=s(49792);let y=e=>{let[t,{isLoading:s}]=(0,A.lz)();return{deleteSale:async s=>{try{let a=await t(s).unwrap();if(a.success)return(0,u.r)("success","Sale deleted successfully"),e&&e(),!0;return(0,u.r)("error",a.message||"Failed to delete sale"),!1}catch(e){return(0,u.r)("error",e.data?.message||"An error occurred while deleting the sale"),!1}},isDeleting:s}},j=e=>{let[t,{isLoading:s}]=(0,A.Kn)();return{bulkDeleteSales:async s=>{try{console.log("Bulk deleting sales with IDs:",s);let a=await t(s).unwrap();if(!a.success)throw Error(a.message||"Failed to delete sales");return(0,u.r)("success",`${s.length} sales deleted successfully`),e&&e(),a.data}catch(e){throw console.error("Bulk delete sales error:",e),(0,u.r)("error",e.message||"Failed to delete sales"),e}},isDeleting:s}};var f=s(48752),b=s(77067),N=s(70001),E=s(25421),w=s(73021),Q=s(81045),v=s(25834),C=s(86977),S=s(63844),B=s(73542),k=s(92273),I=s(16589),M=s.n(I);s(42095);let J=({sales:e,loading:t,onViewSale:s,onDelete:l,onBulkDelete:n,isMobile:o=!1})=>{let c=(0,B.E)(),{user:x}=(0,k.d4)(e=>e.auth),[m,h]=(0,r.useState)([]),[g,A]=(0,r.useState)(!1),p=x?.role==="admin"||x?.role==="superadmin",u=t=>{let s=t.target.checked;A(s),s?h(p?e.map(e=>e.id):[]):h([])},y=(e,t)=>{t?h(t=>[...t,e]):h(t=>t.filter(t=>t!==e))},j=e=>new Intl.NumberFormat("en-GH",{style:"currency",currency:"GHS"}).format(parseFloat(e)),I=e=>M()(e).format("MMM D, YYYY HH:mm"),J=e=>{l(e)},P=e.length>0;return(0,a.jsxs)("div",{className:"overflow-hidden bg-white",children:[m.length>0&&p&&(0,a.jsxs)("div",{className:"flex items-center justify-between border-b bg-gray-100 p-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[m.length," ",1===m.length?"sale":"sales"," selected"]}),(0,a.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,a.jsx)(E.A,{}),onClick:()=>{m.length>0&&n?(n(m),h([]),A(!1)):f.Ay.warning({message:"No sales selected",description:"Please select at least one sale to delete."})},className:"ml-2",children:"Delete Selected"})]}),!P&&e.length>0&&(0,a.jsxs)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:[(0,a.jsx)("p",{children:'You don"t have access to view these sales.'}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"You can only view sales created by you or your team."})]}),P&&(o||c?(0,a.jsxs)(S.jB,{columns:p?"50px 80px 120px 120px 120px 150px":"80px 120px 120px 120px 150px",minWidth:p?"700px":"650px",children:[p&&(0,a.jsx)(S.A0,{className:"text-center",children:(0,a.jsx)(b.A,{checked:g,onChange:u,disabled:0===e.length})}),(0,a.jsx)(S.A0,{children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-1"}),"ID"]})}),(0,a.jsx)(S.A0,{children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"mr-1"}),"Amount"]})}),(0,a.jsx)(S.A0,{children:"Payment"}),(0,a.jsx)(S.A0,{children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(Q.A,{className:"mr-1"}),"Date"]})}),(0,a.jsx)(S.A0,{className:"text-right",children:"Actions"}),e.map(e=>(0,a.jsxs)(S.Hj,{selected:m.includes(e.id),children:[p&&(0,a.jsx)(S.nA,{className:"text-center",children:(0,a.jsx)(b.A,{checked:m.includes(e.id),onChange:t=>y(e.id,t.target.checked)})}),(0,a.jsx)(S.nA,{children:(0,a.jsxs)("span",{className:"font-medium",children:["#",e.id]})}),(0,a.jsx)(S.nA,{children:(0,a.jsx)("span",{className:"font-medium text-green-600",children:j(e.totalAmount)})}),(0,a.jsx)(S.nA,{children:(0,a.jsx)("span",{className:`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${"cash"===e.paymentMethod?"bg-green-800 text-green-100":"card"===e.paymentMethod?"bg-blue-800 text-blue-100":"bg-purple-800 text-purple-100"}`,children:e.paymentMethod.replace("_"," ").toUpperCase()})}),(0,a.jsx)(S.nA,{children:(0,a.jsx)("span",{className:"text-sm",children:M()(e.transactionDate).format("MMM D, YYYY")})}),(0,a.jsx)(S.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(N.A,{title:"View Details",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(v.A,{}),onClick:()=>s(e),type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),(x?.role==="admin"||x?.role==="superadmin")&&(0,a.jsx)(N.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(C.A,{}),onClick:()=>J(e.id),type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[p&&(0,a.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,a.jsx)(b.A,{checked:g,onChange:u,disabled:0===e.length})}),(0,a.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-1"}),"ID"]})}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"mr-1"}),"Total Amount"]})}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"mr-1"}),"Payment Method"]})}),(0,a.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700",children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(Q.A,{className:"mr-1"}),"Date"]})}),(0,a.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:e.map(e=>(0,a.jsxs)("tr",{className:m.includes(e.id)?"bg-blue-50":"",children:[p&&(0,a.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-center",children:(0,a.jsx)(b.A,{checked:m.includes(e.id),onChange:t=>y(e.id,t.target.checked)})}),(0,a.jsxs)("td",{className:"sticky left-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-gray-800",children:["#",e.id]}),(0,a.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-gray-800",children:j(e.totalAmount)}),(0,a.jsx)("td",{className:"whitespace-nowrap px-3 py-4",children:(0,a.jsx)("span",{className:`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${"cash"===e.paymentMethod?"bg-green-800 text-green-100":"card"===e.paymentMethod?"bg-blue-800 text-blue-100":"bg-purple-800 text-purple-100"}`,children:e.paymentMethod.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-gray-800",children:I(e.transactionDate)}),(0,a.jsx)("td",{className:"sticky right-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,a.jsx)(N.A,{title:"View Details",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(v.A,{}),onClick:()=>s(e),type:"text",className:"view-button text-green-500",size:"middle"})}),(x?.role==="admin"||x?.role==="superadmin")&&(0,a.jsx)(N.A,{title:"Delete",children:(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(C.A,{}),onClick:()=>J(e.id),type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})}))]})};var P=s(59022),D=s(60165);let z=({current:e,pageSize:t,total:s,onChange:r,isMobile:l=!1})=>{let n=Math.ceil(s/t);return(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,s)})," of"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s})," results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>r(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)(P.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(t,s)=>{let l=s+1;return(0,a.jsx)("button",{onClick:()=>r(l),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:l},l)}),(0,a.jsxs)("button",{onClick:()=>r(e+1),disabled:e>=n,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=n?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)(D.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>r(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,a.jsx)("button",{onClick:()=>r(e+1),disabled:e>=n,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})};var R=s(94262),K=s(12869),T=s(31111),Y=s(79203),F=s(78959),U=s(51592),H=s(24648),G=s(91054),q=s(98776);let L=({sale:e,isOpen:t,onClose:s})=>{let r;let{data:n,isLoading:m}=(0,A.Vk)(e?.id||0,{skip:!e||!t}),h=l().useMemo(()=>n?.data?.saleItems&&Array.isArray(n.data.saleItems)&&n.data.saleItems.length>0?n.data.saleItems:n?.data?.sale?.items&&Array.isArray(n.data.sale.items)&&n.data.sale.items.length>0?n.data.sale.items:[],[n]),g=t=>{if("createdByName"===t){if(n?.data?.sale?.createdByName)return n.data.sale.createdByName;if(e?.createdByName)return e.createdByName;let t=n?.data?.sale?.createdBy||e?.createdBy;return t?`User ID: ${t}`:null}return"items"===t?h:n?.data?.sale&&t in n.data.sale?n.data.sale[t]:e&&t in e?e[t]:null},p=(0,a.jsx)("div",{className:"flex justify-end space-x-2",children:(0,a.jsx)(i.Ay,{onClick:s,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Close"})});return(0,a.jsx)(q.A,{title:"Sale Details",isOpen:t,onClose:s,width:"500px",footer:p,children:m?(0,a.jsx)("div",{className:"flex justify-center items-center h-full min-h-[300px]",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):n?.data?(0,a.jsxs)("div",{className:"p-4 sales-form",children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-2"}),"Sale #",n?.data?.sale?.id||e?.id||"N/A"]}),(0,a.jsx)("p",{className:"text-gray-600 mt-1 flex items-center",children:"Complete sale information and details"})]}),(0,a.jsxs)(K.A,{bordered:!0,column:1,className:"sale-detail-light",labelStyle:{color:"#333",backgroundColor:"#f5f5f5"},contentStyle:{color:"#333",backgroundColor:"#ffffff"},children:[(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(d.A,{})," Sale ID"]}),children:String(g("id"))||"N/A"}),(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(w.A,{})," Total Amount"]}),children:(e=>{if(null==e)return"GHS 0.00";let t="string"==typeof e?parseFloat(e):e;return`GHS ${t.toFixed(2)}`})("string"==typeof g("totalAmount")||"number"==typeof g("totalAmount")?String(g("totalAmount")):"0")}),(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(U.A,{})," Payment Method"]}),children:g("paymentMethod")?(0,a.jsx)(T.A,{color:(e=>{switch(e){case"cash":return"green";case"card":return"blue";case"mobile_money":return"purple";default:return"default"}})(g("paymentMethod")),children:(r=g("paymentMethod"),r?.replace("_"," ").toUpperCase()||"N/A")}):"N/A"}),(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(Q.A,{})," Transaction Date"]}),children:(e=>e?M()(e).format("MMM D, YYYY HH:mm"):"N/A")(g("transactionDate")||"")}),(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(H.A,{})," Created By"]}),children:String(g("createdByName"))||"N/A"}),(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(x.A,{})," Store"]}),children:String(g("storeName")||"N/A")}),g("receiptUrl")&&(0,a.jsx)(K.A.Item,{label:(0,a.jsxs)("span",{children:[(0,a.jsx)(G.A,{})," Receipt"]}),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(Y.A,{src:g("receiptUrl"),alt:"Receipt",width:200,className:"border border-gray-700 rounded-md",fallback:"data:image/png;base64,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"}),(0,a.jsx)(i.Ay,{type:"link",href:g("receiptUrl"),target:"_blank",className:"mt-2",children:"View Full Receipt"})]})})]}),(0,a.jsxs)("div",{className:"mt-8 bg-white p-4 rounded-lg border border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 text-gray-800 flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-2"})," Sale Items"]}),(0,a.jsx)("div",{className:"overflow-x-auto rounded-md border border-gray-200 shadow-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider",children:"PRODUCT"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider w-24",children:"QUANTITY"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32",children:"PRICE"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider w-32",children:"SUBTOT"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.length>0?h.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800",children:e.productName||"Unknown Product"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-center",children:e.quantity||0}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right",children:["GHS ",(e.price?parseFloat(e.price):0).toFixed(2)]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right",children:["GHS ",((e.price?parseFloat(e.price):0)*(e.quantity||0)).toFixed(2)]})]},`sale-item-${t}`)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:4,className:"px-6 py-8 text-center text-sm text-gray-500",children:"No items found for this sale"})})}),(0,a.jsx)("tfoot",{className:"bg-gray-100",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800",children:"Total"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-center",children:h.length>0?h.reduce((e,t)=>e+(t.quantity||0),0):0}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-800 text-right"}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600 text-right",children:["GHS ",h.length>0?h.reduce((e,t)=>e+(t.price?parseFloat(t.price):0)*(t.quantity||0),0).toFixed(2):("string"==typeof g("totalAmount")?parseFloat(g("totalAmount")):parseFloat(String(g("totalAmount")||"0"))).toFixed(2)]})]})})]})})]})]}):(0,a.jsx)("div",{className:"p-4 bg-white h-full flex items-center justify-center",children:(0,a.jsx)(F.A,{description:"Sale details not found",image:F.A.PRESENTED_IMAGE_SIMPLE})})})};var Z=s(37764);s(86078);let O=({searchTerm:e,setSearchTerm:t,isMobile:s=!1})=>(0,a.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,a.jsx)(Z.A,{placeholder:"Search sales...",prefix:(0,a.jsx)(m.A,{className:"text-gray-500"}),value:e,onChange:e=>{t(e.target.value)},className:"border-gray-300 bg-white text-gray-800 hover:border-blue-500 focus:border-blue-500",style:{width:s?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,a.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),e&&(0,a.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',e,'"']})]});var V=s(51531),X=s(2274);let W=()=>{let{user:e}=(0,h.A)(),t=(0,g.a)(),[s,l]=(0,r.useState)(!1),[A,u]=(0,r.useState)(!1),[f,b]=(0,r.useState)(null),[N,E]=(0,r.useState)(null),[w,Q]=(0,r.useState)(!1),{sales:v,total:C,page:S,limit:B,isLoading:k,refetch:I,searchTerm:M,setSearchTerm:P,handlePageChange:D}=p(),{deleteSale:K,isDeleting:T}=y(()=>{Q(!1),I()}),{bulkDeleteSales:Y,isDeleting:F}=j(()=>{H(!1),I()}),[U,H]=(0,r.useState)(!1),[G,q]=(0,r.useState)([]),{data:Z,isLoading:W}=(0,X.r3)({page:1,limit:1}),_=(Z?.data?.products?.length??0)>0,$=async()=>{N&&await K(N)},ee=async()=>{if(console.log("confirmBulkDelete called with sales:",G),G.length>0)try{await Y(G)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,a.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,a.jsx)(n.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"Sales Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:_&&(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(d.A,{}),onClick:()=>{l(!0)},size:t?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:t?"":"New Sale"}),children:(0,a.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,a.jsx)(O,{searchTerm:M,setSearchTerm:P,isMobile:t}),k?(0,a.jsx)("div",{className:"flex h-60 items-center justify-center bg-gray-50",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(c.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,a.jsx)(a.Fragment,{children:_?(0,a.jsxs)(a.Fragment,{children:[v.length>0?(0,a.jsx)(J,{sales:v,loading:!1,onViewSale:e=>{b(e),u(!0)},onDelete:e=>{E(e),Q(!0)},onBulkDelete:e=>{console.log("handleBulkDelete called with saleIds:",e),q(e),H(!0)},isMobile:t}):(0,a.jsx)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:M?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{children:"No sales found matching your search criteria."}),(0,a.jsx)(i.Ay,{type:"primary",onClick:()=>P(""),className:"mt-4 bg-blue-600 hover:bg-blue-700",icon:(0,a.jsx)(m.A,{}),children:"Clear Search"})]}):(0,a.jsx)("p",{children:'No sales found. Click "New Sale" to create one.'})}),v.length>0&&(0,a.jsx)(z,{current:S,pageSize:B,total:C,onChange:D,isMobile:t})]}):(0,a.jsxs)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:[(0,a.jsx)(x.A,{className:"text-4xl mb-4"}),(0,a.jsx)("p",{children:"No products found. Please add products to start selling."})]})})]})}),(0,a.jsx)(R.A,{isOpen:s,onClose:()=>l(!1),onSuccess:()=>{l(!1),I()}}),(0,a.jsx)(L,{sale:f,isOpen:A,onClose:()=>{u(!1),b(null)}}),(0,a.jsx)(V.A,{isOpen:w,onClose:()=>{Q(!1),E(null)},onConfirm:$,title:"Delete Sale",message:"Are you sure you want to delete this sale? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:T,type:"danger"}),(0,a.jsx)(V.A,{isOpen:U,onClose:()=>{H(!1),q([])},onConfirm:ee,title:"Delete Multiple Sales",message:`Are you sure you want to delete ${G.length} sales? This action cannot be undone.`,confirmText:"Delete All",cancelText:"Cancel",isLoading:F,type:"danger"})]})}},67373:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\sales\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx","default")},86078:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,5050,1785,984,5482,106,4286,6165,4262],()=>s(92356));module.exports=a})();