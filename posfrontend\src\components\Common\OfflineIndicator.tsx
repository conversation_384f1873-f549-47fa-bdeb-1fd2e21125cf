'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Tooltip, Button, Modal, Progress, Typography } from 'antd';
import { 
  WifiOutlined, 
  DisconnectOutlined, 
  SyncOutlined, 
  CloudSyncOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { offlineSync } from '@/utils/offlineSync';
import { offlineStorage } from '@/utils/offlineStorage';

const { Text } = Typography;

interface OfflineIndicatorProps {
  className?: string;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ className = '' }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [pendingSales, setPendingSales] = useState(0);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);

  // Auto-sync function
  const handleAutoSync = useCallback(async () => {
    if (!isOnline || syncStatus === 'syncing') return;

    try {
      setSyncStatus('syncing');
      await offlineSync.forceSyncNow();
      setSyncStatus('idle');

      // Update pending sales count
      const pending = await offlineStorage.getOfflineSales('pending');
      const failed = await offlineStorage.getOfflineSales('failed');
      setPendingSales(pending.length + failed.length);
    } catch (error) {
      console.error('Auto-sync failed:', error);
      setSyncStatus('error');
    }
  }, [isOnline, syncStatus]);

  // Monitor network status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    const handleOnline = () => {
      setIsOnline(true);
      // Auto-sync when coming back online
      handleAutoSync();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    // Set initial status
    updateOnlineStatus();

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleAutoSync]);

  // Monitor pending sales
  useEffect(() => {
    const updatePendingSales = async () => {
      try {
        const pending = await offlineStorage.getOfflineSales('pending');
        const failed = await offlineStorage.getOfflineSales('failed');
        setPendingSales(pending.length + failed.length);
      } catch (error) {
        console.error('Failed to get pending sales:', error);
      }
    };

    updatePendingSales();
    
    // Update every 30 seconds
    const interval = setInterval(updatePendingSales, 30000);
    
    return () => clearInterval(interval);
  }, []);



  const handleManualSync = async () => {
    if (!isOnline) return;
    
    setShowSyncModal(true);
    setSyncProgress(0);
    
    try {
      setSyncStatus('syncing');
      
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setSyncProgress(prev => Math.min(prev + 10, 90));
      }, 200);
      
      await offlineSync.forceSyncNow();
      
      clearInterval(progressInterval);
      setSyncProgress(100);
      setSyncStatus('idle');
      
      // Update pending sales count
      const pending = await offlineStorage.getOfflineSales('pending');
      const failed = await offlineStorage.getOfflineSales('failed');
      setPendingSales(pending.length + failed.length);
      
      setTimeout(() => {
        setShowSyncModal(false);
        setSyncProgress(0);
      }, 1000);
      
    } catch (error) {
      console.error('Manual sync failed:', error);
      setSyncStatus('error');
      setShowSyncModal(false);
      setSyncProgress(0);
    }
  };

  const getStatusColor = () => {
    if (!isOnline) return '#ff4d4f'; // Red for offline
    if (syncStatus === 'syncing') return '#1890ff'; // Blue for syncing
    if (syncStatus === 'error') return '#faad14'; // Orange for error
    if (pendingSales > 0) return '#faad14'; // Orange for pending
    return '#52c41a'; // Green for online and synced
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline Mode';
    if (syncStatus === 'syncing') return 'Syncing...';
    if (syncStatus === 'error') return 'Sync Error';
    if (pendingSales > 0) return `${pendingSales} Pending`;
    return 'Online & Synced';
  };

  const getIcon = () => {
    if (!isOnline) return <DisconnectOutlined />;
    if (syncStatus === 'syncing') return <SyncOutlined spin />;
    if (syncStatus === 'error') return <ExclamationCircleOutlined />;
    return <WifiOutlined />;
  };

  return (
    <>
      <div className={`flex items-center space-x-2 ${className}`}>
        <Tooltip 
          title={
            <div>
              <div><strong>Status:</strong> {getStatusText()}</div>
              {pendingSales > 0 && (
                <div><strong>Pending Sales:</strong> {pendingSales}</div>
              )}
              {!isOnline && (
                <div className="text-yellow-300">
                  Sales will sync when connection returns
                </div>
              )}
              {isOnline && pendingSales > 0 && (
                <div className="text-blue-300">
                  Click to sync now
                </div>
              )}
            </div>
          }
        >
          <Badge 
            count={pendingSales > 0 ? pendingSales : 0}
            size="small"
            offset={[0, 0]}
          >
            <Button
              type="text"
              icon={getIcon()}
              onClick={isOnline && pendingSales > 0 ? handleManualSync : undefined}
              className="flex items-center"
              style={{ 
                color: getStatusColor(),
                cursor: isOnline && pendingSales > 0 ? 'pointer' : 'default'
              }}
              disabled={syncStatus === 'syncing'}
            >
              <span className="ml-1 text-sm font-medium">
                {getStatusText()}
              </span>
            </Button>
          </Badge>
        </Tooltip>
      </div>

      {/* Sync Progress Modal */}
      <Modal
        title={
          <div className="flex items-center">
            <CloudSyncOutlined className="mr-2 text-blue-500" />
            Syncing Offline Sales
          </div>
        }
        open={showSyncModal}
        footer={null}
        closable={false}
        centered
        width={400}
      >
        <div className="py-4">
          <Progress 
            percent={syncProgress} 
            status={syncProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <div className="mt-4 text-center">
            <Text type="secondary">
              {syncProgress < 100 
                ? `Syncing ${pendingSales} offline sales...`
                : 'Sync completed successfully!'
              }
            </Text>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default OfflineIndicator;
