"use client";

import { useCreateSupplierMutation, CreateSupplierDto } from "@/reduxRTK/services/supplierApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useSupplierCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a supplier
  const [createSupplier, { isLoading: isSubmitting }] = useCreateSupplierMutation();

  const createNewSupplier = async (supplierData: CreateSupplierDto) => {
    try {
      console.log("useSupplierCreate - Starting supplier creation with data:", supplierData);

      const result = await createSupplier(supplierData).unwrap() as ApiResponse<any>;
      console.log("useSupplierCreate - API response:", result);

      if (!result.success) {
        console.error("useSupplierCreate - API returned error:", result.message);
        throw new Error(result.message || "Failed to create supplier");
      }

      showMessage("success", "Supplier created successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Create supplier error:", error);
      showMessage("error", error.message || "Failed to create supplier");
      throw error;
    }
  };

  return {
    createSupplier: createNewSupplier,
    isSubmitting,
  };
};
