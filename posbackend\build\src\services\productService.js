"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteProductById = exports.updateProductById = exports.getProductByBarcode = exports.getProductById = exports.getAllProducts = exports.createProduct = void 0;
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const schema_2 = require("../validation/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
// REMOVED: Cache import to ensure fresh data
// import cache from "../config/lruCache";
// ✅ **Bulk Create Products**
const createProduct = async (requester, productsData) => {
    if (!Array.isArray(productsData) || productsData.length === 0) {
        throw new Error("Invalid input: Provide at least one product.");
    }
    // Validate and transform product data
    const validatedProducts = productsData.map((product) => {
        const validatedProduct = schema_2.productSchema.parse(product);
        return {
            ...validatedProduct,
            expiryDate: validatedProduct.expiryDate instanceof Date
                ? validatedProduct.expiryDate
                : validatedProduct.expiryDate
                    ? new Date(validatedProduct.expiryDate)
                    : null,
        };
    });
    // 🔹 Ensure the user is authorized for bulk creation
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "product");
    const createdBy = requester.id;
    // Format product data for insertion
    const formattedProducts = validatedProducts.map((product) => ({
        ...product,
        price: product.price.toString(),
        cost: product.cost.toString(),
        createdBy,
        createdAt: new Date(),
        imageUrl: product.imageUrl || null,
    }));
    // Bulk insert
    const newProducts = await db_1.postgresDb.insert(schema_1.products).values(formattedProducts).returning();
    if (!newProducts || newProducts.length === 0)
        throw new Error("Product creation failed.");
    return { products: newProducts };
};
exports.createProduct = createProduct;
// ✅ **Get All Products (Without Caching)**
const getAllProducts = async (requester, page = 1, limit = 10, searchTerm = "") => {
    const offset = (page - 1) * limit;
    // 🔹 Get the authorized query based on user role
    const query = (0, authorizeAction_1.authorizeAction)(requester, "getAll", "product");
    if (!query)
        throw new Error("Unauthorized: Unable to fetch products.");
    // Create search condition if search term is provided
    let searchCondition = undefined;
    if (searchTerm && searchTerm.trim() !== '') {
        // Use case-insensitive search with wildcards on both sides
        const searchPattern = `%${searchTerm.trim()}%`;
        // Create search condition for name, SKU, and barcode
        searchCondition = (0, drizzle_orm_1.or)((0, drizzle_orm_1.like)(schema_1.products.name, searchPattern), (0, drizzle_orm_1.like)(schema_1.products.sku || '', searchPattern), (0, drizzle_orm_1.like)(schema_1.products.barcode || '', searchPattern));
    }
    // For cashiers, we need to get products created by their admin
    let productsQuery;
    let countQuery;
    let adminId;
    // Handle cashier flow
    if (requester.role === "cashier") {
        // Find the cashier's admin ID
        const adminResult = await db_1.postgresDb
            .select({ adminId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        adminId = adminResult[0]?.adminId || undefined;
        if (!adminId) {
            return { total: 0, page, perPage: limit, products: [] };
        }
    }
    // 🔥 Optimize query building with common fields
    const commonSelectFields = {
        id: schema_1.products.id,
        name: schema_1.products.name,
        categoryId: schema_1.products.categoryId,
        sku: schema_1.products.sku,
        barcode: schema_1.products.barcode,
        price: schema_1.products.price,
        cost: schema_1.products.cost,
        stockQuantity: schema_1.products.stockQuantity,
        minStockLevel: schema_1.products.minStockLevel,
        expiryDate: schema_1.products.expiryDate,
        createdBy: schema_1.products.createdBy,
        createdAt: schema_1.products.createdAt,
        imageUrl: schema_1.products.imageUrl,
    };
    // Build queries based on role
    if (requester.role === "cashier" && adminId) {
        // Fetch products created by the cashier's admin
        const whereCondition = searchCondition
            ? (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.createdBy, adminId), searchCondition)
            : (0, drizzle_orm_1.eq)(schema_1.products.createdBy, adminId);
        productsQuery = db_1.postgresDb
            .select(commonSelectFields)
            .from(schema_1.products)
            .where(whereCondition)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.products.createdAt))
            .limit(limit)
            .offset(offset);
        // Count total products for pagination
        countQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.products)
            .where(whereCondition);
    }
    else if (requester.role === "admin") {
        // Admin can only see products they created
        const whereCondition = searchCondition
            ? (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.createdBy, requester.id), searchCondition)
            : (0, drizzle_orm_1.eq)(schema_1.products.createdBy, requester.id);
        productsQuery = db_1.postgresDb
            .select(commonSelectFields)
            .from(schema_1.products)
            .where(whereCondition)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.products.createdAt))
            .limit(limit)
            .offset(offset);
        // Count total products for pagination
        countQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.products)
            .where(whereCondition);
    }
    else {
        // Superadmin can see all products
        productsQuery = db_1.postgresDb
            .select(commonSelectFields)
            .from(schema_1.products)
            .where(searchCondition || (0, drizzle_orm_1.sql) `1=1`)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.products.createdAt))
            .limit(limit)
            .offset(offset);
        // Count total products for pagination
        countQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.products)
            .where(searchCondition || (0, drizzle_orm_1.sql) `1=1`);
    }
    // Execute queries in parallel
    const [productData, totalResult] = await Promise.all([
        productsQuery,
        countQuery
    ]);
    // Prepare the result
    const result = {
        total: totalResult[0]?.count || 0,
        page,
        perPage: limit,
        products: productData.length > 0 ? productData : [],
    };
    return result;
};
exports.getAllProducts = getAllProducts;
// ✅ **Get Product by ID (Without Caching)**
const getProductById = async (requester, id) => {
    // 🔹 Get the authorized query based on user role
    const query = (0, authorizeAction_1.authorizeAction)(requester, "getById", "product", id);
    if (!query)
        throw new Error("Unauthorized or invalid request.");
    let productQuery;
    let adminId;
    if (requester.role === "cashier") {
        // Find the cashier's admin ID
        const adminResult = await db_1.postgresDb
            .select({ adminId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        adminId = adminResult[0]?.adminId || undefined;
        if (!adminId) {
            throw new Error("Product not found or you don't have permission to view it.");
        }
        // Fetch product created by the cashier's admin
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.id, id), (0, drizzle_orm_1.eq)(schema_1.products.createdBy, adminId)))
            .limit(1);
    }
    else if (requester.role === "admin") {
        // Admin can only see products they created
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.id, id), (0, drizzle_orm_1.eq)(schema_1.products.createdBy, requester.id)))
            .limit(1);
    }
    else {
        // Superadmin can see all products
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, id))
            .limit(1);
    }
    // Execute query
    const productData = await productQuery;
    if (productData.length === 0) {
        throw new Error("Product not found or you don't have permission to view it.");
    }
    const product = productData[0];
    return product;
};
exports.getProductById = getProductById;
// ✅ **Get Product by Barcode (For Barcode Scanning)**
const getProductByBarcode = async (requester, barcode) => {
    // 🔹 Get the authorized query based on user role
    const query = (0, authorizeAction_1.authorizeAction)(requester, "getAll", "product");
    if (!query)
        throw new Error("Unauthorized or invalid request.");
    let productQuery;
    let adminId;
    if (requester.role === "cashier") {
        // Find the cashier's admin ID
        const adminResult = await db_1.postgresDb
            .select({ adminId: schema_1.users.createdBy })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, requester.id))
            .limit(1);
        adminId = adminResult[0]?.adminId || undefined;
        if (!adminId) {
            throw new Error("Product not found or you don't have permission to view it.");
        }
        // Search for product by barcode or SKU created by the cashier's admin
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.createdBy, adminId), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.products.barcode, barcode), (0, drizzle_orm_1.eq)(schema_1.products.sku, barcode))))
            .limit(1);
    }
    else if (requester.role === "admin") {
        // Admin can only see products they created
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.createdBy, requester.id), (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.products.barcode, barcode), (0, drizzle_orm_1.eq)(schema_1.products.sku, barcode))))
            .limit(1);
    }
    else {
        // Superadmin can see all products
        productQuery = db_1.postgresDb
            .select()
            .from(schema_1.products)
            .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.products.barcode, barcode), (0, drizzle_orm_1.eq)(schema_1.products.sku, barcode)))
            .limit(1);
    }
    // Execute query
    const productData = await productQuery;
    if (productData.length === 0) {
        return null; // Return null instead of throwing error for barcode scanning
    }
    const product = productData[0];
    return product;
};
exports.getProductByBarcode = getProductByBarcode;
// ✅ **Update Product**
const updateProductById = async (requester, productId, updateData) => {
    // 🔹 Use authorization utility
    const query = await (0, authorizeAction_1.authorizeAction)(requester, "update", "product", productId);
    if (!query)
        throw new Error("Unauthorized or invalid request.");
    // Convert `price` and `cost` to strings if they exist
    const sanitizedUpdateData = {
        ...updateData,
        price: updateData.price !== undefined ? updateData.price.toString() : undefined,
        cost: updateData.cost !== undefined ? updateData.cost.toString() : undefined,
        expiryDate: updateData.expiryDate ? new Date(updateData.expiryDate) : undefined,
        imageUrl: updateData.imageUrl !== undefined ? updateData.imageUrl : undefined,
    };
    const updatedProduct = await db_1.postgresDb
        .update(schema_1.products)
        .set(sanitizedUpdateData)
        .where((0, drizzle_orm_1.eq)(schema_1.products.id, productId))
        .returning();
    if (!updatedProduct || updatedProduct.length === 0) {
        throw new Error("Update failed: Product not found.");
    }
    // No cache invalidation needed
    return {
        message: "Product updated successfully.",
        updatedProduct: updatedProduct[0],
    };
};
exports.updateProductById = updateProductById;
// ✅ **Bulk Delete Products**
const deleteProductById = async (requester, ids) => {
    const productIds = Array.isArray(ids) ? ids : [ids];
    // 🔹 Ensure authorization for each ID
    await Promise.all(productIds.map(id => (0, authorizeAction_1.authorizeAction)(requester, "delete", "product", id)));
    const deletedProducts = await db_1.postgresDb
        .delete(schema_1.products)
        .where((0, drizzle_orm_1.inArray)(schema_1.products.id, productIds))
        .returning({ deletedId: schema_1.products.id });
    if (!deletedProducts || deletedProducts.length === 0) {
        throw new Error("Delete failed: Product(s) not found.");
    }
    // No cache invalidation needed
    return {
        deletedIds: deletedProducts.map((product) => product.deletedId),
    };
};
exports.deleteProductById = deleteProductById;
