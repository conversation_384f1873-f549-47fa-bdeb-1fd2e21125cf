"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userStores = exports.expenses = exports.expenseCategories = exports.payments = exports.stores = exports.receipts = exports.purchases = exports.userSuppliers = exports.suppliers = exports.stockAdjustments = exports.salesItems = exports.sales = exports.products = exports.userCategories = exports.categories = exports.users = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_orm_1 = require("drizzle-orm");
// ======================= USERS TABLE =======================
exports.users = (0, pg_core_1.pgTable)("users", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 100 }).notNull(),
    email: (0, pg_core_1.varchar)("email", { length: 100 }).unique().notNull(),
    phone: (0, pg_core_1.varchar)("phone", { length: 20 }).unique().notNull(),
    passwordHash: (0, pg_core_1.varchar)("password_hash").notNull(),
    role: (0, pg_core_1.varchar)("role", { length: 20 }).notNull(),
    paymentStatus: (0, pg_core_1.varchar)("payment_status", { length: 20 }).notNull().default("pending"),
    lastPaymentDate: (0, pg_core_1.timestamp)("last_payment_date").defaultNow(),
    nextPaymentDue: (0, pg_core_1.timestamp)("next_payment_due"), // ✅ NEW COLUMN ADDED
    createdBy: (0, pg_core_1.integer)("created_by"),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    roleCheck: (0, drizzle_orm_1.sql) `CHECK (${table.role} IN ('superadmin', 'admin', 'cashier'))`,
    paymentStatusCheck: (0, drizzle_orm_1.sql) `CHECK (${table.paymentStatus} IN ('pending', 'paid', 'overdue', 'inactive'))`,
    idxEmail: (0, pg_core_1.index)("idx_users_email").on(table.email),
    idxPhone: (0, pg_core_1.index)("idx_users_phone").on(table.phone),
    idxRoleCreatedAt: (0, pg_core_1.index)("idx_users_role_created_at").on(table.role, table.createdAt.desc()),
    idxCreatedByCreatedAt: (0, pg_core_1.index)("idx_users_created_by_created_at").on(table.createdBy, table.createdAt.desc()),
    idxPaymentStatus: (0, pg_core_1.index)("idx_users_payment_status").on(table.paymentStatus),
    idxLastPaymentDate: (0, pg_core_1.index)("idx_users_last_payment_date").on(table.lastPaymentDate),
    idxNextPaymentDue: (0, pg_core_1.index)("idx_users_next_payment_due").on(table.nextPaymentDue), // ✅ NEW INDEX ADDED
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));
// ======================= CATEGORIES TABLE =======================
exports.categories = (0, pg_core_1.pgTable)("categories", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 100 }).notNull(),
    description: (0, pg_core_1.text)("description"),
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "cascade" }),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxCategoryName: (0, pg_core_1.index)("idx_categories_name").on(table.name),
}));
exports.userCategories = (0, pg_core_1.pgTable)("user_categories", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").notNull().references(() => exports.users.id, { onDelete: "cascade" }),
    categoryId: (0, pg_core_1.integer)("category_id").notNull().references(() => exports.categories.id, { onDelete: "cascade" }),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxUserCategory: (0, pg_core_1.index)("idx_user_category").on(table.userId, table.categoryId),
    uniqueUserCategory: (0, pg_core_1.unique)("uq_user_category").on(table.userId, table.categoryId) // Prevent duplicate category links per user
}));
// ======================= PRODUCTS TABLE =======================
exports.products = (0, pg_core_1.pgTable)("products", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 255 }).notNull(),
    categoryId: (0, pg_core_1.integer)("category_id")
        .references(() => exports.categories.id, { onDelete: "cascade" })
        .notNull(),
    sku: (0, pg_core_1.varchar)("sku", { length: 50 }).unique(),
    barcode: (0, pg_core_1.varchar)("barcode", { length: 50 }).unique(),
    imageUrl: (0, pg_core_1.text)("image_url"), // Add this line to store Cloudinary image URLs
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
    cost: (0, pg_core_1.decimal)("cost", { precision: 10, scale: 2 }).notNull(),
    stockQuantity: (0, pg_core_1.integer)("stock_quantity").default(0).notNull(),
    minStockLevel: (0, pg_core_1.integer)("min_stock_level").default(0),
    expiryDate: (0, pg_core_1.timestamp)("expiry_date"),
    createdBy: (0, pg_core_1.integer)("created_by").notNull(), // ✅ Ensure products always have an owner
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxBarcode: (0, pg_core_1.index)("idx_products_barcode").on(table.barcode),
    idxSKU: (0, pg_core_1.index)("idx_products_sku").on(table.sku),
    idxCategory: (0, pg_core_1.index)("idx_products_category").on(table.categoryId),
    idxCreatedBy: (0, pg_core_1.index)("idx_products_created_by").on(table.createdBy), // ✅ Index for fast filtering
    checkStock: (0, drizzle_orm_1.sql) `CHECK (${table.stockQuantity} >= 0)`,
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE CASCADE`
}));
// ======================= SALES TABLE =======================
exports.sales = (0, pg_core_1.pgTable)("sales", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    totalAmount: (0, pg_core_1.decimal)("total_amount", { precision: 10, scale: 2 }).notNull(),
    paymentMethod: (0, pg_core_1.varchar)("payment_method", { length: 20 }).notNull(),
    transactionDate: (0, pg_core_1.timestamp)("transaction_date").defaultNow(),
    createdBy: (0, pg_core_1.integer)("created_by").notNull(),
    storeId: (0, pg_core_1.integer)("store_id"),
}, (table) => ({
    paymentMethodCheck: (0, drizzle_orm_1.sql) `CHECK (${table.paymentMethod} IN ('cash', 'card', 'paystack'))`,
    idxTransactionDate: (0, pg_core_1.index)("idx_sales_transaction_date").on(table.transactionDate),
    idxStoreId: (0, pg_core_1.index)("idx_sales_store_id").on(table.storeId),
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`,
    fkStoreId: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.storeId}) REFERENCES stores(id) ON DELETE SET NULL`
}));
// ======================= SALES ITEMS TABLE =======================
exports.salesItems = (0, pg_core_1.pgTable)("sales_items", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    saleId: (0, pg_core_1.integer)("sale_id").references(() => exports.sales.id, { onDelete: "cascade" }).notNull(),
    productId: (0, pg_core_1.integer)("product_id").references(() => exports.products.id, { onDelete: "cascade" }).notNull(),
    quantity: (0, pg_core_1.integer)("quantity").notNull(),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
}, (table) => ({
    idxSale: (0, pg_core_1.index)("idx_sales_items_sale").on(table.saleId),
    idxProduct: (0, pg_core_1.index)("idx_sales_items_product").on(table.productId)
}));
// ======================= STOCK ADJUSTMENTS TABLE =======================
exports.stockAdjustments = (0, pg_core_1.pgTable)("stock_adjustments", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    productId: (0, pg_core_1.integer)("product_id").references(() => exports.products.id, { onDelete: "cascade" }).notNull(),
    quantityChange: (0, pg_core_1.integer)("quantity_change").notNull(),
    reason: (0, pg_core_1.text)("reason").notNull(),
    adjustedBy: (0, pg_core_1.integer)("adjusted_by").references(() => exports.users.id, { onDelete: "set null" }),
    createdBy: (0, pg_core_1.integer)("created_by"),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxProductAdjustments: (0, pg_core_1.index)("idx_stock_adjustments_product").on(table.productId),
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));
// ======================= SUPPLIERS TABLE =======================
exports.suppliers = (0, pg_core_1.pgTable)("suppliers", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 255 }).notNull(),
    contactPerson: (0, pg_core_1.varchar)("contact_person", { length: 100 }),
    phone: (0, pg_core_1.varchar)("phone", { length: 20 }).unique().notNull(),
    email: (0, pg_core_1.varchar)("email", { length: 100 }), // Made optional by removing unique constraint
    address: (0, pg_core_1.text)("address"),
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "set null" }), // Track creator - FIXED: Removed notNull()
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxSupplierName: (0, pg_core_1.index)("idx_suppliers_name").on(table.name),
    idxSupplierPhone: (0, pg_core_1.index)("idx_suppliers_phone").on(table.phone),
    // Only index email if it exists
    idxSupplierEmail: (0, pg_core_1.index)("idx_suppliers_email").on(table.email)
}));
exports.userSuppliers = (0, pg_core_1.pgTable)("user_suppliers", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").notNull().references(() => exports.users.id, { onDelete: "cascade" }),
    supplierId: (0, pg_core_1.integer)("supplier_id").notNull().references(() => exports.suppliers.id, { onDelete: "cascade" }),
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "set null" }), // Track who linked them - FIXED: Removed notNull()
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxUserSupplier: (0, pg_core_1.index)("idx_user_supplier").on(table.userId, table.supplierId),
    uniqueUserSupplier: (0, pg_core_1.unique)("uq_user_supplier").on(table.userId, table.supplierId)
}));
// ======================= PURCHASES TABLE =======================
exports.purchases = (0, pg_core_1.pgTable)("purchases", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    supplierId: (0, pg_core_1.integer)("supplier_id").references(() => exports.suppliers.id, { onDelete: "set null" }),
    productId: (0, pg_core_1.integer)("product_id").references(() => exports.products.id, { onDelete: "cascade" }).notNull(),
    quantity: (0, pg_core_1.integer)("quantity").notNull(),
    costPrice: (0, pg_core_1.decimal)("cost_price", { precision: 10, scale: 2 }).notNull(),
    totalCost: (0, pg_core_1.decimal)("total_cost", { precision: 10, scale: 2 }).notNull(),
    purchaseDate: (0, pg_core_1.timestamp)("purchase_date").defaultNow(),
    purchasedBy: (0, pg_core_1.integer)("purchased_by").references(() => exports.users.id, { onDelete: "set null" }),
    createdBy: (0, pg_core_1.integer)("created_by"),
}, (table) => ({
    idxSupplier: (0, pg_core_1.index)("idx_purchases_supplier").on(table.supplierId),
    idxProduct: (0, pg_core_1.index)("idx_purchases_product").on(table.productId),
    idxPurchaseDate: (0, pg_core_1.index)("idx_purchases_date").on(table.purchaseDate),
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));
// ======================= RECEIPTS TABLE =======================
exports.receipts = (0, pg_core_1.pgTable)("receipts", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    saleId: (0, pg_core_1.integer)("sale_id").references(() => exports.sales.id, { onDelete: "cascade" }).notNull(),
    receiptUrl: (0, pg_core_1.text)("receipt_url").notNull(),
    createdBy: (0, pg_core_1.integer)("created_by"),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxReceiptSale: (0, pg_core_1.index)("idx_receipts_sale").on(table.saleId),
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE SET NULL`
}));
// ======================= STORES TABLE =======================
exports.stores = (0, pg_core_1.pgTable)("stores", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 255 }).notNull(),
    address: (0, pg_core_1.text)("address"),
    city: (0, pg_core_1.varchar)("city", { length: 100 }),
    state: (0, pg_core_1.varchar)("state", { length: 100 }),
    country: (0, pg_core_1.varchar)("country", { length: 100 }),
    phone: (0, pg_core_1.varchar)("phone", { length: 20 }),
    email: (0, pg_core_1.varchar)("email", { length: 100 }),
    logo: (0, pg_core_1.text)("logo"),
    website: (0, pg_core_1.varchar)("website", { length: 255 }),
    taxId: (0, pg_core_1.varchar)("tax_id", { length: 50 }),
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "set null" }),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)("updated_at").defaultNow()
}, (table) => ({
    idxStoreName: (0, pg_core_1.index)("idx_stores_name").on(table.name),
    idxStoreCreatedBy: (0, pg_core_1.index)("idx_stores_created_by").on(table.createdBy)
}));
// ======================= PAYMENTS TABLE =======================
exports.payments = (0, pg_core_1.pgTable)("payments", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").references(() => exports.users.id, { onDelete: "cascade" }).notNull(),
    amount: (0, pg_core_1.decimal)("amount", { precision: 10, scale: 2 }).notNull(),
    provider: (0, pg_core_1.varchar)("provider", { length: 20 }).notNull(),
    transactionId: (0, pg_core_1.varchar)("transaction_id", { length: 100 }).unique().notNull(),
    status: (0, pg_core_1.varchar)("status", { length: 20 }).notNull().default("pending"),
    paidAt: (0, pg_core_1.timestamp)("paid_at").defaultNow(),
    paystackReference: (0, pg_core_1.varchar)("paystack_reference", { length: 100 }).unique(),
    authorizationCode: (0, pg_core_1.varchar)("authorization_code", { length: 100 }),
    channel: (0, pg_core_1.varchar)("channel", { length: 20 }),
    currency: (0, pg_core_1.varchar)("currency", { length: 3 }).default("GHS"),
    subscriptionPeriod: (0, pg_core_1.integer)("subscription_period").default(1) // Number of months (1, 3, 12)
}, (table) => ({
    providerCheck: (0, drizzle_orm_1.sql) `CHECK (${table.provider} IN ('paystack'))`,
    statusCheck: (0, drizzle_orm_1.sql) `CHECK (${table.status} IN ('pending', 'successful', 'failed', 'abandoned'))`,
    idxUser: (0, pg_core_1.index)("idx_payments_user").on(table.userId),
    idxProvider: (0, pg_core_1.index)("idx_payments_provider").on(table.provider),
    idxStatus: (0, pg_core_1.index)("idx_payments_status").on(table.status)
}));
// ======================= EXPENSE CATEGORIES TABLE =======================
exports.expenseCategories = (0, pg_core_1.pgTable)("expense_categories", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.varchar)("name", { length: 100 }).notNull(),
    description: (0, pg_core_1.text)("description"),
    color: (0, pg_core_1.varchar)("color", { length: 7 }).default("#6B7280"), // Hex color for UI
    isDefault: (0, pg_core_1.boolean)("is_default").default(false), // System default categories
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "cascade" }),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxExpenseCategoryName: (0, pg_core_1.index)("idx_expense_categories_name").on(table.name),
    idxCreatedBy: (0, pg_core_1.index)("idx_expense_categories_created_by").on(table.createdBy)
}));
// ======================= EXPENSES TABLE =======================
exports.expenses = (0, pg_core_1.pgTable)("expenses", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    title: (0, pg_core_1.varchar)("title", { length: 255 }).notNull(),
    description: (0, pg_core_1.text)("description"),
    amount: (0, pg_core_1.decimal)("amount", { precision: 10, scale: 2 }).notNull(),
    categoryId: (0, pg_core_1.integer)("category_id")
        .references(() => exports.expenseCategories.id, { onDelete: "set null" }),
    expenseDate: (0, pg_core_1.timestamp)("expense_date").defaultNow(),
    paymentMethod: (0, pg_core_1.varchar)("payment_method", { length: 20 }).notNull(),
    receiptUrl: (0, pg_core_1.text)("receipt_url"), // Optional receipt/invoice attachment
    vendor: (0, pg_core_1.varchar)("vendor", { length: 255 }), // Who was paid
    isRecurring: (0, pg_core_1.boolean)("is_recurring").default(false),
    recurringFrequency: (0, pg_core_1.varchar)("recurring_frequency", { length: 20 }), // monthly, weekly, yearly
    tags: (0, pg_core_1.text)("tags"), // JSON array of tags for better categorization
    storeId: (0, pg_core_1.integer)("store_id").references(() => exports.stores.id, { onDelete: "set null" }),
    createdBy: (0, pg_core_1.integer)("created_by").notNull(),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)("updated_at").defaultNow()
}, (table) => ({
    paymentMethodCheck: (0, drizzle_orm_1.sql) `CHECK (${table.paymentMethod} IN ('cash', 'card', 'mobile_money', 'bank_transfer', 'cheque'))`,
    recurringFrequencyCheck: (0, drizzle_orm_1.sql) `CHECK (${table.recurringFrequency} IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly') OR ${table.recurringFrequency} IS NULL)`,
    idxExpenseDate: (0, pg_core_1.index)("idx_expenses_date").on(table.expenseDate),
    idxCategory: (0, pg_core_1.index)("idx_expenses_category").on(table.categoryId),
    idxCreatedBy: (0, pg_core_1.index)("idx_expenses_created_by").on(table.createdBy),
    idxStore: (0, pg_core_1.index)("idx_expenses_store").on(table.storeId),
    idxAmount: (0, pg_core_1.index)("idx_expenses_amount").on(table.amount),
    fkCreatedBy: (0, drizzle_orm_1.sql) `FOREIGN KEY (${table.createdBy}) REFERENCES users(id) ON DELETE CASCADE`
}));
// ======================= USER STORES TABLE =======================
exports.userStores = (0, pg_core_1.pgTable)("user_stores", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").notNull().references(() => exports.users.id, { onDelete: "cascade" }),
    storeId: (0, pg_core_1.integer)("store_id").notNull().references(() => exports.stores.id, { onDelete: "cascade" }),
    isDefault: (0, pg_core_1.boolean)("is_default").default(false).notNull(),
    createdBy: (0, pg_core_1.integer)("created_by").references(() => exports.users.id, { onDelete: "set null" }), // FIXED: Changed to SET NULL and removed notNull()
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow()
}, (table) => ({
    idxUserStore: (0, pg_core_1.index)("idx_user_store").on(table.userId, table.storeId),
    uniqueUserStore: (0, pg_core_1.unique)("uq_user_store").on(table.userId, table.storeId)
}));
