(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{44539:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,29029,23)),Promise.resolve().then(i.t.bind(i,91325,23)),Promise.resolve().then(i.bind(i,55037)),Promise.resolve().then(i.t.bind(i,84480,23)),Promise.resolve().then(i.bind(i,78534)),Promise.resolve().then(i.bind(i,14638)),Promise.resolve().then(i.t.bind(i,83520,23)),Promise.resolve().then(i.t.bind(i,30928,23)),Promise.resolve().then(i.bind(i,6349))},78534:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>n});var r=i(95155),s=i(14638),o=i(67113);function n(e){let{children:t}=e;return(0,r.jsx)(o.N,{defaultTheme:"light",attribute:"class",enableSystem:!1,forcedTheme:"light",children:(0,r.jsx)(s.SidebarProvider,{children:t})})}},14638:(e,t,i)=>{"use strict";i.d(t,{SidebarProvider:()=>a,V:()=>l});var r=i(95155),s=i(30555),o=i(12115);let n=(0,o.createContext)(null);function l(){let e=(0,o.useContext)(n);if(!e)throw Error("useSidebarContext must be used within a SidebarProvider");return e}function a(e){let{children:t,defaultOpen:i=!0}=e,l=(0,s.a)(),[a,d]=(0,o.useState)(l),[c,u]=(0,o.useState)(!l&&i),[h,f]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{a!==l&&(l?h?console.log("Switching to mobile - keeping sidebar state (manually toggled)"):(u(!1),console.log("Switching to mobile - closing sidebar (not manually toggled)")):(u(!0),f(!1),console.log("Switching to desktop - opening sidebar")),console.log("SidebarProvider - Screen size changed:",{prevIsMobile:a,isMobile:l,isOpen:!l||!!h&&c,wasManuallyToggled:h}),d(l))},[l,a,h,c]),(0,r.jsx)(n.Provider,{value:{state:c?"expanded":"collapsed",isOpen:c,setIsOpen:u,isMobile:l,toggleSidebar:function(){f(!0),setTimeout(()=>{u(e=>{let t=!e;return console.log("Sidebar manually toggled:",{newState:t,isMobile:l}),t})},50)}},children:t})}},29395:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var r=i(95155);i(12115);var s=i(72093),o=i(16419);let n=e=>{let{size:t="large",fullScreen:i=!1,tip:n}=e,l=(0,r.jsx)(o.A,{style:{fontSize:24},spin:!0});return i?(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80",children:(0,r.jsx)(s.A,{size:t,indicator:l,tip:n})}):(0,r.jsx)("div",{className:"flex flex-col h-full w-full items-center justify-center py-8",children:(0,r.jsx)(s.A,{size:t,indicator:l,tip:n})})}},30555:(e,t,i)=>{"use strict";i.d(t,{a:()=>s});var r=i(12115);function s(){let[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),i=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",i),()=>e.removeEventListener("change",i)},[]),!!e}},6349:(e,t,i)=>{"use strict";i.d(t,{default:()=>g});var r=i(95155),s=i(12115),o=i(83391),n=i(44971),l=i(90821),a=i(78034),d=i(72093),c=i(16419),u=i(29395),h=i(63065),f=i(7875);let g=e=>{let{children:t}=e,[i,g]=s.useState(!1);return s.useEffect(()=>{console.log("ClientProvider mounted"),g(!0),window.__FORCE_REFRESH_USER_DATA=async()=>{console.log("\uD83D\uDD04 Global force refresh user data triggered");try{var e;let{user:t,accessToken:i}=l.M.getState().auth;if(!t||!i){console.log("⚠️ No user or access token for refresh");return}l.M.dispatch(h.i$.util.invalidateTags(["User"])),l.M.dispatch(h.i$.util.resetApiState()),await new Promise(e=>setTimeout(e,500));let r=await l.M.dispatch(h.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in r&&(null===(e=r.data)||void 0===e?void 0:e.success)&&r.data.data){let e=r.data.data;console.log("✅ Global refresh: Fresh user data fetched:",{id:e.id,paymentStatus:e.paymentStatus}),l.M.dispatch((0,f.gV)({user:e,accessToken:i})),console.log("✅ Global refresh: Redux state updated")}else console.log("❌ Global refresh: Failed to fetch fresh user data")}catch(e){console.error("❌ Global refresh error:",e)}},console.log("✅ Global refresh function set up")},[]),(0,r.jsx)(o.Kq,{store:l.M,children:(0,r.jsx)(n.Q,{loading:(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-white/80",children:(0,r.jsx)(d.A,{size:"large",indicator:(0,r.jsx)(c.A,{style:{fontSize:24},spin:!0})})}),persistor:l.q,onBeforeLift:()=>{console.log("PersistGate - Before lift")},children:(0,r.jsx)(a.Z,{children:i?t:(0,r.jsx)(u.A,{fullScreen:!0})})})})}},84480:()=>{},83520:()=>{},30928:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[8378,2161,6754,1961,4831,2093,5037,8481,821,8441,1517,7358],()=>t(44539)),_N_E=e.O()}]);