"use client";
import React from "react";
import { Tag, Spin, Alert, Empty } from "antd";
import { useGetPaymentHistoryQuery } from "@/reduxRTK/services/paymentApi";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import dayjs from "dayjs";
import { Payment, ApiResponse, PaginatedPayments } from "@/types/payment";

interface PaymentHistoryProps {
  limit?: number;
}

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ limit = 10 }) => {
  // Use hook for responsive detection
  const isMobile = useResponsiveTable();

  const { data, error, isLoading } = useGetPaymentHistoryQuery({ page: 1, limit });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spin size="large" tip="Loading payment history..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description="Failed to load payment history. Please try again later."
        type="error"
        showIcon
      />
    );
  }

  // Handle data structure based on ApiResponse<PaginatedPayments> type
  const payments = data?.data?.payments || [];
  const total = data?.data?.total || 0;

  if (!payments || payments.length === 0) {
    return (
      <Empty
        description="No payment history found"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  const getProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case "mtn":
        return "yellow";
      case "vodafone":
        return "red";
      case "airteltigo":
        return "blue";
      case "paystack":
        return "green";
      default:
        return "default";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "successful":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
      <h2 className="text-xl font-bold mb-4 text-gray-800">Payment History</h2>

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="1fr 120px 100px 120px"
          minWidth="600px"
          className="mb-4"
        >
          {/* Mobile Headers */}
          <TableHeader>Date</TableHeader>
          <TableHeader>Amount</TableHeader>
          <TableHeader>Provider</TableHeader>
          <TableHeader>Status</TableHeader>

          {/* Mobile Rows */}
          {payments.map((payment: Payment) => (
            <TableRow key={payment.id}>
              <TableCell>
                {dayjs(payment.paidAt).format("MMM D, YYYY")}
              </TableCell>
              <TableCell>
                <span className="font-medium text-green-600">
                  ₵{typeof payment.amount === 'number' ? payment.amount.toFixed(2) : parseFloat(payment.amount || '0').toFixed(2)}
                </span>
              </TableCell>
              <TableCell>
                <Tag color={getProviderColor(payment.provider)}>
                  {payment.provider.toUpperCase()}
                </Tag>
              </TableCell>
              <TableCell>
                <Tag color={getStatusColor(payment.status)}>
                  {payment.status.toUpperCase()}
                </Tag>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto mb-4">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Provider
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Transaction ID
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {payments.map((payment: Payment) => (
                <tr key={payment.id}>
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {dayjs(payment.paidAt).format("MMM D, YYYY h:mm A")}
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <span className="font-medium text-green-600">
                      ₵{typeof payment.amount === 'number' ? payment.amount.toFixed(2) : parseFloat(payment.amount || '0').toFixed(2)}
                    </span>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <Tag color={getProviderColor(payment.provider)}>
                      {payment.provider.toUpperCase()}
                    </Tag>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <span className="font-mono text-xs">
                      {payment.transactionId}
                    </span>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <Tag color={getStatusColor(payment.status)}>
                      {payment.status.toUpperCase()}
                    </Tag>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination Info */}
      {total > limit && (
        <div className="text-sm text-gray-500 text-center mt-4">
          Showing {Math.min(limit, payments.length)} of {total} payments
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;
