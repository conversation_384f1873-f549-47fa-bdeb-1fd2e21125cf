"use client";

import React, { useState, useEffect } from "react";
import { PeriodPicker } from "@/components/period-picker";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import { PaymentsOverviewChart } from "./chart";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function ClientPaymentsOverview({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const [data, setData] = useState<{
    received: { x: unknown; y: number }[];
    due: { x: unknown; y: number }[];
  } | null>(null);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch data on the client side
    const fetchData = async () => {
      try {
        // Simulate API call with timeout
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data (similar to what would be returned by getPaymentsOverviewData)
        const mockData = {
          received: [
            { x: "Jan", y: 1500 },
            { x: "Feb", y: 2300 },
            { x: "Mar", y: 1900 },
            { x: "Apr", y: 2700 },
            { x: "May", y: 2200 },
            { x: "Jun", y: 3100 },
          ],
          due: [
            { x: "Jan", y: 500 },
            { x: "Feb", y: 700 },
            { x: "Mar", y: 400 },
            { x: "Apr", y: 900 },
            { x: "May", y: 600 },
            { x: "Jun", y: 800 },
          ],
        };

        setData(mockData);
      } catch (error) {
        console.error("Error fetching payments overview data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeFrame]);

  if (loading || !data) {
    return (
      <div className={cn(
        "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 border border-gray-200 min-h-[400px] flex items-center justify-center overflow-hidden",
        className,
      )}>
        <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 border border-gray-200 overflow-hidden",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-gray-800">
          Payments Overview
        </h2>

        <PeriodPicker defaultValue={timeFrame} sectionKey="payments_overview" />
      </div>

      <PaymentsOverviewChart data={data} />

      <dl className="grid divide-gray-200 text-center sm:grid-cols-2 sm:divide-x [&>div]:flex [&>div]:flex-col-reverse [&>div]:gap-1">
        <div className="border-gray-200 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-gray-800">
            GH₵{standardFormat(data.received.reduce((acc, { y }) => acc + y, 0))}
          </dt>
          <dd className="font-medium text-gray-600">Received Amount</dd>
        </div>

        <div>
          <dt className="text-xl font-bold text-gray-800">
            GH₵{standardFormat(data.due.reduce((acc, { y }) => acc + y, 0))}
          </dt>
          <dd className="font-medium text-gray-600">Due Amount</dd>
        </div>
      </dl>
    </div>
  );
}
