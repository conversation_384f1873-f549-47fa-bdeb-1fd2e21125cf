"use client";

import React from "react";
import { Modal, Descriptions, Tag, <PERSON><PERSON>, Spin } from "antd";
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  WarningOutlined
} from "@ant-design/icons";
import { User, PaymentStatus, UserRole, ApiResponse } from "@/types/user";
import dayjs from "dayjs";
import { useGetUserByIdQuery } from "@/reduxRTK/services/authApi";
import { LoadingOutlined } from "@ant-design/icons";
import { formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";

interface UserDetailProps {
  visible: boolean;
  onCancel: () => void;
  userId: number | null;
}

const UserDetail: React.FC<UserDetailProps> = ({
  visible,
  onCancel,
  userId,
}) => {
  // Fetch user details when userId is available
  const { data: response, isLoading } = useGetUserByIdQuery(userId || 0, {
    skip: !userId,
  }) as {
    data?: ApiResponse<User>;
    isLoading: boolean
  };

  // Extract user from response
  const user = response?.data;

  const getPaymentStatusTag = (status: PaymentStatus) => {
    switch (status) {
      case "paid":
        return <Tag icon={<CheckCircleOutlined />} color="success">Paid</Tag>;
      case "pending":
        return <Tag icon={<ClockCircleOutlined />} color="warning">Pending</Tag>;
      case "overdue":
        return <Tag icon={<WarningOutlined />} color="error">Overdue</Tag>;
      case "inactive":
        return <Tag icon={<StopOutlined />} color="default">Inactive</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const getRoleTag = (role: UserRole) => {
    switch (role) {
      case "superadmin":
        return <Tag color="purple">Super Admin</Tag>;
      case "admin":
        return <Tag color="blue">Admin</Tag>;
      case "cashier":
        return <Tag color="green">Cashier</Tag>;
      default:
        return <Tag color="default">{role}</Tag>;
    }
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <Modal
      title="User Details"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          Close
        </Button>,
      ]}
      width={600}
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      ) : user ? (
        <Descriptions bordered column={1} className="mt-4">
          <Descriptions.Item label={<span><UserOutlined /> Name</span>}>
            {(user as User)?.name}
          </Descriptions.Item>

          <Descriptions.Item label={<span><MailOutlined /> Email</span>}>
            {(user as User)?.email}
          </Descriptions.Item>

          <Descriptions.Item label={<span><PhoneOutlined /> Phone</span>}>
            {formatPhoneNumberForDisplay((user as User)?.phone)}
          </Descriptions.Item>

          <Descriptions.Item label="Role">
            {getRoleTag((user as User)?.role)}
          </Descriptions.Item>

          <Descriptions.Item label="Payment Status">
            {getPaymentStatusTag((user as User)?.paymentStatus)}
          </Descriptions.Item>

          <Descriptions.Item label={<span><CalendarOutlined /> Created At</span>}>
            {formatDate((user as User)?.createdAt)}
          </Descriptions.Item>

          <Descriptions.Item label="Last Payment Date">
            {formatDate((user as User)?.lastPaymentDate)}
          </Descriptions.Item>

          <Descriptions.Item label="Next Payment Due">
            {formatDate((user as User)?.nextPaymentDue)}
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <div className="text-center py-8">User not found</div>
      )}
    </Modal>
  );
};

export default UserDetail;



