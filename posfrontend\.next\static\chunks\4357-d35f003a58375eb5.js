"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4357],{44287:(e,t,s)=>{s.d(t,{Y:()=>J});var a=s(95155),n=s(5565),l=s(48173),i=s.n(l),r=s(76046),o=s(14638);function c(e){return(0,a.jsxs)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{d:"M3.5625 6C3.5625 5.58579 3.89829 5.25 4.3125 5.25H20.3125C20.7267 5.25 21.0625 5.58579 21.0625 6C21.0625 6.41421 20.7267 6.75 20.3125 6.75L4.3125 6.75C3.89829 6.75 3.5625 6.41422 3.5625 6Z"}),(0,a.jsx)("path",{d:"M3.5625 18C3.5625 17.5858 3.89829 17.25 4.3125 17.25L20.3125 17.25C20.7267 17.25 21.0625 17.5858 21.0625 18C21.0625 18.4142 20.7267 18.75 20.3125 18.75L4.3125 18.75C3.89829 18.75 3.5625 18.4142 3.5625 18Z"}),(0,a.jsx)("path",{d:"M4.3125 11.25C3.89829 11.25 3.5625 11.5858 3.5625 12C3.5625 12.4142 3.89829 12.75 4.3125 12.75L20.3125 12.75C20.7267 12.75 21.0625 12.4142 21.0625 12C21.0625 11.5858 20.7267 11.25 20.3125 11.25L4.3125 11.25Z"})]})}function d(e){return(0,a.jsx)("svg",{width:22,height:22,viewBox:"0 0 22 22",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.551 7.728a.687.687 0 01.895 0l6.417 5.5a.687.687 0 11-.895 1.044l-5.97-5.117-5.969 5.117a.687.687 0 01-.894-1.044l6.416-5.5z"})})}var h=s(12115),u=s(21567);let f=(0,h.createContext)(null);function m(){let e=(0,h.useContext)(f);if(!e)throw Error("useDropdownContext must be used within a Dropdown");return e}function g(e){let{children:t,isOpen:s,setIsOpen:n}=e,l=(0,h.useRef)(null);function i(){n(!1)}return(0,h.useEffect)(()=>{s?(l.current=document.activeElement,document.body.style.pointerEvents="none"):(document.body.style.removeProperty("pointer-events"),setTimeout(()=>{l.current&&l.current.focus()},0))},[s]),(0,a.jsx)(f.Provider,{value:{isOpen:s,handleOpen:function(){n(!0)},handleClose:i},children:(0,a.jsx)("div",{className:"relative",onKeyDown:e=>{"Escape"===e.key&&i()},children:t})})}function x(e){let{children:t,align:s="center",className:n}=e,{isOpen:l,handleClose:i}=m(),r=function(e){let t=(0,h.useRef)(null);return(0,h.useEffect)(()=>{function s(s){t.current&&!t.current.contains(s.target)&&e()}return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[e,t]),t}(()=>{l&&i()});return l?(0,a.jsx)("div",{ref:r,role:"menu","aria-orientation":"vertical",className:(0,u.cn)("fade-in-0 zoom-in-95 pointer-events-auto absolute z-99 mt-2 min-w-[8rem] origin-top-right rounded-lg",{"animate-in right-0":"end"===s,"left-0":"start"===s,"left-1/2 -translate-x-1/2":"center"===s},n),children:t}):null}function p(e){let{children:t,className:s}=e,{handleOpen:n,isOpen:l}=m();return(0,a.jsx)("button",{className:s,onClick:n,"aria-expanded":l,"aria-haspopup":"menu","data-state":l?"open":"closed",children:t})}function v(e){return(0,a.jsx)("svg",{width:20,height:20,viewBox:"0 0 18 18",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 .938a3.562 3.562 0 100 7.124A3.562 3.562 0 009 .938zM6.562 4.5a2.437 2.437 0 114.875 0 2.437 2.437 0 01-4.875 0zM9 9.188c-1.735 0-3.334.394-4.518 1.06-1.167.657-2.045 1.652-2.045 2.877v.076c0 .872-.001 1.965.958 2.747.472.384 1.132.657 2.025.838.894.181 2.06.276 3.58.276s2.685-.095 3.58-.276c.893-.18 1.553-.454 2.025-.838.96-.782.958-1.875.957-2.747v-.076c0-1.226-.877-2.22-2.044-2.877-1.184-.666-2.783-1.06-4.518-1.06zm-5.438 3.937c0-.639.467-1.331 1.471-1.896.987-.555 2.388-.916 3.967-.916 1.579 0 2.98.36 3.967.916 1.004.565 1.47 1.258 1.47 1.896 0 .98-.03 1.533-.542 1.95-.278.227-.743.448-1.538.609-.793.16-1.876.254-3.357.254-1.48 0-2.564-.094-3.357-.255-.795-.16-1.26-.381-1.538-.608-.512-.417-.543-.97-.543-1.95z"})})}function w(e){return(0,a.jsxs)("svg",{width:20,height:20,viewBox:"0 0 18 18",fill:"currentColor",...e,children:[(0,a.jsxs)("g",{clipPath:"url(#clip0_7095_11691)",children:[(0,a.jsx)("path",{d:"M11.209.938c-1.026 0-1.852 0-2.503.087-.675.09-1.243.285-1.695.736-.393.394-.592.878-.697 1.446-.101.553-.12 1.229-.125 2.04a.562.562 0 101.125.006c.005-.82.026-1.401.107-1.842.078-.426.203-.672.386-.854.207-.208.499-.343 1.05-.417.566-.076 1.317-.078 2.393-.078H12c1.077 0 1.828.002 2.394.078.55.074.842.21 1.05.417.207.207.342.499.416 1.05.077.566.078 1.316.078 2.393v6c0 1.077-.002 1.827-.078 2.394-.074.55-.209.842-.417 1.05-.207.207-.499.342-1.049.416-.566.076-1.317.078-2.394.078h-.75c-1.076 0-1.827-.002-2.394-.078-.55-.074-.842-.21-1.05-.417-.182-.182-.307-.428-.385-.854-.081-.44-.102-1.022-.107-1.842a.563.563 0 00-1.125.006c.004.811.024 1.487.125 2.04.105.568.304 1.052.697 1.446.452.451 1.02.645 1.695.736.65.087 1.477.087 2.503.087h.832c1.026 0 1.853 0 2.503-.087.675-.09 1.243-.285 1.695-.736.451-.452.645-1.02.736-1.695.088-.65.088-1.477.088-2.503V5.96c0-1.026 0-1.853-.088-2.503-.09-.675-.285-1.243-.736-1.695-.452-.451-1.02-.645-1.695-.736-.65-.088-1.477-.088-2.503-.087h-.832z"}),(0,a.jsx)("path",{d:"M11.25 8.438a.562.562 0 110 1.124H3.02l1.471 1.26a.563.563 0 01-.732.855l-2.625-2.25a.562.562 0 010-.854l2.625-2.25a.562.562 0 11.732.854l-1.47 1.26h8.229z"})]}),(0,a.jsx)("defs",{children:(0,a.jsx)("clipPath",{id:"clip0_7095_11691",children:(0,a.jsx)("rect",{width:18,height:18,rx:5})})})]})}var b=s(83391),y=s(63065),j=s(7875);let S=()=>{let e=(0,b.wA)(),[t,s]=(0,h.useState)({loading:!1,error:""}),[a]=(0,y.Qg)();return{logout:async t=>{s({loading:!0,error:""});try{let n=await a({email:t}).unwrap();n.success?(e((0,j.lM)()),window.location.href="/",s({loading:!1,error:""})):s({loading:!1,error:n.message})}catch(e){var n;s({loading:!1,error:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)||e.message||"Logout failed"})}},...t}},N=()=>(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"h-6 w-6 text-gray-500",children:[(0,a.jsx)("circle",{cx:"12",cy:"8",r:"4"}),(0,a.jsx)("path",{d:"M4 20c0-4 4-4 8-4s8 4 8 4"})]});function z(){let[e,t]=(0,h.useState)(!1),s=(0,b.d4)(e=>e.auth.user),n=(0,r.useRouter)(),{logout:l}=S(),o=async()=>{s&&s.email&&(await l(s.email),n.replace("/"))};return(0,a.jsxs)(g,{isOpen:e,setIsOpen:t,children:[(0,a.jsxs)(p,{className:"rounded align-middle outline-none ring-primary ring-offset-2 focus-visible:ring-1",children:[(0,a.jsx)("span",{className:"sr-only",children:"My Account"}),(0,a.jsxs)("figure",{className:"flex items-center gap-3",children:[(0,a.jsx)(N,{}),(0,a.jsxs)("figcaption",{className:"flex items-center gap-1 font-medium text-dark max-[1024px]:sr-only",children:[(0,a.jsx)("span",{children:(null==s?void 0:s.name.split(" ")[0])||"John Smith"}),(0,a.jsx)(d,{"aria-hidden":!0,className:(0,u.cn)("rotate-180 transition-transform",e&&"rotate-0"),strokeWidth:1.5})]})]})]}),(0,a.jsxs)(x,{className:"border border-stroke bg-white shadow-md min-[230px]:min-w-[17.5rem]",align:"end",children:[(0,a.jsx)("h2",{className:"sr-only",children:"User information"}),(0,a.jsxs)("figure",{className:"flex items-center gap-2.5 px-5 py-3.5",children:[(0,a.jsx)(N,{}),(0,a.jsxs)("figcaption",{className:"space-y-1 text-base font-medium",children:[(0,a.jsx)("div",{className:"mb-2 leading-none text-dark",children:(null==s?void 0:s.name)||"user"}),(0,a.jsx)("div",{className:"leading-none text-gray-6",children:(null==s?void 0:s.email)||"<EMAIL>"})]})]}),(0,a.jsx)("hr",{className:"border-[#E8E8E8]"}),(0,a.jsx)("div",{className:"p-2 text-base text-[#4B5563] [&>*]:cursor-pointer",children:(0,a.jsxs)(i(),{href:"/dashboard/profile",onClick:e=>{e.preventDefault(),t(!1),n.push("/dashboard/profile")},className:"flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark",children:[(0,a.jsx)(v,{}),(0,a.jsx)("span",{className:"mr-auto text-base font-medium",children:"View profile"})]})}),(0,a.jsx)("hr",{className:"border-[#E8E8E8]"}),(0,a.jsx)("div",{className:"p-2 text-base text-[#4B5563]",children:(0,a.jsxs)("button",{className:"flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark",onClick:o,children:[(0,a.jsx)(w,{}),(0,a.jsx)("span",{className:"text-base font-medium",children:"Log out"})]})})]})]})}var C=s(73937),M=s(6457),E=s(97838),A=s(43316),k=s(46102),R=s(21703),D=s(85407);let I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832.6 191.4c-84.6-84.6-221.5-84.6-306 0l-96.9 96.9 51 51 96.9-96.9c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204l-96.9 96.9 51.1 51.1 96.9-96.9c84.4-84.6 84.4-221.5-.1-306.1zM446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l96.9-96.9-51.1-51.1-96.9 96.9c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l96.9-96.9-51-51-96.8 97zM260.3 209.4a8.03 8.03 0 00-11.3 0L209.4 249a8.03 8.03 0 000 11.3l554.4 554.4c3.1 3.1 8.2 3.1 11.3 0l39.6-39.6c3.1-3.1 3.1-8.2 0-11.3L260.3 209.4z"}}]},name:"disconnect",theme:"outlined"};var P=s(84021),O=h.forwardRef(function(e,t){return h.createElement(P.A,(0,D.A)({},e,{ref:t,icon:I}))}),L=s(76170),V=s(75218),B=s(75365);let _={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M811.4 368.9C765.6 248 648.9 162 512.2 162S258.8 247.9 213 368.8C126.9 391.5 63.5 470.2 64 563.6 64.6 668 145.6 752.9 247.6 762c4.7.4 8.7-3.3 8.7-8v-60.4c0-4-3-7.4-7-7.9-27-3.4-52.5-15.2-72.1-34.5-24-23.5-37.2-55.1-37.2-88.6 0-28 9.1-54.4 26.2-76.4 16.7-21.4 40.2-36.9 66.1-43.7l37.9-10 13.9-36.7c8.6-22.8 20.6-44.2 35.7-63.5 14.9-19.2 32.6-36 52.4-50 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.3c19.9 14 37.5 30.8 52.4 50 15.1 19.3 27.1 40.7 35.7 63.5l13.8 36.6 37.8 10c54.2 14.4 92.1 63.7 92.1 120 0 33.6-13.2 65.1-37.2 88.6-19.5 19.2-44.9 31.1-71.9 34.5-4 .5-6.9 3.9-6.9 7.9V754c0 4.7 4.1 8.4 8.8 8 101.7-9.2 182.5-94 183.2-198.2.6-93.4-62.7-172.1-148.6-194.9z"}},{tag:"path",attrs:{d:"M376.9 656.4c1.8-33.5 15.7-64.7 39.5-88.6 25.4-25.5 60-39.8 96-39.8 36.2 0 70.3 14.1 96 39.8 1.4 1.4 2.7 2.8 4.1 4.3l-25 19.6a8 8 0 003 14.1l98.2 24c5 1.2 9.9-2.6 9.9-7.7l.5-101.3c0-6.7-7.6-10.5-12.9-6.3L663 532.7c-36.6-42-90.4-68.6-150.5-68.6-107.4 0-195 85.1-199.4 191.7-.2 4.5 3.4 8.3 8 8.3H369c4.2-.1 7.7-3.4 7.9-7.7zM703 664h-47.9c-4.2 0-7.7 3.3-8 7.6-1.8 33.5-15.7 64.7-39.5 88.6-25.4 25.5-60 39.8-96 39.8-36.2 0-70.3-14.1-96-39.8-1.4-1.4-2.7-2.8-4.1-4.3l25-19.6a8 8 0 00-3-14.1l-98.2-24c-5-1.2-9.9 2.6-9.9 7.7l-.4 101.4c0 6.7 7.6 10.5 12.9 6.3l23.2-18.2c36.6 42 90.4 68.6 150.5 68.6 107.4 0 195-85.1 199.4-191.7.2-4.5-3.4-8.3-8-8.3z"}}]},name:"cloud-sync",theme:"outlined"};var H=h.forwardRef(function(e,t){return h.createElement(P.A,(0,D.A)({},e,{ref:t,icon:_}))}),T=s(52080),F=s(88908);let{Text:Q}=C.A,U=e=>{let{className:t=""}=e,[s,n]=(0,h.useState)(!0),[l,i]=(0,h.useState)(0),[r,o]=(0,h.useState)("idle"),[c,d]=(0,h.useState)(!1),[u,f]=(0,h.useState)(0),m=(0,h.useCallback)(async()=>{if(s&&"syncing"!==r)try{o("syncing"),await T.zy.forceSyncNow(),o("idle");let e=await F.V.getOfflineSales("pending"),t=await F.V.getOfflineSales("failed");i(e.length+t.length)}catch(e){console.error("Auto-sync failed:",e),o("error")}},[s,r]);(0,h.useEffect)(()=>{let e=()=>{n(!0),m()},t=()=>{n(!1)};return n(navigator.onLine),window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[m]),(0,h.useEffect)(()=>{let e=async()=>{try{let e=await F.V.getOfflineSales("pending"),t=await F.V.getOfflineSales("failed");i(e.length+t.length)}catch(e){console.error("Failed to get pending sales:",e)}};e();let t=setInterval(e,3e4);return()=>clearInterval(t)},[]);let g=async()=>{if(s){d(!0),f(0);try{o("syncing");let e=setInterval(()=>{f(e=>Math.min(e+10,90))},200);await T.zy.forceSyncNow(),clearInterval(e),f(100),o("idle");let t=await F.V.getOfflineSales("pending"),s=await F.V.getOfflineSales("failed");i(t.length+s.length),setTimeout(()=>{d(!1),f(0)},1e3)}catch(e){console.error("Manual sync failed:",e),o("error"),d(!1),f(0)}}},x=()=>s?"syncing"===r?"Syncing...":"error"===r?"Sync Error":l>0?"".concat(l," Pending"):"Online & Synced":"Offline Mode";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex items-center space-x-2 ".concat(t),children:(0,a.jsx)(M.A,{title:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Status:"})," ",x()]}),l>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Pending Sales:"})," ",l]}),!s&&(0,a.jsx)("div",{className:"text-yellow-300",children:"Sales will sync when connection returns"}),s&&l>0&&(0,a.jsx)("div",{className:"text-blue-300",children:"Click to sync now"})]}),children:(0,a.jsx)(E.A,{count:l>0?l:0,size:"small",offset:[0,0],children:(0,a.jsx)(A.Ay,{type:"text",icon:s?"syncing"===r?(0,a.jsx)(L.A,{spin:!0}):"error"===r?(0,a.jsx)(V.A,{}):(0,a.jsx)(B.A,{}):(0,a.jsx)(O,{}),onClick:s&&l>0?g:void 0,className:"flex items-center",style:{color:s?"syncing"===r?"#1890ff":"error"===r||l>0?"#faad14":"#52c41a":"#ff4d4f",cursor:s&&l>0?"pointer":"default"},disabled:"syncing"===r,children:(0,a.jsx)("span",{className:"ml-1 text-sm font-medium",children:x()})})})})}),(0,a.jsx)(k.A,{title:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(H,{className:"mr-2 text-blue-500"}),"Syncing Offline Sales"]}),open:c,footer:null,closable:!1,centered:!0,width:400,children:(0,a.jsxs)("div",{className:"py-4",children:[(0,a.jsx)(R.A,{percent:u,status:100===u?"success":"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(Q,{type:"secondary",children:u<100?"Syncing ".concat(l," offline sales..."):"Sync completed successfully!"})})]})})]})};function J(){let e=(0,r.useRouter)(),{toggleSidebar:t,isMobile:s,setIsOpen:l}=(0,o.V)();return(0,a.jsxs)("header",{className:"w-full flex items-center justify-between border-b border-stroke bg-white px-4 py-5 shadow-1 md:px-5 2xl:px-10",children:[s&&(0,a.jsxs)("button",{onClick:e=>{e.stopPropagation(),console.log("Toggle sidebar clicked from header"),l(!0)},className:"rounded-lg border px-1.5 py-1","aria-label":"Open sidebar menu",children:[(0,a.jsx)(c,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]}),s&&(0,a.jsx)(i(),{href:"/dashboard",className:"ml-2 mobile-logo max-[430px]:hidden min-[375px]:ml-4",onClick:t=>{window.location.pathname.includes("/dashboard/users")||(t.preventDefault(),e.push("/dashboard"))},children:(0,a.jsx)(n.default,{src:"/images/logo/logo-small.png",width:36,height:36,alt:"Company logo",role:"presentation",className:"object-contain"})}),(0,a.jsxs)("div",{className:"max-xl:hidden",children:[(0,a.jsx)("h4",{className:"mb-0.5 text-heading-5 font-bold text-dark",children:"Dashboard"}),(0,a.jsx)("p",{className:"font-medium",children:"POS System | Inventory Management"})]}),(0,a.jsxs)("div",{className:"flex flex-1 items-center justify-end gap-2 min-[375px]:gap-4",children:[(0,a.jsx)(U,{className:"shrink-0"}),(0,a.jsx)("div",{className:"shrink-0",children:(0,a.jsx)(z,{})})]})]})}},40158:(e,t,s)=>{s.d(t,{R:()=>g});var a=s(95155),n=s(23102),l=s(21567),i=s(48173),r=s.n(i),o=s(76046),c=s(12115),d=s(60569),h=s(59674),u=s(12175),f=s(14638),m=s(83391);function g(){let e=(0,o.usePathname)(),t=(0,o.useRouter)(),{setIsOpen:s,isOpen:i,toggleSidebar:g,isMobile:x}=(0,f.V)(),[p,v]=(0,c.useState)([]),{user:w}=(0,m.d4)(e=>e.auth),b=w?(0,d.D)(w.role):d.R,[y,j]=(0,c.useState)(e);(0,c.useEffect)(()=>{y!==e&&""!==y&&x&&i&&(console.log("MobileSidebar - Path changed, closing sidebar on mobile",{from:y,to:e}),setTimeout(()=>{s(!1)},100)),j(e)},[e,x,i,s,y]);let S=e=>{v(t=>t.includes(e)?[]:[e])};return((0,c.useEffect)(()=>{b.some(t=>t.items.some(t=>t.items.some(s=>{if(s.url===e)return p.includes(t.title)||S(t.title),!0})))},[e,b,p]),x&&i)?(console.log("Mobile sidebar is open",{isMobile:x,isOpen:i}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 transition-opacity duration-300",onClick:()=>{console.log("Overlay clicked, closing sidebar with delay"),setTimeout(()=>{s(!1)},50)},"aria-hidden":"true"}),(0,a.jsx)("aside",{className:"fixed bottom-0 left-0 top-0 z-50 h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out","aria-label":"Mobile navigation",children:(0,a.jsxs)("div",{className:"flex h-full flex-col py-10 pl-[25px] pr-[7px]",children:[(0,a.jsxs)("div",{className:"relative pr-4.5",children:[(0,a.jsx)(r(),{href:"/dashboard",onClick:e=>{if(window.location.pathname.includes("/dashboard/users")){g();return}e.preventDefault(),g(),t.push("/dashboard")},className:"px-0 py-2.5 min-[850px]:py-0",children:(0,a.jsx)(n.g,{})}),(0,a.jsxs)("button",{onClick:e=>{e.stopPropagation(),console.log("Mobile back button clicked, closing sidebar with delay"),setTimeout(()=>{s(!1)},50)},className:"mobile-back-button absolute right-0 top-1/2 -translate-y-1/2 text-right","aria-label":"Close sidebar",children:[(0,a.jsx)("span",{className:"sr-only",children:"Close Menu"}),(0,a.jsx)(h.A6,{className:"ml-auto size-7"})]})]}),(0,a.jsx)("div",{className:"custom-scrollbar mt-6 flex-1 overflow-y-auto pr-3",children:b.map(t=>(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"mb-5 text-sm font-medium text-gray-600",children:t.label}),(0,a.jsx)("nav",{role:"navigation","aria-label":t.label,children:(0,a.jsx)("ul",{className:"space-y-2",children:t.items.map(t=>(0,a.jsx)("li",{children:t.items.length?(0,a.jsxs)("div",{children:[(0,a.jsxs)(u.D,{isActive:t.items.some(t=>{let{url:s}=t;return s===e}),onClick:()=>S(t.title),children:[(0,a.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,a.jsx)("span",{children:t.title}),(0,a.jsx)(h.rX,{className:(0,l.cn)("ml-auto rotate-180 transition-transform duration-200",p.includes(t.title)&&"rotate-0"),"aria-hidden":"true"})]}),p.includes(t.title)&&(0,a.jsx)("ul",{className:"ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2",role:"menu",children:t.items.map(t=>(0,a.jsx)("li",{role:"none",children:(0,a.jsx)(u.D,{as:"link",href:t.url,isActive:e===t.url,children:(0,a.jsx)("span",{children:t.title})})},t.title))})]}):(()=>{let s="url"in t&&t.url?t.url:"/"+t.title.toLowerCase().split(" ").join("-");return(0,a.jsxs)(u.D,{className:"flex items-center gap-3 py-3",as:"link",href:s,isActive:e===s,children:[(0,a.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,a.jsx)("span",{children:t.title})]})})()},t.title))})})]},t.label))})]})})]})):null}},60569:(e,t,s)=>{s.d(t,{D:()=>u,R:()=>f});var a=s(59674);let n=[{title:"Dashboard",url:"/dashboard",icon:a.fA,items:[]},{title:"Profile",url:"/dashboard/profile",icon:a.KJ,items:[]}];a.Sl;let l={title:"Inventory Menu",icon:a.Ez,items:[{title:"Categories",url:"/dashboard/categories"},{title:"Products",url:"/dashboard/products"},{title:"Suppliers",url:"/dashboard/suppliers"},{title:"Purchases",url:"/dashboard/purchases"}]},i={title:"Stores",url:"/dashboard/stores",icon:a.U1,items:[]},r={title:"Sales",url:"/dashboard/sales",icon:a.aJ,items:[]},o={title:"Receipts",url:"/dashboard/receipts",icon:a.ww,items:[]},c={title:"Expenses Menu",icon:a.sy,items:[{title:"Expense Categories",url:"/dashboard/expense-categories"},{title:"Expenses",url:"/dashboard/expenses"}]},d={title:"Users",url:"/dashboard/users",icon:a.KJ,items:[]},h={title:"Reports",icon:a.Bi,items:[{title:"Sales Reports",url:"/dashboard/reports/sales"},{title:"Inventory Reports",url:"/dashboard/reports/inventory"}]};a.Vv,a.Vv;let u=e=>{let t=[...n];return"superadmin"===e?t=[...t,d,i]:"admin"===e?t=[...t,l,r,o,c,h,d]:"cashier"===e&&(t=[...t,r,o]),[{label:"MAIN MENU",items:t}]},f=[{label:"MAIN MENU",items:[...n,l,r,o,c,d]}]},59674:(e,t,s)=>{s.d(t,{A6:()=>c,Bi:()=>g,Ez:()=>d,KJ:()=>r,Sl:()=>o,U1:()=>u,Vv:()=>i,aJ:()=>h,fA:()=>l,rX:()=>n,sy:()=>m,ww:()=>f});var a=s(95155);function n(e){return(0,a.jsx)("svg",{width:16,height:8,viewBox:"0 0 16 8",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.553.728a.687.687 0 01.895 0l6.416 5.5a.688.688 0 01-.895 1.044L8 2.155 2.03 7.272a.688.688 0 11-.894-1.044l6.417-5.5z"})})}function l(e){return(0,a.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{d:"M9 17.25a.75.75 0 000 1.5h6a.75.75 0 000-1.5H9z"}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1.25c-.725 0-1.387.2-2.11.537-.702.327-1.512.81-2.528 1.415l-1.456.867c-1.119.667-2.01 1.198-2.686 1.706C2.523 6.3 2 6.84 1.66 7.551c-.342.711-.434 1.456-.405 2.325.029.841.176 1.864.36 3.146l.293 2.032c.237 1.65.426 2.959.707 3.978.29 1.05.702 1.885 1.445 2.524.742.64 1.63.925 2.716 1.062 1.056.132 2.387.132 4.066.132h2.316c1.68 0 3.01 0 4.066-.132 1.086-.137 1.974-.422 2.716-1.061.743-.64 1.155-1.474 1.445-2.525.281-1.02.47-2.328.707-3.978l.292-2.032c.185-1.282.332-2.305.36-3.146.03-.87-.062-1.614-.403-2.325C22 6.84 21.477 6.3 20.78 5.775c-.675-.508-1.567-1.039-2.686-1.706l-1.456-.867c-1.016-.605-1.826-1.088-2.527-1.415-.724-.338-1.386-.537-2.111-.537zM8.096 4.511c1.057-.63 1.803-1.073 2.428-1.365.609-.284 1.047-.396 1.476-.396.43 0 .867.112 1.476.396.625.292 1.37.735 2.428 1.365l1.385.825c1.165.694 1.986 1.184 2.59 1.638.587.443.91.809 1.11 1.225.199.416.282.894.257 1.626-.026.75-.16 1.691-.352 3.026l-.28 1.937c-.246 1.714-.422 2.928-.675 3.845-.247.896-.545 1.415-.977 1.787-.433.373-.994.593-1.925.71-.951.119-2.188.12-3.93.12h-2.213c-1.743 0-2.98-.001-3.931-.12-.93-.117-1.492-.337-1.925-.71-.432-.372-.73-.891-.977-1.787-.253-.917-.43-2.131-.676-3.845l-.279-1.937c-.192-1.335-.326-2.277-.352-3.026-.025-.732.058-1.21.258-1.626.2-.416.521-.782 1.11-1.225.603-.454 1.424-.944 2.589-1.638l1.385-.825z"})]})}function i(e){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:[(0,a.jsx)("path",{d:"M17 14a1 1 0 100-2 1 1 0 000 2zM17 18a1 1 0 100-2 1 1 0 000 2zM13 13a1 1 0 11-2 0 1 1 0 012 0zM13 17a1 1 0 11-2 0 1 1 0 012 0zM7 14a1 1 0 100-2 1 1 0 000 2zM7 18a1 1 0 100-2 1 1 0 000 2z",fill:"currentColor"}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 1.75a.75.75 0 01.75.75v.763c.662-.013 1.391-.013 2.193-.013h4.113c.803 0 1.532 0 2.194.013V2.5a.75.75 0 011.5 0v.827c.26.02.506.045.739.076 1.172.158 2.121.49 2.87 1.238.748.749 1.08 1.698 1.238 2.87.153 1.14.153 2.595.153 4.433v2.112c0 1.838 0 3.294-.153 4.433-.158 1.172-.49 2.121-1.238 2.87-.749.748-1.698 1.08-2.87 1.238-1.14.153-2.595.153-4.433.153H9.944c-1.838 0-3.294 0-4.433-.153-1.172-.158-2.121-.49-2.87-1.238-.748-.749-1.08-1.698-1.238-2.87-.153-1.14-.153-2.595-.153-4.433v-2.112c0-1.838 0-3.294.153-4.433.158-1.172.49-2.121 1.238-2.87.749-.748 1.698-1.08 2.87-1.238.233-.031.48-.056.739-.076V2.5A.75.75 0 017 1.75zM5.71 4.89c-1.005.135-1.585.389-2.008.812-.423.423-.677 1.003-.812 2.009-.023.17-.042.35-.058.539h18.336c-.016-.19-.035-.369-.058-.54-.135-1.005-.389-1.585-.812-2.008-.423-.423-1.003-.677-2.009-.812-1.027-.138-2.382-.14-4.289-.14h-4c-1.907 0-3.261.002-4.29.14zM2.75 12c0-.854 0-1.597.013-2.25h18.474c.013.653.013 1.396.013 2.25v2c0 1.907-.002 3.262-.14 4.29-.135 1.005-.389 1.585-.812 2.008-.423.423-1.003.677-2.009.812-1.027.138-2.382.14-4.289.14h-4c-1.907 0-3.261-.002-4.29-.14-1.005-.135-1.585-.389-2.008-.812-.423-.423-.677-1.003-.812-2.009-.138-1.027-.14-2.382-.14-4.289v-2z",fill:"currentColor"})]})}function r(e){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1.25a4.75 4.75 0 100 9.5 4.75 4.75 0 000-9.5zM8.75 6a3.25 3.25 0 116.5 0 3.25 3.25 0 01-6.5 0zM12 12.25c-2.313 0-4.445.526-6.024 1.414C4.42 14.54 3.25 15.866 3.25 17.5v.102c-.001 1.162-.002 2.62 1.277 3.662.629.512 1.51.877 2.7 1.117 1.192.242 2.747.369 4.773.369s3.58-.127 4.774-.369c1.19-.24 2.07-.605 2.7-1.117 1.279-1.042 1.277-2.5 1.276-3.662V17.5c0-1.634-1.17-2.96-2.725-3.836-1.58-.888-3.711-1.414-6.025-1.414zM4.75 17.5c0-.851.622-1.775 1.961-2.528 1.316-.74 3.184-1.222 5.29-1.222 2.104 0 3.972.482 5.288 1.222 1.34.753 1.961 1.677 1.961 2.528 0 1.308-.04 2.044-.724 2.6-.37.302-.99.597-2.05.811-1.057.214-2.502.339-4.476.339-1.974 0-3.42-.125-4.476-.339-1.06-.214-1.68-.509-2.05-.81-.684-.557-.724-1.293-.724-2.601z",fill:"currentColor"})})}function o(e){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 7A.75.75 0 013 6.25h10a.75.75 0 010 1.5H3A.75.75 0 012.25 7zm14.25-.75a.75.75 0 01.684.442l4.5 10a.75.75 0 11-1.368.616l-1.437-3.194H14.12l-1.437 3.194a.75.75 0 11-1.368-.616l4.5-10a.75.75 0 01.684-.442zm-1.704 6.364h3.408L16.5 8.828l-1.704 3.786zM2.25 12a.75.75 0 01.75-.75h7a.75.75 0 010 1.5H3a.75.75 0 01-.75-.75zm0 5a.75.75 0 01.75-.75h5a.75.75 0 010 1.5H3a.75.75 0 01-.75-.75z",fill:"currentColor"})})}function c(e){return(0,a.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.89775 4.10225C8.11742 4.32192 8.11742 4.67808 7.89775 4.89775L4.358 8.4375H15C15.3107 8.4375 15.5625 8.68934 15.5625 9C15.5625 9.31066 15.3107 9.5625 15 9.5625H4.358L7.89775 13.1023C8.11742 13.3219 8.11742 13.6781 7.89775 13.8977C7.67808 14.1174 7.32192 14.1174 7.10225 13.8977L2.60225 9.39775C2.38258 9.17808 2.38258 8.82192 2.60225 8.60225L7.10225 4.10225C7.32192 3.88258 7.67808 3.88258 7.89775 4.10225Z",fill:""})})}function d(e){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.25 2.5a2.75 2.75 0 00-2.75 2.75v13.5a2.75 2.75 0 002.75 2.75h13.5a2.75 2.75 0 002.75-2.75V5.25a2.75 2.75 0 00-2.75-2.75H5.25zM3.5 5.25c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v13.5a1.75 1.75 0 01-1.75 1.75H5.25a1.75 1.75 0 01-1.75-1.75V5.25zm4.25 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75zm0 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75zm0 3.5a.75.75 0 01.75-.75h7a.75.75 0 010 1.5h-7a.75.75 0 01-.75-.75z"})})}function h(e){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 3a.75.75 0 01.75-.75h1.386c.76 0 1.413.537 1.563 1.282l.786 3.918h13.516a1.5 1.5 0 011.456 1.873l-1.5 6A1.5 1.5 0 0118.75 16.5h-10.5a1.5 1.5 0 01-1.456-1.127l-2.25-11.25a.25.25 0 00-.245-.214H3a.75.75 0 01-.75-.75zM4.5 8.95l1.944 9.72a.25.25 0 00.243.188h10.5a.25.25 0 00.243-.188l1.5-6a.25.25 0 00-.243-.312H4.5v-3.408z"}),(0,a.jsx)("path",{d:"M8.25 19.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM15.75 19.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"})]})}function u(e){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V18a3 3 0 01-3 3H5.25a3 3 0 01-3-3V5.25zm3-1.5a1.5 1.5 0 00-1.5 1.5v1.5h16.5v-1.5a1.5 1.5 0 00-1.5-1.5H5.25zM3.75 9v9a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5V9H3.75z"}),(0,a.jsx)("path",{d:"M6.75 12a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zM6.75 15a.75.75 0 01.75-.75h5.25a.75.75 0 010 1.5H7.5a.75.75 0 01-.75-.75z"})]})}function f(e){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.875 1.5a.75.75 0 00-.75.75v4.5c0 .414.336.75.75.75h8.25a.75.75 0 00.75-.75v-4.5a.75.75 0 00-.75-.75h-8.25zM9 3h6v2.25H9V3z"}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 7.5a.75.75 0 00-.75.75v7.5a.75.75 0 00.75.75h.75v3.75c0 .414.336.75.75.75h13.5a.75.75 0 00.75-.75V16.5h.75a.75.75 0 00.75-.75v-7.5a.75.75 0 00-.75-.75h-16.5zm15 9h-13.5v3h13.5v-3zm-15-7.5v6h16.5v-6h-16.5z"}),(0,a.jsx)("path",{d:"M6.75 12a.75.75 0 000 1.5h.75a.75.75 0 000-1.5h-.75z"})]})}function m(e){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v.816a3.836 3.836 0 00-1.72.756 2.25 2.25 0 000 3.18c.302.267.673.484 1.103.66.431.177.915.329 1.394.492.577.196 1.163.415 1.555.709a.75.75 0 010 1.06c-.392.294-.978.513-1.555.709-.479.163-.963.315-1.394.492-.43.176-.8.393-1.103.66a2.25 2.25 0 000 3.18 3.836 3.836 0 001.72.756V18a.75.75 0 001.5 0v-.816a3.836 3.836 0 001.72-.756 2.25 2.25 0 000-3.18c-.302-.267-.673-.484-1.103-.66-.431-.177-.915-.329-1.394-.492-.577-.196-1.163-.415-1.555-.709a.75.75 0 010-1.06c.392-.294.978-.513 1.555-.709.479-.163.963-.315 1.394-.492.43-.176.8-.393 1.103-.66a2.25 2.25 0 000-3.18 3.836 3.836 0 00-1.72-.756V6z"})})}function g(e){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",...e,children:[(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3.75a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 3.75zM3 8.25a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75A.75.75 0 013 8.25zM3.75 11.25a.75.75 0 000 1.5h16.5a.75.75 0 000-1.5H3.75zM3 16.25a.75.75 0 01.75-.75h16.5a.75.75 0 010 1.5H3.75a.75.75 0 01-.75-.75zM3.75 19.25a.75.75 0 000 1.5h16.5a.75.75 0 000-1.5H3.75z"}),(0,a.jsx)("path",{d:"M6.75 6a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9A.75.75 0 016.75 6zM6.75 10.5a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zM7.5 14.25a.75.75 0 000 1.5h9a.75.75 0 000-1.5h-9z"})]})}},14232:(e,t,s)=>{s.d(t,{B:()=>g});var a=s(95155),n=s(23102),l=s(21567),i=s(48173),r=s.n(i),o=s(76046),c=s(12115),d=s(60569),h=s(59674),u=s(12175),f=s(14638),m=s(83391);function g(){let e=(0,o.usePathname)(),t=(0,o.useRouter)(),{setIsOpen:s,isOpen:i,isMobile:g,toggleSidebar:x}=(0,f.V)(),[p,v]=(0,c.useState)([]),{user:w}=(0,m.d4)(e=>e.auth),b=w?(0,d.D)(w.role):d.R,y=e=>{v(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,c.useEffect)(()=>{let t=null;b.some(s=>s.items.some(s=>s.items.some(a=>a.url===e&&(t=s.title,!0)))),t&&!p.includes(t)&&v(e=>[...e,t])},[e,b,p]),(0,a.jsxs)(a.Fragment,{children:[g&&i&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 transition-opacity duration-300",onClick:()=>{console.log("Overlay clicked, closing sidebar"),s(!1)},"aria-hidden":"true"}),(0,a.jsx)("aside",{className:(0,l.cn)("h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out",g?"fixed left-0 top-0 bottom-0 z-50 transform":"z-40",i||!g?"translate-x-0":"-translate-x-full"),"aria-label":"Main navigation","aria-hidden":g&&!i,children:(0,a.jsxs)("div",{className:"flex h-full flex-col pt-3 pb-5 pl-[25px] pr-[7px]",children:[(0,a.jsxs)("div",{className:"relative sidebar-logo-container pr-4.5",children:[(0,a.jsx)(r(),{href:"/dashboard",onClick:e=>{if(window.location.pathname.includes("/dashboard/users")){g&&x();return}e.preventDefault(),g&&x(),t.push("/dashboard")},className:"sidebar-logo",children:(0,a.jsx)(n.g,{})}),g&&(0,a.jsxs)("button",{onClick:x,className:"absolute right-0 top-1/2 -translate-y-1/2 text-right",children:[(0,a.jsx)("span",{className:"sr-only",children:"Close Menu"}),(0,a.jsx)(h.A6,{className:"ml-auto size-7"})]})]}),(0,a.jsx)("div",{className:"custom-scrollbar flex-1 overflow-y-auto pr-3 sidebar-menu",children:b.map(t=>(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"mb-5 text-sm font-medium text-gray-600",children:t.label}),(0,a.jsx)("nav",{role:"navigation","aria-label":t.label,children:(0,a.jsx)("ul",{className:"space-y-2",children:t.items.map(t=>(0,a.jsx)("li",{children:t.items.length?(0,a.jsxs)("div",{children:[(0,a.jsxs)(u.D,{isActive:t.items.some(t=>{let{url:s}=t;return e.startsWith(s)}),onClick:()=>y(t.title),children:[(0,a.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,a.jsx)("span",{children:t.title}),(0,a.jsx)(h.rX,{className:(0,l.cn)("ml-auto rotate-180 transition-transform duration-200",p.includes(t.title)&&"rotate-0"),"aria-hidden":"true"})]}),p.includes(t.title)&&(0,a.jsx)("ul",{className:"ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2",role:"menu",children:t.items.map(t=>(0,a.jsx)("li",{role:"none",children:(0,a.jsx)(u.D,{as:"link",href:t.url,isActive:e===t.url,children:(0,a.jsx)("span",{children:t.title})})},t.title))})]}):(()=>{let s="url"in t&&t.url?t.url:"/"+t.title.toLowerCase().split(" ").join("-");return(0,a.jsxs)(u.D,{className:"flex items-center gap-3 py-3",as:"link",href:s,isActive:"/dashboard"===s?e===s:e===s||e.startsWith(s+"/"),children:[(0,a.jsx)(t.icon,{className:"size-6 shrink-0","aria-hidden":"true"}),(0,a.jsx)("span",{children:t.title})]})})()},t.title))})})]},t.label))})]})})]})}},12175:(e,t,s)=>{s.d(t,{D:()=>f});var a=s(95155),n=s(21567),l=s(43463);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=l.$;var o=s(48173),c=s.n(o),d=s(76046),h=s(14638);let u=((e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return r(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:n,defaultVariants:l}=t,o=Object.keys(n).map(e=>{let t=null==s?void 0:s[e],a=null==l?void 0:l[e];if(null===t)return null;let r=i(t)||i(a);return n[e][r]}),c=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return r(e,o,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:s,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...c}[t]):({...l,...c})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)})("rounded-lg px-3.5 font-medium text-gray-700 transition-all duration-200",{variants:{isActive:{true:"bg-[rgba(87,80,241,0.07)] text-primary hover:bg-[rgba(87,80,241,0.07)]",false:"hover:bg-gray-100 hover:text-gray-900"}},defaultVariants:{isActive:!1}});function f(e){let{toggleSidebar:t,isMobile:s}=(0,h.V)();return((0,d.useRouter)(),"link"===e.as)?(0,a.jsx)(c(),{href:e.href,onClick:()=>{s&&t()},className:(0,n.cn)(u({isActive:e.isActive,className:"relative block py-2"}),e.className),children:e.children}):(0,a.jsx)("button",{onClick:e.onClick,"aria-expanded":e.isActive,className:u({isActive:e.isActive,className:"flex w-full items-center gap-3 py-3"}),children:e.children})}},14638:(e,t,s)=>{s.d(t,{SidebarProvider:()=>o,V:()=>r});var a=s(95155),n=s(30555),l=s(12115);let i=(0,l.createContext)(null);function r(){let e=(0,l.useContext)(i);if(!e)throw Error("useSidebarContext must be used within a SidebarProvider");return e}function o(e){let{children:t,defaultOpen:s=!0}=e,r=(0,n.a)(),[o,c]=(0,l.useState)(r),[d,h]=(0,l.useState)(!r&&s),[u,f]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{o!==r&&(r?u?console.log("Switching to mobile - keeping sidebar state (manually toggled)"):(h(!1),console.log("Switching to mobile - closing sidebar (not manually toggled)")):(h(!0),f(!1),console.log("Switching to desktop - opening sidebar")),console.log("SidebarProvider - Screen size changed:",{prevIsMobile:o,isMobile:r,isOpen:!r||!!u&&d,wasManuallyToggled:u}),c(r))},[r,o,u,d]),(0,a.jsx)(i.Provider,{value:{state:d?"expanded":"collapsed",isOpen:d,setIsOpen:h,isMobile:r,toggleSidebar:function(){f(!0),setTimeout(()=>{h(e=>{let t=!e;return console.log("Sidebar manually toggled:",{newState:t,isMobile:r}),t})},50)}},children:t})}},23102:(e,t,s)=>{s.d(t,{g:()=>l});var a=s(95155),n=s(5565);function l(){return(0,a.jsx)("div",{className:"flex items-center justify-center h-10 max-w-[12rem] mt-1",children:(0,a.jsx)(n.default,{src:"/images/logo.png",width:120,height:40,className:"object-contain",alt:"Company logo",role:"presentation",quality:100,priority:!0})})}},30555:(e,t,s)=>{s.d(t,{a:()=>n});var a=s(12115);function n(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},21567:(e,t,s)=>{s.d(t,{cn:()=>l});var a=s(43463),n=s(69795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}},88908:(e,t,s)=>{s.d(t,{V:()=>l});var a=s(10733);class n{async init(){try{this.db=await (0,a.P2)(this.DB_NAME,this.DB_VERSION,{upgrade(e){let t=e.createObjectStore("offlineSales",{keyPath:"id"});t.createIndex("by-timestamp","timestamp"),t.createIndex("by-status","status");let s=e.createObjectStore("products",{keyPath:"id"});s.createIndex("by-name","name"),s.createIndex("by-barcode","barcode");let a=e.createObjectStore("syncQueue",{keyPath:"id"});a.createIndex("by-priority","priority"),a.createIndex("by-timestamp","timestamp"),e.objectStoreNames.contains("store")||e.createObjectStore("store")}}),console.log("✅ Offline storage initialized")}catch(e){throw console.error("❌ Failed to initialize offline storage:",e),e}}async saveOfflineSale(e){if(!this.db)throw Error("Database not initialized");let t="offline_sale_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),s={...e,id:t,timestamp:new Date,status:"pending",syncAttempts:0};return await this.db.add("offlineSales",s),console.log("\uD83D\uDCBE Offline sale saved:",t),t}async getOfflineSales(e){if(!this.db)throw Error("Database not initialized");return e?await this.db.getAllFromIndex("offlineSales","by-status",e):await this.db.getAll("offlineSales")}async updateSaleStatus(e,t,s){if(!this.db)throw Error("Database not initialized");let a=await this.db.get("offlineSales",e);if(!a)throw Error("Sale not found");a.status=t,a.syncAttempts+=1,a.lastSyncAttempt=new Date,s&&(a.errorMessage=s),await this.db.put("offlineSales",a)}async deleteOfflineSale(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("offlineSales",e)}async cacheProducts(e){if(!this.db)throw Error("Database not initialized");let t=this.db.transaction("products","readwrite"),s=t.objectStore("products");for(let t of(await s.clear(),e))await s.add({...t,lastUpdated:new Date});await t.done,console.log("\uD83D\uDCBE Cached ".concat(e.length," products for offline use"))}async getCachedProducts(){if(!this.db)throw Error("Database not initialized");return await this.db.getAll("products")}async searchProducts(e){if(!this.db)throw Error("Database not initialized");let t=await this.db.getAll("products"),s=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(s)||e.sku&&e.sku.toLowerCase().includes(s)||e.barcode&&e.barcode.toLowerCase().includes(s))}async getProductByBarcode(e){if(!this.db)throw Error("Database not initialized");return await this.db.getFromIndex("products","by-barcode",e)}async addToSyncQueue(e){if(!this.db)throw Error("Database not initialized");let t={...e,id:"sync_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:new Date,retryCount:0};await this.db.add("syncQueue",t)}async getSyncQueue(){if(!this.db)throw Error("Database not initialized");return await this.db.getAllFromIndex("syncQueue","by-priority")}async removeSyncQueueItem(e){if(!this.db)throw Error("Database not initialized");await this.db.delete("syncQueue",e)}async cacheStore(e){if(!this.db)throw Error("Database not initialized");await this.db.put("store",e,"current-store"),console.log("\uD83D\uDCBE Store details cached for offline use")}async getCachedStore(){if(!this.db)throw Error("Database not initialized");return await this.db.get("store","current-store")}async getStorageStats(){if(!this.db)throw Error("Database not initialized");let[e,t,s,a,n]=await Promise.all([this.db.getAllFromIndex("offlineSales","by-status","pending"),this.db.getAllFromIndex("offlineSales","by-status","synced"),this.db.getAllFromIndex("offlineSales","by-status","failed"),this.db.getAll("products"),this.db.getAll("syncQueue")]);return{pendingSales:e.length,syncedSales:t.length,failedSales:s.length,cachedProducts:a.length,queueItems:n.length}}async clearAllData(){if(!this.db)throw Error("Database not initialized");let e=this.db.transaction(["offlineSales","products","syncQueue"],"readwrite");await Promise.all([e.objectStore("offlineSales").clear(),e.objectStore("products").clear(),e.objectStore("syncQueue").clear()]),await e.done,console.log("\uD83D\uDDD1️ All offline data cleared")}constructor(){this.db=null,this.DB_NAME="NEXAPO_POS_OFFLINE",this.DB_VERSION=1}}let l=new n;l.init().catch(console.error)},52080:(e,t,s)=>{s.d(t,{zy:()=>o});var a=s(88908),n=s(90821),l=s(75912),i=s(2818);class r{setupNetworkListeners(){window.addEventListener("online",()=>{console.log("\uD83C\uDF10 Network connection restored"),(0,l.r)("success","Connection restored - syncing offline sales..."),this.syncOfflineSales()}),window.addEventListener("offline",()=>{console.log("\uD83D\uDCF1 Network connection lost - switching to offline mode"),(0,l.r)("warning","Working offline - sales will sync when connection returns")})}startPeriodicSync(){this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{navigator.onLine&&!this.syncInProgress&&this.syncOfflineSales()},this.SYNC_INTERVAL)}async syncOfflineSales(){if(this.syncInProgress){console.log("⏳ Sync already in progress, skipping...");return}if(!navigator.onLine){console.log("\uD83D\uDCF1 Offline - skipping sync");return}this.syncInProgress=!0,console.log("\uD83D\uDD04 Starting offline sales sync...");try{let e=await a.V.getOfflineSales("pending"),t=await a.V.getOfflineSales("failed"),s=[...e,...t.filter(e=>e.syncAttempts<this.MAX_RETRY_ATTEMPTS)];if(0===s.length){console.log("✅ No offline sales to sync");return}console.log("\uD83D\uDD04 Syncing ".concat(s.length," offline sales..."));let n=0,i=0;for(let e of s)try{await this.syncSingleSale(e),n++}catch(t){console.error("❌ Failed to sync sale ".concat(e.id,":"),t),i++,await a.V.updateSaleStatus(e.id,"failed",t instanceof Error?t.message:"Unknown error")}n>0&&(0,l.r)("success","✅ Synced ".concat(n," offline sales")),i>0&&(0,l.r)("warning","⚠️ ".concat(i," sales failed to sync - will retry later"))}catch(e){console.error("❌ Sync process failed:",e),(0,l.r)("error","Sync failed - will retry automatically")}finally{this.syncInProgress=!1}}async syncSingleSale(e){let t=n.M.getState().auth,s=null==t?void 0:t.accessToken;if(!s)throw Error("No authentication token available");let l={totalAmount:e.totalAmount,paymentMethod:e.paymentMethod,items:e.items.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:e.receiptUrl||"",storeId:e.storeId},r=await fetch("".concat(i.env.NEXT_PUBLIC_API_URL,"/sales"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({mode:"createnew",saleData:l})});if(!r.ok)throw Error((await r.json().catch(()=>({}))).message||"HTTP ".concat(r.status,": ").concat(r.statusText));let o=await r.json();if(!o.success)throw Error(o.message||"Sale creation failed");await a.V.updateSaleStatus(e.id,"synced"),console.log("✅ Successfully synced sale ".concat(e.id)),setTimeout(()=>{a.V.deleteOfflineSale(e.id).catch(console.error)},864e5)}async forceSyncNow(){if(!navigator.onLine){(0,l.r)("warning","No internet connection - cannot sync now");return}(0,l.r)("success","Starting manual sync..."),await this.syncOfflineSales()}async getSyncStatus(){let e=await a.V.getStorageStats();return{isOnline:navigator.onLine,syncInProgress:this.syncInProgress,pendingSales:e.pendingSales,failedSales:e.failedSales,lastSyncAttempt:new Date}}destroy(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null)}constructor(){this.syncInProgress=!1,this.syncInterval=null,this.SYNC_INTERVAL=3e4,this.MAX_RETRY_ATTEMPTS=3,this.setupNetworkListeners(),this.startPeriodicSync()}}let o=new r},75912:(e,t,s)=>{s.d(t,{r:()=>n});var a=s(55037);let n=(e,t)=>{"success"===e?a.oR.success(t):"error"===e?a.oR.error(t):"warning"===e&&(0,a.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};n.success=e=>n("success",e),n.error=e=>n("error",e),n.warning=e=>n("warning",e)}}]);