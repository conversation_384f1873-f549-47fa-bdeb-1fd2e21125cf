import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import dotenv from "dotenv";
import { cleanEnv, str } from "envalid";
import { parse } from "pg-connection-string";
import * as schema from "../db/schema"; // Import your schema

dotenv.config();

// Validate environment variables
const env = cleanEnv(process.env, {
  DATABASE_URL: str(),
});

// Parse the connection string
const dbConfig = parse(env.DATABASE_URL);

// Maximum number of connection retries
const MAX_CONNECTION_RETRIES = 5;
// Delay between retries (in milliseconds)
const RETRY_DELAY = 5000;

// Create an optimized PostgreSQL connection pool
const pool = new Pool({
  host: dbConfig.host || undefined,
  port: dbConfig.port ? Number(dbConfig.port) : 5432,
  user: dbConfig.user || undefined,
  password: dbConfig.password || undefined,
  database: dbConfig.database || undefined,
  ssl:
    dbConfig.ssl || env.DATABASE_URL.includes("sslmode=require")
      ? { rejectUnauthorized: false }
      : undefined, // ✅ Proper SSL handling
  max: 20, // 🔥 Reduced max connections to prevent overwhelming DB
  min: 2, // 🔥 Keep minimum 2 connections ready
  idleTimeoutMillis: 30000, // 🔥 Reduced idle timeout
  connectionTimeoutMillis: 15000, // 🔥 Increased connection timeout to 15s
  statement_timeout: 60000, // 🔥 Increased statement timeout to 60s
  query_timeout: 60000, // 🔥 Increased query timeout to 60s
});

// Track connection status
let connectionRetries = 0;

// Add connection pool error handling
pool.on('error', (err) => {
  console.error('Database pool error:', err);

  // Attempt to reconnect if the error is connection-related
  if (
    err.message.includes('ECONNREFUSED') ||
    err.message.includes('ETIMEDOUT') ||
    err.message.includes('ENOTFOUND') ||
    err.message.includes('connection')
  ) {
    attemptReconnect();
  }
});

// Add connection pool monitoring
pool.on('connect', () => {
  console.log('New database connection established');
  connectionRetries = 0; // Reset retry counter on successful connection
});

// Function to attempt reconnection
const attemptReconnect = async () => {
  if (connectionRetries >= MAX_CONNECTION_RETRIES) {
    console.error(`Failed to reconnect to database after ${MAX_CONNECTION_RETRIES} attempts`);
    return;
  }

  connectionRetries++;
  console.log(`Attempting to reconnect to database (attempt ${connectionRetries}/${MAX_CONNECTION_RETRIES})...`);

  // Wait before attempting to reconnect
  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));

  try {
    // Test the connection
    const client = await pool.connect();
    client.release();
    console.log('Database reconnection successful');
    connectionRetries = 0;
  } catch (error) {
    console.error('Database reconnection failed:', error);
    attemptReconnect(); // Try again
  }
};

// Verify initial connection
(async () => {
  try {
    const client = await pool.connect();
    console.log('Initial database connection successful');
    client.release();
  } catch (error) {
    console.error('Initial database connection failed:', error);
    attemptReconnect();
  }
})();

// We're using the direct drizzle instance without a wrapper for now

// Export the drizzle instance with the optimized pool
export const postgresDb = drizzle(pool, { schema }); // ✅ Use schema
