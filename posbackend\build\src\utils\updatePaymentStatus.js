"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.markOverduePayments = exports.fixIncorrectSubscriptionPeriods = exports.checkSubscriptionRenewals = exports.verifyPaymentStatusUpdate = exports.updatePaymentStatus = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const dayjs_1 = __importDefault(require("dayjs"));
const index_1 = require("../../index");
const updatePaymentStatus = async (userId, newStatus = "paid", subscriptionPeriod = 1 // Number of months
) => {
    const now = new Date();
    const nextPaymentDue = newStatus === "paid" ? (0, dayjs_1.default)(now).add(subscriptionPeriod, "month").toDate() : null; // Add subscription period months
    // Prepare the update data based on the new status
    const updateData = newStatus === "paid"
        ? { paymentStatus: newStatus, lastPaymentDate: now, nextPaymentDue }
        : { paymentStatus: newStatus };
    console.log(`🔄 Starting payment status update for user ID ${userId} to status: ${newStatus}`);
    // Retry logic for database operations
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000; // 1 second
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
            console.log(`📡 Attempt ${attempt}/${MAX_RETRIES} - Fetching user role for ID ${userId}`);
            // Get user role first to determine if we need to update cashiers
            const userResult = await db_1.postgresDb
                .select({ role: schema_1.users.role })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
                .limit(1);
            if (userResult.length === 0) {
                throw new Error(`User with ID ${userId} not found.`);
            }
            const userRole = userResult[0].role;
            console.log(`👤 User ID ${userId} has role: ${userRole}`);
            console.log(`💾 Starting transaction to update payment status for user ID ${userId}`);
            await db_1.postgresDb.transaction(async (tx) => {
                // First update the user
                console.log(`🔄 Updating payment status for user ID ${userId}`);
                const updatedUser = await tx
                    .update(schema_1.users)
                    .set(updateData)
                    .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
                    .returning({ id: schema_1.users.id, paymentStatus: schema_1.users.paymentStatus });
                if (updatedUser.length === 0) {
                    throw new Error(`Failed to update user ID ${userId}`);
                }
                console.log(`✅ User ID ${userId} payment status updated to: ${updatedUser[0].paymentStatus}`);
                // Notify the user via WebSocket
                (0, index_1.notifyUserPaymentStatus)(userId, newStatus);
                // If user is an admin, also update all cashiers created by this admin
                if (userRole === "admin") {
                    console.log(`👥 Updating cashiers created by admin ID ${userId}`);
                    const updatedCashiers = await tx
                        .update(schema_1.users)
                        .set(updateData)
                        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, userId), (0, drizzle_orm_1.eq)(schema_1.users.role, "cashier")))
                        .returning({ id: schema_1.users.id, paymentStatus: schema_1.users.paymentStatus });
                    console.log(`✅ Payment status updated to ${newStatus} for admin ID ${userId} and ${updatedCashiers.length} cashiers.`);
                    // Log each cashier update for debugging
                    updatedCashiers.forEach(cashier => {
                        console.log(`   📝 Cashier ID ${cashier.id} status: ${cashier.paymentStatus}`);
                        // Notify each cashier via WebSocket
                        (0, index_1.notifyUserPaymentStatus)(cashier.id, newStatus);
                    });
                }
                else {
                    console.log(`✅ Payment status updated to ${newStatus} for user ID ${userId}.`);
                }
            });
            // If we reach here, the operation was successful
            console.log(`🎉 Payment status update completed successfully for user ID ${userId} on attempt ${attempt}`);
            return;
        }
        catch (error) {
            console.error(`❌ Attempt ${attempt}/${MAX_RETRIES} failed for user ID ${userId}:`, error.message);
            // Check if it's a database connection error
            const isConnectionError = error.message.includes('ETIMEDOUT') ||
                error.message.includes('ECONNREFUSED') ||
                error.message.includes('ENOTFOUND') ||
                error.message.includes('connection') ||
                error.name === 'AggregateError';
            if (isConnectionError && attempt < MAX_RETRIES) {
                console.log(`🔄 Database connection error detected. Retrying in ${RETRY_DELAY}ms...`);
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                continue;
            }
            // If it's the last attempt or not a connection error, throw the error
            if (attempt === MAX_RETRIES) {
                console.error(`💥 All ${MAX_RETRIES} attempts failed for user ID ${userId}. Final error:`, error);
                throw new Error(`Failed to update payment status for user ID ${userId} after ${MAX_RETRIES} attempts: ${error.message}`);
            }
            // For non-connection errors, throw immediately
            if (!isConnectionError) {
                console.error(`💥 Non-connection error for user ID ${userId}:`, error);
                throw error;
            }
        }
    }
};
exports.updatePaymentStatus = updatePaymentStatus;
/**
 * Verify that a user's payment status was properly updated
 * @param userId The user ID to check
 * @param expectedStatus The expected payment status
 * @returns Promise<boolean> indicating if the status matches
 */
const verifyPaymentStatusUpdate = async (userId, expectedStatus) => {
    try {
        console.log(`🔍 Verifying payment status for user ID ${userId}, expected: ${expectedStatus}`);
        const userResult = await db_1.postgresDb
            .select({
            paymentStatus: schema_1.users.paymentStatus,
            lastPaymentDate: schema_1.users.lastPaymentDate,
            nextPaymentDue: schema_1.users.nextPaymentDue,
            role: schema_1.users.role
        })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .limit(1);
        if (userResult.length === 0) {
            console.error(`❌ User ID ${userId} not found during verification`);
            return false;
        }
        const user = userResult[0];
        const statusMatches = user.paymentStatus === expectedStatus;
        console.log(`📊 User ID ${userId} verification result:`, {
            currentStatus: user.paymentStatus,
            expectedStatus,
            statusMatches,
            lastPaymentDate: user.lastPaymentDate,
            nextPaymentDue: user.nextPaymentDue,
            role: user.role
        });
        // If user is an admin, also check their cashiers
        if (user.role === "admin") {
            const cashierResults = await db_1.postgresDb
                .select({
                id: schema_1.users.id,
                paymentStatus: schema_1.users.paymentStatus
            })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, userId), (0, drizzle_orm_1.eq)(schema_1.users.role, "cashier")));
            console.log(`👥 Admin ${userId} has ${cashierResults.length} cashiers`);
            const allCashiersUpdated = cashierResults.every(cashier => {
                const cashierStatusMatches = cashier.paymentStatus === expectedStatus;
                console.log(`   📝 Cashier ID ${cashier.id}: ${cashier.paymentStatus} (expected: ${expectedStatus}) - ${cashierStatusMatches ? '✅' : '❌'}`);
                return cashierStatusMatches;
            });
            if (!allCashiersUpdated) {
                console.error(`❌ Not all cashiers for admin ${userId} have the correct payment status`);
                return false;
            }
        }
        return statusMatches;
    }
    catch (error) {
        console.error(`❌ Error verifying payment status for user ID ${userId}:`, error);
        return false;
    }
};
exports.verifyPaymentStatusUpdate = verifyPaymentStatusUpdate;
const checkSubscriptionRenewals = async () => {
    try {
        console.info("🔍 Checking subscription renewals and calculating next payment dates...");
        const now = new Date();
        const threeDaysFromNow = (0, dayjs_1.default)(now).add(3, 'day').toDate();
        await db_1.postgresDb.transaction(async (tx) => {
            // Find users whose subscriptions are expiring soon or have expired
            const usersNeedingRenewal = await tx
                .select({
                id: schema_1.users.id,
                email: schema_1.users.email,
                paymentStatus: schema_1.users.paymentStatus,
                lastPaymentDate: schema_1.users.lastPaymentDate,
                nextPaymentDue: schema_1.users.nextPaymentDue,
                role: schema_1.users.role,
                createdAt: schema_1.users.createdAt
            })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.lt)(schema_1.users.nextPaymentDue, threeDaysFromNow), // Users whose subscription expires within 3 days
            (0, drizzle_orm_1.eq)(schema_1.users.paymentStatus, "paid"), // Only check currently active users
            (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.users.role, "admin"), (0, drizzle_orm_1.eq)(schema_1.users.role, "superadmin")) // Only admins and superadmins pay
            ));
            console.info(`📊 Found ${usersNeedingRenewal.length} users needing subscription renewal check`);
            for (const user of usersNeedingRenewal) {
                try {
                    // Get the user's latest successful payment to determine subscription period
                    const latestPayment = await tx
                        .select({
                        subscriptionPeriod: schema_1.payments.subscriptionPeriod,
                        amount: schema_1.payments.amount,
                        paidAt: schema_1.payments.paidAt
                    })
                        .from(schema_1.payments)
                        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.userId, user.id), (0, drizzle_orm_1.eq)(schema_1.payments.status, "successful")))
                        .orderBy((0, drizzle_orm_1.desc)(schema_1.payments.paidAt))
                        .limit(1);
                    if (latestPayment.length === 0) {
                        console.warn(`⚠️ No payment history found for user ${user.email} (ID: ${user.id})`);
                        continue;
                    }
                    const subscriptionPeriod = latestPayment[0].subscriptionPeriod || 1;
                    const paymentAmount = latestPayment[0].amount;
                    // Check if user is in free trial period (no payments made yet)
                    const isInFreeTrial = latestPayment.length === 0;
                    const daysSinceCreation = (0, dayjs_1.default)(now).diff((0, dayjs_1.default)(user.createdAt), 'day');
                    const daysUntilExpiry = (0, dayjs_1.default)(user.nextPaymentDue).diff(now, 'day');
                    console.log(`📋 User ${user.email} (ID: ${user.id}):`);
                    console.log(`   🆕 Account created: ${(0, dayjs_1.default)(user.createdAt).format('YYYY-MM-DD')} (${daysSinceCreation} days ago)`);
                    console.log(`   🎁 Free trial: ${isInFreeTrial ? 'YES' : 'NO'}`);
                    if (!isInFreeTrial) {
                        console.log(`   💰 Last payment: ₵${paymentAmount} for ${subscriptionPeriod} month(s)`);
                    }
                    console.log(`   📅 Current next due: ${(0, dayjs_1.default)(user.nextPaymentDue).format('YYYY-MM-DD')}`);
                    console.log(`   ⏰ Days until expiry: ${daysUntilExpiry}`);
                    // Check if subscription has expired
                    if ((0, dayjs_1.default)(user.nextPaymentDue).isBefore(now)) {
                        if (isInFreeTrial) {
                            console.log(`   ⏰ Free trial expired - marking as overdue (needs first payment)`);
                        }
                        else {
                            console.log(`   ❌ Subscription expired - marking as overdue`);
                        }
                        // Mark as overdue (will be handled by markOverduePayments function)
                        await tx
                            .update(schema_1.users)
                            .set({ paymentStatus: "overdue" })
                            .where((0, drizzle_orm_1.eq)(schema_1.users.id, user.id));
                        console.log(`   ✅ User ${user.email} marked as overdue`);
                    }
                    else {
                        if (isInFreeTrial) {
                            console.log(`   ✅ Free trial still active (${daysUntilExpiry} days remaining)`);
                        }
                        else {
                            console.log(`   ✅ Subscription still active`);
                        }
                    }
                }
                catch (userError) {
                    console.error(`❌ Error processing user ${user.email} (ID: ${user.id}):`, userError);
                }
            }
        });
        console.info("✅ Subscription renewal check completed");
    }
    catch (error) {
        console.error("❌ Error in subscription renewal check:", error);
    }
};
exports.checkSubscriptionRenewals = checkSubscriptionRenewals;
const fixIncorrectSubscriptionPeriods = async () => {
    try {
        console.info("🔧 Fixing incorrect subscription periods based on payment amounts...");
        await db_1.postgresDb.transaction(async (tx) => {
            // Find payments with incorrect subscription periods based on amount
            const incorrectPayments = await tx
                .select({
                id: schema_1.payments.id,
                userId: schema_1.payments.userId,
                amount: schema_1.payments.amount,
                subscriptionPeriod: schema_1.payments.subscriptionPeriod
            })
                .from(schema_1.payments)
                .where((0, drizzle_orm_1.eq)(schema_1.payments.status, "successful"));
            let fixedCount = 0;
            for (const payment of incorrectPayments) {
                const amount = parseFloat(payment.amount);
                let correctPeriod;
                // Determine correct subscription period based on amount (NEW PRICING)
                if (amount >= 360) {
                    correctPeriod = 12; // Annual - ₵360
                }
                else if (amount >= 108) {
                    correctPeriod = 3; // Quarterly - ₵108
                }
                else {
                    correctPeriod = 1; // Monthly - ₵40
                }
                // Update if incorrect
                if (payment.subscriptionPeriod !== correctPeriod) {
                    await tx
                        .update(schema_1.payments)
                        .set({ subscriptionPeriod: correctPeriod })
                        .where((0, drizzle_orm_1.eq)(schema_1.payments.id, payment.id));
                    console.log(`   ✅ Fixed payment ID ${payment.id}: ₵${amount} → ${correctPeriod} months`);
                    fixedCount++;
                }
            }
            console.info(`✅ Fixed ${fixedCount} payment records with incorrect subscription periods`);
        });
    }
    catch (error) {
        console.error("❌ Error fixing subscription periods:", error);
    }
};
exports.fixIncorrectSubscriptionPeriods = fixIncorrectSubscriptionPeriods;
const markOverduePayments = async () => {
    try {
        console.info("🔍 Checking for overdue payments...");
        const now = new Date();
        await db_1.postgresDb.transaction(async (tx) => {
            // Find users whose payment is overdue
            const usersToCheck = await tx
                .select({
                id: schema_1.users.id,
                email: schema_1.users.email,
                paymentStatus: schema_1.users.paymentStatus,
                nextPaymentDue: schema_1.users.nextPaymentDue,
                createdAt: schema_1.users.createdAt,
                role: schema_1.users.role
            })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.lt)(schema_1.users.nextPaymentDue, now), // Users past their payment date
            (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.users.paymentStatus, "paid"), (0, drizzle_orm_1.eq)(schema_1.users.paymentStatus, "pending")), // Include "pending" users too
            (0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.users.role, "admin"), (0, drizzle_orm_1.eq)(schema_1.users.role, "superadmin")) // Only admins and superadmins pay
            ));
            let overdueCount = 0;
            let trialExpiredCount = 0;
            for (const user of usersToCheck) {
                // Check if user has made any payments (to determine if in trial)
                const hasPayments = await tx
                    .select({ count: (0, drizzle_orm_1.sql) `count(*)` })
                    .from(schema_1.payments)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.userId, user.id), (0, drizzle_orm_1.eq)(schema_1.payments.status, "successful")));
                const isInFreeTrial = hasPayments[0].count === 0;
                const daysSinceCreation = (0, dayjs_1.default)(now).diff((0, dayjs_1.default)(user.createdAt), 'day');
                if (isInFreeTrial) {
                    console.log(`   🎁 ${user.email}: Free trial expired after ${daysSinceCreation} days`);
                    trialExpiredCount++;
                }
                else {
                    console.log(`   💰 ${user.email}: Subscription payment overdue`);
                    overdueCount++;
                }
                // Mark as overdue regardless of trial status
                await tx
                    .update(schema_1.users)
                    .set({ paymentStatus: "overdue" })
                    .where((0, drizzle_orm_1.eq)(schema_1.users.id, user.id));
            }
            console.info(`✅ Payment status update completed:`);
            console.info(`   🎁 Free trials expired: ${trialExpiredCount}`);
            console.info(`   💰 Subscriptions overdue: ${overdueCount}`);
            console.info(`   📧 Total users marked overdue: ${usersToCheck.length}`);
        });
    }
    catch (error) {
        console.error("❌ Error updating overdue payments:", error);
    }
};
exports.markOverduePayments = markOverduePayments;
