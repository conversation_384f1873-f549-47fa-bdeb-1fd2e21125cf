"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1349],{71349:(e,t,a)=>{a.d(t,{A:()=>T});var n=a(12115),o=a(4617),c=a.n(o),i=a(70527),r=a(31049),l=a(27651),d=a(43288),s=a(99907),g=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};let b=e=>{var{prefixCls:t,className:a,hoverable:o=!0}=e,i=g(e,["prefixCls","className","hoverable"]);let{getPrefixCls:l}=n.useContext(r.QO),d=l("card",t),s=c()("".concat(d,"-grid"),a,{["".concat(d,"-grid-hoverable")]:o});return n.createElement("div",Object.assign({},i,{className:s}))};var p=a(67548),u=a(70695),m=a(1086),h=a(56204);let y=e=>{let{antCls:t,componentCls:a,headerHeight:n,headerPadding:o,tabsMarginBottom:c}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:"0 ".concat((0,p.zA)(o)),color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorderSecondary),borderRadius:"".concat((0,p.zA)(e.borderRadiusLG)," ").concat((0,p.zA)(e.borderRadiusLG)," 0 0")},(0,u.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},u.L9),{["\n          > ".concat(a,"-typography,\n          > ").concat(a,"-typography-edit-content\n        ")]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),["".concat(t,"-tabs-top")]:{clear:"both",marginBottom:c,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorderSecondary)}}})},v=e=>{let{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:"\n      ".concat((0,p.zA)(o)," 0 0 0 ").concat(a,",\n      0 ").concat((0,p.zA)(o)," 0 0 ").concat(a,",\n      ").concat((0,p.zA)(o)," ").concat((0,p.zA)(o)," 0 0 ").concat(a,",\n      ").concat((0,p.zA)(o)," 0 0 0 ").concat(a," inset,\n      0 ").concat((0,p.zA)(o)," 0 0 ").concat(a," inset;\n    "),transition:"all ".concat(e.motionDurationMid),"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},f=e=>{let{componentCls:t,iconCls:a,actionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:c,actionsBg:i}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(c),display:"flex",borderRadius:"0 0 ".concat((0,p.zA)(e.borderRadiusLG)," ").concat((0,p.zA)(e.borderRadiusLG))},(0,u.t6)()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:"color ".concat(e.motionDurationMid)},["a:not(".concat(t,"-btn), > ").concat(a)]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:(0,p.zA)(e.fontHeight),transition:"color ".concat(e.motionDurationMid),"&:hover":{color:e.colorPrimary}},["> ".concat(a)]:{fontSize:o,lineHeight:(0,p.zA)(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(c)}}})},S=e=>Object.assign(Object.assign({margin:"".concat((0,p.zA)(e.calc(e.marginXXS).mul(-1).equal())," 0"),display:"flex"},(0,u.t6)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},u.L9),"&-description":{color:e.colorTextDescription}}),z=e=>{let{componentCls:t,colorFillAlter:a,headerPadding:n,bodyPadding:o}=e;return{["".concat(t,"-head")]:{padding:"0 ".concat((0,p.zA)(n)),background:a,"&-title":{fontSize:e.fontSize}},["".concat(t,"-body")]:{padding:"".concat((0,p.zA)(e.padding)," ").concat((0,p.zA)(o))}}},O=e=>{let{componentCls:t}=e;return{overflow:"hidden",["".concat(t,"-body")]:{userSelect:"none"}}},x=e=>{let{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:o,boxShadowTertiary:c,bodyPadding:i,extraColor:r}=e;return{[t]:Object.assign(Object.assign({},(0,u.dF)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,["&:not(".concat(t,"-bordered)")]:{boxShadow:c},["".concat(t,"-head")]:y(e),["".concat(t,"-extra")]:{marginInlineStart:"auto",color:r,fontWeight:"normal",fontSize:e.fontSize},["".concat(t,"-body")]:Object.assign({padding:i,borderRadius:"0 0 ".concat((0,p.zA)(e.borderRadiusLG)," ").concat((0,p.zA)(e.borderRadiusLG))},(0,u.t6)()),["".concat(t,"-grid")]:v(e),["".concat(t,"-cover")]:{"> *":{display:"block",width:"100%",borderRadius:"".concat((0,p.zA)(e.borderRadiusLG)," ").concat((0,p.zA)(e.borderRadiusLG)," 0 0")}},["".concat(t,"-actions")]:f(e),["".concat(t,"-meta")]:S(e)}),["".concat(t,"-bordered")]:{border:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(o),["".concat(t,"-cover")]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},["".concat(t,"-hoverable")]:{cursor:"pointer",transition:"box-shadow ".concat(e.motionDurationMid,", border-color ").concat(e.motionDurationMid),"&:hover":{borderColor:"transparent",boxShadow:a}},["".concat(t,"-contain-grid")]:{borderRadius:"".concat((0,p.zA)(e.borderRadiusLG)," ").concat((0,p.zA)(e.borderRadiusLG)," 0 0 "),["".concat(t,"-body")]:{display:"flex",flexWrap:"wrap"},["&:not(".concat(t,"-loading) ").concat(t,"-body")]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},["".concat(t,"-contain-tabs")]:{["> div".concat(t,"-head")]:{minHeight:0,["".concat(t,"-head-title, ").concat(t,"-extra")]:{paddingTop:n}}},["".concat(t,"-type-inner")]:z(e),["".concat(t,"-loading")]:O(e),["".concat(t,"-rtl")]:{direction:"rtl"}}},j=e=>{let{componentCls:t,bodyPaddingSM:a,headerPaddingSM:n,headerHeightSM:o,headerFontSizeSM:c}=e;return{["".concat(t,"-small")]:{["> ".concat(t,"-head")]:{minHeight:o,padding:"0 ".concat((0,p.zA)(n)),fontSize:c,["> ".concat(t,"-head-wrapper")]:{["> ".concat(t,"-extra")]:{fontSize:e.fontSize}}},["> ".concat(t,"-body")]:{padding:a}},["".concat(t,"-small").concat(t,"-contain-tabs")]:{["> ".concat(t,"-head")]:{["".concat(t,"-head-title, ").concat(t,"-extra")]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},A=(0,m.OF)("Card",e=>{let t=(0,h.oX)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[x(t),j(t)]},e=>{var t,a;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:"".concat(e.paddingSM,"px 0"),tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(a=e.headerPadding)&&void 0!==a?a:e.paddingLG}});var w=a(51388),E=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};let N=e=>{let{actionClasses:t,actions:a=[],actionStyle:o}=e;return n.createElement("ul",{className:t,style:o},a.map((e,t)=>n.createElement("li",{style:{width:"".concat(100/a.length,"%")},key:"action-".concat(t)},n.createElement("span",null,e))))},C=n.forwardRef((e,t)=>{let a;let{prefixCls:o,className:g,rootClassName:p,style:u,extra:m,headStyle:h={},bodyStyle:y={},title:v,loading:f,bordered:S,variant:z,size:O,type:x,cover:j,actions:C,tabList:L,children:T,activeTabKey:P,defaultActiveTabKey:G,tabBarExtraContent:R,hoverable:B,tabProps:H={},classNames:W,styles:I}=e,k=E(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:M,direction:D,card:q}=n.useContext(r.QO),[F]=(0,w.A)("card",z,S),X=e=>{var t;return c()(null===(t=null==q?void 0:q.classNames)||void 0===t?void 0:t[e],null==W?void 0:W[e])},K=e=>{var t;return Object.assign(Object.assign({},null===(t=null==q?void 0:q.styles)||void 0===t?void 0:t[e]),null==I?void 0:I[e])},_=n.useMemo(()=>{let e=!1;return n.Children.forEach(T,t=>{(null==t?void 0:t.type)===b&&(e=!0)}),e},[T]),Q=M("card",o),[J,U,V]=A(Q),Y=n.createElement(d.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},T),Z=void 0!==P,$=Object.assign(Object.assign({},H),{[Z?"activeKey":"defaultActiveKey"]:Z?P:G,tabBarExtraContent:R}),ee=(0,l.A)(O),et=ee&&"default"!==ee?ee:"large",ea=L?n.createElement(s.A,Object.assign({size:et},$,{className:"".concat(Q,"-head-tabs"),onChange:t=>{var a;null===(a=e.onTabChange)||void 0===a||a.call(e,t)},items:L.map(e=>{var{tab:t}=e;return Object.assign({label:t},E(e,["tab"]))})})):null;if(v||m||ea){let e=c()("".concat(Q,"-head"),X("header")),t=c()("".concat(Q,"-head-title"),X("title")),o=c()("".concat(Q,"-extra"),X("extra")),i=Object.assign(Object.assign({},h),K("header"));a=n.createElement("div",{className:e,style:i},n.createElement("div",{className:"".concat(Q,"-head-wrapper")},v&&n.createElement("div",{className:t,style:K("title")},v),m&&n.createElement("div",{className:o,style:K("extra")},m)),ea)}let en=c()("".concat(Q,"-cover"),X("cover")),eo=j?n.createElement("div",{className:en,style:K("cover")},j):null,ec=c()("".concat(Q,"-body"),X("body")),ei=Object.assign(Object.assign({},y),K("body")),er=n.createElement("div",{className:ec,style:ei},f?Y:T),el=c()("".concat(Q,"-actions"),X("actions")),ed=(null==C?void 0:C.length)?n.createElement(N,{actionClasses:el,actionStyle:K("actions"),actions:C}):null,es=(0,i.A)(k,["onTabChange"]),eg=c()(Q,null==q?void 0:q.className,{["".concat(Q,"-loading")]:f,["".concat(Q,"-bordered")]:"borderless"!==F,["".concat(Q,"-hoverable")]:B,["".concat(Q,"-contain-grid")]:_,["".concat(Q,"-contain-tabs")]:null==L?void 0:L.length,["".concat(Q,"-").concat(ee)]:ee,["".concat(Q,"-type-").concat(x)]:!!x,["".concat(Q,"-rtl")]:"rtl"===D},g,p,U,V),eb=Object.assign(Object.assign({},null==q?void 0:q.style),u);return J(n.createElement("div",Object.assign({ref:t},es,{className:eg,style:eb}),a,eo,er,ed))});var L=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};C.Grid=b,C.Meta=e=>{let{prefixCls:t,className:a,avatar:o,title:i,description:l}=e,d=L(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=n.useContext(r.QO),g=s("card",t),b=c()("".concat(g,"-meta"),a),p=o?n.createElement("div",{className:"".concat(g,"-meta-avatar")},o):null,u=i?n.createElement("div",{className:"".concat(g,"-meta-title")},i):null,m=l?n.createElement("div",{className:"".concat(g,"-meta-description")},l):null,h=u||m?n.createElement("div",{className:"".concat(g,"-meta-detail")},u,m):null;return n.createElement("div",Object.assign({},d,{className:b}),p,h)};let T=C}}]);