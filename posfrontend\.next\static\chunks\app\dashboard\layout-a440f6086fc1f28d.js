(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1954,6402],{16944:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,36128,23)),Promise.resolve().then(a.t.bind(a,67973,23)),Promise.resolve().then(a.bind(a,11049))},1227:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(85407),s=a(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var i=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(i.A,(0,n.A)({},e,{ref:t,icon:r}))})},97139:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(85407),s=a(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var i=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(i.A,(0,n.A)({},e,{ref:t,icon:r}))})},72278:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(85407),s=a(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var i=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(i.A,(0,n.A)({},e,{ref:t,icon:r}))})},5099:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var n=a(85407),s=a(12115);let r={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 00-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 100 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 00-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 00-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6z"}}]},name:"shopping-cart",theme:"outlined"};var i=a(84021);let l=s.forwardRef(function(e,t){return s.createElement(i.A,(0,n.A)({},e,{ref:t,icon:r}))})},9365:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var n=a(12115),s=a(4617),r=a.n(s),i=a(31049),l=a(67548),o=a(70695),c=a(1086),d=a(56204);let u=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:a,colorSplit:n,lineWidth:s,textPaddingInline:r,orientationMargin:i,verticalMarginInline:c}=e;return{[t]:Object.assign(Object.assign({},(0,o.dF)(e)),{borderBlockStart:"".concat((0,l.zA)(s)," solid ").concat(n),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:c,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,l.zA)(s)," solid ").concat(n)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,l.zA)(e.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(t,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,l.zA)(e.dividerHorizontalWithTextGutterMargin)," 0"),color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(n),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,l.zA)(s)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(t,"-with-text-start")]:{"&::before":{width:"calc(".concat(i," * 100%)")},"&::after":{width:"calc(100% - ".concat(i," * 100%)")}},["&-horizontal".concat(t,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(i," * 100%)")},"&::after":{width:"calc(".concat(i," * 100%)")}},["".concat(t,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:r},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:"".concat((0,l.zA)(s)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(t,"-dashed")]:{borderInlineStartWidth:s,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:"".concat((0,l.zA)(s)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(t,"-dotted")]:{borderInlineStartWidth:s,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(t,"-with-text")]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},["&-horizontal".concat(t,"-with-text-start").concat(t,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(t,"-inner-text")]:{paddingInlineStart:a}},["&-horizontal".concat(t,"-with-text-end").concat(t,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(t,"-inner-text")]:{paddingInlineEnd:a}}})}},m=(0,c.OF)("Divider",e=>[u((0,d.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,dividerHorizontalGutterMargin:e.marginLG,sizePaddingEdgeHorizontal:0}))],e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var p=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(a[n[s]]=e[n[s]]);return a};let h=e=>{let{getPrefixCls:t,direction:a,className:s,style:l}=(0,i.TP)("divider"),{prefixCls:o,type:c="horizontal",orientation:d="center",orientationMargin:u,className:h,rootClassName:f,children:y,dashed:g,variant:v="solid",plain:x,style:b}=e,S=p(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),w=t("divider",o),[j,A,E]=m(w),P=!!y,N=n.useMemo(()=>"left"===d?"rtl"===a?"end":"start":"right"===d?"rtl"===a?"start":"end":d,[a,d]),k="start"===N&&null!=u,R="end"===N&&null!=u,z=r()(w,s,A,E,"".concat(w,"-").concat(c),{["".concat(w,"-with-text")]:P,["".concat(w,"-with-text-").concat(N)]:P,["".concat(w,"-dashed")]:!!g,["".concat(w,"-").concat(v)]:"solid"!==v,["".concat(w,"-plain")]:!!x,["".concat(w,"-rtl")]:"rtl"===a,["".concat(w,"-no-default-orientation-margin-start")]:k,["".concat(w,"-no-default-orientation-margin-end")]:R},h,f),C=n.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return j(n.createElement("div",Object.assign({className:z,style:Object.assign(Object.assign({},l),b)},S,{role:"separator"}),y&&"vertical"!==c&&n.createElement("span",{className:"".concat(w,"-inner-text"),style:{marginInlineStart:k?C:void 0,marginInlineEnd:R?C:void 0}},y)))}},86402:(e,t,a)=>{"use strict";a.d(t,{default:()=>f});var n=a(95155),s=a(12115),r=a(83391),i=a(76046),l=a(29395);let o=e=>{let{children:t}=e,{user:a,accessToken:o}=(0,r.d4)(e=>e.auth),c=(0,i.useRouter)(),d=(0,i.usePathname)(),[u,m]=(0,s.useState)(!1),[p,h]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{m(!0),console.log("AuthGuard mounted on client",{isAuthenticated:!!a&&!!o,userRole:null==a?void 0:a.role,pathname:d})},[o,d,a]),(0,s.useEffect)(()=>{!u||a&&o||p||(console.log("AuthGuard - Redirecting to login",{pathname:d}),h(!0),sessionStorage.setItem("redirectUrl",d),setTimeout(()=>{c.push("/")},100))},[a,o,c,d,u,p]),u&&a&&o)?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)(l.A,{fullScreen:!0})};var c=a(20148);let d=e=>{let{children:t,allowedRoles:a,fallbackPath:o="/dashboard"}=e,{user:d}=(0,r.d4)(e=>e.auth),u=(0,i.useRouter)();return((0,s.useEffect)(()=>{d&&!a.includes(d.role)&&u.push(o)},[d,a,u,o]),d)?a.includes(d.role)?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)("div",{className:"flex h-screen w-full items-center justify-center",children:(0,n.jsx)(c.A,{message:"Access Denied",description:"You don't have permission to access this page. This area requires ".concat(a.join(" or ")," role."),type:"error",showIcon:!0})}):(0,n.jsx)(l.A,{fullScreen:!0})};var u=a(70854),m=a(21455),p=a.n(m);let h=e=>{let{children:t}=e,{user:a}=(0,r.d4)(e=>e.auth),o=(0,i.useRouter)(),c=(0,i.usePathname)(),{needsPayment:d,status:m,isActive:h,daysRemaining:f}=(0,u._)();if((0,s.useRef)(null==a?void 0:a.paymentStatus),(0,r.wA)(),console.log("\uD83D\uDEE1️ PaymentGuard COMPONENT RENDERED - This confirms PaymentGuard is being called"),console.log("\uD83D\uDEE1️ PaymentGuard - Payment Status Check:",{userRole:null==a?void 0:a.role,userPaymentStatus:null==a?void 0:a.paymentStatus,needsPayment:d,status:m,isActive:h,daysRemaining:f,pathname:c,lastPaymentDate:null==a?void 0:a.lastPaymentDate,nextPaymentDue:null==a?void 0:a.nextPaymentDue,createdAt:null==a?void 0:a.createdAt}),(null==a?void 0:a.createdAt)&&(null==a?void 0:a.paymentStatus)==="paid"&&!(null==a?void 0:a.lastPaymentDate)){let e=p()().diff(p()(a.createdAt),"day");console.log("\uD83C\uDF81 PaymentGuard - FREE TRIAL USER DETECTED:",{email:a.email,daysSinceCreation:e,isInFreeTrial:e<=90,trialDaysRemaining:Math.max(0,90-e),paymentStatus:a.paymentStatus,nextPaymentDue:a.nextPaymentDue})}if((null==a?void 0:a.paymentStatus)==="pending"&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER DETECTED! Should redirect to payment page"),(null==a?void 0:a.paymentStatus)!=="paid"){let e=sessionStorage.getItem("lastPaymentCheck"),t=Date.now();(!e||t-parseInt(e)>3e4)&&(console.log("\uD83D\uDD04 PaymentGuard - Forcing user data refresh due to non-paid status"),sessionStorage.setItem("lastPaymentCheck",t.toString()),window.__FORCE_REFRESH_USER_DATA&&window.__FORCE_REFRESH_USER_DATA())}if((0,s.useEffect)(()=>{a&&"superadmin"!==a.role&&"paid"!==a.paymentStatus&&o.replace("/payment")},[a,o]),(0,s.useEffect)(()=>{if(!a||"superadmin"===a.role)return;let e=c.includes("/profile")||c.includes("/dashboard/profile"),t=!0===window.__PROFILE_UPDATE_IN_PROGRESS,n=window.__LAST_PROFILE_UPDATE_PATH||"";if(e||t){console.log("PaymentGuard - Skipping payment check:",{isProfilePage:e,isProfileUpdateInProgress:t,pathname:c,lastProfileUpdatePath:n});return}let s="/payment"===c;console.log("PaymentGuard - Payment check status:",{pathname:c,isProfilePage:e,isExemptPath:s,needsPayment:d});let r=d&&!s&&"paid"!==a.paymentStatus||!s&&"paid"!==a.paymentStatus;console.log("\uD83D\uDD0D PaymentGuard - Redirect decision breakdown:",{needsPayment:d,isExemptPath:s,userPaymentStatus:a.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==a.paymentStatus,shouldRedirectToPayment:r,pathname:c}),"pending"===a.paymentStatus&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER REDIRECT CHECK:",{needsPayment:d,isExemptPath:s,userPaymentStatus:a.paymentStatus,shouldRedirectToPayment:r,"Will redirect?":r?"YES":"NO"}),r?(console.log("\uD83D\uDEA8 PaymentGuard - Redirecting to payment page from:",c),o.push("/payment")):"paid"===a.paymentStatus&&d?console.log("⚠️ PaymentGuard - User has paid status but needsPayment is true. This might indicate a cache issue."):"pending"!==a.paymentStatus||r||console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER NOT REDIRECTED! This is the bug!")},[a,d,o,c]),(0,s.useEffect)(()=>{a&&"superadmin"!==a.role&&"paid"!==a.paymentStatus&&"/payment"!==window.location.pathname&&o.replace("/payment")},[a,o]),!a)return(0,n.jsx)(l.A,{tip:"Checking payment status...",fullScreen:!0});if("superadmin"===a.role)return(0,n.jsx)(n.Fragment,{children:t});let y=c.includes("/profile")||c.includes("/dashboard/profile"),g=!0===window.__PROFILE_UPDATE_IN_PROGRESS,v=window.__LAST_PROFILE_UPDATE_PATH||"";if(y||g)return console.log("PaymentGuard (render) - Skipping payment check:",{isProfilePage:y,isProfileUpdateInProgress:g,pathname:c,lastProfileUpdatePath:v}),(0,n.jsx)(n.Fragment,{children:t});let x="/payment"===c,b=d&&!x&&"paid"!==a.paymentStatus||!x&&"paid"!==a.paymentStatus;return(console.log("\uD83C\uDFA8 PaymentGuard (render) - Final render decision:",{needsPayment:d,isExemptPath:x,userPaymentStatus:a.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==a.paymentStatus,shouldShowPaymentLoading:b,pathname:c}),b)?(0,n.jsx)(l.A,{tip:"Checking payment status...",fullScreen:!0}):(0,n.jsx)(n.Fragment,{children:t})},f=e=>{let{children:t,allowedRoles:a=["superadmin","admin","cashier"],checkPayment:s=!0,fallbackPath:r="/dashboard"}=e;return(0,n.jsx)(o,{children:s?(0,n.jsx)(h,{children:(0,n.jsx)(d,{allowedRoles:a,fallbackPath:r,children:t})}):(0,n.jsx)(d,{allowedRoles:a,fallbackPath:r,children:t})})}},11049:(e,t,a)=>{"use strict";a.d(t,{default:()=>H});var n=a(95155),s=a(12115),r=a(14232),i=a(40158),l=a(44287),o=a(83391),c=a(20148),d=a(43316),u=a(76046),m=a(70854);let p=()=>{let e=(0,o.d4)(e=>e.auth.user),t=(0,u.useRouter)(),[a,r]=(0,s.useState)(!0),{daysRemaining:i,needsPayment:l,status:p}=(0,m._)();return e&&a&&l&&"superadmin"!==e.role?(0,n.jsx)(c.A,{message:"Payment Reminder: ".concat("overdue"===p||null!==i&&i<0?"Your subscription payment is overdue. Please make a payment to continue using the system.":0===i?"Your subscription payment is due today. Please make a payment to avoid service interruption.":null!==i?"Your subscription payment is due in ".concat(i," day").concat(i>1?"s":"","."):"pending"===p?"Your payment is pending verification. If you've already made a payment, please wait for confirmation.":"Your subscription is inactive. Please make a payment to activate your account."),type:"overdue"===p||null!==i&&i<0?"error":null!==i&&i<=3?"warning":"info",showIcon:!0,action:(0,n.jsx)(d.Ay,{size:"small",type:"primary",onClick:()=>{t.push("/payment")},children:"Make Payment"}),closable:!0,onClose:()=>r(!1),className:"mb-4"}):null};var h=a(86402),f=a(29395),y=a(14638),g=a(73937),v=a(71349),x=a(46102),b=a(9365),S=a(97139),w=a(85407);let j={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"mobile",theme:"outlined"};var A=a(84021),E=s.forwardRef(function(e,t){return s.createElement(A.A,(0,w.A)({},e,{ref:t,icon:j}))}),P=a(79624),N=a(5099),k=a(1227),R=a(75365);let z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"}}]},name:"desktop",theme:"outlined"};var C=s.forwardRef(function(e,t){return s.createElement(A.A,(0,w.A)({},e,{ref:t,icon:z}))});let{Title:I,Text:O,Paragraph:D}=g.A,_=()=>{let[e,t]=(0,s.useState)(null),[a,r]=(0,s.useState)(!1),[i,l]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1);(0,s.useEffect)(()=>{(()=>{let e=window.matchMedia("(display-mode: standalone)").matches,t=!0===window.navigator.standalone;l(e||t)})();let e=e=>{e.preventDefault(),t(e),i||setTimeout(()=>{r(!0)},1e4)},a=()=>{l(!0),r(!1),t(null)};return window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",a),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",a)}},[i]);let u=async()=>{if(e)try{await e.prompt();let{outcome:a}=await e.userChoice;"accepted"===a?console.log("PWA installation accepted"):console.log("PWA installation dismissed"),t(null),r(!1),c(!1)}catch(e){console.error("Error during PWA installation:",e)}};return i||sessionStorage.getItem("pwa-install-dismissed")?null:(0,n.jsxs)(n.Fragment,{children:[a&&e&&(0,n.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,n.jsxs)(v.A,{className:"shadow-lg border-blue-500 border-2",style:{maxWidth:300},actions:[(0,n.jsx)(d.Ay,{type:"primary",icon:(0,n.jsx)(S.A,{}),onClick:()=>{c(!0)},className:"w-full",children:"Install App"},"install")],children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(E,{className:"text-2xl text-blue-500 mb-2"}),(0,n.jsx)(I,{level:5,className:"mb-1",children:"Install NEXAPO POS"}),(0,n.jsx)(O,{type:"secondary",className:"text-sm",children:"Get faster access and work offline"})]}),(0,n.jsx)(d.Ay,{type:"text",icon:(0,n.jsx)(P.A,{}),onClick:()=>{r(!1),sessionStorage.setItem("pwa-install-dismissed","true")},className:"absolute top-2 right-2 p-1",size:"small"})]})}),(0,n.jsx)(x.A,{title:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(S.A,{className:"mr-2 text-blue-500"}),"Install NEXAPO POS App"]}),open:o,onCancel:()=>c(!1),footer:[(0,n.jsx)(d.Ay,{onClick:()=>c(!1),children:"Maybe Later"},"cancel"),(0,n.jsx)(d.Ay,{type:"primary",icon:(0,n.jsx)(S.A,{}),onClick:u,disabled:!e,children:"Install Now"},"install")],width:500,children:(0,n.jsxs)("div",{className:"py-4",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(N.A,{className:"text-3xl text-blue-500"})}),(0,n.jsx)(I,{level:4,className:"mb-2",children:"Get the full NEXAPO POS experience"}),(0,n.jsx)(O,{type:"secondary",children:"Install our app for faster access and enhanced offline capabilities"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(k.A,{className:"text-green-500 mt-1"}),(0,n.jsxs)("div",{children:[(0,n.jsx)(O,{strong:!0,children:"Lightning Fast"}),(0,n.jsx)("br",{}),(0,n.jsx)(O,{type:"secondary",className:"text-sm",children:"Instant loading and smooth performance"})]})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(R.A,{className:"text-green-500 mt-1"}),(0,n.jsxs)("div",{children:[(0,n.jsx)(O,{strong:!0,children:"Works Offline"}),(0,n.jsx)("br",{}),(0,n.jsx)(O,{type:"secondary",className:"text-sm",children:"Continue making sales even without internet"})]})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(E,{className:"text-green-500 mt-1"}),(0,n.jsxs)("div",{children:[(0,n.jsx)(O,{strong:!0,children:"Native App Experience"}),(0,n.jsx)("br",{}),(0,n.jsx)(O,{type:"secondary",className:"text-sm",children:"Full-screen mode with app-like navigation"})]})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(C,{className:"text-green-500 mt-1"}),(0,n.jsxs)("div",{children:[(0,n.jsx)(O,{strong:!0,children:"Desktop & Mobile"}),(0,n.jsx)("br",{}),(0,n.jsx)(O,{type:"secondary",className:"text-sm",children:"Works perfectly on all your devices"})]})]})]}),(0,n.jsx)(b.A,{}),(0,n.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,n.jsx)(O,{strong:!0,className:"text-blue-700",children:"\uD83D\uDCA1 Pro Tip:"}),(0,n.jsx)("br",{}),(0,n.jsx)(O,{className:"text-blue-600 text-sm",children:"After installation, you can access NEXAPO POS directly from your home screen or desktop, just like any other app!"})]})]})})]})};var T=a(80766),G=a(72278);let L=()=>{let[e,t]=(0,s.useState)(!1),[a,r]=(0,s.useState)(null),i=(0,s.useCallback)(e=>{(null==a?void 0:a.waiting)&&a.waiting.postMessage({type:"SKIP_WAITING"}),T.Ay.destroy(e)},[a]),l=(0,s.useCallback)(e=>{t(!1),T.Ay.destroy(e)},[]),o=(0,s.useCallback)(()=>{let e="pwa-update";T.Ay.info({key:e,message:"App Update Available",description:"A new version of NEXAPO POS is available. Update now for the latest features and improvements.",duration:0,placement:"topRight",btn:(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(d.Ay,{type:"primary",size:"small",icon:(0,n.jsx)(G.A,{}),onClick:()=>i(e),children:"Update Now"}),(0,n.jsx)(d.Ay,{size:"small",icon:(0,n.jsx)(P.A,{}),onClick:()=>l(e),children:"Later"})]}),onClose:()=>l(e)})},[l,i]);return(0,s.useEffect)(()=>{"serviceWorker"in navigator&&(navigator.serviceWorker.addEventListener("controllerchange",()=>{window.location.reload()}),navigator.serviceWorker.ready.then(e=>{r(e),e.addEventListener("updatefound",()=>{let a=e.installing;a&&a.addEventListener("statechange",()=>{"installed"===a.state&&navigator.serviceWorker.controller&&(t(!0),o())})}),e.waiting&&(t(!0),o())}))},[o]),null};var M=a(63065),W=a(7875),F=a(67259);let H=e=>{let{children:t}=e,[a,c]=(0,s.useState)(!1),{setIsOpen:d,isMobile:m}=(0,y.V)(),g=(0,o.wA)(),v=(0,o.d4)(e=>e.auth.user),x=(0,u.useRouter)(),b=(0,o.d4)(e=>e.auth.accessToken),S=(0,u.usePathname)();return((0,F.X)(),s.useEffect(()=>{v&&g(M.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0})).then(e=>{var t,a,n,s,r,i,l,o,c,d,u,m;if((null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.success)&&e.data.data){let t=e.data.data;b?(console.log("[Sync] Updating Redux state with fresh user and token:",t,b),g((0,W.gV)({user:t,accessToken:b}))):console.warn("[Sync] No access token found in Redux state; not updating user.")}else{let t="";console.log("[Sync] Error message:",t="string"==typeof(null==e?void 0:e.error)?e.error.toLowerCase():(null==e?void 0:null===(s=e.error)||void 0===s?void 0:null===(n=s.message)||void 0===n?void 0:null===(a=n.toLowerCase)||void 0===a?void 0:a.call(n))||(null==e?void 0:null===(o=e.error)||void 0===o?void 0:null===(l=o.data)||void 0===l?void 0:null===(i=l.message)||void 0===i?void 0:null===(r=i.toLowerCase)||void 0===r?void 0:r.call(i))||(null==e?void 0:null===(m=e.error)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:null===(d=u.error)||void 0===d?void 0:null===(c=d.toLowerCase)||void 0===c?void 0:c.call(d))||""),t.includes("payment required")&&(v&&"paid"===v.paymentStatus&&b?(console.log("[Sync] Setting user paymentStatus to 'pending' with token:",b),g((0,W.gV)({user:{...v,paymentStatus:"pending"},accessToken:b}))):console.warn("[Sync] No access token found in Redux state; not updating user to pending."),console.log("[Sync] Redirecting to /payment"),x.replace("/payment"))}})},[g,v,x,b]),s.useEffect(()=>{v&&"superadmin"!==v.role&&"paid"!==v.paymentStatus&&"/payment"!==S&&(console.log("[Watcher] Payment status changed, redirecting to /payment"),x.replace("/payment"))},[v,x,S]),(0,s.useEffect)(()=>{c(!0),console.log("ProtectedDashboardContent mounted on client",{isMobile:m})},[m]),(0,s.useEffect)(()=>()=>{d(!1),console.log("ProtectedDashboardContent unmounted - closing sidebar")},[d]),a)?(0,n.jsxs)(h.default,{children:[(0,n.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,n.jsx)(i.R,{}),(0,n.jsx)("div",{className:"fixed left-0 top-0 h-full z-20 hidden lg:block",children:(0,n.jsx)(r.B,{})}),(0,n.jsxs)("div",{className:"w-full lg:ml-[290px] bg-white flex flex-col h-screen min-w-0",children:[(0,n.jsx)("div",{className:"sticky top-0 z-30 w-full bg-white border-b border-gray-200",children:(0,n.jsx)(l.Y,{})}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto min-w-0",children:(0,n.jsxs)("main",{className:"isolate mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10",children:[(0,n.jsx)(p,{}),t]})})]})]}),(0,n.jsx)(_,{}),(0,n.jsx)(L,{})]}):(0,n.jsx)(f.A,{tip:"Loading dashboard..."})}},29395:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var n=a(95155);a(12115);var s=a(72093),r=a(16419);let i=e=>{let{size:t="large",fullScreen:a=!1,tip:i}=e,l=(0,n.jsx)(r.A,{style:{fontSize:24},spin:!0});return a?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80",children:(0,n.jsx)(s.A,{size:t,indicator:l,tip:i})}):(0,n.jsx)("div",{className:"flex flex-col h-full w-full items-center justify-center py-8",children:(0,n.jsx)(s.A,{size:t,indicator:l,tip:i})})}},70854:(e,t,a)=>{"use strict";a.d(t,{_:()=>l});var n=a(12115),s=a(83391),r=a(21455),i=a.n(r);let l=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,a]=(0,n.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,n.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,n=!1,s=!0,r="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){n=!0,s=!1,r="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let r=i()(e.nextPaymentDue),l=i()();if(t=r.diff(l,"day"),a){let a=i()().diff(i()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:n,needsPayment:s})}}}else"pending"===e.paymentStatus?(n=!1,s=!0,r="pending"):"overdue"===e.paymentStatus?(n=!1,s=!0,r="overdue"):(n=!1,s=!0,r="inactive");a({isActive:n,daysRemaining:t,status:r,needsPayment:s})},[e]),t}},67259:(e,t,a)=>{"use strict";a.d(t,{X:()=>l});var n=a(12115),s=a(83391),r=a(7875),i=a(63065);function l(){let e=(0,s.wA)(),t=(0,s.d4)(e=>e.auth.user),a=(0,s.d4)(e=>e.auth.accessToken),l=(0,n.useRef)(t),o=(0,n.useRef)(a);(0,n.useEffect)(()=>{l.current=t,o.current=a},[t,a]),(0,n.useEffect)(()=>{if(!t||!t.id)return;let a="wss://nexapoapi.up.railway.app",n=new WebSocket(a);return n.onopen=()=>{n.send(JSON.stringify({type:"auth",userId:t.id})),console.log("[WebSocket] Connected and authenticated as user",t.id,"URL:",a),e(i.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0})).then(t=>{var a;(null==t?void 0:null===(a=t.data)||void 0===a?void 0:a.success)&&t.data.data&&o.current&&e((0,r.gV)({user:t.data.data,accessToken:o.current}))})},n.onmessage=t=>{let a=JSON.parse(t.data);if("paymentStatus"===a.type){var n;if((null===(n=l.current)||void 0===n?void 0:n.role)==="superadmin")return;if(console.log("[WebSocket] Received paymentStatus update:",a.status),o.current&&l.current&&"number"==typeof l.current.id){let{id:t,name:n,email:s,phone:i,role:c,createdAt:d,lastPaymentDate:u,nextPaymentDue:m,createdBy:p}=l.current;e((0,r.gV)({user:{id:t,name:n,email:s,phone:i,role:c,createdAt:d,lastPaymentDate:u,nextPaymentDue:m,createdBy:p,paymentStatus:a.status},accessToken:o.current}))}}},n.onerror=e=>{console.error("[WebSocket] Error:",e)},n.onclose=()=>{console.log("[WebSocket] Connection closed")},()=>n.close()},[null==t?void 0:t.id,e])}},36128:()=>{},67973:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[1236,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,2375,6102,2910,766,5565,8408,2270,1703,821,4357,8441,1517,7358],()=>t(16944)),_N_E=e.O()}]);