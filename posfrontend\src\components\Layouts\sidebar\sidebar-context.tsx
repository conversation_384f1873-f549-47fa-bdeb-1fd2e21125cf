"use client";

import { useIsMobile } from "@/hooks/use-mobile";
import { createContext, useContext, useEffect, useState } from "react";

type SidebarState = "expanded" | "collapsed";

type SidebarContextType = {
  state: SidebarState;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isMobile: boolean;
  toggleSidebar: () => void;
};

const SidebarContext = createContext<SidebarContextType | null>(null);

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebarContext must be used within a SidebarProvider");
  }
  return context;
}

export function SidebarProvider({
  children,
  defaultOpen = true,
}: {
  children: React.ReactNode;
  defaultOpen?: boolean;
}) {
  // On mobile, always start with sidebar closed
  const isMobile = useIsMobile();
  // Track previous mobile state to detect transitions
  const [prevIsMobile, setPrevIsMobile] = useState(isMobile);
  const [isOpen, setIsOpen] = useState(isMobile ? false : defaultOpen);
  // Track if sidebar was manually toggled
  const [wasManuallyToggled, setWasManuallyToggled] = useState(false);

  // When screen size changes, update sidebar state
  useEffect(() => {
    // Only auto-adjust if screen size actually changed
    if (prevIsMobile !== isMobile) {
      // If switching to desktop, always open sidebar
      if (!isMobile) {
        setIsOpen(true);
        setWasManuallyToggled(false);
        console.log("Switching to desktop - opening sidebar");
      }
      // If switching to mobile AND sidebar wasn't manually toggled, close it
      else if (!wasManuallyToggled) {
        setIsOpen(false);
        console.log("Switching to mobile - closing sidebar (not manually toggled)");
      }
      // If it was manually toggled, respect that state
      else {
        console.log("Switching to mobile - keeping sidebar state (manually toggled)");
      }

      console.log("SidebarProvider - Screen size changed:", {
        prevIsMobile,
        isMobile,
        isOpen: !isMobile ? true : (wasManuallyToggled ? isOpen : false),
        wasManuallyToggled
      });

      // Update previous mobile state
      setPrevIsMobile(isMobile);
    }
  }, [isMobile, prevIsMobile, wasManuallyToggled, isOpen]);

  function toggleSidebar() {
    // Mark as manually toggled and prevent auto-closing
    setWasManuallyToggled(true);

    // Use a timeout to ensure state updates don't conflict
    setTimeout(() => {
      setIsOpen((prev) => {
        const newState = !prev;
        console.log("Sidebar manually toggled:", { newState, isMobile });
        return newState;
      });
    }, 50);
  }

  return (
    <SidebarContext.Provider
      value={{
        state: isOpen ? "expanded" : "collapsed",
        isOpen,
        setIsOpen,
        isMobile,
        toggleSidebar,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
}
