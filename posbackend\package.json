{"name": "e-api", "version": "1.0.0", "description": "POS System", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node build/index.js", "dev": "nodemon --config nodemon.json", "build": "tsc --pretty --diagnostics", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate:apply --migration-folder=drizzle/migrations", "db:pull": "drizzle-kit pull", "assign-categories": "ts-node src/scripts/assignDefaultCategories.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "drizzle-orm": "^0.40.0", "envalid": "^8.0.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "paystack": "^2.0.1", "pg": "^8.13.3", "pg-connection-string": "^2.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ws": "^8.18.2", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.5", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.11", "@types/ws": "^8.18.1", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.5", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}