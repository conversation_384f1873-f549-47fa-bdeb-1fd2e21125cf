import { Response } from "express";
import { sendResponse } from "./responseHelper";
import { ValidationError } from "../middleware/errorHandler";

/**
 * Validates the request mode.
 * @param res - Express Response object
 * @param mode - The mode from request body
 * @param validModes - Array of allowed modes
 * @returns {boolean} - Returns true if the mode is valid, otherwise sends a response and returns false.
 */
export const validateMode = (
  res: Response,
  mode: string,
  validModes: string[]
): boolean => {
  if (!validModes.includes(mode)) {
    // For backward compatibility, still use sendResponse directly
    // This allows existing code to work without changes
    sendResponse(
      res,
      400,
      false,
      `Invalid mode. Allowed modes: ${validModes.join(", ")}.`
    );
    return false;
  }
  return true;
};

/**
 * Validates the request mode and throws a ValidationError if invalid.
 * This version is meant to be used with the asyncHandler and global error handler.
 *
 * @param mode - The mode from request body
 * @param validModes - Array of allowed modes
 * @throws {ValidationError} - If the mode is invalid
 */
export const validateModeWithError = (
  mode: string,
  validModes: string[]
): void => {
  if (!validModes.includes(mode)) {
    throw new ValidationError(`Invalid mode. Allowed modes: ${validModes.join(", ")}.`);
  }
};
