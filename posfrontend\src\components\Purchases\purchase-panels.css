/* Purchase Form Styles */
.purchase-form .ant-form-item-label > label {
  color: #333 !important;
}

.purchase-form .ant-form-item-explain-error {
  color: #ff4d4f;
  margin-top: 2px;
}

.purchase-form .ant-input,
.purchase-form .ant-input-number,
.purchase-form .ant-select-selector,
.purchase-form .ant-picker {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #333 !important;
}

.purchase-form .ant-input:hover,
.purchase-form .ant-input-number:hover,
.purchase-form .ant-select-selector:hover,
.purchase-form .ant-picker:hover {
  border-color: #40a9ff !important;
}

.purchase-form .ant-input:focus,
.purchase-form .ant-input-number:focus,
.purchase-form .ant-select-selector:focus,
.purchase-form .ant-picker:focus,
.purchase-form .ant-input-focused,
.purchase-form .ant-input-number-focused,
.purchase-form .ant-select-focused .ant-select-selector,
.purchase-form .ant-picker-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.purchase-form .ant-input-number-handler-wrap {
  background-color: #f5f5f5 !important;
}

.purchase-form .ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.purchase-form .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: #40a9ff !important;
}

.purchase-form .ant-select-arrow,
.purchase-form .ant-picker-suffix {
  color: rgba(0, 0, 0, 0.25) !important;
}

.purchase-form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

.purchase-form .ant-input::placeholder,
.purchase-form .ant-select-selection-placeholder,
.purchase-form .ant-input-number-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Ensure InputNumber placeholder is visible */
.purchase-form .ant-input-number .ant-input-number-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Additional specificity for InputNumber */
.purchase-form .ant-input-number input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Target the InputNumber component directly */
.ant-input-number-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Button Styles */
.view-button {
  color: #10b981 !important; /* green-500 */
}

.view-button:hover {
  color: #34d399 !important; /* green-400 */
}

.edit-button {
  color: #3b82f6 !important; /* blue-500 */
}

.edit-button:hover {
  color: #60a5fa !important; /* blue-400 */
}

.delete-button {
  color: #ef4444 !important; /* red-500 */
}

.delete-button:hover {
  color: #f87171 !important; /* red-400 */
}

/* Detail Panel Styles */
.purchase-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  padding: 12px 16px !important;
  border-color: #e8e8e8 !important;
}

.purchase-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  padding: 12px 16px !important;
  border-color: #e8e8e8 !important;
}

.purchase-detail-light .ant-descriptions-bordered {
  border-color: #e8e8e8 !important;
}
