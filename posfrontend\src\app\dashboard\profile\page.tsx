"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Avatar, Divider } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined, CreditCardOutlined, ShopOutlined } from "@ant-design/icons";
import { useRouter, usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import ProfileForm from "@/components/Profile/ProfileForm";
import ChangePasswordForm from "@/components/Profile/ChangePasswordForm";
import StoreForm from "@/components/Profile/StoreForm";
import ProfileAvatar from "@/components/Profile/ProfileAvatar";
import { useAuth } from "@/hooks/useAuth";
import PaymentStatus from "@/components/Payment/PaymentStatus";
import dayjs from "dayjs";
import "./profile.css";

export default function ProfilePage() {
  const { user, refreshUser } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState("1");
  const [refreshKey, setRefreshKey] = useState(0);

  // Log the complete user object for debugging
  console.log("ProfilePage - Complete user object:", JSON.stringify(user, null, 2));

  // Log specific fields we're having trouble with
  console.log("ProfilePage - Critical fields:", {
    phone: user?.phone,
    phoneType: user?.phone ? typeof user.phone : 'undefined/null',
    createdAt: user?.createdAt,
    createdAtType: user?.createdAt ? typeof user.createdAt : 'undefined/null'
  });

  // Log the current pathname to help with debugging
  useEffect(() => {
    console.log("ProfilePage - Current pathname:", pathname);
  }, [pathname]);

  // Function to refresh the profile data
  const handleProfileUpdate = async () => {
    console.log("ProfilePage - Refreshing user data");

    try {
      // Refresh the user data from the backend
      await refreshUser();

      // Log the user data after refresh
      console.log("ProfilePage - User data after refresh:", {
        phone: user?.phone,
        phoneType: user?.phone ? typeof user.phone : 'undefined/null',
        createdAt: user?.createdAt,
        createdAtType: user?.createdAt ? typeof user.createdAt : 'undefined/null'
      });

      // Force re-render of child components
      setRefreshKey(prevKey => prevKey + 1);

      console.log("ProfilePage - Refresh key updated:", refreshKey + 1);
    } catch (error) {
      console.error("Error updating profile:", error);
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-gray-500">Loading profile information...</div>
      </div>
    );
  }

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    return dayjs(dateString).format("MMM D, YYYY");
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">My Profile</h1>
        <p className="text-gray-600">Manage your account information and settings</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Profile Avatar Section */}
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <ProfileAvatar key={`avatar-${refreshKey}`} user={user} />
          </div>

          {/* Payment Status Section - Only show for non-superadmin users */}
          {user.role !== 'superadmin' && (
            <div className="bg-white rounded-lg shadow-md p-6 mt-6 border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <CreditCardOutlined className="mr-2" />
                <span>Subscription Details</span>
              </h3>

              <PaymentStatus />

              <p className="mb-4 text-gray-600">
                Your subscription allows you to access all features of the POS system.
              </p>

              {user.nextPaymentDue && (
                <p className="mb-4 text-gray-600">
                  Next payment due: <span className="text-gray-800">{formatDate(user.nextPaymentDue)}</span>
                </p>
              )}

              {user.role !== 'cashier' && <Button
                type="primary"
                block
                onClick={() => router.push("/payment")}
                className="bg-blue-600 hover:bg-blue-700 border-none text-white"
              >
                Manage Subscription
              </Button>}

              
            </div>
          )}
        </div>

        {/* Profile Forms Section */}
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              items={[
                {
                  key: "1",
                  label: "Profile Information",
                  children: <ProfileForm
                    key={`profile-form-${refreshKey}`}
                    user={user}
                    onProfileUpdated={handleProfileUpdate}
                  />,
                },
                {
                  key: "2",
                  label: "Change Password",
                  children: <ChangePasswordForm
                    key={`password-form-${refreshKey}`}
                    userId={user.id}
                  />,
                },
                // Only show Store Information tab for admin users
                ...(user.role === 'admin' ? [
                  {
                    key: "3",
                    label: (
                      <span>
                        <ShopOutlined className="mr-1" />
                        Store Information
                      </span>
                    ),
                    children: <StoreForm
                      key={`store-form-${refreshKey}`}
                      user={user}
                      onStoreUpdated={handleProfileUpdate}
                    />,
                  }
                ] : [])
              ]}
              className="profile-tabs"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
