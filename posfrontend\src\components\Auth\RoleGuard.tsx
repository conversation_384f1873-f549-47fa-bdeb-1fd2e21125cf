"use client";

import { useEffect, ReactNode } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter } from "next/navigation";
import { Alert } from "antd";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { UserRole } from "@/types/user";

interface RoleGuardProps {
  children: ReactNode;
  allowedRoles: UserRole[];
  fallbackPath?: string;
}

/**
 * RoleGuard component to protect routes based on user roles
 * Redirects to fallback path or shows access denied if user doesn't have required role
 */
const RoleGuard = ({
  children,
  allowedRoles,
  fallbackPath = "/dashboard"
}: RoleGuardProps) => {
  const { user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();

  // Debug log to see role status (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log("RoleGuard - Role Status:", {
      userRole: user?.role,
      allowedRoles,
      hasPermission: user ? allowedRoles.includes(user.role) : false
    });
  }

  useEffect(() => {
    // If user exists but doesn't have the required role
    if (user && !allowedRoles.includes(user.role)) {
      router.push(fallbackPath);
    }
  }, [user, allowedRoles, router, fallbackPath]);

  // Show loading spinner while checking role
  if (!user) {
    return <LoadingSpinner fullScreen />;
  }

  // If user doesn't have the required role, show access denied
  if (!allowedRoles.includes(user.role)) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Alert
          message="Access Denied"
          description={`You don't have permission to access this page. This area requires ${allowedRoles.join(" or ")} role.`}
          type="error"
          showIcon
        />
      </div>
    );
  }

  // User has the required role, render children
  return <>{children}</>;
};

export default RoleGuard;
