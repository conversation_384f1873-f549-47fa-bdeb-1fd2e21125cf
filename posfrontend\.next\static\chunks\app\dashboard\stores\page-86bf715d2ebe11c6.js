(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3488],{49915:(e,s,t)=>{Promise.resolve().then(t.bind(t,20141))},20141:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>et});var r=t(95155),a=t(12115),l=t(71349),i=t(43316),n=t(72093),c=t(96030),d=t(55750),o=t(16419),x=t(80766),m=t(92895),h=t(6457),u=t(17084),p=t(39279),g=t(64522),j=t(15424),y=t(80519),b=t(86260),f=t(27656),N=t(60102),A=t(91256),v=t(71518),w=t(33621),C=t(44549);let k=e=>{let{current:s,pageSize:t,total:a,onChange:l,isMobile:i=!1}=e,n=Math.ceil(a/t);return(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(s-1)*t+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(s*t,a)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:a})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>l(Math.max(1,s-1)),disabled:1===s,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===s?"text-gray-400 cursor-not-allowed":"text-gray-700"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(w.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,n)},(e,t)=>{let a=t+1;return(0,r.jsx)("button",{onClick:()=>l(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(s===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700"),children:a},a)}),(0,r.jsxs)("button",{onClick:()=>l(s+1),disabled:s>=n,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(s>=n?"text-gray-400 cursor-not-allowed":"text-gray-700"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(C.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>l(Math.max(1,s-1)),disabled:1===s,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===s?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white"),children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",s," of ",n]}),(0,r.jsx)("button",{onClick:()=>l(s+1),disabled:s>=n,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(s>=n?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white"),children:"Next"})]})]})};var S=t(12467),I=t(75912);let E=e=>{let[s,{isLoading:t}]=(0,v.Vf)();return{deleteStore:async t=>{try{let r=await s(t).unwrap();if(!r.success)throw Error(r.message||"Failed to delete store");return(0,I.r)("success","Store deleted successfully"),e&&e(),r.data}catch(e){throw console.error("Delete store error:",e),(0,I.r)("error",e.message||"Failed to delete store"),e}},isDeleting:t}};var D=t(21455),P=t.n(D);let z=e=>{var s;let{search:t,onEdit:l,onView:n,onSuccess:c,onSelect:o,onBulkDelete:w,isMobile:C=!1}=e,I=(0,A.E)(),D=C||I,[P,z]=(0,a.useState)(1),[F,L]=(0,a.useState)(10),[M,B]=(0,a.useState)(null),[T,O]=(0,a.useState)(!1),[U,W]=(0,a.useState)(null),[R,V]=(0,a.useState)([]),[_,Y]=(0,a.useState)(!1),{data:q,isLoading:H,isFetching:K,refetch:Q}=(0,v.tD)({page:P,limit:F,search:t}),{deleteStore:$,isDeleting:G}=E(()=>{O(!1),c()}),J=e=>{W(e),O(!0)},X=async()=>{U&&await $(U)},Z=e=>{let s=e.target.checked;Y(s),s?V(es.map(e=>e.id)):V([])},ee=(e,s)=>{s?V(s=>[...s,e]):V(s=>s.filter(s=>s!==e))},es=(null==q?void 0:null===(s=q.data)||void 0===s?void 0:s.stores)||[];return(0,r.jsxs)("div",{className:"overflow-hidden bg-white",children:[(0,r.jsx)(S.A,{isOpen:T,onClose:()=>{O(!1),W(null)},onConfirm:X,title:"Delete Store",message:"Are you sure you want to delete this store? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:G,type:"danger"}),R.length>0&&(0,r.jsxs)("div",{className:"p-2 bg-gray-100 border-b flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[R.length," ",1===R.length?"store":"stores"," selected"]}),(0,r.jsx)(i.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(u.A,{}),onClick:()=>{R.length>0&&w?(w(R),V([]),Y(!1)):x.Ay.warning({message:"No stores selected",description:"Please select at least one store to delete."})},className:"ml-2",children:"Delete Selected"})]}),D?(0,r.jsxs)(N.jB,{columns:"50px 200px 200px 150px 150px",minWidth:"800px",children:[(0,r.jsx)(N.A0,{className:"text-center",children:(0,r.jsx)(m.A,{checked:_,onChange:Z,disabled:0===es.length})}),(0,r.jsx)(N.A0,{sticky:D?void 0:"left",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)(N.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"}),"Location"]})}),(0,r.jsx)(N.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"mr-1"}),"Contact"]})}),(0,r.jsx)(N.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),"Created By"]})}),(0,r.jsx)(N.A0,{sticky:D?void 0:"right",className:"text-right",children:"Actions"}),es.map(e=>(0,r.jsxs)(N.Hj,{selected:R.includes(e.id),children:[(0,r.jsx)(N.nA,{className:"text-center",children:(0,r.jsx)(m.A,{checked:R.includes(e.id),onChange:s=>ee(e.id,s.target.checked)})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis font-medium",children:e.name})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis text-gray-600",children:[e.address,e.city,e.state,e.country].filter(Boolean).join(", ")||"N/A"})}),(0,r.jsx)(N.nA,{children:e.phone||e.email?(0,r.jsxs)("div",{className:"max-w-[130px] overflow-hidden text-ellipsis",children:[e.phone&&(0,r.jsx)("div",{className:"font-mono text-sm",children:e.phone}),e.email&&(0,r.jsx)("div",{className:"text-blue-600 text-sm",children:e.email})]}):"N/A"}),(0,r.jsx)(N.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(h.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(y.A,{}),onClick:s=>{s.stopPropagation(),n(e)},type:"text",className:"view-button text-green-500 hover:text-green-400",size:"small"})}),(0,r.jsx)(h.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(b.A,{}),onClick:s=>{s.stopPropagation(),l(e)},type:"text",className:"edit-button text-blue-500 hover:text-blue-400",size:"small"})}),(0,r.jsx)(h.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(f.A,{}),onClick:s=>{s.stopPropagation(),J(e.id)},type:"text",className:"delete-button text-red-500 hover:text-red-400",size:"small"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"w-10 px-3 py-3 text-center",children:(0,r.jsx)(m.A,{checked:_,onChange:Z,disabled:0===es.length})}),(0,r.jsx)("th",{scope:"col",className:"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-1"}),"Name"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"}),"Location"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"mr-1"}),"Contact"]})}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider",children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"}),"Created By"]})}),(0,r.jsx)("th",{scope:"col",className:"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:es.map(e=>(0,r.jsxs)("tr",{className:R.includes(e.id)?"bg-blue-50":"",children:[(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:(0,r.jsx)(m.A,{checked:R.includes(e.id),onChange:s=>ee(e.id,s.target.checked)})}),(0,r.jsx)("td",{className:"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[120px] overflow-hidden text-ellipsis",children:e.name})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:(0,r.jsx)("div",{className:"max-w-[200px] overflow-hidden text-ellipsis",children:[e.address,e.city,e.state,e.country].filter(Boolean).join(", ")||"N/A"})}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.phone||e.email?(0,r.jsxs)("div",{className:"max-w-[150px] overflow-hidden text-ellipsis",children:[e.phone&&(0,r.jsx)("div",{children:e.phone}),e.email&&(0,r.jsx)("div",{children:e.email})]}):"N/A"}),(0,r.jsx)("td",{className:"px-3 py-4 whitespace-nowrap text-gray-800",children:e.createdByName||"N/A"}),(0,r.jsx)("td",{className:"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(h.A,{title:"View",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(y.A,{}),onClick:s=>{s.stopPropagation(),n(e)},type:"text",className:"view-button text-green-500",size:"middle"})}),(0,r.jsx)(h.A,{title:"Edit",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(b.A,{}),onClick:s=>{s.stopPropagation(),l(e)},type:"text",className:"edit-button text-blue-500",size:"middle"})}),(0,r.jsx)(h.A,{title:"Delete",children:(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(f.A,{}),onClick:s=>{s.stopPropagation(),J(e.id)},type:"text",className:"delete-button text-red-500",size:"middle"})})]})})]},e.id))})]})}),(null==q?void 0:q.data)&&(0,r.jsx)(k,{current:P,pageSize:F,total:q.data.total,onChange:(e,s)=>{z(e),s&&L(s)},isMobile:D})]})};var F=t(41657),L=t(5413);t(66202);let M=e=>{let{value:s,onChange:t,isMobile:a=!1}=e;return(0,r.jsxs)("div",{className:"sticky top-0 z-10 mb-4 border-b border-gray-200 bg-white px-3 py-3",children:[(0,r.jsx)(F.A,{placeholder:"Search stores by name, location...",prefix:(0,r.jsx)(L.A,{className:"text-gray-500"}),value:s,onChange:e=>{let s=e.target.value;console.log("Store search input changed:",s),t(s)},className:"border-gray-300 bg-white text-gray-800 focus:border-blue-500",style:{width:a?"100%":"300px",height:"36px",backgroundColor:"white",color:"#333"},allowClear:{clearIcon:(0,r.jsx)("span",{className:"text-gray-500",children:"\xd7"})}}),s&&(0,r.jsxs)("div",{className:"ml-1 mt-1 text-xs text-gray-600",children:['Searching for: "',s,'"']})]})};var B=t(83414),T=t(7162),O=t(60046),U=t(80877),W=t(24988);t(71944);let R=e=>{let{isOpen:s,onClose:t,onSuccess:l,editStore:n,mode:c}=e,[d]=B.A.useForm(),[o,{isLoading:x}]=(0,v.ST)(),[m,{isLoading:h}]=(0,v.HP)(),{refetch:u}=(0,v.tD)({page:1,limit:1e3},{skip:!0});(0,a.useEffect)(()=>{s&&"edit"===c&&n?d.setFieldsValue({name:n.name,address:n.address,city:n.city,state:n.state,country:n.country,phone:n.phone,email:n.email,website:n.website,taxId:n.taxId}):s&&"create"===c&&d.resetFields()},[s,c,n,d]);let y=async()=>{try{let e=await d.validateFields();if("create"===c){let s={name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId},r=await o(s).unwrap();r.success?((0,I.r)("success","Store created successfully"),d.resetFields(),setTimeout(()=>{t(),l&&l(),u()},500)):(0,I.r)("error",r.message||"Failed to create store")}else if("edit"===c&&n){let s={name:e.name,address:e.address,city:e.city,state:e.state,country:e.country,phone:e.phone,email:e.email,website:e.website,taxId:e.taxId},r=await m({storeId:n.id,updateData:s}).unwrap();r.success?((0,I.r)("success","Store updated successfully"),t(),l&&l()):(0,I.r)("error",r.message||"Failed to update store")}}catch(s){var e;(0,I.r)("error",(null===(e=s.data)||void 0===e?void 0:e.message)||"An error occurred while saving the store")}},b=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Cancel"}),(0,r.jsx)(i.Ay,{type:"primary",onClick:()=>d.submit(),loading:x||h,icon:(0,r.jsx)(p.A,{}),className:"bg-blue-600 hover:bg-blue-700 border-blue-600",children:"create"===c?"Create Store":"Update Store"})]});return(0,r.jsx)(W.A,{title:"create"===c?"Create Store":"Edit Store",isOpen:s,onClose:t,width:"500px",footer:b,children:(0,r.jsx)("div",{className:"p-6 bg-white",children:(0,r.jsxs)(B.A,{form:d,layout:"vertical",className:"store-form",onFinish:y,requiredMark:!0,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Store Information"}),(0,r.jsx)(B.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-1"})," Store Name"]}),rules:[{required:!0,message:"Please enter the store name"}],tooltip:"Name of your store or business",children:(0,r.jsx)(F.A,{placeholder:"Enter store name"})}),(0,r.jsx)(B.A.Item,{name:"address",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"})," Address"]}),tooltip:"Physical address of your store",children:(0,r.jsx)(F.A.TextArea,{rows:2,placeholder:"Enter store address"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(B.A.Item,{name:"city",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"})," City"]}),children:(0,r.jsx)(F.A,{placeholder:"Enter city"})}),(0,r.jsx)(B.A.Item,{name:"state",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"})," State/Province"]}),children:(0,r.jsx)(F.A,{placeholder:"Enter state/province"})}),(0,r.jsx)(B.A.Item,{name:"country",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-1"})," Country"]}),children:(0,r.jsx)(F.A,{placeholder:"Enter country"})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2",children:"Contact Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(B.A.Item,{name:"phone",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"mr-1"})," Phone"]}),tooltip:"Contact phone number for the store",children:(0,r.jsx)(F.A,{placeholder:"Enter phone number"})}),(0,r.jsx)(B.A.Item,{name:"email",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(T.A,{className:"mr-1"})," Email"]}),tooltip:"Contact email for the store",rules:[{type:"email",message:"Please enter a valid email address"}],children:(0,r.jsx)(F.A,{placeholder:"Enter email address"})})]}),(0,r.jsx)(B.A.Item,{name:"website",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(O.A,{className:"mr-1"})," Website"]}),tooltip:"Store website URL (if available)",children:(0,r.jsx)(F.A,{placeholder:"Enter website URL"})}),(0,r.jsx)(B.A.Item,{name:"taxId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(U.A,{className:"mr-1"})," Tax ID / Business Registration"]}),tooltip:"Tax ID or business registration number",children:(0,r.jsx)(F.A,{placeholder:"Enter tax ID or registration number"})})]})]})})})};var V=t(67649),_=t(87181);let Y=e=>{let{isOpen:s,onClose:t,onEdit:a,storeId:l}=e,{data:c,isLoading:x}=(0,v.vs)(l||0,{skip:!l||!s}),m=null==c?void 0:c.data,h=e=>e?P()(e).format("MMM D, YYYY"):"N/A",u=m?(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"}),(0,r.jsx)(i.Ay,{type:"primary",icon:(0,r.jsx)(b.A,{}),onClick:()=>a(m),className:"border-blue-600 bg-blue-600 hover:bg-blue-700",children:"Edit"})]}):(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(i.Ay,{onClick:t,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"})});return(0,r.jsx)(W.A,{title:"Store Details",isOpen:s,onClose:t,width:"500px",footer:u,children:(0,r.jsx)("div",{className:"bg-white p-6s",children:x?(0,r.jsx)("div",{className:"flex h-full min-h-[300px] items-center justify-center",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):m?(0,r.jsxs)("div",{className:"mx-auto max-w-2xl",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,r.jsxs)("h2",{className:"flex items-center text-xl font-bold text-gray-800",children:[(0,r.jsx)(p.A,{className:"mr-2"}),"Store: ",m.name]}),(0,r.jsx)("p",{className:"mt-1 flex items-center text-gray-600",children:"Complete store information and details"})]}),(0,r.jsxs)(V.A,{bordered:!0,column:1,className:"store-detail-light",children:[(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(p.A,{})," Store ID"]}),children:m.id}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(p.A,{})," Name"]}),children:m.name}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(g.A,{})," Address"]}),children:m.address||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(g.A,{})," City"]}),children:m.city||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(g.A,{})," State/Province"]}),children:m.state||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(g.A,{})," Country"]}),children:m.country||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(j.A,{})," Phone"]}),children:m.phone||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(T.A,{})," Email"]}),children:m.email||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(O.A,{})," Website"]}),children:m.website||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(U.A,{})," Tax ID"]}),children:m.taxId||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(d.A,{})," Created By"]}),children:m.createdByName||"N/A"}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(_.A,{})," Created At"]}),children:h(m.createdAt)}),(0,r.jsx)(V.A.Item,{label:(0,r.jsxs)("span",{children:[(0,r.jsx)(_.A,{})," Last Updated"]}),children:h(m.updatedAt)})]})]}):(0,r.jsx)("div",{className:"text-center text-gray-800",children:(0,r.jsx)("p",{children:'Store not found or you don"t have permission to view it.'})})})})};var q=t(42426),H=t(21614),K=t(13250),Q=t(53096),$=t(1227),G=t(98195),J=t(63065),X=t(33429);let Z=e=>{var s,t;let{isOpen:l,onClose:c,store:x,onSuccess:m}=e,[h]=B.A.useForm(),[u,g]=(0,a.useState)(null),[j,y]=(0,a.useState)([]),{data:b,isLoading:f}=(0,J.VL)({page:1,limit:100}),{data:N,isLoading:A,refetch:v}=(0,X.lr)(u||0,{skip:!u}),[w,{isLoading:C}]=(0,X.tU)(),[k,{isLoading:S}]=(0,X.ih)(),[E,{isLoading:D}]=(0,X.PK)();(0,a.useEffect)(()=>{(null==N?void 0:N.data)?y(N.data):y([])},[N]);let P=async()=>{try{if(!x){(0,I.r)("error","No store selected");return}let e=await h.validateFields(),s=await w({userId:e.userId,storeId:x.id,isDefault:e.isDefault}).unwrap();s.success?((0,I.r)("success","User associated with store successfully"),v(),m&&m()):(0,I.r)("error",s.message||"Failed to associate user with store")}catch(s){var e;(0,I.r)("error",(null===(e=s.data)||void 0===e?void 0:e.message)||"An error occurred")}},z=async(e,s)=>{try{let t=await k({userId:e,storeId:s}).unwrap();t.success?((0,I.r)("success","Default store set successfully"),v(),m&&m()):(0,I.r)("error",t.message||"Failed to set default store")}catch(e){var t;(0,I.r)("error",(null===(t=e.data)||void 0===t?void 0:t.message)||"An error occurred")}},F=async(e,s)=>{try{if(window.confirm("Are you sure you want to remove this user from the store?")){let t=await E({userId:e,storeId:s}).unwrap();t.success?((0,I.r)("success","User removed from store successfully"),v(),m&&m()):(0,I.r)("error",t.message||"Failed to remove user from store")}}catch(e){var t;(0,I.r)("error",(null===(t=e.data)||void 0===t?void 0:t.message)||"An error occurred")}},L=[{title:"Store",dataIndex:"name",key:"name",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2"}),(0,r.jsx)("span",{children:e})]})},{title:"Default",dataIndex:"isDefault",key:"isDefault",render:(e,s)=>(0,r.jsx)(q.A,{checked:e,onChange:()=>z(u,s.id),loading:S,checkedChildren:(0,r.jsx)($.A,{}),unCheckedChildren:(0,r.jsx)(G.A,{})})},{title:"Actions",key:"actions",render:(e,s)=>(0,r.jsx)(i.Ay,{danger:!0,onClick:()=>F(u,s.id),loading:D,className:"border-red-600 bg-red-600 text-white hover:bg-red-700",children:"Remove"})}],M=(0,r.jsx)("div",{className:"flex justify-end space-x-2",children:(0,r.jsx)(i.Ay,{onClick:c,className:"text-gray-700 hover:text-gray-900",style:{background:"#f5f5f5",borderColor:"#d9d9d9"},children:"Close"})});return(0,r.jsx)(W.A,{title:"Manage Users for ".concat((null==x?void 0:x.name)||"Store"),isOpen:l,onClose:c,width:"500px",footer:M,children:(0,r.jsxs)("div",{className:"bg-white p-6 pt-20",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800",children:"Associate User with Store"}),(0,r.jsxs)(B.A,{form:h,layout:"vertical",onFinish:P,className:"store-form",children:[(0,r.jsx)(B.A.Item,{name:"userId",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-1"})," User"]}),rules:[{required:!0,message:"Please select a user"}],children:(0,r.jsx)(H.A,{placeholder:"Select a user",loading:f,onChange:e=>{g(e),h.setFieldsValue({userId:e})},children:null==b?void 0:null===(t=b.data)||void 0===t?void 0:null===(s=t.users)||void 0===s?void 0:s.map(e=>(0,r.jsx)(H.A.Option,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-2"}),e.name," (",e.role,")"]})},e.id))})}),(0,r.jsx)(B.A.Item,{name:"isDefault",valuePropName:"checked",initialValue:!1,children:(0,r.jsx)(q.A,{checkedChildren:"Set as default store",unCheckedChildren:"Not default"})}),(0,r.jsx)(i.Ay,{type:"primary",htmlType:"submit",loading:C,className:"border-blue-600 bg-blue-600 hover:bg-blue-700",disabled:!x||!u,children:"Associate User with Store"})]})]}),u&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"mb-2 border-b border-gray-200 pb-2 text-lg font-medium text-gray-800",children:'User"s Stores'}),A?(0,r.jsx)("div",{className:"flex h-40 items-center justify-center",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):j.length>0?(0,r.jsx)(K.A,{dataSource:j,columns:L,rowKey:"id",pagination:!1,className:"user-stores-table"}):(0,r.jsx)(Q.A,{description:"No stores associated with this user"})]})]})})},ee=e=>{let[s,{isLoading:t}]=(0,v.yv)();return{bulkDeleteStores:async t=>{try{console.log("Bulk deleting stores with IDs:",t);let r=await s(t).unwrap();if(!r.success)throw Error(r.message||"Failed to delete stores");return(0,I.r)("success","".concat(t.length," stores deleted successfully")),e&&e(),r.data}catch(e){throw console.error("Bulk delete stores error:",e),(0,I.r)("error",e.message||"Failed to delete stores"),e}},isDeleting:t}};var es=t(30555);let et=()=>{var e;let s=(0,es.a)(),[t,x]=(0,a.useState)(""),[m,h]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!1),[g,j]=(0,a.useState)(!1),[y,b]=(0,a.useState)(!1),[f,N]=(0,a.useState)(null),[A,w]=(0,a.useState)(!1),[C,k]=(0,a.useState)([]),{refetch:I,isLoading:E,data:D}=(0,v.tD)({page:1,limit:s?5:10,search:t}),{bulkDeleteStores:P,isDeleting:F}=ee(()=>{w(!1),I()});(0,a.useEffect)(()=>{I()},[s,I]);let L=e=>{N(e),p(!0)},B=e=>{N(e),b(!0)},T=()=>{I()},O=async()=>{if(console.log("confirmBulkDelete called with stores:",C),C.length>0)try{await P(C)}catch(e){console.error("Error in confirmBulkDelete:",e)}};return(0,r.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,r.jsx)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Store Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:s?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(i.Ay,{type:"primary",icon:(0,r.jsx)(c.A,{}),onClick:()=>h(!0),size:s?"small":"middle",className:"bg-blue-600",children:s?"":"Add Store"}),f&&(0,r.jsx)(i.Ay,{icon:(0,r.jsx)(d.A,{}),onClick:()=>B(f),className:"bg-green-600 text-white",size:s?"small":"middle",children:s?"":"Manage Users"})]}),children:(0,r.jsxs)("div",{className:"w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm",children:[(0,r.jsx)(M,{value:t,onChange:x,isMobile:s}),E?(0,r.jsx)("div",{className:"flex h-60 items-center justify-center bg-gray-50",children:(0,r.jsx)(n.A,{indicator:(0,r.jsx)(o.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):(0,r.jsx)(r.Fragment,{children:(null==D?void 0:null===(e=D.data)||void 0===e?void 0:e.stores)&&D.data.stores.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(z,{search:t,onEdit:L,onView:e=>{N(e),j(!0)},onSuccess:T,onSelect:N,onBulkDelete:e=>{console.log("handleBulkDelete called with storeIds:",e),k(e),w(!0)},isMobile:s})}):(0,r.jsx)("div",{className:"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800",children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"No stores found matching your search criteria."}),(0,r.jsx)(i.Ay,{type:"primary",onClick:()=>x(""),className:"mt-4 bg-blue-600",children:"Clear Search"})]}):(0,r.jsx)("p",{children:'No stores found. Click "Add Store" to create one.'})})})]})}),(0,r.jsx)(R,{isOpen:m,onClose:()=>h(!1),onSuccess:()=>{h(!1),I()},mode:"create"}),(0,r.jsx)(R,{isOpen:u,onClose:()=>p(!1),onSuccess:()=>{p(!1),I()},editStore:f,mode:"edit"}),(0,r.jsx)(Y,{isOpen:g,onClose:()=>j(!1),onEdit:L,storeId:null==f?void 0:f.id}),(0,r.jsx)(Z,{isOpen:y,onClose:()=>b(!1),store:f,onSuccess:T}),(0,r.jsx)(S.A,{isOpen:A,onClose:()=>{w(!1),k([])},onConfirm:O,title:"Delete Multiple Stores",message:"Are you sure you want to delete ".concat(C.length," stores? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:F,type:"danger"})]})}},12467:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(95155);t(12115);var a=t(46102),l=t(43316),i=t(75218);let n=e=>{let{isOpen:s,onClose:t,onConfirm:n,title:c,message:d,confirmText:o="Confirm",cancelText:x="Cancel",isLoading:m=!1,type:h="danger"}=e;return(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.A,{style:{color:"danger"===h?"#ff4d4f":"warning"===h?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:c})]}),open:s,onCancel:t,footer:[(0,r.jsx)(l.Ay,{onClick:t,disabled:m,children:x},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===h?"primary":"default",danger:"danger"===h,onClick:n,loading:m,children:o},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:d})})}},60102:(e,s,t)=>{"use strict";t.d(s,{A0:()=>i,Hj:()=>c,jB:()=>l,nA:()=>n});var r=t(95155);t(12115);var a=t(21567);let l=e=>{let{children:s,columns:t,className:l,minWidth:i="800px"}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0",n?"grid":"block"),style:n?{gridTemplateColumns:t,minWidth:i,width:"max-content"}:{},children:s})})},i=e=>{let{children:s,className:t,sticky:l}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:i?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:i?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],t),children:s})},n=e=>{let{children:s,className:t,sticky:l}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:i?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:i?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],t),children:s})},c=e=>{let{children:s,className:t,selected:l=!1,onClick:i}=e;return(0,r.jsx)("div",{className:(0,a.cn)("contents",l&&"bg-blue-50",i&&"cursor-pointer hover:bg-gray-50",t),onClick:i,children:s})}},24988:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(95155),a=t(12115),l=t(43316),i=t(79624);let n=e=>{let{isOpen:s,onClose:t,title:n,children:c,width:d="400px",footer:o,fullWidth:x=!1}=e,[m,h]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!1),[g,j]=(0,a.useState)(window.innerWidth);if((0,a.useEffect)(()=>{let e=()=>{j(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",s,"title:",n),s)p(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{h(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{h(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{p(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[s,n]),!u)return null;let y="Point of Sale"===n||x||"100vw"===d;return(0,r.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(y?"sales-panel-container":""),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:t}),(0,r.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===n||x||"100vw"===d||g<640?"100vw":g<1024?"500px":"string"==typeof d&&d.includes("px")&&parseInt(d)>600?"600px":d},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:n}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(i.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:c}),o&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:o})]})]})}},30555:(e,s,t)=>{"use strict";t.d(s,{a:()=>a});var r=t(12115);function a(){let[e,s]=(0,r.useState)();return(0,r.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),t=()=>{s(window.innerWidth<850)};return s(window.innerWidth<850),e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[]),!!e}},91256:(e,s,t)=>{"use strict";t.d(s,{E:()=>a});var r=t(12115);let a=()=>{let[e,s]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{s(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,s,t)=>{"use strict";t.d(s,{cn:()=>l});var r=t(43463),a=t(69795);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}},75912:(e,s,t)=>{"use strict";t.d(s,{r:()=>a});var r=t(55037);let a=(e,s)=>{"success"===e?r.oR.success(s):"error"===e?r.oR.error(s):"warning"===e&&(0,r.oR)(s,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)},71944:()=>{},66202:()=>{}},e=>{var s=s=>e(e.s=s);e.O(0,[8059,4265,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,5211,2877,821,8441,1517,7358],()=>s(49915)),_N_E=e.O()}]);