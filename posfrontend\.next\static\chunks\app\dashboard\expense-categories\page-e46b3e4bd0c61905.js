(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[137],{65110:(e,t,s)=>{Promise.resolve().then(s.bind(s,77313))},77313:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var r=s(95155),a=s(12115),l=s(71349),n=s(43316),i=s(41657),o=s(72093),d=s(72278),c=s(96030),x=s(5413),m=s(16419),u=s(89895),h=s(36060),g=s(30555),y=s(82461),p=s(92895),f=s(45100),j=s(6457),v=s(17084),b=s(16810),w=s(86260),A=s(27656),N=s(60102),C=s(91256),k=s(83391);let D=e=>{let{categories:t,loading:s=!1,onEdit:a,onDelete:l,onBulkDelete:i,selectedCategories:o=[],onSelectionChange:d,isMobile:c=!1}=e,x=(0,C.E)(),m=c||x,h=(0,k.d4)(e=>e.auth.user),g=null==h?void 0:h.role,y="admin"===g||"superadmin"===g,D="admin"===g||"superadmin"===g,S=(e,t)=>{d&&d(t?[...o,e]:o.filter(t=>t!==e))},P=e=>{d&&(e.target.checked?d(t.filter(e=>!e.isDefault).map(e=>e.id)):d([]))},E=t.filter(e=>!e.isDefault),F=E.length>0&&o.length===E.length,R=o.length>0&&o.length<E.length;return(0,r.jsxs)("div",{children:[o.length>0&&D&&i&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-red-700",children:[o.length," category(s) selected"]}),(0,r.jsx)(n.Ay,{type:"primary",danger:!0,icon:(0,r.jsx)(v.A,{}),onClick:()=>i(o),size:m?"small":"middle",children:"Delete Selected"})]})}),m?(0,r.jsxs)(N.jB,{columns:d?"50px 200px 120px 100px 150px":"200px 120px 100px 150px",minWidth:d?"700px":"650px",children:[d&&(0,r.jsx)(N.A0,{className:"text-center",children:(0,r.jsx)(p.A,{indeterminate:R,checked:F,onChange:P})}),(0,r.jsx)(N.A0,{children:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-1"}),"Category"]})}),(0,r.jsx)(N.A0,{children:"Color"}),(0,r.jsx)(N.A0,{children:"Type"}),(0,r.jsx)(N.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,r.jsxs)(N.Hj,{selected:o.includes(e.id),children:[d&&(0,r.jsx)(N.nA,{className:"text-center",children:!e.isDefault&&(0,r.jsx)(p.A,{checked:o.includes(e.id),onChange:t=>S(e.id,t.target.checked)})}),(0,r.jsx)(N.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full mr-2",style:{backgroundColor:e.color}}),(0,r.jsxs)("div",{className:"max-w-[150px] overflow-hidden text-ellipsis font-medium",children:[e.name,e.isDefault&&(0,r.jsx)(b.A,{className:"ml-1 text-yellow-500",title:"System Default"})]})]})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(f.A,{color:e.color,className:"text-xs",children:e.color})}),(0,r.jsx)(N.nA,{children:e.isDefault?(0,r.jsx)(f.A,{color:"gold",icon:(0,r.jsx)(b.A,{}),className:"text-xs",children:"Default"}):(0,r.jsx)(f.A,{color:"blue",className:"text-xs",children:"Custom"})}),(0,r.jsx)(N.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[y&&a&&!e.isDefault&&(0,r.jsx)(j.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{type:"text",size:"small",icon:(0,r.jsx)(w.A,{}),onClick:()=>a(e),className:"text-blue-500 hover:text-blue-400"})}),D&&l&&!e.isDefault&&(0,r.jsx)(j.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{type:"text",size:"small",danger:!0,icon:(0,r.jsx)(A.A,{}),onClick:()=>l(e.id),className:"text-red-500 hover:text-red-400"})})]})})]},e.id))]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[d&&(0,r.jsx)("th",{className:"px-4 py-3 text-left",children:(0,r.jsx)(p.A,{indeterminate:R,checked:F,onChange:P})}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Color"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[d&&(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:!e.isDefault&&(0,r.jsx)(p.A,{checked:o.includes(e.id),onChange:t=>S(e.id,t.target.checked)})}),(0,r.jsx)("td",{className:"px-4 py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900 flex items-center",children:[e.name,e.isDefault&&(0,r.jsx)(b.A,{className:"ml-2 text-yellow-500",title:"System Default"})]})})]})}),(0,r.jsx)("td",{className:"px-4 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",children:e.description||"-"})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,r.jsx)(f.A,{color:e.color,className:"text-xs",children:e.color})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:e.isDefault?(0,r.jsx)(f.A,{color:"gold",icon:(0,r.jsx)(b.A,{}),children:"System Default"}):(0,r.jsx)(f.A,{color:"blue",children:"Custom"})}),(0,r.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[y&&a&&!e.isDefault&&(0,r.jsx)(j.A,{title:"Edit",children:(0,r.jsx)(n.Ay,{type:"text",size:"small",icon:(0,r.jsx)(w.A,{}),onClick:()=>a(e),className:"text-blue-500"})}),D&&l&&!e.isDefault&&(0,r.jsx)(j.A,{title:"Delete",children:(0,r.jsx)(n.Ay,{type:"text",size:"small",danger:!0,icon:(0,r.jsx)(A.A,{}),onClick:()=>l(e.id),className:"text-red-500"})})]})})]},e.id))})]})})]})};var S=s(36197),P=s(83414),E=s(26936),F=s(24988),R=s(51814),L=s(50147),T=s(75912);let{TextArea:z}=i.A,_=e=>{let{isOpen:t,onClose:s,onSuccess:l,category:o}=e,[d]=P.A.useForm(),c=!!o,[x,{isLoading:h}]=(0,y.DY)(),[g,{isLoading:p}]=(0,y.CQ)(),f=h||p,j=(0,a.useMemo)(()=>["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#6366F1","#14B8A6","#F97316","#6B7280"],[]);(0,a.useEffect)(()=>{t&&(c&&o?d.setFieldsValue({name:o.name,description:o.description,color:o.color,isDefault:o.isDefault}):(d.resetFields(),d.setFieldsValue({color:j[Math.floor(Math.random()*j.length)],isDefault:!1})))},[t,c,o,d,j]);let v=async e=>{var t,r,a;try{let a;let n={name:e.name,description:e.description,color:"string"==typeof e.color?e.color:(null===(r=e.color)||void 0===r?void 0:null===(t=r.toHexString)||void 0===t?void 0:t.call(r))||"#6B7280",isDefault:e.isDefault||!1};(a=c&&o?await g({categoryId:o.id,categoryData:n}).unwrap():await x(n).unwrap()).success?(T.r.success(c?"Category updated successfully!":"Category created successfully!"),d.resetFields(),null==l||l(),s()):T.r.error(a.message||"Failed to save category")}catch(e){console.error("Error saving category:",e),T.r.error((null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)||(null==e?void 0:e.message)||"Failed to ".concat(c?"update":"create"," category"))}},b=()=>{d.resetFields(),s()},w=c?"Edit Expense Category":"Add New Expense Category",A=(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.Ay,{onClick:b,disabled:f,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,r.jsxs)(n.Ay,{type:"primary",onClick:()=>d.submit(),loading:f,icon:f?(0,r.jsx)(m.A,{}):(0,r.jsx)(R.A,{}),children:[c?"Update":"Create"," Category"]})]});return(0,r.jsxs)(F.A,{isOpen:t,onClose:b,title:w,width:"500px",footer:A,children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4 pt-10",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Category: ",null==o?void 0:o.name]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2"}),"Add New Expense Category"]})}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:c?"Update category information":"Create a new expense category for better organization"})]}),(0,r.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,r.jsxs)(P.A,{form:d,layout:"vertical",onFinish:v,className:"expense-category-form",requiredMark:!0,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Category Information"}),(0,r.jsx)(P.A.Item,{name:"name",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-1"})," Category Name ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter category name"},{max:100,message:"Category name must be at most 100 characters"}],tooltip:"A descriptive name for this expense category",children:(0,r.jsx)(i.A,{placeholder:"e.g., Office Supplies, Utilities, Marketing",maxLength:100})}),(0,r.jsx)(P.A.Item,{name:"description",label:(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(L.A,{className:"mr-1"})," Description"]}),tooltip:"Optional description to help identify this category",children:(0,r.jsx)(z,{placeholder:"Brief description of what expenses belong to this category...",rows:3,maxLength:500})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Appearance"}),(0,r.jsx)(P.A.Item,{name:"color",label:"Category Color",tooltip:"Choose a color to help identify this category visually",children:(0,r.jsx)(E.A,{showText:!0,size:"large",presets:[{label:"Recommended",colors:j}]})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Quick Colors:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:j.map((e,t)=>(0,r.jsx)("button",{type:"button",className:"w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-500 transition-colors",style:{backgroundColor:e},onClick:()=>d.setFieldValue("color",e),title:"Set color to ".concat(e)},t))})]})]}),c&&(null==o?void 0:o.isDefault)&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("div",{className:"text-yellow-600 font-medium",children:"System Default Category"})}),(0,r.jsx)("div",{className:"text-yellow-700 text-sm mt-1",children:"This is a system default category. Some properties cannot be modified."})]})})]})]})};var B=s(12467);let M=()=>{var e,t;let{user:s}=(0,h.A)(),p=(0,g.a)(),f=null==s?void 0:s.role,[j,v]=(0,a.useState)(1),[b]=(0,a.useState)(20),[w,A]=(0,a.useState)(""),[N,C]=(0,a.useState)([]),[k,P]=(0,a.useState)(!1),[E,F]=(0,a.useState)(null),[R,L]=(0,a.useState)(!1),[z,M]=(0,a.useState)(!1),[I,O]=(0,a.useState)(null),[U,W]=(0,a.useState)([]),H="admin"===f||"superadmin"===f,V="admin"===f||"superadmin"===f,Y={page:j,limit:b,search:w.trim()},{data:q,isLoading:Q,error:G,refetch:$}=(0,y.L)(Y),[J,{isLoading:K}]=(0,y.Uc)(),X=(null==q?void 0:null===(e=q.data)||void 0===e?void 0:e.categories)||[],Z=(null==q?void 0:null===(t=q.data)||void 0===t?void 0:t.total)||0,ee=e=>{A(e),v(1)},et=async()=>{if(I)try{let e=await J(I).unwrap();e.success?(T.r.success("Category deleted successfully!"),C(e=>e.filter(e=>e!==I)),L(!1),O(null)):T.r.error(e.message||"Failed to delete category")}catch(t){var e;console.error("Error deleting category:",t),T.r.error((null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Failed to delete category")}},es=async()=>{try{let e=U.map(e=>J(e).unwrap());await Promise.all(e),T.r.success("".concat(U.length," category(s) deleted successfully!")),C([]),M(!1),W([])}catch(e){console.error("Error deleting categories:",e),T.r.error("Failed to delete some categories")}};return G?(0,r.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,r.jsx)(l.A,{className:"w-full",children:(0,r.jsxs)("div",{className:"text-center text-red-500 p-8",children:[(0,r.jsx)("p",{children:"Error loading expense categories. Please try again."}),(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(d.A,{}),onClick:()=>$(),className:"mt-4",children:"Retry"})]})})}):(0,r.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,r.jsxs)(l.A,{title:(0,r.jsx)("span",{className:"text-gray-800",children:"Expense Categories"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:p?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:H&&(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(c.A,{}),onClick:()=>{if(!H){T.r.error("You don't have permission to add categories");return}F(null),P(!0)},size:p?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:p?"":"Add Category"}),children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(i.A,{placeholder:"Search categories...",prefix:(0,r.jsx)(x.A,{}),value:w,onChange:e=>ee(e.target.value),className:"max-w-md",allowClear:!0})}),(0,r.jsx)("div",{className:"min-h-96",children:Q?(0,r.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,r.jsx)(o.A,{indicator:(0,r.jsx)(m.A,{style:{fontSize:48},spin:!0})})}):(0,r.jsxs)(r.Fragment,{children:[X.length>0?(0,r.jsx)(D,{categories:X,loading:Q,onEdit:e=>{if(e.isDefault){T.r.warning("System default categories cannot be edited");return}F(e),P(!0)},onDelete:e=>{if(!V){T.r.error("You don't have permission to delete categories");return}let t=X.find(t=>t.id===e);if(null==t?void 0:t.isDefault){T.r.warning("System default categories cannot be deleted");return}O(e),L(!0)},onBulkDelete:V?e=>{if(!V){T.r.error("You don't have permission to delete categories");return}let t=e.filter(e=>{let t=X.find(t=>t.id===e);return t&&!t.isDefault});if(0===t.length){T.r.warning("No deletable categories selected");return}W(t),M(!0)}:void 0,selectedCategories:N,onSelectionChange:C,isMobile:p}):(0,r.jsxs)("div",{className:"text-center text-gray-500 py-12",children:[(0,r.jsx)(u.A,{className:"text-4xl mb-4"}),w?(0,r.jsx)("p",{children:"No categories found matching your search."}):(0,r.jsxs)("p",{children:["No expense categories found. ",H&&"Click 'Add Category' to create one."]})]}),X.length>0&&(0,r.jsx)(S.A,{current:j,pageSize:b,total:Z,onChange:e=>{v(e)},isMobile:p})]})})]}),(0,r.jsx)(_,{isOpen:k,onClose:()=>{P(!1),F(null)},onSuccess:()=>{$(),C([])},category:E}),(0,r.jsx)(B.A,{isOpen:R,onClose:()=>{L(!1),O(null)},onConfirm:et,title:"Delete Category",message:"Are you sure you want to delete this category? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:K,type:"danger"}),(0,r.jsx)(B.A,{isOpen:z,onClose:()=>{M(!1),W([])},onConfirm:es,title:"Delete Multiple Categories",message:"Are you sure you want to delete ".concat(U.length," categories? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:K,type:"danger"})]})}},36197:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(95155);s(12115);var a=s(33621),l=s(44549);let n=e=>{let{current:t,pageSize:s,total:n,onChange:i,isMobile:o=!1}=e,d=Math.ceil(n/s);return 0===n?null:(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*s+1})," to"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*s,n)})," of"," ",(0,r.jsx)("span",{className:"font-medium text-gray-900",children:n})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>i(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)(a.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,d)},(e,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>i(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:a},a)}),(0,r.jsxs)("button",{onClick:()=>i(t+1),disabled:t>=d,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=d?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)(l.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>i(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",d]}),(0,r.jsx)("button",{onClick:()=>i(t+1),disabled:t>=d,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=d?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})}},12467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155);s(12115);var a=s(46102),l=s(43316),n=s(75218);let i=e=>{let{isOpen:t,onClose:s,onConfirm:i,title:o,message:d,confirmText:c="Confirm",cancelText:x="Cancel",isLoading:m=!1,type:u="danger"}=e;return(0,r.jsx)(a.A,{title:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{style:{color:"danger"===u?"#ff4d4f":"warning"===u?"#faad14":"#1890ff",marginRight:8}}),(0,r.jsx)("span",{children:o})]}),open:t,onCancel:s,footer:[(0,r.jsx)(l.Ay,{onClick:s,disabled:m,children:x},"cancel"),(0,r.jsx)(l.Ay,{type:"danger"===u?"primary":"default",danger:"danger"===u,onClick:i,loading:m,children:c},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,r.jsx)("p",{className:"my-4",children:d})})}},60102:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,Hj:()=>o,jB:()=>l,nA:()=>i});var r=s(95155);s(12115);var a=s(21567);let l=e=>{let{children:t,columns:s,className:l,minWidth:n="800px"}=e,i=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",l),children:(0,r.jsx)("div",{className:(0,a.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:s,minWidth:n,width:"max-content"}:{},children:t})})},n=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",l&&({left:n?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:n?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[l],s),children:t})},i=e=>{let{children:t,className:s,sticky:l}=e,n=window.innerWidth<768;return(0,r.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",l&&({left:n?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:n?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[l],s),children:t})},o=e=>{let{children:t,className:s,selected:l=!1,onClick:n}=e;return(0,r.jsx)("div",{className:(0,a.cn)("contents",l&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",s),onClick:n,children:t})}},24988:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155),a=s(12115),l=s(43316),n=s(79624);let i=e=>{let{isOpen:t,onClose:s,title:i,children:o,width:d="400px",footer:c,fullWidth:x=!1}=e,[m,u]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[y,p]=(0,a.useState)(window.innerWidth);if((0,a.useEffect)(()=>{let e=()=>{p(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)g(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{u(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{u(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{g(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!h)return null;let f="Point of Sale"===i||x||"100vw"===d;return(0,r.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(f?"sales-panel-container":""),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:s}),(0,r.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||x||"100vw"===d||y<640?"100vw":y<1024?"500px":"string"==typeof d&&d.includes("px")&&parseInt(d)>600?"600px":d},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,r.jsx)(l.Ay,{type:"text",icon:(0,r.jsx)(n.A,{style:{color:"#333"}}),onClick:s,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:o}),c&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},30555:(e,t,s)=>{"use strict";s.d(t,{a:()=>a});var r=s(12115);function a(){let[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},36060:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(83391),a=s(70854),l=s(63065),n=s(7875);let i=()=>{let e=(0,r.wA)(),{user:t,accessToken:s}=(0,r.d4)(e=>e.auth),i=(0,a._)(),{refetch:o}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!s,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let r=await o();console.log("useAuth - Refetch result:",r);let a=r.data;if((null==a?void 0:a.success)&&(null==a?void 0:a.data)){console.log("useAuth - API response data:",a.data);let r=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=a.data.phone||t.phone||"",i=a.data.createdAt||t.createdAt||"",o=a.data.lastPaymentDate||t.lastPaymentDate||void 0,d=a.data.nextPaymentDue||t.nextPaymentDue||null,c=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let x={...a.data,phone:l,createdAt:i,lastPaymentDate:o,nextPaymentDue:d,createdBy:c,paymentStatus:r};console.log("useAuth - Updating Redux store with:",x),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:x,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==a?void 0:a.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:c}}},70854:(e,t,s)=>{"use strict";s.d(t,{_:()=>i});var r=s(12115),a=s(83391),l=s(21455),n=s.n(l);let i=()=>{let e=(0,a.d4)(e=>e.auth.user),[t,s]=(0,r.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,r.useEffect)(()=>{if(!e){s({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,r=!1,a=!0,l="inactive";if("superadmin"===e.role){s({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){r=!0,a=!1,l="active";let s=!e.lastPaymentDate;if(e.nextPaymentDue){let l=n()(e.nextPaymentDue),i=n()();if(t=l.diff(i,"day"),s){let s=n()().diff(n()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:s,daysRemaining:t,trialDaysUsed:s,trialDaysRemaining:t,isActive:r,needsPayment:a})}}}else"pending"===e.paymentStatus?(r=!1,a=!0,l="pending"):"overdue"===e.paymentStatus?(r=!1,a=!0,l="overdue"):(r=!1,a=!0,l="inactive");s({isActive:r,daysRemaining:t,status:l,needsPayment:a})},[e]),t}},91256:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(12115);let a=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(43463),a=s(69795);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},75912:(e,t,s)=>{"use strict";s.d(t,{r:()=>a});var r=s(55037);let a=(e,t)=>{"success"===e?r.oR.success(t):"error"===e?r.oR.error(t):"warning"===e&&(0,r.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};a.success=e=>a("success",e),a.error=e=>a("error",e),a.warning=e=>a("warning",e)}},e=>{var t=t=>e(e.s=t);e.O(0,[6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,3414,6102,2910,1614,1637,2254,821,8441,1517,7358],()=>t(65110)),_N_E=e.O()}]);