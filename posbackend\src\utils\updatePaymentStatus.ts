import { and, eq, lt, or, desc, sql } from "drizzle-orm";
import { postgresDb } from "../db/db";
import { users, payments } from "../db/schema";
import dayjs from "dayjs";
import { notifyUserPaymentStatus } from '../../index';

export const updatePaymentStatus = async (
  userId: number,
  newStatus: "paid" | "pending" | "overdue" | "inactive" = "paid",
  subscriptionPeriod: number = 1 // Number of months
): Promise<void> => {
    const now = new Date();
    const nextPaymentDue = newStatus === "paid" ? dayjs(now).add(subscriptionPeriod, "month").toDate() : null; // Add subscription period months

    // Prepare the update data based on the new status
    const updateData = newStatus === "paid"
      ? { paymentStatus: newStatus, lastPaymentDate: now, nextPaymentDue }
      : { paymentStatus: newStatus };

    console.log(`🔄 Starting payment status update for user ID ${userId} to status: ${newStatus}`);

    // Retry logic for database operations
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000; // 1 second

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        console.log(`📡 Attempt ${attempt}/${MAX_RETRIES} - Fetching user role for ID ${userId}`);

        // Get user role first to determine if we need to update cashiers
        const userResult = await postgresDb
          .select({ role: users.role })
          .from(users)
          .where(eq(users.id, userId))
          .limit(1);

        if (userResult.length === 0) {
          throw new Error(`User with ID ${userId} not found.`);
        }

        const userRole = userResult[0].role;
        console.log(`👤 User ID ${userId} has role: ${userRole}`);

        console.log(`💾 Starting transaction to update payment status for user ID ${userId}`);

        await postgresDb.transaction(async (tx) => {
          // First update the user
          console.log(`🔄 Updating payment status for user ID ${userId}`);
          const updatedUser = await tx
            .update(users)
            .set(updateData)
            .where(eq(users.id, userId))
            .returning({ id: users.id, paymentStatus: users.paymentStatus });

          if (updatedUser.length === 0) {
            throw new Error(`Failed to update user ID ${userId}`);
          }

          console.log(`✅ User ID ${userId} payment status updated to: ${updatedUser[0].paymentStatus}`);

          // Notify the user via WebSocket
          notifyUserPaymentStatus(userId, newStatus);

          // If user is an admin, also update all cashiers created by this admin
          if (userRole === "admin") {
            console.log(`👥 Updating cashiers created by admin ID ${userId}`);
            const updatedCashiers = await tx
              .update(users)
              .set(updateData)
              .where(
                and(
                  eq(users.createdBy, userId),
                  eq(users.role, "cashier")
                )
              )
              .returning({ id: users.id, paymentStatus: users.paymentStatus });

            console.log(`✅ Payment status updated to ${newStatus} for admin ID ${userId} and ${updatedCashiers.length} cashiers.`);

            // Log each cashier update for debugging
            updatedCashiers.forEach(cashier => {
              console.log(`   📝 Cashier ID ${cashier.id} status: ${cashier.paymentStatus}`);
              // Notify each cashier via WebSocket
              notifyUserPaymentStatus(cashier.id, newStatus);
            });
          } else {
            console.log(`✅ Payment status updated to ${newStatus} for user ID ${userId}.`);
          }
        });

        // If we reach here, the operation was successful
        console.log(`🎉 Payment status update completed successfully for user ID ${userId} on attempt ${attempt}`);
        return;

      } catch (error: any) {
        console.error(`❌ Attempt ${attempt}/${MAX_RETRIES} failed for user ID ${userId}:`, error.message);

        // Check if it's a database connection error
        const isConnectionError = error.message.includes('ETIMEDOUT') ||
                                 error.message.includes('ECONNREFUSED') ||
                                 error.message.includes('ENOTFOUND') ||
                                 error.message.includes('connection') ||
                                 error.name === 'AggregateError';

        if (isConnectionError && attempt < MAX_RETRIES) {
          console.log(`🔄 Database connection error detected. Retrying in ${RETRY_DELAY}ms...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          continue;
        }

        // If it's the last attempt or not a connection error, throw the error
        if (attempt === MAX_RETRIES) {
          console.error(`💥 All ${MAX_RETRIES} attempts failed for user ID ${userId}. Final error:`, error);
          throw new Error(`Failed to update payment status for user ID ${userId} after ${MAX_RETRIES} attempts: ${error.message}`);
        }

        // For non-connection errors, throw immediately
        if (!isConnectionError) {
          console.error(`💥 Non-connection error for user ID ${userId}:`, error);
          throw error;
        }
      }
    }
  };

  /**
   * Verify that a user's payment status was properly updated
   * @param userId The user ID to check
   * @param expectedStatus The expected payment status
   * @returns Promise<boolean> indicating if the status matches
   */
  export const verifyPaymentStatusUpdate = async (userId: number, expectedStatus: "paid" | "pending" | "overdue" | "inactive"): Promise<boolean> => {
    try {
      console.log(`🔍 Verifying payment status for user ID ${userId}, expected: ${expectedStatus}`);

      const userResult = await postgresDb
        .select({
          paymentStatus: users.paymentStatus,
          lastPaymentDate: users.lastPaymentDate,
          nextPaymentDue: users.nextPaymentDue,
          role: users.role
        })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (userResult.length === 0) {
        console.error(`❌ User ID ${userId} not found during verification`);
        return false;
      }

      const user = userResult[0];
      const statusMatches = user.paymentStatus === expectedStatus;

      console.log(`📊 User ID ${userId} verification result:`, {
        currentStatus: user.paymentStatus,
        expectedStatus,
        statusMatches,
        lastPaymentDate: user.lastPaymentDate,
        nextPaymentDue: user.nextPaymentDue,
        role: user.role
      });

      // If user is an admin, also check their cashiers
      if (user.role === "admin") {
        const cashierResults = await postgresDb
          .select({
            id: users.id,
            paymentStatus: users.paymentStatus
          })
          .from(users)
          .where(
            and(
              eq(users.createdBy, userId),
              eq(users.role, "cashier")
            )
          );

        console.log(`👥 Admin ${userId} has ${cashierResults.length} cashiers`);

        const allCashiersUpdated = cashierResults.every(cashier => {
          const cashierStatusMatches = cashier.paymentStatus === expectedStatus;
          console.log(`   📝 Cashier ID ${cashier.id}: ${cashier.paymentStatus} (expected: ${expectedStatus}) - ${cashierStatusMatches ? '✅' : '❌'}`);
          return cashierStatusMatches;
        });

        if (!allCashiersUpdated) {
          console.error(`❌ Not all cashiers for admin ${userId} have the correct payment status`);
          return false;
        }
      }

      return statusMatches;
    } catch (error: any) {
      console.error(`❌ Error verifying payment status for user ID ${userId}:`, error);
      return false;
    }
  };

export const checkSubscriptionRenewals = async (): Promise<void> => {
  try {
    console.info("🔍 Checking subscription renewals and calculating next payment dates...");

    const now = new Date();
    const threeDaysFromNow = dayjs(now).add(3, 'day').toDate();

    await postgresDb.transaction(async (tx) => {
      // Find users whose subscriptions are expiring soon or have expired
      const usersNeedingRenewal = await tx
        .select({
          id: users.id,
          email: users.email,
          paymentStatus: users.paymentStatus,
          lastPaymentDate: users.lastPaymentDate,
          nextPaymentDue: users.nextPaymentDue,
          role: users.role,
          createdAt: users.createdAt
        })
        .from(users)
        .where(
          and(
            lt(users.nextPaymentDue, threeDaysFromNow), // Users whose subscription expires within 3 days
            eq(users.paymentStatus, "paid"), // Only check currently active users
            or(eq(users.role, "admin"), eq(users.role, "superadmin")) // Only admins and superadmins pay
          )
        );

      console.info(`📊 Found ${usersNeedingRenewal.length} users needing subscription renewal check`);

      for (const user of usersNeedingRenewal) {
        try {
          // Get the user's latest successful payment to determine subscription period
          const latestPayment = await tx
            .select({
              subscriptionPeriod: payments.subscriptionPeriod,
              amount: payments.amount,
              paidAt: payments.paidAt
            })
            .from(payments)
            .where(
              and(
                eq(payments.userId, user.id),
                eq(payments.status, "successful")
              )
            )
            .orderBy(desc(payments.paidAt))
            .limit(1);

          if (latestPayment.length === 0) {
            console.warn(`⚠️ No payment history found for user ${user.email} (ID: ${user.id})`);
            continue;
          }

          const subscriptionPeriod = latestPayment[0].subscriptionPeriod || 1;
          const paymentAmount = latestPayment[0].amount;

          // Check if user is in free trial period (no payments made yet)
          const isInFreeTrial = latestPayment.length === 0;
          const daysSinceCreation = dayjs(now).diff(dayjs(user.createdAt), 'day');
          const daysUntilExpiry = dayjs(user.nextPaymentDue).diff(now, 'day');

          console.log(`📋 User ${user.email} (ID: ${user.id}):`);
          console.log(`   🆕 Account created: ${dayjs(user.createdAt).format('YYYY-MM-DD')} (${daysSinceCreation} days ago)`);
          console.log(`   🎁 Free trial: ${isInFreeTrial ? 'YES' : 'NO'}`);

          if (!isInFreeTrial) {
            console.log(`   💰 Last payment: ₵${paymentAmount} for ${subscriptionPeriod} month(s)`);
          }

          console.log(`   📅 Current next due: ${dayjs(user.nextPaymentDue).format('YYYY-MM-DD')}`);
          console.log(`   ⏰ Days until expiry: ${daysUntilExpiry}`);

          // Check if subscription has expired
          if (dayjs(user.nextPaymentDue).isBefore(now)) {
            if (isInFreeTrial) {
              console.log(`   ⏰ Free trial expired - marking as overdue (needs first payment)`);
            } else {
              console.log(`   ❌ Subscription expired - marking as overdue`);
            }

            // Mark as overdue (will be handled by markOverduePayments function)
            await tx
              .update(users)
              .set({ paymentStatus: "overdue" })
              .where(eq(users.id, user.id));

            console.log(`   ✅ User ${user.email} marked as overdue`);
          } else {
            if (isInFreeTrial) {
              console.log(`   ✅ Free trial still active (${daysUntilExpiry} days remaining)`);
            } else {
              console.log(`   ✅ Subscription still active`);
            }
          }

        } catch (userError) {
          console.error(`❌ Error processing user ${user.email} (ID: ${user.id}):`, userError);
        }
      }
    });

    console.info("✅ Subscription renewal check completed");
  } catch (error) {
    console.error("❌ Error in subscription renewal check:", error);
  }
};

export const fixIncorrectSubscriptionPeriods = async (): Promise<void> => {
  try {
    console.info("🔧 Fixing incorrect subscription periods based on payment amounts...");

    await postgresDb.transaction(async (tx) => {
      // Find payments with incorrect subscription periods based on amount
      const incorrectPayments = await tx
        .select({
          id: payments.id,
          userId: payments.userId,
          amount: payments.amount,
          subscriptionPeriod: payments.subscriptionPeriod
        })
        .from(payments)
        .where(eq(payments.status, "successful"));

      let fixedCount = 0;

      for (const payment of incorrectPayments) {
        const amount = parseFloat(payment.amount);
        let correctPeriod: number;

        // Determine correct subscription period based on amount (NEW PRICING)
        if (amount >= 360) {
          correctPeriod = 12; // Annual - ₵360
        } else if (amount >= 108) {
          correctPeriod = 3;  // Quarterly - ₵108
        } else {
          correctPeriod = 1;  // Monthly - ₵40
        }

        // Update if incorrect
        if (payment.subscriptionPeriod !== correctPeriod) {
          await tx
            .update(payments)
            .set({ subscriptionPeriod: correctPeriod })
            .where(eq(payments.id, payment.id));

          console.log(`   ✅ Fixed payment ID ${payment.id}: ₵${amount} → ${correctPeriod} months`);
          fixedCount++;
        }
      }

      console.info(`✅ Fixed ${fixedCount} payment records with incorrect subscription periods`);
    });
  } catch (error) {
    console.error("❌ Error fixing subscription periods:", error);
  }
};

export const markOverduePayments = async (): Promise<void> => {
  try {
    console.info("🔍 Checking for overdue payments...");

    const now = new Date();

    await postgresDb.transaction(async (tx) => {
      // Find users whose payment is overdue
      const usersToCheck = await tx
        .select({
          id: users.id,
          email: users.email,
          paymentStatus: users.paymentStatus,
          nextPaymentDue: users.nextPaymentDue,
          createdAt: users.createdAt,
          role: users.role
        })
        .from(users)
        .where(
          and(
            lt(users.nextPaymentDue, now), // Users past their payment date
            or(eq(users.paymentStatus, "paid"), eq(users.paymentStatus, "pending")), // Include "pending" users too
            or(eq(users.role, "admin"), eq(users.role, "superadmin")) // Only admins and superadmins pay
          )
        );

      let overdueCount = 0;
      let trialExpiredCount = 0;

      for (const user of usersToCheck) {
        // Check if user has made any payments (to determine if in trial)
        const hasPayments = await tx
          .select({ count: sql<number>`count(*)` })
          .from(payments)
          .where(
            and(
              eq(payments.userId, user.id),
              eq(payments.status, "successful")
            )
          );

        const isInFreeTrial = hasPayments[0].count === 0;
        const daysSinceCreation = dayjs(now).diff(dayjs(user.createdAt), 'day');

        if (isInFreeTrial) {
          console.log(`   🎁 ${user.email}: Free trial expired after ${daysSinceCreation} days`);
          trialExpiredCount++;
        } else {
          console.log(`   💰 ${user.email}: Subscription payment overdue`);
          overdueCount++;
        }

        // Mark as overdue regardless of trial status
        await tx
          .update(users)
          .set({ paymentStatus: "overdue" })
          .where(eq(users.id, user.id));
      }

      console.info(`✅ Payment status update completed:`);
      console.info(`   🎁 Free trials expired: ${trialExpiredCount}`);
      console.info(`   💰 Subscriptions overdue: ${overdueCount}`);
      console.info(`   📧 Total users marked overdue: ${usersToCheck.length}`);
    });
  } catch (error) {
    console.error("❌ Error updating overdue payments:", error);
  }
};

