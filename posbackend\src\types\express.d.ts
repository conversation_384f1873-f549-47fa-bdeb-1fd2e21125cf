import { JwtPayload } from "./type"; // Import your JWT payload type
import { UserRole } from "../types"; // Import UserRole type if you have it
import { DecodedToken } from "./type";

declare module "express" {
  export interface Request {
    user?: {
      id: number;
      role: UserRole;
    };
  }
}



declare module "express-serve-static-core" {
  interface Request {
    user?: JwtPayload; // Attach the user payload to the request
  }
}


declare global {
  namespace Express {
    interface Request {
      user?: DecodedToken; // Allow `user` to be optional
    }
  }
}
