"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2671],{22882:(e,t,a)=>{a.d(t,{A:()=>w});var s=a(95155),n=a(12115),r=a(83391),l=a(20148),i=a(27114),c=a(71349),u=a(43316),o=a(97838),d=a(1227),m=a(99315),y=a(45556),f=a(98623),p=a(72278),x=a(76046),g=a(21455),h=a.n(g),v=a(70854),D=a(19840);let w=e=>{let{showPayButton:t=!0}=e,a=(0,r.d4)(e=>e.auth.user),g=(0,r.d4)(e=>e.auth.accessToken),w=(0,r.wA)(),P=(0,x.useRouter)(),{needsPayment:b,status:S,daysRemaining:j}=(0,v._)(),[A,N]=(0,n.useState)(!1);if(!a)return(0,s.jsx)(l.A,{message:"Authentication Required",description:"Please log in to view your payment status.",type:"warning",showIcon:!0});let R=()=>{switch(a.paymentStatus){case"paid":return"success";case"pending":return"warning";case"overdue":return"error";default:return"default"}},k=e=>e?h()(e).format("MMM D, YYYY"):"N/A",C=async()=>{if(!a||!g){i.Ay.error("Unable to refresh: User not authenticated");return}N(!0),console.log("\uD83D\uDD04 Manual payment status refresh triggered");try{(await (0,D.hB)({dispatch:w,accessToken:g,currentUser:a,maxRetries:3,retryDelay:1e3})).success?(i.Ay.success("Payment status refreshed successfully! Reloading page..."),console.log("✅ Manual refresh successful"),setTimeout(()=>{window.location.reload()},1500)):(i.Ay.warning("Refresh completed but status may not have changed"),console.log("⚠️ Manual refresh completed with warnings"))}catch(e){console.error("❌ Manual refresh failed:",e),i.Ay.error("Failed to refresh payment status. Please try again.")}finally{N(!1)}};return(0,s.jsx)(c.A,{title:(0,s.jsxs)("div",{className:"flex items-center justify-between pl-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(()=>{switch(a.paymentStatus){case"paid":return(0,s.jsx)(d.A,{className:"text-green-500 text-xl"});case"pending":return(0,s.jsx)(m.A,{className:"text-yellow-500 text-xl"});case"overdue":return(0,s.jsx)(y.A,{className:"text-red-500 text-xl"});case"inactive":return(0,s.jsx)(f.A,{className:"text-gray-500 text-xl"});default:return null}})(),(0,s.jsx)("span",{className:"ml-2 text-gray-800",children:"Subscription Status"})]}),(0,s.jsx)(u.Ay,{type:"text",icon:(0,s.jsx)(p.A,{spin:A}),onClick:C,loading:A,size:"small",className:"text-gray-600 hover:text-blue-600",title:"Refresh payment status"})]}),className:"w-full shadow-md bg-white border border-gray-200",styles:{header:{backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"},body:{backgroundColor:"#ffffff"}},children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4 px-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,s.jsx)(o.A,{status:R(),text:a.paymentStatus.toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Plan:"}),(0,s.jsx)("span",{className:"text-gray-800",children:(()=>{if(!(null==a?void 0:a.lastPaymentDate)||!(null==a?void 0:a.nextPaymentDue))return"Monthly (₵40/month)";let e=h()(a.lastPaymentDate),t=h()(a.nextPaymentDue),s=t.diff(e,"day"),n=t.diff(e,"month",!0);return s>=350||n>=11?(console.log("✅ Detected: Annual subscription"),"Annual (₵360/year)"):s>=80||n>=2.5?(console.log("✅ Detected: Quarterly subscription"),"Quarterly (₵108/3 months)"):(console.log("✅ Detected: Monthly subscription"),"Monthly (₵40/month)")})()})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Last Payment:"}),(0,s.jsx)("span",{className:"text-gray-800",children:k(a.lastPaymentDate)})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Next Payment Due:"}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-gray-800",children:k(a.nextPaymentDue)}),a.nextPaymentDue&&"paid"===a.paymentStatus&&(0,s.jsx)("div",{className:"text-xs text-green-600",children:(()=>{let e=h()(a.nextPaymentDue).diff(h()(),"day");return e>0?"".concat(e," days remaining"):0===e?"Due today":"".concat(Math.abs(e)," days overdue")})()})]})]}),(0,s.jsx)(l.A,{message:(()=>{switch(a.paymentStatus){case"paid":return"Your subscription is active";case"pending":return"Your payment is pending verification";case"overdue":return"Your payment is overdue";case"inactive":return"Your subscription is inactive";default:return"Unknown status"}})(),type:R(),showIcon:!0,className:"mt-2"}),t&&b&&(0,s.jsx)(u.Ay,{type:"primary",onClick:()=>{P.push("/payment")},className:"w-full mt-4 bg-blue-600 hover:bg-blue-700 h-10 font-medium",children:"Manage Subscription"})]})})}},70854:(e,t,a)=>{a.d(t,{_:()=>i});var s=a(12115),n=a(83391),r=a(21455),l=a.n(r);let i=()=>{let e=(0,n.d4)(e=>e.auth.user),[t,a]=(0,s.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,s.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,s=!1,n=!0,r="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){s=!0,n=!1,r="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let r=l()(e.nextPaymentDue),i=l()();if(t=r.diff(i,"day"),a){let a=l()().diff(l()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:s,needsPayment:n})}}}else"pending"===e.paymentStatus?(s=!1,n=!0,r="pending"):"overdue"===e.paymentStatus?(s=!1,n=!0,r="overdue"):(s=!1,n=!0,r="inactive");a({isActive:s,daysRemaining:t,status:r,needsPayment:n})},[e]),t}},19840:(e,t,a)=>{a.d(t,{hB:()=>r,jH:()=>l});var s=a(7875),n=a(63065);let r=async e=>{let{dispatch:t,accessToken:a,currentUser:r,maxRetries:l=3,retryDelay:i=2e3}=e;console.log("\uD83D\uDD04 Starting user data refresh after payment..."),t(n.i$.util.invalidateTags(["User"])),t(n.i$.util.resetApiState()),console.log("\uD83E\uDDF9 Cleared all RTK Query cache");for(let e=1;e<=l;e++)try{var c;console.log("\uD83D\uDCE1 Attempt ".concat(e,"/").concat(l," - Fetching fresh user data...")),e>1&&await new Promise(e=>setTimeout(e,i));let u=await t(n.i$.endpoints.getCurrentUser.initiate(void 0,{forceRefetch:!0}));if("data"in u&&(null===(c=u.data)||void 0===c?void 0:c.success)&&u.data.data){let n=u.data.data;if(console.log("✅ Successfully fetched updated user data:",{id:n.id,paymentStatus:n.paymentStatus,lastPaymentDate:n.lastPaymentDate,nextPaymentDue:n.nextPaymentDue,attempt:e}),t((0,s.gV)({user:n,accessToken:a})),"paid"===n.paymentStatus)return console.log('\uD83C\uDF89 Payment status successfully updated to "paid"'),{success:!0,user:n};if(console.log('⚠️ Payment status is "'.concat(n.paymentStatus,'", not "paid". Retrying...')),e===l){console.log("⚠️ Max retries reached, using fallback update");let e={...r,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return t((0,s.gV)({user:e,accessToken:a})),{success:!0,user:e}}continue}if(console.log("⚠️ Attempt ".concat(e,": Backend response not successful or no data")),e===l)throw Error("Failed to fetch user data from backend");continue}catch(n){if(console.error("❌ Attempt ".concat(e," failed:"),n),e===l){if(console.log("\uD83D\uDCA5 All attempts failed, using fallback update"),!r)return{success:!1,error:"Failed to refresh user data and no current user for fallback"};{let e={...r,paymentStatus:"paid",lastPaymentDate:new Date().toISOString(),nextPaymentDue:new Date(Date.now()+2592e6).toISOString()};return t((0,s.gV)({user:e,accessToken:a})),console.log("✅ Fallback: Updated user payment status in Redux"),{success:!0,user:e}}}}return{success:!1,error:"Unexpected error in user data refresh"}},l=e=>{if(!e)return console.log("❌ No user data to verify"),!1;let t="paid"===e.paymentStatus,a=e.lastPaymentDate&&new Date(e.lastPaymentDate).getTime()>Date.now()-3e5;return console.log("\uD83D\uDD0D Payment status verification:",{paymentStatus:e.paymentStatus,isPaymentStatusPaid:t,lastPaymentDate:e.lastPaymentDate,hasRecentPaymentDate:a,nextPaymentDue:e.nextPaymentDue}),t}},75912:(e,t,a)=>{a.d(t,{r:()=>n});var s=a(55037);let n=(e,t)=>{"success"===e?s.oR.success(t):"error"===e?s.oR.error(t):"warning"===e&&(0,s.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};n.success=e=>n("success",e),n.error=e=>n("error",e),n.warning=e=>n("warning",e)}}]);