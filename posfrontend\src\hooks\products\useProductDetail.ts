"use client";

import { useGetProductByIdQuery, Product } from "@/reduxRTK/services/productApi";

export const useProductDetail = (productId: number | null) => {
  // Skip the query if no productId is provided
  const { 
    data, 
    error, 
    isLoading, 
    refetch 
  } = useGetProductByIdQuery(productId || 0, {
    skip: !productId,
  });

  // Extract product from the response
  const product: Product | null = data?.data || null;

  return {
    product,
    isLoading,
    error,
    refetch
  };
};
