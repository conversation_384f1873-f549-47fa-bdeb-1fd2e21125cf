import { postgresDb } from "../db/db";
import { products, stockAdjustments } from "../db/schema";
import { stockAdjustmentSchema } from "../validation/schema";
import { JwtPayload } from "../types/type";
import { authorizeAction } from "../utils/authorizeAction";
import { eq, and, count, desc, sql } from "drizzle-orm";

// ✅ **Create Stock Adjustment**
export const createStockAdjustment = async (
  requester: JwtPayload,
  adjustmentData: {
    productId: number;
    quantityChange: number;
    reason: string;
  }
) => {
  // ✅ Validate Input
  stockAdjustmentSchema.parse(adjustmentData);

  // ❌ Explicitly prevent cashiers from adjusting stock
  if (requester.role === "cashier") {
    throw new Error("Unauthorized: Cashiers cannot perform stock adjustments.");
  }

  await authorizeAction(requester, "create", "stockAdjustments");

  const createdBy = requester.id;

  // ✅ Fetch the current product stock
  const product = await postgresDb
    .select({
      id: products.id,
      stockQuantity: products.stockQuantity,
    })
    .from(products)
    .where(eq(products.id, adjustmentData.productId))
    .limit(1);

  if (!product.length) {
    throw new Error("Product not found.");
  }

  const currentStock = product[0].stockQuantity || 0;
  const newStock = currentStock + adjustmentData.quantityChange;

  // ✅ Ensure stock does not go negative
  if (newStock < 0) {
    throw new Error("Stock adjustment failed: Insufficient stock.");
  }

  // ✅ Proceed with stock adjustment
  const [newAdjustment] = await postgresDb
    .insert(stockAdjustments)
    .values({
      ...adjustmentData,
      adjustedBy: createdBy,
      createdBy,
      createdAt: new Date(),
    })
    .returning();

  if (!newAdjustment) throw new Error("Stock adjustment creation failed.");

  // ✅ Update product stock in the database - Using a direct SQL query for maximum reliability
  console.log(`Updating product ${adjustmentData.productId} stock from ${currentStock} to ${newStock}`);

  try {
    // First attempt with drizzle ORM
    const updatedProduct = await postgresDb
      .update(products)
      .set({ stockQuantity: newStock })
      .where(eq(products.id, adjustmentData.productId))
      .returning();

    console.log(`Product update result: ${JSON.stringify(updatedProduct)}`);

    // Verify the update was successful by directly querying the database
    const verifyProduct = await postgresDb
      .select({ stockQuantity: products.stockQuantity })
      .from(products)
      .where(eq(products.id, adjustmentData.productId))
      .limit(1);

    console.log(`Verification query result: Current stock is now ${verifyProduct[0]?.stockQuantity}`);

    if (verifyProduct[0]?.stockQuantity !== newStock) {
      console.error(`Stock update verification failed! Expected ${newStock}, got ${verifyProduct[0]?.stockQuantity}`);

      // If verification failed, try a direct SQL update as a fallback
      console.log("Attempting direct SQL update as fallback");
      await postgresDb.execute(
        sql`UPDATE products SET "stockQuantity" = ${newStock} WHERE id = ${adjustmentData.productId}`
      );

      // Verify again
      const reverifyProduct = await postgresDb
        .select({ stockQuantity: products.stockQuantity })
        .from(products)
        .where(eq(products.id, adjustmentData.productId))
        .limit(1);

      console.log(`Re-verification result: Current stock is now ${reverifyProduct[0]?.stockQuantity}`);
    }
  } catch (error: any) {
    console.error("Error updating product stock:", error);
    throw new Error(`Failed to update product stock: ${error?.message || String(error)}`);
  }

  // Log the current stock level after adjustment for verification
  console.log(`Product ${adjustmentData.productId} stock is now ${newStock} after adjustment`);

  return { stockAdjustment: newAdjustment };
};

// ✅ **Get All Stock Adjustments**
export const getAllStockAdjustments = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  await authorizeAction(requester, "getAll", "stockAdjustments");

  const offset = (page - 1) * limit;
  const isSuperadmin = requester.role === "superadmin";

  // ✅ Fetch all stock adjustments (Superadmin) or only the user's
  const [adjustmentData, totalAdjustments] = await Promise.all([
    postgresDb
      .select()
      .from(stockAdjustments)
      .where(isSuperadmin ? undefined : eq(stockAdjustments.createdBy, requester.id))
      .orderBy(desc(stockAdjustments.createdAt))
      .limit(limit)
      .offset(offset),
    postgresDb
      .select({ count: count() })
      .from(stockAdjustments)
      .where(isSuperadmin ? undefined : eq(stockAdjustments.createdBy, requester.id)),
  ]);

  return {
    total: totalAdjustments[0].count,
    page,
    perPage: limit,
    stockAdjustments: adjustmentData,
  };
};

// ✅ **Get Stock Adjustment by ID**
export const getStockAdjustmentById = async (
  requester: JwtPayload,
  id: number
) => {
  await authorizeAction(requester, "getById", "stockAdjustments", id);

  const isSuperadmin = requester.role === "superadmin";

  const adjustmentData = await postgresDb
    .select()
    .from(stockAdjustments)
    .where(
      isSuperadmin
        ? eq(stockAdjustments.id, id)
        : and(eq(stockAdjustments.id, id), eq(stockAdjustments.createdBy, requester.id))
    )
    .limit(1);

  if (adjustmentData.length === 0) throw new Error("Stock adjustment not found or unauthorized.");

  return adjustmentData[0];
};

// ✅ **Update Stock Adjustment**
export const updateStockAdjustmentById = async (
  requester: JwtPayload,
  adjustmentId: number,
  updateData: Partial<{ quantityChange: number; reason: string }>
) => {
  await authorizeAction(requester, "update", "stockAdjustments", adjustmentId);

  const isSuperadmin = requester.role === "superadmin";

  // ✅ Ensure the stock adjustment exists
  const existingAdjustment = await postgresDb
    .select()
    .from(stockAdjustments)
    .where(
      isSuperadmin
        ? eq(stockAdjustments.id, adjustmentId)
        : and(eq(stockAdjustments.id, adjustmentId), eq(stockAdjustments.createdBy, requester.id))
    )
    .limit(1);

  if (existingAdjustment.length === 0) {
    throw new Error("Stock adjustment not found or unauthorized.");
  }

  // ✅ Update the stock adjustment record
  const updatedAdjustment = await postgresDb
    .update(stockAdjustments)
    .set(updateData)
    .where(eq(stockAdjustments.id, adjustmentId))
    .returning();

  if (!updatedAdjustment || updatedAdjustment.length === 0) {
    throw new Error("Update failed: Stock adjustment not found.");
  }

  return { updatedStockAdjustment: updatedAdjustment[0] };
};

// ✅ **Delete Stock Adjustment**
export const deleteStockAdjustmentById = async (
  requester: JwtPayload,
  id: number
) => {
  await authorizeAction(requester, "delete", "stockAdjustments", id);

  const isSuperadmin = requester.role === "superadmin";

  // ✅ Ensure the stock adjustment exists
  const existingAdjustment = await postgresDb
    .select()
    .from(stockAdjustments)
    .where(
      isSuperadmin
        ? eq(stockAdjustments.id, id)
        : and(eq(stockAdjustments.id, id), eq(stockAdjustments.createdBy, requester.id))
    )
    .limit(1);

  if (existingAdjustment.length === 0) {
    throw new Error("Stock adjustment not found or unauthorized.");
  }

  // ✅ Delete the stock adjustment
  const deletedAdjustment = await postgresDb
    .delete(stockAdjustments)
    .where(eq(stockAdjustments.id, id))
    .returning({ deletedId: stockAdjustments.id });

  if (!deletedAdjustment || deletedAdjustment.length === 0) {
    throw new Error("Delete failed: Stock adjustment not found.");
  }

  return { deletedId: deletedAdjustment[0].deletedId };
};
