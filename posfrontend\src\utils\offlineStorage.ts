// Offline Storage Utility using IndexedDB for POS Sales
import { openDB, DBSchema, IDBPDatabase } from 'idb';

// Define the database schema
interface POSOfflineDB extends DBSchema {
  offlineSales: {
    key: string;
    value: OfflineSale;
    indexes: {
      'by-timestamp': Date;
      'by-status': 'pending' | 'synced' | 'failed';
    };
  };
  products: {
    key: number;
    value: OfflineProduct;
    indexes: {
      'by-name': string;
      'by-barcode': string;
    };
  };
  syncQueue: {
    key: string;
    value: SyncQueueItem;
    indexes: {
      'by-priority': number;
      'by-timestamp': Date;
    };
  };
  store: {
    key: string;
    value: any;
  };
}

// Types for offline data
export interface OfflineSale {
  id: string;
  totalAmount: number;
  paymentMethod: 'cash' | 'card' | 'mobile_money';
  items: OfflineSaleItem[];
  receiptUrl?: string;
  storeId?: number;
  createdBy: number;
  timestamp: Date;
  status: 'pending' | 'synced' | 'failed';
  syncAttempts: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

export interface OfflineSaleItem {
  productId: number;
  productName: string;
  quantity: number;
  price: number;
}

export interface OfflineProduct {
  id: number;
  name: string;
  price: string;
  stockQuantity: number;
  sku?: string;
  barcode?: string;
  imageUrl?: string;
  categoryId?: number;
  lastUpdated: Date;
}

export interface SyncQueueItem {
  id: string;
  type: 'sale' | 'product_update';
  data: any;
  priority: number;
  timestamp: Date;
  retryCount: number;
}

class OfflineStorageManager {
  private db: IDBPDatabase<POSOfflineDB> | null = null;
  private readonly DB_NAME = 'NEXAPO_POS_OFFLINE';
  private readonly DB_VERSION = 1;

  async init(): Promise<void> {
    try {
      this.db = await openDB<POSOfflineDB>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          // Create offline sales store
          const salesStore = db.createObjectStore('offlineSales', {
            keyPath: 'id'
          });
          salesStore.createIndex('by-timestamp', 'timestamp');
          salesStore.createIndex('by-status', 'status');

          // Create products cache store
          const productsStore = db.createObjectStore('products', {
            keyPath: 'id'
          });
          productsStore.createIndex('by-name', 'name');
          productsStore.createIndex('by-barcode', 'barcode');

          // Create sync queue store
          const syncStore = db.createObjectStore('syncQueue', {
            keyPath: 'id'
          });
          syncStore.createIndex('by-priority', 'priority');
          syncStore.createIndex('by-timestamp', 'timestamp');

          if (!db.objectStoreNames.contains('store')) {
            db.createObjectStore('store');
          }
        }
      });
      console.log('✅ Offline storage initialized');
    } catch (error) {
      console.error('❌ Failed to initialize offline storage:', error);
      throw error;
    }
  }

  // Sales Management
  async saveOfflineSale(sale: Omit<OfflineSale, 'id' | 'timestamp' | 'status' | 'syncAttempts'>): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');

    const saleId = `offline_sale_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const offlineSale: OfflineSale = {
      ...sale,
      id: saleId,
      timestamp: new Date(),
      status: 'pending',
      syncAttempts: 0
    };

    await this.db.add('offlineSales', offlineSale);
    console.log('💾 Offline sale saved:', saleId);
    return saleId;
  }

  async getOfflineSales(status?: 'pending' | 'synced' | 'failed'): Promise<OfflineSale[]> {
    if (!this.db) throw new Error('Database not initialized');

    if (status) {
      return await this.db.getAllFromIndex('offlineSales', 'by-status', status);
    }
    return await this.db.getAll('offlineSales');
  }

  async updateSaleStatus(saleId: string, status: 'pending' | 'synced' | 'failed', errorMessage?: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const sale = await this.db.get('offlineSales', saleId);
    if (!sale) throw new Error('Sale not found');

    sale.status = status;
    sale.syncAttempts += 1;
    sale.lastSyncAttempt = new Date();
    if (errorMessage) sale.errorMessage = errorMessage;

    await this.db.put('offlineSales', sale);
  }

  async deleteOfflineSale(saleId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.delete('offlineSales', saleId);
  }

  // Products Cache Management
  async cacheProducts(products: OfflineProduct[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tx = this.db.transaction('products', 'readwrite');
    const store = tx.objectStore('products');

    // Clear existing products and add new ones
    await store.clear();
    for (const product of products) {
      await store.add({
        ...product,
        lastUpdated: new Date()
      });
    }
    await tx.done;
    console.log(`💾 Cached ${products.length} products for offline use`);
  }

  async getCachedProducts(): Promise<OfflineProduct[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAll('products');
  }

  async searchProducts(query: string): Promise<OfflineProduct[]> {
    if (!this.db) throw new Error('Database not initialized');

    const allProducts = await this.db.getAll('products');
    const searchTerm = query.toLowerCase();

    return allProducts.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      (product.sku && product.sku.toLowerCase().includes(searchTerm)) ||
      (product.barcode && product.barcode.toLowerCase().includes(searchTerm))
    );
  }

  async getProductByBarcode(barcode: string): Promise<OfflineProduct | undefined> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getFromIndex('products', 'by-barcode', barcode);
  }

  // Sync Queue Management
  async addToSyncQueue(item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const queueItem: SyncQueueItem = {
      ...item,
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      retryCount: 0
    };

    await this.db.add('syncQueue', queueItem);
  }

  async getSyncQueue(): Promise<SyncQueueItem[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAllFromIndex('syncQueue', 'by-priority');
  }

  async removeSyncQueueItem(itemId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.delete('syncQueue', itemId);
  }

  // Store details caching
  async cacheStore(storeData: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.put('store', storeData, 'current-store');
    console.log('💾 Store details cached for offline use');
  }

  async getCachedStore(): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.get('store', 'current-store');
  }

  // Utility Methods
  async getStorageStats(): Promise<{
    pendingSales: number;
    syncedSales: number;
    failedSales: number;
    cachedProducts: number;
    queueItems: number;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    const [pendingSales, syncedSales, failedSales, products, queue] = await Promise.all([
      this.db.getAllFromIndex('offlineSales', 'by-status', 'pending'),
      this.db.getAllFromIndex('offlineSales', 'by-status', 'synced'),
      this.db.getAllFromIndex('offlineSales', 'by-status', 'failed'),
      this.db.getAll('products'),
      this.db.getAll('syncQueue')
    ]);

    return {
      pendingSales: pendingSales.length,
      syncedSales: syncedSales.length,
      failedSales: failedSales.length,
      cachedProducts: products.length,
      queueItems: queue.length
    };
  }

  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tx = this.db.transaction(['offlineSales', 'products', 'syncQueue'], 'readwrite');
    await Promise.all([
      tx.objectStore('offlineSales').clear(),
      tx.objectStore('products').clear(),
      tx.objectStore('syncQueue').clear()
    ]);
    await tx.done;
    console.log('🗑️ All offline data cleared');
  }
}

// Create singleton instance
export const offlineStorage = new OfflineStorageManager();

// Initialize on module load
if (typeof window !== 'undefined') {
  offlineStorage.init().catch(console.error);
}

export default offlineStorage;
