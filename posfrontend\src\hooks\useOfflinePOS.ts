// Offline POS Hook
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { offlineStorage, OfflineProduct, OfflineSale } from '@/utils/offlineStorage';
import { offlineSync, isOfflineMode } from '@/utils/offlineSync';
import { useGetAllProductsQuery } from '@/reduxRTK/services/productApi';
import { useCreateSaleMutation } from '@/reduxRTK/services/salesApi';
import { showMessage } from '@/utils/showMessage';
import { generateReceiptHTML, generateReceiptImage } from '@/utils/cloudinaryUtils';
import { useGetUserDefaultStoreQuery } from '@/reduxRTK/services/userStoreApi';

export interface CartItem {
  productId: number;
  productName: string;
  quantity: number;
  price: number;
}

export interface OfflineStatus {
  isOffline: boolean;
  pendingSales: number;
  failedSales: number;
  cachedProducts: number;
  lastSync?: Date;
}

export const useOfflinePOS = () => {
  const { user } = useAuth();
  const [isOffline, setIsOffline] = useState(false);
  const [cachedProducts, setCachedProducts] = useState<OfflineProduct[]>([]);
  const [cachedStore, setCachedStore] = useState<any>(null);
  const [offlineStatus, setOfflineStatus] = useState<OfflineStatus>({
    isOffline: false,
    pendingSales: 0,
    failedSales: 0,
    cachedProducts: 0
  });

  // Online API hooks with force cache option
  const { data: onlineProductsData, isLoading: loadingOnlineProducts } = useGetAllProductsQuery(
    { page: 1, limit: 1000 },
    { 
      skip: isOffline,
      // Force cache the response
      refetchOnMountOrArgChange: false,
      refetchOnReconnect: false
    }
  );
  const [createOnlineSale] = useCreateSaleMutation();

  // Fetch store data online
  const { data: storeData } = useGetUserDefaultStoreQuery(user?.id || 0, { skip: !user?.id });

  // Load cached products
  const loadCachedProducts = useCallback(async () => {
    try {
      const products = await offlineStorage.getCachedProducts();
      setCachedProducts(products);
    } catch (error) {
      console.error('Failed to load cached products:', error);
    }
  }, []);

  // Update offline status
  const updateOfflineStatus = useCallback(async () => {
    try {
      const stats = await offlineStorage.getStorageStats();
      const syncStatus = await offlineSync.getSyncStatus();

      setOfflineStatus({
        isOffline: !navigator.onLine,
        pendingSales: stats.pendingSales,
        failedSales: stats.failedSales,
        cachedProducts: stats.cachedProducts,
        lastSync: syncStatus.lastSyncAttempt
      });

      console.log('✅ Offline status updated:', {
        isOffline: !navigator.onLine,
        cachedProducts: stats.cachedProducts
      });
    } catch (error) {
      console.error('Failed to update offline status:', error);
    }
  }, []);

  // Initialize offline capabilities
  useEffect(() => {
    const initializeOffline = async () => {
      try {
        await offlineStorage.init();
        await updateOfflineStatus();
        await loadCachedProducts();
        console.log('✅ Offline POS initialized');
      } catch (error) {
        console.error('Failed to initialize offline POS:', error);
      }
    };

    initializeOffline();

    // Set up periodic refresh of cached data while online
    const refreshInterval = setInterval(() => {
      if (navigator.onLine) {
        updateOfflineStatus();
        loadCachedProducts();
      }
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => clearInterval(refreshInterval);
  }, [loadCachedProducts, updateOfflineStatus]);

  // Monitor network status
  useEffect(() => {
    const updateNetworkStatus = () => {
      const offline = !navigator.onLine;
      setIsOffline(offline);
      updateOfflineStatus();
      console.log(`Network status changed: ${offline ? 'offline' : 'online'}`);
    };

    updateNetworkStatus();
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
    };
  }, [updateOfflineStatus]);

  // Cache products when online data is available
  useEffect(() => {
    const cacheProductsFromOnline = async () => {
      if (onlineProductsData?.data?.products && !isOffline) {
        try {
          const productsToCache: OfflineProduct[] = onlineProductsData.data.products.map(product => ({
            id: product.id,
            name: product.name,
            price: product.price,
            stockQuantity: product.stockQuantity,
            sku: product.sku,
            barcode: product.barcode,
            imageUrl: product.imageUrl || undefined,
            categoryId: product.categoryId,
            lastUpdated: new Date()
          }));
          await offlineStorage.cacheProducts(productsToCache);
          await loadCachedProducts();
        } catch (error) {
          console.error('Failed to cache products:', error);
        }
      }
    };
    cacheProductsFromOnline();
  }, [onlineProductsData, isOffline, loadCachedProducts]);

  // Cache store details when online
  useEffect(() => {
    const cacheStoreFromOnline = async () => {
      if (storeData?.data && !isOffline) {
        try {
          await offlineStorage.cacheStore(storeData.data);
          setCachedStore(storeData.data);
        } catch (error) {
          console.error('Failed to cache store details:', error);
        }
      }
    };
    cacheStoreFromOnline();
  }, [storeData, isOffline]);

  // Get products (online or cached)
  const getProducts = useCallback((): OfflineProduct[] => {
    if (isOffline || !onlineProductsData?.data?.products) {
      return cachedProducts;
    }
    
    // Convert online products to offline format
    return onlineProductsData.data.products.map(product => ({
      id: product.id,
      name: product.name,
      price: product.price,
      stockQuantity: product.stockQuantity,
      sku: product.sku,
      barcode: product.barcode,
      imageUrl: product.imageUrl || undefined,
      categoryId: product.categoryId,
      lastUpdated: new Date()
    }));
  }, [isOffline, onlineProductsData, cachedProducts]);

  // Search products
  const searchProducts = useCallback(async (query: string): Promise<OfflineProduct[]> => {
    if (isOffline) {
      return await offlineStorage.searchProducts(query);
    }
    
    const products = getProducts();
    const searchTerm = query.toLowerCase();
    
    return products.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      (product.sku && product.sku.toLowerCase().includes(searchTerm)) ||
      (product.barcode && product.barcode.toLowerCase().includes(searchTerm))
    );
  }, [isOffline, getProducts]);

  // Find product by barcode
  const findProductByBarcode = useCallback(async (barcode: string): Promise<OfflineProduct | undefined> => {
    if (isOffline) {
      return await offlineStorage.getProductByBarcode(barcode);
    }
    
    const products = getProducts();
    return products.find(product => 
      product.barcode === barcode || product.sku === barcode
    );
  }, [isOffline, getProducts]);

  // Create sale (online or offline)
  const createSale = useCallback(async (
    cartItems: CartItem[],
    paymentMethod: 'cash' | 'card' | 'mobile_money',
    storeId?: number
  ): Promise<{ success: boolean; saleId?: string; isOffline?: boolean }> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const totalAmount = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // Generate receipt
    let receiptUrl = '';
    try {
      const receiptHTML = generateReceiptHTML({
        id: Date.now(),
        totalAmount,
        paymentMethod,
        transactionDate: new Date().toISOString(),
        items: cartItems.map(item => ({
          productName: item.productName,
          quantity: item.quantity,
          price: item.price
        }))
      }, {
        name: 'POS System',
        address: '',
        city: '',
        country: 'Ghana'
      });
      
      receiptUrl = await generateReceiptImage(receiptHTML);
    } catch (error) {
      console.warn('Failed to generate receipt:', error);
    }

    if (isOffline) {
      // Save sale offline
      try {
        const saleId = await offlineStorage.saveOfflineSale({
          totalAmount,
          paymentMethod,
          items: cartItems,
          receiptUrl,
          storeId,
          createdBy: user.id
        });

        await updateOfflineStatus();
        showMessage('success', `Sale saved offline - will sync when connection returns`);
        
        return { success: true, saleId, isOffline: true };
      } catch (error) {
        console.error('Failed to save offline sale:', error);
        throw new Error('Failed to save sale offline');
      }
    } else {
      // Create sale online
      try {
        const saleData = {
          totalAmount,
          paymentMethod,
          items: cartItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price
          })),
          receiptUrl,
          storeId
        };

        const response = await createOnlineSale(saleData).unwrap();
        
        if (response.success) {
          showMessage('success', 'Sale created successfully');
          return { success: true, saleId: response.data?.sales?.[0]?.saleId?.toString() || 'unknown' };
        } else {
          throw new Error(response.message || 'Sale creation failed');
        }
      } catch (error) {
        console.error('Online sale failed:', error);
        
        // Fallback to offline if online fails
        try {
          const saleId = await offlineStorage.saveOfflineSale({
            totalAmount,
            paymentMethod,
            items: cartItems,
            receiptUrl,
            storeId,
            createdBy: user.id
          });

          await updateOfflineStatus();
          showMessage('warning', 'Online sale failed - saved offline instead');
          
          return { success: true, saleId, isOffline: true };
        } catch (offlineError) {
          console.error('Offline fallback failed:', offlineError);
          throw new Error('Both online and offline sale creation failed');
        }
      }
    }
  }, [user, isOffline, createOnlineSale, updateOfflineStatus]);

  // Get offline sales
  const getOfflineSales = useCallback(async (): Promise<OfflineSale[]> => {
    return await offlineStorage.getOfflineSales();
  }, []);

  // Force sync
  const forceSyncNow = useCallback(async (): Promise<void> => {
    await offlineSync.forceSyncNow();
    await updateOfflineStatus();
  }, [updateOfflineStatus]);

  // Get store details (online or cached)
  const getStore = useCallback((): any => {
    if (isOffline || !storeData?.data) {
      return cachedStore;
    }
    return storeData.data;
  }, [isOffline, storeData, cachedStore]);

  return {
    // State
    isOffline,
    offlineStatus,
    isLoading: loadingOnlineProducts && cachedProducts.length === 0,
    
    // Products
    products: getProducts(),
    searchProducts,
    findProductByBarcode,
    
    // Sales
    createSale,
    getOfflineSales,
    
    // Sync
    forceSyncNow,
    updateOfflineStatus,

    getStore
  };
};
