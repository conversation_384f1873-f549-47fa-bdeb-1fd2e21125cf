"use client";

import { useState, useEffect } from 'react';

/**
 * Hook to determine if we should use mobile table layout
 * Returns true for mobile devices (width < 768px)
 */
export const useResponsiveTable = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on mount
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return isMobile;
};
