"use client";

import { useDeleteSupplierMutation } from "@/reduxRTK/services/supplierApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useSupplierDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a supplier
  const [deleteSupplier, { isLoading }] = useDeleteSupplierMutation();

  const deleteSupplierById = async (supplierId: number) => {
    try {
      const result = await deleteSupplier(supplierId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete supplier");
      }

      showMessage("success", "Supplier deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete supplier error:", error);
      showMessage("error", error.message || "Failed to delete supplier");
      throw error;
    }
  };

  return {
    deleteSupplier: deleteSupplierById,
    isDeleting: isLoading
  };
};
