"use client";

import { useUpdateUserMutation } from "@/reduxRTK/services/authApi";
import { ApiResponse, UpdateUserDto, User } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useUserUpdate = (onSuccess?: () => void) => {
  // RTK Query hook for updating a user
  const [updateUser, { isLoading }] = useUpdateUserMutation();

  const updateExistingUser = async (userId: number, userData: UpdateUserDto) => {
    try {
      const result = await updateUser({
        userId,
        data: userData
      }).unwrap() as ApiResponse<User>;

      if (!result.success) {
        throw new Error(result.message || "Failed to update user");
      }

      showMessage("success", "User updated successfully");

      if (onSuccess) {
        onSuccess();
      }

      return result.data;
    } catch (error: any) {
      console.error("Update user error:", error);
      showMessage("error", error.message || "Failed to update user");
      throw error;
    }
  };

  return {
    updateUser: updateExistingUser,
    isUpdating: isLoading
  };
};
