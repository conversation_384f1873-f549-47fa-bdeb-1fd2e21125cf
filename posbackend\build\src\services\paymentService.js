"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPaymentHistory = exports.verifyPayment = exports.verifyPaystackPaymentByReference = exports.getPaymentById = exports.getAllPayments = exports.initializePayment = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const updatePaymentStatus_1 = require("../utils/updatePaymentStatus");
const paystackProvider_1 = require("../integrations/paystackProvider");
/**
 * Initialize a Paystack payment
 * @param requester The user making the request
 * @param paymentData The payment data
 * @returns The payment initialization result
 */
const initializePayment = async (requester, paymentData) => {
    try {
        // Generate unique reference
        const reference = `pos_${requester.id}_${Date.now()}`;
        // Convert amount to kobo (Paystack uses smallest currency unit)
        const amountInKobo = (0, paystackProvider_1.convertCedisToKobo)(paymentData.amount);
        console.log('=== PAYMENT INITIALIZATION DEBUG ===');
        console.log('Original amount (GHS):', paymentData.amount);
        console.log('Converted amount (pesewas):', amountInKobo);
        console.log('Email:', paymentData.email);
        console.log('Reference:', reference);
        console.log('Callback URL:', paymentData.callbackUrl);
        console.log('Subscription Period (months):', paymentData.subscriptionPeriod || 1);
        console.log('User ID:', requester.id);
        console.log('=====================================');
        // Initialize payment with Paystack (no explicit currency)
        const initResult = await (0, paystackProvider_1.initializePaystackPayment)(paymentData.email, amountInKobo, reference, paymentData.callbackUrl);
        if (!initResult.success) {
            throw new Error(initResult.message || "Payment initialization failed");
        }
        // Store payment record with pending status
        console.log("Attempting to insert payment record...");
        let newPayment;
        try {
            [newPayment] = await db_1.postgresDb
                .insert(schema_1.payments)
                .values({
                userId: requester.id,
                amount: paymentData.amount.toString(),
                provider: "paystack",
                transactionId: reference,
                paystackReference: reference,
                status: "pending",
                currency: "GHS",
                subscriptionPeriod: paymentData.subscriptionPeriod || 1,
                paidAt: new Date()
            })
                .returning();
            console.log("Payment record inserted successfully:", newPayment.id);
        }
        catch (dbError) {
            console.error("Database insertion error:", dbError);
            // Check for specific database errors
            if (dbError.message.includes('timeout')) {
                throw new Error("Database timeout - please try again in a moment");
            }
            else if (dbError.message.includes('duplicate key')) {
                throw new Error("Payment reference already exists - please try again");
            }
            else if (dbError.message.includes('column') && dbError.message.includes('does not exist')) {
                throw new Error("Database schema issue - please contact support");
            }
            else {
                throw new Error(`Database error: ${dbError.message}`);
            }
        }
        return {
            payment: newPayment,
            authorizationUrl: initResult.data.authorization_url,
            accessCode: initResult.data.access_code,
            reference: reference
        };
    }
    catch (error) {
        console.error("Payment initialization error:", error);
        throw new Error(error.message || "Failed to initialize payment");
    }
};
exports.initializePayment = initializePayment;
/**
 * Get all payments (paginated)
 * @param requester The user making the request
 * @param page The page number
 * @param limit The number of items per page
 * @returns Paginated list of payments
 */
const getAllPayments = async (requester, page = 1, limit = 10) => {
    // Only admins and superadmins can view all payments
    if (requester.role !== "admin" && requester.role !== "superadmin") {
        throw new Error("Unauthorized: Only admins and superadmins can view all payments.");
    }
    const offset = (page - 1) * limit;
    const isSuperadmin = requester.role === "superadmin";
    // Different filter logic based on role
    let paymentsFilter;
    if (isSuperadmin) {
        // Superadmins can see all payments
        paymentsFilter = undefined;
    }
    else {
        // Admins can only see their own payments and payments from users they created
        paymentsFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
    }
    // Get payments with user information
    const [paymentsData, totalPayments] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.payments.id,
            userId: schema_1.payments.userId,
            userName: schema_1.users.name,
            amount: schema_1.payments.amount,
            provider: schema_1.payments.provider,
            transactionId: schema_1.payments.transactionId,
            status: schema_1.payments.status,
            paidAt: schema_1.payments.paidAt
        })
            .from(schema_1.payments)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.payments.userId, schema_1.users.id))
            .where(paymentsFilter)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.payments.paidAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.payments)
            .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.payments.userId, schema_1.users.id))
            .where(paymentsFilter)
    ]);
    return {
        total: totalPayments[0]?.count ?? 0,
        page,
        perPage: limit,
        payments: paymentsData
    };
};
exports.getAllPayments = getAllPayments;
/**
 * Get payment by ID
 * @param requester The user making the request
 * @param paymentId The payment ID
 * @returns The payment
 */
const getPaymentById = async (requester, paymentId) => {
    // Determine access based on role
    let paymentFilter;
    if (requester.role === "superadmin") {
        // Superadmins can see any payment
        paymentFilter = (0, drizzle_orm_1.eq)(schema_1.payments.id, paymentId);
    }
    else if (requester.role === "admin") {
        // Admins can see their own payments and payments from users they created
        paymentFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.id, paymentId), (0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
    }
    else {
        // Regular users can only see their own payments
        paymentFilter = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.payments.id, paymentId), (0, drizzle_orm_1.eq)(schema_1.payments.userId, requester.id));
    }
    // Get payment with user information
    const payment = await db_1.postgresDb
        .select({
        id: schema_1.payments.id,
        userId: schema_1.payments.userId,
        userName: schema_1.users.name,
        amount: schema_1.payments.amount,
        provider: schema_1.payments.provider,
        transactionId: schema_1.payments.transactionId,
        status: schema_1.payments.status,
        paidAt: schema_1.payments.paidAt
    })
        .from(schema_1.payments)
        .leftJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.payments.userId, schema_1.users.id))
        .where(paymentFilter)
        .limit(1);
    if (payment.length === 0) {
        throw new Error("Payment not found or unauthorized.");
    }
    return payment[0];
};
exports.getPaymentById = getPaymentById;
/**
 * Verify a Paystack payment by reference
 * @param requester The user making the request
 * @param reference The Paystack reference to verify
 * @returns The verification result
 */
const verifyPaystackPaymentByReference = async (requester, reference) => {
    try {
        // Get the payment record by reference
        const [payment] = await db_1.postgresDb
            .select()
            .from(schema_1.payments)
            .where((0, drizzle_orm_1.eq)(schema_1.payments.paystackReference, reference))
            .limit(1);
        if (!payment) {
            return {
                success: false,
                message: "Payment not found.",
                payment: null
            };
        }
        // Check if user has permission to verify this payment
        if (requester.role !== "superadmin" && payment.userId !== requester.id) {
            return {
                success: false,
                message: "Unauthorized: You can only verify your own payments.",
                payment: null
            };
        }
        // If payment is already successful, return it
        if (payment.status === "successful") {
            return {
                success: true,
                message: "Payment already verified.",
                payment: payment
            };
        }
        // Verify the transaction with Paystack
        const verificationResult = await (0, paystackProvider_1.verifyPaystackPayment)(reference);
        if (verificationResult.success && verificationResult.data.status === "success") {
            console.log(`💳 Payment verification successful for payment ID ${payment.id}, updating database...`);
            try {
                // Update payment status to successful
                const [updatedPayment] = await db_1.postgresDb
                    .update(schema_1.payments)
                    .set({
                    status: "successful",
                    authorizationCode: verificationResult.data.authorization?.authorization_code,
                    channel: verificationResult.data.channel,
                    paidAt: new Date()
                })
                    .where((0, drizzle_orm_1.eq)(schema_1.payments.id, payment.id))
                    .returning();
                console.log(`✅ Payment record updated successfully for payment ID ${payment.id}`);
                // Update user payment status with subscription period
                console.log(`🔄 Updating user payment status for user ID ${payment.userId} with ${payment.subscriptionPeriod} month(s)...`);
                await (0, updatePaymentStatus_1.updatePaymentStatus)(payment.userId, "paid", payment.subscriptionPeriod || 1);
                console.log(`✅ User payment status updated successfully for user ID ${payment.userId}`);
                // Verify the payment status was actually updated
                const isVerified = await (0, updatePaymentStatus_1.verifyPaymentStatusUpdate)(payment.userId, "paid");
                if (!isVerified) {
                    console.error(`⚠️ Payment status verification failed for user ID ${payment.userId}. Status may not have been properly updated.`);
                }
                else {
                    console.log(`🎉 Payment status verification successful for user ID ${payment.userId}`);
                }
                return {
                    success: true,
                    message: "Payment verified successfully.",
                    payment: updatedPayment
                };
            }
            catch (error) {
                console.error(`❌ Error updating payment/user status for payment ID ${payment.id}:`, error);
                // Even if database update fails, the payment was successful on Paystack
                // We should still return success but log the database issue
                console.error(`⚠️ Payment was successful on Paystack but database update failed. Manual intervention may be required.`);
                // Try to update user status one more time as a fallback
                try {
                    console.log(`🔄 Attempting fallback user status update for user ID ${payment.userId}...`);
                    await (0, updatePaymentStatus_1.updatePaymentStatus)(payment.userId, "paid", payment.subscriptionPeriod || 1);
                    console.log(`✅ Fallback user payment status update successful for user ID ${payment.userId}`);
                }
                catch (fallbackError) {
                    console.error(`❌ Fallback user status update also failed for user ID ${payment.userId}:`, fallbackError);
                }
                return {
                    success: true,
                    message: "Payment verified successfully. Database update may be pending.",
                    payment: payment // Return original payment record
                };
            }
        }
        else {
            // Update payment status to failed
            const [updatedPayment] = await db_1.postgresDb
                .update(schema_1.payments)
                .set({
                status: "failed"
            })
                .where((0, drizzle_orm_1.eq)(schema_1.payments.id, payment.id))
                .returning();
            return {
                success: false,
                message: "Payment verification failed or payment was not successful.",
                payment: updatedPayment
            };
        }
    }
    catch (error) {
        console.error("Paystack payment verification error:", error);
        return {
            success: false,
            message: error.message || "Payment verification failed.",
            payment: null
        };
    }
};
exports.verifyPaystackPaymentByReference = verifyPaystackPaymentByReference;
/**
 * Verify a Paystack payment by transaction ID
 * @param requester The user making the request (not used but kept for consistency)
 * @param transactionId The transaction ID
 * @returns Verification result
 */
const verifyPayment = async (_requester, transactionId) => {
    // Find the payment by transaction ID
    const payment = await db_1.postgresDb
        .select()
        .from(schema_1.payments)
        .where((0, drizzle_orm_1.eq)(schema_1.payments.transactionId, transactionId))
        .limit(1);
    if (payment.length === 0) {
        return {
            success: false,
            message: "Payment not found.",
            payment: null
        };
    }
    const paymentRecord = payment[0];
    // If payment is already verified, return success
    if (paymentRecord.status === "successful") {
        return {
            success: true,
            message: "Payment already verified.",
            payment: paymentRecord
        };
    }
    // If payment is failed, return failure
    if (paymentRecord.status === "failed") {
        return {
            success: false,
            message: "Payment verification failed.",
            payment: paymentRecord
        };
    }
    try {
        // Only support Paystack payments
        if (paymentRecord.provider === "paystack" && paymentRecord.paystackReference) {
            const paystackResult = await (0, paystackProvider_1.verifyPaystackPayment)(paymentRecord.paystackReference);
            if (paystackResult.success && paystackResult.data.status === "success") {
                console.log(`💳 Payment verification successful for payment ID ${paymentRecord.id}, updating database...`);
                try {
                    // Update payment status to successful
                    const [updatedPayment] = await db_1.postgresDb
                        .update(schema_1.payments)
                        .set({
                        status: "successful",
                        authorizationCode: paystackResult.data.authorization?.authorization_code,
                        channel: paystackResult.data.channel
                    })
                        .where((0, drizzle_orm_1.eq)(schema_1.payments.id, paymentRecord.id))
                        .returning();
                    console.log(`✅ Payment record updated successfully for payment ID ${paymentRecord.id}`);
                    // Update user payment status with subscription period
                    console.log(`🔄 Updating user payment status for user ID ${paymentRecord.userId} with ${paymentRecord.subscriptionPeriod} month(s)...`);
                    await (0, updatePaymentStatus_1.updatePaymentStatus)(paymentRecord.userId, "paid", paymentRecord.subscriptionPeriod || 1);
                    console.log(`✅ User payment status updated successfully for user ID ${paymentRecord.userId}`);
                    // Verify the payment status was actually updated
                    const isVerified = await (0, updatePaymentStatus_1.verifyPaymentStatusUpdate)(paymentRecord.userId, "paid");
                    if (!isVerified) {
                        console.error(`⚠️ Payment status verification failed for user ID ${paymentRecord.userId}. Status may not have been properly updated.`);
                    }
                    else {
                        console.log(`🎉 Payment status verification successful for user ID ${paymentRecord.userId}`);
                    }
                    return {
                        success: true,
                        message: "Payment verified successfully.",
                        payment: updatedPayment
                    };
                }
                catch (error) {
                    console.error(`❌ Error updating payment/user status for payment ID ${paymentRecord.id}:`, error);
                    // Even if database update fails, the payment was successful on Paystack
                    console.error(`⚠️ Payment was successful on Paystack but database update failed. Manual intervention may be required.`);
                    // Try to update user status one more time as a fallback
                    try {
                        console.log(`🔄 Attempting fallback user status update for user ID ${paymentRecord.userId}...`);
                        await (0, updatePaymentStatus_1.updatePaymentStatus)(paymentRecord.userId, "paid", paymentRecord.subscriptionPeriod || 1);
                        console.log(`✅ Fallback user payment status update successful for user ID ${paymentRecord.userId}`);
                    }
                    catch (fallbackError) {
                        console.error(`❌ Fallback user status update also failed for user ID ${paymentRecord.userId}:`, fallbackError);
                    }
                    return {
                        success: true,
                        message: "Payment verified successfully. Database update may be pending.",
                        payment: paymentRecord // Return original payment record
                    };
                }
            }
            else {
                // Update payment status to failed
                const [updatedPayment] = await db_1.postgresDb
                    .update(schema_1.payments)
                    .set({ status: "failed" })
                    .where((0, drizzle_orm_1.eq)(schema_1.payments.id, paymentRecord.id))
                    .returning();
                return {
                    success: false,
                    message: "Payment verification failed or payment was not successful.",
                    payment: updatedPayment
                };
            }
        }
        else {
            return {
                success: false,
                message: "Only Paystack payments are supported.",
                payment: paymentRecord
            };
        }
    }
    catch (error) {
        console.error("Payment verification error:", error);
        return {
            success: false,
            message: error.message || "Payment verification failed.",
            payment: paymentRecord
        };
    }
};
exports.verifyPayment = verifyPayment;
/**
 * Get payment history for a user
 * @param requester The user making the request
 * @param userId The user ID
 * @param page The page number
 * @param limit The number of items per page
 * @returns Paginated list of payments for the user
 */
const getUserPaymentHistory = async (requester, userId, page = 1, limit = 10) => {
    // Only allow admins/superadmins to view other users' payment history
    if (userId !== requester.id &&
        requester.role !== "admin" &&
        requester.role !== "superadmin") {
        throw new Error("Unauthorized: You can only view your own payment history.");
    }
    const offset = (page - 1) * limit;
    // Get payments for the user
    const [paymentsData, totalPayments] = await Promise.all([
        db_1.postgresDb
            .select({
            id: schema_1.payments.id,
            amount: schema_1.payments.amount,
            provider: schema_1.payments.provider,
            transactionId: schema_1.payments.transactionId,
            status: schema_1.payments.status,
            paidAt: schema_1.payments.paidAt
        })
            .from(schema_1.payments)
            .where((0, drizzle_orm_1.eq)(schema_1.payments.userId, userId))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.payments.paidAt))
            .limit(limit)
            .offset(offset),
        db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.payments)
            .where((0, drizzle_orm_1.eq)(schema_1.payments.userId, userId))
    ]);
    return {
        total: totalPayments[0]?.count ?? 0,
        page,
        perPage: limit,
        payments: paymentsData
    };
};
exports.getUserPaymentHistory = getUserPaymentHistory;
