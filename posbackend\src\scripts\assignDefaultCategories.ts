import { postgresDb } from "../db/db";
import { expenses, expenseCategories, users } from "../db/schema";
import { eq, isNull } from "drizzle-orm";

/**
 * Script to assign default categories to expenses that don't have categories
 */
async function assignDefaultCategories() {
  try {
    console.log("🔄 Starting category assignment for uncategorized expenses...");

    // Get the first admin user to use as creator
    const [firstAdmin] = await postgresDb
      .select({ id: users.id })
      .from(users)
      .where(eq(users.role, "admin"))
      .limit(1);

    if (!firstAdmin) {
      console.error("❌ No admin user found. Please create an admin user first.");
      return;
    }

    console.log(`ℹ️  Using admin user ID ${firstAdmin.id} as category creator`);

    // First, create default categories if they don't exist
    const defaultCategories = [
      { name: "General", description: "General business expenses", color: "#6B7280", isDefault: true },
      { name: "Utilities", description: "Utility bills and services", color: "#3B82F6", isDefault: true },
      { name: "Office Expenses", description: "Office supplies and equipment", color: "#10B981", isDefault: true },
      { name: "Travel", description: "Travel and transportation", color: "#F59E0B", isDefault: true },
      { name: "Marketing", description: "Marketing and advertising", color: "#8B5CF6", isDefault: true },
    ];

    // Insert default categories (ignore if they already exist)
    for (const category of defaultCategories) {
      try {
        const [existingCategory] = await postgresDb
          .select()
          .from(expenseCategories)
          .where(eq(expenseCategories.name, category.name))
          .limit(1);

        if (!existingCategory) {
          await postgresDb
            .insert(expenseCategories)
            .values({
              ...category,
              createdBy: firstAdmin.id,
            });
          console.log(`✅ Created default category: ${category.name}`);
        } else {
          console.log(`ℹ️  Category already exists: ${category.name}`);
        }
      } catch (error) {
        console.log(`⚠️  Could not create category ${category.name}:`, error);
      }
    }

    // Get the "General" category ID for uncategorized expenses
    const [generalCategory] = await postgresDb
      .select()
      .from(expenseCategories)
      .where(eq(expenseCategories.name, "General"))
      .limit(1);

    if (!generalCategory) {
      console.error("❌ Could not find or create General category");
      return;
    }

    // Get all expenses without categories
    const uncategorizedExpenses = await postgresDb
      .select()
      .from(expenses)
      .where(isNull(expenses.categoryId));

    console.log(`📊 Found ${uncategorizedExpenses.length} uncategorized expenses`);

    if (uncategorizedExpenses.length === 0) {
      console.log("✅ All expenses already have categories assigned!");
      return;
    }

    // Assign categories based on expense titles (smart assignment)
    for (const expense of uncategorizedExpenses) {
      let categoryId = generalCategory.id; // Default to General

      // Smart category assignment based on title
      const title = expense.title.toLowerCase();

      if (title.includes('utility') || title.includes('utilities') || title.includes('electric') || title.includes('water') || title.includes('gas')) {
        const [utilitiesCategory] = await postgresDb
          .select()
          .from(expenseCategories)
          .where(eq(expenseCategories.name, "Utilities"))
          .limit(1);
        if (utilitiesCategory) categoryId = utilitiesCategory.id;
      } else if (title.includes('office') || title.includes('supplies') || title.includes('equipment')) {
        const [officeCategory] = await postgresDb
          .select()
          .from(expenseCategories)
          .where(eq(expenseCategories.name, "Office Expenses"))
          .limit(1);
        if (officeCategory) categoryId = officeCategory.id;
      } else if (title.includes('travel') || title.includes('transport') || title.includes('fuel') || title.includes('taxi')) {
        const [travelCategory] = await postgresDb
          .select()
          .from(expenseCategories)
          .where(eq(expenseCategories.name, "Travel"))
          .limit(1);
        if (travelCategory) categoryId = travelCategory.id;
      } else if (title.includes('marketing') || title.includes('advertising') || title.includes('promotion')) {
        const [marketingCategory] = await postgresDb
          .select()
          .from(expenseCategories)
          .where(eq(expenseCategories.name, "Marketing"))
          .limit(1);
        if (marketingCategory) categoryId = marketingCategory.id;
      }

      // Update the expense with the assigned category
      await postgresDb
        .update(expenses)
        .set({
          categoryId,
          updatedAt: new Date()
        })
        .where(eq(expenses.id, expense.id));

      const categoryName = await postgresDb
        .select({ name: expenseCategories.name })
        .from(expenseCategories)
        .where(eq(expenseCategories.id, categoryId))
        .limit(1);

      console.log(`✅ Assigned "${expense.title}" to category: ${categoryName[0]?.name || 'General'}`);
    }

    console.log("🎉 Successfully assigned categories to all uncategorized expenses!");

  } catch (error) {
    console.error("❌ Error assigning categories:", error);
  } finally {
    process.exit(0);
  }
}

// Run the script
assignDefaultCategories();
