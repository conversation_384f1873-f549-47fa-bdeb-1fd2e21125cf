exports.id=4262,exports.ids=[4262],exports.modules={94262:(e,t,s)=>{"use strict";s.d(t,{A:()=>S});var a=s(45512),r=s(58009),l=s(45103),i=s(41257),o=s(37764),n=s(7325),d=s(3117),c=s(88206),u=s(79203),m=s(21419),p=s(58733),x=s(63440),g=s(45905),h=s(86977),y=s(91054),f=s(10499),b=s(2274),v=s(46211),j=s(43087),w=s(98776),N=s(49792);let C=({onProductFound:e,onProductNotFound:t})=>{let[s,a]=(0,r.useState)(!1),[l,i]=(0,r.useState)(""),{data:o,isLoading:n,refetch:d}=(0,b.eb)(l,{skip:!l,refetchOnMountOrArgChange:!0});return{isOpen:s,openScanner:(0,r.useCallback)(()=>{a(!0)},[]),closeScanner:(0,r.useCallback)(()=>{a(!1),i("")},[]),handleBarcodeScanned:(0,r.useCallback)(async s=>{if(console.log("Camera barcode scanned:",s),!s||""===s.trim()){(0,N.r)("error","Invalid barcode scanned");return}let a=s.trim();console.log("Searching for barcode:",a),i(a),setTimeout(async()=>{try{let s=await d();if(console.log("Barcode search result:",s),s.data?.success&&s.data.data){let t=s.data.data;console.log("Product found via camera:",t),(0,N.r)("success",`Product found: ${t.name}`),e(t)}else console.log("No product found for barcode:",a),console.log("API response:",s.data),(0,N.r)("warning",`No product found for barcode: ${a}`),t&&t(a)}catch(e){console.error("Error searching for product:",e),(0,N.r)("error","Error searching for product")}},100)},[d,e,t]),isLoading:n}};var k=s(66785);s(42095),s(13053);let S=({isOpen:e,onClose:t,onSuccess:s})=>{let[S]=i.A.useForm(),[A]=i.A.useForm(),[$,F]=(0,r.useState)([]),[D,P]=(0,r.useState)(null),[I,O]=(0,r.useState)(1),[T,L]=(0,r.useState)(0),[E,Q]=(0,r.useState)(""),[q,z]=(0,r.useState)(null),[W,U]=(0,r.useState)(!1),[M,R]=(0,r.useState)(!1),[B,_]=(0,r.useState)(null),[H,V]=(0,r.useState)(!1),[Y,X]=(0,r.useState)(""),[G,K]=(0,r.useState)("Walk-in Customer"),[J,Z]=(0,r.useState)(!1),[ee,et]=(0,r.useState)([]),[es,ea]=(0,r.useState)(!1),[er,el]=(0,r.useState)(null),[ei,eo]=(0,r.useState)(null),{isOpen:en,openScanner:ed,closeScanner:ec,handleBarcodeScanned:eu,isLoading:em}=C({onProductFound:e=>{P(e),O(1),A&&A.setFieldsValue({productId:e.id,quantity:1}),setTimeout(()=>{eA()},100)},onProductNotFound:e=>{console.log("Product not found for barcode:",e)}}),ep=async e=>{if(X(e),e&&e.length>=8)try{console.log("Searching for product with barcode:",e);let t=ex?.data?.products||[],s=t.find(t=>t.barcode===e.trim());if(s||(s=t.find(t=>t.sku===e.trim())),s){if(console.log("Product found via hardware scanner:",s),s.stockQuantity<=0){(0,N.r)("error",`${s.name} is out of stock`),X("");return}let e={productId:s.id,productName:s.name,price:s.price,quantity:1},t=$.findIndex(e=>e.productId===s.id);if(t>=0&&$[t].quantity+1>s.stockQuantity){(0,N.r)("error",`Only ${s.stockQuantity} units available in stock`),X("");return}F(t=>[...t,{...e,price:Number(e.price)}]),X(""),(0,N.r)("success",`Product added: ${s.name}`)}else console.log("No product found for barcode:",e),(0,N.r)("warning",`No product found for barcode: ${e}`)}catch(e){console.error("Error searching for product:",e),(0,N.r)("error","Error searching for product")}};(0,r.useEffect)(()=>{console.log("Items state changed:",$)},[$]);let{data:ex,isLoading:eg,refetch:eh}=(0,b.r3)({page:1,limit:1e3,search:E},{refetchOnMountOrArgChange:!0,refetchOnFocus:!1,refetchOnReconnect:!0}),{data:ey,isLoading:ef}=(0,j.lg)({page:1,limit:100,search:""});(0,r.useEffect)(()=>{ex&&console.log("\uD83D\uDED2 Products loaded:",{total:ex.data?.total||0,productsCount:ex.data?.products?.length||0,isLoading:eg})},[ex,eg]);let{data:eb}=(0,v.lr)(0),{data:ev}=(0,v.Rv)(0),ej=e=>{let t=e.toLowerCase();return t.includes("electronic")||t.includes("tech")||t.includes("phone")||t.includes("computer")?"\uD83D\uDCF1":t.includes("clothing")||t.includes("fashion")||t.includes("apparel")||t.includes("wear")?"\uD83D\uDC55":t.includes("food")||t.includes("grocery")||t.includes("snack")||t.includes("drink")?"\uD83C\uDF55":t.includes("book")||t.includes("education")||t.includes("stationery")||t.includes("office")?"\uD83D\uDCDA":t.includes("toy")||t.includes("game")||t.includes("play")||t.includes("kid")?"\uD83E\uDDF8":t.includes("health")||t.includes("medical")||t.includes("pharmacy")||t.includes("care")?"\uD83D\uDC8A":t.includes("beauty")||t.includes("cosmetic")||t.includes("makeup")||t.includes("skincare")?"\uD83D\uDC84":t.includes("sport")||t.includes("fitness")||t.includes("gym")||t.includes("exercise")?"⚽":t.includes("home")||t.includes("furniture")||t.includes("decor")||t.includes("kitchen")?"\uD83C\uDFE0":t.includes("auto")||t.includes("car")||t.includes("vehicle")||t.includes("motor")?"\uD83D\uDE97":"\uD83C\uDFF7️"},ew=()=>{ea(!0)},eN=e=>{F(e.items),K(e.customer),L(e.totalAmount),el(e.id),ea(!1),(0,N.r)("success",`Loaded saved sale: ₵${e.totalAmount.toFixed(2)}`)},eC=e=>{et(t=>t.filter(t=>t.id!==e)),(0,N.r)("success","Saved sale deleted")};(0,r.useEffect)(()=>{ev?.data?(z(ev.data),S.setFieldsValue({storeId:ev.data.id})):eb?.data&&eb.data.length>0&&(z(eb.data[0]),S.setFieldsValue({storeId:eb.data[0].id}))},[ev,eb,S]);let[ek,{isLoading:eS}]=(0,f.E$)();(0,r.useEffect)(()=>{if($&&$.length>0){let e=$.reduce((e,t)=>e+t.price*t.quantity,0);L(e),S&&S.setFieldsValue({totalAmount:e}),console.log("Current items in useEffect:",$)}else L(0),S&&S.setFieldsValue({totalAmount:0})},[$,S]),(0,r.useEffect)(()=>{e?(console.log("\uD83D\uDED2 Sales panel opened - fetching fresh product data"),eh()):(S&&S.resetFields(),A&&A.resetFields(),F([]),P(null),O(1),L(0),_(null),R(!1),V(!1))},[e,S,A,eh]);let eA=()=>{if(!D){(0,N.r)("error","Please select a product");return}if(I<=0){(0,N.r)("error","Quantity must be greater than 0");return}if(D.stockQuantity<=0){(0,N.r)("error",`${D.name} is out of stock`);return}let e=$.findIndex(e=>e.productId===D.id);if(e>=0){let t=$[e].quantity+I;t>D.stockQuantity&&(t=D.stockQuantity,(0,N.r)("error",`Cannot add more than ${D.stockQuantity} units. Quantity set to maximum available.`));let s=[...$];s[e]={...s[e],quantity:t},F(s),(0,N.r)("success",`Updated quantity of ${D.name} to ${t}`)}else{let e=I;e>D.stockQuantity&&(e=D.stockQuantity,(0,N.r)("error",`Cannot add more than ${D.stockQuantity} units in stock. Quantity set to maximum available.`)),F([...$,{productId:D.id,productName:D.name,quantity:e,price:"string"==typeof D.price?parseFloat(D.price):D.price}]),(0,N.r)("success",`Added ${e} ${D.name} to sale`)}P(null),O(1),A&&A.setFieldsValue({productId:void 0,quantity:1})},e$=e=>{let t=[...$];t.splice(e,1),F(t),(0,N.r)("success","Item removed from sale")},eF=(e,t)=>{if(t<=0){e$(e);return}let s=$[e],a=ex?.data?.products.find(e=>e.id===s.productId);if(a&&t>a.stockQuantity){(0,N.r)("error",`Only ${a.stockQuantity} units available in stock`);return}let r=[...$];r[e]={...s,quantity:t},F(r),(0,N.r)("success","Quantity updated")},eD=(0,r.useCallback)(()=>{if(!B||H){console.log("Skipping print: ",B?"Already printed":"No receipt URL");return}console.log("Printing receipt:",B),V(!0);let e=document.createElement("iframe");e.style.display="none",document.body.appendChild(e),e.onload=()=>{e.contentWindow&&(e.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${B}" alt="Receipt" />
            </body>
          </html>
        `),e.contentWindow.document.close(),setTimeout(()=>{if(e.contentWindow){try{e.contentWindow.focus(),e.contentWindow.print()}catch(e){console.error("Error printing receipt:",e)}setTimeout(()=>{document.body.removeChild(e)},1e3)}},500))},e.src="about:blank"},[B,H]);(0,r.useEffect)(()=>{if(M&&B&&!H){let e=setTimeout(()=>{eD()},800);return()=>clearTimeout(e)}},[M,B,H,eD]);let eP=async()=>{try{if(0===$.length){(0,N.r)("error","Please add at least one item to the sale");return}let e=await S.validateFields();if(!q){(0,N.r)("error","No store information available. Please set up your store in your profile settings.");return}U(!0);let t=q||eb?.data?.find(t=>t.id===e.storeId)||{name:"POS System"},a=(0,k.jY)({id:Date.now(),totalAmount:T,paymentMethod:e.paymentMethod,transactionDate:new Date().toISOString(),items:$.map(e=>({productName:e.productName,quantity:e.quantity,price:e.price}))},t),r="https://receipt.example.com/placeholder";try{r=await (0,k.fS)(a)}catch(e){console.error("Failed to generate receipt image:",e)}let l={totalAmount:T,paymentMethod:e.paymentMethod,items:$.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:r,storeId:q?.id},i=await ek(l).unwrap();i.success?((0,N.r)("success","Sale created successfully"),er&&(et(e=>e.filter(e=>e.id!==er)),el(null),(0,N.r)("success","Saved sale completed and removed from pending list")),_(r),R(!0),eh(),setTimeout(()=>{s&&eh()},300)):(0,N.r)("error",i.message||"Failed to create sale")}catch(e){(0,N.r)("error",e.data?.message||"An error occurred while creating the sale")}finally{U(!1)}};return console.log("Rendering with items:",$),(0,a.jsxs)(w.A,{title:"Point of Sale",isOpen:e,onClose:t,width:"100vw",fullWidth:!0,children:[(0,a.jsxs)("div",{className:"pos-panel h-screen bg-gray-100 flex flex-col",children:[(0,a.jsxs)("div",{className:"bg-gray-800 text-white px-4 py-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-lg font-bold",children:"NEXAPO"}),(0,a.jsx)("span",{className:"text-sm",children:"POS System - v1.0.0"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("span",{className:"text-sm",children:["User: ","Admin"]})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col lg:flex-row",children:[(0,a.jsxs)("div",{className:"flex-1 p-2 lg:p-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 mb-3",children:[(0,a.jsx)(o.A,{placeholder:"Search products...",value:E,onChange:e=>Q(e.target.value),className:"h-10 rounded-md border w-full",suffix:(0,a.jsx)(p.A,{})}),(0,a.jsx)(o.A,{placeholder:"Barcode scanner...",value:Y,onChange:e=>ep(e.target.value),className:"h-10 rounded-md border w-full",style:{borderColor:"#10b981",backgroundColor:"#f0fdf4"}}),(0,a.jsxs)(n.A,{showSearch:!0,allowClear:!0,placeholder:"Filter by category",value:ei??void 0,onChange:e=>eo(e??null),optionFilterProp:"label",filterOption:(e,t)=>String(t?.label??"").toLowerCase().includes(e.toLowerCase()),className:"h-10 rounded-md border w-full",dropdownStyle:{fontSize:14},loading:ef,bordered:!0,children:[(0,a.jsx)(n.A.Option,{value:null,label:"All",children:"✨ All"}),ey?.data?.categories?.map(e=>a.jsxs(n.A.Option,{value:e.id,label:`${ej(e.name)} ${e.name}`,children:[ej(e.name)," ",e.name]},e.id))]})]}),ex?.data?.products?.length===0?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-10 text-gray-500",children:[(0,a.jsx)(x.A,{className:"text-4xl mb-4"}),(0,a.jsx)("p",{children:"No products found. Please add products to start selling."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-3 mb-3 lg:mb-4",children:ex?.data?.products?.filter(e=>{let t=!E||e.name.toLowerCase().includes(E.toLowerCase())||e.barcode?.toLowerCase().includes(E.toLowerCase())||e.sku?.toLowerCase().includes(E.toLowerCase()),s=null===ei||e.categoryId===ei;return t&&s})?.slice(0,36)?.map(e=>a.jsxs("div",{className:`aspect-square rounded-lg p-2 lg:p-3 cursor-pointer transition-all duration-200 flex flex-col items-center justify-center text-center shadow-md ${e.stockQuantity<=0?"bg-gray-200 opacity-50 cursor-not-allowed":"bg-blue-400 hover:bg-blue-500 text-white"}`,onClick:()=>{if(e.stockQuantity>0){let t,s="",a="success",r=$.findIndex(t=>t.productId===e.id);if(r>=0){let l=$[r].quantity+1;l>e.stockQuantity?(l=e.stockQuantity,s=`Cannot add more than ${e.stockQuantity} units. Quantity set to maximum available.`,a="error"):(s=`Updated quantity of ${e.name} to ${l}`,a="success"),(t=[...$])[r]={...t[r],quantity:l}}else s=`${e.name} added to cart`,a="success",t=[...$,{productId:e.id,productName:e.name,price:Number(e.price),quantity:1}];F(t),s&&N.r(a,s)}},children:[a.jsxs("div",{className:"w-12 h-12 lg:w-16 lg:h-16 bg-white/20 rounded-lg mb-1 lg:mb-2 flex items-center justify-center overflow-hidden",children:[e.imageUrl?a.jsx(l.default,{src:e.imageUrl,alt:e.name,width:64,height:64,className:"object-cover rounded-lg",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextElementSibling;t&&(t.style.display="flex")}}):null,a.jsx("span",{className:`text-2xl ${e.imageUrl?"hidden":"block"}`,children:"\uD83D\uDCE6"})]}),a.jsx("div",{className:"text-xs font-medium mb-1 line-clamp-2 leading-tight",children:e.name.length>15?`${e.name.substring(0,15)}...`:e.name}),a.jsxs("div",{className:"text-xs font-bold",children:["₵",Number(e.price).toFixed(2)]}),a.jsx("div",{className:`text-[11px] mt-1 ${e.stockQuantity>0?"text-green-700":"text-red-500"}`,style:{fontWeight:500},children:e.stockQuantity>0?`In Stock: ${e.stockQuantity}`:"Out of Stock"})]},e.id))}),(0,a.jsxs)("div",{className:"grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3",children:[(0,a.jsx)("div",{className:`bg-green-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${0===$.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-green-600"}`,onClick:()=>{if(0===$.length)return;let e={id:Date.now(),items:[...$],totalAmount:T,customer:G,timestamp:new Date().toISOString()};et(t=>[...t,e]),(0,N.r)("success",`Sale saved! Total: ₵${T.toFixed(2)}`),F([]),P(null),O(1),L(0),el(null),S.resetFields(),A.resetFields()},children:(0,a.jsx)("div",{className:"text-xs font-medium",children:"Save Sale"})}),(0,a.jsx)("div",{className:`bg-indigo-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${0===ee.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-indigo-600"}`,onClick:()=>{0!==ee.length&&ew()},children:(0,a.jsxs)("div",{className:"text-xs font-medium",children:["Load Saved",ee.length>0&&(0,a.jsxs)("div",{className:"text-xs opacity-80",children:["(",ee.length,")"]})]})}),(0,a.jsx)("div",{className:`bg-orange-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${0===$.length?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-orange-600"}`,onClick:()=>{0!==$.length&&(F([]),P(null),O(1),L(0),el(null),S.resetFields(),A.resetFields(),(0,N.r)("success","Cart cleared"))},children:(0,a.jsx)("div",{className:"text-xs font-medium",children:"Clear Cart"})}),(0,a.jsx)("div",{className:"bg-red-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-red-600 transition-colors shadow-md",onClick:()=>{t()},children:(0,a.jsx)("div",{className:"text-xs font-medium",children:"Close POS"})}),(0,a.jsx)("div",{className:"bg-gray-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-gray-600 transition-colors shadow-md",onClick:()=>{(0,N.r)("success","Help: Click products to add to cart, use barcode scanner, adjust quantities, then click PAY to complete sale")},children:(0,a.jsx)("div",{className:"text-xs font-medium",children:"Help"})})]})]})]}),(0,a.jsx)("div",{className:"w-full lg:w-80 bg-white border-t lg:border-t-0 lg:border-l border-gray-200 flex flex-col",children:(0,a.jsxs)("div",{className:"flex-1 p-2 lg:p-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-3 lg:p-4 mb-3 lg:mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium mb-2",children:"Cart Items"}),(0,a.jsx)("div",{className:"space-y-2 max-h-40 lg:max-h-60 overflow-y-auto",children:0===$.length?(0,a.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,a.jsx)(g.A,{className:"text-2xl mb-2"}),(0,a.jsx)("div",{children:"No items in cart"})]}):$.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between py-2 border-b border-gray-100 space-y-2 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:e.productName}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["₵",Number(e.price).toFixed(2)," each"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,a.jsx)(d.Ay,{size:"small",type:"text",icon:(0,a.jsx)("span",{className:"text-xs",children:"−"}),onClick:()=>eF(t,e.quantity-1),className:"w-6 h-6 flex items-center justify-center"}),(0,a.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,a.jsx)(d.Ay,{size:"small",type:"text",icon:(0,a.jsx)("span",{className:"text-xs",children:"+"}),onClick:()=>eF(t,e.quantity+1),className:"w-6 h-6 flex items-center justify-center"}),(0,a.jsx)(d.Ay,{size:"small",type:"text",icon:(0,a.jsx)(h.A,{}),onClick:()=>e$(t),className:"w-6 h-6 flex items-center justify-center text-red-500"})]})]},`${e.productId}-${t}`))})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"TOTAL"}),(0,a.jsxs)("span",{className:"font-bold",children:["₵",T.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:"TAX"}),(0,a.jsx)("span",{children:"₵0.00"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:"NET"}),(0,a.jsxs)("span",{children:["₵",T.toFixed(2)]})]})]})}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 mb-4",children:[(0,a.jsx)("div",{className:"text-xs text-gray-600 mb-1",children:"Customer"}),(0,a.jsx)("div",{className:"font-medium text-sm",children:G}),ee.length>0&&(0,a.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:[ee.length," saved sale",ee.length>1?"s":""]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(i.A,{form:S,layout:"vertical",children:(0,a.jsx)(i.A.Item,{name:"paymentMethod",initialValue:"cash",className:"mb-4",children:(0,a.jsxs)(n.A,{className:"w-full",size:"large",disabled:J,children:[(0,a.jsx)(n.A.Option,{value:"cash",children:"\uD83D\uDCB5 Cash"}),(0,a.jsx)(n.A.Option,{value:"card",children:"\uD83D\uDCB3 Card"}),(0,a.jsx)(n.A.Option,{value:"mobile_money",children:"\uD83D\uDCF1 Mobile Money"})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-3 lg:mb-4",children:[(0,a.jsxs)(d.Ay,{className:"bg-orange-400 text-white border-0 hover:bg-orange-500",onClick:()=>{let e="Walk-in Customer"===G?"Custom Customer":"Walk-in Customer";K(e),(0,N.r)("success",`Customer set to: ${e}`)},children:["\uD83D\uDC65 ","Walk-in Customer"===G?"Walk-in":"Custom"]}),(0,a.jsx)(d.Ay,{className:`text-white border-0 ${J?"bg-red-500 hover:bg-red-600":"bg-orange-400 hover:bg-orange-500"}`,onClick:()=>{Z(!J),(0,N.r)("success",J?"POS Unlocked":"POS Locked")},children:J?"\uD83D\uDD13 Unlock":"\uD83D\uDD12 Lock"})]}),(0,a.jsx)(d.Ay,{className:"w-full h-10 lg:h-12 bg-red-500 text-white border-0 hover:bg-red-600 text-sm lg:text-lg font-bold mb-2",onClick:()=>{if(0===$.length){(0,N.r)("error","Please add items to cart");return}let e={id:Date.now(),items:[...$],totalAmount:T,customer:G,timestamp:new Date().toISOString(),status:"pending"};et(t=>[...t,e]),(0,N.r)("success",`Sale saved as pending! Total: ₵${T.toFixed(2)}`),F([]),P(null),O(1),L(0),el(null),S.resetFields(),A.resetFields()},children:"\uD83D\uDDD1️ SALE (Save Pending)"}),(0,a.jsx)(d.Ay,{type:"primary",className:"w-full h-12 lg:h-16 bg-green-500 border-0 hover:bg-green-600 text-lg lg:text-xl font-bold",onClick:eP,loading:eS||W,children:"\uD83D\uDCB0 PAY (Complete Sale)"})]})]})})]})]}),(0,a.jsx)(c.A,{title:(0,a.jsxs)("div",{className:"flex items-center text-gray-800",children:[(0,a.jsx)(y.A,{className:"mr-2"}),(0,a.jsx)("span",{children:"Receipt Preview"})]}),open:M,onCancel:()=>{R(!1),_(null),V(!1),S.resetFields(),A.resetFields(),F([]),P(null),O(1),L(0)},width:500,centered:!0,className:"receipt-preview-modal",footer:[(0,a.jsx)(d.Ay,{onClick:()=>{R(!1),_(null),V(!1),S.resetFields(),A.resetFields(),F([]),P(null),O(1),L(0)},className:"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200",children:"Close & New Sale"},"close"),(0,a.jsx)(d.Ay,{type:"primary",icon:(0,a.jsx)(y.A,{}),onClick:()=>{H&&V(!1),eD()},className:"bg-blue-600 hover:bg-blue-700",children:H?"Print Again":"Print Receipt"},"print")],children:(0,a.jsx)("div",{className:"flex flex-col items-center",children:B?(0,a.jsx)("div",{className:"receipt-image-container",children:(0,a.jsx)(u.A,{src:B,alt:"Receipt",className:"receipt-image",style:{maxWidth:"100%"}})}):(0,a.jsx)("div",{className:"flex h-64 items-center justify-center",children:(0,a.jsx)(m.A,{size:"large"})})})}),(0,a.jsx)(c.A,{title:"Saved Sales",open:es,onCancel:()=>ea(!1),width:600,centered:!0,footer:[(0,a.jsx)(d.Ay,{onClick:()=>ea(!1),className:"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200",children:"Close"},"close")],children:(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===ee.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(g.A,{className:"text-4xl mb-4"}),(0,a.jsx)("p",{children:"No saved sales available"})]}):(0,a.jsx)("div",{className:"space-y-3",children:ee.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"font-medium text-gray-800",children:e.customer}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₵",e.totalAmount.toFixed(2)]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mb-2",children:new Date(e.timestamp).toLocaleString()}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[e.items.length," item",e.items.length>1?"s":"",": "," ",e.items.map(e=>e.productName).join(", ")]})]}),(0,a.jsxs)("div",{className:"ml-4 flex flex-col space-y-2",children:[(0,a.jsx)(d.Ay,{type:"primary",size:"small",onClick:()=>eN(e),className:"bg-blue-600 hover:bg-blue-700",children:"Load Sale"}),(0,a.jsx)(d.Ay,{danger:!0,size:"small",onClick:()=>eC(e.id),children:"Delete"})]})]})},e.id))})})})]})}},66785:(e,t,s)=>{"use strict";s.d(t,{fS:()=>l,iG:()=>o,jY:()=>i});let a="dutmiedgk",r="pos_receipts",l=async e=>{try{let t=document.createElement("div");t.innerHTML=e,t.style.width="350px",t.style.padding="20px",t.style.backgroundColor="white",t.style.color="#555555",t.style.fontFamily="Arial, sans-serif",t.style.position="absolute",t.style.left="-9999px",t.style.borderLeft="1px solid #000000",t.style.borderRight="1px solid #000000",t.style.borderTop="1px solid #000000",t.style.borderBottom="1px solid #000000",t.style.boxSizing="border-box",t.style.fontSize="14px",t.style.lineHeight="1.5",document.body.appendChild(t);let l=(await s.e(2835).then(s.bind(s,2835))).default,i=await l(t,{scale:3,backgroundColor:"white",logging:!1,width:350,height:t.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(t);let o=await new Promise(e=>{i.toBlob(t=>{e(t)},"image/png",.95)}),n=new FormData;n.append("file",o),n.append("upload_preset",r);let d=await fetch(`https://api.cloudinary.com/v1_1/${a}/image/upload`,{method:"POST",body:n}),c=await d.json();if(d.ok)return c.secure_url;throw console.error("Cloudinary upload failed:",c),Error(c.error?.message||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},i=(e,t)=>{let s=new Date(e.transactionDate),a=s.toLocaleDateString(),r=s.toLocaleTimeString(),l=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return`
  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">

  <!-- Header and Title -->
  <div style="text-align: center; margin-bottom: 10px;">
    <div style="font-size: 18px; font-weight: bold;">${t.name||"POS System"}</div>
    <div style="font-size: 16px; font-weight: bold;">#INV-${e.id}-${new Date().getFullYear()}${(new Date().getMonth()+1).toString().padStart(2,"0")}${new Date().getDate().toString().padStart(2,"0")}</div>
    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">
      ${t.address?`<div>${t.address}</div>`:""}
      ${t.city?`<div>${t.city}</div>`:""}
      ${t.country?`<div>${t.country}</div>`:""}
    </div>
  </div>

  <!-- Items Table -->
  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">
    <thead>
      <tr>
        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>
      </tr>
    </thead>
    <tbody>
      ${e.items.map((e,t)=>{let s="string"==typeof e.price?parseFloat(e.price):e.price,a=s*e.quantity;return`
          <tr>
            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">${t+1}. ${e.productName}</td>
            <td style="text-align: right;">${e.quantity}</td>
            <td style="text-align: right;">${s.toFixed(2)}</td>
            <td style="text-align: right;">${a.toFixed(2)}</td>
          </tr>
        `}).join("")}
    </tbody>
  </table>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Totals -->
  <div style="display: flex; justify-content: space-between; font-weight: bold;">
    <div>TOTAL</div>
    <div>GHS ${"string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2)}</div>
  </div>
  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">
    <div>TAX</div>
    <div>0.00</div>
  </div>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Payment Info -->
  <div style="font-size: 11px; margin-bottom: 6px;">
    <div>Payment: ${l.toUpperCase()}</div>
    ${l.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":""}
  </div>

  <!-- Date/Time -->
  <div style="display: flex; justify-content: space-between; font-size: 11px;">
    <div><strong>Time:</strong> ${r}</div>
    <div><strong>Date:</strong> ${a}</div>
  </div>

  <!-- Barcode -->
  <div style="text-align: center; margin: 12px 0;">
    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>
    <div style="font-size: 11px; margin-top: 4px;">${e.id}</div>
  </div>

  <!-- Footer -->
  <div style="text-align: center; font-size: 12px; margin-top: 8px;">
    THANK YOU!
  </div>
</div>

`},o=async e=>{try{let t=new FormData;t.append("file",e),t.append("upload_preset",r),t.append("folder","products");let s=await fetch(`https://api.cloudinary.com/v1_1/${a}/image/upload`,{method:"POST",body:t}),l=await s.json();if(s.ok)return l.secure_url;throw console.error("Cloudinary upload failed:",l),Error(l.error?.message||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},13053:()=>{},42095:()=>{}};