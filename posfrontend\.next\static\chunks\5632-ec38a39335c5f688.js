(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5632,7114],{63285:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M120 160H72c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zm833 0h-48c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zM200 736h112c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H200c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm321 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm126 0h178c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H647c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-255 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-79 64H201c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm257 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm256 0H648c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h178c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-385 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"barcode",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},40794:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},35153:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"minus",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},95872:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},95693:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M696 480H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"plus-circle",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},72278:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},34802:(a,t,e)=>{"use strict";e.d(t,{A:()=>p});var i=e(85407),n=e(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"}}]},name:"shopping",theme:"outlined"};var c=e(84021);let p=n.forwardRef(function(a,t){return n.createElement(c.A,(0,i.A)({},a,{ref:t,icon:o}))})},27114:(a,t,e)=>{"use strict";e.d(t,{Ay:()=>$});var i=e(39014),n=e(12115),o=e(89842),c=e(31049),p=e(11432),l=e(24330),r=e(4951),s=e(6140),d=e(51629),m=e(92984),u=e(16419),v=e(4617),f=e.n(v),x=e(22946),g=e(7926),b=e(67548),h=e(78877),y=e(70695),k=e(1086),w=e(56204);let j=a=>{let{componentCls:t,iconCls:e,boxShadow:i,colorText:n,colorSuccess:o,colorError:c,colorWarning:p,colorInfo:l,fontSizeLG:r,motionEaseInOutCirc:s,motionDurationSlow:d,marginXS:m,paddingXS:u,borderRadiusLG:v,zIndexPopup:f,contentPadding:x,contentBg:g}=a,h="".concat(t,"-notice"),k=new b.Mo("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:u,transform:"translateY(0)",opacity:1}}),w=new b.Mo("MessageMoveOut",{"0%":{maxHeight:a.height,padding:u,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),j={padding:u,textAlign:"center",["".concat(t,"-custom-content")]:{display:"flex",alignItems:"center"},["".concat(t,"-custom-content > ").concat(e)]:{marginInlineEnd:m,fontSize:r},["".concat(h,"-content")]:{display:"inline-block",padding:x,background:g,borderRadius:v,boxShadow:i,pointerEvents:"all"},["".concat(t,"-success > ").concat(e)]:{color:o},["".concat(t,"-error > ").concat(e)]:{color:c},["".concat(t,"-warning > ").concat(e)]:{color:p},["".concat(t,"-info > ").concat(e,",\n      ").concat(t,"-loading > ").concat(e)]:{color:l}};return[{[t]:Object.assign(Object.assign({},(0,y.dF)(a)),{color:n,position:"fixed",top:m,width:"100%",pointerEvents:"none",zIndex:f,["".concat(t,"-move-up")]:{animationFillMode:"forwards"},["\n        ".concat(t,"-move-up-appear,\n        ").concat(t,"-move-up-enter\n      ")]:{animationName:k,animationDuration:d,animationPlayState:"paused",animationTimingFunction:s},["\n        ".concat(t,"-move-up-appear").concat(t,"-move-up-appear-active,\n        ").concat(t,"-move-up-enter").concat(t,"-move-up-enter-active\n      ")]:{animationPlayState:"running"},["".concat(t,"-move-up-leave")]:{animationName:w,animationDuration:d,animationPlayState:"paused",animationTimingFunction:s},["".concat(t,"-move-up-leave").concat(t,"-move-up-leave-active")]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{["".concat(h,"-wrapper")]:Object.assign({},j)}},{["".concat(t,"-notice-pure-panel")]:Object.assign(Object.assign({},j),{padding:0,textAlign:"start"})}]},O=(0,k.OF)("Message",a=>[j((0,w.oX)(a,{height:150}))],a=>({zIndexPopup:a.zIndexPopupBase+h.jH+10,contentBg:a.colorBgElevated,contentPadding:"".concat((a.controlHeightLG-a.fontSize*a.lineHeight)/2,"px ").concat(a.paddingSM,"px")}));var z=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let C={info:n.createElement(m.A,null),success:n.createElement(r.A,null),error:n.createElement(s.A,null),warning:n.createElement(d.A,null),loading:n.createElement(u.A,null)},E=a=>{let{prefixCls:t,type:e,icon:i,children:o}=a;return n.createElement("div",{className:f()("".concat(t,"-custom-content"),"".concat(t,"-").concat(e))},i||C[e],n.createElement("span",null,o))};var S=e(79624),A=e(28415);function D(a){let t;let e=new Promise(e=>{t=a(()=>{e(!0)})}),i=()=>{null==t||t()};return i.then=(a,t)=>e.then(a,t),i.promise=e,i}var P=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let F=a=>{let{children:t,prefixCls:e}=a,i=(0,g.A)(e),[o,c,p]=O(e,i);return o(n.createElement(x.ph,{classNames:{list:f()(c,p,i)}},t))},q=(a,t)=>{let{prefixCls:e,key:i}=t;return n.createElement(F,{prefixCls:e,key:i},a)},I=n.forwardRef((a,t)=>{let{top:e,prefixCls:i,getContainer:o,maxCount:p,duration:l=3,rtl:r,transitionName:s,onAllRemoved:d}=a,{getPrefixCls:m,getPopupContainer:u,message:v,direction:g}=n.useContext(c.QO),b=i||m("message"),h=n.createElement("span",{className:"".concat(b,"-close-x")},n.createElement(S.A,{className:"".concat(b,"-close-icon")})),[y,k]=(0,x.hN)({prefixCls:b,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=e?e:8}),className:()=>f()({["".concat(b,"-rtl")]:null!=r?r:"rtl"===g}),motion:()=>(function(a,t){return{motionName:null!=t?t:"".concat(a,"-move-up")}})(b,s),closable:!1,closeIcon:h,duration:l,getContainer:()=>(null==o?void 0:o())||(null==u?void 0:u())||document.body,maxCount:p,onAllRemoved:d,renderNotifications:q});return n.useImperativeHandle(t,()=>Object.assign(Object.assign({},y),{prefixCls:b,message:v})),k}),R=0;function B(a){let t=n.useRef(null);return(0,A.rJ)("Message"),[n.useMemo(()=>{let a=a=>{var e;null===(e=t.current)||void 0===e||e.close(a)},e=e=>{if(!t.current){let a=()=>{};return a.then=()=>{},a}let{open:i,prefixCls:o,message:c}=t.current,p="".concat(o,"-notice"),{content:l,icon:r,type:s,key:d,className:m,style:u,onClose:v}=e,x=P(e,["content","icon","type","key","className","style","onClose"]),g=d;return null==g&&(R+=1,g="antd-message-".concat(R)),D(t=>(i(Object.assign(Object.assign({},x),{key:g,content:n.createElement(E,{prefixCls:o,type:s,icon:r},l),placement:"top",className:f()(s&&"".concat(p,"-").concat(s),m,null==c?void 0:c.className),style:Object.assign(Object.assign({},null==c?void 0:c.style),u),onClose:()=>{null==v||v(),t()}})),()=>{a(g)}))},i={open:e,destroy:e=>{var i;void 0!==e?a(e):null===(i=t.current)||void 0===i||i.destroy()}};return["info","success","warning","error","loading"].forEach(a=>{i[a]=(t,i,n)=>{let o,c,p;return o=t&&"object"==typeof t&&"content"in t?t:{content:t},"function"==typeof i?p=i:(c=i,p=n),e(Object.assign(Object.assign({onClose:p,duration:c},o),{type:a}))}}),i},[]),n.createElement(I,Object.assign({key:"message-holder"},a,{ref:t}))]}let M=null,T=a=>a(),H=[],N={};function _(){let{getContainer:a,duration:t,rtl:e,maxCount:i,top:n}=N,o=(null==a?void 0:a())||document.body;return{getContainer:()=>o,duration:t,rtl:e,maxCount:i,top:n}}let L=n.forwardRef((a,t)=>{let{messageConfig:e,sync:i}=a,{getPrefixCls:p}=(0,n.useContext)(c.QO),l=N.prefixCls||p("message"),r=(0,n.useContext)(o.B),[s,d]=B(Object.assign(Object.assign(Object.assign({},e),{prefixCls:l}),r.message));return n.useImperativeHandle(t,()=>{let a=Object.assign({},s);return Object.keys(a).forEach(t=>{a[t]=function(){return i(),s[t].apply(s,arguments)}}),{instance:a,sync:i}}),d}),V=n.forwardRef((a,t)=>{let[e,i]=n.useState(_),o=()=>{i(_)};n.useEffect(o,[]);let c=(0,p.cr)(),l=c.getRootPrefixCls(),r=c.getIconPrefixCls(),s=c.getTheme(),d=n.createElement(L,{ref:t,sync:o,messageConfig:e});return n.createElement(p.Ay,{prefixCls:l,iconPrefixCls:r,theme:s},c.holderRender?c.holderRender(d):d)});function W(){if(!M){let a=document.createDocumentFragment(),t={fragment:a};M=t,T(()=>{(0,l.K)()(n.createElement(V,{ref:a=>{let{instance:e,sync:i}=a||{};Promise.resolve().then(()=>{!t.instance&&e&&(t.instance=e,t.sync=i,W())})}}),a)});return}M.instance&&(H.forEach(a=>{let{type:t,skipped:e}=a;if(!e)switch(t){case"open":T(()=>{let t=M.instance.open(Object.assign(Object.assign({},N),a.config));null==t||t.then(a.resolve),a.setCloseFn(t)});break;case"destroy":T(()=>{null==M||M.instance.destroy(a.key)});break;default:T(()=>{var e;let n=(e=M.instance)[t].apply(e,(0,i.A)(a.args));null==n||n.then(a.resolve),a.setCloseFn(n)})}}),H=[])}let K={open:function(a){let t=D(t=>{let e;let i={type:"open",config:a,resolve:t,setCloseFn:a=>{e=a}};return H.push(i),()=>{e?T(()=>{e()}):i.skipped=!0}});return W(),t},destroy:a=>{H.push({type:"destroy",key:a}),W()},config:function(a){N=Object.assign(Object.assign({},N),a),T(()=>{var a;null===(a=null==M?void 0:M.sync)||void 0===a||a.call(M)})},useMessage:function(a){return B(a)},_InternalPanelDoNotUseOrYouWillBeFired:a=>{let{prefixCls:t,className:e,type:i,icon:o,content:p}=a,l=z(a,["prefixCls","className","type","icon","content"]),{getPrefixCls:r}=n.useContext(c.QO),s=t||r("message"),d=(0,g.A)(s),[m,u,v]=O(s,d);return m(n.createElement(x.$T,Object.assign({},l,{prefixCls:s,className:f()(e,u,"".concat(s,"-notice-pure-panel"),v,d),eventKey:"pure",duration:null,content:n.createElement(E,{prefixCls:s,type:i,icon:o},p)})))}};["success","info","warning","error","loading"].forEach(a=>{K[a]=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(a,t){(0,p.cr)();let e=D(e=>{let i;let n={type:a,args:t,resolve:e,setCloseFn:a=>{i=a}};return H.push(n),()=>{i?T(()=>{i()}):n.skipped=!0}});return W(),e}(a,e)}});let $=K},89351:(a,t,e)=>{"use strict";e.d(t,{Ay:()=>M});var i=e(12115),n=e(4617),o=e.n(n),c=e(35015),p=e(97181),l=e(31049),r=e(7926),s=e(27651);let d=i.createContext(null),m=d.Provider,u=i.createContext(null),v=u.Provider;var f=e(37801),x=e(15231),g=e(71054),b=e(43144),h=e(83427),y=e(30033),k=e(30149),w=e(67548),j=e(70695),O=e(1086),z=e(56204);let C=a=>{let{componentCls:t,antCls:e}=a,i="".concat(t,"-group");return{[i]:Object.assign(Object.assign({},(0,j.dF)(a)),{display:"inline-block",fontSize:0,["&".concat(i,"-rtl")]:{direction:"rtl"},["&".concat(i,"-block")]:{display:"flex"},["".concat(e,"-badge ").concat(e,"-badge-count")]:{zIndex:1},["> ".concat(e,"-badge:not(:first-child) > ").concat(e,"-button-wrapper")]:{borderInlineStart:"none"}})}},E=a=>{let{componentCls:t,wrapperMarginInlineEnd:e,colorPrimary:i,radioSize:n,motionDurationSlow:o,motionDurationMid:c,motionEaseInOutCirc:p,colorBgContainer:l,colorBorder:r,lineWidth:s,colorBgContainerDisabled:d,colorTextDisabled:m,paddingXS:u,dotColorDisabled:v,lineType:f,radioColor:x,radioBgColor:g,calc:b}=a,h="".concat(t,"-inner"),y=b(n).sub(b(4).mul(2)),k=b(1).mul(n).equal({unit:!0});return{["".concat(t,"-wrapper")]:Object.assign(Object.assign({},(0,j.dF)(a)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:e,cursor:"pointer","&:last-child":{marginInlineEnd:0},["&".concat(t,"-wrapper-rtl")]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:a.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},["".concat(t,"-checked::after")]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:"".concat((0,w.zA)(s)," ").concat(f," ").concat(i),borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},(0,j.dF)(a)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),["".concat(t,"-wrapper:hover &,\n        &:hover ").concat(h)]:{borderColor:i},["".concat(t,"-input:focus-visible + ").concat(h)]:Object.assign({},(0,j.jk)(a)),["".concat(t,":hover::after, ").concat(t,"-wrapper:hover &::after")]:{visibility:"visible"},["".concat(t,"-inner")]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:k,height:k,marginBlockStart:b(1).mul(n).div(-2).equal({unit:!0}),marginInlineStart:b(1).mul(n).div(-2).equal({unit:!0}),backgroundColor:x,borderBlockStart:0,borderInlineStart:0,borderRadius:k,transform:"scale(0)",opacity:0,transition:"all ".concat(o," ").concat(p),content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:k,height:k,backgroundColor:l,borderColor:r,borderStyle:"solid",borderWidth:s,borderRadius:"50%",transition:"all ".concat(c)},["".concat(t,"-input")]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},["".concat(t,"-checked")]:{[h]:{borderColor:i,backgroundColor:g,"&::after":{transform:"scale(".concat(a.calc(a.dotSize).div(n).equal(),")"),opacity:1,transition:"all ".concat(o," ").concat(p)}}},["".concat(t,"-disabled")]:{cursor:"not-allowed",[h]:{backgroundColor:d,borderColor:r,cursor:"not-allowed","&::after":{backgroundColor:v}},["".concat(t,"-input")]:{cursor:"not-allowed"},["".concat(t,"-disabled + span")]:{color:m,cursor:"not-allowed"},["&".concat(t,"-checked")]:{[h]:{"&::after":{transform:"scale(".concat(b(y).div(n).equal(),")")}}}},["span".concat(t," + *")]:{paddingInlineStart:u,paddingInlineEnd:u}})}},S=a=>{let{buttonColor:t,controlHeight:e,componentCls:i,lineWidth:n,lineType:o,colorBorder:c,motionDurationSlow:p,motionDurationMid:l,buttonPaddingInline:r,fontSize:s,buttonBg:d,fontSizeLG:m,controlHeightLG:u,controlHeightSM:v,paddingXS:f,borderRadius:x,borderRadiusSM:g,borderRadiusLG:b,buttonCheckedBg:h,buttonSolidCheckedColor:y,colorTextDisabled:k,colorBgContainerDisabled:O,buttonCheckedBgDisabled:z,buttonCheckedColorDisabled:C,colorPrimary:E,colorPrimaryHover:S,colorPrimaryActive:A,buttonSolidCheckedBg:D,buttonSolidCheckedHoverBg:P,buttonSolidCheckedActiveBg:F,calc:q}=a;return{["".concat(i,"-button-wrapper")]:{position:"relative",display:"inline-block",height:e,margin:0,paddingInline:r,paddingBlock:0,color:t,fontSize:s,lineHeight:(0,w.zA)(q(e).sub(q(n).mul(2)).equal()),background:d,border:"".concat((0,w.zA)(n)," ").concat(o," ").concat(c),borderBlockStartWidth:q(n).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:n,cursor:"pointer",transition:["color ".concat(l),"background ".concat(l),"box-shadow ".concat(l)].join(","),a:{color:t},["> ".concat(i,"-button")]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:q(n).mul(-1).equal(),insetInlineStart:q(n).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:n,paddingInline:0,backgroundColor:c,transition:"background-color ".concat(p),content:'""'}},"&:first-child":{borderInlineStart:"".concat((0,w.zA)(n)," ").concat(o," ").concat(c),borderStartStartRadius:x,borderEndStartRadius:x},"&:last-child":{borderStartEndRadius:x,borderEndEndRadius:x},"&:first-child:last-child":{borderRadius:x},["".concat(i,"-group-large &")]:{height:u,fontSize:m,lineHeight:(0,w.zA)(q(u).sub(q(n).mul(2)).equal()),"&:first-child":{borderStartStartRadius:b,borderEndStartRadius:b},"&:last-child":{borderStartEndRadius:b,borderEndEndRadius:b}},["".concat(i,"-group-small &")]:{height:v,paddingInline:q(f).sub(n).equal(),paddingBlock:0,lineHeight:(0,w.zA)(q(v).sub(q(n).mul(2)).equal()),"&:first-child":{borderStartStartRadius:g,borderEndStartRadius:g},"&:last-child":{borderStartEndRadius:g,borderEndEndRadius:g}},"&:hover":{position:"relative",color:E},"&:has(:focus-visible)":Object.assign({},(0,j.jk)(a)),["".concat(i,"-inner, input[type='checkbox'], input[type='radio']")]:{width:0,height:0,opacity:0,pointerEvents:"none"},["&-checked:not(".concat(i,"-button-wrapper-disabled)")]:{zIndex:1,color:E,background:h,borderColor:E,"&::before":{backgroundColor:E},"&:first-child":{borderColor:E},"&:hover":{color:S,borderColor:S,"&::before":{backgroundColor:S}},"&:active":{color:A,borderColor:A,"&::before":{backgroundColor:A}}},["".concat(i,"-group-solid &-checked:not(").concat(i,"-button-wrapper-disabled)")]:{color:y,background:D,borderColor:D,"&:hover":{color:y,background:P,borderColor:P},"&:active":{color:y,background:F,borderColor:F}},"&-disabled":{color:k,backgroundColor:O,borderColor:c,cursor:"not-allowed","&:first-child, &:hover":{color:k,backgroundColor:O,borderColor:c}},["&-disabled".concat(i,"-button-wrapper-checked")]:{color:C,backgroundColor:z,borderColor:c,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},A=(0,O.OF)("Radio",a=>{let{controlOutline:t,controlOutlineWidth:e}=a,i="0 0 0 ".concat((0,w.zA)(e)," ").concat(t),n=(0,z.oX)(a,{radioFocusShadow:i,radioButtonFocusShadow:i});return[C(n),E(n),S(n)]},a=>{let{wireframe:t,padding:e,marginXS:i,lineWidth:n,fontSizeLG:o,colorText:c,colorBgContainer:p,colorTextDisabled:l,controlItemBgActiveDisabled:r,colorTextLightSolid:s,colorPrimary:d,colorPrimaryHover:m,colorPrimaryActive:u,colorWhite:v}=a;return{radioSize:o,dotSize:t?o-8:o-(4+n)*2,dotColorDisabled:l,buttonSolidCheckedColor:s,buttonSolidCheckedBg:d,buttonSolidCheckedHoverBg:m,buttonSolidCheckedActiveBg:u,buttonBg:p,buttonCheckedBg:p,buttonColor:c,buttonCheckedBgDisabled:r,buttonCheckedColorDisabled:l,buttonPaddingInline:e-n,wrapperMarginInlineEnd:i,radioColor:t?d:v,radioBgColor:t?p:d}},{unitless:{radioSize:!0,dotSize:!0}});var D=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let P=i.forwardRef((a,t)=>{var e,n;let c=i.useContext(d),p=i.useContext(u),{getPrefixCls:s,direction:m,radio:v}=i.useContext(l.QO),w=i.useRef(null),j=(0,x.K4)(t,w),{isFormItemInput:O}=i.useContext(k.$W),{prefixCls:z,className:C,rootClassName:E,children:S,style:P,title:F}=a,q=D(a,["prefixCls","className","rootClassName","children","style","title"]),I=s("radio",z),R="button"===((null==c?void 0:c.optionType)||p),B=R?"".concat(I,"-button"):I,M=(0,r.A)(I),[T,H,N]=A(I,M),_=Object.assign({},q),L=i.useContext(y.A);c&&(_.name=c.name,_.onChange=t=>{var e,i;null===(e=a.onChange)||void 0===e||e.call(a,t),null===(i=null==c?void 0:c.onChange)||void 0===i||i.call(c,t)},_.checked=a.value===c.value,_.disabled=null!==(e=_.disabled)&&void 0!==e?e:c.disabled),_.disabled=null!==(n=_.disabled)&&void 0!==n?n:L;let V=o()("".concat(B,"-wrapper"),{["".concat(B,"-wrapper-checked")]:_.checked,["".concat(B,"-wrapper-disabled")]:_.disabled,["".concat(B,"-wrapper-rtl")]:"rtl"===m,["".concat(B,"-wrapper-in-form-item")]:O,["".concat(B,"-wrapper-block")]:!!(null==c?void 0:c.block)},null==v?void 0:v.className,C,E,H,N,M),[W,K]=(0,h.A)(_.onClick);return T(i.createElement(g.A,{component:"Radio",disabled:_.disabled},i.createElement("label",{className:V,style:Object.assign(Object.assign({},null==v?void 0:v.style),P),onMouseEnter:a.onMouseEnter,onMouseLeave:a.onMouseLeave,title:F,onClick:W},i.createElement(f.A,Object.assign({},_,{className:o()(_.className,{[b.D]:!R}),type:"radio",prefixCls:B,ref:j,onClick:K})),void 0!==S?i.createElement("span",{className:"".concat(B,"-label")},S):null)))});var F=e(51335);let q=i.forwardRef((a,t)=>{let{getPrefixCls:e,direction:n}=i.useContext(l.QO),d=(0,F.A)(),{prefixCls:u,className:v,rootClassName:f,options:x,buttonStyle:g="outline",disabled:b,children:h,size:y,style:k,id:w,optionType:j,name:O=d,defaultValue:z,value:C,block:E=!1,onChange:S,onMouseEnter:D,onMouseLeave:q,onFocus:I,onBlur:R}=a,[B,M]=(0,c.A)(z,{value:C}),T=i.useCallback(t=>{let e=t.target.value;"value"in a||M(e),e!==B&&(null==S||S(t))},[B,M,S]),H=e("radio",u),N="".concat(H,"-group"),_=(0,r.A)(H),[L,V,W]=A(H,_),K=h;x&&x.length>0&&(K=x.map(a=>"string"==typeof a||"number"==typeof a?i.createElement(P,{key:a.toString(),prefixCls:H,disabled:b,value:a,checked:B===a},a):i.createElement(P,{key:"radio-group-value-options-".concat(a.value),prefixCls:H,disabled:a.disabled||b,value:a.value,checked:B===a.value,title:a.title,style:a.style,id:a.id,required:a.required},a.label)));let $=(0,s.A)(y),Q=o()(N,"".concat(N,"-").concat(g),{["".concat(N,"-").concat($)]:$,["".concat(N,"-rtl")]:"rtl"===n,["".concat(N,"-block")]:E},v,f,V,W,_),U=i.useMemo(()=>({onChange:T,value:B,disabled:b,name:O,optionType:j,block:E}),[T,B,b,O,j,E]);return L(i.createElement("div",Object.assign({},(0,p.A)(a,{aria:!0,data:!0}),{className:Q,style:k,onMouseEnter:D,onMouseLeave:q,onFocus:I,onBlur:R,id:w,ref:t}),i.createElement(m,{value:U},K)))}),I=i.memo(q);var R=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let B=i.forwardRef((a,t)=>{let{getPrefixCls:e}=i.useContext(l.QO),{prefixCls:n}=a,o=R(a,["prefixCls"]),c=e("radio",n);return i.createElement(v,{value:"button"},i.createElement(P,Object.assign({prefixCls:c},o,{type:"radio",ref:t})))});P.Button=B,P.Group=I,P.__ANT_RADIO=!0;let M=P},45100:(a,t,e)=>{"use strict";e.d(t,{A:()=>D});var i=e(12115),n=e(4617),o=e.n(n),c=e(70527),p=e(28673),l=e(64766),r=e(58292),s=e(71054),d=e(31049),m=e(67548),u=e(10815),v=e(70695),f=e(56204),x=e(1086);let g=a=>{let{paddingXXS:t,lineWidth:e,tagPaddingHorizontal:i,componentCls:n,calc:o}=a,c=o(i).sub(e).equal(),p=o(t).sub(e).equal();return{[n]:Object.assign(Object.assign({},(0,v.dF)(a)),{display:"inline-block",height:"auto",marginInlineEnd:a.marginXS,paddingInline:c,fontSize:a.tagFontSize,lineHeight:a.tagLineHeight,whiteSpace:"nowrap",background:a.defaultBg,border:"".concat((0,m.zA)(a.lineWidth)," ").concat(a.lineType," ").concat(a.colorBorder),borderRadius:a.borderRadiusSM,opacity:1,transition:"all ".concat(a.motionDurationMid),textAlign:"start",position:"relative",["&".concat(n,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:a.defaultColor},["".concat(n,"-close-icon")]:{marginInlineStart:p,fontSize:a.tagIconSize,color:a.colorTextDescription,cursor:"pointer",transition:"all ".concat(a.motionDurationMid),"&:hover":{color:a.colorTextHeading}},["&".concat(n,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(a.iconCls,"-close, ").concat(a.iconCls,"-close:hover")]:{color:a.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(n,"-checkable-checked):hover")]:{color:a.colorPrimary,backgroundColor:a.colorFillSecondary},"&:active, &-checked":{color:a.colorTextLightSolid},"&-checked":{backgroundColor:a.colorPrimary,"&:hover":{backgroundColor:a.colorPrimaryHover}},"&:active":{backgroundColor:a.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(a.iconCls," + span, > span + ").concat(a.iconCls)]:{marginInlineStart:c}}),["".concat(n,"-borderless")]:{borderColor:"transparent",background:a.tagBorderlessBg}}},b=a=>{let{lineWidth:t,fontSizeIcon:e,calc:i}=a,n=a.fontSizeSM;return(0,f.oX)(a,{tagFontSize:n,tagLineHeight:(0,m.zA)(i(a.lineHeightSM).mul(n).equal()),tagIconSize:i(e).sub(i(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:a.defaultBg})},h=a=>({defaultBg:new u.Y(a.colorFillQuaternary).onBackground(a.colorBgContainer).toHexString(),defaultColor:a.colorText}),y=(0,x.OF)("Tag",a=>g(b(a)),h);var k=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let w=i.forwardRef((a,t)=>{let{prefixCls:e,style:n,className:c,checked:p,onChange:l,onClick:r}=a,s=k(a,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:u}=i.useContext(d.QO),v=m("tag",e),[f,x,g]=y(v),b=o()(v,"".concat(v,"-checkable"),{["".concat(v,"-checkable-checked")]:p},null==u?void 0:u.className,c,x,g);return f(i.createElement("span",Object.assign({},s,{ref:t,style:Object.assign(Object.assign({},n),null==u?void 0:u.style),className:b,onClick:a=>{null==l||l(!p),null==r||r(a)}})))});var j=e(46258);let O=a=>(0,j.A)(a,(t,e)=>{let{textColor:i,lightBorderColor:n,lightColor:o,darkColor:c}=e;return{["".concat(a.componentCls).concat(a.componentCls,"-").concat(t)]:{color:i,background:o,borderColor:n,"&-inverse":{color:a.colorTextLightSolid,background:c,borderColor:c},["&".concat(a.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),z=(0,x.bf)(["Tag","preset"],a=>O(b(a)),h),C=(a,t,e)=>{let i=function(a){return"string"!=typeof a?a:a.charAt(0).toUpperCase()+a.slice(1)}(e);return{["".concat(a.componentCls).concat(a.componentCls,"-").concat(t)]:{color:a["color".concat(e)],background:a["color".concat(i,"Bg")],borderColor:a["color".concat(i,"Border")],["&".concat(a.componentCls,"-borderless")]:{borderColor:"transparent"}}}},E=(0,x.bf)(["Tag","status"],a=>{let t=b(a);return[C(t,"success","Success"),C(t,"processing","Info"),C(t,"error","Error"),C(t,"warning","Warning")]},h);var S=function(a,t){var e={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&0>t.indexOf(i)&&(e[i]=a[i]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(a);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(a,i[n])&&(e[i[n]]=a[i[n]]);return e};let A=i.forwardRef((a,t)=>{let{prefixCls:e,className:n,rootClassName:m,style:u,children:v,icon:f,color:x,onClose:g,bordered:b=!0,visible:h}=a,k=S(a,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:j,tag:O}=i.useContext(d.QO),[C,A]=i.useState(!0),D=(0,c.A)(k,["closeIcon","closable"]);i.useEffect(()=>{void 0!==h&&A(h)},[h]);let P=(0,p.nP)(x),F=(0,p.ZZ)(x),q=P||F,I=Object.assign(Object.assign({backgroundColor:x&&!q?x:void 0},null==O?void 0:O.style),u),R=w("tag",e),[B,M,T]=y(R),H=o()(R,null==O?void 0:O.className,{["".concat(R,"-").concat(x)]:q,["".concat(R,"-has-color")]:x&&!q,["".concat(R,"-hidden")]:!C,["".concat(R,"-rtl")]:"rtl"===j,["".concat(R,"-borderless")]:!b},n,m,M,T),N=a=>{a.stopPropagation(),null==g||g(a),a.defaultPrevented||A(!1)},[,_]=(0,l.A)((0,l.d)(a),(0,l.d)(O),{closable:!1,closeIconRender:a=>{let t=i.createElement("span",{className:"".concat(R,"-close-icon"),onClick:N},a);return(0,r.fx)(a,t,a=>({onClick:t=>{var e;null===(e=null==a?void 0:a.onClick)||void 0===e||e.call(a,t),N(t)},className:o()(null==a?void 0:a.className,"".concat(R,"-close-icon"))}))}}),L="function"==typeof k.onClick||v&&"a"===v.type,V=f||null,W=V?i.createElement(i.Fragment,null,V,v&&i.createElement("span",null,v)):v,K=i.createElement("span",Object.assign({},D,{ref:t,className:H,style:I}),W,_,P&&i.createElement(z,{key:"preset",prefixCls:R}),F&&i.createElement(E,{key:"status",prefixCls:R}));return B(L?i.createElement(s.A,{component:"Tag"},K):K)});A.CheckableTag=w;let D=A},27685:(a,t)=>{"use strict";t.__esModule=!0,t.default=function(a,t){if(a&&t){var e=Array.isArray(t)?t:t.split(",");if(0===e.length)return!0;var i=a.name||"",n=(a.type||"").toLowerCase(),o=n.replace(/\/.*$/,"");return e.some(function(a){var t=a.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):n===t})}return!0}},65192:(a,t,e)=>{"use strict";var i=e(80859);function n(){}function o(){}o.resetWarningCache=n,a.exports=function(){function a(a,t,e,n,o,c){if(c!==i){var p=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}function t(){return a}a.isRequired=a;var e={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:t,element:a,elementType:a,instanceOf:t,node:a,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return e.PropTypes=e,e}},81996:(a,t,e)=>{a.exports=e(65192)()},80859:a=>{"use strict";a.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},46702:(a,t,e)=>{"use strict";e.d(t,{VB:()=>J});var i=e(12115),n=e(81996);function o(a,t,e,i){return new(e||(e=Promise))(function(n,o){function c(a){try{l(i.next(a))}catch(a){o(a)}}function p(a){try{l(i.throw(a))}catch(a){o(a)}}function l(a){var t;a.done?n(a.value):((t=a.value)instanceof e?t:new e(function(a){a(t)})).then(c,p)}l((i=i.apply(a,t||[])).next())})}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;let c=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function p(a,t,e){let i=function(a){let{name:t}=a;if(t&&-1!==t.lastIndexOf(".")&&!a.type){let e=t.split(".").pop().toLowerCase(),i=c.get(e);i&&Object.defineProperty(a,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return a}(a),{webkitRelativePath:n}=a,o="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${a.name}`;return"string"!=typeof i.path&&l(i,"path",o),void 0!==e&&Object.defineProperty(i,"handle",{value:e,writable:!1,configurable:!1,enumerable:!0}),l(i,"relativePath",o),i}function l(a,t,e){Object.defineProperty(a,t,{value:e,writable:!1,configurable:!1,enumerable:!0})}let r=[".DS_Store","Thumbs.db"];function s(a){return"object"==typeof a&&null!==a}function d(a){return a.filter(a=>-1===r.indexOf(a.name))}function m(a){if(null===a)return[];let t=[];for(let e=0;e<a.length;e++){let i=a[e];t.push(i)}return t}function u(a){if("function"!=typeof a.webkitGetAsEntry)return v(a);let t=a.webkitGetAsEntry();return t&&t.isDirectory?x(t):v(a,t)}function v(a,t){return o(this,void 0,void 0,function*(){var e;if(globalThis.isSecureContext&&"function"==typeof a.getAsFileSystemHandle){let t=yield a.getAsFileSystemHandle();if(null===t)throw Error(`${a} is not a File`);if(void 0!==t){let a=yield t.getFile();return a.handle=t,p(a)}}let i=a.getAsFile();if(!i)throw Error(`${a} is not a File`);return p(i,null!==(e=null==t?void 0:t.fullPath)&&void 0!==e?e:void 0)})}function f(a){return o(this,void 0,void 0,function*(){return a.isDirectory?x(a):function(a){return o(this,void 0,void 0,function*(){return new Promise((t,e)=>{a.file(e=>{t(p(e,a.fullPath))},a=>{e(a)})})})}(a)})}function x(a){let t=a.createReader();return new Promise((a,e)=>{let i=[];!function n(){t.readEntries(t=>o(this,void 0,void 0,function*(){if(t.length){let a=Promise.all(t.map(f));i.push(a),n()}else try{let t=yield Promise.all(i);a(t)}catch(a){e(a)}}),a=>{e(a)})}()})}var g=e(27685);function b(a){return function(a){if(Array.isArray(a))return O(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||j(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(a,t){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(a,t).enumerable})),e.push.apply(e,i)}return e}function y(a){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?h(Object(e),!0).forEach(function(t){k(a,t,e[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):h(Object(e)).forEach(function(t){Object.defineProperty(a,t,Object.getOwnPropertyDescriptor(e,t))})}return a}function k(a,t,e){return t in a?Object.defineProperty(a,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[t]=e,a}function w(a,t){return function(a){if(Array.isArray(a))return a}(a)||function(a,t){var e,i,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var o=[],c=!0,p=!1;try{for(n=n.call(a);!(c=(e=n.next()).done)&&(o.push(e.value),!t||o.length!==t);c=!0);}catch(a){p=!0,i=a}finally{try{c||null==n.return||n.return()}finally{if(p)throw i}}return o}}(a,t)||j(a,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(a,t){if(a){if("string"==typeof a)return O(a,t);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return O(a,t)}}function O(a,t){(null==t||t>a.length)&&(t=a.length);for(var e=0,i=Array(t);e<t;e++)i[e]=a[e];return i}var z="function"==typeof g?g:g.default,C=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=a.split(","),e=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(e)}},E=function(a){return{code:"file-too-large",message:"File is larger than ".concat(a," ").concat(1===a?"byte":"bytes")}},S=function(a){return{code:"file-too-small",message:"File is smaller than ".concat(a," ").concat(1===a?"byte":"bytes")}},A={code:"too-many-files",message:"Too many files"};function D(a,t){var e="application/x-moz-file"===a.type||z(a,t);return[e,e?null:C(t)]}function P(a,t,e){if(F(a.size)){if(F(t)&&F(e)){if(a.size>e)return[!1,E(e)];if(a.size<t)return[!1,S(t)]}else if(F(t)&&a.size<t)return[!1,S(t)];else if(F(e)&&a.size>e)return[!1,E(e)]}return[!0,null]}function F(a){return null!=a}function q(a){return"function"==typeof a.isPropagationStopped?a.isPropagationStopped():void 0!==a.cancelBubble&&a.cancelBubble}function I(a){return a.dataTransfer?Array.prototype.some.call(a.dataTransfer.types,function(a){return"Files"===a||"application/x-moz-file"===a}):!!a.target&&!!a.target.files}function R(a){a.preventDefault()}function B(){for(var a=arguments.length,t=Array(a),e=0;e<a;e++)t[e]=arguments[e];return function(a){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return t.some(function(t){return!q(a)&&t&&t.apply(void 0,[a].concat(i)),q(a)})}}function M(a){return"audio/*"===a||"video/*"===a||"image/*"===a||"text/*"===a||"application/*"===a||/\w+\/[-+.\w]+/g.test(a)}function T(a){return/^.*\.[\w]+$/.test(a)}var H=["children"],N=["open"],_=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],L=["refKey","onChange","onClick"];function V(a,t){return function(a){if(Array.isArray(a))return a}(a)||function(a,t){var e,i,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var o=[],c=!0,p=!1;try{for(n=n.call(a);!(c=(e=n.next()).done)&&(o.push(e.value),!t||o.length!==t);c=!0);}catch(a){p=!0,i=a}finally{try{c||null==n.return||n.return()}finally{if(p)throw i}}return o}}(a,t)||W(a,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(a,t){if(a){if("string"==typeof a)return K(a,t);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return K(a,t)}}function K(a,t){(null==t||t>a.length)&&(t=a.length);for(var e=0,i=Array(t);e<t;e++)i[e]=a[e];return i}function $(a,t){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(a,t).enumerable})),e.push.apply(e,i)}return e}function Q(a){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?$(Object(e),!0).forEach(function(t){U(a,t,e[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):$(Object(e)).forEach(function(t){Object.defineProperty(a,t,Object.getOwnPropertyDescriptor(e,t))})}return a}function U(a,t,e){return t in a?Object.defineProperty(a,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[t]=e,a}function Y(a,t){if(null==a)return{};var e,i,n=function(a,t){if(null==a)return{};var e,i,n={},o=Object.keys(a);for(i=0;i<o.length;i++)e=o[i],t.indexOf(e)>=0||(n[e]=a[e]);return n}(a,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(a);for(i=0;i<o.length;i++)e=o[i],!(t.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(a,e)&&(n[e]=a[e])}return n}var X=(0,i.forwardRef)(function(a,t){var e=a.children,n=J(Y(a,H)),o=n.open,c=Y(n,N);return(0,i.useImperativeHandle)(t,function(){return{open:o}},[o]),i.createElement(i.Fragment,null,e(Q(Q({},c),{},{open:o})))});X.displayName="Dropzone";var G={disabled:!1,getFilesFromEvent:function(a){return o(this,void 0,void 0,function*(){return s(a)&&s(a.dataTransfer)?function(a,t){return o(this,void 0,void 0,function*(){if(a.items){let e=m(a.items).filter(a=>"file"===a.kind);return"drop"!==t?e:d(function a(t){return t.reduce((t,e)=>[...t,...Array.isArray(e)?a(e):[e]],[])}((yield Promise.all(e.map(u)))))}return d(m(a.files).map(a=>p(a)))})}(a.dataTransfer,a.type):s(a)&&s(a.target)?m(a.target.files).map(a=>p(a)):Array.isArray(a)&&a.every(a=>"getFile"in a&&"function"==typeof a.getFile)?function(a){return o(this,void 0,void 0,function*(){return(yield Promise.all(a.map(a=>a.getFile()))).map(a=>p(a))})}(a):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};X.defaultProps=G,X.propTypes={children:n.func,accept:n.objectOf(n.arrayOf(n.string)),multiple:n.bool,preventDropOnDocument:n.bool,noClick:n.bool,noKeyboard:n.bool,noDrag:n.bool,noDragEventsBubbling:n.bool,minSize:n.number,maxSize:n.number,maxFiles:n.number,disabled:n.bool,getFilesFromEvent:n.func,onFileDialogCancel:n.func,onFileDialogOpen:n.func,useFsAccessApi:n.bool,autoFocus:n.bool,onDragEnter:n.func,onDragLeave:n.func,onDragOver:n.func,onDrop:n.func,onDropAccepted:n.func,onDropRejected:n.func,onError:n.func,validator:n.func};var Z={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function J(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Q(Q({},G),a),e=t.accept,n=t.disabled,o=t.getFilesFromEvent,c=t.maxSize,p=t.minSize,l=t.multiple,r=t.maxFiles,s=t.onDragEnter,d=t.onDragLeave,m=t.onDragOver,u=t.onDrop,v=t.onDropAccepted,f=t.onDropRejected,x=t.onFileDialogCancel,g=t.onFileDialogOpen,h=t.useFsAccessApi,j=t.autoFocus,O=t.preventDropOnDocument,z=t.noClick,C=t.noKeyboard,E=t.noDrag,S=t.noDragEventsBubbling,H=t.onError,N=t.validator,$=(0,i.useMemo)(function(){return function(a){if(F(a))return Object.entries(a).reduce(function(a,t){var e=w(t,2),i=e[0],n=e[1];return[].concat(b(a),[i],b(n))},[]).filter(function(a){return M(a)||T(a)}).join(",")}(e)},[e]),X=(0,i.useMemo)(function(){return F(e)?[{description:"Files",accept:Object.entries(e).filter(function(a){var t=w(a,2),e=t[0],i=t[1],n=!0;return M(e)||(console.warn('Skipped "'.concat(e,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(T)||(console.warn('Skipped "'.concat(e,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(a,t){var e=w(t,2),i=e[0],n=e[1];return y(y({},a),{},k({},i,n))},{})}]:e},[e]),J=(0,i.useMemo)(function(){return"function"==typeof g?g:at},[g]),ae=(0,i.useMemo)(function(){return"function"==typeof x?x:at},[x]),ai=(0,i.useRef)(null),an=(0,i.useRef)(null),ao=V((0,i.useReducer)(aa,Z),2),ac=ao[0],ap=ao[1],al=ac.isFocused,ar=ac.isFileDialogActive,as=(0,i.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),ad=function(){!as.current&&ar&&setTimeout(function(){an.current&&!an.current.files.length&&(ap({type:"closeDialog"}),ae())},300)};(0,i.useEffect)(function(){return window.addEventListener("focus",ad,!1),function(){window.removeEventListener("focus",ad,!1)}},[an,ar,ae,as]);var am=(0,i.useRef)([]),au=function(a){ai.current&&ai.current.contains(a.target)||(a.preventDefault(),am.current=[])};(0,i.useEffect)(function(){return O&&(document.addEventListener("dragover",R,!1),document.addEventListener("drop",au,!1)),function(){O&&(document.removeEventListener("dragover",R),document.removeEventListener("drop",au))}},[ai,O]),(0,i.useEffect)(function(){return!n&&j&&ai.current&&ai.current.focus(),function(){}},[ai,j,n]);var av=(0,i.useCallback)(function(a){H?H(a):console.error(a)},[H]),af=(0,i.useCallback)(function(a){var t;a.preventDefault(),a.persist(),aS(a),am.current=[].concat(function(a){if(Array.isArray(a))return K(a)}(t=am.current)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(t)||W(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[a.target]),I(a)&&Promise.resolve(o(a)).then(function(t){if(!q(a)||S){var e,i,n,o,d,m,u,v,f=t.length,x=f>0&&(i=(e={files:t,accept:$,minSize:p,maxSize:c,multiple:l,maxFiles:r,validator:N}).files,n=e.accept,o=e.minSize,d=e.maxSize,m=e.multiple,u=e.maxFiles,v=e.validator,(!!m||!(i.length>1))&&(!m||!(u>=1)||!(i.length>u))&&i.every(function(a){var t=w(D(a,n),1)[0],e=w(P(a,o,d),1)[0],i=v?v(a):null;return t&&e&&!i}));ap({isDragAccept:x,isDragReject:f>0&&!x,isDragActive:!0,type:"setDraggedFiles"}),s&&s(a)}}).catch(function(a){return av(a)})},[o,s,av,S,$,p,c,l,r,N]),ax=(0,i.useCallback)(function(a){a.preventDefault(),a.persist(),aS(a);var t=I(a);if(t&&a.dataTransfer)try{a.dataTransfer.dropEffect="copy"}catch(a){}return t&&m&&m(a),!1},[m,S]),ag=(0,i.useCallback)(function(a){a.preventDefault(),a.persist(),aS(a);var t=am.current.filter(function(a){return ai.current&&ai.current.contains(a)}),e=t.indexOf(a.target);-1!==e&&t.splice(e,1),am.current=t,!(t.length>0)&&(ap({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),I(a)&&d&&d(a))},[ai,d,S]),ab=(0,i.useCallback)(function(a,t){var e=[],i=[];a.forEach(function(a){var t=V(D(a,$),2),n=t[0],o=t[1],l=V(P(a,p,c),2),r=l[0],s=l[1],d=N?N(a):null;if(n&&r&&!d)e.push(a);else{var m=[o,s];d&&(m=m.concat(d)),i.push({file:a,errors:m.filter(function(a){return a})})}}),(!l&&e.length>1||l&&r>=1&&e.length>r)&&(e.forEach(function(a){i.push({file:a,errors:[A]})}),e.splice(0)),ap({acceptedFiles:e,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),u&&u(e,i,t),i.length>0&&f&&f(i,t),e.length>0&&v&&v(e,t)},[ap,l,$,p,c,r,u,v,f,N]),ah=(0,i.useCallback)(function(a){a.preventDefault(),a.persist(),aS(a),am.current=[],I(a)&&Promise.resolve(o(a)).then(function(t){(!q(a)||S)&&ab(t,a)}).catch(function(a){return av(a)}),ap({type:"reset"})},[o,ab,av,S]),ay=(0,i.useCallback)(function(){if(as.current){ap({type:"openDialog"}),J(),window.showOpenFilePicker({multiple:l,types:X}).then(function(a){return o(a)}).then(function(a){ab(a,null),ap({type:"closeDialog"})}).catch(function(a){a instanceof DOMException&&("AbortError"===a.name||a.code===a.ABORT_ERR)?(ae(a),ap({type:"closeDialog"})):a instanceof DOMException&&("SecurityError"===a.name||a.code===a.SECURITY_ERR)?(as.current=!1,an.current?(an.current.value=null,an.current.click()):av(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):av(a)});return}an.current&&(ap({type:"openDialog"}),J(),an.current.value=null,an.current.click())},[ap,J,ae,h,ab,av,X,l]),ak=(0,i.useCallback)(function(a){ai.current&&ai.current.isEqualNode(a.target)&&(" "===a.key||"Enter"===a.key||32===a.keyCode||13===a.keyCode)&&(a.preventDefault(),ay())},[ai,ay]),aw=(0,i.useCallback)(function(){ap({type:"focus"})},[]),aj=(0,i.useCallback)(function(){ap({type:"blur"})},[]),aO=(0,i.useCallback)(function(){z||(function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==a.indexOf("MSIE")||-1!==a.indexOf("Trident/")||-1!==a.indexOf("Edge/")}()?setTimeout(ay,0):ay())},[z,ay]),az=function(a){return n?null:a},aC=function(a){return C?null:az(a)},aE=function(a){return E?null:az(a)},aS=function(a){S&&a.stopPropagation()},aA=(0,i.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=a.refKey,e=a.role,i=a.onKeyDown,o=a.onFocus,c=a.onBlur,p=a.onClick,l=a.onDragEnter,r=a.onDragOver,s=a.onDragLeave,d=a.onDrop,m=Y(a,_);return Q(Q(U({onKeyDown:aC(B(i,ak)),onFocus:aC(B(o,aw)),onBlur:aC(B(c,aj)),onClick:az(B(p,aO)),onDragEnter:aE(B(l,af)),onDragOver:aE(B(r,ax)),onDragLeave:aE(B(s,ag)),onDrop:aE(B(d,ah)),role:"string"==typeof e&&""!==e?e:"presentation"},void 0===t?"ref":t,ai),n||C?{}:{tabIndex:0}),m)}},[ai,ak,aw,aj,aO,af,ax,ag,ah,C,E,n]),aD=(0,i.useCallback)(function(a){a.stopPropagation()},[]),aP=(0,i.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=a.refKey,e=a.onChange,i=a.onClick,n=Y(a,L);return Q(Q({},U({accept:$,multiple:l,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:az(B(e,ah)),onClick:az(B(i,aD)),tabIndex:-1},void 0===t?"ref":t,an)),n)}},[an,e,l,ah,n]);return Q(Q({},ac),{},{isFocused:al&&!n,getRootProps:aA,getInputProps:aP,rootRef:ai,inputRef:an,open:az(ay)})}function aa(a,t){switch(t.type){case"focus":return Q(Q({},a),{},{isFocused:!0});case"blur":return Q(Q({},a),{},{isFocused:!1});case"openDialog":return Q(Q({},Z),{},{isFileDialogActive:!0});case"closeDialog":return Q(Q({},a),{},{isFileDialogActive:!1});case"setDraggedFiles":return Q(Q({},a),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return Q(Q({},a),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return Q({},Z);default:return a}}function at(){}}}]);