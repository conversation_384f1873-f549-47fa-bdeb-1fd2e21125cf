"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSupplierById = exports.updateSupplierById = exports.getSupplierById = exports.getAllSuppliers = exports.createSupplier = void 0;
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const authorizeAction_1 = require("../utils/authorizeAction");
const schema_2 = require("../validation/schema");
// ✅ **Create Supplier**
const createSupplier = async (requester, supplierData) => {
    schema_2.supplierSchema.parse(supplierData);
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "supplier");
    const createdBy = requester.id;
    // 🔹 Check if the supplier already exists by phone
    const existingSupplier = await db_1.postgresDb
        .select()
        .from(schema_1.suppliers)
        .where((0, drizzle_orm_1.eq)(schema_1.suppliers.phone, supplierData.phone))
        .limit(1);
    // 🔹 If email is provided, also check if it already exists
    if (supplierData.email && supplierData.email.trim() !== '') {
        try {
            const existingSupplierByEmail = await db_1.postgresDb
                .select()
                .from(schema_1.suppliers)
                .where((0, drizzle_orm_1.eq)(schema_1.suppliers.email, supplierData.email))
                .limit(1);
            if (existingSupplierByEmail.length > 0) {
                throw new Error("A supplier with this email already exists.");
            }
        }
        catch (error) {
            console.error("Error checking for duplicate supplier email:", error);
            // Continue with creation even if there's an error checking for duplicates
        }
    }
    if (existingSupplier.length > 0) {
        const supplier = existingSupplier[0];
        // 🔹 Check if the requester is already linked to this supplier
        const userSupplierLink = await db_1.postgresDb
            .select()
            .from(schema_1.userSuppliers)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userSuppliers.userId, createdBy), (0, drizzle_orm_1.eq)(schema_1.userSuppliers.supplierId, supplier.id)))
            .limit(1);
        if (userSupplierLink.length > 0) {
            throw new Error("You are already linked to this supplier.");
        }
        // 🔹 If supplier exists but user is not linked, create a new link
        await db_1.postgresDb.insert(schema_1.userSuppliers).values({
            userId: createdBy,
            supplierId: supplier.id,
            createdBy,
            createdAt: new Date(),
        });
        return {
            message: "Supplier already exists. You have been linked to this supplier.",
            supplier,
        };
    }
    // 🔹 Create a new supplier if it does not exist
    const [newSupplier] = await db_1.postgresDb
        .insert(schema_1.suppliers)
        .values({
        ...supplierData,
        createdBy,
        createdAt: new Date(),
    })
        .returning();
    if (!newSupplier)
        throw new Error("Supplier creation failed.");
    // 🔹 Link the new supplier to the creator
    await db_1.postgresDb.insert(schema_1.userSuppliers).values({
        userId: createdBy,
        supplierId: newSupplier.id,
        createdBy,
        createdAt: new Date(),
    });
    return {
        message: "Supplier created successfully.",
        supplier: newSupplier,
    };
};
exports.createSupplier = createSupplier;
// ✅ **Get All Suppliers**
const getAllSuppliers = async (requester, page = 1, limit = 10) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "supplier");
    const offset = (page - 1) * limit;
    // ✅ Count total suppliers for pagination
    let totalQuery;
    if (requester.role === "superadmin") {
        totalQuery = db_1.postgresDb.select({ count: (0, drizzle_orm_1.count)() }).from(schema_1.suppliers); // ✅ Count all suppliers
    }
    else {
        totalQuery = db_1.postgresDb
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.userSuppliers)
            .where((0, drizzle_orm_1.eq)(schema_1.userSuppliers.userId, requester.id));
    }
    const totalResult = await totalQuery;
    const total = totalResult[0]?.count ?? 0; // ✅ Avoid "Property 'total' does not exist" error
    // ✅ Fetch suppliers linked to the user (or all if superadmin)
    const supplierQuery = db_1.postgresDb
        .select({
        id: schema_1.suppliers.id,
        name: schema_1.suppliers.name,
        contactPerson: schema_1.suppliers.contactPerson,
        phone: schema_1.suppliers.phone,
        email: schema_1.suppliers.email,
        address: schema_1.suppliers.address,
        createdAt: schema_1.suppliers.createdAt,
    })
        .from(schema_1.suppliers)
        .innerJoin(schema_1.userSuppliers, (0, drizzle_orm_1.eq)(schema_1.suppliers.id, schema_1.userSuppliers.supplierId))
        .orderBy((0, drizzle_orm_1.desc)(schema_1.suppliers.createdAt))
        .limit(limit)
        .offset(offset);
    if (requester.role !== "superadmin") {
        supplierQuery.where((0, drizzle_orm_1.eq)(schema_1.userSuppliers.userId, requester.id));
    }
    const supplierData = await supplierQuery;
    return {
        total,
        page,
        perPage: limit,
        suppliers: supplierData,
    };
};
exports.getAllSuppliers = getAllSuppliers;
// ✅ **Check Supplier Ownership (Utility Function)**
const isSupplierLinkedToUser = async (userId, supplierId) => {
    const result = await db_1.postgresDb
        .select({
        exists: (0, drizzle_orm_1.sql) `EXISTS (
        SELECT 1 FROM ${schema_1.userSuppliers}
        WHERE ${schema_1.userSuppliers.userId} = ${userId}
        AND ${schema_1.userSuppliers.supplierId} = ${supplierId}
      )`,
    })
        .from(schema_1.userSuppliers);
    return result[0]?.exists ?? false; // ✅ Ensures it returns a boolean
};
// ✅ **Get Supplier by ID**
const getSupplierById = async (requester, id) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "supplier", id);
    // 🔹 Check if the supplier is linked to the user
    const linked = await isSupplierLinkedToUser(requester.id, id);
    if (!linked)
        throw new Error("Unauthorized: Supplier not linked to you.");
    const supplierData = await db_1.postgresDb
        .select()
        .from(schema_1.suppliers)
        .where((0, drizzle_orm_1.eq)(schema_1.suppliers.id, id))
        .limit(1);
    if (supplierData.length === 0)
        throw new Error("Supplier not found.");
    return supplierData[0];
};
exports.getSupplierById = getSupplierById;
// ✅ **Update Supplier**
const updateSupplierById = async (requester, supplierId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "supplier", supplierId);
    const userId = requester.id;
    // 🔹 Check if the supplier is already linked to the user
    const linkedSupplier = await db_1.postgresDb
        .select()
        .from(schema_1.userSuppliers)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userSuppliers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userSuppliers.supplierId, supplierId)))
        .limit(1);
    if (linkedSupplier.length === 0) {
        throw new Error("Unauthorized: Supplier not linked to you.");
    }
    // 🔹 Build the conditions for checking existing suppliers
    const conditions = [];
    // Add phone condition if phone is being updated
    if (updateData.phone) {
        conditions.push((0, drizzle_orm_1.eq)(schema_1.suppliers.phone, updateData.phone));
    }
    // Add email condition if email is being updated and is not empty
    if (updateData.email && updateData.email.trim() !== '') {
        conditions.push((0, drizzle_orm_1.eq)(schema_1.suppliers.email, updateData.email));
    }
    // Initialize existingSupplier with the correct type
    let existingSupplier = [];
    // Only check for duplicates if we have conditions to check
    if (conditions.length > 0) {
        try {
            // Check if the updated phone or email already exists in another supplier
            existingSupplier = await db_1.postgresDb
                .select()
                .from(schema_1.suppliers)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.or)(...conditions), 
            // Exclude the current supplier
            (0, drizzle_orm_1.ne)(schema_1.suppliers.id, supplierId)))
                .limit(1);
        }
        catch (error) {
            console.error("Error checking for duplicate suppliers:", error);
            // If there's an error, assume no duplicates
            existingSupplier = [];
        }
    }
    if (existingSupplier.length > 0) {
        const newSupplier = existingSupplier[0];
        // 🔹 Check if user is already linked to the existing supplier
        const existingLink = await db_1.postgresDb
            .select()
            .from(schema_1.userSuppliers)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.userSuppliers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.userSuppliers.supplierId, newSupplier.id)))
            .limit(1);
        if (existingLink.length > 0) {
            return {
                message: "You are already linked to this supplier.",
                supplier: newSupplier,
            };
        }
        // 🔹 Link the user to the existing supplier instead of updating
        await db_1.postgresDb.insert(schema_1.userSuppliers).values({
            userId,
            supplierId: newSupplier.id,
            createdBy: userId,
            createdAt: new Date(),
        });
        return {
            message: "Supplier already exists. You have been linked to this supplier.",
            supplier: newSupplier,
        };
    }
    // 🔹 Update the supplier if no duplicate was found
    const updatedSupplier = await db_1.postgresDb
        .update(schema_1.suppliers)
        .set({
        ...updateData,
    })
        .where((0, drizzle_orm_1.eq)(schema_1.suppliers.id, supplierId))
        .returning();
    if (!updatedSupplier || updatedSupplier.length === 0) {
        throw new Error("Update failed: Supplier not found.");
    }
    return {
        message: "Supplier updated successfully.",
        supplier: updatedSupplier[0],
    };
};
exports.updateSupplierById = updateSupplierById;
// ✅ **Delete Suppliers (Single or Multiple)**
const deleteSupplierById = async (requester, ids) => {
    const supplierIds = Array.isArray(ids) ? ids : [ids];
    // Check authorization and link for each supplier
    for (const id of supplierIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "supplier", id);
        // 🔹 Check if the supplier is linked to the user
        const linked = await isSupplierLinkedToUser(requester.id, id);
        if (!linked)
            throw new Error(`Unauthorized: Supplier with ID ${id} not linked to you.`);
    }
    const deletedSuppliers = await db_1.postgresDb
        .delete(schema_1.suppliers)
        .where((0, drizzle_orm_1.inArray)(schema_1.suppliers.id, supplierIds))
        .returning({ deletedId: schema_1.suppliers.id });
    if (!deletedSuppliers || deletedSuppliers.length === 0) {
        throw new Error("Delete failed: Suppliers not found.");
    }
    return {
        message: supplierIds.length > 1 ? "Suppliers deleted successfully" : "Supplier deleted successfully",
        deletedIds: deletedSuppliers.map(supplier => supplier.deletedId),
    };
};
exports.deleteSupplierById = deleteSupplierById;
