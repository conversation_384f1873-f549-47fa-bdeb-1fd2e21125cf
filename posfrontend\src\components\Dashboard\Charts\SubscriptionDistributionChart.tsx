"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface SubscriptionDistributionChartProps {
  users: any[];
}

const SubscriptionDistributionChart: React.FC<SubscriptionDistributionChartProps> = ({ users }) => {
  const chartData = useMemo(() => {
    // Filter admin users with payment data
    const adminUsers = users.filter(user =>
      user.role === 'admin' &&
      user.paymentStatus === 'paid' &&
      user.lastPaymentDate &&
      user.nextPaymentDue
    );

    let monthly = 0;
    let quarterly = 0;
    let annual = 0;

    adminUsers.forEach(user => {
      const lastPayment = dayjs(user.lastPaymentDate);
      const nextDue = dayjs(user.nextPaymentDue);
      const daysDiff = nextDue.diff(lastPayment, 'day');

      if (daysDiff >= 350) {
        annual++;
      } else if (daysDiff >= 80) {
        quarterly++;
      } else {
        monthly++;
      }
    });

    return {
      labels: ['Monthly', 'Quarterly', 'Annual'],
      series: [monthly, quarterly, annual],
      total: monthly + quarterly + annual
    };
  }, [users]);

  const options: ApexOptions = {
    chart: {
      type: 'donut',
      height: 300,
    },
    colors: ['#5750F1', '#0ABEF9', '#10B981'],
    labels: chartData.labels,
    legend: {
      position: 'bottom',
      horizontalAlign: 'center',
      fontSize: '14px',
      fontFamily: 'inherit',
      markers: {
        size: 8,
      }
    },
    plotOptions: {
      pie: {
        donut: {
          size: '60%',
          labels: {
            show: true,
            total: {
              show: true,
              label: 'Total Admins',
              fontSize: '16px',
              fontWeight: 600,
              color: '#374151',
              formatter: function() {
                return chartData.total.toString();
              }
            },
            value: {
              show: true,
              fontSize: '24px',
              fontWeight: 700,
              color: '#111827',
            }
          }
        }
      }
    },
    dataLabels: {
      enabled: true,
      formatter: function(val, opts) {
        const value = opts.w.config.series[opts.seriesIndex];
        if (value === 0) return ''; // Don't show label for 0 values
        return `${value}`;
      },
      style: {
        fontSize: '14px',
        fontWeight: 700,
        colors: ['#ffffff']
      },
      dropShadow: {
        enabled: true,
        top: 1,
        left: 1,
        blur: 1,
        color: '#000',
        opacity: 0.45
      }
    },
    tooltip: {
      y: {
        formatter: function(value, { seriesIndex }) {
          const planType = chartData.labels[seriesIndex];
          const percentage = ((value / chartData.total) * 100).toFixed(1);
          return `${value} admins (${percentage}%)`;
        }
      }
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          height: 250
        },
        legend: {
          position: 'bottom'
        }
      }
    }]
  };

  if (chartData.total === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No subscription data available</p>
          <p className="text-sm">Chart will appear when admins have active subscriptions</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={chartData.series}
        type="donut"
        height={250}
      />
    </div>
  );
};

export default SubscriptionDistributionChart;
