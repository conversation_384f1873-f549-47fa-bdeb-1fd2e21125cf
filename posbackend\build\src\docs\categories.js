"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: Beverages
 *         description:
 *           type: string
 *           example: All types of beverages including soft drinks, juices, and water
 *         createdBy:
 *           type: integer
 *           example: 1
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: 2024-01-01T10:30:00Z
 *
 *     CreateCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           example: Beverages
 *           description: Category name
 *         description:
 *           type: string
 *           example: All types of beverages including soft drinks, juices, and water
 *           description: Category description (optional)
 */
/**
 * @swagger
 * /categories:
 *   post:
 *     summary: Manage categories
 *     description: Create, read, update, or delete product categories
 *     tags: [Category Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - allOf:
 *                   - type: object
 *                     properties:
 *                       mode:
 *                         type: string
 *                         enum: [createnew]
 *                         example: createnew
 *                   - $ref: '#/components/schemas/CreateCategoryRequest'
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                     description: Page number for pagination
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                     description: Number of items per page
 *                   search:
 *                     type: string
 *                     example: beverage
 *                     description: Search term for filtering categories
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [update]
 *                     example: update
 *                   id:
 *                     type: integer
 *                     example: 1
 *                     description: Category ID to update
 *                   name:
 *                     type: string
 *                     example: Soft Drinks
 *                   description:
 *                     type: string
 *                     example: Carbonated and non-carbonated soft drinks
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [delete]
 *                     example: delete
 *                   id:
 *                     type: integer
 *                     example: 1
 *                     description: Category ID to delete
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [bulk-delete]
 *                     example: bulk-delete
 *                   ids:
 *                     type: array
 *                     items:
 *                       type: integer
 *                     example: [1, 2, 3]
 *                     description: Array of category IDs to delete
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: Categories retrieved successfully
 *                     data:
 *                       type: object
 *                       properties:
 *                         categories:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Category'
 *                         total:
 *                           type: integer
 *                           example: 15
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 10
 *                         totalPages:
 *                           type: integer
 *                           example: 2
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Category not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Category name already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
