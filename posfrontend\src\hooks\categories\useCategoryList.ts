"use client";

import { useGetAllCategoriesQuery, Category } from "@/reduxRTK/services/categoryApi";
import { useState, useEffect } from "react";
import { useDebounce } from "@/hooks/useDebounce";

export const useCategoryList = (initialPage = 1, initialLimit = 10) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [searchTerm, setSearchTerm] = useState('');

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // RTK Query hook for fetching categories
  const {
    data,
    error,
    isLoading,
    isFetching,
    refetch
  } = useGetAllCategoriesQuery({
    page,
    limit,
    search: debouncedSearchTerm
  }, {
    // Refetch when the search term changes
    refetchOnMountOrArgChange: true
  });

  // Log search state for debugging
  console.log("Category search state:", {
    searchTerm,
    debouncedSearchTerm,
    resultsCount: (data as any)?.data?.categories?.length || 0,
    totalResults: (data as any)?.data?.total || 0
  });

  // Extract categories and pagination info from the response
  const categories: Category[] = (data as any)?.data?.categories || [];
  const total = (data as any)?.data?.total || 0;
  const currentPage = (data as any)?.data?.page || page;
  const perPage = (data as any)?.data?.perPage || limit;

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };

  return {
    categories,
    total,
    page: currentPage,
    limit: perPage,
    isLoading: isLoading || isFetching,
    isFetching,
    error,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
    handleLimitChange
  };
};
