"use client";

import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useGetAllUsersQuery } from '@/reduxRTK/services/authApi';

interface AdminSubscription {
  id: number;
  name: string;
  email: string;
  plan: string;
  amount: number;
  status: string;
  nextPaymentDue: string;
  subscriptionPeriod: number;
}

interface AdminSubscriptionsTableProps {
  className?: string;
}

const AdminSubscriptionsTable: React.FC<AdminSubscriptionsTableProps> = ({ className = "" }) => {
  const [subscriptions, setSubscriptions] = useState<AdminSubscription[]>([]);

  // Use Redux API to fetch users
  const { data: usersData, isLoading, error: apiError, refetch } = useGetAllUsersQuery({
    page: 1,
    limit: 100,
    search: ''
  });

  useEffect(() => {
    if ((usersData as any)?.success && (usersData as any).data) {
      // The API returns paginated data with users array
      const allUsers = (usersData as any).data.users || [];
      const adminUsers = allUsers.filter((user: any) => user.role === 'admin');

      const formattedSubscriptions: AdminSubscription[] = adminUsers.map((user: any) => {
        // Determine plan based on date difference between lastPaymentDate and nextPaymentDue
        let plan = 'Monthly';
        let amount = 89;
        let subscriptionPeriod = 1;

        if (user.lastPaymentDate && user.nextPaymentDue) {
          const lastPayment = new Date(user.lastPaymentDate);
          const nextDue = new Date(user.nextPaymentDue);
          const daysDiff = Math.round((nextDue.getTime() - lastPayment.getTime()) / (1000 * 60 * 60 * 24));

          console.log(`📊 User ${user.email} - Days between payments: ${daysDiff}`);

          // Determine subscription period based on days difference (NEW PRICING)
          if (daysDiff >= 350) { // ~12 months (allowing for some variance)
            plan = 'Annual';
            amount = 360;
            subscriptionPeriod = 12;
          } else if (daysDiff >= 80) { // ~3 months (allowing for some variance)
            plan = 'Quarterly';
            amount = 108;
            subscriptionPeriod = 3;
          } else {
            plan = 'Monthly';
            amount = 40;
            subscriptionPeriod = 1;
          }
        }

        return {
          id: user.id,
          name: user.name || 'Admin User',
          email: user.email,
          plan,
          amount,
          status: user.paymentStatus === 'paid' ? 'Active' :
                 user.paymentStatus === 'pending' ? 'Pending' :
                 user.paymentStatus === 'overdue' ? 'Overdue' : 'Inactive',
          nextPaymentDue: user.nextPaymentDue ?
            new Date(user.nextPaymentDue).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : 'N/A',
          subscriptionPeriod
        };
      });

      setSubscriptions(formattedSubscriptions);
      console.log('✅ Formatted admin subscriptions from Redux:', formattedSubscriptions);
    }
  }, [usersData]);

  // Handle refresh button click
  const handleRefresh = () => {
    refetch();
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Admin Subscriptions</h2>
        </div>
        <div className="flex justify-center items-center h-40">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        </div>
      </div>
    );
  }

  if (apiError) {
    return (
      <div className={`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Admin Subscriptions</h2>
          <button
            onClick={handleRefresh}
            className="text-sm text-blue-600 cursor-pointer hover:underline"
          >
            Retry
          </button>
        </div>
        <div className="text-center text-red-500 py-8">
          <p>Error loading subscriptions: {apiError.toString()}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800">Admin Subscriptions</h2>
        <button
          onClick={handleRefresh}
          className="text-sm text-blue-600 cursor-pointer hover:underline"
        >
          Refresh
        </button>
      </div>

      {subscriptions.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          <p>No admin subscriptions found</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Admin
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Next Payment
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscriptions.map((subscription) => (
                <tr key={subscription.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-sm">
                          {getInitials(subscription.name)}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{subscription.name}</div>
                        <div className="text-sm text-gray-500">{subscription.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{subscription.plan}</div>
                    <div className="text-sm text-gray-500">₵{subscription.amount}.00</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(subscription.status)}`}>
                      {subscription.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {subscription.nextPaymentDue}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default AdminSubscriptionsTable;
