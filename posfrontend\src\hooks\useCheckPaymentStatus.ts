import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import dayjs from "dayjs";

interface PaymentStatusInfo {
  isActive: boolean;
  daysRemaining: number | null;
  status: 'active' | 'pending' | 'overdue' | 'inactive';
  needsPayment: boolean;
}

export const useCheckPaymentStatus = (): PaymentStatusInfo => {
  const user = useSelector((state: RootState) => state.auth.user);
  const [statusInfo, setStatusInfo] = useState<PaymentStatusInfo>({
    isActive: false,
    daysRemaining: null,
    status: 'inactive',
    needsPayment: true,
  });

  useEffect(() => {
    if (!user) {
      setStatusInfo({
        isActive: false,
        daysRemaining: null,
        status: 'inactive',
        needsPayment: true,
      });
      return;
    }

    // Payment status checking

    let daysRemaining: number | null = null;
    let isActive = false;
    let needsPayment = true;
    let status: 'active' | 'pending' | 'overdue' | 'inactive' = 'inactive';

    // Superadmin always has active status and doesn't need payment
    if (user.role === 'superadmin') {
      setStatusInfo({
        isActive: true,
        daysRemaining: null,
        status: 'active',
        needsPayment: false,
      });
      return;
    }

    // Admin users now need to pay like regular users
    // Removed admin bypass to enforce payment for all non-superadmin users

    // Check payment status
    if (user.paymentStatus === 'paid') {
      isActive = true;
      needsPayment = false;  // Initially set to false for paid users
      status = 'active';

      // Check if user is in free trial (no lastPaymentDate means no payments made yet)
      const isInFreeTrial = !user.lastPaymentDate;

      // Calculate days remaining if nextPaymentDue exists
      if (user.nextPaymentDue) {
        const nextPaymentDate = dayjs(user.nextPaymentDue);
        const today = dayjs();
        daysRemaining = nextPaymentDate.diff(today, 'day');

        if (isInFreeTrial) {
          const daysSinceCreation = dayjs().diff(dayjs(user.createdAt), 'day');
          console.log("🎁 useCheckPaymentStatus - FREE TRIAL USER:", {
            email: user.email,
            daysSinceCreation,
            daysRemaining,
            trialDaysUsed: daysSinceCreation,
            trialDaysRemaining: daysRemaining,
            isActive,
            needsPayment
          });
        }

        // If less than 7 days remaining, show payment notification but don't force redirect
        // We'll keep needsPayment as false so they're not redirected to payment page
        // This allows users with 'paid' status to access the dashboard
      }
    } else if (user.paymentStatus === 'pending') {
      isActive = false;
      needsPayment = true;
      status = 'pending';
    } else if (user.paymentStatus === 'overdue') {
      isActive = false;
      needsPayment = true;
      status = 'overdue';
    } else {
      isActive = false;
      needsPayment = true;
      status = 'inactive';
    }

    const finalStatusInfo = {
      isActive,
      daysRemaining,
      status,
      needsPayment,
    };



    setStatusInfo(finalStatusInfo);
  }, [user]);

  return statusInfo;
};
