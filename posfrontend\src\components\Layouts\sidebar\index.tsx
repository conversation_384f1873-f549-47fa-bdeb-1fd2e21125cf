"use client";

import { Logo } from "@/components/logo";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { NAV_DATA, getMenuItemsByRole } from "./data";
import { ArrowLeftIcon, ChevronUp } from "./icons";
import { MenuItem } from "./menu-item";
import { useSidebarContext } from "./sidebar-context";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";

// Define types for navigation items
interface SubItem {
  title: string;
  url: string;
}

interface NavItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  items: SubItem[];
  url?: string;
}

interface NavSection {
  label: string;
  items: NavItem[];
}

export function Sidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { setIsOpen, isOpen, isMobile, toggleSidebar } = useSidebarContext();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Get user role from Redux store
  const { user } = useSelector((state: RootState) => state.auth);

  // Get menu items based on user role
  const menuData = user ? getMenuItemsByRole(user.role) : NAV_DATA;

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title) ? prev.filter((t) => t !== title) : [...prev, title]
    );
  };

  useEffect(() => {
    // Find the submenu containing the active route
    let activeMenu: string | null = null;
    menuData.some((section: NavSection) => {
      return section.items.some((item: NavItem) => {
        return item.items.some((subItem: SubItem) => {
          if (subItem.url === pathname) {
            activeMenu = item.title;
            return true;
          }
          return false;
        });
      });
    });
    if (activeMenu && !expandedItems.includes(activeMenu)) {
      setExpandedItems(prev => [...prev, activeMenu!]);
    }
    // Do not forcibly close other submenus, allow manual toggling
  }, [pathname, menuData, expandedItems]);

  return (
    <>
      {/* Mobile Overlay - Only show when sidebar is explicitly toggled open on mobile */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
          onClick={() => {
            console.log("Overlay clicked, closing sidebar");
            setIsOpen(false);
          }}
          aria-hidden="true"
        />
      )}

      <aside
        className={cn(
          "h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out",
          isMobile ? "fixed left-0 top-0 bottom-0 z-50 transform" : "z-40",
          isOpen || !isMobile ? "translate-x-0" : "-translate-x-full"
        )}
        aria-label="Main navigation"
        aria-hidden={isMobile && !isOpen}
      >
        <div className="flex h-full flex-col pt-3 pb-5 pl-[25px] pr-[7px]">
          {/* Logo container with fixed height and proper alignment */}
          <div className="relative sidebar-logo-container pr-4.5">
            <Link
              href={"/dashboard"}
              onClick={(e) => {
                // Check if we're already on a page with a different layout
                const isOnUsersPage = window.location.pathname.includes("/dashboard/users");

                // If we're on a page with a different layout, allow full page navigation
                if (isOnUsersPage) {
                  if (isMobile) {
                    toggleSidebar();
                  }
                  return; // Allow default navigation
                }

                // Otherwise use client-side navigation
                e.preventDefault();

                if (isMobile) {
                  toggleSidebar();
                }

                // Use Next.js router for client-side navigation
                router.push("/dashboard");
              }}
              className="sidebar-logo"
            >
              <Logo />
            </Link>

            {isMobile && (
              <button
                onClick={toggleSidebar}
                className="absolute right-0 top-1/2 -translate-y-1/2 text-right"
              >
                <span className="sr-only">Close Menu</span>
                <ArrowLeftIcon className="ml-auto size-7" />
              </button>
            )}
          </div>

          {/* Navigation */}
          <div className="custom-scrollbar flex-1 overflow-y-auto pr-3 sidebar-menu">
            {menuData.map((section: NavSection) => (
              <div key={section.label} className="mb-6">
                <h2 className="mb-5 text-sm font-medium text-gray-600">
                  {section.label}
                </h2>

                <nav role="navigation" aria-label={section.label}>
                  <ul className="space-y-2">
                    {section.items.map((item: NavItem) => (
                      <li key={item.title}>
                        {item.items.length ? (
                          <div>
                            <MenuItem
                              isActive={item.items.some(
                                ({ url }) => pathname.startsWith(url),
                              )}
                              onClick={() => toggleExpanded(item.title)}
                            >
                              <item.icon
                                className="size-6 shrink-0"
                                aria-hidden="true"
                              />

                              <span>{item.title}</span>

                              <ChevronUp
                                className={cn(
                                  "ml-auto rotate-180 transition-transform duration-200",
                                  expandedItems.includes(item.title) &&
                                    "rotate-0",
                                )}
                                aria-hidden="true"
                              />
                            </MenuItem>

                            {expandedItems.includes(item.title) && (
                              <ul
                                className="ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2"
                                role="menu"
                              >
                                {item.items.map((subItem: SubItem) => (
                                  <li key={subItem.title} role="none">
                                    <MenuItem
                                      as="link"
                                      href={subItem.url}
                                      isActive={pathname === subItem.url}
                                    >
                                      <span>{subItem.title}</span>
                                    </MenuItem>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ) : (
                          (() => {
                            const href =
                              "url" in item && item.url
                                ? item.url
                                : "/" +
                                  item.title.toLowerCase().split(" ").join("-");

                            return (
                              <MenuItem
                                className="flex items-center gap-3 py-3"
                                as="link"
                                href={href}
                                isActive={(() => {
                                  // Clean active detection logic

                                  // Special handling for dashboard - only exact match
                                  if (href === '/dashboard') {
                                    return pathname === href;
                                  }

                                  // For other routes - exact match or starts with href + '/'
                                  return pathname === href || pathname.startsWith(href + '/');
                                })()}
                              >
                                <item.icon
                                  className="size-6 shrink-0"
                                  aria-hidden="true"
                                />

                                <span>{item.title}</span>
                              </MenuItem>
                            );
                          })()
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            ))}
          </div>
        </div>
      </aside>
    </>
  );
}

