"use strict";exports.id=7277,exports.ids=[7277],exports.modules={24648:(e,n,t)=>{t.d(n,{A:()=>c});var i=t(11855),a=t(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var r=t(78480);let c=a.forwardRef(function(e,n){return a.createElement(r.A,(0,i.A)({},e,{ref:n,icon:l}))})},87173:(e,n,t)=>{t.d(n,{A:()=>f});var i=t(58009),a=t.n(i),l=t(56073),r=t.n(l),c=t(86866);function o(e){return["small","middle","large"].includes(e)}function d(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var s=t(27343),u=t(66799);let h=a().createContext({latestIndex:0}),m=h.Provider,g=e=>{let{className:n,index:t,children:a,split:l,style:r}=e,{latestIndex:c}=i.useContext(h);return null==a?null:i.createElement(i.Fragment,null,i.createElement("div",{className:n,style:r},a),t<c&&l&&i.createElement("span",{className:`${n}-split`},l))};var $=t(94953),p=function(e,n){var t={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>n.indexOf(i)&&(t[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>n.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(t[i[a]]=e[i[a]]);return t};let b=i.forwardRef((e,n)=>{var t;let{getPrefixCls:a,direction:l,size:u,className:h,style:b,classNames:f,styles:v}=(0,s.TP)("space"),{size:k=null!=u?u:"small",align:S,className:I,rootClassName:w,children:y,direction:C="horizontal",prefixCls:A,split:E,style:x,wrap:O=!1,classNames:N,styles:z}=e,q=p(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[M,j]=Array.isArray(k)?k:[k,k],D=o(j),H=o(M),P=d(j),T=d(M),L=(0,c.A)(y,{keepEmpty:!0}),R=void 0===S&&"horizontal"===C?"center":S,X=a("space",A),[W,F,K]=(0,$.A)(X),G=r()(X,h,F,`${X}-${C}`,{[`${X}-rtl`]:"rtl"===l,[`${X}-align-${R}`]:R,[`${X}-gap-row-${j}`]:D,[`${X}-gap-col-${M}`]:H},I,w,K),V=r()(`${X}-item`,null!==(t=null==N?void 0:N.item)&&void 0!==t?t:f.item),_=0,B=L.map((e,n)=>{var t;null!=e&&(_=n);let a=(null==e?void 0:e.key)||`${V}-${n}`;return i.createElement(g,{className:V,key:a,index:n,split:E,style:null!==(t=null==z?void 0:z.item)&&void 0!==t?t:v.item},e)}),Q=i.useMemo(()=>({latestIndex:_}),[_]);if(0===L.length)return null;let Y={};return O&&(Y.flexWrap="wrap"),!H&&T&&(Y.columnGap=M),!D&&P&&(Y.rowGap=j),W(i.createElement("div",Object.assign({ref:n,className:G,style:Object.assign(Object.assign(Object.assign({},Y),b),x)},q),i.createElement(m,{value:Q},B)))});b.Compact=u.Ay;let f=b},46542:(e,n,t)=>{t.d(n,{A:()=>q});var i=t(58009),a=t(88752),l=t(56073),r=t.n(l),c=t(11855),o=t(65074),d=t(7770),s=t(49543),u=t(61849),h=t(73924),m=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],g=i.forwardRef(function(e,n){var t,a=e.prefixCls,l=void 0===a?"rc-switch":a,g=e.className,$=e.checked,p=e.defaultChecked,b=e.disabled,f=e.loadingIcon,v=e.checkedChildren,k=e.unCheckedChildren,S=e.onClick,I=e.onChange,w=e.onKeyDown,y=(0,s.A)(e,m),C=(0,u.A)(!1,{value:$,defaultValue:p}),A=(0,d.A)(C,2),E=A[0],x=A[1];function O(e,n){var t=E;return b||(x(t=e),null==I||I(t,n)),t}var N=r()(l,g,(t={},(0,o.A)(t,"".concat(l,"-checked"),E),(0,o.A)(t,"".concat(l,"-disabled"),b),t));return i.createElement("button",(0,c.A)({},y,{type:"button",role:"switch","aria-checked":E,disabled:b,className:N,ref:n,onKeyDown:function(e){e.which===h.A.LEFT?O(!1,e):e.which===h.A.RIGHT&&O(!0,e),null==w||w(e)},onClick:function(e){var n=O(!E,e);null==S||S(n,e)}}),f,i.createElement("span",{className:"".concat(l,"-inner")},i.createElement("span",{className:"".concat(l,"-inner-checked")},v),i.createElement("span",{className:"".concat(l,"-inner-unchecked")},k)))});g.displayName="Switch";var $=t(81567),p=t(27343),b=t(87375),f=t(43089),v=t(1439),k=t(43891),S=t(47285),I=t(13662),w=t(10941);let y=e=>{let{componentCls:n,trackHeightSM:t,trackPadding:i,trackMinWidthSM:a,innerMinMarginSM:l,innerMaxMarginSM:r,handleSizeSM:c,calc:o}=e,d=`${n}-inner`,s=(0,v.zA)(o(c).add(o(i).mul(2)).equal()),u=(0,v.zA)(o(r).mul(2).equal());return{[n]:{[`&${n}-small`]:{minWidth:a,height:t,lineHeight:(0,v.zA)(t),[`${n}-inner`]:{paddingInlineStart:r,paddingInlineEnd:l,[`${d}-checked, ${d}-unchecked`]:{minHeight:t},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${u})`,marginInlineEnd:`calc(100% - ${s} + ${u})`},[`${d}-unchecked`]:{marginTop:o(t).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${n}-handle`]:{width:c,height:c},[`${n}-loading-icon`]:{top:o(o(c).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${n}-checked`]:{[`${n}-inner`]:{paddingInlineStart:l,paddingInlineEnd:r,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${u})`,marginInlineEnd:`calc(-100% + ${s} - ${u})`}},[`${n}-handle`]:{insetInlineStart:`calc(100% - ${(0,v.zA)(o(c).add(i).equal())})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:o(e.marginXXS).div(2).equal(),marginInlineEnd:o(e.marginXXS).mul(-1).div(2).equal()}},[`&${n}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:o(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(e.marginXXS).div(2).equal()}}}}}}},C=e=>{let{componentCls:n,handleSize:t,calc:i}=e;return{[n]:{[`${n}-loading-icon${e.iconCls}`]:{position:"relative",top:i(i(t).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${n}-checked ${n}-loading-icon`]:{color:e.switchColor}}}},A=e=>{let{componentCls:n,trackPadding:t,handleBg:i,handleShadow:a,handleSize:l,calc:r}=e,c=`${n}-handle`;return{[n]:{[c]:{position:"absolute",top:t,insetInlineStart:t,width:l,height:l,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:i,borderRadius:r(l).div(2).equal(),boxShadow:a,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${n}-checked ${c}`]:{insetInlineStart:`calc(100% - ${(0,v.zA)(r(l).add(t).equal())})`},[`&:not(${n}-disabled):active`]:{[`${c}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${n}-checked ${c}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},E=e=>{let{componentCls:n,trackHeight:t,trackPadding:i,innerMinMargin:a,innerMaxMargin:l,handleSize:r,calc:c}=e,o=`${n}-inner`,d=(0,v.zA)(c(r).add(c(i).mul(2)).equal()),s=(0,v.zA)(c(l).mul(2).equal());return{[n]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:l,paddingInlineEnd:a,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:t},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${s})`,marginInlineEnd:`calc(100% - ${d} + ${s})`},[`${o}-unchecked`]:{marginTop:c(t).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${n}-checked ${o}`]:{paddingInlineStart:a,paddingInlineEnd:l,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${s})`,marginInlineEnd:`calc(-100% + ${d} - ${s})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:c(i).mul(2).equal(),marginInlineEnd:c(i).mul(-1).mul(2).equal()}},[`&${n}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:c(i).mul(-1).mul(2).equal(),marginInlineEnd:c(i).mul(2).equal()}}}}}},x=e=>{let{componentCls:n,trackHeight:t,trackMinWidth:i}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,S.dF)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:i,height:t,lineHeight:(0,v.zA)(t),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${n}-disabled)`]:{background:e.colorTextTertiary}}),(0,S.K8)(e)),{[`&${n}-checked`]:{background:e.switchColor,[`&:hover:not(${n}-disabled)`]:{background:e.colorPrimaryHover}},[`&${n}-loading, &${n}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${n}-rtl`]:{direction:"rtl"}})}},O=(0,I.OF)("Switch",e=>{let n=(0,w.oX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[x(n),E(n),A(n),C(n),y(n)]},e=>{let{fontSize:n,lineHeight:t,controlHeight:i,colorWhite:a}=e,l=n*t,r=i/2,c=l-4,o=r-4;return{trackHeight:l,trackHeightSM:r,trackMinWidth:2*c+8,trackMinWidthSM:2*o+4,trackPadding:2,handleBg:a,handleSize:c,handleSizeSM:o,handleShadow:`0 2px 4px 0 ${new k.Y("#00230b").setA(.2).toRgbString()}`,innerMinMargin:c/2,innerMaxMargin:c+2+4,innerMinMarginSM:o/2,innerMaxMarginSM:o+2+4}});var N=function(e,n){var t={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>n.indexOf(i)&&(t[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>n.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(t[i[a]]=e[i[a]]);return t};let z=i.forwardRef((e,n)=>{let{prefixCls:t,size:l,disabled:c,loading:o,className:d,rootClassName:s,style:h,checked:m,value:v,defaultChecked:k,defaultValue:S,onChange:I}=e,w=N(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[y,C]=(0,u.A)(!1,{value:null!=m?m:v,defaultValue:null!=k?k:S}),{getPrefixCls:A,direction:E,switch:x}=i.useContext(p.QO),z=i.useContext(b.A),q=(null!=c?c:z)||o,M=A("switch",t),j=i.createElement("div",{className:`${M}-handle`},o&&i.createElement(a.A,{className:`${M}-loading-icon`})),[D,H,P]=O(M),T=(0,f.A)(l),L=r()(null==x?void 0:x.className,{[`${M}-small`]:"small"===T,[`${M}-loading`]:o,[`${M}-rtl`]:"rtl"===E},d,s,H,P),R=Object.assign(Object.assign({},null==x?void 0:x.style),h);return D(i.createElement($.A,{component:"Switch"},i.createElement(g,Object.assign({},w,{checked:y,onChange:function(){C(arguments.length<=0?void 0:arguments[0]),null==I||I.apply(void 0,arguments)},prefixCls:M,className:L,style:R,disabled:q,ref:n,loadingIcon:j}))))});z.__ANT_SWITCH=!0;let q=z}};