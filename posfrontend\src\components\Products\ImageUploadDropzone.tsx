import React from 'react';
import Image from 'next/image';
import { useDropzone } from 'react-dropzone';
import { Button, message } from 'antd';
import { PictureOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { uploadProductImage } from '@/utils/cloudinaryUtils';

interface ImageUploadDropzoneProps {
  imageUrl: string | null;
  setImageUrl: (url: string | null) => void;
  form: any;
}

const ImageUploadDropzone: React.FC<ImageUploadDropzoneProps> = ({
  imageUrl,
  setImageUrl,
  form,
}) => {
  const [isUploading, setIsUploading] = React.useState(false);
  const [previewError, setPreviewError] = React.useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles: 1,
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        try {
          setIsUploading(true);
          setPreviewError(false);
          const url = await uploadProductImage(acceptedFiles[0]);
          setImageUrl(url);
          form.setFieldsValue({ imageUrl: url });
          message.success('Image uploaded successfully');
        } catch (error) {
          console.error('Upload error:', error);
          message.error('Failed to upload image');
          setImageUrl(null);
          form.setFieldsValue({ imageUrl: null });
        } finally {
          setIsUploading(false);
        }
      }
    }
  });

  return (
    <div className="flex flex-col items-start">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 w-full text-center cursor-pointer transition-all duration-200 ${
          isDragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
        }`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-3">
          <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
            <PictureOutlined className="text-2xl text-gray-400" />
          </div>
          {isDragActive ? (
            <p className="text-blue-500 font-medium">Drop the image here...</p>
          ) : (
            <div className="space-y-1">
              <p className="text-gray-600 font-medium">
                Drag & drop an image here, or click to select
              </p>
              <p className="text-sm text-gray-500">
                Supports: JPG, PNG, GIF, WEBP
              </p>
            </div>
          )}
        </div>
      </div>
      
      {imageUrl && !previewError && (
        <div className="mt-4 relative group">
          <Image
            src={imageUrl}
            alt="Product"
            width={200}
            height={200}
            className="object-cover border border-gray-200 rounded-lg shadow-sm"
            onError={() => {
              console.error('Image failed to load:', imageUrl);
              setPreviewError(true);
              setImageUrl(null);
              form.setFieldsValue({ imageUrl: null });
              message.error('Failed to load image preview');
            }}
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                setImageUrl(null);
                form.setFieldsValue({ imageUrl: null });
              }}
              className="text-white hover:text-red-500"
            >
              Remove
            </Button>
          </div>
        </div>
      )}
      
      {isUploading && (
        <div className="mt-2 text-blue-500 flex items-center">
          <LoadingOutlined className="mr-2" />
          Uploading...
        </div>
      )}
    </div>
  );
};

export default ImageUploadDropzone; 