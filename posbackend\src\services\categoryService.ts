import { postgresDb } from "../db/db";
import { categories } from "../db/schema";
import { eq, and, count, desc, like, or, inArray, ne } from "drizzle-orm";
import { categorySchema } from "../validation/schema";
import { JwtPayload } from "../types/type";
import { authorizeAction } from "../utils/authorizeAction";

// ✅ **Create Category**
export const createCategory = async (
  requester: JwtPayload,
  categoryData: { name: string; description?: string }
) => {
  categorySchema.parse(categoryData);
  await authorizeAction(requester, "create", "category");

  const createdBy = requester.id;

  // ✅ Check if the user already has this category name
  const existingCategory = await postgresDb
    .select()
    .from(categories)
    .where(and(eq(categories.name, categoryData.name), eq(categories.createdBy, createdBy)))
    .limit(1);

  if (existingCategory.length > 0) {
    throw new Error("You already have a category with this name.");
  }

  // ✅ Create the category for this user
  const [newCategory] = await postgresDb
    .insert(categories)
    .values({
      ...categoryData,
      createdBy,
      createdAt: new Date(),
    })
    .returning();

  if (!newCategory) throw new Error("Category creation failed.");

  return { category: newCategory };
};


// ✅ **Get All Categories**
export const getAllCategories = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10,
  searchTerm?: string
) => {
  const offset = (page - 1) * limit;
  await authorizeAction(requester, "getAll", "category");

  const isSuperadmin = requester.role === "superadmin";

  // Apply search filter if provided
  if (searchTerm && searchTerm.trim() !== '') {
    // Use case-insensitive search with wildcards on both sides
    const searchPattern = `%${searchTerm.trim()}%`;

    // Log the exact search pattern for debugging
    console.log(`Category search pattern: "${searchPattern}"`);

    // Create search condition
    const searchCondition = or(
      like(categories.name, searchPattern),
      like(categories.description || '', searchPattern)
    );

    // Combine with role-based condition
    if (isSuperadmin) {
      // Superadmin can see all categories that match search
      const [categoryData, totalCategories] = await Promise.all([
        postgresDb
          .select()
          .from(categories)
          .where(searchCondition)
          .orderBy(desc(categories.createdAt))
          .limit(limit)
          .offset(offset),
        postgresDb
          .select({ count: count() })
          .from(categories)
          .where(searchCondition),
      ]);

      return {
        total: totalCategories[0].count,
        page,
        perPage: limit,
        categories: categoryData,
      };
    } else {
      // Others can only see their own categories that match search
      const [categoryData, totalCategories] = await Promise.all([
        postgresDb
          .select()
          .from(categories)
          .where(and(eq(categories.createdBy, requester.id), searchCondition))
          .orderBy(desc(categories.createdAt))
          .limit(limit)
          .offset(offset),
        postgresDb
          .select({ count: count() })
          .from(categories)
          .where(and(eq(categories.createdBy, requester.id), searchCondition)),
      ]);

      return {
        total: totalCategories[0].count,
        page,
        perPage: limit,
        categories: categoryData,
      };
    }
  }

  // ✅ If no search term, fetch either all categories (for superadmin) or only the user's categories
  const [categoryData, totalCategories] = await Promise.all([
    postgresDb
      .select()
      .from(categories)
      .where(isSuperadmin ? undefined : eq(categories.createdBy, requester.id))
      .orderBy(desc(categories.createdAt))
      .limit(limit)
      .offset(offset),
    postgresDb
      .select({ count: count() })
      .from(categories)
      .where(isSuperadmin ? undefined : eq(categories.createdBy, requester.id)),
  ]);

  return {
    total: totalCategories[0].count,
    page,
    perPage: limit,
    categories: categoryData,
  };
};



// ✅ **Get Category by ID**
export const getCategoryById = async (requester: JwtPayload, id: number) => {
  await authorizeAction(requester, "getById", "category", id);

  const isSuperadmin = requester.role === "superadmin";

  const categoryData = await postgresDb
    .select()
    .from(categories)
    .where(isSuperadmin ? eq(categories.id, id) : and(eq(categories.id, id), eq(categories.createdBy, requester.id)))
    .limit(1);

  if (categoryData.length === 0) throw new Error("Category not found or unauthorized.");

  return categoryData[0];
};



// ✅ **Update Category**
export const updateCategoryById = async (
  requester: JwtPayload,
  categoryId: number,
  updateData: Partial<{ name: string; description: string }>
) => {
  await authorizeAction(requester, "update", "category", categoryId);

  const isSuperadmin = requester.role === "superadmin";

  // ✅ Ensure the category exists (Superadmin can update any, others only their own)
  const existingCategory = await postgresDb
    .select()
    .from(categories)
    .where(isSuperadmin ? eq(categories.id, categoryId) : and(eq(categories.id, categoryId), eq(categories.createdBy, requester.id)))
    .limit(1);

  if (existingCategory.length === 0) {
    throw new Error("Category not found or unauthorized.");
  }

  // ✅ Check if the new name already exists for this user (Superadmin is excluded from this check)
  if (!isSuperadmin && updateData.name) {
    const duplicateCategory = await postgresDb
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.name, updateData.name),
          eq(categories.createdBy, requester.id),
          // Exclude the current category being updated - use the 'ne' (not equal) operator
          ne(categories.id, categoryId)
        )
      )
      .limit(1);

    if (duplicateCategory.length > 0) {
      throw new Error("You already have a category with this name.");
    }
  }

  // ✅ Update the category
  const updatedCategory = await postgresDb
    .update(categories)
    .set(updateData)
    .where(eq(categories.id, categoryId))
    .returning();

  if (!updatedCategory || updatedCategory.length === 0) {
    throw new Error("Update failed: Category not found.");
  }

  return { updatedCategory: updatedCategory[0] };
};



// ✅ **Delete Categories (Single or Multiple)**
export const deleteCategoryById = async (requester: JwtPayload, ids: number | number[]) => {
  const categoryIds = Array.isArray(ids) ? ids : [ids];
  const isSuperadmin = requester.role === "superadmin";

  // ✅ Check authorization for each category
  for (const id of categoryIds) {
    await authorizeAction(requester, "delete", "category", id);

    // ✅ Ensure the category exists (Superadmin can delete any, others only their own)
    const existingCategory = await postgresDb
      .select()
      .from(categories)
      .where(isSuperadmin ? eq(categories.id, id) : and(eq(categories.id, id), eq(categories.createdBy, requester.id)))
      .limit(1);

    if (existingCategory.length === 0) {
      throw new Error(`Category with ID ${id} not found or unauthorized.`);
    }
  }

  // ✅ Delete the categories
  const deletedCategories = await postgresDb
    .delete(categories)
    .where(inArray(categories.id, categoryIds))
    .returning({ deletedId: categories.id });

  if (!deletedCategories || deletedCategories.length === 0) {
    throw new Error("Delete failed: Categories not found.");
  }

  return {
    deletedIds: deletedCategories.map(category => category.deletedId)
  };
};


