export type UserRole = "superadmin" | "admin" | "cashier";
export type PaymentStatus = "pending" | "paid" | "overdue" | "inactive";

export interface JwtPayload {
    id: number;
    role: UserRole;
}

export interface DecodedToken {
    id: number;
    role: UserRole;
}

// ✅ Extend Express Request globally
declare module "express-serve-static-core" {
  interface Request {
    user?: JwtPayload;
  }
}

// ✅ Updated User Type with all fields
export interface UserRecord {
  id: number;
  name: string;
  email: string;
  phone: string;
  passwordHash: string;
  role: UserRole;
  paymentStatus: PaymentStatus;
  createdAt: Date | string | null;
  lastPaymentDate?: Date | string | null;
  nextPaymentDue?: Date | string | null;
  createdBy?: number | null;
}

export interface SaleItem {
  productId: number;
  quantity: number;
  price: number;
}

export interface SaleData {
  totalAmount: number;
  paymentMethod: "cash" | "card" | "mobile_money";
  items: SaleItem[];
}

export interface BulkSaleData {
  sales: SaleData[];
}
