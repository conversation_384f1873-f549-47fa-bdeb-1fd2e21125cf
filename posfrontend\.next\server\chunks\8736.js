"use strict";exports.id=8736,exports.ids=[8736],exports.modules={66157:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(11855),n=r(58009);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var l=r(78480);let o=n.forwardRef(function(e,t){return n.createElement(l.A,(0,a.A)({},e,{ref:t,icon:s}))})},85303:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(58009);function n(){let[,e]=a.useReducer(e=>e+1,0);return e}},83893:(e,t,r)=>{r.d(t,{Ay:()=>d,ko:()=>c,ye:()=>l});var a=r(58009),n=r.n(a),s=r(39772);let l=["xxl","xl","lg","md","sm","xs"],o=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}),i=e=>{let t=[].concat(l).reverse();return t.forEach((r,a)=>{let n=r.toUpperCase(),s=`screen${n}Min`,l=`screen${n}`;if(!(e[s]<=e[l]))throw Error(`${s}<=${l} fails : !(${e[s]}<=${e[l]})`);if(a<t.length-1){let r=`screen${n}Max`;if(!(e[l]<=e[r]))throw Error(`${l}<=${r} fails : !(${e[l]}<=${e[r]})`);let s=t[a+1].toUpperCase(),o=`screen${s}Min`;if(!(e[r]<=e[o]))throw Error(`${r}<=${o} fails : !(${e[r]}<=${e[o]})`)}}),e},c=(e,t)=>{if(t){for(let r of l)if(e[r]&&(null==t?void 0:t[r])!==void 0)return t[r]}},d=()=>{let[,e]=(0,s.Ay)(),t=o(i(e));return n().useMemo(()=>{let e=new Map,r=-1,a={};return{responsiveMap:t,matchHandlers:{},dispatch:t=>(a=t,e.forEach(e=>e(a)),e.size>=1),subscribe(t){return e.size||this.register(),r+=1,e.set(r,t),t(a),r},unsubscribe(t){e.delete(t),e.size||this.unregister()},register(){Object.keys(t).forEach(e=>{let r=t[e],n=t=>{let{matches:r}=t;this.dispatch(Object.assign(Object.assign({},a),{[e]:r}))},s=window.matchMedia(r);s.addListener(n),this.matchHandlers[r]={mql:s,listener:n},n(s)})},unregister(){Object.keys(t).forEach(e=>{let r=t[e],a=this.matchHandlers[r];null==a||a.mql.removeListener(null==a?void 0:a.listener)}),e.clear()}}},[e])}},9170:(e,t,r)=>{r.d(t,{A:()=>a});let a=r(59286).A},84133:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(58009).createContext)({})},59286:(e,t,r)=>{r.d(t,{A:()=>f});var a=r(58009),n=r(56073),s=r.n(n),l=r(27343),o=r(84133),i=r(49342),c=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],f=a.forwardRef((e,t)=>{let{getPrefixCls:r,direction:n}=a.useContext(l.QO),{gutter:f,wrap:p}=a.useContext(o.A),{prefixCls:h,span:y,order:m,offset:g,push:$,pull:x,className:A,children:v,flex:b,style:P}=e,O=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),w=r("col",h),[j,E,D]=(0,i.xV)(w),C={},R={};u.forEach(t=>{let r={},a=e[t];"number"==typeof a?r.span=a:"object"==typeof a&&(r=a||{}),delete O[t],R=Object.assign(Object.assign({},R),{[`${w}-${t}-${r.span}`]:void 0!==r.span,[`${w}-${t}-order-${r.order}`]:r.order||0===r.order,[`${w}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${w}-${t}-push-${r.push}`]:r.push||0===r.push,[`${w}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${w}-rtl`]:"rtl"===n}),r.flex&&(R[`${w}-${t}-flex`]=!0,C[`--${w}-${t}-flex`]=d(r.flex))});let S=s()(w,{[`${w}-${y}`]:void 0!==y,[`${w}-order-${m}`]:m,[`${w}-offset-${g}`]:g,[`${w}-push-${$}`]:$,[`${w}-pull-${x}`]:x},A,R,E,D),_={};if(f&&f[0]>0){let e=f[0]/2;_.paddingLeft=e,_.paddingRight=e}return b&&(_.flex=d(b),!1!==p||_.minWidth||(_.minWidth=0)),j(a.createElement("div",Object.assign({},O,{style:Object.assign(Object.assign(Object.assign({},_),P),C),className:S,ref:t}),v))})},52271:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(58009),n=r(55977),s=r(85303),l=r(83893);let o=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,a.useRef)(t),o=(0,s.A)(),i=(0,l.Ay)();return(0,n.A)(()=>{let t=i.subscribe(t=>{r.current=t,e&&o()});return()=>i.unsubscribe(t)},[]),r.current}},14207:(e,t,r)=>{r.d(t,{A:()=>p});var a=r(58009),n=r(56073),s=r.n(n),l=r(83893),o=r(27343),i=r(52271),c=r(84133),d=r(49342),u=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};function f(e,t){let[r,n]=a.useState("string"==typeof e?e:""),s=()=>{if("string"==typeof e&&n(e),"object"==typeof e)for(let r=0;r<l.ye.length;r++){let a=l.ye[r];if(!t||!t[a])continue;let s=e[a];if(void 0!==s){n(s);return}}};return a.useEffect(()=>{s()},[JSON.stringify(e),t]),r}let p=a.forwardRef((e,t)=>{let{prefixCls:r,justify:n,align:p,className:h,style:y,children:m,gutter:g=0,wrap:$}=e,x=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:A,direction:v}=a.useContext(o.QO),b=(0,i.A)(!0,null),P=f(p,b),O=f(n,b),w=A("row",r),[j,E,D]=(0,d.L3)(w),C=function(e,t){let r=[void 0,void 0],a=Array.isArray(e)?e:[e,void 0],n=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return a.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let a=0;a<l.ye.length;a++){let s=l.ye[a];if(n[s]&&void 0!==e[s]){r[t]=e[s];break}}else r[t]=e}),r}(g,b),R=s()(w,{[`${w}-no-wrap`]:!1===$,[`${w}-${O}`]:O,[`${w}-${P}`]:P,[`${w}-rtl`]:"rtl"===v},h,E,D),S={},_=null!=C[0]&&C[0]>0?-(C[0]/2):void 0;_&&(S.marginLeft=_,S.marginRight=_);let[L,N]=C;S.rowGap=N;let M=a.useMemo(()=>({gutter:[L,N],wrap:$}),[L,N,$]);return j(a.createElement(c.A.Provider,{value:M},a.createElement("div",Object.assign({},x,{className:R,style:Object.assign(Object.assign({},S),y),ref:t}),m)))})},1236:(e,t,r)=>{r.d(t,{A:()=>a});let a=r(14207).A},60636:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(92273),n=r(25510),s=r(97245),l=r(42211);let o=()=>{let e=(0,a.wA)(),{user:t,accessToken:r}=(0,a.d4)(e=>e.auth),o=(0,n._)(),{refetch:i}=(0,s.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!r,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await i();console.log("useAuth - Refetch result:",a);let n=a.data;if(n?.success&&n?.data){console.log("useAuth - API response data:",n.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let s=n.data.phone||t.phone||"",o=n.data.createdAt||t.createdAt||"",i=n.data.lastPaymentDate||t.lastPaymentDate||void 0,c=n.data.nextPaymentDue||t.nextPaymentDue||null,d=n.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:n.data.phone,userPhone:t.phone,finalPhone:s,apiCreatedAt:n.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:n.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:n.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:n.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...n.data,phone:s,createdAt:o,lastPaymentDate:i,nextPaymentDue:c,createdBy:d,paymentStatus:a};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,l.gV)({user:u,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",n?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:d}}}};