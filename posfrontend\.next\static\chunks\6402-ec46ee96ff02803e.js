"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6402],{86402:(e,t,a)=>{a.d(t,{default:()=>S});var n=a(95155),s=a(12115),i=a(83391),l=a(76046),r=a(29395);let u=e=>{let{children:t}=e,{user:a,accessToken:u}=(0,i.d4)(e=>e.auth),d=(0,l.useRouter)(),o=(0,l.usePathname)(),[c,m]=(0,s.useState)(!1),[p,y]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{m(!0),console.log("AuthGuard mounted on client",{isAuthenticated:!!a&&!!u,userRole:null==a?void 0:a.role,pathname:o})},[u,o,a]),(0,s.useEffect)(()=>{!c||a&&u||p||(console.log("AuthGuard - Redirecting to login",{pathname:o}),y(!0),sessionStorage.setItem("redirectUrl",o),setTimeout(()=>{d.push("/")},100))},[a,u,d,o,c,p]),c&&a&&u)?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)(r.A,{fullScreen:!0})};var d=a(20148);let o=e=>{let{children:t,allowedRoles:a,fallbackPath:u="/dashboard"}=e,{user:o}=(0,i.d4)(e=>e.auth),c=(0,l.useRouter)();return((0,s.useEffect)(()=>{o&&!a.includes(o.role)&&c.push(u)},[o,a,c,u]),o)?a.includes(o.role)?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)("div",{className:"flex h-screen w-full items-center justify-center",children:(0,n.jsx)(d.A,{message:"Access Denied",description:"You don't have permission to access this page. This area requires ".concat(a.join(" or ")," role."),type:"error",showIcon:!0})}):(0,n.jsx)(r.A,{fullScreen:!0})};var c=a(70854),m=a(21455),p=a.n(m);let y=e=>{let{children:t}=e,{user:a}=(0,i.d4)(e=>e.auth),u=(0,l.useRouter)(),d=(0,l.usePathname)(),{needsPayment:o,status:m,isActive:y,daysRemaining:S}=(0,c._)();if((0,s.useRef)(null==a?void 0:a.paymentStatus),(0,i.wA)(),console.log("\uD83D\uDEE1️ PaymentGuard COMPONENT RENDERED - This confirms PaymentGuard is being called"),console.log("\uD83D\uDEE1️ PaymentGuard - Payment Status Check:",{userRole:null==a?void 0:a.role,userPaymentStatus:null==a?void 0:a.paymentStatus,needsPayment:o,status:m,isActive:y,daysRemaining:S,pathname:d,lastPaymentDate:null==a?void 0:a.lastPaymentDate,nextPaymentDue:null==a?void 0:a.nextPaymentDue,createdAt:null==a?void 0:a.createdAt}),(null==a?void 0:a.createdAt)&&(null==a?void 0:a.paymentStatus)==="paid"&&!(null==a?void 0:a.lastPaymentDate)){let e=p()().diff(p()(a.createdAt),"day");console.log("\uD83C\uDF81 PaymentGuard - FREE TRIAL USER DETECTED:",{email:a.email,daysSinceCreation:e,isInFreeTrial:e<=90,trialDaysRemaining:Math.max(0,90-e),paymentStatus:a.paymentStatus,nextPaymentDue:a.nextPaymentDue})}if((null==a?void 0:a.paymentStatus)==="pending"&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER DETECTED! Should redirect to payment page"),(null==a?void 0:a.paymentStatus)!=="paid"){let e=sessionStorage.getItem("lastPaymentCheck"),t=Date.now();(!e||t-parseInt(e)>3e4)&&(console.log("\uD83D\uDD04 PaymentGuard - Forcing user data refresh due to non-paid status"),sessionStorage.setItem("lastPaymentCheck",t.toString()),window.__FORCE_REFRESH_USER_DATA&&window.__FORCE_REFRESH_USER_DATA())}if((0,s.useEffect)(()=>{a&&"superadmin"!==a.role&&"paid"!==a.paymentStatus&&u.replace("/payment")},[a,u]),(0,s.useEffect)(()=>{if(!a||"superadmin"===a.role)return;let e=d.includes("/profile")||d.includes("/dashboard/profile"),t=!0===window.__PROFILE_UPDATE_IN_PROGRESS,n=window.__LAST_PROFILE_UPDATE_PATH||"";if(e||t){console.log("PaymentGuard - Skipping payment check:",{isProfilePage:e,isProfileUpdateInProgress:t,pathname:d,lastProfileUpdatePath:n});return}let s="/payment"===d;console.log("PaymentGuard - Payment check status:",{pathname:d,isProfilePage:e,isExemptPath:s,needsPayment:o});let i=o&&!s&&"paid"!==a.paymentStatus||!s&&"paid"!==a.paymentStatus;console.log("\uD83D\uDD0D PaymentGuard - Redirect decision breakdown:",{needsPayment:o,isExemptPath:s,userPaymentStatus:a.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==a.paymentStatus,shouldRedirectToPayment:i,pathname:d}),"pending"===a.paymentStatus&&console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER REDIRECT CHECK:",{needsPayment:o,isExemptPath:s,userPaymentStatus:a.paymentStatus,shouldRedirectToPayment:i,"Will redirect?":i?"YES":"NO"}),i?(console.log("\uD83D\uDEA8 PaymentGuard - Redirecting to payment page from:",d),u.push("/payment")):"paid"===a.paymentStatus&&o?console.log("⚠️ PaymentGuard - User has paid status but needsPayment is true. This might indicate a cache issue."):"pending"!==a.paymentStatus||i||console.log("\uD83D\uDEA8 PaymentGuard - PENDING USER NOT REDIRECTED! This is the bug!")},[a,o,u,d]),(0,s.useEffect)(()=>{a&&"superadmin"!==a.role&&"paid"!==a.paymentStatus&&"/payment"!==window.location.pathname&&u.replace("/payment")},[a,u]),!a)return(0,n.jsx)(r.A,{tip:"Checking payment status...",fullScreen:!0});if("superadmin"===a.role)return(0,n.jsx)(n.Fragment,{children:t});let f=d.includes("/profile")||d.includes("/dashboard/profile"),h=!0===window.__PROFILE_UPDATE_IN_PROGRESS,P=window.__LAST_PROFILE_UPDATE_PATH||"";if(f||h)return console.log("PaymentGuard (render) - Skipping payment check:",{isProfilePage:f,isProfileUpdateInProgress:h,pathname:d,lastProfileUpdatePath:P}),(0,n.jsx)(n.Fragment,{children:t});let E="/payment"===d,g=o&&!E&&"paid"!==a.paymentStatus||!E&&"paid"!==a.paymentStatus;return(console.log("\uD83C\uDFA8 PaymentGuard (render) - Final render decision:",{needsPayment:o,isExemptPath:E,userPaymentStatus:a.paymentStatus,'user.paymentStatus !== "paid"':"paid"!==a.paymentStatus,shouldShowPaymentLoading:g,pathname:d}),g)?(0,n.jsx)(r.A,{tip:"Checking payment status...",fullScreen:!0}):(0,n.jsx)(n.Fragment,{children:t})},S=e=>{let{children:t,allowedRoles:a=["superadmin","admin","cashier"],checkPayment:s=!0,fallbackPath:i="/dashboard"}=e;return(0,n.jsx)(u,{children:s?(0,n.jsx)(y,{children:(0,n.jsx)(o,{allowedRoles:a,fallbackPath:i,children:t})}):(0,n.jsx)(o,{allowedRoles:a,fallbackPath:i,children:t})})}},29395:(e,t,a)=>{a.d(t,{A:()=>l});var n=a(95155);a(12115);var s=a(72093),i=a(16419);let l=e=>{let{size:t="large",fullScreen:a=!1,tip:l}=e,r=(0,n.jsx)(i.A,{style:{fontSize:24},spin:!0});return a?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80",children:(0,n.jsx)(s.A,{size:t,indicator:r,tip:l})}):(0,n.jsx)("div",{className:"flex flex-col h-full w-full items-center justify-center py-8",children:(0,n.jsx)(s.A,{size:t,indicator:r,tip:l})})}},70854:(e,t,a)=>{a.d(t,{_:()=>r});var n=a(12115),s=a(83391),i=a(21455),l=a.n(i);let r=()=>{let e=(0,s.d4)(e=>e.auth.user),[t,a]=(0,n.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,n.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,n=!1,s=!0,i="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){n=!0,s=!1,i="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let i=l()(e.nextPaymentDue),r=l()();if(t=i.diff(r,"day"),a){let a=l()().diff(l()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:n,needsPayment:s})}}}else"pending"===e.paymentStatus?(n=!1,s=!0,i="pending"):"overdue"===e.paymentStatus?(n=!1,s=!0,i="overdue"):(n=!1,s=!0,i="inactive");a({isActive:n,daysRemaining:t,status:i,needsPayment:s})},[e]),t}}}]);