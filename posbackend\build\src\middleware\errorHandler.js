"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = exports.AuthorizationError = exports.AuthenticationError = exports.NotFoundError = exports.ValidationError = exports.NetworkError = exports.DatabaseError = exports.ApiError = void 0;
const responseHelper_1 = require("../utils/responseHelper");
// Custom error class for API errors
class ApiError extends Error {
    constructor(statusCode, message, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.ApiError = ApiError;
// Database connection error handler
class DatabaseError extends ApiError {
    constructor(message) {
        super(503, message || 'Database connection error', true);
    }
}
exports.DatabaseError = DatabaseError;
// Network error handler
class NetworkError extends ApiError {
    constructor(message) {
        super(503, message || 'Network error', true);
    }
}
exports.NetworkError = NetworkError;
// Validation error
class ValidationError extends ApiError {
    constructor(message) {
        super(400, message || 'Validation error', true);
    }
}
exports.ValidationError = ValidationError;
// Not found error
class NotFoundError extends ApiError {
    constructor(message) {
        super(404, message || 'Resource not found', true);
    }
}
exports.NotFoundError = NotFoundError;
// Authentication error
class AuthenticationError extends ApiError {
    constructor(message) {
        super(401, message || 'Authentication error', true);
    }
}
exports.AuthenticationError = AuthenticationError;
// Authorization error
class AuthorizationError extends ApiError {
    constructor(message) {
        super(403, message || 'Authorization error', true);
    }
}
exports.AuthorizationError = AuthorizationError;
// Global error handler middleware
const errorHandler = (err, req, res, next) => {
    console.error('Error caught by global handler:', {
        name: err.name,
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        body: req.body,
    });
    // Handle specific error types
    if (err instanceof ApiError) {
        return (0, responseHelper_1.sendResponse)(res, err.statusCode, false, err.message);
    }
    // Handle PostgreSQL specific errors
    if (err.name === 'PostgresError' || err.message.includes('database') || err.message.includes('sql')) {
        return (0, responseHelper_1.sendResponse)(res, 503, false, 'Database error occurred. Please try again later.');
    }
    // Handle network errors
    if (err.name === 'FetchError' ||
        err.message.includes('network') ||
        err.message.includes('ECONNREFUSED') ||
        err.message.includes('ETIMEDOUT') ||
        err.message.includes('ENOTFOUND')) {
        return (0, responseHelper_1.sendResponse)(res, 503, false, 'Network error occurred. Please check your connection and try again.');
    }
    // Handle JSON parsing errors
    if (err instanceof SyntaxError && 'body' in err) {
        return (0, responseHelper_1.sendResponse)(res, 400, false, 'Invalid JSON in request body');
    }
    // Handle validation errors (e.g., from Zod)
    if (err.name === 'ZodError') {
        return (0, responseHelper_1.sendResponse)(res, 400, false, `Validation error: ${err.message}`);
    }
    // Default error handler for unhandled errors
    return (0, responseHelper_1.sendResponse)(res, 500, false, process.env.NODE_ENV === 'production'
        ? 'An unexpected error occurred. Please try again later.'
        : `Internal Server Error: ${err.message}`);
};
exports.errorHandler = errorHandler;
// Async handler to catch errors in async route handlers
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
