(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7810],{64866:(e,t,a)=>{Promise.resolve().then(a.bind(a,42261))},42261:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>M});var s=a(95155),n=a(12115),l=a(21614),r=a(27114),o=a(71349),i=a(43316),c=a(22810),d=a(2796),m=a(89801),u=a(72093),x=a(19397),h=a(53452),y=a(97139),p=a(45556),f=a(16419),g=a(1227),v=a(36060),b=a(30555),j=a(93968),N=a(10927),A=a(21455),k=a.n(A),S=a(10142),w=a(42531),P=a(36197);let{Option:C}=l.A,M=()=>{var e,t;let{user:a}=(0,v.A)(),A=(0,b.a)(),[M,D]=(0,n.useState)("stock-levels"),[R,I]=(0,n.useState)("all"),[L,_]=(0,n.useState)(!0),{data:E,isLoading:T,refetch:Y}=(0,j.r3)({page:1,limit:1e3,search:""}),{data:F,isLoading:U}=(0,N.lg)({page:1,limit:100,search:""}),O=T||U,{inventoryStats:z,inventoryData:V}=(0,n.useMemo)(()=>{var e,t;let a=(null==E?void 0:null===(e=E.data)||void 0===e?void 0:e.products)||[],s=((null==F?void 0:null===(t=F.data)||void 0===t?void 0:t.categories)||[]).reduce((e,t)=>(e[t.id]=t.name,e),{}),n="all"===R?a:a.filter(e=>{let t=s[e.categoryId];return(null==t?void 0:t.toLowerCase())===R.toLowerCase()}),l=n.length,r=n.filter(e=>e.stockQuantity>0&&e.minStockLevel&&e.stockQuantity<=e.minStockLevel).length;return{inventoryStats:{totalProducts:l,lowStockItems:r,outOfStockItems:n.filter(e=>0===e.stockQuantity).length,totalValue:n.reduce((e,t)=>e+t.stockQuantity*parseFloat(t.price),0)},inventoryData:n.map(e=>{let t=e.minStockLevel||5,a="In Stock";return 0===e.stockQuantity?a="Out of Stock":e.stockQuantity<=t&&(a="Low Stock"),{key:e.id.toString(),id:e.id,name:e.name,category:s[e.categoryId]||"Unknown",currentStock:e.stockQuantity,minStock:t,status:a,value:e.stockQuantity*parseFloat(e.price),price:parseFloat(e.price),sku:e.sku||"N/A"}})}},[E,F,R]),[B,Q]=(0,n.useState)(1),[G,K]=(0,n.useState)(10),W=V;"low-stock"===M?W=V.filter(e=>"Low Stock"===e.status):"out-of-stock"===M?W=V.filter(e=>"Out of Stock"===e.status):"stock-levels"===M?W=V:"movement"===M&&(W=V);let H=W.slice((B-1)*G,B*G);return(0,s.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,s.jsx)(o.A,{title:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"text-blue-600"}),(0,s.jsx)("span",{className:"text-gray-800",children:"Inventory Reports"})]}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:A?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,s.jsxs)("div",{className:A?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,s.jsx)(i.Ay,{type:"primary",icon:(0,s.jsx)(h.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){r.Ay.error("Please allow popups to print inventory report");return}let t='\n      <html>\n        <head>\n          <title>Inventory Report</title>\n          <style>\n            body { font-family: Arial, sans-serif; }\n            table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n            th { background-color: #f5f5f5; }\n            .header { text-align: center; margin-bottom: 20px; }\n            .date { color: #666; font-size: 0.9em; }\n            @media print { .no-print { display: none; } }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h2>Inventory Report</h2>\n            <p class="date">Generated on: '.concat(k()().format("MMMM D, YYYY h:mm A"),"</p>\n          </div>\n          <table>\n            <thead>\n              <tr>\n                <th>SKU</th>\n                <th>Product Name</th>\n                <th>Category</th>\n                ").concat("movement"===M||"valuation"===M?"<th>Month</th>":"","\n                <th>Current Stock</th>\n                <th>Min Stock</th>\n                <th>Unit Price</th>\n                <th>Status</th>\n                <th>Total Value</th>\n              </tr>\n            </thead>\n            <tbody>\n              ").concat(W.map(e=>"\n                <tr>\n                  <td>".concat(e.sku,"</td>\n                  <td>").concat(e.name,"</td>\n                  <td>").concat(e.category,"</td>\n                  ").concat("movement"===M||"valuation"===M?"<td>".concat(k()().format("MMMM YYYY"),"</td>"):"","\n                  <td>").concat(e.currentStock,"</td>\n                  <td>").concat(e.minStock,"</td>\n                  <td>₵").concat(e.price.toFixed(2),"</td>\n                  <td>").concat(e.status,"</td>\n                  <td>₵").concat(e.value.toFixed(2),"</td>\n                </tr>\n              ")).join(""),'\n              <tr>\n                <td colspan="').concat("movement"===M||"valuation"===M?"8":"7",'" style="font-weight:bold; text-align:right;">Total Value</td>\n                <td style="font-weight:bold;">₵').concat(W.reduce((e,t)=>e+t.value,0).toFixed(2),'</td>\n              </tr>\n            </tbody>\n          </table>\n          <div class="no-print" style="margin-top: 20px; text-align: center;">\n            <button onclick="window.print()">Print Report</button>\n          </div>\n        </body>\n      </html>\n    ');e.document.write(t),e.document.close()},size:A?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:A?"":"Print"}),(0,s.jsx)(i.Ay,{type:"primary",icon:(0,s.jsx)(y.A,{}),onClick:()=>{let e=new S.uE;e.setFontSize(16),e.text("Inventory Report",14,15),e.setFontSize(10),e.text("Generated on: ".concat(k()().format("MMMM D, YYYY h:mm A")),14,22);let t=W.map(e=>[e.sku,e.name,e.category,..."movement"===M||"valuation"===M?[k()().format("MMMM YYYY")]:[],e.currentStock,e.minStock,"₵".concat(e.price.toFixed(2)),e.status,"₵".concat(e.value.toFixed(2))]);t.push([{content:"Total Value",colSpan:9,styles:{fontStyle:"bold",halign:"right"}},{content:"₵".concat(W.reduce((e,t)=>e+t.value,0).toFixed(2)),styles:{fontStyle:"bold"}}]),(0,w.Ay)(e,{head:[["SKU","Product Name","Category",..."movement"===M||"valuation"===M?["Month"]:[],"Current Stock","Min Stock","Unit Price","Status","Total Value"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("inventory-report.pdf")},size:A?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:A?"":"Export"})]}),children:(0,s.jsxs)("div",{className:"w-full space-y-6",children:[(0,s.jsx)(o.A,{title:"Report Filters",size:"small",className:"bg-gray-50",children:(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(d.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Report Type"}),(0,s.jsxs)(l.A,{value:M,onChange:D,className:"w-full",children:[(0,s.jsx)(C,{value:"stock-levels",children:"Stock Levels"}),(0,s.jsx)(C,{value:"low-stock",children:"Low Stock Alert"}),(0,s.jsx)(C,{value:"out-of-stock",children:"Out of Stock"}),(0,s.jsx)(C,{value:"valuation",children:"Inventory Valuation"}),(0,s.jsx)(C,{value:"movement",children:"Stock Movement"})]})]})}),(0,s.jsx)(d.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category"}),(0,s.jsxs)(l.A,{value:R,onChange:I,className:"w-full",children:[(0,s.jsx)(C,{value:"all",children:"All Categories"}),null==F?void 0:null===(t=F.data)||void 0===t?void 0:null===(e=t.categories)||void 0===e?void 0:e.map(e=>(0,s.jsx)(C,{value:e.name.toLowerCase(),children:e.name},e.id))]})]})}),(0,s.jsx)(d.A,{xs:24,sm:24,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xa0"}),(0,s.jsx)(i.Ay,{type:"primary",onClick:()=>{_(!0),Y(),r.Ay.success("Inventory report generated successfully!")},loading:O,className:"w-full bg-blue-600 hover:bg-blue-700",icon:(0,s.jsx)(x.A,{}),children:"Generate Report"})]})})]})}),(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(m.A,{title:"Total Products",value:z.totalProducts,valueStyle:{color:"#1890ff",fontSize:"18px"},className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(m.A,{title:"Low Stock Items",value:z.lowStockItems,valueStyle:{color:"#fa8c16",fontSize:"18px"},suffix:(0,s.jsx)(p.A,{}),className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(m.A,{title:"Out of Stock",value:z.outOfStockItems,valueStyle:{color:"#f5222d",fontSize:"18px"},suffix:(0,s.jsx)(p.A,{}),className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(m.A,{title:"Total Value",value:z.totalValue,precision:2,prefix:"₵",valueStyle:{color:"#3f8600",fontSize:"18px"},className:"w-full"})})})]}),(0,s.jsx)(o.A,{title:"Inventory Details",children:O?(0,s.jsx)("div",{className:"flex h-60 items-center justify-center",children:(0,s.jsx)(u.A,{indicator:(0,s.jsx)(f.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):L?"movement"===M?(0,s.jsxs)("div",{className:"text-center text-blue-700 py-10",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Stock Movement Tracking Coming Soon"}),(0,s.jsx)("p",{className:"text-sm",children:"Stock movement logs, adjustments, and history will be available in future updates."})]}):H.length>0?(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[800px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"SKU"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Product Name"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Category"}),"movement"===M||"valuation"===M?(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Month"}):null,(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Current Stock"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Min Stock"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Unit Price"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Status"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Total Value"})]})}),(0,s.jsx)("tbody",{children:H.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.sku}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.name}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.category}),"movement"===M||"valuation"===M?(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:k()().format("MMMM YYYY")}):null,(0,s.jsx)("td",{className:"px-4 py-3 text-sm font-semibold ".concat(0===e.currentStock?"text-red-600":e.currentStock<=e.minStock?"text-orange-600":"text-green-600"),children:e.currentStock}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.minStock}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:["₵",e.price.toFixed(2)]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("Out of Stock"===e.status?"bg-red-100 text-red-800":"Low Stock"===e.status?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:["Out of Stock"===e.status&&(0,s.jsx)(p.A,{className:"mr-1"}),"Low Stock"===e.status&&(0,s.jsx)(p.A,{className:"mr-1"}),"In Stock"===e.status&&(0,s.jsx)(g.A,{className:"mr-1"}),e.status]})}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.value.toFixed(2)]})]},e.key))})]})}),(0,s.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t text-sm text-gray-600",children:["Showing ",H.length," products"]})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("p",{children:"out-of-stock"===M?"No out of stock products found.":"No products found for the selected criteria."}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"all"!==R?"Try selecting 'All Categories' or check if products exist in this category.":"No products have been added to the system yet."})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-20",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("p",{children:'Click "Generate Report" to view inventory details'}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Product data will be displayed here"})]})}),(0,s.jsx)(P.A,{current:B,pageSize:G,total:W.length,onChange:e=>Q(e),isMobile:A}),(0,s.jsx)(o.A,{className:"bg-blue-50 border-blue-200",children:(0,s.jsxs)("div",{className:"text-center text-blue-700",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCE6 Advanced Inventory Analytics Coming Soon"}),(0,s.jsx)("p",{className:"text-sm",children:"Stock movement tracking, reorder suggestions, and ABC analysis will be available in future updates."})]})})]})})})}},36197:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(95155);a(12115);var n=a(33621),l=a(44549);let r=e=>{let{current:t,pageSize:a,total:r,onChange:o,isMobile:i=!1}=e,c=Math.ceil(r/a);return 0===r?null:(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,s.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*a+1})," to"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*a,r)})," of"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:r})," results"]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,s.jsxs)("button",{onClick:()=>o(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("span",{className:"sr-only",children:"Previous"}),(0,s.jsx)(n.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,c)},(e,a)=>{let n=a+1;return(0,s.jsx)("button",{onClick:()=>o(n),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===n?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:n},n)}),(0,s.jsxs)("button",{onClick:()=>o(t+1),disabled:t>=c,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=c?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("span",{className:"sr-only",children:"Next"}),(0,s.jsx)(l.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,s.jsx)("button",{onClick:()=>o(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",c]}),(0,s.jsx)("button",{onClick:()=>o(t+1),disabled:t>=c,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=c?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})}},30555:(e,t,a)=>{"use strict";a.d(t,{a:()=>n});var s=a(12115);function n(){let[e,t]=(0,s.useState)();return(0,s.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),a=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",a),()=>e.removeEventListener("change",a)},[]),!!e}},36060:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var s=a(83391),n=a(70854),l=a(63065),r=a(7875);let o=()=>{let e=(0,s.wA)(),{user:t,accessToken:a}=(0,s.d4)(e=>e.auth),o=(0,n._)(),{refetch:i}=(0,l.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!a,d=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let s=await i();console.log("useAuth - Refetch result:",s);let n=s.data;if((null==n?void 0:n.success)&&(null==n?void 0:n.data)){console.log("useAuth - API response data:",n.data);let s=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let l=n.data.phone||t.phone||"",o=n.data.createdAt||t.createdAt||"",i=n.data.lastPaymentDate||t.lastPaymentDate||void 0,c=n.data.nextPaymentDue||t.nextPaymentDue||null,d=n.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:n.data.phone,userPhone:t.phone,finalPhone:l,apiCreatedAt:n.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:n.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:n.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:n.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let m={...n.data,phone:l,createdAt:o,lastPaymentDate:i,nextPaymentDue:c,createdBy:d,paymentStatus:s};console.log("useAuth - Updating Redux store with:",m),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,r.gV)({user:m,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==n?void 0:n.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:d}}},70854:(e,t,a)=>{"use strict";a.d(t,{_:()=>o});var s=a(12115),n=a(83391),l=a(21455),r=a.n(l);let o=()=>{let e=(0,n.d4)(e=>e.auth.user),[t,a]=(0,s.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,s.useEffect)(()=>{if(!e){a({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,s=!1,n=!0,l="inactive";if("superadmin"===e.role){a({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){s=!0,n=!1,l="active";let a=!e.lastPaymentDate;if(e.nextPaymentDue){let l=r()(e.nextPaymentDue),o=r()();if(t=l.diff(o,"day"),a){let a=r()().diff(r()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:a,daysRemaining:t,trialDaysUsed:a,trialDaysRemaining:t,isActive:s,needsPayment:n})}}}else"pending"===e.paymentStatus?(s=!1,n=!0,l="pending"):"overdue"===e.paymentStatus?(s=!1,n=!0,l="overdue"):(s=!1,n=!0,l="inactive");a({isActive:s,daysRemaining:t,status:l,needsPayment:n})},[e]),t}}},e=>{var t=t=>e(e.s=t);e.O(0,[3930,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,1349,4798,2375,1614,7114,8642,2998,821,8441,1517,7358],()=>t(64866)),_N_E=e.O()}]);