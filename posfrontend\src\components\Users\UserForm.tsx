"use client";

import React, { useEffect } from "react";
import { Modal, Form, Input, Select, Button, message } from "antd";
import {
  useCreateUserMutation,
  useUpdateUserMutation
} from "@/reduxRTK/services/authApi";
import { User, CreateUserDto, UpdateUserDto, UserRole, PaymentStatus, ApiResponse } from "@/types/user";

const { Option } = Select;

interface UserFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  user?: User | null;
  currentUser?: User | null;
}

const UserForm: React.FC<UserFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  user,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!user;

  // RTK Query hooks
  const [createUser, { isLoading: isCreating }] = useCreateUserMutation();
  const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();

  const isLoading = isCreating || isUpdating;

  // Reset form when modal opens/closes or user changes
  useEffect(() => {
    if (visible) {
      if (user) {
        // In edit mode, pre-fill the form with user data
        form.setFieldsValue({
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          paymentStatus: user.paymentStatus,
        });
      } else {
        // In create mode, reset the form
        form.resetFields();
        // Set default role based on current user's role
        if (currentUser?.role === "admin") {
          form.setFieldsValue({ role: "cashier" });
        }
      }
    }
  }, [visible, user, form, currentUser]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEditMode && user) {
        // Update existing user
        const updateData: UpdateUserDto = {
          name: values.name,
          email: values.email,
          phone: values.phone,
        };

        // Only include role if current user can change it
        if (currentUser?.role === "superadmin" ||
            (currentUser?.role === "admin" && values.role === "cashier")) {
          updateData.role = values.role;
        }

        // Only superadmin and admin can update payment status
        if (currentUser?.role === "superadmin" || currentUser?.role === "admin") {
          updateData.paymentStatus = values.paymentStatus;
        }

        const result = await updateUser({
          userId: user.id,
          data: updateData
        }).unwrap() as ApiResponse<any>;

        if (!result.success) {
          throw new Error(result.message || "Failed to update user");
        }

        message.success("User updated successfully");
      } else {
        // Create new user
        const createData: CreateUserDto = {
          name: values.name,
          email: values.email,
          password: values.password,
          phone: values.phone,
        };

        // Only include role if current user can set it
        if (currentUser?.role === "superadmin") {
          createData.role = values.role;
        } else if (currentUser?.role === "admin") {
          createData.role = "cashier"; // Admins can only create cashiers
        }

        const result = await createUser(createData).unwrap() as ApiResponse<any>;

        if (!result.success) {
          throw new Error(result.message || "Failed to create user");
        }

        message.success("User created successfully");
      }

      onSuccess();
    } catch (error: any) {
      console.error("Form submission error:", error);
      message.error(error.message || "Failed to save user");
    }
  };

  // Determine which roles the current user can assign
  const getAvailableRoles = () => {
    if (currentUser?.role === "superadmin") {
      return ["superadmin", "admin", "cashier"];
    } else if (currentUser?.role === "admin") {
      return ["cashier"];
    }
    return [];
  };

  const availableRoles = getAvailableRoles();

  return (
    <Modal
      title={isEditMode ? "Edit User" : "Add New User"}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={isLoading}
          onClick={handleSubmit}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>,
      ]}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ role: "cashier", paymentStatus: "pending" }}
      >
        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: "Please enter name" }]}
        >
          <Input placeholder="Enter full name" />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: "Please enter email" },
            { type: "email", message: "Please enter a valid email" },
          ]}
        >
          <Input placeholder="Enter email address" />
        </Form.Item>

        <Form.Item
          name="phone"
          label="Phone"
          rules={[
            { required: true, message: "Please enter phone number" },
            { min: 5, message: "Phone number must be at least 5 characters" },
          ]}
        >
          <Input placeholder="Enter phone number" />
        </Form.Item>

        {!isEditMode && (
          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: "Please enter password" },
              { min: 6, message: "Password must be at least 6 characters" },
            ]}
          >
            <Input.Password placeholder="Enter password" />
          </Form.Item>
        )}

        {/* Only show role selector if user has permission to change roles */}
        {availableRoles.length > 0 && (
          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: "Please select role" }]}
          >
            <Select placeholder="Select role">
              {availableRoles.map((role) => (
                <Option key={role} value={role}>
                  {role === "superadmin"
                    ? "Super Admin"
                    : role === "admin"
                      ? "Admin"
                      : "Cashier"}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Only show payment status in edit mode for admin/superadmin */}
        {isEditMode &&
         (currentUser?.role === "superadmin" || currentUser?.role === "admin") && (
          <Form.Item
            name="paymentStatus"
            label="Payment Status"
            rules={[{ required: true, message: "Please select payment status" }]}
          >
            <Select placeholder="Select payment status">
              <Option value="pending">Pending</Option>
              <Option value="paid">Paid</Option>
              <Option value="overdue">Overdue</Option>
              <Option value="inactive">Inactive</Option>
            </Select>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default UserForm;

