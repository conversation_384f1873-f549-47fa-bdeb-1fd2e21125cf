{"name": "nexapo", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "deploy:build": "next build && next export", "deploy:production": "NODE_ENV=production npm run deploy:build"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@reduxjs/toolkit": "^2.6.1", "@types/react-responsive": "^8.0.8", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "antd": "^5.21.6", "apexcharts": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "html2canvas": "^1.4.1", "idb": "^8.0.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.263.1", "next": "15.1.6", "next-pwa": "^5.6.0", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "react": "^18", "react-apexcharts": "^1.7.0", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "redux-persist": "^6.0.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5"}}