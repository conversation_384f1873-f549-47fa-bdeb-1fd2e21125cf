"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[821],{32904:(e,t,a)=>{a.d(t,{V:()=>i});let o="https://nexapoapi.up.railway.app/api/v1",n=async(e,t,a)=>{try{let n;console.log("API Request:",{url:"".concat(o).concat(t),method:"POST",payload:e,hasToken:!!a});let r={method:"POST",headers:{Accept:"application/json","Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(e),credentials:"same-origin",mode:"cors"},i=await fetch("".concat(o).concat(t),r);try{n=await i.json()}catch(e){return console.error("Failed to parse JSON response:",e),{success:!1,message:"Failed to parse server response: ".concat(i.statusText||"Unknown error")}}if(i.ok)return{success:!0,message:n.message||"Action completed successfully",data:n.data||null};return console.error("API Error Response:",{status:i.status,statusText:i.statusText,result:n}),{success:!1,message:n.message||"Request failed with status: ".concat(i.status," ").concat(i.statusText)}}catch(e){return console.error("API call failed:",e),{success:!1,message:e instanceof TypeError&&e.message.includes("fetch")?"Network error: Unable to connect to the server. Please check your internet connection.":"Error: ".concat(e.message||"Unable to complete action. An unknown error occurred!")}}},r=async(e,t)=>{try{let a;console.log("Login API Request:",{url:"".concat(o).concat(e),method:"POST",payload:t});let n={method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t),credentials:"same-origin",mode:"cors"},r=await fetch("".concat(o).concat(e),n);try{a=await r.json()}catch(e){return console.error("Failed to parse JSON response:",e),{success:!1,message:"Failed to parse server response: ".concat(r.statusText||"Unknown error")}}if(r.ok)return{success:!0,message:a.message||"Login successful",data:a.data||null};return console.error("Login API Error Response:",{status:r.status,statusText:r.statusText,result:a}),{success:!1,message:a.message||"Login failed with status: ".concat(r.status," ").concat(r.statusText)}}catch(e){return console.error("Login API call failed:",e),{success:!1,message:e instanceof TypeError&&e.message.includes("fetch")?"Network error: Unable to connect to the server. Please check your internet connection.":"Error: ".concat(e.message||"Unable to login. An unknown error occurred!")}}},i=async e=>{let{urlpath:t,payloaddata:a,token:o}=e;try{if("/login"===t||"/logout"===t){let e=await r(t,a);if(e.success)return{data:e};return{error:e}}if(o){console.log("Making authenticated request:",{urlpath:t,payloaddata:a,hasToken:!!o});try{if(!o.trim())return console.error("Empty token provided for authenticated request"),{error:{success:!1,message:"Authentication token is empty. Please log in again."}};let e=await n(a,t,o);if(console.log("Authenticated request result:",{success:e.success,urlpath:t}),e.success)return{data:e};return console.error("API error:",{urlpath:t,error:e.message}),{error:e}}catch(e){return console.error("API call exception:",{urlpath:t,error:e}),{error:{success:!1,message:"Error in API call: ".concat(e.message||"Unknown error")}}}}console.error("Invalid API call - No token provided or invalid request structure",{urlpath:t,hasToken:!!o,payloaddata:a});let e="Authentication error";return e=o?t?"Invalid API call - Please check your request structure.":"Invalid API call - No URL path provided.":"No authentication token provided. Please log in again.",{error:{success:!1,message:e}}}catch(e){return console.error("API call failed:",e),{error:{success:!1,message:"An error occurred during the API call"}}}}},63065:(e,t,a)=>{a.d(t,{$f:()=>d,H7:()=>g,HC:()=>h,Qg:()=>m,SV:()=>y,VL:()=>u,go:()=>c,h8:()=>s,i$:()=>i,i0:()=>l,qx:()=>p});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"userApi",baseQuery:n.V,tagTypes:["User"],endpoints:e=>({loginUser:e.mutation({query:e=>({urlpath:"/login",payloaddata:e})}),createUser:e.mutation({query:e=>{let t=r.M.getState().auth.accessToken||void 0;return{urlpath:"/users",payloaddata:{mode:"createnew",...e},token:t}},invalidatesTags:["User"]}),getAllUsers:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth.accessToken||void 0;return console.log("getAllUsers query params:",{page:t,limit:a,search:o}),{urlpath:"/users",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:n}},keepUnusedDataFor:0,providesTags:["User"]}),getUserById:e.query({query:e=>({urlpath:"/users",payloaddata:{mode:"retrieve",userId:e},token:r.M.getState().auth.accessToken||void 0}),providesTags:["User"]}),getCurrentUser:e.query({query:()=>({urlpath:"/users",payloaddata:{mode:"current"},token:r.M.getState().auth.accessToken||void 0}),providesTags:["User"]}),updateUser:e.mutation({query:e=>{let{userId:t,data:a}=e,o=r.M.getState().auth.accessToken||void 0;return{urlpath:"/users",payloaddata:{mode:"update",userId:t,...a},token:o}},invalidatesTags:["User"]}),deleteUser:e.mutation({query:e=>({urlpath:"/users",payloaddata:{mode:"delete",userId:e},token:r.M.getState().auth.accessToken||void 0}),invalidatesTags:["User"]}),bulkDeleteUsers:e.mutation({query:e=>({urlpath:"/users",payloaddata:{mode:"delete",userIds:e},token:r.M.getState().auth.accessToken||void 0}),invalidatesTags:["User"]}),logoutUser:e.mutation({query:e=>({urlpath:"/logout",method:"POST",payloaddata:e})}),changePassword:e.mutation({query:e=>{let{userId:t,currentPassword:a,newPassword:o}=e;return{urlpath:"/users",payloaddata:{mode:"update",userId:t,currentPassword:a,newPassword:o,passwordChange:!0},token:r.M.getState().auth.accessToken||void 0}},invalidatesTags:["User"]})})}),{useLoginUserMutation:s,useCreateUserMutation:l,useGetAllUsersQuery:u,useGetUserByIdQuery:d,useGetCurrentUserQuery:c,useUpdateUserMutation:g,useDeleteUserMutation:h,useBulkDeleteUsersMutation:p,useLogoutUserMutation:m,useChangePasswordMutation:y}=i},7875:(e,t,a)=>{a.d(t,{Ay:()=>i,gV:()=>n,lM:()=>r});let o=(0,a(72212).Z0)({name:"auth",initialState:{user:null,accessToken:null},reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.accessToken=t.payload.accessToken},clearUser:e=>{e.user=null,e.accessToken=null}}}),{setUser:n,clearUser:r}=o.actions,i=o.reducer},10927:(e,t,a)=>{a.d(t,{BP:()=>i,OU:()=>l,XY:()=>u,ec:()=>c,ez:()=>d,gH:()=>g,lg:()=>s});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"categoryApi",baseQuery:n.V,tagTypes:["Category"],endpoints:e=>({getAllCategories:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(console.log("getAllCategories query params:",{page:t,limit:a,search:o}),!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["Category"]}),getCategoryById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"retrieve",categoryId:e},token:a}},providesTags:(e,t,a)=>[{type:"Category",id:a}]}),createCategory:e.mutation({query:e=>{var t;let a=r.M.getState().auth,o=(null==a?void 0:a.accessToken)||"";if(console.log("Creating category with token:",o?"Token exists (not showing for security)":"No token found"),console.log("Category data:",e),console.log("Auth state:",{hasUser:!!(null==a?void 0:a.user),hasToken:!!(null==a?void 0:a.accessToken),userRole:null==a?void 0:null===(t=a.user)||void 0===t?void 0:t.role}),!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"createnew",...e},token:o}},invalidatesTags:["Category"]}),updateCategory:e.mutation({query:e=>{let{categoryId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"update",categoryId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{categoryId:o}=a;return[{type:"Category",id:o},"Category"]}}),deleteCategory:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"delete",categoryId:e},token:a}},invalidatesTags:["Category"]}),bulkDeleteCategories:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/categories",payloaddata:{mode:"delete",categoryIds:e},token:a}},invalidatesTags:["Category"]})})}),{useGetAllCategoriesQuery:s,useGetCategoryByIdQuery:l,useCreateCategoryMutation:u,useUpdateCategoryMutation:d,useDeleteCategoryMutation:c,useBulkDeleteCategoriesMutation:g}=i},7165:(e,t,a)=>{a.d(t,{H:()=>l,P:()=>u});var o=a(29575),n=a(32904),r=a(90821),i=a(21455),s=a.n(i);let l=(0,o.xP)({reducerPath:"dashboardApi",baseQuery:n.V,tagTypes:["Dashboard"],endpoints:e=>({getDashboardStats:e.query({query:()=>{let e=r.M.getState().auth,t=(null==e?void 0:e.accessToken)||"";if(!t)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");let a=s()().startOf("month").format("YYYY-MM-DD"),o=s()().endOf("month").format("YYYY-MM-DD"),n=new Date().getTime(),i=Math.random().toString(36).substring(7);return{urlpath:"/dashboard",payloaddata:{mode:"stats",timestamp:n,randomId:i,sessionId:Date.now()+Math.random(),cacheBuster:"".concat(n,"_").concat(i),dateRange:{startDate:a,endDate:o}},token:t}},keepUnusedDataFor:0,providesTags:["Dashboard"]})})}),{useGetDashboardStatsQuery:u}=l},92353:(e,t,a)=>{a.d(t,{GH:()=>c,Nx:()=>u,cY:()=>s,cp:()=>d,z9:()=>i});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"expenseApi",baseQuery:n.V,tagTypes:["Expense"],endpoints:e=>({getAllExpenses:e.query({query:e=>{let{page:t=1,limit:a=10,search:o="",categoryId:n,startDate:i,endDate:s}=e,l=r.M.getState().auth,u=(null==l?void 0:l.accessToken)||"";if(!u)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"retrieve",page:t,limit:a,search:o,categoryId:n,startDate:i,endDate:s},token:u}},keepUnusedDataFor:0,providesTags:["Expense"]}),getExpenseById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"retrieve",expenseId:e},token:a}},providesTags:(e,t,a)=>[{type:"Expense",id:a}]}),createExpense:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Expense"]}),updateExpense:e.mutation({query:e=>{let{expenseId:t,expenseData:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"update",expenseId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{expenseId:o}=a;return[{type:"Expense",id:o},"Expense"]}}),deleteExpense:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"delete",expenseId:e},token:a}},invalidatesTags:["Expense"]}),getExpenseStats:e.query({query:function(){let{startDate:e,endDate:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=r.M.getState().auth,o=(null==a?void 0:a.accessToken)||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expenses",payloaddata:{mode:"stats",startDate:e,endDate:t},token:o}},keepUnusedDataFor:0,providesTags:["Expense"]})})}),{useGetAllExpensesQuery:s,useGetExpenseByIdQuery:l,useCreateExpenseMutation:u,useUpdateExpenseMutation:d,useDeleteExpenseMutation:c,useGetExpenseStatsQuery:g}=i},82461:(e,t,a)=>{a.d(t,{CQ:()=>d,DY:()=>u,J0:()=>i,L:()=>s,Uc:()=>c});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"expenseCategoryApi",baseQuery:n.V,tagTypes:["ExpenseCategory"],endpoints:e=>({getAllExpenseCategories:e.query({query:e=>{let{page:t=1,limit:a=50,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"retrieve",page:t,limit:a,search:o},token:i}},keepUnusedDataFor:300,providesTags:["ExpenseCategory"]}),getExpenseCategoryById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"retrieve",categoryId:e},token:a}},providesTags:(e,t,a)=>[{type:"ExpenseCategory",id:a}]}),createExpenseCategory:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["ExpenseCategory"]}),updateExpenseCategory:e.mutation({query:e=>{let{categoryId:t,categoryData:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"update",categoryId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{categoryId:o}=a;return[{type:"ExpenseCategory",id:o},"ExpenseCategory"]}}),deleteExpenseCategory:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/expense-categories",payloaddata:{mode:"delete",categoryId:e},token:a}},invalidatesTags:["ExpenseCategory"]})})}),{useGetAllExpenseCategoriesQuery:s,useGetExpenseCategoryByIdQuery:l,useCreateExpenseCategoryMutation:u,useUpdateExpenseCategoryMutation:d,useDeleteExpenseCategoryMutation:c}=i},78767:(e,t,a)=>{a.d(t,{Ex:()=>d,Zf:()=>i,nm:()=>s});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"paymentApi",baseQuery:n.V,tagTypes:["Payment"],endpoints:e=>({initializePayment:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"initialize",...e},token:a}},invalidatesTags:["Payment"]}),getPaymentHistory:e.query({query:e=>{let{page:t=1,limit:a=10}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"history",page:t,limit:a},token:n}},providesTags:["Payment"]}),getPaymentById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"retrieve",paymentId:e},token:a}},providesTags:["Payment"]}),verifyPaystackPayment:e.mutation({query:e=>{let{reference:t}=e,a=r.M.getState().auth,o=(null==a?void 0:a.accessToken)||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"verify-paystack",reference:t},token:o}},invalidatesTags:["Payment"]}),verifyPayment:e.mutation({query:e=>{let{transactionId:t}=e,a=r.M.getState().auth,o=(null==a?void 0:a.accessToken)||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/payment",payloaddata:{mode:"verify",transactionId:t},token:o}},invalidatesTags:["Payment"]}),updateUserPaymentStatus:e.mutation({query:e=>{let{userId:t,paymentStatus:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/users",payloaddata:{mode:"update",userId:t,paymentStatus:a},token:n}},invalidatesTags:["Payment"]})})}),{useInitializePaymentMutation:s,useGetPaymentHistoryQuery:l,useGetPaymentByIdQuery:u,useVerifyPaystackPaymentMutation:d,useVerifyPaymentMutation:c,useUpdateUserPaymentStatusMutation:g}=i},93968:(e,t,a)=>{a.d(t,{GQ:()=>i,IT:()=>h,Q$:()=>d,eb:()=>u,hi:()=>l,lY:()=>g,r3:()=>s,vM:()=>c});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"productApi",baseQuery:n.V,tagTypes:["Product"],endpoints:e=>({getAllProducts:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["Product"]}),getProductById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"retrieve",productId:e},token:a}},keepUnusedDataFor:0,providesTags:(e,t,a)=>[{type:"Product",id:a}]}),getProductByBarcode:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"barcode",barcode:e},token:a}},keepUnusedDataFor:0,providesTags:(e,t,a)=>[{type:"Product",id:"barcode-".concat(a)}]}),createProduct:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"createnew",productsData:[e]},token:a}},invalidatesTags:["Product"]}),updateProduct:e.mutation({query:e=>{let{productId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"update",productId:t,...a},token:n}},invalidatesTags:["Product"]}),deleteProduct:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"delete",productId:e},token:a}},invalidatesTags:["Product"]}),bulkDeleteProducts:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/products",payloaddata:{mode:"delete",productIds:e},token:a}},invalidatesTags:["Product"]})})}),{useGetAllProductsQuery:s,useGetProductByIdQuery:l,useGetProductByBarcodeQuery:u,useCreateProductMutation:d,useUpdateProductMutation:c,useDeleteProductMutation:g,useBulkDeleteProductsMutation:h}=i},77418:(e,t,a)=>{a.d(t,{PY:()=>c,Pt:()=>l,eT:()=>i,hZ:()=>s,o3:()=>u,oq:()=>g,xc:()=>d});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"purchaseApi",baseQuery:n.V,tagTypes:["Purchase"],endpoints:e=>({getAllPurchases:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["Purchase"]}),getPurchaseById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"retrieve",purchaseId:e},token:a}},providesTags:(e,t,a)=>[{type:"Purchase",id:a}]}),createPurchase:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Purchase"]}),updatePurchase:e.mutation({query:e=>{let{purchaseId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"update",purchaseId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{purchaseId:o}=a;return[{type:"Purchase",id:o},"Purchase"]}}),deletePurchase:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"delete",purchaseId:e},token:a}},invalidatesTags:["Purchase"]}),bulkDeletePurchases:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/purchases",payloaddata:{mode:"delete",purchaseIds:e},token:a}},invalidatesTags:["Purchase"]})})}),{useGetAllPurchasesQuery:s,useGetPurchaseByIdQuery:l,useCreatePurchaseMutation:u,useUpdatePurchaseMutation:d,useDeletePurchaseMutation:c,useBulkDeletePurchasesMutation:g}=i},47141:(e,t,a)=>{a.d(t,{Jy:()=>i,iB:()=>u,rN:()=>d,sK:()=>s});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"receiptApi",baseQuery:n.V,tagTypes:["Receipt"],endpoints:e=>({getAllReceipts:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"retrieve",page:t,limit:a,search:o},token:i}},providesTags:["Receipt"]}),getReceiptBySaleId:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"retrieveBySaleId",saleId:e},token:a}},providesTags:["Receipt"]}),deleteReceipt:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"delete",id:e},token:a}},invalidatesTags:["Receipt"]}),bulkDeleteReceipts:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/receipt",payloaddata:{mode:"delete",receiptIds:e},token:a}},invalidatesTags:["Receipt"]})})}),{useGetAllReceiptsQuery:s,useGetReceiptBySaleIdQuery:l,useDeleteReceiptMutation:u,useBulkDeleteReceiptsMutation:d}=i},21633:(e,t,a)=>{a.d(t,{E$:()=>u,J2:()=>i,JZ:()=>s,Kn:()=>g,Vk:()=>l,lz:()=>c});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"salesApi",baseQuery:n.V,tagTypes:["Sale"],endpoints:e=>({getAllSales:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["Sale"]}),getSaleById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"retrieve",saleId:e},token:a}},providesTags:(e,t,a)=>[{type:"Sale",id:a}]}),createSale:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"createnew",saleData:e},token:a}},invalidatesTags:["Sale"]}),updateSale:e.mutation({query:e=>{let{saleId:t,updateData:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"update",saleId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{saleId:o}=a;return[{type:"Sale",id:o}]}}),deleteSale:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"delete",saleId:e},token:a}},invalidatesTags:["Sale"]}),bulkDeleteSales:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/sales",payloaddata:{mode:"delete",saleIds:e},token:a}},invalidatesTags:["Sale"]})})}),{useGetAllSalesQuery:s,useGetSaleByIdQuery:l,useCreateSaleMutation:u,useUpdateSaleMutation:d,useDeleteSaleMutation:c,useBulkDeleteSalesMutation:g}=i},34366:(e,t,a)=>{a.d(t,{Wn:()=>u,sc:()=>i});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"stockAdjustmentApi",baseQuery:n.V,tagTypes:["StockAdjustment"],endpoints:e=>({getAllStockAdjustments:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["StockAdjustment"]}),getStockAdjustmentById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"retrieve",adjustmentId:e},token:a}},providesTags:(e,t,a)=>[{type:"StockAdjustment",id:a}]}),createStockAdjustment:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["StockAdjustment"]}),updateStockAdjustment:e.mutation({query:e=>{let{adjustmentId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"update",adjustmentId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{adjustmentId:o}=a;return[{type:"StockAdjustment",id:o},"StockAdjustment"]}}),deleteStockAdjustment:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stockadjustment",payloaddata:{mode:"delete",adjustmentId:e},token:a}},invalidatesTags:["StockAdjustment"]})})}),{useGetAllStockAdjustmentsQuery:s,useGetStockAdjustmentByIdQuery:l,useCreateStockAdjustmentMutation:u,useUpdateStockAdjustmentMutation:d,useDeleteStockAdjustmentMutation:c}=i},71518:(e,t,a)=>{a.d(t,{HP:()=>d,ST:()=>u,Vf:()=>c,g0:()=>i,tD:()=>s,vs:()=>l,yv:()=>g});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"storeApi",baseQuery:n.V,tagTypes:["Store"],endpoints:e=>({getAllStores:e.query({query:e=>{let{page:t=1,limit:a=10,search:o=""}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"retrieve",page:t,limit:a,search:o.trim()},token:i}},keepUnusedDataFor:0,providesTags:["Store"]}),getStoreById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"retrieve",storeId:e},token:a}},providesTags:(e,t,a)=>[{type:"Store",id:a}]}),createStore:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Store"]}),updateStore:e.mutation({query:e=>{let{storeId:t,updateData:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"update",storeId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{storeId:o}=a;return[{type:"Store",id:o}]}}),deleteStore:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"delete",storeId:e},token:a}},invalidatesTags:["Store"]}),bulkDeleteStores:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/stores",payloaddata:{mode:"delete",storeIds:e},token:a}},invalidatesTags:["Store"]})})}),{useGetAllStoresQuery:s,useGetStoreByIdQuery:l,useCreateStoreMutation:u,useUpdateStoreMutation:d,useDeleteStoreMutation:c,useBulkDeleteStoresMutation:g}=i},6564:(e,t,a)=>{a.d(t,{$i:()=>u,HC:()=>i,kH:()=>c,mP:()=>d,tr:()=>g,w$:()=>s,w1:()=>l});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"supplierApi",baseQuery:n.V,tagTypes:["Supplier"],endpoints:e=>({getAllSuppliers:e.query({query:e=>{var t;let{page:a=1,limit:o=10,search:n=""}=e,i=r.M.getState().auth,s=(null==i?void 0:i.accessToken)||"";if(!s)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");let l=null===(t=r.M.getState().auth.user)||void 0===t?void 0:t.id;return console.log("API call - User ID:",l),{urlpath:"/suppliers",payloaddata:{mode:"retrieve",page:a,limit:o,search:n.trim(),userId:l},token:s}},keepUnusedDataFor:0,providesTags:["Supplier"]}),getSupplierById:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"retrieve",supplierId:e},token:a}},providesTags:(e,t,a)=>[{type:"Supplier",id:a}]}),createSupplier:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"createnew",...e},token:a}},invalidatesTags:["Supplier"]}),updateSupplier:e.mutation({query:e=>{let{supplierId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"update",supplierId:t,...a},token:n}},invalidatesTags:(e,t,a)=>{let{supplierId:o}=a;return[{type:"Supplier",id:o},"Supplier"]}}),deleteSupplier:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"delete",supplierId:e},token:a}},invalidatesTags:["Supplier"]}),bulkDeleteSuppliers:e.mutation({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/suppliers",payloaddata:{mode:"delete",supplierIds:e},token:a}},invalidatesTags:["Supplier"]})})}),{useGetAllSuppliersQuery:s,useGetSupplierByIdQuery:l,useCreateSupplierMutation:u,useUpdateSupplierMutation:d,useDeleteSupplierMutation:c,useBulkDeleteSuppliersMutation:g}=i},33429:(e,t,a)=>{a.d(t,{He:()=>g,PK:()=>c,Rv:()=>l,aW:()=>h,ih:()=>d,lr:()=>s,tU:()=>u,z$:()=>i});var o=a(29575),n=a(32904),r=a(90821);let i=(0,o.xP)({reducerPath:"userStoreApi",baseQuery:n.V,tagTypes:["UserStore","Store"],endpoints:e=>({getUserStores:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"getUserStores",userId:e},token:a}},keepUnusedDataFor:0,providesTags:["UserStore","Store"]}),getUserDefaultStore:e.query({query:e=>{let t=r.M.getState().auth,a=(null==t?void 0:t.accessToken)||"";if(!a)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"getDefaultStore",userId:e},token:a}},providesTags:["UserStore","Store"]}),associateUserWithStore:e.mutation({query:e=>{let{userId:t,storeId:a,isDefault:o=!1}=e,n=r.M.getState().auth,i=(null==n?void 0:n.accessToken)||"";if(!i)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"associate",userId:t,storeId:a,isDefault:o},token:i}},invalidatesTags:["UserStore","Store"]}),setUserDefaultStore:e.mutation({query:e=>{let{userId:t,storeId:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"setDefaultStore",userId:t,storeId:a},token:n}},invalidatesTags:["UserStore","Store"]}),removeUserFromStore:e.mutation({query:e=>{let{userId:t,storeId:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"removeAssociation",userId:t,storeId:a},token:n}},invalidatesTags:["UserStore","Store"]}),createUserStore:e.mutation({query:e=>{let{data:t}=e,a=r.M.getState().auth,o=(null==a?void 0:a.accessToken)||"";if(!o)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"createStore",...t},token:o}},invalidatesTags:["UserStore","Store"]}),updateUserStore:e.mutation({query:e=>{let{storeId:t,data:a}=e,o=r.M.getState().auth,n=(null==o?void 0:o.accessToken)||"";if(!n)throw console.error("Authentication token is missing. User may need to log in again."),Error("Authentication token is missing. Please log in again.");return{urlpath:"/user-stores",payloaddata:{mode:"updateStore",storeId:t,...a},token:n}},invalidatesTags:["UserStore","Store"]})})}),{useGetUserStoresQuery:s,useGetUserDefaultStoreQuery:l,useAssociateUserWithStoreMutation:u,useSetUserDefaultStoreMutation:d,useRemoveUserFromStoreMutation:c,useCreateUserStoreMutation:g,useUpdateUserStoreMutation:h}=i},90821:(e,t,a)=>{a.d(t,{q:()=>q,M:()=>w});var o=a(5647),n=a(72212),r=a(46259),i=a(7875),s=a(63065),l=a(78767),u=a(10927),d=a(93968),c=a(34366),g=a(6564),h=a(77418),p=a(21633),m=a(71518),y=a(33429),k=a(47141),v=a(7165),A=a(92353),T=a(82461);let P=(0,a(51743).A)("local"),S=(0,o.HY)({auth:i.Ay,[s.i$.reducerPath]:s.i$.reducer,[l.Zf.reducerPath]:l.Zf.reducer,[u.BP.reducerPath]:u.BP.reducer,[d.GQ.reducerPath]:d.GQ.reducer,[c.sc.reducerPath]:c.sc.reducer,[g.HC.reducerPath]:g.HC.reducer,[h.eT.reducerPath]:h.eT.reducer,[p.J2.reducerPath]:p.J2.reducer,[m.g0.reducerPath]:m.g0.reducer,[y.z$.reducerPath]:y.z$.reducer,[k.Jy.reducerPath]:k.Jy.reducer,[v.H.reducerPath]:v.H.reducer,[A.z9.reducerPath]:A.z9.reducer,[T.J0.reducerPath]:T.J0.reducer}),U=(0,r.rL)({key:"root",storage:P,whitelist:["auth"]},S),w=(0,n.U1)({reducer:U,middleware:e=>e({serializableCheck:!1}).concat(s.i$.middleware,l.Zf.middleware,u.BP.middleware,d.GQ.middleware,c.sc.middleware,g.HC.middleware,h.eT.middleware,p.J2.middleware,m.g0.middleware,y.z$.middleware,k.Jy.middleware,v.H.middleware,A.z9.middleware,T.J0.middleware)});w.subscribe(()=>{window.__REDUX_STATE=w.getState()});let q=(0,r.GM)(w)}}]);