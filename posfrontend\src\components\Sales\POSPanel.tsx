import React, { useState, useMemo } from "react";
import Image from "next/image";
import { useGetAllProductsQuery, Product } from "@/reduxRTK/services/productApi";
import { useAuth } from "@/hooks/useAuth";
import { showMessage } from "@/utils/showMessage";
import { useCreateSaleMutation, CreateSaleDto } from "@/reduxRTK/services/salesApi";
import { generateReceiptHTML, generateReceiptImage } from "@/utils/cloudinaryUtils";

const POSPanel: React.FC = () => {
  const { user } = useAuth();
  const { data, isLoading, refetch } = useGetAllProductsQuery({ page: 1, limit: 100 });
  const [cart, setCart] = useState<{
    productId: number;
    name: string;
    code?: string;
    qty: number;
    price: number;
  }[]>([]);
  const [search, setSearch] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createSale] = useCreateSaleMutation();

  // Filtered products
  const filteredProducts = useMemo(() => {
    const products: Product[] = data?.data?.products || [];
    if (!search) return products;
    return products.filter(p =>
      p.name.toLowerCase().includes(search.toLowerCase()) ||
      (p.sku && p.sku.toLowerCase().includes(search.toLowerCase())) ||
      (p.barcode && p.barcode.toLowerCase().includes(search.toLowerCase()))
    );
  }, [data?.data?.products, search]);

  // Cart logic
  const handleAddToCart = (product: Product) => {
    setCart(prev => {
      const idx = prev.findIndex(item => item.productId === product.id);
      if (idx >= 0) {
        const updated = [...prev];
        updated[idx].qty += 1;
        return updated;
      }
      return [
        ...prev,
        {
          productId: product.id,
          name: product.name,
          code: product.sku || product.barcode || "",
          qty: 1,
          price: Number(product.price),
        },
      ];
    });
  };
  const handleRemoveFromCart = (productId: number) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };
  const handleUpdateQty = (productId: number, qty: number) => {
    setCart(prev => prev.map(item =>
      item.productId === productId ? { ...item, qty: Math.max(1, qty) } : item
    ));
  };
  const handleCancel = () => setCart([]);

  // Totals
  const total = useMemo(() => cart.reduce((sum, item) => sum + item.price * item.qty, 0), [cart]);
  const tax = useMemo(() => total * 0.1, [total]);
  const net = useMemo(() => total - tax, [total, tax]);

  // Sale/Pay logic (with receipt)
  const handleSale = async () => {
    if (cart.length === 0) {
      showMessage("error", "Cart is empty");
      return;
    }
    setIsSubmitting(true);
    try {
      // Generate receipt HTML
      const receiptHTML = generateReceiptHTML({
        id: Date.now(),
        totalAmount: total,
        paymentMethod: "cash",
        transactionDate: new Date().toISOString(),
        items: cart.map(item => ({ productName: item.name, quantity: item.qty, price: item.price })),
      }, { name: "POS System" });
      // Generate receipt image
      let receiptUrl = "https://receipt.example.com/placeholder";
      try {
        receiptUrl = await generateReceiptImage(receiptHTML);
      } catch (e) {}
      // Create sale
      const saleData: CreateSaleDto = {
        totalAmount: total,
        paymentMethod: "cash",
        items: cart.map(item => ({ productId: item.productId, quantity: item.qty, price: item.price })),
        receiptUrl,
        storeId: undefined,
      };
      const response = await createSale(saleData).unwrap();
      if (response.success) {
        showMessage("success", "Sale completed and receipt generated!");
        setCart([]);
        refetch();
        // Optionally, open/print the receipt
        window.open(receiptUrl, "_blank");
      } else {
        showMessage("error", response.message || "Sale failed");
      }
    } catch (error: any) {
      showMessage("error", error.message || "Sale failed");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full h-full min-h-[90vh] bg-gray-100 flex flex-col">
      {/* Top Bar */}
      <div className="flex items-center justify-between bg-black text-white px-6 py-3">
        <div className="flex items-center gap-4">
          <Image src="/images/logo.png" alt="Logo" width={40} height={40} className="w-auto" />
          <span className="text-xl font-bold tracking-wide">POS Terminal</span>
        </div>
        <div className="flex items-center gap-4">
          <span className="font-semibold text-lg">{user?.name || user?.email || "User"}</span>
          <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-xl font-bold">
            {user?.name ? user.name[0].toUpperCase() : "U"}
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Cart/Customer/Info (Left) */}
        <div className="w-[350px] bg-white flex flex-col p-4 border-r border-gray-200">
          {/* Customer Info (placeholder) */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-xl font-bold text-gray-500">{user?.name ? user.name[0].toUpperCase() : "U"}</div>
              <div>
                <div className="font-bold text-lg text-gray-800">{user?.name || user?.email || "User"}</div>
                <div className="text-xs text-gray-500">Logged in</div>
              </div>
            </div>
          </div>
          {/* Cart Table */}
          <div className="flex-1 overflow-y-auto">
            <table className="w-full text-sm mb-2">
              <thead>
                <tr className="text-gray-500 border-b">
                  <th className="text-left pb-1">Name</th>
                  <th className="text-center pb-1">Qty</th>
                  <th className="text-right pb-1">Total</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {cart.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="text-center text-gray-400 py-8">Cart is empty</td>
                  </tr>
                ) : (
                  cart.map((item) => (
                    <tr key={item.productId} className="border-b last:border-b-0">
                      <td className="font-semibold text-gray-700 py-2">{item.name}</td>
                      <td className="text-center">
                        <input
                          type="number"
                          min={1}
                          value={item.qty}
                          onChange={(e) => handleUpdateQty(item.productId, Number(e.target.value))}
                          className="w-12 text-center border rounded bg-slate-50"
                        />
                      </td>
                      <td className="text-right font-semibold">{(item.price * item.qty).toFixed(2)}</td>
                      <td>
                        <button
                          className="text-red-500 hover:underline text-xs px-2"
                          onClick={() => handleRemoveFromCart(item.productId)}
                        >
                          ✕
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          {/* Totals */}
          <div className="pt-2 border-t border-gray-200 mt-2">
            <div className="flex justify-between text-base font-bold text-gray-700 mb-1">
              <span>TOTAL</span>
              <span>${total.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>TAX</span>
              <span>{tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mb-4">
              <span>NET</span>
              <span>{net.toFixed(2)}</span>
            </div>
          </div>
        </div>
        {/* Product Grid (Right) */}
        <div className="flex-1 flex flex-col bg-[#f6f7fa]">
          {/* Product Search */}
          <div className="p-4 pb-2 flex items-center gap-2">
            <input
              type="text"
              placeholder="Search product by name, SKU, or barcode..."
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none text-base"
            />
          </div>
          {/* Product Grid */}
          <div className="flex-1 grid grid-cols-4 xl:grid-cols-5 gap-4 p-4 overflow-y-auto">
            {isLoading ? (
              <div className="col-span-4 xl:col-span-5 text-center text-lg text-gray-400 py-12">Loading products...</div>
            ) : filteredProducts.length === 0 ? (
              <div className="col-span-4 xl:col-span-5 text-center text-lg text-gray-400 py-12">No products found.</div>
            ) : (
              filteredProducts.map((product) => (
                <button
                  key={product.id}
                  className="bg-white border-2 border-gray-200 hover:border-blue-400 active:scale-95 rounded-xl flex flex-col items-center justify-center p-3 h-32 shadow group transition-all duration-150"
                  onClick={() => handleAddToCart(product)}
                >
                  {product.imageUrl ? (
                    <Image src={product.imageUrl} alt={product.name} width={48} height={48} className="object-cover rounded mb-2 border border-slate-200 group-hover:border-blue-400" />
                  ) : (
                    <div className="w-12 h-12 bg-slate-100 rounded mb-2 flex items-center justify-center text-2xl text-gray-300">🛍️</div>
                  )}
                  <span className="font-bold text-base text-gray-800 truncate w-full mb-1">{product.name}</span>
                  <span className="text-sm text-blue-600 font-bold">${Number(product.price).toFixed(2)}</span>
                </button>
              ))
            )}
          </div>
          {/* Action Buttons */}
          <div className="flex w-full bg-[#f6f7fa] p-4 space-x-4 justify-end border-t border-gray-200">
            <button
              className="bg-yellow-400 hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-bold text-lg shadow transition"
              onClick={handleSale}
              disabled={isSubmitting || cart.length === 0}
            >
              SALE
            </button>
            <button
              className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-lg font-bold text-lg shadow transition"
              onClick={handleCancel}
              disabled={isSubmitting || cart.length === 0}
            >
              CANCEL
            </button>
            <button
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-bold text-lg shadow transition"
              onClick={handleSale}
              disabled={isSubmitting || cart.length === 0}
            >
              PAY
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default POSPanel; 