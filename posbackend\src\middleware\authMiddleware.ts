import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { JWT_SECRET } from "../constant/constant";
import { DecodedToken } from "../types/type"; // Ensure correct import
import { AuthenticationError, AuthorizationError } from "./errorHandler";

export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization; // ✅ Extract token from headers

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    next(new AuthenticationError("Unauthorized: No token provided"));
    return;
  }

  const token = authHeader.split(" ")[1]; // ✅ Extract actual token

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as DecodedToken;
    req.user = decoded; // ✅ Attach decoded user info to request
    next();
  } catch (error: any) {
    // Check for specific JWT errors
    if (error.name === 'TokenExpiredError') {
      next(new AuthorizationError("Your session has expired. Please log in again."));
    } else if (error.name === 'JsonWebTokenError') {
      next(new AuthorizationError("Invalid token. Please log in again."));
    } else {
      next(new AuthorizationError("Forbidden: Invalid or expired token"));
    }
  }
};
