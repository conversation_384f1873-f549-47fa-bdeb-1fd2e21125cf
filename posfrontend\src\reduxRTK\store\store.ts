import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import authReducer from '@/reduxRTK/services/authSlice';
import { userApi } from '@/reduxRTK/services/authApi';
import { paymentApi } from '@/reduxRTK/services/paymentApi';
import { categoryApi } from '@/reduxRTK/services/categoryApi';
import { productApi } from '@/reduxRTK/services/productApi';
import { stockAdjustmentApi } from '@/reduxRTK/services/stockAdjustmentApi';
import { supplierApi } from '@/reduxRTK/services/supplierApi';
import { purchaseApi } from '@/reduxRTK/services/purchaseApi';
import { salesApi } from '@/reduxRTK/services/salesApi';
import { storeApi } from '@/reduxRTK/services/storeApi';
import { userStoreApi } from '@/reduxRTK/services/userStoreApi';
import { receiptApi } from '@/reduxRTK/services/receiptApi';
import { dashboardApi } from '@/reduxRTK/services/dashboardApi';
import { expenseApi } from '@/reduxRTK/services/expenseApi';
import { expenseCategoryApi } from '@/reduxRTK/services/expenseCategoryApi';
import storage from '@/reduxRTK/storage'; // Custom storage that handles SSR

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // only persist the auth slice
};

const rootReducer = combineReducers({
  auth: authReducer,
  [userApi.reducerPath]: userApi.reducer,
  [paymentApi.reducerPath]: paymentApi.reducer,
  [categoryApi.reducerPath]: categoryApi.reducer,
  [productApi.reducerPath]: productApi.reducer,
  [stockAdjustmentApi.reducerPath]: stockAdjustmentApi.reducer,
  [supplierApi.reducerPath]: supplierApi.reducer,
  [purchaseApi.reducerPath]: purchaseApi.reducer,
  [salesApi.reducerPath]: salesApi.reducer,
  [storeApi.reducerPath]: storeApi.reducer,
  [userStoreApi.reducerPath]: userStoreApi.reducer,
  [receiptApi.reducerPath]: receiptApi.reducer,
  [dashboardApi.reducerPath]: dashboardApi.reducer,
  [expenseApi.reducerPath]: expenseApi.reducer,
  [expenseCategoryApi.reducerPath]: expenseCategoryApi.reducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(
      userApi.middleware,
      paymentApi.middleware,
      categoryApi.middleware,
      productApi.middleware,
      stockAdjustmentApi.middleware,
      supplierApi.middleware,
      purchaseApi.middleware,
      salesApi.middleware,
      storeApi.middleware,
      userStoreApi.middleware,
      receiptApi.middleware,
      dashboardApi.middleware,
      expenseApi.middleware,
      expenseCategoryApi.middleware
    ),
});

// Expose Redux state for debugging
if (typeof window !== 'undefined') {
  store.subscribe(() => {
    (window as any).__REDUX_STATE = store.getState();
  });
}

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
