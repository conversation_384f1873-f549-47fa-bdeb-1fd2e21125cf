import express from "express";
import {
  handleUserRequest,
  handleCategoryRequest,
  handleProductRequest,
  handleSupplierRequest,
  loginUserController,
  handlePurchaseRequest,
  handleStockAdjustmentRequest,
  handleSalesRequest,
  handleReceiptRequest,
  handlePaymentRequest,
  handleStoreRequest,
  handleUserStoreRequest,
  handleExpenseRequest,
  handleExpenseCategoryRequest,
  logoutController,
} from "../controllers";
import { handleDashboardRequest } from "../controllers/dashboardController";
import { authMiddleware } from "../middleware/authMiddleware";
import { checkPaymentStatus } from "../middleware/checkPaymentStatus";

const router = express.Router();

// ✅ Public Routes (No Auth Required)
router.post("/login", loginUserController);
router.post("/logout", logoutController);

// ✅ Protected Routes (Require authentication)
router.post("/users", authMiddleware, checkPaymentStatus, handleUserRequest);
router.post(
  "/categories",
  authMiddleware,
  checkPaymentStatus,
  handleCategoryRequest
);
router.post(
  "/products",
  authMiddleware,
  checkPaymentStatus,
  handleProductRequest
);
router.post(
  "/suppliers",
  authMiddleware,
  checkPaymentStatus,
  handleSupplierRequest
);
router.post(
  "/purchases",
  authMiddleware,
  checkPaymentStatus,
  handlePurchaseRequest
);
router.post(
  "/stockadjustment",
  authMiddleware,
  checkPaymentStatus,
  handleStockAdjustmentRequest
);
router.post("/sales", authMiddleware, checkPaymentStatus, handleSalesRequest);
router.post(
  "/receipt",
  authMiddleware,
  checkPaymentStatus,
  handleReceiptRequest
);
router.post(
  "/dashboard",
  authMiddleware,
  checkPaymentStatus,
  handleDashboardRequest
);

// Payment route - doesn't need payment status check
router.post(
  "/payment",
  authMiddleware,
  handlePaymentRequest
);

// Store routes
router.post(
  "/stores",
  authMiddleware,
  checkPaymentStatus,
  handleStoreRequest
);

// User-Store routes
router.post(
  "/user-stores",
  authMiddleware,
  checkPaymentStatus,
  handleUserStoreRequest
);

// Expense routes
router.post(
  "/expenses",
  authMiddleware,
  checkPaymentStatus,
  handleExpenseRequest
);

// Expense Category routes
router.post(
  "/expense-categories",
  authMiddleware,
  checkPaymentStatus,
  handleExpenseCategoryRequest
);

export default router;
