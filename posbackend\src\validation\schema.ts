import { z } from "zod";

export const userSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required.")
    .max(100, "Name must be at most 100 characters."),
  email: z
    .string()
    .email("Invalid email format.")
    .max(100, "Email must be at most 100 characters."),
  phone: z
    .string()
    .min(5, "Phone number must be at least 5 digits.")
    .max(20, "Phone number must be at most 20 digits."),
  password: z.string().min(6, "Password must be at least 6 characters."),
  role: z.enum(["superadmin", "admin", "cashier"], {
    message: "Invalid role. Must be 'superadmin', 'admin', or 'cashier'.",
  }),
  paymentStatus: z
    .enum(["pending", "paid", "overdue", "inactive"], {
      message:
        "Invalid payment status. Must be 'pending', 'paid', 'overdue', or 'inactive'.",
    })
    .default("pending"),
  createdAt: z.string().datetime().optional(), // ✅ Use string datetime if receiving timestamps as strings
});

export const loginSchema = z.object({
  email: z
    .string()
    .email("Invalid email format.")
    .max(100, "Email must be at most 100 characters."),
  password: z.string().min(6, "Password must be at least 6 characters."),
});

// Categories Schema
export const categorySchema = z.object({
  name: z
    .string()
    .min(1, "Category name is required.")
    .max(100, "Category name must be at most 100 characters."),
  description: z.string().optional(),
});

// Products Schema
export const productSchema = z.object({
  name: z.string().min(1, "Product name is required.").max(255),
  categoryId: z.number().int().positive("Category ID is required."),
  sku: z.string().max(50).optional(),
  barcode: z.string().max(50).optional(),
  imageUrl: z.string().url("Invalid image URL").nullable().optional(),
  price: z.number().min(0, "Price must be a positive number."),
  cost: z.number().min(0, "Cost must be a positive number."),
  stockQuantity: z.number().int().min(0).default(0),
  minStockLevel: z.number().int().min(0).default(0),
  expiryDate: z.preprocess((arg) => {
    if (
      typeof arg === "string" ||
      typeof arg === "number" ||
      arg instanceof Date
    ) {
      const date = new Date(arg);
      return isNaN(date.getTime()) ? undefined : date;
    }
    return undefined;
  }, z.date().optional()),

  createdAt: z.date().optional(),
});

// Sales Schema
export const saleSchema = z.object({
  totalAmount: z.number().min(0, "Total amount must be a positive number."),
  paymentMethod: z.enum(["cash", "card", "mobile_money"]),
  items: z
    .array(
      z.object({
        productId: z.number().int().positive("Product ID must be a positive integer."),
        quantity: z.number().int().min(1, "Quantity must be at least 1."),
        price: z.number().min(0, "Price must be a positive number."),
      })
    )
    .min(1, "At least one product must be included."),
});

// Stock Adjustments Schema
export const stockAdjustmentSchema = z.object({
  productId: z
    .number()
    .int()
    .positive("Product ID must be a positive integer."),
  quantityChange: z
    .number()
    .int()
    .refine((val) => val !== 0, {
      message: "Quantity change cannot be zero.",
    }),
  reason: z.string().min(1, "Reason is required."),
  createdAt: z.date().optional(),
});

// Receipts Schema
export const receiptSchema = z.object({
  saleId: z.number().int().positive("Sale ID must be a positive integer."),
  receiptUrl: z.string().url("Invalid receipt URL format."),
});

export const supplierSchema = z.object({
  name: z
    .string()
    .min(1, "Supplier name is required.")
    .max(255, "Supplier name must be at most 255 characters."),
  contactPerson: z
    .string()
    .max(100, "Contact person must be at most 100 characters.")
    .optional(),
  phone: z
    .string()
    .min(5, "Phone number must be at least 5 digits.")
    .max(20, "Phone number must be at most 20 digits."),
  email: z
    .string()
    .max(100, "Email must be at most 100 characters.")
    .email("Invalid email format.")
    .optional()
    .or(z.literal("")), // Allow empty string
  address: z.string().optional(),
});

export const purchaseSchema = z.object({
  id: z.number().int().positive().optional(),
  supplierId: z.number().int().positive().nullable().optional(),
  productId: z.number().int().positive(),
  quantity: z.number().int().positive(),
  costPrice: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid cost price format"),
  totalCost: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid total cost format"),
  purchaseDate: z.date().optional(),
  purchasedBy: z.number().int().positive().nullable().optional(),
  createdBy: z.number().int().positive().nullable().optional(),
});

// Expense Categories Schema
export const expenseCategorySchema = z.object({
  name: z
    .string()
    .min(1, "Category name is required.")
    .max(100, "Category name must be at most 100 characters."),
  description: z.string().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color code.")
    .default("#6B7280"),
  isDefault: z.boolean().default(false),
});

// Expenses Schema
export const expenseSchema = z.object({
  title: z
    .string()
    .min(1, "Expense title is required.")
    .max(255, "Title must be at most 255 characters."),
  description: z.string().optional(),
  amount: z.number().min(0.01, "Amount must be greater than 0."),
  categoryId: z.number().int().positive().optional(),
  expenseDate: z.preprocess((arg) => {
    if (typeof arg === "string" || typeof arg === "number" || arg instanceof Date) {
      const date = new Date(arg);
      return isNaN(date.getTime()) ? new Date() : date;
    }
    return new Date();
  }, z.date()),
  paymentMethod: z.enum(["cash", "card", "mobile_money", "bank_transfer", "cheque"]),
  receiptUrl: z.string().url().optional().or(z.literal("")),
  vendor: z.string().max(255).optional(),
  isRecurring: z.boolean().default(false),
  recurringFrequency: z.enum(["daily", "weekly", "monthly", "quarterly", "yearly"]).optional(),
  tags: z.string().optional(), // JSON string of tags
  storeId: z.number().int().positive().optional(),
});
