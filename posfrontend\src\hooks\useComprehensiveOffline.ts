// Comprehensive Offline Hook for ALL NEXAPO POS Features
import { useState, useEffect, useCallback } from 'react';
import { comprehensiveOfflineStorage } from '@/utils/comprehensiveOfflineStorage';
import { comprehensiveOfflineSync } from '@/utils/comprehensiveOfflineSync';
import { showMessage } from '@/utils/showMessage';

interface OfflineState {
  isOnline: boolean;
  isSyncing: boolean;
  pendingActions: number;
  lastSyncAttempt?: Date;
  storageStats: { [key: string]: number };
}

interface OfflineOperations {
  // Sales operations
  createOfflineSale: (saleData: any) => Promise<string>;
  
  // Product operations
  createOfflineProduct: (productData: any) => Promise<string>;
  updateOfflineProduct: (productData: any) => Promise<string>;
  deleteOfflineProduct: (productId: number) => Promise<string>;
  getOfflineProducts: () => Promise<any[]>;
  searchOfflineProducts: (query: string) => Promise<any[]>;
  
  // Category operations
  createOfflineCategory: (categoryData: any) => Promise<string>;
  updateOfflineCategory: (categoryData: any) => Promise<string>;
  deleteOfflineCategory: (categoryId: number) => Promise<string>;
  getOfflineCategories: () => Promise<any[]>;
  
  // Supplier operations
  createOfflineSupplier: (supplierData: any) => Promise<string>;
  updateOfflineSupplier: (supplierData: any) => Promise<string>;
  deleteOfflineSupplier: (supplierId: number) => Promise<string>;
  getOfflineSuppliers: () => Promise<any[]>;
  
  // Customer operations
  createOfflineCustomer: (customerData: any) => Promise<string>;
  updateOfflineCustomer: (customerData: any) => Promise<string>;
  deleteOfflineCustomer: (customerId: number) => Promise<string>;
  getOfflineCustomers: () => Promise<any[]>;
  
  // Expense operations
  createOfflineExpense: (expenseData: any) => Promise<string>;
  updateOfflineExpense: (expenseData: any) => Promise<string>;
  deleteOfflineExpense: (expenseId: string) => Promise<string>;
  getOfflineExpenses: () => Promise<any[]>;
  
  // Expense Category operations
  createOfflineExpenseCategory: (categoryData: any) => Promise<string>;
  updateOfflineExpenseCategory: (categoryData: any) => Promise<string>;
  deleteOfflineExpenseCategory: (categoryId: number) => Promise<string>;
  getOfflineExpenseCategories: () => Promise<any[]>;
  
  // Purchase operations
  createOfflinePurchase: (purchaseData: any) => Promise<string>;
  updateOfflinePurchase: (purchaseData: any) => Promise<string>;
  deleteOfflinePurchase: (purchaseId: string) => Promise<string>;
  getOfflinePurchases: () => Promise<any[]>;
  
  // Stock Adjustment operations
  createOfflineStockAdjustment: (adjustmentData: any) => Promise<string>;
  getOfflineStockAdjustments: () => Promise<any[]>;
  
  // Store operations
  createOfflineStore: (storeData: any) => Promise<string>;
  updateOfflineStore: (storeData: any) => Promise<string>;
  deleteOfflineStore: (storeId: number) => Promise<string>;
  getOfflineStores: () => Promise<any[]>;
  
  // User operations
  updateOfflineUser: (userData: any) => Promise<string>;
  getOfflineUsers: () => Promise<any[]>;
  
  // Sync operations
  forceSyncNow: () => Promise<void>;
  cacheAllData: () => Promise<void>;
  refreshStorageStats: () => Promise<void>;
}

export const useComprehensiveOffline = (): [OfflineState, OfflineOperations] => {
  const [state, setState] = useState<OfflineState>({
    isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    isSyncing: false,
    pendingActions: 0,
    storageStats: {}
  });

  // Update online status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setState(prev => ({ ...prev, isOnline: navigator.onLine }));
    };

    const handleOnline = () => {
      updateOnlineStatus();
      // Auto-sync when coming back online
      comprehensiveOfflineSync.forceSyncNow().catch(console.error);
    };

    const handleOffline = () => {
      updateOnlineStatus();
      showMessage('warning', 'You are now offline. Changes will sync when connection returns.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Refresh storage stats
  const refreshStorageStats = useCallback(async () => {
    try {
      const stats = await comprehensiveOfflineStorage.getStorageStats();
      const syncStatus = await comprehensiveOfflineSync.getSyncStatus();
      
      setState(prev => ({
        ...prev,
        storageStats: stats,
        pendingActions: syncStatus.pendingActions,
        isSyncing: syncStatus.isSyncing,
        lastSyncAttempt: syncStatus.lastSyncAttempt
      }));
    } catch (error) {
      console.error('Failed to refresh storage stats:', error);
    }
  }, []);

  // Initialize and refresh stats on mount
  useEffect(() => {
    refreshStorageStats();
    
    // Refresh stats every 30 seconds
    const interval = setInterval(refreshStorageStats, 30000);
    return () => clearInterval(interval);
  }, [refreshStorageStats]);

  // Generic operations
  const createOfflineEntity = useCallback(async (entity: string, data: any, priority: number = 5): Promise<string> => {
    try {
      const actionId = await comprehensiveOfflineSync.syncOfflineEntity(entity, 'CREATE', data, priority);
      await refreshStorageStats();
      
      if (!state.isOnline) {
        showMessage('success', `${entity} saved offline. Will sync when connection returns.`);
      }
      
      return actionId;
    } catch (error) {
      console.error(`Failed to create offline ${entity}:`, error);
      showMessage('error', `Failed to save ${entity}`);
      throw error;
    }
  }, [state.isOnline, refreshStorageStats]);

  const updateOfflineEntity = useCallback(async (entity: string, data: any, priority: number = 5): Promise<string> => {
    try {
      const actionId = await comprehensiveOfflineSync.syncOfflineEntity(entity, 'UPDATE', data, priority);
      await refreshStorageStats();
      
      if (!state.isOnline) {
        showMessage('success', `${entity} updated offline. Will sync when connection returns.`);
      }
      
      return actionId;
    } catch (error) {
      console.error(`Failed to update offline ${entity}:`, error);
      showMessage('error', `Failed to update ${entity}`);
      throw error;
    }
  }, [state.isOnline, refreshStorageStats]);

  const deleteOfflineEntity = useCallback(async (entity: string, entityId: string | number): Promise<string> => {
    try {
      const actionId = await comprehensiveOfflineSync.deleteOfflineEntity(entity, entityId);
      await refreshStorageStats();
      
      if (!state.isOnline) {
        showMessage('success', `${entity} deleted offline. Will sync when connection returns.`);
      }
      
      return actionId;
    } catch (error) {
      console.error(`Failed to delete offline ${entity}:`, error);
      showMessage('error', `Failed to delete ${entity}`);
      throw error;
    }
  }, [state.isOnline, refreshStorageStats]);

  const getOfflineEntities = useCallback(async (storeName: any): Promise<any[]> => {
    try {
      return await comprehensiveOfflineStorage.getAllEntities(storeName);
    } catch (error) {
      console.error(`Failed to get offline ${storeName}:`, error);
      return [];
    }
  }, []);

  // Force sync
  const forceSyncNow = useCallback(async (): Promise<void> => {
    if (!state.isOnline) {
      showMessage('error', 'Cannot sync while offline');
      return;
    }

    try {
      setState(prev => ({ ...prev, isSyncing: true }));
      await comprehensiveOfflineSync.forceSyncNow();
      await refreshStorageStats();
    } catch (error) {
      console.error('Force sync failed:', error);
      showMessage('error', 'Sync failed. Please try again.');
    } finally {
      setState(prev => ({ ...prev, isSyncing: false }));
    }
  }, [state.isOnline, refreshStorageStats]);

  // Cache all data
  const cacheAllData = useCallback(async (): Promise<void> => {
    if (!state.isOnline) {
      showMessage('error', 'Cannot cache data while offline');
      return;
    }

    try {
      await comprehensiveOfflineSync.cacheAllData();
      await refreshStorageStats();
    } catch (error) {
      console.error('Cache data failed:', error);
      showMessage('error', 'Failed to cache data');
    }
  }, [state.isOnline, refreshStorageStats]);

  // Search products
  const searchOfflineProducts = useCallback(async (query: string): Promise<any[]> => {
    try {
      // This would need to be implemented in comprehensiveOfflineStorage
      const allProducts = await comprehensiveOfflineStorage.getAllEntities('products');
      return allProducts.filter((product: any) =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        (product.sku && product.sku.toLowerCase().includes(query.toLowerCase())) ||
        (product.barcode && product.barcode.toLowerCase().includes(query.toLowerCase()))
      );
    } catch (error) {
      console.error('Failed to search offline products:', error);
      return [];
    }
  }, []);

  const operations: OfflineOperations = {
    // Sales operations
    createOfflineSale: (data) => createOfflineEntity('sale', data, 1),
    
    // Product operations
    createOfflineProduct: (data) => createOfflineEntity('product', data, 3),
    updateOfflineProduct: (data) => updateOfflineEntity('product', data, 3),
    deleteOfflineProduct: (id) => deleteOfflineEntity('product', id),
    getOfflineProducts: () => getOfflineEntities('products'),
    searchOfflineProducts,
    
    // Category operations
    createOfflineCategory: (data) => createOfflineEntity('category', data, 4),
    updateOfflineCategory: (data) => updateOfflineEntity('category', data, 4),
    deleteOfflineCategory: (id) => deleteOfflineEntity('category', id),
    getOfflineCategories: () => getOfflineEntities('categories'),
    
    // Supplier operations
    createOfflineSupplier: (data) => createOfflineEntity('supplier', data, 4),
    updateOfflineSupplier: (data) => updateOfflineEntity('supplier', data, 4),
    deleteOfflineSupplier: (id) => deleteOfflineEntity('supplier', id),
    getOfflineSuppliers: () => getOfflineEntities('suppliers'),
    
    // Customer operations
    createOfflineCustomer: (data) => createOfflineEntity('customer', data, 4),
    updateOfflineCustomer: (data) => updateOfflineEntity('customer', data, 4),
    deleteOfflineCustomer: (id) => deleteOfflineEntity('customer', id),
    getOfflineCustomers: () => getOfflineEntities('customers'),
    
    // Expense operations
    createOfflineExpense: (data) => createOfflineEntity('expense', data, 2),
    updateOfflineExpense: (data) => updateOfflineEntity('expense', data, 2),
    deleteOfflineExpense: (id) => deleteOfflineEntity('expense', id),
    getOfflineExpenses: () => getOfflineEntities('expenses'),
    
    // Expense Category operations
    createOfflineExpenseCategory: (data) => createOfflineEntity('expenseCategory', data, 4),
    updateOfflineExpenseCategory: (data) => updateOfflineEntity('expenseCategory', data, 4),
    deleteOfflineExpenseCategory: (id) => deleteOfflineEntity('expenseCategory', id),
    getOfflineExpenseCategories: () => getOfflineEntities('expenseCategories'),
    
    // Purchase operations
    createOfflinePurchase: (data) => createOfflineEntity('purchase', data, 2),
    updateOfflinePurchase: (data) => updateOfflineEntity('purchase', data, 2),
    deleteOfflinePurchase: (id) => deleteOfflineEntity('purchase', id),
    getOfflinePurchases: () => getOfflineEntities('purchases'),
    
    // Stock Adjustment operations
    createOfflineStockAdjustment: (data) => createOfflineEntity('stockAdjustment', data, 2),
    getOfflineStockAdjustments: () => getOfflineEntities('stockAdjustments'),
    
    // Store operations
    createOfflineStore: (data) => createOfflineEntity('store', data, 4),
    updateOfflineStore: (data) => updateOfflineEntity('store', data, 4),
    deleteOfflineStore: (id) => deleteOfflineEntity('store', id),
    getOfflineStores: () => getOfflineEntities('stores'),
    
    // User operations
    updateOfflineUser: (data) => updateOfflineEntity('user', data, 4),
    getOfflineUsers: () => getOfflineEntities('users'),
    
    // Sync operations
    forceSyncNow,
    cacheAllData,
    refreshStorageStats
  };

  return [state, operations];
};
