"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's email address
 *         password:
 *           type: string
 *           format: password
 *           example: password123
 *           description: User's password
 *
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Login successful
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             accessToken:
 *               type: string
 *               example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *               description: JWT access token
 *
 *     CreateUserRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - phone
 *         - password
 *         - role
 *       properties:
 *         name:
 *           type: string
 *           example: John <PERSON>
 *           description: Full name of the user
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: Unique email address
 *         phone:
 *           type: string
 *           example: +233123456789
 *           description: Phone number with country code
 *         password:
 *           type: string
 *           format: password
 *           example: securePassword123
 *           description: Strong password (min 8 characters)
 *         role:
 *           type: string
 *           enum: [admin, cashier]
 *           example: cashier
 *           description: User role (superadmin can only be created by system)
 */
/**
 * @swagger
 * /login:
 *   post:
 *     summary: User login
 *     description: Authenticate user with email and password
 *     tags: [Authentication]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Account suspended or payment overdue
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
/**
 * @swagger
 * /logout:
 *   post:
 *     summary: User logout
 *     description: Logout user and invalidate session
 *     tags: [Authentication]
 *     security: []
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 */
/**
 * @swagger
 * /users:
 *   post:
 *     summary: Manage users
 *     description: Create, read, update, or delete users based on mode parameter
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - allOf:
 *                   - type: object
 *                     properties:
 *                       mode:
 *                         type: string
 *                         enum: [createnew]
 *                         example: createnew
 *                   - $ref: '#/components/schemas/CreateUserRequest'
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                     description: Page number for pagination
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                     description: Number of items per page
 *                   search:
 *                     type: string
 *                     example: john
 *                     description: Search term for filtering users
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [update]
 *                     example: update
 *                   id:
 *                     type: integer
 *                     example: 1
 *                     description: User ID to update
 *                   name:
 *                     type: string
 *                     example: John Updated
 *                   email:
 *                     type: string
 *                     example: <EMAIL>
 *                   phone:
 *                     type: string
 *                     example: +233987654321
 *                   role:
 *                     type: string
 *                     enum: [admin, cashier]
 *                     example: admin
 *                   paymentStatus:
 *                     type: string
 *                     enum: [pending, paid, overdue, inactive]
 *                     example: paid
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [delete]
 *                     example: delete
 *                   id:
 *                     type: integer
 *                     example: 1
 *                     description: User ID to delete
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [current]
 *                     example: current
 *                     description: Get current user's data
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [fix-payment-status]
 *                     example: fix-payment-status
 *                     description: Fix payment status (SuperAdmin only)
 *                   userId:
 *                     type: integer
 *                     example: 1
 *                     description: User ID to fix payment status for
 *                   paymentStatus:
 *                     type: string
 *                     enum: [pending, paid, overdue, inactive]
 *                     example: paid
 *                     description: New payment status
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
