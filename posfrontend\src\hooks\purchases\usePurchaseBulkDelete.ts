import { useBulkDeletePurchasesMutation } from "@/reduxRTK/services/purchaseApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const usePurchaseBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting purchases
  const [bulkDeletePurchases, { isLoading }] = useBulkDeletePurchasesMutation();

  const deletePurchases = async (purchaseIds: number[]) => {
    try {
      console.log("Bulk deleting purchases with IDs:", purchaseIds);
      
      const result = await bulkDeletePurchases(purchaseIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete purchases");
      }

      showMessage("success", `${purchaseIds.length} purchases deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete purchases error:", error);
      showMessage("error", error.message || "Failed to delete purchases");
      throw error;
    }
  };

  return {
    bulkDeletePurchases: deletePurchases,
    isDeleting: isLoading
  };
};
