"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[766],{80766:(t,e,n)=>{n.d(e,{Ay:()=>to});var o=n(12115),a=n(89842),c=n(31049),i=n(11432),r=n(24330),l=n(4951),s=n(6140),p=n(79624),d=n(51629),u=n(92984),f=n(16419),m=n(4617),g=n.n(m),b=n(22946),h=n(7926),y=n(67548),v=n(78877),O=n(70695),k=n(56204),w=n(1086);let j=t=>{let{componentCls:e,notificationMarginEdge:n,animationMaxHeight:o}=t,a="".concat(e,"-notice"),c=new y.Mo("antNotificationFadeIn",{"0%":{transform:"translate3d(100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}}),i=new y.Mo("antNotificationTopFadeIn",{"0%":{top:-o,opacity:0},"100%":{top:0,opacity:1}}),r=new y.Mo("antNotificationBottomFadeIn",{"0%":{bottom:t.calc(o).mul(-1).equal(),opacity:0},"100%":{bottom:0,opacity:1}}),l=new y.Mo("antNotificationLeftFadeIn",{"0%":{transform:"translate3d(-100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}});return{[e]:{["&".concat(e,"-top, &").concat(e,"-bottom")]:{marginInline:0,[a]:{marginInline:"auto auto"}},["&".concat(e,"-top")]:{["".concat(e,"-fade-enter").concat(e,"-fade-enter-active, ").concat(e,"-fade-appear").concat(e,"-fade-appear-active")]:{animationName:i}},["&".concat(e,"-bottom")]:{["".concat(e,"-fade-enter").concat(e,"-fade-enter-active, ").concat(e,"-fade-appear").concat(e,"-fade-appear-active")]:{animationName:r}},["&".concat(e,"-topRight, &").concat(e,"-bottomRight")]:{["".concat(e,"-fade-enter").concat(e,"-fade-enter-active, ").concat(e,"-fade-appear").concat(e,"-fade-appear-active")]:{animationName:c}},["&".concat(e,"-topLeft, &").concat(e,"-bottomLeft")]:{marginRight:{value:0,_skip_check_:!0},marginLeft:{value:n,_skip_check_:!0},[a]:{marginInlineEnd:"auto",marginInlineStart:0},["".concat(e,"-fade-enter").concat(e,"-fade-enter-active, ").concat(e,"-fade-appear").concat(e,"-fade-appear-active")]:{animationName:l}}}}},x=["top","topLeft","topRight","bottom","bottomLeft","bottomRight"],E={topLeft:"left",topRight:"right",bottomLeft:"left",bottomRight:"right",top:"left",bottom:"left"},I=(t,e)=>{let{componentCls:n}=t;return{["".concat(n,"-").concat(e)]:{["&".concat(n,"-stack > ").concat(n,"-notice-wrapper")]:{[e.startsWith("top")?"top":"bottom"]:0,[E[e]]:{value:0,_skip_check_:!0}}}}},N=t=>{let e={};for(let n=1;n<t.notificationStackLayer;n++)e["&:nth-last-child(".concat(n+1,")")]={overflow:"hidden",["& > ".concat(t.componentCls,"-notice")]:{opacity:0,transition:"opacity ".concat(t.motionDurationMid)}};return Object.assign({["&:not(:nth-last-child(-n+".concat(t.notificationStackLayer,"))")]:{opacity:0,overflow:"hidden",color:"transparent",pointerEvents:"none"}},e)},C=t=>{let e={};for(let n=1;n<t.notificationStackLayer;n++)e["&:nth-last-child(".concat(n+1,")")]={background:t.colorBgBlur,backdropFilter:"blur(10px)","-webkit-backdrop-filter":"blur(10px)"};return Object.assign({},e)},S=t=>{let{componentCls:e}=t;return Object.assign({["".concat(e,"-stack")]:{["& > ".concat(e,"-notice-wrapper")]:Object.assign({transition:"transform ".concat(t.motionDurationSlow,", backdrop-filter 0s"),willChange:"transform, opacity",position:"absolute"},N(t))},["".concat(e,"-stack:not(").concat(e,"-stack-expanded)")]:{["& > ".concat(e,"-notice-wrapper")]:Object.assign({},C(t))},["".concat(e,"-stack").concat(e,"-stack-expanded")]:{["& > ".concat(e,"-notice-wrapper")]:{"&:not(:nth-last-child(-n + 1))":{opacity:1,overflow:"unset",color:"inherit",pointerEvents:"auto",["& > ".concat(t.componentCls,"-notice")]:{opacity:1}},"&:after":{content:'""',position:"absolute",height:t.margin,width:"100%",insetInline:0,bottom:t.calc(t.margin).mul(-1).equal(),background:"transparent",pointerEvents:"auto"}}}},x.map(e=>I(t,e)).reduce((t,e)=>Object.assign(Object.assign({},t),e),{}))},P=t=>{let{iconCls:e,componentCls:n,boxShadow:o,fontSizeLG:a,notificationMarginBottom:c,borderRadiusLG:i,colorSuccess:r,colorInfo:l,colorWarning:s,colorError:p,colorTextHeading:d,notificationBg:u,notificationPadding:f,notificationMarginEdge:m,notificationProgressBg:g,notificationProgressHeight:b,fontSize:h,lineHeight:v,width:k,notificationIconSize:w,colorText:j}=t,x="".concat(n,"-notice");return{position:"relative",marginBottom:c,marginInlineStart:"auto",background:u,borderRadius:i,boxShadow:o,[x]:{padding:f,width:k,maxWidth:"calc(100vw - ".concat((0,y.zA)(t.calc(m).mul(2).equal()),")"),overflow:"hidden",lineHeight:v,wordWrap:"break-word"},["".concat(x,"-message")]:{marginBottom:t.marginXS,color:d,fontSize:a,lineHeight:t.lineHeightLG},["".concat(x,"-description")]:{fontSize:h,color:j},["".concat(x,"-closable ").concat(x,"-message")]:{paddingInlineEnd:t.paddingLG},["".concat(x,"-with-icon ").concat(x,"-message")]:{marginBottom:t.marginXS,marginInlineStart:t.calc(t.marginSM).add(w).equal(),fontSize:a},["".concat(x,"-with-icon ").concat(x,"-description")]:{marginInlineStart:t.calc(t.marginSM).add(w).equal(),fontSize:h},["".concat(x,"-icon")]:{position:"absolute",fontSize:w,lineHeight:1,["&-success".concat(e)]:{color:r},["&-info".concat(e)]:{color:l},["&-warning".concat(e)]:{color:s},["&-error".concat(e)]:{color:p}},["".concat(x,"-close")]:Object.assign({position:"absolute",top:t.notificationPaddingVertical,insetInlineEnd:t.notificationPaddingHorizontal,color:t.colorIcon,outline:"none",width:t.notificationCloseButtonSize,height:t.notificationCloseButtonSize,borderRadius:t.borderRadiusSM,transition:"background-color ".concat(t.motionDurationMid,", color ").concat(t.motionDurationMid),display:"flex",alignItems:"center",justifyContent:"center",background:"none",border:"none","&:hover":{color:t.colorIconHover,backgroundColor:t.colorBgTextHover},"&:active":{backgroundColor:t.colorBgTextActive}},(0,O.K8)(t)),["".concat(x,"-progress")]:{position:"absolute",display:"block",appearance:"none",inlineSize:"calc(100% - ".concat((0,y.zA)(i)," * 2)"),left:{_skip_check_:!0,value:i},right:{_skip_check_:!0,value:i},bottom:0,blockSize:b,border:0,"&, &::-webkit-progress-bar":{borderRadius:i,backgroundColor:"rgba(0, 0, 0, 0.04)"},"&::-moz-progress-bar":{background:g},"&::-webkit-progress-value":{borderRadius:i,background:g}},["".concat(x,"-actions")]:{float:"right",marginTop:t.marginSM}}},M=t=>{let{componentCls:e,notificationMarginBottom:n,notificationMarginEdge:o,motionDurationMid:a,motionEaseInOut:c}=t,i="".concat(e,"-notice"),r=new y.Mo("antNotificationFadeOut",{"0%":{maxHeight:t.animationMaxHeight,marginBottom:n},"100%":{maxHeight:0,marginBottom:0,paddingTop:0,paddingBottom:0,opacity:0}});return[{[e]:Object.assign(Object.assign({},(0,O.dF)(t)),{position:"fixed",zIndex:t.zIndexPopup,marginRight:{value:o,_skip_check_:!0},["".concat(e,"-hook-holder")]:{position:"relative"},["".concat(e,"-fade-appear-prepare")]:{opacity:"0 !important"},["".concat(e,"-fade-enter, ").concat(e,"-fade-appear")]:{animationDuration:t.motionDurationMid,animationTimingFunction:c,animationFillMode:"both",opacity:0,animationPlayState:"paused"},["".concat(e,"-fade-leave")]:{animationTimingFunction:c,animationFillMode:"both",animationDuration:a,animationPlayState:"paused"},["".concat(e,"-fade-enter").concat(e,"-fade-enter-active, ").concat(e,"-fade-appear").concat(e,"-fade-appear-active")]:{animationPlayState:"running"},["".concat(e,"-fade-leave").concat(e,"-fade-leave-active")]:{animationName:r,animationPlayState:"running"},"&-rtl":{direction:"rtl",["".concat(i,"-actions")]:{float:"left"}}})},{[e]:{["".concat(i,"-wrapper")]:Object.assign({},P(t))}}]},_=t=>({zIndexPopup:t.zIndexPopupBase+v.jH+50,width:384}),z=t=>{let e=t.paddingMD,n=t.paddingLG;return(0,k.oX)(t,{notificationBg:t.colorBgElevated,notificationPaddingVertical:e,notificationPaddingHorizontal:n,notificationIconSize:t.calc(t.fontSizeLG).mul(t.lineHeightLG).equal(),notificationCloseButtonSize:t.calc(t.controlHeightLG).mul(.55).equal(),notificationMarginBottom:t.margin,notificationPadding:"".concat((0,y.zA)(t.paddingMD)," ").concat((0,y.zA)(t.paddingContentHorizontalLG)),notificationMarginEdge:t.marginLG,animationMaxHeight:150,notificationStackLayer:3,notificationProgressHeight:2,notificationProgressBg:"linear-gradient(90deg, ".concat(t.colorPrimaryBorderHover,", ").concat(t.colorPrimary,")")})},B=(0,w.OF)("Notification",t=>{let e=z(t);return[M(e),j(e),S(e)]},_),L=(0,w.bf)(["Notification","PurePanel"],t=>{let e="".concat(t.componentCls,"-notice"),n=z(t);return{["".concat(e,"-pure-panel")]:Object.assign(Object.assign({},P(n)),{width:n.width,maxWidth:"calc(100vw - ".concat((0,y.zA)(t.calc(n.notificationMarginEdge).mul(2).equal()),")"),margin:0})}},_);var R=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(t);a<o.length;a++)0>e.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(t,o[a])&&(n[o[a]]=t[o[a]]);return n};function A(t,e){return null===e||!1===e?null:e||o.createElement(p.A,{className:"".concat(t,"-close-icon")})}u.A,l.A,s.A,d.A,f.A;let H={success:l.A,info:u.A,error:s.A,warning:d.A},F=t=>{let{prefixCls:e,icon:n,type:a,message:c,description:i,actions:r,role:l="alert"}=t,s=null;return n?s=o.createElement("span",{className:"".concat(e,"-icon")},n):a&&(s=o.createElement(H[a]||null,{className:g()("".concat(e,"-icon"),"".concat(e,"-icon-").concat(a))})),o.createElement("div",{className:g()({["".concat(e,"-with-icon")]:s}),role:l},s,o.createElement("div",{className:"".concat(e,"-message")},c),o.createElement("div",{className:"".concat(e,"-description")},i),r&&o.createElement("div",{className:"".concat(e,"-actions")},r))};var D=n(28415),T=n(68711),q=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(t);a<o.length;a++)0>e.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(t,o[a])&&(n[o[a]]=t[o[a]]);return n};let G=t=>{let{children:e,prefixCls:n}=t,a=(0,h.A)(n),[c,i,r]=B(n,a);return c(o.createElement(b.ph,{classNames:{list:g()(i,r,a)}},e))},W=(t,e)=>{let{prefixCls:n,key:a}=e;return o.createElement(G,{prefixCls:n,key:a},t)},X=o.forwardRef((t,e)=>{let{top:n,bottom:a,prefixCls:i,getContainer:r,maxCount:l,rtl:s,onAllRemoved:p,stack:d,duration:u,pauseOnHover:f=!0,showProgress:m}=t,{getPrefixCls:h,getPopupContainer:y,notification:v,direction:O}=(0,o.useContext)(c.QO),[,k]=(0,T.Ay)(),w=i||h("notification"),[j,x]=(0,b.hN)({prefixCls:w,style:t=>(function(t,e,n){let o;switch(t){case"top":o={left:"50%",transform:"translateX(-50%)",right:"auto",top:e,bottom:"auto"};break;case"topLeft":o={left:0,top:e,bottom:"auto"};break;case"topRight":o={right:0,top:e,bottom:"auto"};break;case"bottom":o={left:"50%",transform:"translateX(-50%)",right:"auto",top:"auto",bottom:n};break;case"bottomLeft":o={left:0,top:"auto",bottom:n};break;default:o={right:0,top:"auto",bottom:n}}return o})(t,null!=n?n:24,null!=a?a:24),className:()=>g()({["".concat(w,"-rtl")]:null!=s?s:"rtl"===O}),motion:()=>({motionName:"".concat(w,"-fade")}),closable:!0,closeIcon:A(w),duration:null!=u?u:4.5,getContainer:()=>(null==r?void 0:r())||(null==y?void 0:y())||document.body,maxCount:l,pauseOnHover:f,showProgress:m,onAllRemoved:p,renderNotifications:W,stack:!1!==d&&{threshold:"object"==typeof d?null==d?void 0:d.threshold:void 0,offset:8,gap:k.margin}});return o.useImperativeHandle(e,()=>Object.assign(Object.assign({},j),{prefixCls:w,notification:v})),x});function K(t){let e=o.useRef(null);return(0,D.rJ)("Notification"),[o.useMemo(()=>{let n=n=>{var a;if(!e.current)return;let{open:c,prefixCls:i,notification:r}=e.current,l="".concat(i,"-notice"),{message:s,description:p,icon:d,type:u,btn:f,actions:m,className:b,style:h,role:y="alert",closeIcon:v,closable:O}=n,k=q(n,["message","description","icon","type","btn","actions","className","style","role","closeIcon","closable"]),w=A(l,void 0!==v?v:void 0!==(null==t?void 0:t.closeIcon)?t.closeIcon:null==r?void 0:r.closeIcon);return c(Object.assign(Object.assign({placement:null!==(a=null==t?void 0:t.placement)&&void 0!==a?a:"topRight"},k),{content:o.createElement(F,{prefixCls:l,icon:d,type:u,message:s,description:p,actions:null!=m?m:f,role:y}),className:g()(u&&"".concat(l,"-").concat(u),b,null==r?void 0:r.className),style:Object.assign(Object.assign({},null==r?void 0:r.style),h),closeIcon:w,closable:null!=O?O:!!w}))},a={open:n,destroy:t=>{var n,o;void 0!==t?null===(n=e.current)||void 0===n||n.close(t):null===(o=e.current)||void 0===o||o.destroy()}};return["success","info","warning","error"].forEach(t=>{a[t]=e=>n(Object.assign(Object.assign({},e),{type:t}))}),a},[]),o.createElement(X,Object.assign({key:"notification-holder"},t,{ref:e}))]}let Q=null,J=t=>t(),U=[],V={};function Y(){let{getContainer:t,rtl:e,maxCount:n,top:o,bottom:a,showProgress:c,pauseOnHover:i}=V,r=(null==t?void 0:t())||document.body;return{getContainer:()=>r,rtl:e,maxCount:n,top:o,bottom:a,showProgress:c,pauseOnHover:i}}let $=o.forwardRef((t,e)=>{let{notificationConfig:n,sync:i}=t,{getPrefixCls:r}=(0,o.useContext)(c.QO),l=V.prefixCls||r("notification"),s=(0,o.useContext)(a.B),[p,d]=K(Object.assign(Object.assign(Object.assign({},n),{prefixCls:l}),s.notification));return o.useEffect(i,[]),o.useImperativeHandle(e,()=>{let t=Object.assign({},p);return Object.keys(t).forEach(e=>{t[e]=function(){return i(),p[e].apply(p,arguments)}}),{instance:t,sync:i}}),d}),Z=o.forwardRef((t,e)=>{let[n,a]=o.useState(Y),c=()=>{a(Y)};o.useEffect(c,[]);let r=(0,i.cr)(),l=r.getRootPrefixCls(),s=r.getIconPrefixCls(),p=r.getTheme(),d=o.createElement($,{ref:e,sync:c,notificationConfig:n});return o.createElement(i.Ay,{prefixCls:l,iconPrefixCls:s,theme:p},r.holderRender?r.holderRender(d):d)});function tt(){if(!Q){let t=document.createDocumentFragment(),e={fragment:t};Q=e,J(()=>{(0,r.K)()(o.createElement(Z,{ref:t=>{let{instance:n,sync:o}=t||{};Promise.resolve().then(()=>{!e.instance&&n&&(e.instance=n,e.sync=o,tt())})}}),t)});return}Q.instance&&(U.forEach(t=>{switch(t.type){case"open":J(()=>{Q.instance.open(Object.assign(Object.assign({},V),t.config))});break;case"destroy":J(()=>{null==Q||Q.instance.destroy(t.key)})}}),U=[])}function te(t){(0,i.cr)(),U.push({type:"open",config:t}),tt()}let tn={open:te,destroy:t=>{U.push({type:"destroy",key:t}),tt()},config:function(t){V=Object.assign(Object.assign({},V),t),J(()=>{var t;null===(t=null==Q?void 0:Q.sync)||void 0===t||t.call(Q)})},useNotification:function(t){return K(t)},_InternalPanelDoNotUseOrYouWillBeFired:t=>{let{prefixCls:e,className:n,icon:a,type:i,message:r,description:l,btn:s,actions:p,closable:d=!0,closeIcon:u,className:f}=t,m=R(t,["prefixCls","className","icon","type","message","description","btn","actions","closable","closeIcon","className"]),{getPrefixCls:y}=o.useContext(c.QO),v=e||y("notification"),O="".concat(v,"-notice"),k=(0,h.A)(v),[w,j,x]=B(v,k);return w(o.createElement("div",{className:g()("".concat(O,"-pure-panel"),j,n,x,k)},o.createElement(L,{prefixCls:v}),o.createElement(b.$T,Object.assign({},m,{prefixCls:v,eventKey:"pure",duration:null,closable:d,className:g()({notificationClassName:f}),closeIcon:A(v,u),content:o.createElement(F,{prefixCls:O,icon:a,type:i,message:r,description:l,actions:null!=p?p:s})}))))}};["success","info","warning","error"].forEach(t=>{tn[t]=e=>te(Object.assign(Object.assign({},e),{type:t}))});let to=tn}}]);