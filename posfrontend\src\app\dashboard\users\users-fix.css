/* Complete overhaul of table styles to fix transparent background issues */

/* Apply white background to the entire table and all its children */
.user-table,
.user-table *,
.user-table *::before,
.user-table *::after {
  background-color: white !important;
}

/* Force white background on table cells */
.user-table .ant-table-cell {
  background-color: white !important;
  position: relative;
}

/* Add a white background layer to each cell */
.user-table .ant-table-cell::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white !important;
  z-index: -1;
}

/* Specific fixes for table elements */
.user-table .ant-table,
.user-table .ant-table-container,
.user-table .ant-table-content,
.user-table .ant-table-body,
.user-table .ant-table-thead,
.user-table .ant-table-tbody,
.user-table .ant-table-wrapper,
.user-table .ant-table-header,
.user-table .ant-table-summary,
.user-table .ant-table-footer,
.user-table .ant-table-title,
.user-table .ant-table-sticky-scroll,
.user-table .ant-table-placeholder,
.user-table .ant-empty,
.user-table .ant-table-pagination {
  background-color: white !important;
}

/* Fix for table cells */
.user-table .ant-table-thead > tr > th,
.user-table .ant-table-tbody > tr > td,
.user-table .ant-table-tbody > tr.ant-table-placeholder:hover > td,
.user-table .ant-table-tbody > tr.ant-table-row:hover > td {
  background-color: white !important;
}

/* Fix for fixed columns */
.user-table .ant-table-cell-fix-left,
.user-table .ant-table-cell-fix-right {
  background-color: white !important;
}

/* Remove all shadows and borders that might show background */
.user-table .ant-table-cell-fix-left-last::after,
.user-table .ant-table-cell-fix-right-first::after {
  box-shadow: none !important;
  background-color: white !important;
}

/* Remove all pseudo-elements that might show background */
.user-table .ant-table-container::before,
.user-table .ant-table-container::after,
.user-table .ant-table-header::before,
.user-table .ant-table-header::after,
.user-table .ant-table-body::before,
.user-table .ant-table-body::after,
.user-table .ant-table::before,
.user-table .ant-table::after,
.user-table .ant-table-thead::before,
.user-table .ant-table-thead::after,
.user-table .ant-table-tbody::before,
.user-table .ant-table-tbody::after {
  display: none !important;
}

/* Fix for scrollbar area */
.user-table .ant-table-sticky-scroll {
  background-color: white !important;
}

.user-table .ant-table-sticky-scroll-bar {
  background-color: #e6e6e6 !important;
}

/* Fix for pagination area */
.user-table .ant-pagination,
.user-table .ant-pagination * {
  background-color: white !important;
}

/* Fix for the table wrapper */
.table-container {
  background-color: white !important;
  max-width: 100%;
  width: 100%;
  overflow: visible !important;
}

/* Make individual cells scrollable instead of the whole table */
.user-table .ant-table-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px; /* Limit cell width */
}

/* Make cell content scrollable on hover */
.user-table .ant-table-cell.scrollable-cell {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  cursor: text;
}

/* Show scrollbar on hover for scrollable cells */
.user-table .ant-table-cell.scrollable-cell:hover {
  overflow: auto;
  text-overflow: clip;
  z-index: 10;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

/* Style for scrollbar */
.user-table .ant-table-cell.scrollable-cell::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

.user-table .ant-table-cell.scrollable-cell::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 4px;
}

.user-table .ant-table-cell.scrollable-cell::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* Fixed width for specific columns */
.user-table .ant-table-cell.name-column {
  max-width: 120px;
}

.user-table .ant-table-cell.email-column {
  max-width: 150px;
}

.user-table .ant-table-cell.phone-column {
  max-width: 100px;
}

.user-table .ant-table-cell.role-column,
.user-table .ant-table-cell.status-column {
  max-width: 100px;
}

.user-table .ant-table-cell.date-column {
  max-width: 100px;
}

.user-table .ant-table-cell.actions-column {
  max-width: 100px;
}

/* Fix for tag backgrounds */
.user-table .ant-tag {
  position: relative;
  z-index: 1;
}

/* Create a white background behind each tag */
.user-table .ant-tag::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background-color: white !important;
  z-index: -1;
}

/* Ensure the tag content is above the background */
.user-table .ant-tag > span {
  position: relative;
  z-index: 2;
}

/* Fix for role and status columns */
.user-table td[class*="ant-table-cell"]:nth-child(3),
.user-table td[class*="ant-table-cell"]:nth-child(4) {
  background-color: white !important;
  position: relative;
}

/* Add a solid white background to these cells */
.user-table td[class*="ant-table-cell"]:nth-child(3)::before,
.user-table td[class*="ant-table-cell"]:nth-child(4)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white !important;
  z-index: -1;
}

/* Mobile-specific fixes */
@media (max-width: 640px) {
  /* Ensure the entire card has white background */
  .card-container,
  .card-container * {
    background-color: white !important;
  }

  /* Fix for table on mobile */
  .user-table .ant-table-thead > tr > th,
  .user-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    background-color: white !important;
  }

  /* Fix for tags on mobile */
  .user-table .ant-tag {
    margin-right: 0;
    padding: 0 4px;
    font-size: 11px;
    position: relative;
    z-index: 1;
  }

  /* Fix for tag containers */
  .user-table .ant-tag::before {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background-color: white !important;
    z-index: -1;
  }

  /* Ensure all cells with tags have white background */
  .user-table .ant-table-cell {
    background-color: white !important;
    position: relative;
    z-index: 0;
  }

  /* Fix for action buttons on mobile */
  .user-table .ant-space-item {
    margin-right: 0 !important;
  }

  .user-table .ant-btn {
    padding: 0 4px;
  }
}

/* Style for white background rows */
.white-background-row {
  background-color: white !important;
}

.white-background-row td {
  background-color: white !important;
}

/* Fix for hover state */
.white-background-row:hover td {
  background-color: white !important;
}

/* Fix for selected state */
.white-background-row.ant-table-row-selected td {
  background-color: white !important;
}
