"use client";

import { ReactNode } from "react";
import AuthGuard from "./AuthGuard";
import RoleGuard from "./RoleGuard";
import PaymentGuard from "./PaymentGuard";
import { UserRole } from "@/types/user";

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
  checkPayment?: boolean;
  fallbackPath?: string;
}

/**
 * ProtectedRoute component that combines AuthGuard, RoleGuard, and PaymentGuard
 * Use this component to protect routes with authentication, role-based access, and payment status
 *
 * - AuthGuard ensures the user is authenticated
 * - RoleGuard ensures the user has the required role
 * - PaymentGuard ensures the user has a valid payment status (except for superadmins)
 *
 * Set checkPayment=false only for routes that should be accessible regardless of payment status
 * (e.g., profile page, payment page)
 */
const ProtectedRoute = ({
  children,
  allowedRoles = ["superadmin", "admin", "cashier"],
  checkPayment = true,
  fallbackPath = "/dashboard",
}: ProtectedRouteProps) => {
  return (
    <AuthGuard>
      {checkPayment ? (
        <PaymentGuard>
          <RoleGuard allowedRoles={allowedRoles} fallbackPath={fallbackPath}>
            {children}
          </RoleGuard>
        </PaymentGuard>
      ) : (
        <RoleGuard allowedRoles={allowedRoles} fallbackPath={fallbackPath}>
          {children}
        </RoleGuard>
      )}
    </AuthGuard>
  );
};

export default ProtectedRoute;
