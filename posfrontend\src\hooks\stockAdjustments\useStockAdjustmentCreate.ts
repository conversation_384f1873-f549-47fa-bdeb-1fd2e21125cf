"use client";

import { useCreateStockAdjustmentMutation, CreateStockAdjustmentDto } from "@/reduxRTK/services/stockAdjustmentApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";
import { useGetAllProductsQuery } from "@/reduxRTK/services/productApi";

export const useStockAdjustmentCreate = (onSuccess?: () => void) => {
  // RTK Query hook for creating a stock adjustment
  const [createStockAdjustment, { isLoading: isSubmitting }] = useCreateStockAdjustmentMutation();

  // Get access to the product list query to force a refetch
  const { refetch: refetchProducts } = useGetAllProductsQuery({ page: 1, limit: 10 }, { skip: false });

  const createNewStockAdjustment = async (adjustmentData: CreateStockAdjustmentDto) => {
    try {
      console.log("useStockAdjustmentCreate - Starting stock adjustment creation with data:", adjustmentData);

      const result = await createStockAdjustment(adjustmentData).unwrap() as ApiResponse<any>;
      console.log("useStockAdjustmentCreate - API response:", result);

      if (!result.success) {
        console.error("useStockAdjustmentCreate - API returned error:", result.message);
        throw new Error(result.message || "Failed to create stock adjustment");
      }

      showMessage("success", "Stock adjustment created successfully");

      // We'll let the onSuccess callback handle the page refresh
      return result.data;
    } catch (error: any) {
      console.error("Create stock adjustment error:", error);
      showMessage("error", error.message || "Failed to create stock adjustment");
      throw error;
    }
  };

  return {
    createStockAdjustment: createNewStockAdjustment,
    isSubmitting,
  };
};
