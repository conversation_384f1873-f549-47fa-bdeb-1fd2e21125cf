"use client";
import React, { useState, useEffect } from "react";
import { Form, Input, <PERSON><PERSON>, Al<PERSON>, <PERSON>, Badge, Divider } from "antd";
import { CheckCircleFilled } from "@ant-design/icons";
import { showMessage } from "@/utils/showMessage";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useInitializePaymentMutation, useVerifyPaystackPaymentMutation } from "@/reduxRTK/services/paymentApi";
import { userApi } from "@/reduxRTK/services/authApi";
import { setUser } from "@/reduxRTK/services/authSlice";

// Define subscription plans with NEW PRICING
const SUBSCRIPTION_PLANS = {
  monthly: {
    price: 40,
    discount: 0,
    label: "Monthly",
    description: "Basic subscription plan",
    period: 1,
  },
  quarterly: {
    price: 108, // ₵36/month - 10% discount
    discount: 10,
    label: "Quarterly",
    description: "Save 10% with quarterly billing",
    period: 3,
  },
  annual: {
    price: 360, // ₵30/month - 25% discount
    discount: 25,
    label: "Annual",
    description: "Save 25% with annual billing",
    period: 12,
  }
};

interface PaystackPaymentFormProps {
  onSuccess?: () => void;
}

const PaystackPaymentForm: React.FC<PaystackPaymentFormProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [initializePayment, { isLoading: isInitializing }] = useInitializePaymentMutation();
  const [verifyPaystackPayment, { isLoading: isVerifying }] = useVerifyPaystackPaymentMutation();
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string>("monthly");
  const [paymentConfig, setPaymentConfig] = useState<any>(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);

  // Get user info from Redux store
  const user = useSelector((state: RootState) => state.auth.user);

  // Update form values when plan changes
  useEffect(() => {
    const plan = SUBSCRIPTION_PLANS[selectedPlan as keyof typeof SUBSCRIPTION_PLANS];
    form.setFieldsValue({
      amount: Math.round(plan.price),
      email: user?.email || '',
    });
  }, [selectedPlan, form, user]);

  const handleInitializePayment = async (values: {
    amount: number;
    email: string;
    plan: string;
  }) => {
    try {
      setError(null);

      // Get subscription period from selected plan
      const plan = SUBSCRIPTION_PLANS[selectedPlan as keyof typeof SUBSCRIPTION_PLANS];

      console.log('🔄 Initializing payment with subscription period:', {
        selectedPlan,
        planDetails: plan,
        subscriptionPeriod: plan.period,
        amount: values.amount
      });

      const response = await initializePayment({
        amount: values.amount,
        email: values.email,
        callbackUrl: `${window.location.origin}/payment/callback`,
        subscriptionPeriod: plan.period // Send the subscription period
      }).unwrap();

      if (response.success && response.data) {
        // Store the authorization URL and payment details
        setPaymentConfig({
          authorizationUrl: response.data.authorizationUrl,
          reference: response.data.reference,
          email: values.email,
          amount: values.amount,
          key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || 'pk_test_your_public_key_here',
        });

        console.log('Payment initialized successfully:', {
          authorizationUrl: response.data.authorizationUrl,
          reference: response.data.reference,
          amount: values.amount
        });
      } else {
        setError(response.message || "Failed to initialize payment");
      }
    } catch (err: any) {
      setError(err.data?.message || err.message || "An error occurred during payment initialization.");
    }
  };

  const handlePaymentSuccess = async (reference: any) => {
    try {
      setError(null);
      console.log('🎉 Payment success callback triggered with reference:', reference.reference);

      // Verify payment with backend
      const verifyResponse = await verifyPaystackPayment({
        reference: reference.reference
      }).unwrap();

      if (verifyResponse.success) {
        const plan = SUBSCRIPTION_PLANS[selectedPlan as keyof typeof SUBSCRIPTION_PLANS];
        const periodText = plan.period === 12 ? '1 year' : plan.period === 3 ? '3 months' : '1 month';
        showMessage("success", `Payment successful! Your ${plan.label.toLowerCase()} subscription is now active for ${periodText}.`);

        console.log('✅ Payment verified successfully, cleaning up form...');
        form.resetFields();
        setPaymentConfig(null);

        // Immediately refetch user data to update Redux state
        if (accessToken) {
          (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, { forceRefetch: true }))
            .then((result: any) => {
              console.log('[Payment] User data refetch result after payment:', result);
              if (result?.data?.success && result.data.data) {
                dispatch(setUser({ user: result.data.data, accessToken }));
              }
            });
        }

        if (onSuccess) {
          onSuccess();
        }

        // Navigate to callback page for proper user state update
        console.log('🚀 Redirecting to payment callback for user state update...');
        router.push(`/payment/callback?reference=${reference.reference}`);
      } else {
        console.error('❌ Payment verification failed:', verifyResponse.message);
        setError(verifyResponse.message || "Payment verification failed. Please contact support if this issue persists.");
      }
    } catch (err: any) {
      setError(err.data?.message || err.message || "Payment verification failed. Please contact support if this issue persists.");
    }
  };

  const handlePaymentClose = () => {
    setPaymentConfig(null);
    showMessage("error", "Payment was cancelled.");
  };

  // Function to redirect to Paystack checkout page
  const openPaystackCheckout = () => {
    if (!paymentConfig || !paymentConfig.authorizationUrl) {
      setError("Payment not initialized. Please try again.");
      return;
    }

    console.log('Redirecting to Paystack checkout:', paymentConfig.authorizationUrl);

    // Redirect to Paystack checkout page
    window.location.href = paymentConfig.authorizationUrl;
  };

  const renderPlanCard = (planKey: string) => {
    const plan = SUBSCRIPTION_PLANS[planKey as keyof typeof SUBSCRIPTION_PLANS];
    const isSelected = selectedPlan === planKey;
    const isRecommended = planKey === "annual";

    return (
      <Card
        className={`bg-white border-2 transition-all duration-200 h-full ${isSelected ? 'border-blue-500 shadow-lg' : 'border-gray-200'}`}
        styles={{
          body: {
            padding: '16px',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }
        }}
        onClick={() => setSelectedPlan(planKey)}
      >
        {isRecommended && (
          <Badge.Ribbon text="Best Value" color="blue" className="absolute top-0 right-0">
            <div className="h-6"></div>
          </Badge.Ribbon>
        )}
        <div className="flex flex-col h-full flex-grow">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-bold text-gray-800">{plan.label}</h3>
            {isSelected && <CheckCircleFilled className="text-blue-500 text-xl" />}
          </div>

          <div className="text-gray-600 text-sm mb-3">{plan.description}</div>

          <div className="mt-auto">
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-gray-800">GH₵{Math.round(plan.price)}</span>
              {plan.discount > 0 && (
                <span className="ml-2 text-sm text-green-500">Save {plan.discount}%</span>
              )}
            </div>
            <div className="text-gray-500 text-xs mt-1">
              {planKey === "monthly" ? "per month" :
               planKey === "quarterly" ? "every 3 months" : "per year"}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 pl-2">Choose a Subscription Plan</h2>

      {error && (
        <Alert
          message="Payment Error"
          description={error}
          type="error"
          showIcon
          className="mb-4"
          closable
          onClose={() => setError(null)}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {Object.keys(SUBSCRIPTION_PLANS).map(planKey => (
          <div key={planKey} className="col-span-1 h-full">
            <div className="h-full">
              {renderPlanCard(planKey)}
            </div>
          </div>
        ))}
      </div>

      <Divider className="border-gray-200 my-6" />

      <h3 className="text-xl font-bold mb-4 text-gray-800 pl-2">Payment Details</h3>

      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg mx-2">
        <p className="text-sm text-green-700">
          <strong>🔒 Secure Payment:</strong> All payments are processed securely through Paystack.
          You can pay with cards, or mobile money.
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleInitializePayment}
        initialValues={{
          amount: SUBSCRIPTION_PLANS.monthly.price,
          email: user?.email || '',
          plan: "monthly"
        }}
        className="px-2"
      >
        <Form.Item
          name="plan"
          hidden
        >
          <Input type="hidden" value={selectedPlan} />
        </Form.Item>

        <Form.Item
          label={<span className="text-gray-700">Amount (GH₵)</span>}
          name="amount"
        >
          <Input
            className="bg-white border-gray-300 text-gray-800"
            disabled
            prefix={<span className="text-gray-600">GH₵</span>}
            suffix={<span className="text-gray-600">GHS</span>}
            style={{ color: '#333333' }}
          />
        </Form.Item>

        <Form.Item
          label={<span className="text-gray-700">Email Address</span>}
          name="email"
          rules={[
            { required: true, message: "Please enter your email address" },
            { type: "email", message: "Please enter a valid email address" }
          ]}
        >
          <Input
            placeholder="<EMAIL>"
            className="bg-white border-gray-300 text-gray-800"
            style={{ color: '#333333' }}
          />
        </Form.Item>

        <Form.Item className="mt-6">
          {paymentConfig ? (
            <Button
              type="primary"
              onClick={openPaystackCheckout}
              className="w-full bg-green-500 hover:bg-green-600 h-12 text-base font-medium"
              loading={isVerifying}
            >
              {isVerifying ? "Processing Payment..." : "Pay with Paystack"}
            </Button>
          ) : (
            <Button
              type="primary"
              htmlType="submit"
              className="w-full bg-blue-500 hover:bg-blue-600 h-12 text-base font-medium"
              loading={isInitializing}
            >
              {isInitializing ? "Initializing..." : "Initialize Payment"}
            </Button>
          )}
        </Form.Item>
      </Form>

      {isVerifying && (
        <div className="text-center mt-4">
          <Alert
            message="Verifying Payment"
            description="Please wait while we verify your payment..."
            type="info"
            showIcon
          />
        </div>
      )}
    </div>
  );
};

export default PaystackPaymentForm;
