"use client";

import React, { useEffect } from "react";
import { Form, Input, Button, InputNumber, Radio } from "antd";
import { Product, useGetProductByIdQuery } from "@/reduxRTK/services/productApi";
import { CreateStockAdjustmentDto } from "@/reduxRTK/services/stockAdjustmentApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useStockAdjustmentCreate } from "@/hooks/stockAdjustments/useStockAdjustmentCreate";
import { PlusOutlined, MinusOutlined, ShoppingOutlined, EditOutlined } from "@ant-design/icons";
import "../Products/product-panels.css";

const { TextArea } = Input;

interface StockAdjustmentFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  product: Product | null;
}

const StockAdjustmentFormPanel: React.FC<StockAdjustmentFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  product,
}) => {
  const [form] = Form.useForm();
  const { createStockAdjustment, isSubmitting } = useStockAdjustmentCreate(onSuccess);

  // Fetch the latest product data to ensure we have the most up-to-date stock quantity
  const { data: productData, refetch: refetchProduct } = useGetProductByIdQuery(
    product?.id || 0,
    { skip: !product?.id || !isOpen }
  );

  // Get the latest product data
  const currentProduct = productData?.data || product;

  // Reset form when product changes
  useEffect(() => {
    if (isOpen && product) {
      // Refetch the product to get the latest stock quantity
      if (product.id) {
        refetchProduct();
      }

      form.resetFields();
      form.setFieldsValue({
        adjustmentType: 'add',
        quantityChange: 1,
        reason: '',
      });
    }
  }, [form, isOpen, product, refetchProduct]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    if (!currentProduct) return;

    try {
      const quantityChange = values.adjustmentType === 'add'
        ? Math.abs(values.quantityChange)
        : -Math.abs(values.quantityChange);

      const adjustmentData: CreateStockAdjustmentDto = {
        productId: currentProduct.id,
        quantityChange,
        reason: values.reason,
      };

      console.log("Submitting stock adjustment:", adjustmentData);

      // Wait for the adjustment to complete
      const result = await createStockAdjustment(adjustmentData);
      console.log("Stock adjustment result:", result);

      // Force refetch of the product data to update the UI
      refetchProduct();

      // Schedule additional refetches to ensure we get fresh data
      setTimeout(() => {
        console.log("Delayed product refetch after stock adjustment");
        refetchProduct();
      }, 500);

      // Call onSuccess to refresh the product table in the parent component
      if (onSuccess) {
        console.log("Calling onSuccess to refresh the product table");
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to create stock adjustment:", error);
      alert("Failed to adjust stock. Please try again.");
    }
  };

  // Panel title
  const panelTitle = currentProduct ? `Adjust Stock: ${currentProduct.name}` : "Adjust Stock";

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isSubmitting}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isSubmitting}
        onClick={() => form.submit()}
      >
        Adjust Stock
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={panelTitle}
      width="500px"
      footer={panelFooter}
    >
      <div className="p-6 bg-white">
        {/* Form heading with icon */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <ShoppingOutlined className="mr-2" />
            Stock Adjustment
          </h2>
          <p className="text-gray-600 mt-1">
            Adjust the stock quantity for {currentProduct?.name}
          </p>
        </div>

        {/* Required fields indicator */}
        <div className="mb-4 text-sm text-gray-600">
          <span className="text-red-500 mr-1">*</span> indicates required fields
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="product-form"
          requiredMark={true}
        >
          {/* Current Stock Information */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Current Stock Information
            </h3>

            <div className="bg-gray-100 p-3 rounded mb-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Current Stock:</span>
                <span className="text-gray-800 font-medium">{currentProduct?.stockQuantity || 0}</span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span className="text-gray-600">Minimum Stock Level:</span>
                <span className="text-gray-800 font-medium">{currentProduct?.minStockLevel || 0}</span>
              </div>
            </div>
          </div>

          {/* Adjustment Details */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Adjustment Details
            </h3>

            <Form.Item
              name="adjustmentType"
              label={<span className="flex items-center text-gray-800">Adjustment Type</span>}
              rules={[{ required: true, message: "Please select adjustment type" }]}
              initialValue="add"
            >
              <Radio.Group>
                <Radio value="add" className="text-gray-800">
                  <span className="flex items-center">
                    <PlusOutlined className="mr-1 text-green-500" /> Add Stock
                  </span>
                </Radio>
                <Radio value="remove" className="text-gray-800">
                  <span className="flex items-center">
                    <MinusOutlined className="mr-1 text-red-500" /> Remove Stock
                  </span>
                </Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="quantityChange"
              label={<span className="flex items-center"><EditOutlined className="mr-1" /> Quantity</span>}
              rules={[
                { required: true, message: "Please enter quantity" },
                { type: 'number', min: 1, message: "Quantity must be at least 1" }
              ]}
              initialValue={1}
              tooltip="The quantity to add or remove"
            >
              <InputNumber
                min={1}
                style={{ width: '100%' }}
                placeholder="Enter quantity"
              />
            </Form.Item>

            <Form.Item
              name="reason"
              label={<span className="flex items-center"><EditOutlined className="mr-1" /> Reason</span>}
              rules={[{ required: true, message: "Please enter reason for adjustment" }]}
              tooltip="Why you are adjusting the stock"
            >
              <TextArea
                rows={4}
                placeholder="Enter reason for adjustment"
              />
            </Form.Item>
          </div>
        </Form>
      </div>
    </SlidingPanel>
  );
};

export default StockAdjustmentFormPanel;
