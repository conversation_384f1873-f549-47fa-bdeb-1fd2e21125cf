import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  associateUserWithStore,
  getUserStores,
  getUserDefaultStore,
  setUserDefaultStore,
  removeUserFromStore,
} from "../services/userStoreService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

/**
 * Handle User-Store Requests
 * This controller handles all user-store association operations
 */
export const handleUserStoreRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, userId, storeId, isDefault } = req.body;
  const requester = req.user as DecodedToken;

  // Validate mode - support both old and new mode names for backward compatibility
  const validModes = [
    // Standard CRUD operations
    "create", "retrieve", "update", "delete",
    // Legacy modes
    "associate", "getUserStores", "getDefaultStore", "setDefaultStore", "removeAssociation",
    // Store operations
    "createStore", "updateStore"
  ];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      /**
       * Associate a user with a store
       * Required fields: userId, storeId
       */
      case "associate":
      case "create": {
        // Validate required fields
        if (!userId || !storeId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID and Store ID are required."
          );
        }

        // Associate user with store
        const association = await associateUserWithStore(
          requester,
          Number(userId),
          Number(storeId),
          isDefault === true
        );
        return sendResponse(
          res,
          201,
          true,
          "User associated with store successfully.",
          association
        );
      }

      /**
       * Get all stores for a user
       * Required fields: userId
       */
      case "getUserStores":
      case "retrieve": {
        if (!userId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID is required."
          );
        }

        // Get user stores
        const stores = await getUserStores(requester, Number(userId));
        return sendResponse(
          res,
          200,
          true,
          "User stores retrieved successfully.",
          stores
        );
      }

      /**
       * Get the default store for a user
       * Required fields: userId
       */
      case "getDefaultStore": {
        if (!userId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID is required."
          );
        }

        // Get default store
        const store = await getUserDefaultStore(requester, Number(userId));
        return sendResponse(
          res,
          200,
          true,
          store ? "Default store retrieved successfully." : "No default store found.",
          store
        );
      }

      /**
       * Set the default store for a user
       * Required fields: userId, storeId
       */
      case "setDefaultStore":
      case "update": {
        if (!userId || !storeId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID and Store ID are required."
          );
        }

        // Set default store
        const association = await setUserDefaultStore(
          requester,
          Number(userId),
          Number(storeId)
        );
        return sendResponse(
          res,
          200,
          true,
          "Default store set successfully.",
          association
        );
      }

      /**
       * Remove a user-store association
       * Required fields: userId, storeId
       */
      case "removeAssociation":
      case "delete": {
        if (!userId || !storeId) {
          return sendResponse(
            res,
            400,
            false,
            "User ID and Store ID are required."
          );
        }

        // Remove association
        const result = await removeUserFromStore(
          requester,
          Number(userId),
          Number(storeId)
        );
        return sendResponse(
          res,
          200,
          true,
          "User removed from store successfully.",
          result
        );
      }

      /**
       * Create a store for a user
       * Required fields: userId, name (in storeData)
       */
      case "createStore": {
        // Extract store data from request body
        const storeData = { ...req.body };
        delete storeData.mode;
        delete storeData.userId;

        if (!userId || !storeData.name) {
          return sendResponse(
            res,
            400,
            false,
            "User ID and store name are required."
          );
        }

        // Create store using storeService and then associate with user
        const { createStore } = require("../services/storeService");

        try {
          // Create the store
          const newStore = await createStore(requester, storeData);

          // Associate the user with the store
          await associateUserWithStore(
            requester,
            Number(userId),
            newStore.id,
            true // Make it the default store
          );

          return sendResponse(
            res,
            201,
            true,
            "Store created and associated with user successfully.",
            newStore
          );
        } catch (error: any) {
          return sendResponse(
            res,
            error.statusCode || 500,
            false,
            error.message || "Failed to create store"
          );
        }
      }

      /**
       * Update a store
       * Required fields: storeId
       */
      case "updateStore": {
        // Extract store data from request body
        const storeData = { ...req.body };
        delete storeData.mode;
        delete storeData.storeId;

        if (!storeId) {
          return sendResponse(
            res,
            400,
            false,
            "Store ID is required."
          );
        }

        // Update store using storeService
        const { updateStore } = require("../services/storeService");

        try {
          // Update the store
          const updatedStore = await updateStore(requester, Number(storeId), storeData);

          return sendResponse(
            res,
            200,
            true,
            "Store updated successfully.",
            updatedStore
          );
        } catch (error: any) {
          return sendResponse(
            res,
            error.statusCode || 500,
            false,
            error.message || "Failed to update store"
          );
        }
      }

      default:
        return sendResponse(res, 400, false, "Invalid mode.");
    }
  } catch (error: any) {
    console.error("User-Store request error:", error);
    return sendResponse(
      res,
      error.statusCode || 500,
      false,
      error.message || "Internal Server Error"
    );
  }
};
