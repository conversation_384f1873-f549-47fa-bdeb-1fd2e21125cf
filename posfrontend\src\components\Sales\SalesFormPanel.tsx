"use client";

import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import {
  Form,
  Button,
  Select,
  InputNumber,
  Input,
  Empty,
  Spin,
  Modal,
  Image as AntImage,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ShoppingCartOutlined,
  SearchOutlined,
  LoadingOutlined,
  ShopOutlined,
  PrinterOutlined,
  EyeOutlined,
  ScanOutlined,
} from "@ant-design/icons";
import {
  useCreateSaleMutation,
  CreateSaleDto,
  CreateSaleItemDto,
} from "@/reduxRTK/services/salesApi";
import {
  useGetAllProductsQuery,
  Product,
} from "@/reduxRTK/services/productApi";
import {
  useGetUserStoresQuery,
  useGetUserDefaultStoreQuery,
} from "@/reduxRTK/services/userStoreApi";
import {
  useGetAllCategoriesQuery,
  Category,
} from "@/reduxRTK/services/categoryApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import BarcodeScanner from "@/components/BarcodeScanner/BarcodeScanner";
import { useBarcodeScanner } from "@/hooks/useBarcodeScanner";
import { showMessage } from "@/utils/showMessage";
import {
  generateReceiptHTML,
  generateReceiptImage,
} from "@/utils/cloudinaryUtils";
import { Store } from "@/types/store";
import "./sales-panels.css";
import "./modern-sales.css";

interface SalesFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const SalesFormPanel: React.FC<SalesFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [productForm] = Form.useForm();
  const [items, setItems] = useState<
    (CreateSaleItemDto & { productName: string })[]
  >([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [receiptPreviewVisible, setReceiptPreviewVisible] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);
  const [hasPrinted, setHasPrinted] = useState(false);
  const [barcodeInput, setBarcodeInput] = useState<string>('');
  const [selectedCustomer, setSelectedCustomer] = useState<string>('Walk-in Customer');
  const [isLocked, setIsLocked] = useState(false);
  const [savedSales, setSavedSales] = useState<any[]>([]);
  const [savedSalesModalVisible, setSavedSalesModalVisible] = useState(false);
  const [currentLoadedSaleId, setCurrentLoadedSaleId] = useState<number | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  // Barcode scanner functionality
  const {
    isOpen: isScannerOpen,
    openScanner,
    closeScanner,
    handleBarcodeScanned,
    isLoading: isScannerLoading
  } = useBarcodeScanner({
    onProductFound: (product: Product) => {
      setSelectedProduct(product);
      setQuantity(1);
      if (productForm) {
        productForm.setFieldsValue({
          productId: product.id,
          quantity: 1
        });
      }
      // Auto-add to cart after scanning
      setTimeout(() => {
        handleAddItem();
      }, 100);
    },
    onProductNotFound: (barcode: string) => {
      console.log('Product not found for barcode:', barcode);
    }
  });

  // Handle hardware barcode scanner input
  const handleBarcodeInputChange = async (value: string) => {
    setBarcodeInput(value);

    // If input looks like a barcode (typically 8+ characters), try to find product
    if (value && value.length >= 8) {
      try {
        console.log('Searching for product with barcode:', value);

        // Use the existing products data to search for barcode
        const products = productsData?.data?.products || [];

        // Look for exact barcode match first
        let foundProduct = products.find(product =>
          product.barcode === value.trim()
        );

        // If no exact barcode match, try SKU
        if (!foundProduct) {
          foundProduct = products.find(product =>
            product.sku === value.trim()
          );
        }

        if (foundProduct) {
          console.log('Product found via hardware scanner:', foundProduct);

          // Check stock quantity
          if (foundProduct.stockQuantity <= 0) {
            showMessage('error', `${foundProduct.name} is out of stock`);
            setBarcodeInput('');
            return;
          }

          // Create the item directly without using form validation
          const newItem = {
            productId: foundProduct.id,
            productName: foundProduct.name,
            price: foundProduct.price,
            quantity: 1,
          };

          // Check if product already exists in cart
          const existingItemIndex = items.findIndex(
            (item) => item.productId === foundProduct.id
          );

          if (existingItemIndex >= 0) {
            // Check if adding one more would exceed stock
            const currentQuantity = items[existingItemIndex].quantity;
            if (currentQuantity + 1 > foundProduct.stockQuantity) {
              showMessage('error', `Only ${foundProduct.stockQuantity} units available in stock`);
              setBarcodeInput('');
              return;
            }
          }

          // Add directly to cart
          setItems(prevItems => [...prevItems, {...newItem, price: Number(newItem.price)}]);

          // Clear the barcode input
          setBarcodeInput('');

          showMessage('success', `Product added: ${foundProduct.name}`);
        } else {
          console.log('No product found for barcode:', value);
          showMessage('warning', `No product found for barcode: ${value}`);
        }
      } catch (error) {
        console.error('Error searching for product:', error);
        showMessage('error', 'Error searching for product');
      }
    }
  };

  // Debug state changes
  useEffect(() => {
    console.log("Items state changed:", items);
  }, [items]);


  const {
    data: productsData,
    isLoading: isLoadingProducts,
    refetch: refetchProducts,
  } = useGetAllProductsQuery(
    {
      page: 1,
      limit: 1000, // Increased limit to get more products for sales
      search: searchTerm,
    },
    {
      // Force refetch when component mounts and when panel opens
      refetchOnMountOrArgChange: true,
      refetchOnFocus: false,
      refetchOnReconnect: true, // Refetch when reconnecting
    },
  );

  // Fetch categories for filtering
  const {
    data: categoriesData,
    isLoading: isLoadingCategories,
  } = useGetAllCategoriesQuery({
    page: 1,
    limit: 100, // Get all categories
    search: '',
  });

  // Debug products data
  useEffect(() => {
    if (productsData) {
      console.log("🛒 Products loaded:", {
        total: productsData.data?.total || 0,
        productsCount: productsData.data?.products?.length || 0,
        isLoading: isLoadingProducts,
      });
    }
  }, [productsData, isLoadingProducts]);

  // Get current user ID from auth state
  const getCurrentUserId = () => {
    if (typeof window !== "undefined") {
      // @ts-ignore - Redux state is exposed for debugging
      const state = window.__REDUX_STATE;
      return state?.auth?.user?.id || 0;
    }
    return 0;
  };

  // Fetch user stores
  const { data: userStoresData } = useGetUserStoresQuery(getCurrentUserId());

  // Fetch default store
  const { data: defaultStoreData } =
    useGetUserDefaultStoreQuery(getCurrentUserId());

  // Get user profile for header display
  const userProfile = { firstName: 'Admin' }; // Placeholder for now

  // Helper function to get emoji for category
  const getCategoryEmoji = (categoryName: string): string => {
    const name = categoryName.toLowerCase();
    if (name.includes('electronic') || name.includes('tech') || name.includes('phone') || name.includes('computer')) return '📱';
    if (name.includes('clothing') || name.includes('fashion') || name.includes('apparel') || name.includes('wear')) return '👕';
    if (name.includes('food') || name.includes('grocery') || name.includes('snack') || name.includes('drink')) return '🍕';
    if (name.includes('book') || name.includes('education') || name.includes('stationery') || name.includes('office')) return '📚';
    if (name.includes('toy') || name.includes('game') || name.includes('play') || name.includes('kid')) return '🧸';
    if (name.includes('health') || name.includes('medical') || name.includes('pharmacy') || name.includes('care')) return '💊';
    if (name.includes('beauty') || name.includes('cosmetic') || name.includes('makeup') || name.includes('skincare')) return '💄';
    if (name.includes('sport') || name.includes('fitness') || name.includes('gym') || name.includes('exercise')) return '⚽';
    if (name.includes('home') || name.includes('furniture') || name.includes('decor') || name.includes('kitchen')) return '🏠';
    if (name.includes('auto') || name.includes('car') || name.includes('vehicle') || name.includes('motor')) return '🚗';
    return '🏷️'; // Default category emoji
  };

  // Show saved sales modal
  const showSavedSalesModal = () => {
    setSavedSalesModalVisible(true);
  };

  // Load a saved sale
  const loadSavedSale = (saleData: any) => {
    setItems(saleData.items);
    setSelectedCustomer(saleData.customer);
    setTotalAmount(saleData.totalAmount);
    setCurrentLoadedSaleId(saleData.id); // Track which sale is loaded
    setSavedSalesModalVisible(false);
    showMessage('success', `Loaded saved sale: ₵${saleData.totalAmount.toFixed(2)}`);
  };

  // Delete a saved sale
  const deleteSavedSale = (saleId: number) => {
    setSavedSales(prev => prev.filter(sale => sale.id !== saleId));
    showMessage('success', 'Saved sale deleted');
  };

  // Set default store when data is loaded
  useEffect(() => {
    if (defaultStoreData?.data) {
      setSelectedStore(defaultStoreData.data);
      form.setFieldsValue({ storeId: defaultStoreData.data.id });
    } else if (userStoresData?.data && userStoresData.data.length > 0) {
      setSelectedStore(userStoresData.data[0]);
      form.setFieldsValue({ storeId: userStoresData.data[0].id });
    }
  }, [defaultStoreData, userStoresData, form]);

  // Create sale mutation
  const [createSale, { isLoading: isSubmitting }] = useCreateSaleMutation();

  // Calculate total amount whenever items change
  useEffect(() => {
    if (items && items.length > 0) {
      const total = items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );
      setTotalAmount(total);
      if (form) {
        form.setFieldsValue({ totalAmount: total });
      }

      // Debug log to check items state
      console.log("Current items in useEffect:", items);
    } else {
      setTotalAmount(0);
      if (form) {
        form.setFieldsValue({ totalAmount: 0 });
      }
    }
  }, [items, form]);

  // Handle panel open/close
  useEffect(() => {
    if (isOpen) {
      // When panel opens, ensure we have fresh product data
      console.log("🛒 Sales panel opened - fetching fresh product data");
      refetchProducts();
    } else {
      // Reset forms when panel is closed
      if (form) {
        form.resetFields();
      }
      if (productForm) {
        productForm.resetFields();
      }
      setItems([]);
      setSelectedProduct(null);
      setQuantity(1);
      setTotalAmount(0);
      setReceiptUrl(null);
      setReceiptPreviewVisible(false);
      setHasPrinted(false);
    }
  }, [isOpen, form, productForm, refetchProducts]);

  // Handle adding an item to the sale
  const handleAddItem = () => {
    if (!selectedProduct) {
      showMessage("error", "Please select a product");
      return;
    }

    if (quantity <= 0) {
      showMessage("error", "Quantity must be greater than 0");
      return;
    }

    // Check if product is out of stock
    if (selectedProduct.stockQuantity <= 0) {
      showMessage("error", `${selectedProduct.name} is out of stock`);
      return;
    }

    // Find if product already exists in cart
    const existingItemIndex = items.findIndex(
      (item) => item.productId === selectedProduct.id
    );

    if (existingItemIndex >= 0) {
      // Update quantity, but do not exceed stock
      const currentQuantity = items[existingItemIndex].quantity;
      let newTotalQuantity = currentQuantity + quantity;
      if (newTotalQuantity > selectedProduct.stockQuantity) {
        newTotalQuantity = selectedProduct.stockQuantity;
        showMessage(
          "error",
          `Cannot add more than ${selectedProduct.stockQuantity} units. Quantity set to maximum available.`
        );
      }
      const updatedItems = [...items];
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: newTotalQuantity,
      };
      setItems(updatedItems);
      showMessage("success", `Updated quantity of ${selectedProduct.name} to ${newTotalQuantity}`);
    } else {
      // Only add if requested quantity does not exceed stock
      let addQuantity = quantity;
      if (addQuantity > selectedProduct.stockQuantity) {
        addQuantity = selectedProduct.stockQuantity;
        showMessage(
          "error",
          `Cannot add more than ${selectedProduct.stockQuantity} units in stock. Quantity set to maximum available.`
        );
      }
      const newItem = {
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        quantity: addQuantity,
        price:
          typeof selectedProduct.price === "string"
            ? parseFloat(selectedProduct.price)
            : selectedProduct.price,
      };
      setItems([...items, newItem]);
      showMessage(
        "success",
        `Added ${addQuantity} ${selectedProduct.name} to sale`,
      );
    }
    // Reset selection
    setSelectedProduct(null);
    setQuantity(1);
    if (productForm) {
      productForm.setFieldsValue({ productId: undefined, quantity: 1 });
    }
  };

  // Handle removing an item from the sale
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
    showMessage("success", "Item removed from sale");
  };

  // Handle updating quantity of an item in the cart
  const handleUpdateItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(index);
      return;
    }

    const item = items[index];
    const product = productsData?.data?.products.find(p => p.id === item.productId);

    if (product && newQuantity > product.stockQuantity) {
      showMessage('error', `Only ${product.stockQuantity} units available in stock`);
      return;
    }

    const updatedItems = [...items];
    updatedItems[index] = { ...item, quantity: newQuantity };
    setItems(updatedItems);
    showMessage('success', 'Quantity updated');
  };

  // Handle printing receipt - directly trigger print dialog
  const handlePrintReceipt = useCallback(() => {

    if (!receiptUrl || hasPrinted) {
      console.log(
        "Skipping print: ",
        !receiptUrl ? "No receipt URL" : "Already printed",
      );
      return;
    }

    console.log("Printing receipt:", receiptUrl);

    // Mark as printed immediately to prevent multiple print dialogs
    setHasPrinted(true);

    // Create a hidden iframe to load the image
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Set up the iframe content with the image and print CSS
    iframe.onload = () => {
      if (iframe.contentWindow) {
        // Write the HTML content to the iframe
        iframe.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${receiptUrl}" alt="Receipt" />
            </body>
          </html>
        `);

        // Close the document
        iframe.contentWindow.document.close();

        // Use a single print trigger with a delay to ensure the image is loaded
        setTimeout(() => {
          if (iframe.contentWindow) {
            try {
              // Print the iframe content
              iframe.contentWindow.focus();
              iframe.contentWindow.print();
            } catch (e) {
              console.error("Error printing receipt:", e);
            }

            // Remove the iframe after printing
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          }
        }, 500);
      }
    };

    // Set the iframe source to trigger the onload event
    iframe.src = "about:blank";
  }, [receiptUrl, hasPrinted]);

  // Effect to automatically print receipt when modal is shown
  useEffect(() => {
    if (receiptPreviewVisible && receiptUrl && !hasPrinted) {
      // Add a small delay to ensure the receipt image is loaded
      const timer = setTimeout(() => {
        handlePrintReceipt(); // This now handles the hasPrinted state internally
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [receiptPreviewVisible, receiptUrl, hasPrinted, handlePrintReceipt]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (items.length === 0) {
        showMessage("error", "Please add at least one item to the sale");
        return;
      }

      // Validate form fields
      const values = await form.validateFields();

      // Check if store is selected
      if (!selectedStore) {
        showMessage(
          "error",
          "No store information available. Please set up your store in your profile settings.",
        );
        return;
      }

      // Set loading state for receipt generation
      setIsGeneratingReceipt(true);

      // Get store information for receipt
      const storeInfo = selectedStore ||
        userStoresData?.data?.find((store) => store.id === values.storeId) || {
          name: "POS System",
        };

      // Generate receipt HTML
      const receiptHTML = generateReceiptHTML(
        {
          id: Date.now(), // Temporary ID until we get the real one
          totalAmount,
          paymentMethod: values.paymentMethod,
          transactionDate: new Date().toISOString(),
          items: items.map((item) => ({
            productName: item.productName,
            quantity: item.quantity,
            price: item.price,
          })),
        },
        storeInfo,
      );

      // Generate receipt image and get URL
      let receiptUrl = "https://receipt.example.com/placeholder";
      try {
        receiptUrl = await generateReceiptImage(receiptHTML);
      } catch (error) {
        console.error("Failed to generate receipt image:", error);
        // Continue with placeholder URL if image generation fails
      }

      const saleData: CreateSaleDto = {
        totalAmount,
        paymentMethod: values.paymentMethod,
        items: items.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
        receiptUrl,
        storeId: selectedStore?.id,
      };

      const response = await createSale(saleData).unwrap();

      if (response.success) {
        showMessage("success", "Sale created successfully");

        // If this was a loaded saved sale, remove it from saved sales
        if (currentLoadedSaleId) {
          setSavedSales(prev => prev.filter(sale => sale.id !== currentLoadedSaleId));
          setCurrentLoadedSaleId(null);
          showMessage('success', 'Saved sale completed and removed from pending list');
        }

        // Store the receipt URL for preview
        setReceiptUrl(receiptUrl);

        // Show receipt preview modal and offer print option
        setReceiptPreviewVisible(true);

        // Refresh product data to get updated stock quantities
        refetchProducts();

        // Trigger the success callback to refresh the list WITHOUT closing the panel
        setTimeout(() => {
          if (onSuccess) {
            // Call refetch directly instead of closing the panel
            refetchProducts();
          }
        }, 300);

        // Keep the panel open until the user explicitly closes it
        // This ensures the receipt modal stays visible
      } else {
        showMessage("error", response.message || "Failed to create sale");
      }
    } catch (error: any) {
      showMessage(
        "error",
        error.data?.message || "An error occurred while creating the sale",
      );
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  // Debug log to check items state when rendering
  console.log("Rendering with items:", items);

  return (
    <SlidingPanel
      title="Point of Sale"
      isOpen={isOpen}
      onClose={onClose}
      width="100vw"
      fullWidth={true}
    >
      <div className="pos-panel h-screen bg-gray-100 flex flex-col">
        {/* Top Header */}
        <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-lg font-bold">NEXAPO</span>
            <span className="text-sm">POS System - v1.0.0</span>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm">User: {userProfile?.firstName || 'Admin'}</span>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col lg:flex-row">
          {/* Left Side - Product Search and Grid */}
          <div className="flex-1 p-2 lg:p-4">
            {/* Search Bar */}
            <div className="flex flex-col sm:flex-row gap-2 mb-3">
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-10 rounded-md border w-full"
                suffix={<SearchOutlined />}
              />
              <Input
                placeholder="Barcode scanner..."
                value={barcodeInput}
                onChange={(e) => handleBarcodeInputChange(e.target.value)}
                className="h-10 rounded-md border w-full"
                style={{ borderColor: '#10b981', backgroundColor: '#f0fdf4' }}
              />
              <Select
                showSearch
                allowClear
                placeholder="Filter by category"
                value={selectedCategoryId ?? undefined}
                onChange={val => setSelectedCategoryId(val ?? null)}
                optionFilterProp="label"
                filterOption={(input, option) => {
                  const label = String(option?.label ?? '');
                  return label.toLowerCase().includes(input.toLowerCase());
                }}
                className="h-10 rounded-md border w-full"
                dropdownStyle={{ fontSize: 14 }}
                loading={isLoadingCategories}
                bordered={true}
              >
                <Select.Option value={null} label="All">✨ All</Select.Option>
                {(categoriesData as any)?.data?.categories?.map((category: any) => (
                  <Select.Option
                    key={category.id}
                    value={category.id}
                    label={`${getCategoryEmoji(category.name)} ${category.name}`}
                  >
                    {getCategoryEmoji(category.name)} {category.name}
                  </Select.Option>
                ))}
              </Select>
            </div>

            {/* If no products, show a message and do not render the product grid or action buttons */}
            {productsData?.data?.products?.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 text-gray-500">
                <ShopOutlined className="text-4xl mb-4" />
                <p>No products found. Please add products to start selling.</p>
              </div>
            ) : (
              <>
                {/* Product Grid */}
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-3 mb-3 lg:mb-4">
                  {/* Product Tiles */}
                  {productsData?.data?.products
                    ?.filter(product => {
                      // Search filter
                      const matchesSearch = !searchTerm ||
                        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        product.barcode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        product.sku?.toLowerCase().includes(searchTerm.toLowerCase());

                      // Category filter
                      const matchesCategory = selectedCategoryId === null ||
                        product.categoryId === selectedCategoryId;

                      return matchesSearch && matchesCategory;
                    })
                    ?.slice(0, 36) // Show max 36 products (6x6 grid)
                    ?.map((product) => (
                    <div
                      key={product.id}
                      className={`aspect-square rounded-lg p-2 lg:p-3 cursor-pointer transition-all duration-200 flex flex-col items-center justify-center text-center shadow-md ${
                        product.stockQuantity <= 0
                          ? 'bg-gray-200 opacity-50 cursor-not-allowed'
                          : 'bg-blue-400 hover:bg-blue-500 text-white'
                      }`}
                      onClick={() => {
                        if (product.stockQuantity > 0) {
                          let message = '';
                          let messageType = 'success';
                          let newCart;
                          const existingIndex = items.findIndex(item => item.productId === product.id);
                          if (existingIndex >= 0) {
                            const currentQuantity = items[existingIndex].quantity;
                            let newTotalQuantity = currentQuantity + 1;
                            if (newTotalQuantity > product.stockQuantity) {
                              newTotalQuantity = product.stockQuantity;
                              message = `Cannot add more than ${product.stockQuantity} units. Quantity set to maximum available.`;
                              messageType = 'error';
                            } else {
                              message = `Updated quantity of ${product.name} to ${newTotalQuantity}`;
                              messageType = 'success';
                            }
                            newCart = [...items];
                            newCart[existingIndex] = { ...newCart[existingIndex], quantity: newTotalQuantity };
                          } else {
                            message = `${product.name} added to cart`;
                            messageType = 'success';
                            newCart = [
                              ...items,
                              {
                                productId: product.id,
                                productName: product.name,
                                price: Number(product.price),
                                quantity: 1,
                              },
                            ];
                          }
                          setItems(newCart);
                          if (message) showMessage(messageType as "success" | "error" | "warning", message);
                        }
                      }}
                    >
                      {/* Product Image */}
                      <div className="w-12 h-12 lg:w-16 lg:h-16 bg-white/20 rounded-lg mb-1 lg:mb-2 flex items-center justify-center overflow-hidden">
                        {product.imageUrl ? (
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            width={64}
                            height={64}
                            className="object-cover rounded-lg"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              e.currentTarget.style.display = 'none';
                              const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                              if (nextElement) {
                                nextElement.style.display = 'flex';
                              }
                            }}
                          />
                        ) : null}
                        <span className={`text-2xl ${product.imageUrl ? 'hidden' : 'block'}`}>📦</span>
                      </div>

                      {/* Product Name */}
                      <div className="text-xs font-medium mb-1 line-clamp-2 leading-tight">
                        {product.name.length > 15 ? `${product.name.substring(0, 15)}...` : product.name}
                      </div>

                      {/* Product Price */}
                      <div className="text-xs font-bold">
                        ₵{Number(product.price).toFixed(2)}
                      </div>
                      {/* Stock Quantity */}
                      <div className={`text-[11px] mt-1 ${product.stockQuantity > 0 ? 'text-green-700' : 'text-red-500'}`}
                           style={{ fontWeight: 500 }}>
                        {product.stockQuantity > 0 ? `In Stock: ${product.stockQuantity}` : 'Out of Stock'}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Functional Action Buttons */}
                <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3">
                  {/* Row 1 - Core Functions */}
                  <div
                    className={`bg-green-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${items.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-green-600'}`}
                    onClick={() => {
                      if (items.length === 0) return;
                      // Save current sale for later completion
                      const saleData = {
                        id: Date.now(),
                        items: [...items],
                        totalAmount,
                        customer: selectedCustomer,
                        timestamp: new Date().toISOString(),
                      };
                      setSavedSales(prev => [...prev, saleData]);
                      showMessage('success', `Sale saved! Total: ₵${totalAmount.toFixed(2)}`);
                      // Clear current cart after saving
                      setItems([]);
                      setSelectedProduct(null);
                      setQuantity(1);
                      setTotalAmount(0);
                      setCurrentLoadedSaleId(null); // Reset loaded sale tracking
                      form.resetFields();
                      productForm.resetFields();
                    }}
                  >
                    <div className="text-xs font-medium">Save Sale</div>
                  </div>

                  <div
                    className={`bg-indigo-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${savedSales.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-indigo-600'}`}
                    onClick={() => {
                      if (savedSales.length === 0) return;
                      // Show modal to select which saved sale to load
                      showSavedSalesModal();
                    }}
                  >
                    <div className="text-xs font-medium">Load Saved
                    {savedSales.length > 0 && (
                      <div className="text-xs opacity-80">({savedSales.length})</div>
                    )}
                    </div>
                    
                  </div>

                  {/* <div
                    className="bg-purple-500 rounded-lg p-3 text-center text-white cursor-pointer hover:bg-purple-600 transition-colors shadow-md"
                    onClick={() => {
                      if (receiptUrl) {
                        handlePrintReceipt();
                      } else {
                        showMessage('warning', 'No receipt to print. Complete a sale first.');
                      }
                    }}
                  >
                    <div className="text-xs font-medium">Print Receipt</div>
                  </div> */}

                  <div
                    className={`bg-orange-500 rounded-lg p-2 lg:p-3 text-center text-white transition-colors shadow-md ${items.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-orange-600'}`}
                    onClick={() => {
                      if (items.length === 0) return;
                      setItems([]);
                      setSelectedProduct(null);
                      setQuantity(1);
                      setTotalAmount(0);
                      setCurrentLoadedSaleId(null); // Reset loaded sale tracking
                      form.resetFields();
                      productForm.resetFields();
                      showMessage('success', 'Cart cleared');
                    }}
                  >
                    <div className="text-xs font-medium">Clear Cart</div>
                  </div>

                  <div
                    className="bg-red-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-red-600 transition-colors shadow-md"
                    onClick={() => {
                      onClose();
                    }}
                  >
                    <div className="text-xs font-medium">Close POS</div>
                  </div>

                  <div
                    className="bg-gray-500 rounded-lg p-2 lg:p-3 text-center text-white cursor-pointer hover:bg-gray-600 transition-colors shadow-md"
                    onClick={() => {
                      showMessage('success', 'Help: Click products to add to cart, use barcode scanner, adjust quantities, then click PAY to complete sale');
                    }}
                  >
                    <div className="text-xs font-medium">Help</div>
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Right Side - Cart and Checkout */}
          <div className="w-full lg:w-80 bg-white border-t lg:border-t-0 lg:border-l border-gray-200 flex flex-col">
            {/* Cart Items */}
            <div className="flex-1 p-2 lg:p-4">
              <div className="bg-white rounded-lg border border-gray-200 p-3 lg:p-4 mb-3 lg:mb-4">
                <div className="text-sm font-medium mb-2">Cart Items</div>
                <div className="space-y-2 max-h-40 lg:max-h-60 overflow-y-auto">
                  {items.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      <ShoppingCartOutlined className="text-2xl mb-2" />
                      <div>No items in cart</div>
                    </div>
                  ) : (
                    items.map((item, index) => (
                      <div key={`${item.productId}-${index}`} className="flex flex-col sm:flex-row sm:items-center justify-between py-2 border-b border-gray-100 space-y-2 sm:space-y-0">
                        <div className="flex-1">
                          <div className="text-sm font-medium">{item.productName}</div>
                          <div className="text-xs text-gray-500">₵{Number(item.price).toFixed(2)} each</div>
                        </div>
                        <div className="flex items-center space-x-1 sm:space-x-2">
                          <Button
                            size="small"
                            type="text"
                            icon={<span className="text-xs">−</span>}
                            onClick={() => handleUpdateItemQuantity(index, item.quantity - 1)}
                            className="w-6 h-6 flex items-center justify-center"
                          />
                          <span className="w-8 text-center text-sm">{item.quantity}</span>
                          <Button
                            size="small"
                            type="text"
                            icon={<span className="text-xs">+</span>}
                            onClick={() => handleUpdateItemQuantity(index, item.quantity + 1)}
                            className="w-6 h-6 flex items-center justify-center"
                          />
                          <Button
                            size="small"
                            type="text"
                            icon={<DeleteOutlined />}
                            onClick={() => handleRemoveItem(index)}
                            className="w-6 h-6 flex items-center justify-center text-red-500"
                          />
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Total Section */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>TOTAL</span>
                    <span className="font-bold">₵{totalAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>TAX</span>
                    <span>₵0.00</span>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>NET</span>
                    <span>₵{totalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Customer Info */}
              <div className="bg-blue-50 rounded-lg p-3 mb-4">
                <div className="text-xs text-gray-600 mb-1">Customer</div>
                <div className="font-medium text-sm">{selectedCustomer}</div>
                {savedSales.length > 0 && (
                  <div className="text-xs text-blue-600 mt-1">
                    {savedSales.length} saved sale{savedSales.length > 1 ? 's' : ''}
                  </div>
                )}
              </div>

              {/* Payment Method */}
              <div className="space-y-3">
                <Form form={form} layout="vertical">
                  <Form.Item
                    name="paymentMethod"
                    initialValue="cash"
                    className="mb-4"
                  >
                    <Select className="w-full" size="large" disabled={isLocked}>
                      <Select.Option value="cash">💵 Cash</Select.Option>
                      <Select.Option value="card">💳 Card</Select.Option>
                      <Select.Option value="mobile_money">📱 Mobile Money</Select.Option>
                    </Select>
                  </Form.Item>
                </Form>

                {/* Action Buttons */}
                <div className="grid grid-cols-2 gap-2 mb-3 lg:mb-4">
                  <Button
                    className="bg-orange-400 text-white border-0 hover:bg-orange-500"
                    onClick={() => {
                      // Toggle between Walk-in Customer and Custom Customer
                      const newCustomer = selectedCustomer === 'Walk-in Customer' ? 'Custom Customer' : 'Walk-in Customer';
                      setSelectedCustomer(newCustomer);
                      showMessage('success', `Customer set to: ${newCustomer}`);
                    }}
                  >
                    👥 {selectedCustomer === 'Walk-in Customer' ? 'Walk-in' : 'Custom'}
                  </Button>
                  <Button
                    className={`text-white border-0 ${isLocked ? 'bg-red-500 hover:bg-red-600' : 'bg-orange-400 hover:bg-orange-500'}`}
                    onClick={() => {
                      setIsLocked(!isLocked);
                      showMessage('success', isLocked ? 'POS Unlocked' : 'POS Locked');
                    }}
                  >
                    {isLocked ? '🔓 Unlock' : '🔒 Lock'}
                  </Button>
                </div>

                {/* Sale Button - Save current sale without payment */}
                <Button
                  className="w-full h-10 lg:h-12 bg-red-500 text-white border-0 hover:bg-red-600 text-sm lg:text-lg font-bold mb-2"
                  onClick={() => {
                    if (items.length === 0) {
                      showMessage('error', 'Please add items to cart');
                      return;
                    }
                    // Save sale without completing payment (for later)
                    const saleData = {
                      id: Date.now(),
                      items: [...items],
                      totalAmount,
                      customer: selectedCustomer,
                      timestamp: new Date().toISOString(),
                      status: 'pending'
                    };
                    setSavedSales(prev => [...prev, saleData]);
                    showMessage('success', `Sale saved as pending! Total: ₵${totalAmount.toFixed(2)}`);
                    // Clear cart after saving
                    setItems([]);
                    setSelectedProduct(null);
                    setQuantity(1);
                    setTotalAmount(0);
                    setCurrentLoadedSaleId(null); // Reset loaded sale tracking
                    form.resetFields();
                    productForm.resetFields();
                  }}
                >
                  🗑️ SALE (Save Pending)
                </Button>

                {/* Pay Button - Complete the sale with payment */}
                <Button
                  type="primary"
                  className="w-full h-12 lg:h-16 bg-green-500 border-0 hover:bg-green-600 text-lg lg:text-xl font-bold"
                  onClick={handleSubmit}
                  loading={isSubmitting || isGeneratingReceipt}
                >
                  💰 PAY (Complete Sale)
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Receipt Preview Modal */}
      <Modal
        title={
          <div className="flex items-center text-gray-800">
            <PrinterOutlined className="mr-2" />
            <span>Receipt Preview</span>
          </div>
        }
        open={receiptPreviewVisible}
        onCancel={() => {
          // Close the modal and reset the receipt state
          setReceiptPreviewVisible(false);
          setReceiptUrl(null);
          setHasPrinted(false);

          // Reset the forms to start a new sale
          form.resetFields();
          productForm.resetFields();
          setItems([]);
          setSelectedProduct(null);
          setQuantity(1);
          setTotalAmount(0);
        }}
        width={500}
        centered
        className="receipt-preview-modal"
        footer={[
          <Button
            key="close"
            onClick={() => {
              // Close the modal and reset the receipt state
              setReceiptPreviewVisible(false);
              setReceiptUrl(null);
              setHasPrinted(false);

              // Reset the forms to start a new sale
              form.resetFields();
              productForm.resetFields();
              setItems([]);
              setSelectedProduct(null);
              setQuantity(1);
              setTotalAmount(0);
            }}
            className="border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Close & New Sale
          </Button>,
          <Button
            key="print"
            type="primary"
            icon={<PrinterOutlined />}
            onClick={() => {
              // If already printed once, reset the flag to allow printing again
              if (hasPrinted) {
                setHasPrinted(false);
              }
              handlePrintReceipt();
            }}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {hasPrinted ? "Print Again" : "Print Receipt"}
          </Button>,
        ]}
      >
        <div className="flex flex-col items-center">
          {receiptUrl ? (
            <div className="receipt-image-container">
              <AntImage
                src={receiptUrl}
                alt="Receipt"
                className="receipt-image"
                style={{ maxWidth: "100%" }}
              />
            </div>
          ) : (
            <div className="flex h-64 items-center justify-center">
              <Spin size="large" />
            </div>
          )}
        </div>
      </Modal>

      {/* Saved Sales Modal */}
      <Modal
        title="Saved Sales"
        open={savedSalesModalVisible}
        onCancel={() => setSavedSalesModalVisible(false)}
        width={600}
        centered
        footer={[
          <Button
            key="close"
            onClick={() => setSavedSalesModalVisible(false)}
            className="border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Close
          </Button>
        ]}
      >
        <div className="max-h-96 overflow-y-auto">
          {savedSales.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ShoppingCartOutlined className="text-4xl mb-4" />
              <p>No saved sales available</p>
            </div>
          ) : (
            <div className="space-y-3">
              {savedSales.map((sale) => (
                <div key={sale.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-800">
                          {sale.customer}
                        </span>
                        <span className="text-lg font-bold text-green-600">
                          ₵{sale.totalAmount.toFixed(2)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 mb-2">
                        {new Date(sale.timestamp).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">
                        {sale.items.length} item{sale.items.length > 1 ? 's' : ''}: {' '}
                        {sale.items.map((item: any) => item.productName).join(', ')}
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col space-y-2">
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => loadSavedSale(sale)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Load Sale
                      </Button>
                      <Button
                        danger
                        size="small"
                        onClick={() => deleteSavedSale(sale.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Modal>

      {/* Camera Barcode Scanner - Commented out for now */}
      {/*
      <BarcodeScanner
        isOpen={isScannerOpen}
        onClose={closeScanner}
        onScan={handleBarcodeScanned}
        title="Scan Product Barcode"
      />
      */}
    </SlidingPanel>
  );
};

export default SalesFormPanel;
