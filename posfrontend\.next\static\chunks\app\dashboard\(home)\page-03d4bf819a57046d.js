(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{78023:(e,t,s)=>{Promise.resolve().then(s.bind(s,30415))},30415:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>en});var a=s(95155),l=s(12115),r=s(36060),n=s(7165),i=s(63065),o=s(72093),c=s(22810),d=s(2796),x=s(71349),h=s(89801),m=s(16419);let u=e=>{let{className:t=""}=e,[s,r]=(0,l.useState)([]),{data:n,isLoading:c,error:d,refetch:x}=(0,i.VL)({page:1,limit:100,search:""});(0,l.useEffect)(()=>{if((null==n?void 0:n.success)&&n.data){let e=(n.data.users||[]).filter(e=>"admin"===e.role).map(e=>{let t="Monthly",s=89,a=1;if(e.lastPaymentDate&&e.nextPaymentDue){let l=new Date(e.lastPaymentDate),r=Math.round((new Date(e.nextPaymentDue).getTime()-l.getTime())/864e5);console.log("\uD83D\uDCCA User ".concat(e.email," - Days between payments: ").concat(r)),r>=350?(t="Annual",s=360,a=12):r>=80?(t="Quarterly",s=108,a=3):(t="Monthly",s=40,a=1)}return{id:e.id,name:e.name||"Admin User",email:e.email,plan:t,amount:s,status:"paid"===e.paymentStatus?"Active":"pending"===e.paymentStatus?"Pending":"overdue"===e.paymentStatus?"Overdue":"Inactive",nextPaymentDue:e.nextPaymentDue?new Date(e.nextPaymentDue).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",subscriptionPeriod:a}});r(e),console.log("✅ Formatted admin subscriptions from Redux:",e)}},[n]);let h=()=>{x()},u=e=>{switch(e.toLowerCase()){case"active":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},f=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase();return c?(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-md border border-gray-200 ".concat(t),children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"})}),(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(m.A,{style:{fontSize:24},spin:!0})})})]}):d?(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-md border border-gray-200 ".concat(t),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"}),(0,a.jsx)("button",{onClick:h,className:"text-sm text-blue-600 cursor-pointer hover:underline",children:"Retry"})]}),(0,a.jsx)("div",{className:"text-center text-red-500 py-8",children:(0,a.jsxs)("p",{children:["Error loading subscriptions: ",d.toString()]})})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-md border border-gray-200 ".concat(t),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"}),(0,a.jsx)("button",{onClick:h,className:"text-sm text-blue-600 cursor-pointer hover:underline",children:"Refresh"})]}),0===s.length?(0,a.jsx)("div",{className:"text-center text-gray-500 py-8",children:(0,a.jsx)("p",{children:"No admin subscriptions found"})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Admin"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Next Payment"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-medium text-sm",children:f(e.name)})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.plan}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₵",e.amount,".00"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(u(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.nextPaymentDue})]},e.id))})]})})]})};var f=s(77711),p=s(21455),y=s.n(p);let g=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),v=e=>{let{users:t}=e,s=(0,l.useMemo)(()=>{let e=t.filter(e=>"admin"===e.role&&e.lastPaymentDate);console.log("\uD83D\uDCCA Revenue Chart - Admin users found:",e.length);let s=[];for(let e=11;e>=0;e--){let t=y()().subtract(e,"month");s.push({monthKey:t.format("YYYY-MM"),monthLabel:t.format("MMM YYYY"),revenue:0,adminCount:0})}e.forEach(e=>{if(e.lastPaymentDate&&e.nextPaymentDue){let t=y()(e.lastPaymentDate),a=y()(e.nextPaymentDue).diff(t,"day"),l=89;a>=350?l=1014:a>=80&&(l=259),console.log("\uD83D\uDCB0 User ".concat(e.email,": ").concat(a," days = ₵").concat(l));let r=t.format("YYYY-MM"),n=s.find(e=>e.monthKey===r);n&&(n.revenue+=l)}});let a=s.map(e=>({x:e.monthLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Revenue chart data:",a),a},[t]),r=[{name:"Revenue",data:s}];return 0===s.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No revenue data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Revenue will appear when admins make payments"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(g,{options:{chart:{type:"area",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.7,opacityTo:.1,stops:[0,90,100]}},stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},tooltip:{y:{formatter:function(e){return"₵".concat(e.toFixed(2))}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return"₵".concat(e)},style:{fontSize:"12px"}}}},series:r,type:"area",height:250})})},j=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),b=e=>{let{users:t}=e,s=(0,l.useMemo)(()=>{let e=t.filter(e=>"admin"===e.role&&"paid"===e.paymentStatus&&e.lastPaymentDate&&e.nextPaymentDue),s=0,a=0,l=0;return e.forEach(e=>{let t=y()(e.lastPaymentDate),r=y()(e.nextPaymentDue).diff(t,"day");r>=350?l++:r>=80?a++:s++}),{labels:["Monthly","Quarterly","Annual"],series:[s,a,l],total:s+a+l}},[t]),r={chart:{type:"donut",height:300},colors:["#5750F1","#0ABEF9","#10B981"],labels:s.labels,legend:{position:"bottom",horizontalAlign:"center",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},plotOptions:{pie:{donut:{size:"60%",labels:{show:!0,total:{show:!0,label:"Total Admins",fontSize:"16px",fontWeight:600,color:"#374151",formatter:function(){return s.total.toString()}},value:{show:!0,fontSize:"24px",fontWeight:700,color:"#111827"}}}}},dataLabels:{enabled:!0,formatter:function(e,t){let s=t.w.config.series[t.seriesIndex];return 0===s?"":"".concat(s)},style:{fontSize:"14px",fontWeight:700,colors:["#ffffff"]},dropShadow:{enabled:!0,top:1,left:1,blur:1,color:"#000",opacity:.45}},tooltip:{y:{formatter:function(e,t){let{seriesIndex:a}=t;s.labels[a];let l=(e/s.total*100).toFixed(1);return"".concat(e," admins (").concat(l,"%)")}}},responsive:[{breakpoint:480,options:{chart:{height:250},legend:{position:"bottom"}}}]};return 0===s.total?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No subscription data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when admins have active subscriptions"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(j,{options:r,series:s.series,type:"donut",height:250})})},N=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),w=e=>{let{users:t}=e,s=(0,l.useMemo)(()=>{let e=new Map;t.forEach(t=>{if(t.createdAt){let s=y()(t.createdAt).format("YYYY-MM"),a=e.get(s)||{admins:0,cashiers:0,total:0};"admin"===t.role?a.admins++:"cashier"===t.role&&a.cashiers++,a.total++,e.set(s,a)}});let s=Array.from(e.entries()).sort((e,t)=>{let[s]=e,[a]=t;return s.localeCompare(a)}).slice(-12),a=0,l=0,r=0,n=[],i=[],o=[];for(let[e,t]of s){a+=t.admins,l+=t.cashiers,r+=t.total;let s=y()(e).format("MMM YYYY");n.push({x:s,y:a}),i.push({x:s,y:l}),o.push({x:s,y:r})}return{adminSeries:n,cashierSeries:i,totalSeries:o}},[t]),r=[{name:"Total Users",data:s.totalSeries},{name:"Admins",data:s.adminSeries},{name:"Cashiers",data:s.cashierSeries}];return 0===s.totalSeries.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No user growth data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear as users register over time"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(N,{options:{chart:{type:"line",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9","#10B981"],stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:{formatter:function(e){return"".concat(e," users")}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}}},series:r,type:"line",height:250})})},A=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),S=e=>{let{users:t}=e,s=(0,l.useMemo)(()=>{let e=t.filter(e=>"admin"===e.role),s=0,a=0,l=0,r=0;return e.forEach(e=>{switch(e.paymentStatus){case"paid":s++;break;case"overdue":a++;break;case"pending":l++;break;default:r++}}),{labels:["Active (Paid)","Overdue","Pending","Inactive"],series:[s,a,l,r],total:s+a+l+r}},[t]),r={chart:{type:"bar",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981","#EF4444","#F59E0B","#6B7280"],plotOptions:{bar:{horizontal:!1,columnWidth:"60%",borderRadius:4}},dataLabels:{enabled:!0,style:{fontSize:"12px",fontWeight:600,colors:["#fff"]}},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},legend:{show:!1},tooltip:{y:{formatter:function(e,t){let{dataPointIndex:a}=t;s.labels[a];let l=(e/s.total*100).toFixed(1);return"".concat(e," admins (").concat(l,"%)")}}},xaxis:{categories:s.labels,axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"},rotate:-45}},yaxis:{labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}}},n=[{name:"Admin Count",data:s.series}];return 0===s.total?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No payment status data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when admin users are registered"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(A,{options:r,series:n,type:"bar",height:250})})},D=e=>{var t,s,l,r,f;let{className:p=""}=e,{data:y,isLoading:g,error:j}=(0,n.P)(),{data:N,isLoading:A}=(0,i.VL)({page:1,limit:1e3,search:""}),D=null==y?void 0:y.data,M=(null==N?void 0:null===(t=N.data)||void 0===t?void 0:t.users)||[];return(console.log("\uD83D\uDD0D SuperAdmin Dashboard Data:",{dashboardData:y,stats:D,usersCount:M.length,dashboardLoading:g,usersLoading:A,dashboardError:j}),g||A)?(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(m.A,{style:{fontSize:48},spin:!0})})}):j?(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})}):(0,a.jsxs)("div",{className:"space-y-6 ".concat(p),children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SuperAdmin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive overview of your POS system performance"})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(h.A,{title:"Total Revenue",value:Number(null==D?void 0:null===(s=D.revenue)||void 0===s?void 0:s.value)||0,prefix:"₵",precision:0,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Monthly recurring revenue"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(h.A,{title:"Active Stores",value:Number(null==D?void 0:null===(l=D.stores)||void 0===l?void 0:l.value)||0,precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Stores using the system"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(h.A,{title:"Admin Users",value:Number(null==D?void 0:null===(r=D.admins)||void 0===r?void 0:r.value)||0,precision:0,valueStyle:{color:"#722ed1",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Paying admin accounts"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(h.A,{title:"Total Users",value:Number(null==D?void 0:null===(f=D.users)||void 0===f?void 0:f.value)||0,precision:0,valueStyle:{color:"#fa8c16",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"All system users"})]})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Revenue Trends",className:"shadow-md h-96",children:(0,a.jsx)(v,{users:M})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Subscription Distribution",className:"shadow-md h-96",children:(0,a.jsx)(b,{users:M})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"User Growth",className:"shadow-md h-96",children:(0,a.jsx)(w,{users:M})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Payment Status Overview",className:"shadow-md h-96",children:(0,a.jsx)(S,{users:M})})})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(u,{className:"shadow-lg"})})]})};var M=s(21633),P=s(93968),R=s(6457),k=s(56458),z=s(17297),C=s(68787),F=s(5099),Y=s(21703),T=s(83391),L=s(76046),E=s(70854);let W=()=>{let e=(0,T.d4)(e=>e.auth.user),t=(0,L.useRouter)(),{isActive:s,daysRemaining:l,needsPayment:r,status:n}=(0,E._)();if(console.log("\uD83D\uDCB3 PaymentStatusWidget - Status:",{userRole:null==e?void 0:e.role,userPaymentStatus:null==e?void 0:e.paymentStatus,isActive:s,daysRemaining:l,needsPayment:r,status:n,nextPaymentDue:null==e?void 0:e.nextPaymentDue,lastPaymentDate:null==e?void 0:e.lastPaymentDate}),(null==e?void 0:e.paymentStatus)==="pending"&&console.log("\uD83D\uDEA8 PaymentStatusWidget - PENDING USER detected but showing as active! This is the UI bug!"),!e||"superadmin"===e.role)return null;let i=()=>{t.push("/payment")};return(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Subscription Status"}),(0,a.jsx)("button",{onClick:i,className:"text-blue-600 hover:text-blue-700 text-sm",children:"Manage"})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(Y.A,{type:"circle",percent:(()=>{if(!e.nextPaymentDue||!e.lastPaymentDate)return 0;let t=y()(e.lastPaymentDate),s=y()(e.nextPaymentDue),a=y()(),l=s.diff(t,"day");if(l<=0)return 0;let r=a.diff(t,"day");return r<0?0:Math.min(100,Math.round(r/l*100))})(),status:"pending"===e.paymentStatus?"warning":"overdue"!==e.paymentStatus&&"inactive"!==e.paymentStatus&&("paid"!==e.paymentStatus||s)?null!==l&&l<=7?"warning":"success":"exception",strokeColor:"pending"===e.paymentStatus?"#faad14":"overdue"!==e.paymentStatus&&"inactive"!==e.paymentStatus&&("paid"!==e.paymentStatus||s)?null!==l&&l<=7?"#faad14":"#52c41a":"#f5222d",className:"mx-auto",trailColor:"#f0f0f0"}),(0,a.jsx)("div",{className:"text-center mt-4",children:"paid"===e.paymentStatus&&s?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-green-500 font-medium",children:"Your subscription is active"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵40/month)"}),null!==l&&(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:l>0?"Renews in ".concat(l," day").concat(l>1?"s":""):"Renewal due today"})]}):"pending"===e.paymentStatus?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-yellow-500 font-medium",children:"Your payment is pending verification"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Please wait while we verify your payment"})]}):"overdue"===e.paymentStatus?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-500 font-medium",children:"Your payment is overdue"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Please make a payment to continue using the system"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-500 font-medium",children:"Your subscription is inactive"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Make a payment to continue using the system"})]})}),r&&(0,a.jsx)("button",{onClick:i,className:"w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors",children:"Manage Subscription"})]})]})},B=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),O=e=>{let{sales:t}=e,s=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Sales Chart - Sales found:",t.length);let e=[];for(let t=29;t>=0;t--){let s=y()().subtract(t,"day");e.push({dayKey:s.format("YYYY-MM-DD"),dayLabel:s.format("MMM DD"),salesCount:0,revenue:0})}t.forEach(t=>{if(t.transactionDate){let s=y()(t.transactionDate).format("YYYY-MM-DD"),a=e.find(e=>e.dayKey===s);a&&(a.salesCount++,a.revenue+=parseFloat(t.totalAmount||"0"))}});let s=e.map(e=>({x:e.dayLabel,y:e.salesCount})),a=e.map(e=>({x:e.dayLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Sales chart data:",{salesData:s.slice(-7),revenueData:a.slice(-7)}),{salesData:s,revenueData:a}},[t]),r=[{name:"Sales Count",type:"line",data:s.salesData},{name:"Revenue",type:"line",yAxisIndex:1,data:s.revenueData}];return 0===s.salesData.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No sales data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(B,{options:{chart:{type:"line",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9"],stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:[{formatter:function(e){return"".concat(e," sales")}},{formatter:function(e){return"₵".concat(e.toFixed(2))}}]},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"},rotate:-45}},yaxis:[{title:{text:"Number of Sales",style:{fontSize:"12px"}},labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}},{opposite:!0,title:{text:"Revenue (₵)",style:{fontSize:"12px"}},labels:{formatter:function(e){return"₵".concat(e.toFixed(0))},style:{fontSize:"12px"}}}]},series:r,type:"line",height:250})})},I=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),G=e=>{let{products:t,sales:s}=e,r=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Products Chart - Products found:",t.length,"Sales found:",s.length),console.log("\uD83D\uDCCA Sample sale structure:",s[0]),console.log("\uD83D\uDCCA Sample product structure:",t[0]);let e=new Map;t.forEach(t=>{let s=String(t.id||t.productId||t._id);e.set(s,{name:t.name||t.productName||"Unknown Product",count:0,revenue:0,productId:s})}),console.log("\uD83D\uDCCA Initialized product sales map:",Array.from(e.entries()).slice(0,3)),s.forEach((t,s)=>{console.log("\uD83D\uDCCA Processing sale ".concat(s,":"),{saleId:t.id,saleItems:t.saleItems,items:t.items,products:t.products});let a=t.saleItems||t.items||t.products||[];Array.isArray(a)&&a.forEach((t,s)=>{var a,l;console.log("\uD83D\uDCCA Processing item ".concat(s,":"),t);let r=String(t.productId||t.product_id||t.id||(null===(a=t.product)||void 0===a?void 0:a.id)||(null===(l=t.product)||void 0===l?void 0:l.productId)||""),n=Number(t.quantity||t.qty||t.amount||1),i=parseFloat(t.totalPrice||t.total_price||t.price||t.total||t.amount||"0");if(console.log("\uD83D\uDCCA Extracted data:",{productId:r,quantity:n,totalPrice:i}),r&&e.has(r)){let t=e.get(r);t.count+=n,t.revenue+=i,console.log("\uD83D\uDCCA Updated product ".concat(r,":"),t)}else console.log("\uD83D\uDCCA Product ".concat(r," not found in products map"))})});let a=Array.from(e.values()).filter(e=>e.count>0).sort((e,t)=>t.count-e.count);console.log("\uD83D\uDCCA Products with sales:",a);let l=a.slice(0,15),r=l.map(e=>e.name.length>20?e.name.substring(0,17)+"...":e.name),n=l.map(e=>e.count),i=l.map(e=>e.revenue);return console.log("\uD83D\uDCC8 Final chart data:",{totalProducts:t.length,productsWithSales:a.length,topProductsShown:l.length,categories:r,salesData:n,revenueData:i}),{categories:r,salesData:n,revenueData:i,totalProductsWithSales:a.length,totalProducts:t.length}},[t,s]),n=r.categories.length>8,i={chart:{type:"bar",height:n?Math.max(300,25*r.categories.length):300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9"],plotOptions:{bar:{horizontal:n,columnWidth:n?"70%":"60%",borderRadius:4}},dataLabels:{enabled:!1},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:[{formatter:function(e){return"".concat(e," units sold")}},{formatter:function(e){return"₵".concat(e.toFixed(2)," revenue")}}]},xaxis:{categories:r.categories,axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"11px"},rotate:n?0:-45,maxHeight:n?void 0:60,trim:!0}},yaxis:[{title:{text:"Units Sold",style:{fontSize:"12px"}},labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}},{opposite:!0,title:{text:"Revenue (₵)",style:{fontSize:"12px"}},labels:{formatter:function(e){return"₵".concat(e.toFixed(0))},style:{fontSize:"12px"}}}]},o=[{name:"Units Sold",data:r.salesData},{name:"Revenue",data:r.revenueData}];if(0===r.categories.length)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No product sales data available"}),(0,a.jsxs)("p",{className:"text-sm",children:["You have ",r.totalProducts," products, but none have been sold yet"]}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when products are sold"})]})});let c=n?Math.max(250,25*r.categories.length):230;return(0,a.jsxs)("div",{className:n?"auto":"h-64",style:{minHeight:n?c+50:void 0},children:[(0,a.jsxs)("div",{className:"mb-2 text-xs text-gray-500 text-center",children:["Showing top ",r.categories.length," of ",r.totalProductsWithSales," products with sales (",r.totalProducts," total products)"]}),(0,a.jsx)(I,{options:i,series:o,type:"bar",height:c})]})},U=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),_=e=>{let{sales:t}=e,s=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Revenue Chart - Sales found:",t.length);let e=[];for(let t=11;t>=0;t--){let s=y()().subtract(t,"month");e.push({monthKey:s.format("YYYY-MM"),monthLabel:s.format("MMM YYYY"),revenue:0,salesCount:0})}t.forEach(t=>{if(t.transactionDate){let s=y()(t.transactionDate).format("YYYY-MM"),a=e.find(e=>e.monthKey===s);a&&(a.revenue+=parseFloat(t.totalAmount||"0"),a.salesCount++)}});let s=e.map(e=>({x:e.monthLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Revenue chart data:",s),s},[t]),r=[{name:"Revenue",data:s}];return 0===s.length||s.every(e=>0===e.y)?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No revenue data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(U,{options:{chart:{type:"area",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.7,opacityTo:.1,stops:[0,90,100]}},stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},tooltip:{y:{formatter:function(e){return"₵".concat(e.toFixed(2))}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return"₵".concat(e.toFixed(0))},style:{fontSize:"12px"}}}},series:r,type:"area",height:250})})},K=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),H=e=>{let{stats:t,formatLargeNumber:s}=e,l=[{name:"Revenue",data:[t.revenue.value]},{name:"COGS",data:[t.cogs.value]},{name:"Expenses",data:[t.expenses.value]},{name:"Profit",data:[t.profit.value]}],r=e=>s?s(e):"₵".concat(e.toLocaleString());return(0,a.jsxs)("div",{className:"h-80",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,a.jsx)("div",{className:"text-green-600 font-semibold",children:"Profit Margin"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-green-700",children:[t.profitMargin.value.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,a.jsx)("div",{className:"text-blue-600 font-semibold",children:"Net Profit"}),(0,a.jsx)("div",{className:"text-lg font-bold text-blue-700",children:r(t.profit.value)})]})]})}),(0,a.jsx)(K,{options:{chart:{type:"bar",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981","#EF4444","#F59E0B","#3B82F6"],plotOptions:{bar:{horizontal:!1,columnWidth:"60%",borderRadius:4}},dataLabels:{enabled:!0,formatter:function(e){return r(e)},style:{fontSize:"12px",fontWeight:"bold"}},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:["Financial Overview"],axisBorder:{show:!1},axisTicks:{show:!1}},yaxis:{labels:{formatter:function(e){return r(e)}}},fill:{opacity:1},tooltip:{y:{formatter:function(e){return r(e)}}},legend:{position:"top",horizontalAlign:"center"},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}}},series:l,type:"bar",height:250})]})},q=(0,f.default)(()=>Promise.all([s.e(7776),s.e(2242)]).then(s.bind(s,92242)),{loadableGenerated:{webpack:()=>[92242]},ssr:!1}),Q=e=>{let{sales:t}=e,s=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Daily Sales Chart - Sales found:",t.length);let e=[];for(let t=6;t>=0;t--){let s=y()().subtract(t,"day");e.push({dayKey:s.format("YYYY-MM-DD"),dayLabel:s.format("ddd DD"),salesCount:0,revenue:0})}t.forEach(t=>{if(t.transactionDate){let s=y()(t.transactionDate).format("YYYY-MM-DD"),a=e.find(e=>e.dayKey===s);a&&(a.salesCount++,a.revenue+=parseFloat(t.totalAmount||"0"))}});let s=e.map(e=>({x:e.dayLabel,y:e.salesCount})),a=e.map(e=>({x:e.dayLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Daily Sales chart data:",{salesData:s,revenueData:a}),{salesData:s,revenueData:a}},[t]),r=[{name:"Sales Count",type:"column",data:s.salesData},{name:"Revenue (₵)",type:"line",data:s.revenueData}],n={chart:{height:350,type:"line",stacked:!1,toolbar:{show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1},stroke:{width:[0,4],curve:"smooth"},plotOptions:{bar:{columnWidth:"50%"}},fill:{opacity:[.85,1],gradient:{inverseColors:!1,shade:"light",type:"vertical",opacityFrom:.85,opacityTo:.55,stops:[0,100,100,100]}},labels:s.salesData.map(e=>e.x),markers:{size:0},xaxis:{type:"category",labels:{style:{fontSize:"12px",fontWeight:500}}},yaxis:[{title:{text:"Sales Count",style:{color:"#1f77b4",fontSize:"14px",fontWeight:600}},labels:{style:{colors:"#1f77b4"},formatter:e=>Math.round(e).toString()}},{opposite:!0,title:{text:"Revenue (₵)",style:{color:"#ff7f0e",fontSize:"14px",fontWeight:600}},labels:{style:{colors:"#ff7f0e"},formatter:e=>"₵".concat(e.toFixed(0))}}],tooltip:{shared:!0,intersect:!1,y:[{formatter:e=>"".concat(e," sales")},{formatter:e=>"₵".concat(e.toFixed(2))}]},legend:{horizontalAlign:"left",offsetX:40},colors:["#1f77b4","#ff7f0e"],grid:{borderColor:"#f1f1f1",strokeDashArray:3}};return 0===s.salesData.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No daily sales data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(q,{options:n,series:r,type:"line",height:250})})},V=e=>{var t,s;let{className:l=""}=e,{data:r,isLoading:i,error:u}=(0,n.P)(),{data:f,isLoading:p}=(0,M.JZ)({page:1,limit:1e3,search:""}),{data:y,isLoading:g}=(0,P.r3)({page:1,limit:1e3,search:""}),v=null==r?void 0:r.data,j=(null==f?void 0:null===(t=f.data)||void 0===t?void 0:t.sales)||[],b=(null==y?void 0:null===(s=y.data)||void 0===s?void 0:s.products)||[];if(i||p||g)return(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(m.A,{style:{fontSize:48},spin:!0})})});if(u)return(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})});if(!v)return(0,a.jsx)("div",{className:"text-center text-gray-500 p-8",children:(0,a.jsx)("p",{children:"No dashboard data available."})});j.length,j.reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0),b.length;let N=e=>{let t=(100*e).toFixed(1),s=e>=0;return{value:"".concat(s?"+":"").concat(t,"%"),color:s?"#3f8600":"#cf1322",icon:s?(0,a.jsx)(k.A,{}):(0,a.jsx)(z.A,{})}};function w(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=t?"₵":"";return e>=1e9?"".concat(s).concat((e/1e9).toFixed(1),"B"):e>=1e6?"".concat(s).concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat(s).concat((e/1e3).toFixed(1),"k"):t?"₵".concat(e.toLocaleString()):e.toLocaleString()}function A(e){let{value:t,isMoney:s=!1,isPercent:l=!1}=e,r=w(t,s),n="";return(r.includes("k")&&(n="k = thousand"),r.includes("M")&&(n="M = million"),r.includes("B")&&(n="B = billion"),l)?(0,a.jsxs)(a.Fragment,{children:[r," %"]}):n?(0,a.jsx)(R.A,{title:n,children:r}):(0,a.jsx)(a.Fragment,{children:r})}return(0,a.jsxs)("div",{className:"space-y-6 ".concat(l),children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your store operations and track performance"})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Revenue (Team) - This Month",value:v.revenue.value,valueRender:()=>(0,a.jsx)(A,{value:v.revenue.value,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:[(0,a.jsx)(C.A,{className:"mr-1"}),"Admin + Cashiers"]}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.revenue.growthRate).color},children:[N(v.revenue.growthRate).icon," ",N(v.revenue.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Total Sales - This Month",value:v.sales.value,valueRender:()=>(0,a.jsx)(A,{value:v.sales.value}),precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Completed transactions"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.sales.growthRate).color},children:[N(v.sales.growthRate).icon," ",N(v.sales.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Net Profit - This Month",value:v.profit.value,valueRender:()=>(0,a.jsx)(A,{value:v.profit.value,isMoney:!0}),precision:2,valueStyle:{color:v.profit.value>=0?"#3f8600":"#cf1322",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"After expenses & COGS"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.profit.growthRate).color},children:[N(v.profit.growthRate).icon," ",N(v.profit.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Products (All Time)",value:v.products.value,valueRender:()=>(0,a.jsx)(A,{value:v.products.value}),precision:0,valueStyle:{color:"#fa8c16",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"In inventory"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.products.growthRate).color},children:[N(v.products.growthRate).icon," ",N(v.products.growthRate).value]})]})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Cashiers - This Month",value:v.cashiers.value,valueRender:()=>(0,a.jsx)(A,{value:v.cashiers.value}),precision:0,valueStyle:{color:"#722ed1",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Team members"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.cashiers.growthRate).color},children:[N(v.cashiers.growthRate).icon," ",N(v.cashiers.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Expenses - This Month",value:v.expenses.value,valueRender:()=>(0,a.jsx)(A,{value:v.expenses.value,isMoney:!0}),precision:2,valueStyle:{color:"#cf1322",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Total expenses"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.expenses.growthRate).color},children:[N(v.expenses.growthRate).icon," ",N(v.expenses.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"COGS - This Month",value:v.cogs.value,valueRender:()=>(0,a.jsx)(A,{value:v.cogs.value,isMoney:!0}),precision:2,valueStyle:{color:"#fa541c",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Cost of goods sold"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.cogs.growthRate).color},children:[N(v.cogs.growthRate).icon," ",N(v.cogs.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(h.A,{title:"Profit Margin - This Month",value:v.profitMargin.value,valueRender:()=>(0,a.jsx)(A,{value:v.profitMargin.value,isPercent:!0}),precision:1,valueStyle:{color:v.profitMargin.value>=0?"#3f8600":"#cf1322",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Profit percentage"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:N(v.profitMargin.growthRate).color},children:[N(v.profitMargin.growthRate).icon," ",N(v.profitMargin.growthRate).value]})]})})})]}),(0,a.jsx)(c.A,{gutter:[16,16],className:"mb-6",children:(0,a.jsx)(d.A,{xs:24,children:(0,a.jsxs)(x.A,{title:"Daily Sales (Last 7 Days)",className:"shadow-md h-96",children:[(0,a.jsx)(Q,{sales:j}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(F.A,{className:"mr-1"}),"Sales count and revenue for the past week"]})]})})}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Sales Trends (30 Days)",className:"shadow-md h-96",children:(0,a.jsx)(O,{sales:j})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsxs)(x.A,{title:"Revenue Over Time (Team)",className:"shadow-md h-96",children:[(0,a.jsx)(_,{sales:j}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(C.A,{className:"mr-1"}),"Includes admin and cashier sales"]})]})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Profit & Loss Analysis (This Month)",className:"shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col",bodyStyle:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,a.jsx)(H,{stats:v,formatLargeNumber:w})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Product Performance",className:"shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col",bodyStyle:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,a.jsx)(G,{products:b,sales:j})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Payment Status",className:"shadow-md h-96",children:(0,a.jsx)(W,{})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Team Performance (This Month)",className:"shadow-md h-96",children:(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 h-full",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:w(v.sales.value)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Sales"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:N(v.sales.growthRate).color},children:[N(v.sales.growthRate).icon," ",N(v.sales.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w(v.revenue.value)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Team Revenue"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:N(v.revenue.growthRate).color},children:[N(v.revenue.growthRate).icon," ",N(v.revenue.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:v.cashiers.value}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Cashiers"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:N(v.cashiers.growthRate).color},children:[N(v.cashiers.growthRate).icon," ",N(v.cashiers.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:v.products.value}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Products"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:N(v.products.growthRate).color},children:[N(v.products.growthRate).icon," ",N(v.products.growthRate).value]})]})]})})})})]})]})};var J=s(43316),Z=s(96030),X=s(25955),$=s(55750),ee=s(9124),et=s(54084),es=s.n(et);y().extend(es());let ea=e=>{var t,s;let{className:r=""}=e,i=(0,T.d4)(e=>e.auth.user),u=(0,L.useRouter)(),[f,p]=(0,l.useState)(!1),{data:g,isLoading:v,error:j}=(0,n.P)(),{data:b,isLoading:N}=(0,M.JZ)({page:1,limit:1e3,search:""}),{data:w,isLoading:A}=(0,P.r3)({page:1,limit:1e3,search:""});null==g||g.data;let S=(null==b?void 0:null===(t=b.data)||void 0===t?void 0:t.sales)||[];null==w||null===(s=w.data)||void 0===s||s.products;let D=S.filter(e=>e.createdBy===(null==i?void 0:i.id));if(v||N||A)return(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(m.A,{style:{fontSize:48},spin:!0})})});if(j)return(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})});let R=y()().startOf("month"),k=y()().endOf("month"),z=D.filter(e=>y()(e.transactionDate).isBetween(R,k,null,"[]")),C=z.reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0),Y=z.length,E=Y>0?C/Y:0,W=y()(),B=D.filter(e=>y()(e.transactionDate).isSame(W,"day")).reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0);function I(e){let{value:t,isMoney:s=!1}=e,l=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=t?"₵":"";return e>=1e9?"".concat(s).concat((e/1e9).toFixed(1),"B"):e>=1e6?"".concat(s).concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat(s).concat((e/1e3).toFixed(1),"k"):t?"₵".concat(e.toLocaleString()):e.toLocaleString()}(t,s),r="";return l.includes("k")&&(r="k = thousand"),l.includes("M")&&(r="M = million"),l.includes("B")&&(r="B = billion"),r?(0,a.jsx)("span",{title:r,children:l}):(0,a.jsx)(a.Fragment,{children:l})}return(0,a.jsxs)("div",{className:"space-y-6 ".concat(r),children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",null==i?void 0:i.name,"!"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Ready to serve customers and process sales"})]})})}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(h.A,{title:"Today's Revenue",value:B,valueRender:()=>(0,a.jsx)(I,{value:B,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Earnings for today"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(h.A,{title:"Revenue (This Month)",value:C,valueRender:()=>(0,a.jsx)(I,{value:C,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Earnings for this month"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(h.A,{title:"Sales (This Month)",value:Y,valueRender:()=>(0,a.jsx)(I,{value:Y}),precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Transactions this month"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(h.A,{title:"Average Order (This Month)",value:E,valueRender:()=>(0,a.jsx)(I,{value:E,isMoney:!0}),precision:2,valueStyle:{color:"#722ed1",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Per transaction (monthly)"})]})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"My Sales Performance",className:"shadow-md h-96",children:(0,a.jsx)(O,{sales:D})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"My Revenue Trends",className:"shadow-md h-96",children:(0,a.jsx)(_,{sales:D})})})]}),(0,a.jsx)(c.A,{gutter:[16,16],className:"mb-6",children:(0,a.jsx)(d.A,{xs:24,children:(0,a.jsx)(x.A,{title:"Quick Actions",className:"shadow-md",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(J.Ay,{type:"primary",size:"large",className:"h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700 border-green-600",onClick:()=>p(!0),children:[(0,a.jsx)(Z.A,{className:"text-xl mb-2"}),"New Sale"]}),(0,a.jsxs)(J.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-blue-50 border-blue-300",onClick:()=>u.push("/dashboard/sales"),children:[(0,a.jsx)(F.A,{className:"text-xl mb-2"}),"View Sales"]}),(0,a.jsxs)(J.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-purple-50 border-purple-300",onClick:()=>u.push("/dashboard/products"),children:[(0,a.jsx)(X.A,{className:"text-xl mb-2"}),"Products"]}),(0,a.jsxs)(J.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-gray-50 border-gray-300",onClick:()=>u.push("/dashboard/profile"),children:[(0,a.jsx)($.A,{className:"text-xl mb-2"}),"Profile"]})]})})})}),(0,a.jsx)(ee.A,{isOpen:f,onClose:()=>p(!1),onSuccess:()=>{p(!1)}})]})},el=e=>{let{extractTimeFrame:t}=e,{user:s,isSuperAdmin:l,isAdmin:n,isCashier:i}=(0,r.A)();return s?l()?(0,a.jsx)(D,{}):n()?(0,a.jsx)(V,{}):i()?(0,a.jsx)(ea,{}):(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-5 shadow-md",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-gray-800",children:["Welcome, ",s.name,"!"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Your role (",s.role,') doesn"t have a specific dashboard view.']}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Please contact your administrator for assistance."})]}):null},er=e=>{let{selected_time_frame:t}=e,{user:s}=(0,T.d4)(e=>e.auth);if((0,l.useEffect)(()=>{console.log("DashboardContent - Mounted with params:",{selected_time_frame:t,user:null==s?void 0:s.name,role:null==s?void 0:s.role})},[t,s]),!s)return(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{children:"Please log in to view the dashboard."})]})});let r=function(e){return t=>null==e?void 0:e.split(",").find(e=>e.includes(t))}(t||void 0);return(0,a.jsx)("div",{className:"dashboard-content w-full overflow-hidden",children:(0,a.jsx)(el,{extractTimeFrame:r})})};function en(){let e=(0,L.useSearchParams)(),t=(null==e?void 0:e.get("selected_time_frame"))||null,[s,r]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{console.log("Dashboard Home mounted",{timeFrame:t});let e=setTimeout(()=>{r(!1)},500);return()=>clearTimeout(e)},[t]),s)?(0,a.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(m.A,{style:{fontSize:24},spin:!0})})}):(0,a.jsx)(er,{selected_time_frame:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3247,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,3414,6102,1614,5565,3252,1703,3201,821,921,8441,1517,7358],()=>t(78023)),_N_E=e.O()}]);