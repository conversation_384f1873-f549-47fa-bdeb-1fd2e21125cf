import dayjs from 'dayjs';

/**
 * Format a date string to a human-readable format
 * @param dateString The date string to format
 * @param format The format to use (default: 'MMM D, YYYY h:mm A')
 * @returns The formatted date string or 'N/A' if the date is invalid
 */
export const formatDate = (dateString?: string | null, format: string = 'MMM D, YYYY h:mm A'): string => {
  if (!dateString) return 'N/A';
  
  const date = dayjs(dateString);
  if (!date.isValid()) return 'N/A';
  
  return date.format(format);
};
