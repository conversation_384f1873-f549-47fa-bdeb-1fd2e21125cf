"use client";

import React, { useEffect } from "react";
import { Card } from "antd";
import PaystackPaymentForm from "@/components/Payment/PaystackPaymentForm";
import PaymentStatus from "@/components/Payment/PaymentStatus";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter } from "next/navigation";
import { userApi } from "@/reduxRTK/services/authApi";
import { usePaymentWebSocket } from "@/hooks/usePaymentWebSocket";
import Image from "next/image";

function PaymentStatusRedirector() {
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();
  useEffect(() => {
    if (user && user.paymentStatus === "paid") {
      router.replace("/dashboard");
    }
  }, [user, router]);
  return null;
}

function CashierPaymentNotice() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center border border-gray-200">
        <Image src="/images/access.jpg" alt="Admin Payment Required" className="mx-auto mb-6 w-32 h-32 object-contain" width={128} height={128} />
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h2>
        <p className="text-gray-600 mb-6">
          Your admin needs to make a payment for you to access the dashboard.<br/>
          Please contact your admin to renew the subscription.
        </p>
        <div className="mt-4">
          <span className="inline-block bg-yellow-100 text-yellow-800 text-sm px-4 py-2 rounded-full font-semibold">
            Waiting for admin payment
          </span>
        </div>
      </div>
    </div>
  );
}

function useCashierPolling(user: any) {
  const dispatch = useDispatch();
  useEffect(() => {
    if (!user || user.role !== "cashier" || user.paymentStatus === "paid") return;
    const interval = setInterval(() => {
      // @ts-ignore
      dispatch(userApi.endpoints.getCurrentUser.initiate(undefined, { forceRefetch: true }));
    }, 5000);
    return () => clearInterval(interval);
  }, [user, dispatch]);
}

export default function PaymentPage() {
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();
  usePaymentWebSocket();
  useCashierPolling(user);

  // Always render the redirector so it works for all roles
  // (including cashiers on the notice page)
  return (
    <>
      <PaymentStatusRedirector />
      {(!user) ? null :
        user.role === "cashier" && user.paymentStatus !== "paid" ? (
          <CashierPaymentNotice />
        ) : user.role === "admin" && user.paymentStatus !== "paid" ? (
          <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Subscription Payment</h1>
                <p className="mt-2 text-gray-600">Choose your subscription plan and complete the payment</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <PaystackPaymentForm />
                </div>
                <div className="md:col-span-1">
                  <PaymentStatus showPayButton={false} />
                  <Card
                    title={<span className="text-gray-800 pl-2">Payment Instructions</span>}
                    className="mt-6 shadow-md bg-white border border-gray-200"
                    styles={{
                      header: { backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' },
                      body: { backgroundColor: '#ffffff' }
                    }}
                  >
                    <ol className="list-decimal pl-5 space-y-2 text-gray-700">
                      <li>Select your preferred subscription plan (Monthly: <strong className="text-gray-900">₵40</strong>, Quarterly: <strong className="text-gray-900">₵108</strong>, or Annual: <strong className="text-gray-900">₵360</strong>)</li>
                      <li>Enter your email address for payment confirmation</li>
                      <li>Click <strong className="text-gray-900">&quot;Initialize Payment&quot;</strong> to set up your Paystack payment</li>
                      <li>Click <strong className="text-gray-900">&quot;Pay Now&quot;</strong> to complete payment securely via Paystack</li>
                      <li>Your subscription will be activated automatically after successful payment</li>
                    </ol>
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">💳 Accepted Payment Methods (via Paystack):</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• <strong>Cards:</strong> Visa, Mastercard, Verve (local & international)</li>
                        <li>• <strong>Mobile Money:</strong> MTN, Vodafone, AirtelTigo</li>
                      </ul>
                    </div>
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <h4 className="text-sm font-semibold text-green-800 mb-2">💡 Payment Status Not Updated?</h4>
                      <p className="text-sm text-green-700">
                        If your payment was successful but your status still shows as pending,
                        click the <strong>refresh button (🔄)</strong> in the Subscription Status card above to update your status.
                      </p>
                    </div>
                    <p className="mt-4 text-sm text-gray-600">
                      <strong>🔒 Secure Payment:</strong> All payments are processed securely through Paystack, Ghana&apos;s leading payment gateway.
                      Your subscription will be activated immediately after verification.
                      For any issues, please contact support.
                    </p>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        ) : null
      }
    </>
  );
} 