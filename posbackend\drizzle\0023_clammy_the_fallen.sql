ALTER TABLE "user_stores" DROP CONSTRAINT "user_stores_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "suppliers" ALTER COLUMN "created_by" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user_stores" ALTER COLUMN "created_by" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user_suppliers" ALTER COLUMN "created_by" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user_stores" ADD CONSTRAINT "user_stores_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;