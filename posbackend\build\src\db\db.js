"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.postgresDb = void 0;
const node_postgres_1 = require("drizzle-orm/node-postgres");
const pg_1 = require("pg");
const dotenv_1 = __importDefault(require("dotenv"));
const envalid_1 = require("envalid");
const pg_connection_string_1 = require("pg-connection-string");
const schema = __importStar(require("../db/schema")); // Import your schema
dotenv_1.default.config();
// Validate environment variables
const env = (0, envalid_1.cleanEnv)(process.env, {
    DATABASE_URL: (0, envalid_1.str)(),
});
// Parse the connection string
const dbConfig = (0, pg_connection_string_1.parse)(env.DATABASE_URL);
// Maximum number of connection retries
const MAX_CONNECTION_RETRIES = 5;
// Delay between retries (in milliseconds)
const RETRY_DELAY = 5000;
// Create an optimized PostgreSQL connection pool
const pool = new pg_1.Pool({
    host: dbConfig.host || undefined,
    port: dbConfig.port ? Number(dbConfig.port) : 5432,
    user: dbConfig.user || undefined,
    password: dbConfig.password || undefined,
    database: dbConfig.database || undefined,
    ssl: dbConfig.ssl || env.DATABASE_URL.includes("sslmode=require")
        ? { rejectUnauthorized: false }
        : undefined, // ✅ Proper SSL handling
    max: 20, // 🔥 Reduced max connections to prevent overwhelming DB
    min: 2, // 🔥 Keep minimum 2 connections ready
    idleTimeoutMillis: 30000, // 🔥 Reduced idle timeout
    connectionTimeoutMillis: 15000, // 🔥 Increased connection timeout to 15s
    statement_timeout: 60000, // 🔥 Increased statement timeout to 60s
    query_timeout: 60000, // 🔥 Increased query timeout to 60s
});
// Track connection status
let connectionRetries = 0;
// Add connection pool error handling
pool.on('error', (err) => {
    console.error('Database pool error:', err);
    // Attempt to reconnect if the error is connection-related
    if (err.message.includes('ECONNREFUSED') ||
        err.message.includes('ETIMEDOUT') ||
        err.message.includes('ENOTFOUND') ||
        err.message.includes('connection')) {
        attemptReconnect();
    }
});
// Add connection pool monitoring
pool.on('connect', () => {
    console.log('New database connection established');
    connectionRetries = 0; // Reset retry counter on successful connection
});
// Function to attempt reconnection
const attemptReconnect = async () => {
    if (connectionRetries >= MAX_CONNECTION_RETRIES) {
        console.error(`Failed to reconnect to database after ${MAX_CONNECTION_RETRIES} attempts`);
        return;
    }
    connectionRetries++;
    console.log(`Attempting to reconnect to database (attempt ${connectionRetries}/${MAX_CONNECTION_RETRIES})...`);
    // Wait before attempting to reconnect
    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    try {
        // Test the connection
        const client = await pool.connect();
        client.release();
        console.log('Database reconnection successful');
        connectionRetries = 0;
    }
    catch (error) {
        console.error('Database reconnection failed:', error);
        attemptReconnect(); // Try again
    }
};
// Verify initial connection
(async () => {
    try {
        const client = await pool.connect();
        console.log('Initial database connection successful');
        client.release();
    }
    catch (error) {
        console.error('Initial database connection failed:', error);
        attemptReconnect();
    }
})();
// We're using the direct drizzle instance without a wrapper for now
// Export the drizzle instance with the optimized pool
exports.postgresDb = (0, node_postgres_1.drizzle)(pool, { schema }); // ✅ Use schema
