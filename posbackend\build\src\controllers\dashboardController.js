"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleDashboardRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const modeValidator_1 = require("../utils/modeValidator");
const dashboardService_1 = require("../services/dashboardService");
const handleDashboardRequest = async (req, res) => {
    const { mode } = req.body;
    const requester = req.user;
    const validModes = ["stats"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            case "stats": {
                let stats;
                // Get stats based on user role
                if (requester.role === "superadmin") {
                    stats = await (0, dashboardService_1.getSuperAdminStats)(requester);
                }
                else if (requester.role === "admin") {
                    stats = await (0, dashboardService_1.getAdminStats)(requester);
                }
                else if (requester.role === "cashier") {
                    stats = await (0, dashboardService_1.getCashierStats)(requester);
                }
                else {
                    return (0, responseHelper_1.sendResponse)(res, 403, false, "Unauthorized: Invalid role");
                }
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Dashboard statistics retrieved successfully", stats);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid mode");
        }
    }
    catch (error) {
        console.error("Error in dashboard controller:", error);
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Error retrieving dashboard statistics");
    }
};
exports.handleDashboardRequest = handleDashboardRequest;
