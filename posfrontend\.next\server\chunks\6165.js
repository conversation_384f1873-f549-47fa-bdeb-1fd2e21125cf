"use strict";exports.id=6165,exports.ids=[6165],exports.modules={31111:(e,t,r)=>{r.d(t,{A:()=>N});var o=r(58009),a=r(56073),n=r.n(a),l=r(55681),s=r(22301),i=r(61876),c=r(2866),d=r(81567),u=r(27343),g=r(1439),p=r(43891),y=r(47285),f=r(10941),b=r(13662);let h=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:a,calc:n}=e,l=n(o).sub(r).equal(),s=n(t).sub(r).equal();return{[a]:Object.assign(Object.assign({},(0,y.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,g.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},m=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,a=e.fontSizeSM;return(0,f.oX)(e,{tagFontSize:a,tagLineHeight:(0,g.zA)(o(e.lineHeightSM).mul(a).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new p.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),v=(0,b.OF)("Tag",e=>h(m(e)),x);var C=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]]);return r};let w=o.forwardRef((e,t)=>{let{prefixCls:r,style:a,className:l,checked:s,onChange:i,onClick:c}=e,d=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:p}=o.useContext(u.QO),y=g("tag",r),[f,b,h]=v(y),m=n()(y,`${y}-checkable`,{[`${y}-checkable-checked`]:s},null==p?void 0:p.className,l,b,h);return f(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},a),null==p?void 0:p.style),className:m,onClick:e=>{null==i||i(!s),null==c||c(e)}})))});var A=r(92864);let P=e=>(0,A.A)(e,(t,r)=>{let{textColor:o,lightBorderColor:a,lightColor:n,darkColor:l}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:o,background:n,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}}),k=(0,b.bf)(["Tag","preset"],e=>P(m(e)),x),S=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},j=(0,b.bf)(["Tag","status"],e=>{let t=m(e);return[S(t,"success","Success"),S(t,"processing","Info"),S(t,"error","Error"),S(t,"warning","Warning")]},x);var $=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]]);return r};let O=o.forwardRef((e,t)=>{let{prefixCls:r,className:a,rootClassName:g,style:p,children:y,icon:f,color:b,onClose:h,bordered:m=!0,visible:x}=e,C=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:A,tag:P}=o.useContext(u.QO),[S,O]=o.useState(!0),N=(0,l.A)(C,["closeIcon","closable"]);o.useEffect(()=>{void 0!==x&&O(x)},[x]);let E=(0,s.nP)(b),D=(0,s.ZZ)(b),T=E||D,B=Object.assign(Object.assign({backgroundColor:b&&!T?b:void 0},null==P?void 0:P.style),p),z=w("tag",r),[I,R,L]=v(z),_=n()(z,null==P?void 0:P.className,{[`${z}-${b}`]:T,[`${z}-has-color`]:b&&!T,[`${z}-hidden`]:!S,[`${z}-rtl`]:"rtl"===A,[`${z}-borderless`]:!m},a,g,R,L),F=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||O(!1)},[,H]=(0,i.A)((0,i.d)(e),(0,i.d)(P),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${z}-close-icon`,onClick:F},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),F(t)},className:n()(null==e?void 0:e.className,`${z}-close-icon`)}))}}),U="function"==typeof C.onClick||y&&"a"===y.type,M=f||null,q=M?o.createElement(o.Fragment,null,M,y&&o.createElement("span",null,y)):y,W=o.createElement("span",Object.assign({},N,{ref:t,className:_,style:B}),q,H,E&&o.createElement(k,{key:"preset",prefixCls:z}),D&&o.createElement(j,{key:"status",prefixCls:z}));return I(U?o.createElement(d.A,{component:"Tag"},W):W)});O.CheckableTag=w;let N=O},51531:(e,t,r)=>{r.d(t,{A:()=>s});var o=r(45512);r(58009);var a=r(88206),n=r(3117),l=r(75238);let s=({isOpen:e,onClose:t,onConfirm:r,title:s,message:i,confirmText:c="Confirm",cancelText:d="Cancel",isLoading:u=!1,type:g="danger"})=>(0,o.jsx)(a.A,{title:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)(l.A,{style:{color:"danger"===g?"#ff4d4f":"warning"===g?"#faad14":"#1890ff",marginRight:8}}),(0,o.jsx)("span",{children:s})]}),open:e,onCancel:t,footer:[(0,o.jsx)(n.Ay,{onClick:t,disabled:u,children:d},"cancel"),(0,o.jsx)(n.Ay,{type:"danger"===g?"primary":"default",danger:"danger"===g,onClick:r,loading:u,children:c},"confirm")],maskClosable:!u,closable:!u,centered:!0,children:(0,o.jsx)("p",{className:"my-4",children:i})})},63844:(e,t,r)=>{r.d(t,{A0:()=>l,Hj:()=>i,jB:()=>n,nA:()=>s});var o=r(45512);r(58009);var a=r(44195);let n=({children:e,columns:t,className:r,minWidth:n="800px"})=>(0,o.jsx)("div",{className:(0,a.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",r),children:(0,o.jsx)("div",{className:(0,a.cn)("gap-0","block"),style:{},children:e})}),l=({children:e,className:t,sticky:r})=>(0,o.jsx)("div",{className:(0,a.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",r&&({left:"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[r],t),children:e}),s=({children:e,className:t,sticky:r})=>(0,o.jsx)("div",{className:(0,a.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",r&&({left:"sticky left-0 z-10 bg-white border-r border-gray-200",right:"sticky right-0 z-10 bg-white border-l border-gray-200"})[r],t),children:e}),i=({children:e,className:t,selected:r=!1,onClick:n})=>(0,o.jsx)("div",{className:(0,a.cn)("contents",r&&"bg-blue-50",n&&"cursor-pointer hover:bg-gray-50",t),onClick:n,children:e})},98776:(e,t,r)=>{r.d(t,{A:()=>s});var o=r(45512),a=r(58009),n=r(3117),l=r(97071);let s=({isOpen:e,onClose:t,title:r,children:s,width:i="400px",footer:c,fullWidth:d=!1})=>{let[u,g]=(0,a.useState)(!1),[p,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(1024);if((0,a.useEffect)(()=>{let e=()=>{b(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",r),e)y(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{g(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{g(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{y(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,r]),!p)return null;let h="Point of Sale"===r||d||"100vw"===i;return(0,o.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${h?"sales-panel-container":""}`,children:[(0,o.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${u?"opacity-50":"opacity-0"}`,onClick:t}),(0,o.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${u?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===r||d||"100vw"===i||f<640?"100vw":f<1024?"500px":"string"==typeof i&&i.includes("px")&&parseInt(i)>600?"600px":i},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,o.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:r}),(0,o.jsx)(n.Ay,{type:"text",icon:(0,o.jsx)(l.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,o.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:s}),c&&(0,o.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},60636:(e,t,r)=>{r.d(t,{A:()=>s});var o=r(92273),a=r(25510),n=r(97245),l=r(42211);let s=()=>{let e=(0,o.wA)(),{user:t,accessToken:r}=(0,o.d4)(e=>e.auth),s=(0,a._)(),{refetch:i}=(0,n.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!r,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let o=await i();console.log("useAuth - Refetch result:",o);let a=o.data;if(a?.success&&a?.data){console.log("useAuth - API response data:",a.data);let o=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let n=a.data.phone||t.phone||"",s=a.data.createdAt||t.createdAt||"",i=a.data.lastPaymentDate||t.lastPaymentDate||void 0,c=a.data.nextPaymentDue||t.nextPaymentDue||null,d=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:n,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:s,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...a.data,phone:n,createdAt:s,lastPaymentDate:i,nextPaymentDue:c,createdBy:d,paymentStatus:o};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,l.gV)({user:u,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",a?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&s.needsPayment,paymentStatus:s,refreshUser:d}}},73542:(e,t,r)=>{r.d(t,{E:()=>a});var o=r(58009);let a=()=>{let[e,t]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}}};