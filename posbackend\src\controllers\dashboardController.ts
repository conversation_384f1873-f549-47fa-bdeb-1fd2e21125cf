import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";
import { getSuperAdminStats, getAdminStats, getCashierStats } from "../services/dashboardService";

export const handleDashboardRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode } = req.body;
  const requester = req.user as DecodedToken;

  const validModes = ["stats"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      case "stats": {
        let stats;
        
        // Get stats based on user role
        if (requester.role === "superadmin") {
          stats = await getSuperAdminStats(requester);
        } else if (requester.role === "admin") {
          stats = await getAdminStats(requester);
        } else if (requester.role === "cashier") {
          stats = await getCashierStats(requester);
        } else {
          return sendResponse(res, 403, false, "Unauthorized: Invalid role");
        }
        
        return sendResponse(res, 200, true, "Dashboard statistics retrieved successfully", stats);
      }
      
      default:
        return sendResponse(res, 400, false, "Invalid mode");
    }
  } catch (error: any) {
    console.error("Error in dashboard controller:", error);
    return sendResponse(res, 500, false, error.message || "Error retrieving dashboard statistics");
  }
};
