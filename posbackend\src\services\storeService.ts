import { and, eq, count, desc, inArray, or } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import { stores, userStores, users } from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";

/**
 * Create a new store
 * @param requester The user making the request
 * @param storeData The store data
 * @returns The created store
 */
export const createStore = async (
  requester: JwtPayload,
  storeData: {
    name: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    phone?: string;
    email?: string;
    logo?: string;
    website?: string;
    taxId?: string;
  }
) => {
  // Only admins and superadmins can create stores
  if (requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: Only admins and superadmins can create stores.");
  }

  // Start a transaction to ensure atomicity
  return await postgresDb.transaction(async (trx) => {
    // Insert the store
    const [newStore] = await trx
      .insert(stores)
      .values({
        ...storeData,
        createdBy: requester.id,
      })
      .returning();

    if (!newStore) {
      throw new Error("Store creation failed.");
    }

    // Associate the store with the creator
    await trx
      .insert(userStores)
      .values({
        userId: requester.id,
        storeId: newStore.id,
        isDefault: true, // Make this the default store for the creator
        createdBy: requester.id,
      });

    return newStore;
  });
};

/**
 * Get all stores (paginated)
 * @param requester The user making the request
 * @param page The page number
 * @param limit The number of items per page
 * @returns Paginated list of stores
 */
export const getAllStores = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  await authorizeAction(requester, "getAll", "stores");

  const offset = (page - 1) * limit;
  const isSuperadmin = requester.role === "superadmin";

  // Different filter logic based on role
  let storesFilter;
  if (isSuperadmin) {
    // Superadmins can see all stores
    storesFilter = undefined;
  } else {
    // Get all stores associated with the user
    const userStoreIds = await postgresDb
      .select({ storeId: userStores.storeId })
      .from(userStores)
      .where(eq(userStores.userId, requester.id));

    const storeIds = userStoreIds.map((s: { storeId: number }) => s.storeId);

    if (storeIds.length > 0) {
      // User can see stores they're associated with
      storesFilter = inArray(stores.id, storeIds);
    } else {
      // If no stores found, return empty result
      return {
        total: 0,
        page,
        perPage: limit,
        stores: [],
      };
    }
  }

  // Get stores with creator information
  const [storesData, totalStores] = await Promise.all([
    postgresDb
      .select({
        id: stores.id,
        name: stores.name,
        address: stores.address,
        city: stores.city,
        state: stores.state,
        country: stores.country,
        phone: stores.phone,
        email: stores.email,
        logo: stores.logo,
        website: stores.website,
        taxId: stores.taxId,
        createdBy: stores.createdBy,
        createdByName: users.name,
        createdAt: stores.createdAt,
        updatedAt: stores.updatedAt,
      })
      .from(stores)
      .leftJoin(users, eq(stores.createdBy, users.id))
      .where(storesFilter)
      .orderBy(desc(stores.createdAt))
      .limit(limit)
      .offset(offset),
    postgresDb
      .select({ count: count() })
      .from(stores)
      .where(storesFilter),
  ]);

  return {
    total: totalStores[0]?.count ?? 0,
    page,
    perPage: limit,
    stores: storesData,
  };
};

/**
 * Get store by ID
 * @param requester The user making the request
 * @param storeId The store ID
 * @returns The store
 */
export const getStoreById = async (
  requester: JwtPayload,
  storeId: number
) => {
  await authorizeAction(requester, "getById", "stores", storeId);

  const isSuperadmin = requester.role === "superadmin";

  // Different filter logic based on role
  let storeFilter;
  if (isSuperadmin) {
    // Superadmins can see any store
    storeFilter = eq(stores.id, storeId);
  } else {
    // Check if user is associated with the store
    const userStoreAssociation = await postgresDb
      .select()
      .from(userStores)
      .where(
        and(
          eq(userStores.userId, requester.id),
          eq(userStores.storeId, storeId)
        )
      )
      .limit(1);

    if (userStoreAssociation.length === 0) {
      throw new Error("Store not found or unauthorized.");
    }

    storeFilter = eq(stores.id, storeId);
  }

  // Get store with creator information
  const store = await postgresDb
    .select({
      id: stores.id,
      name: stores.name,
      address: stores.address,
      city: stores.city,
      state: stores.state,
      country: stores.country,
      phone: stores.phone,
      email: stores.email,
      logo: stores.logo,
      website: stores.website,
      taxId: stores.taxId,
      createdBy: stores.createdBy,
      createdByName: users.name,
      createdAt: stores.createdAt,
      updatedAt: stores.updatedAt,
    })
    .from(stores)
    .leftJoin(users, eq(stores.createdBy, users.id))
    .where(storeFilter)
    .limit(1);

  if (store.length === 0) {
    throw new Error("Store not found or unauthorized.");
  }

  return store[0];
};

/**
 * Update store
 * @param requester The user making the request
 * @param storeId The store ID
 * @param updateData The update data
 * @returns The updated store
 */
export const updateStore = async (
  requester: JwtPayload,
  storeId: number,
  updateData: Partial<{
    name: string;
    address: string;
    city: string;
    state: string;
    country: string;
    phone: string;
    email: string;
    logo: string;
    website: string;
    taxId: string;
  }>
) => {
  await authorizeAction(requester, "update", "stores", storeId);

  // Only admins and superadmins can update stores
  if (requester.role !== "admin" && requester.role !== "superadmin") {
    throw new Error("Unauthorized: Only admins and superadmins can update stores.");
  }

  // Check if user is associated with the store (unless superadmin)
  if (requester.role !== "superadmin") {
    const userStoreAssociation = await postgresDb
      .select()
      .from(userStores)
      .where(
        and(
          eq(userStores.userId, requester.id),
          eq(userStores.storeId, storeId)
        )
      )
      .limit(1);

    if (userStoreAssociation.length === 0) {
      throw new Error("Store not found or unauthorized.");
    }
  }

  // Update the store
  const updatedStore = await postgresDb
    .update(stores)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(eq(stores.id, storeId))
    .returning();

  if (!updatedStore || updatedStore.length === 0) {
    throw new Error("Update failed: Store not found.");
  }

  return updatedStore[0];
};

/**
 * Delete stores (Single or Multiple)
 * @param requester The user making the request
 * @param ids The store ID or array of store IDs
 * @returns The deleted store IDs
 */
export const deleteStore = async (
  requester: JwtPayload,
  ids: number | number[]
) => {
  const storeIds = Array.isArray(ids) ? ids : [ids];

  // Check authorization for each store
  for (const storeId of storeIds) {
    await authorizeAction(requester, "delete", "stores", storeId);

    // Only admins and superadmins can delete stores
    if (requester.role !== "admin" && requester.role !== "superadmin") {
      throw new Error("Unauthorized: Only admins and superadmins can delete stores.");
    }

    // Check if user is associated with the store (unless superadmin)
    if (requester.role !== "superadmin") {
      const userStoreAssociation = await postgresDb
        .select()
        .from(userStores)
        .where(
          and(
            eq(userStores.userId, requester.id),
            eq(userStores.storeId, storeId)
          )
        )
        .limit(1);

      if (userStoreAssociation.length === 0) {
        throw new Error(`Store with ID ${storeId} not found or unauthorized.`);
      }
    }
  }

  // Delete the stores
  const deletedStores = await postgresDb
    .delete(stores)
    .where(inArray(stores.id, storeIds))
    .returning({ deletedId: stores.id });

  if (!deletedStores || deletedStores.length === 0) {
    throw new Error("Delete failed: Stores not found.");
  }

  return {
    deletedIds: deletedStores.map(store => store.deletedId)
  };
};
