export type UserRole = 'cashier' | 'admin' | 'superadmin';
export type PaymentStatus = 'pending' | 'paid' | 'overdue' | 'inactive';

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  paymentStatus: PaymentStatus;
  createdAt: string;
  lastPaymentDate?: string | null;
  nextPaymentDue?: string | null;
  createdBy?: number | null;
}

export interface CreateUserDto {
  name: string;
  email: string;
  password: string;
  phone: string;
  role?: UserRole;
  paymentStatus?: PaymentStatus;
}

export interface UpdateUserDto {
  name?: string;
  email?: string;
  phone?: string;
  role?: UserRole;
  paymentStatus?: PaymentStatus;
}

export interface UserResponse {
  user: User;
  accessToken: string;
}

export interface LoginUserDto {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
}

export interface PaginatedUsers {
  total: number;
  page: number;
  perPage: number;
  users: User[];
}

export interface ApiResponse<T> {
    success: boolean;
    message: string;
    data?: T;
  }

