{"id": "0a989e60-111c-4d6f-b47c-72faf0d685ec", "prevId": "9ad1e3db-4894-4d9a-ae41-1178b4b128cb", "version": "7", "dialect": "postgresql", "tables": {"public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_categories_name": {"name": "idx_categories_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "barcode": {"name": "barcode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "stock_quantity": {"name": "stock_quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "min_stock_level": {"name": "min_stock_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_products_barcode": {"name": "idx_products_barcode", "columns": [{"expression": "barcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_products_sku": {"name": "idx_products_sku", "columns": [{"expression": "sku", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_products_category": {"name": "idx_products_category", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_category_id_categories_id_fk": {"name": "products_category_id_categories_id_fk", "tableFrom": "products", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"products_sku_unique": {"name": "products_sku_unique", "nullsNotDistinct": false, "columns": ["sku"]}, "products_barcode_unique": {"name": "products_barcode_unique", "nullsNotDistinct": false, "columns": ["barcode"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchases": {"name": "purchases", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "integer", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "cost_price": {"name": "cost_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_cost": {"name": "total_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "purchase_date": {"name": "purchase_date", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "purchased_by": {"name": "purchased_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"idx_purchases_supplier": {"name": "idx_purchases_supplier", "columns": [{"expression": "supplier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_purchases_product": {"name": "idx_purchases_product", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_purchases_date": {"name": "idx_purchases_date", "columns": [{"expression": "purchase_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"purchases_supplier_id_suppliers_id_fk": {"name": "purchases_supplier_id_suppliers_id_fk", "tableFrom": "purchases", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "purchases_product_id_products_id_fk": {"name": "purchases_product_id_products_id_fk", "tableFrom": "purchases", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "purchases_purchased_by_users_id_fk": {"name": "purchases_purchased_by_users_id_fk", "tableFrom": "purchases", "tableTo": "users", "columnsFrom": ["purchased_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.receipts": {"name": "receipts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "sale_id": {"name": "sale_id", "type": "integer", "primaryKey": false, "notNull": true}, "receipt_url": {"name": "receipt_url", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_receipts_sale": {"name": "idx_receipts_sale", "columns": [{"expression": "sale_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"receipts_sale_id_sales_id_fk": {"name": "receipts_sale_id_sales_id_fk", "tableFrom": "receipts", "tableTo": "sales", "columnsFrom": ["sale_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales": {"name": "sales", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "cashier_id": {"name": "cashier_id", "type": "integer", "primaryKey": false, "notNull": false}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "transaction_date": {"name": "transaction_date", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"idx_sales_transaction_date": {"name": "idx_sales_transaction_date", "columns": [{"expression": "transaction_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sales_cashier": {"name": "idx_sales_cashier", "columns": [{"expression": "cashier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sales_cashier_id_users_id_fk": {"name": "sales_cashier_id_users_id_fk", "tableFrom": "sales", "tableTo": "users", "columnsFrom": ["cashier_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales_items": {"name": "sales_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "sale_id": {"name": "sale_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_sales_items_sale": {"name": "idx_sales_items_sale", "columns": [{"expression": "sale_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sales_items_product": {"name": "idx_sales_items_product", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sales_items_sale_id_sales_id_fk": {"name": "sales_items_sale_id_sales_id_fk", "tableFrom": "sales_items", "tableTo": "sales", "columnsFrom": ["sale_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "sales_items_product_id_products_id_fk": {"name": "sales_items_product_id_products_id_fk", "tableFrom": "sales_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_adjustments": {"name": "stock_adjustments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity_change": {"name": "quantity_change", "type": "integer", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true}, "adjusted_by": {"name": "adjusted_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_stock_adjustments_product": {"name": "idx_stock_adjustments_product", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stock_adjustments_product_id_products_id_fk": {"name": "stock_adjustments_product_id_products_id_fk", "tableFrom": "stock_adjustments", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stock_adjustments_adjusted_by_users_id_fk": {"name": "stock_adjustments_adjusted_by_users_id_fk", "tableFrom": "stock_adjustments", "tableTo": "users", "columnsFrom": ["adjusted_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suppliers": {"name": "suppliers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_suppliers_name": {"name": "idx_suppliers_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_suppliers_phone": {"name": "idx_suppliers_phone", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_suppliers_email": {"name": "idx_suppliers_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"suppliers_phone_unique": {"name": "suppliers_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}, "suppliers_email_unique": {"name": "suppliers_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "payment_status": {"name": "payment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_users_email": {"name": "idx_users_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_phone": {"name": "idx_users_phone", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_role_created_at": {"name": "idx_users_role_created_at", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_created_by_created_at": {"name": "idx_users_created_by_created_at", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_users_payment_status": {"name": "idx_users_payment_status", "columns": [{"expression": "payment_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_payments_user": {"name": "idx_payments_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payments_provider": {"name": "idx_payments_provider", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payments_status": {"name": "idx_payments_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payments_user_id_users_id_fk": {"name": "payments_user_id_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payments_transaction_id_unique": {"name": "payments_transaction_id_unique", "nullsNotDistinct": false, "columns": ["transaction_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}