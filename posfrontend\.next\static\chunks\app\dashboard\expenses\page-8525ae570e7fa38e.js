(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5343],{48292:(e,t,s)=>{Promise.resolve().then(s.bind(s,65127))},65127:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>en});var a=s(95155),n=s(12115),r=s(51554),l=s(21614),i=s(80766),d=s(71349),o=s(43316),c=s(41657),x=s(72093),m=s(72278),u=s(96030),h=s(53452),p=s(97139),g=s(5413),y=s(37673),f=s(16419),j=s(40794),v=s(36060),b=s(30555),A=s(92353),N=s(82461),w=s(45100),C=s(68773),k=s(92895),D=s(6457),P=s(17084),S=s(87181),E=s(80519),M=s(86260),R=s(27656),F=s(60102),Y=s(91256),I=s(21455),z=s.n(I),L=s(83391),T=s(10142),_=s(42531);let q=e=>{let{expenses:t,loading:s=!1,onEdit:n,onDelete:r,onBulkDelete:l,onView:i,selectedExpenses:d=[],onSelectionChange:c,isMobile:x=!1}=e,u=(0,Y.E)(),g=x||u,y=(0,L.d4)(e=>e.auth.user),f=null==y?void 0:y.role,v="admin"===f||"superadmin"===f,b="admin"===f||"superadmin"===f,A=(e,t)=>{c&&c(t?[...d,e]:d.filter(t=>t!==e))},N=e=>{c&&(e.target.checked?c(t.map(e=>e.id)):c([]))},I=t.length>0&&d.length===t.length,T=d.length>0&&d.length<t.length,_=e=>{let t={cash:{label:"Cash",color:"green"},card:{label:"Card",color:"blue"},mobile_money:{label:"Mobile Money",color:"purple"},bank_transfer:{label:"Bank Transfer",color:"orange"},cheque:{label:"Cheque",color:"cyan"}}[e]||{label:e,color:"default"};return(0,a.jsx)(w.A,{color:t.color,children:t.label})},q=e=>"₵".concat(parseFloat(e).toFixed(2));return C.A,o.Ay,h.A,o.Ay,p.A,(0,a.jsxs)("div",{children:[d.length>0&&b&&l&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-red-700",children:[d.length," expense(s) selected"]}),(0,a.jsx)(o.Ay,{type:"primary",danger:!0,icon:(0,a.jsx)(P.A,{}),onClick:()=>l(d),size:g?"small":"middle",children:"Delete Selected"})]})}),g?(0,a.jsxs)(F.jB,{columns:c?"50px 200px 120px 120px 120px 150px":"200px 120px 120px 120px 150px",minWidth:c?"800px":"750px",children:[c&&(0,a.jsx)(F.A0,{className:"text-center",children:(0,a.jsx)(k.A,{indeterminate:T,checked:I,onChange:N})}),(0,a.jsx)(F.A0,{children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-1"}),"Expense"]})}),(0,a.jsx)(F.A0,{children:"Amount"}),(0,a.jsx)(F.A0,{children:"Category"}),(0,a.jsx)(F.A0,{children:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(S.A,{className:"mr-1"}),"Date"]})}),(0,a.jsx)(F.A0,{className:"text-right",children:"Actions"}),t.map(e=>(0,a.jsxs)(F.Hj,{selected:d.includes(e.id),children:[c&&(0,a.jsx)(F.nA,{className:"text-center",children:(0,a.jsx)(k.A,{checked:d.includes(e.id),onChange:t=>A(e.id,t.target.checked)})}),(0,a.jsx)(F.nA,{children:(0,a.jsxs)("div",{className:"max-w-[180px] overflow-hidden text-ellipsis",children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),e.isRecurring&&(0,a.jsxs)(w.A,{color:"blue",className:"mt-1 text-xs",children:[(0,a.jsx)(m.A,{className:"mr-1"}),e.recurringFrequency]})]})}),(0,a.jsx)(F.nA,{children:(0,a.jsx)("span",{className:"font-semibold text-green-600",children:q(e.amount)})}),(0,a.jsx)(F.nA,{children:e.category?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-1",style:{backgroundColor:e.category.color}}),(0,a.jsx)("span",{className:"text-sm truncate max-w-[80px]",children:e.category.name})]}):(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"No category"})}),(0,a.jsx)(F.nA,{children:(0,a.jsx)("span",{className:"text-sm",children:z()(e.expenseDate).format("MMM DD, YYYY")})}),(0,a.jsx)(F.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-1",children:[i&&(0,a.jsx)(D.A,{title:"View Details",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(E.A,{}),onClick:()=>i(e),className:"text-green-500 hover:text-green-400"})}),v&&n&&(0,a.jsx)(D.A,{title:"Edit",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(M.A,{}),onClick:()=>n(e),className:"text-blue-500 hover:text-blue-400"})}),b&&r&&(0,a.jsx)(D.A,{title:"Delete",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",danger:!0,icon:(0,a.jsx)(R.A,{}),onClick:()=>r(e.id),className:"text-red-500 hover:text-red-400"})})]})})]},e.id))]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[c&&(0,a.jsx)("th",{className:"px-4 py-3 text-left",children:(0,a.jsx)(k.A,{indeterminate:T,checked:I,onChange:N})}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Expense"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vendor"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[c&&(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,a.jsx)(k.A,{checked:d.includes(e.id),onChange:t=>A(e.id,t.target.checked)})}),(0,a.jsx)("td",{className:"px-4 py-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),e.description&&(0,a.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description}),e.isRecurring&&(0,a.jsxs)(w.A,{color:"blue",className:"mt-1 text-xs",children:[(0,a.jsx)(m.A,{className:"mr-1"}),e.recurringFrequency]})]})}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"text-lg font-semibold text-green-600",children:q(e.amount)})}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:e.category?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.category.color}}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.category.name})]}):(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"No category"})}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:z()(e.expenseDate).format("MMM DD, YYYY")}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:_(e.paymentMethod)}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:e.vendor||"-"}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[i&&(0,a.jsx)(D.A,{title:"View Details",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(E.A,{}),onClick:()=>i(e),className:"text-green-500"})}),v&&n&&(0,a.jsx)(D.A,{title:"Edit",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(M.A,{}),onClick:()=>n(e),className:"text-blue-500"})}),b&&r&&(0,a.jsx)(D.A,{title:"Delete",children:(0,a.jsx)(o.Ay,{type:"text",size:"small",danger:!0,icon:(0,a.jsx)(R.A,{}),onClick:()=>r(e.id),className:"text-red-500"})})]})})]},e.id))})]})})]})};var O=s(36197),B=s(83414),U=s(18198),W=s(42426),V=s(24988),H=s(51814),G=s(50147),J=s(89895),Q=s(81910),$=s(55750),K=s(75912);let{Option:X}=l.A,{TextArea:Z}=c.A,ee=e=>{var t;let{isOpen:s,onClose:i,onSuccess:d,expense:x}=e,[m]=B.A.useForm(),u=!!x,[h,{isLoading:p}]=(0,A.Nx)(),[g,{isLoading:y}]=(0,A.cp)(),{data:v,isLoading:b}=(0,N.L)({page:1,limit:100}),w=(null==v?void 0:null===(t=v.data)||void 0===t?void 0:t.categories)||[],C=p||y,k=[{value:"daily",label:"Daily"},{value:"weekly",label:"Weekly"},{value:"monthly",label:"Monthly"},{value:"quarterly",label:"Quarterly"},{value:"yearly",label:"Yearly"}];(0,n.useEffect)(()=>{s&&(u&&x?m.setFieldsValue({title:x.title,description:x.description,amount:parseFloat(x.amount),categoryId:x.categoryId,expenseDate:x.expenseDate?z()(x.expenseDate):z()(),paymentMethod:x.paymentMethod,vendor:x.vendor,isRecurring:x.isRecurring,recurringFrequency:x.recurringFrequency,tags:x.tags}):(m.resetFields(),m.setFieldsValue({expenseDate:z()(),paymentMethod:"cash",isRecurring:!1})))},[s,u,x,m]);let D=async e=>{var t,s;try{let s;let a={title:e.title,description:e.description,amount:e.amount,expenseDate:null===(t=e.expenseDate)||void 0===t?void 0:t.toISOString(),paymentMethod:e.paymentMethod,vendor:e.vendor,isRecurring:e.isRecurring||!1,recurringFrequency:e.isRecurring?e.recurringFrequency:void 0,tags:e.tags};e.categoryId&&(a.categoryId=e.categoryId),(s=u&&x?await g({expenseId:x.id,expenseData:a}).unwrap():await h(a).unwrap()).success?(K.r.success(u?"Expense updated successfully!":"Expense created successfully!"),m.resetFields(),null==d||d(),i()):K.r.error(s.message||"Failed to save expense")}catch(e){console.error("Error saving expense:",e),K.r.error((null==e?void 0:null===(s=e.data)||void 0===s?void 0:s.message)||(null==e?void 0:e.message)||"Failed to ".concat(u?"update":"create"," expense"))}},P=()=>{m.resetFields(),i()},E=u?"Edit Expense":"Add New Expense",M=(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(o.Ay,{onClick:P,disabled:C,className:"text-gray-700 hover:text-gray-900",style:{borderColor:"#d9d9d9",background:"#f5f5f5"},children:"Cancel"}),(0,a.jsxs)(o.Ay,{type:"primary",onClick:()=>m.submit(),loading:C,icon:C?(0,a.jsx)(f.A,{}):(0,a.jsx)(H.A,{}),children:[u?"Update":"Create"," Expense"]})]});return(0,a.jsxs)(V.A,{isOpen:s,onClose:P,title:E,width:"500px",footer:M,children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 flex items-center",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Editing Expense: ",null==x?void 0:x.title]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"mr-2"}),"Add New Expense"]})}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:u?"Update expense information":"Record a new business expense"})]}),(0,a.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"text-red-500 mr-1",children:"*"})," indicates required fields"]}),(0,a.jsxs)(B.A,{form:m,layout:"vertical",onFinish:D,className:"expense-form",requiredMark:!0,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Basic Information"}),(0,a.jsx)(B.A.Item,{name:"title",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(G.A,{className:"mr-1"})," Expense Title ",(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter expense title"}],tooltip:"Brief description of the expense",children:(0,a.jsx)(c.A,{placeholder:"e.g., Office Rent, Utilities, Equipment Purchase",maxLength:255})}),(0,a.jsx)(B.A.Item,{name:"description",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(G.A,{className:"mr-1"})," Description"]}),tooltip:"Additional details about the expense",children:(0,a.jsx)(Z,{placeholder:"Additional details about this expense...",rows:3,maxLength:500})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Financial Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(B.A.Item,{name:"amount",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"mr-1"})," Amount (₵) ",(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please enter amount"},{type:"number",min:.01,message:"Amount must be greater than 0"}],tooltip:"The total amount spent",children:(0,a.jsx)(U.A,{min:0,step:.01,precision:2,style:{width:"100%"},placeholder:"0.00",prefix:"₵"})}),(0,a.jsx)(B.A.Item,{name:"categoryId",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(J.A,{className:"mr-1"})," Category"]}),tooltip:"Select expense category for better organization",children:(0,a.jsx)(l.A,{placeholder:"Select category",loading:b,allowClear:!0,showSearch:!0,optionFilterProp:"children",children:w.map(e=>(0,a.jsx)(X,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),e.name]})},e.id))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(B.A.Item,{name:"expenseDate",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(S.A,{className:"mr-1"})," Expense Date ",(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please select expense date"}],tooltip:"When this expense occurred",children:(0,a.jsx)(r.A,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"Select date"})}),(0,a.jsx)(B.A.Item,{name:"paymentMethod",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(Q.A,{className:"mr-1"})," Payment Method ",(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),rules:[{required:!0,message:"Please select payment method"}],tooltip:"How this expense was paid",children:(0,a.jsx)(l.A,{placeholder:"Select payment method",children:[{value:"cash",label:"Cash"},{value:"card",label:"Card"},{value:"mobile_money",label:"Mobile Money"},{value:"bank_transfer",label:"Bank Transfer"},{value:"cheque",label:"Cheque"}].map(e=>(0,a.jsx)(X,{value:e.value,children:e.label},e.value))})})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2",children:"Additional Information"}),(0,a.jsx)(B.A.Item,{name:"vendor",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)($.A,{className:"mr-1"})," Vendor/Supplier"]}),tooltip:"Who was paid for this expense",children:(0,a.jsx)(c.A,{placeholder:"e.g., ABC Company, John Doe, Utility Company",maxLength:255})}),(0,a.jsx)(B.A.Item,{name:"tags",label:(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(J.A,{className:"mr-1"})," Tags"]}),tooltip:"Comma-separated tags for better categorization",children:(0,a.jsx)(c.A,{placeholder:"e.g., urgent, monthly, equipment",maxLength:200})}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)(B.A.Item,{name:"isRecurring",valuePropName:"checked",label:(0,a.jsx)("span",{className:"flex items-center",children:"Recurring Expense"}),tooltip:"Check if this is a recurring expense",children:(0,a.jsx)(W.A,{})}),(0,a.jsx)(B.A.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.isRecurring!==t.isRecurring,children:e=>{let{getFieldValue:t}=e;return t("isRecurring")?(0,a.jsx)(B.A.Item,{name:"recurringFrequency",label:"Frequency",rules:[{required:!0,message:"Please select frequency"}],children:(0,a.jsx)(l.A,{placeholder:"Select frequency",children:k.map(e=>(0,a.jsx)(X,{value:e.value,children:e.label},e.value))})}):null}})]})]})]})]})};var et=s(12467);let{RangePicker:es}=r.A,{Option:ea}=l.A,en=()=>{var e,t,s,r,w;let{user:C}=(0,v.A)(),k=(0,b.a)(),D=null==C?void 0:C.role,[P,S]=(0,n.useState)(1),[E]=(0,n.useState)(10),[M,R]=(0,n.useState)(""),[F,Y]=(0,n.useState)(),[I,L]=(0,n.useState)(null),[B,U]=(0,n.useState)([]),[W,V]=(0,n.useState)(!1),[H,G]=(0,n.useState)(null),[J,Q]=(0,n.useState)(!1),[$,X]=(0,n.useState)(!1),[Z,en]=(0,n.useState)(null),[er,el]=(0,n.useState)([]),ei="admin"===D||"superadmin"===D,ed="admin"===D||"superadmin"===D,eo={page:P,limit:E,search:M.trim(),categoryId:F,startDate:null==I?void 0:null===(e=I[0])||void 0===e?void 0:e.format("YYYY-MM-DD"),endDate:null==I?void 0:null===(t=I[1])||void 0===t?void 0:t.endOf("day").toISOString()},{data:ec,isLoading:ex,error:em,refetch:eu}=(0,A.cY)(eo),{data:eh}=(0,N.L)({page:1,limit:100}),[ep,{isLoading:eg}]=(0,A.GH)(),ey=(null==ec?void 0:null===(s=ec.data)||void 0===s?void 0:s.expenses)||[],ef=(null==ec?void 0:null===(r=ec.data)||void 0===r?void 0:r.total)||0,ej=(null==eh?void 0:null===(w=eh.data)||void 0===w?void 0:w.categories)||[],ev=e=>{R(e),S(1)},eb=async()=>{if(Z)try{let e=await ep(Z).unwrap();e.success?(K.r.success("Expense deleted successfully!"),U(e=>e.filter(e=>e!==Z)),Q(!1),en(null)):K.r.error(e.message||"Failed to delete expense")}catch(t){var e;console.error("Error deleting expense:",t),K.r.error((null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Failed to delete expense")}},eA=async()=>{try{let e=er.map(e=>ep(e).unwrap());await Promise.all(e),K.r.success("".concat(er.length," expense(s) deleted successfully!")),U([]),X(!1),el([])}catch(e){console.error("Error deleting expenses:",e),K.r.error("Failed to delete some expenses")}};return em?(0,a.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,a.jsx)(d.A,{className:"w-full",children:(0,a.jsxs)("div",{className:"text-center text-red-500 p-8",children:[(0,a.jsx)("p",{children:"Error loading expenses. Please try again."}),(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(m.A,{}),onClick:()=>eu(),className:"mt-4",children:"Retry"})]})})}):(0,a.jsxs)("div",{className:"w-full p-2 sm:p-4",children:[(0,a.jsxs)(d.A,{title:(0,a.jsx)("span",{className:"text-gray-800",children:"Expense Management"}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:k?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,a.jsxs)("div",{className:k?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(u.A,{}),onClick:()=>{if(!ei){K.r.error("You don't have permission to add expenses");return}G(null),V(!0)},size:k?"small":"middle",className:"bg-blue-600 hover:bg-blue-700",children:k?"":"Add Expense"}),(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(h.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){i.Ay.error({message:"Please allow popups to print expenses"});return}let t='\n      <html>\n        <head>\n          <title>Expenses Report</title>\n          <style>\n            body { font-family: Arial, sans-serif; }\n            table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n            th { background-color: #f5f5f5; }\n            .header { text-align: center; margin-bottom: 20px; }\n            .date { color: #666; font-size: 0.9em; }\n            @media print {\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h2>Expenses Report</h2>\n            <p class="date">Generated on: '.concat(z()().format("MMMM D, YYYY h:mm A"),"</p>\n          </div>\n          <table>\n            <thead>\n              <tr>\n                <th>Title</th>\n                <th>Amount</th>\n                <th>Category</th>\n                <th>Date</th>\n                <th>Payment Method</th>\n                <th>Vendor</th>\n              </tr>\n            </thead>\n            <tbody>\n              ").concat(ey.map(e=>{var t;return"\n                <tr>\n                  <td>".concat(e.title,"</td>\n                  <td>₵").concat(parseFloat(e.amount).toFixed(2),"</td>\n                  <td>").concat((null===(t=e.category)||void 0===t?void 0:t.name)||"No category","</td>\n                  <td>").concat(z()(e.expenseDate).format("MMM DD, YYYY"),"</td>\n                  <td>").concat(e.paymentMethod,"</td>\n                  <td>").concat(e.vendor||"-","</td>\n                </tr>\n              ")}).join(""),'\n            </tbody>\n          </table>\n          <div class="no-print" style="margin-top: 20px; text-align: center;">\n            <button onclick="window.print()">Print Report</button>\n          </div>\n        </body>\n      </html>\n    ');e.document.write(t),e.document.close()},size:k?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:k?"":"Print"}),(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(p.A,{}),onClick:()=>{let e=new T.uE;e.setFontSize(16),e.text("Expenses Report",14,15),e.setFontSize(10),e.text("Generated on: ".concat(z()().format("MMMM D, YYYY h:mm A")),14,22);let t=ey.map(e=>{var t;return[e.title,"₵".concat(parseFloat(e.amount).toFixed(2)),(null===(t=e.category)||void 0===t?void 0:t.name)||"No category",z()(e.expenseDate).format("MMM DD, YYYY"),e.paymentMethod,e.vendor||"-"]});(0,_.Ay)(e,{head:[["Title","Amount","Category","Date","Payment Method","Vendor"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("expenses-report.pdf")},size:k?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:k?"":"Export"})]}),children:[(0,a.jsx)("div",{className:"mb-4 space-y-3",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)(c.A,{placeholder:"Search expenses...",prefix:(0,a.jsx)(g.A,{}),value:M,onChange:e=>ev(e.target.value),className:"flex-1",allowClear:!0}),(0,a.jsx)(l.A,{placeholder:"Filter by category",value:F,onChange:e=>{Y(e),S(1)},className:"w-full sm:w-48",allowClear:!0,children:ej.map(e=>(0,a.jsx)(ea,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),e.name]})},e.id))}),(0,a.jsx)(es,{value:I,onChange:e=>{e&&e[0]&&e[1]?L([e[0],e[1].endOf("day")]):L(null),S(1)},className:"w-full sm:w-64",format:"YYYY-MM-DD"}),(M||F||I)&&(0,a.jsx)(o.Ay,{onClick:()=>{R(""),Y(void 0),L(null),S(1)},icon:(0,a.jsx)(y.A,{}),children:"Clear"})]})}),(0,a.jsx)("div",{className:"min-h-96",children:ex?(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(x.A,{indicator:(0,a.jsx)(f.A,{style:{fontSize:48},spin:!0})})}):(0,a.jsxs)(a.Fragment,{children:[ey.length>0?(0,a.jsx)(q,{expenses:ey,loading:ex,onEdit:e=>{G(e),V(!0)},onDelete:e=>{if(!ed){K.r.error("You don't have permission to delete expenses");return}en(e),Q(!0)},onBulkDelete:ed?e=>{if(!ed){K.r.error("You don't have permission to delete expenses");return}el(e),X(!0)}:void 0,selectedExpenses:B,onSelectionChange:U,isMobile:k}):(0,a.jsxs)("div",{className:"text-center text-gray-500 py-12",children:[(0,a.jsx)(j.A,{className:"text-4xl mb-4"}),M||F||I?(0,a.jsx)("p",{children:"No expenses found matching your filters."}):(0,a.jsxs)("p",{children:["No expenses found. ",ei&&"Click 'Add Expense' to create one."]})]}),ey.length>0&&(0,a.jsx)(O.A,{current:P,pageSize:E,total:ef,onChange:e=>{S(e)},isMobile:k})]})})]}),(0,a.jsx)(ee,{isOpen:W,onClose:()=>{V(!1),G(null)},onSuccess:()=>{eu(),U([])},expense:H}),(0,a.jsx)(et.A,{isOpen:J,onClose:()=>{Q(!1),en(null)},onConfirm:eb,title:"Delete Expense",message:"Are you sure you want to delete this expense? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",isLoading:eg,type:"danger"}),(0,a.jsx)(et.A,{isOpen:$,onClose:()=>{X(!1),el([])},onConfirm:eA,title:"Delete Multiple Expenses",message:"Are you sure you want to delete ".concat(er.length," expenses? This action cannot be undone."),confirmText:"Delete All",cancelText:"Cancel",isLoading:eg,type:"danger"})]})}},36197:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(95155);s(12115);var n=s(33621),r=s(44549);let l=e=>{let{current:t,pageSize:s,total:l,onChange:i,isMobile:d=!1}=e,o=Math.ceil(l/s);return 0===l?null:(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(t-1)*s+1})," to"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(t*s,l)})," of"," ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:l})," results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>i(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)(n.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,o)},(e,s)=>{let n=s+1;return(0,a.jsx)("button",{onClick:()=>i(n),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t===n?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:n},n)}),(0,a.jsxs)("button",{onClick:()=>i(t+1),disabled:t>=o,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(t>=o?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)(r.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>i(Math.max(1,t-1)),disabled:1===t,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Previous"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",t," of ",o]}),(0,a.jsx)("button",{onClick:()=>i(t+1),disabled:t>=o,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(t>=o?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"),children:"Next"})]})]})}},12467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155);s(12115);var n=s(46102),r=s(43316),l=s(75218);let i=e=>{let{isOpen:t,onClose:s,onConfirm:i,title:d,message:o,confirmText:c="Confirm",cancelText:x="Cancel",isLoading:m=!1,type:u="danger"}=e;return(0,a.jsx)(n.A,{title:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{style:{color:"danger"===u?"#ff4d4f":"warning"===u?"#faad14":"#1890ff",marginRight:8}}),(0,a.jsx)("span",{children:d})]}),open:t,onCancel:s,footer:[(0,a.jsx)(r.Ay,{onClick:s,disabled:m,children:x},"cancel"),(0,a.jsx)(r.Ay,{type:"danger"===u?"primary":"default",danger:"danger"===u,onClick:i,loading:m,children:c},"confirm")],maskClosable:!m,closable:!m,centered:!0,children:(0,a.jsx)("p",{className:"my-4",children:o})})}},60102:(e,t,s)=>{"use strict";s.d(t,{A0:()=>l,Hj:()=>d,jB:()=>r,nA:()=>i});var a=s(95155);s(12115);var n=s(21567);let r=e=>{let{children:t,columns:s,className:r,minWidth:l="800px"}=e,i=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,n.cn)("w-full overflow-x-auto overflow-y-visible","border border-gray-200 rounded-lg shadow-sm","bg-white","scroll-smooth",r),children:(0,a.jsx)("div",{className:(0,n.cn)("gap-0",i?"grid":"block"),style:i?{gridTemplateColumns:s,minWidth:l,width:"max-content"}:{},children:t})})},l=e=>{let{children:t,className:s,sticky:r}=e,l=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,n.cn)("bg-gray-50 border-b border-gray-200","font-medium text-xs text-gray-700 uppercase tracking-wider","px-3 py-3 text-left","sticky top-0 z-10",r&&({left:l?"":"sticky left-0 z-20 bg-gray-50 border-r border-gray-200",right:l?"":"sticky right-0 z-20 bg-gray-50 border-l border-gray-200"})[r],s),children:t})},i=e=>{let{children:t,className:s,sticky:r}=e,l=window.innerWidth<768;return(0,a.jsx)("div",{className:(0,n.cn)("px-3 py-4 text-sm text-gray-900","border-b border-gray-200","whitespace-nowrap",r&&({left:l?"":"sticky left-0 z-10 bg-white border-r border-gray-200",right:l?"":"sticky right-0 z-10 bg-white border-l border-gray-200"})[r],s),children:t})},d=e=>{let{children:t,className:s,selected:r=!1,onClick:l}=e;return(0,a.jsx)("div",{className:(0,n.cn)("contents",r&&"bg-blue-50",l&&"cursor-pointer hover:bg-gray-50",s),onClick:l,children:t})}},24988:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),n=s(12115),r=s(43316),l=s(79624);let i=e=>{let{isOpen:t,onClose:s,title:i,children:d,width:o="400px",footer:c,fullWidth:x=!1}=e,[m,u]=(0,n.useState)(!1),[h,p]=(0,n.useState)(!1),[g,y]=(0,n.useState)(window.innerWidth);if((0,n.useEffect)(()=>{let e=()=>{y(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,n.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",t,"title:",i),t)p(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{u(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{u(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{p(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[t,i]),!h)return null;let f="Point of Sale"===i||x||"100vw"===o;return(0,a.jsxs)("div",{className:"fixed inset-0 z-[1000] overflow-hidden ".concat(f?"sales-panel-container":""),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black transition-opacity duration-300 ".concat(m?"opacity-50":"opacity-0"),onClick:s}),(0,a.jsxs)("div",{className:"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ".concat(m?"translate-x-0":"translate-x-full"),style:{width:"Point of Sale"===i||x||"100vw"===o||g<640?"100vw":g<1024?"500px":"string"==typeof o&&o.includes("px")&&parseInt(o)>600?"600px":o},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:i}),(0,a.jsx)(r.Ay,{type:"text",icon:(0,a.jsx)(l.A,{style:{color:"#333"}}),onClick:s,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:d}),c&&(0,a.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},30555:(e,t,s)=>{"use strict";s.d(t,{a:()=>n});var a=s(12115);function n(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{let e=window.matchMedia("(max-width: ".concat(849,"px)")),s=()=>{t(window.innerWidth<850)};return t(window.innerWidth<850),e.addEventListener("change",s),()=>e.removeEventListener("change",s)},[]),!!e}},36060:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(83391),n=s(70854),r=s(63065),l=s(7875);let i=()=>{let e=(0,a.wA)(),{user:t,accessToken:s}=(0,a.d4)(e=>e.auth),i=(0,n._)(),{refetch:d}=(0,r.$f)((null==t?void 0:t.id)||0,{skip:!(null==t?void 0:t.id)});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:null==t?void 0:t.role,phone:null==t?void 0:t.phone,phoneType:(null==t?void 0:t.phone)?typeof t.phone:"undefined/null",createdAt:null==t?void 0:t.createdAt,createdAtType:(null==t?void 0:t.createdAt)?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let o=!!t&&!!s,c=async()=>{if(!(null==t?void 0:t.id)){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await d();console.log("useAuth - Refetch result:",a);let n=a.data;if((null==n?void 0:n.success)&&(null==n?void 0:n.data)){console.log("useAuth - API response data:",n.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let r=n.data.phone||t.phone||"",i=n.data.createdAt||t.createdAt||"",d=n.data.lastPaymentDate||t.lastPaymentDate||void 0,o=n.data.nextPaymentDue||t.nextPaymentDue||null,c=n.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:n.data.phone,userPhone:t.phone,finalPhone:r,apiCreatedAt:n.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:n.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:d,apiNextPaymentDue:n.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:o,apiCreatedBy:n.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let x={...n.data,phone:r,createdAt:i,lastPaymentDate:d,nextPaymentDue:o,createdBy:c,paymentStatus:a};console.log("useAuth - Updating Redux store with:",x),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,l.gV)({user:x,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",(null==n?void 0:n.message)||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:o,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>(null==t?void 0:t.role)==="superadmin",isAdmin:()=>(null==t?void 0:t.role)==="admin",isCashier:()=>(null==t?void 0:t.role)==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:c}}},70854:(e,t,s)=>{"use strict";s.d(t,{_:()=>i});var a=s(12115),n=s(83391),r=s(21455),l=s.n(r);let i=()=>{let e=(0,n.d4)(e=>e.auth.user),[t,s]=(0,a.useState)({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return(0,a.useEffect)(()=>{if(!e){s({isActive:!1,daysRemaining:null,status:"inactive",needsPayment:!0});return}let t=null,a=!1,n=!0,r="inactive";if("superadmin"===e.role){s({isActive:!0,daysRemaining:null,status:"active",needsPayment:!1});return}if("paid"===e.paymentStatus){a=!0,n=!1,r="active";let s=!e.lastPaymentDate;if(e.nextPaymentDue){let r=l()(e.nextPaymentDue),i=l()();if(t=r.diff(i,"day"),s){let s=l()().diff(l()(e.createdAt),"day");console.log("\uD83C\uDF81 useCheckPaymentStatus - FREE TRIAL USER:",{email:e.email,daysSinceCreation:s,daysRemaining:t,trialDaysUsed:s,trialDaysRemaining:t,isActive:a,needsPayment:n})}}}else"pending"===e.paymentStatus?(a=!1,n=!0,r="pending"):"overdue"===e.paymentStatus?(a=!1,n=!0,r="overdue"):(a=!1,n=!0,r="inactive");s({isActive:a,daysRemaining:t,status:r,needsPayment:n})},[e]),t}},91256:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(12115);let n=()=>{let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},21567:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(43463),n=s(69795);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}},75912:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var a=s(55037);let n=(e,t)=>{"success"===e?a.oR.success(t):"error"===e?a.oR.error(t):"warning"===e&&(0,a.oR)(t,{icon:"⚠️",style:{background:"#FEF3C7",color:"#92400E",border:"1px solid #F59E0B"}})};n.success=e=>n("success",e),n.error=e=>n("error",e),n.warning=e=>n("warning",e)}},e=>{var t=t=>e(e.s=t);e.O(0,[3930,6754,1961,2261,4831,3316,9135,2093,1388,9907,3288,5037,2204,1349,2336,4798,1657,2375,3414,6102,2910,1614,766,1637,1554,8642,786,821,8441,1517,7358],()=>t(48292)),_N_E=e.O()}]);