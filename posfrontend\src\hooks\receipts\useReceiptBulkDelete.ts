import { useBulkDeleteReceiptsMutation } from "@/reduxRTK/services/receiptApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useReceiptBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting receipts
  const [bulkDeleteReceipts, { isLoading }] = useBulkDeleteReceiptsMutation();

  const deleteReceipts = async (receiptIds: number[]) => {
    try {
      console.log("Bulk deleting receipts with IDs:", receiptIds);
      
      const result = await bulkDeleteReceipts(receiptIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete receipts");
      }

      showMessage("success", `${receiptIds.length} receipts deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete receipts error:", error);
      showMessage("error", error.message || "Failed to delete receipts");
      throw error;
    }
  };

  return {
    bulkDeleteReceipts: deleteReceipts,
    isDeleting: isLoading
  };
};
