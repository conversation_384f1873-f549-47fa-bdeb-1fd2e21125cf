"use client";

import React, { useEffect } from "react";
import { Form, Input, Button } from "antd";
import { Supplier, CreateSupplierDto, UpdateSupplierDto } from "@/reduxRTK/services/supplierApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useSupplierCreate } from "@/hooks/suppliers/useSupplierCreate";
import { useSupplierUpdate } from "@/hooks/suppliers/useSupplierUpdate";
import { User } from "@/types/user";
import { UserOutlined, MailOutlined, PhoneOutlined, HomeOutlined } from "@ant-design/icons";
import "./supplier-panels.css";

interface SupplierFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  supplier?: Supplier | null;
  currentUser?: User | null;
}

const SupplierFormPanel: React.FC<SupplierFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  supplier,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!supplier;

  // Hooks for creating and updating suppliers
  const { createSupplier, isSubmitting: isCreating } = useSupplierCreate(onSuccess);
  const { updateSupplier, isUpdating } = useSupplierUpdate(onSuccess);

  // Reset form when supplier changes
  useEffect(() => {
    if (isOpen) {
      form.resetFields();

      if (supplier) {
        // Set form values for edit mode
        form.setFieldsValue({
          name: supplier.name,
          email: supplier.email,
          phone: supplier.phone,
          address: supplier.address,
          contactPerson: supplier.contactPerson,
        });
      }
    }
  }, [form, isOpen, supplier]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      if (isEditMode && supplier) {
        // Update existing supplier
        await updateSupplier(supplier.id, values as UpdateSupplierDto);
      } else {
        // Create new supplier
        await createSupplier(values as CreateSupplierDto);
      }
    } catch (error) {
      console.error("Failed to save supplier:", error);
    }
  };

  // Panel title
  const panelTitle = isEditMode ? "Edit Supplier" : "Add Supplier";

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isCreating || isUpdating}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isCreating || isUpdating}
        onClick={() => form.submit()}
      >
        {isEditMode ? "Update" : "Save"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={panelTitle}
      width="500px"
      footer={panelFooter}
    >
      <div className="p-6 bg-white">
        {/* Form heading with icon */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            {isEditMode ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Supplier: {supplier?.name}
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                Add New Supplier
              </>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {isEditMode
              ? "Update supplier information"
              : "Fill in the details to add a new supplier"}
          </p>
        </div>

        {/* Required fields indicator */}
        <div className="mb-4 text-sm text-gray-600">
          <span className="text-red-500 mr-1">*</span> indicates required fields
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="supplier-form"
          requiredMark={true}
        >
          <Form.Item
            name="name"
            label={<span className="flex items-center"><UserOutlined className="mr-1" /> Supplier Name <span className="text-red-500 ml-1">*</span></span>}
            rules={[{ required: true, message: "Please enter supplier name" }]}
            tooltip="Name of the supplier company or individual"
          >
            <Input
              placeholder="Enter supplier name"
            />
          </Form.Item>

          <Form.Item
            name="phone"
            label={<span className="flex items-center"><PhoneOutlined className="mr-1" /> Phone <span className="text-red-500 ml-1">*</span></span>}
            rules={[{ required: true, message: "Please enter phone number" }]}
            tooltip="Contact phone number for the supplier"
          >
            <Input
              placeholder="Enter phone number"
            />
          </Form.Item>

          <Form.Item
            name="email"
            label={<span className="flex items-center"><MailOutlined className="mr-1" /> Email</span>}
            rules={[
              { type: 'email', message: "Please enter a valid email" }
            ]}
            tooltip="Contact email for the supplier (optional)"
          >
            <Input
              placeholder="Enter email address (optional)"
            />
          </Form.Item>

          <Form.Item
            name="address"
            label={<span className="flex items-center"><HomeOutlined className="mr-1" /> Address</span>}
            tooltip="Physical address of the supplier (optional)"
          >
            <Input.TextArea
              placeholder="Enter address (optional)"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="contactPerson"
            label={<span className="flex items-center"><UserOutlined className="mr-1" /> Contact Person</span>}
            tooltip="Name of the primary contact person (optional)"
          >
            <Input
              placeholder="Enter contact person name (optional)"
            />
          </Form.Item>

          <div className="text-gray-500 text-sm mt-4 mb-2">
            <span className="text-red-500">*</span> Required fields
          </div>
        </Form>
      </div>
    </SlidingPanel>
  );
};

export default SupplierFormPanel;
