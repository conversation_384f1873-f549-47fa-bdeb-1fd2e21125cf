import { and, eq, desc, sql, or, count, inArray } from "drizzle-orm";
import { JwtPayload, SaleData, BulkSaleData } from "../types/type";
import { postgresDb } from "../db/db";
import {
  products,
  receipts,
  sales,
  salesItems,
  stockAdjustments,
  users,
  stores,
  userStores,
} from "../db/schema";
import { authorizeAction } from "../utils/authorizeAction";
import { receiptSchema, saleSchema } from "../validation/schema";
import { getUserDefaultStore } from "./userStoreService";

/**
 * ✅ Create a new sale (Cashiers, Admins, Superadmins)
 */
export const createSale = async (
  requester: JwtPayload,
  bulkSaleData: {
    saleData: {
      totalAmount: number;
      paymentMethod: string;
      items: { productId: number; quantity: number; price: number }[];
      receiptUrl: string; // Now expecting receipt URL from frontend
      storeId?: number; // Optional store ID
    };
  }
): Promise<{ sales: { saleId: number }[] }> => {
  // Authorization to create a sale
  await authorizeAction(requester, "create", "sales");

  // Validate sale data using Zod
  const parsedSaleData = saleSchema.parse(bulkSaleData.saleData);
  const parsedReceiptData = receiptSchema.parse({
    saleId: 1, // Placeholder, will be replaced after sale creation
    receiptUrl: bulkSaleData.saleData.receiptUrl,
  });

  // Get store information
  let storeId = bulkSaleData.saleData.storeId;

  // If no store ID provided, try to get the user's default store
  if (!storeId) {
    try {
      const defaultStore = await getUserDefaultStore(requester, requester.id);
      if (defaultStore) {
        storeId = defaultStore.id;
      }
    } catch (error) {
      console.error("Error getting default store:", error);
      // Continue without store ID if there's an error
    }
  }

  // Start a transaction to ensure atomicity
  return await postgresDb.transaction(async (trx) => {
    // Insert the sale data
    const insertedSales = await trx
      .insert(sales)
      .values({
        totalAmount: parsedSaleData.totalAmount.toString(),
        paymentMethod: parsedSaleData.paymentMethod,
        createdBy: requester.id,
        storeId: storeId, // Include store ID if available
      })
      .returning({ saleId: sales.id });

    if (insertedSales.length === 0) {
      throw new Error("Sale creation failed.");
    }

    const saleId = insertedSales[0].saleId;

    // Insert sale items in bulk
    const saleItemsData = parsedSaleData.items.map((item) => ({
      saleId: saleId, // Link the sale ID to the inserted sale
      productId: item.productId,
      quantity: item.quantity,
      price: item.price.toString(),
    }));

    await trx.insert(salesItems).values(saleItemsData);

    // Stock update for each product in the sale
    for (const item of parsedSaleData.items) {
      // Validate stock before proceeding with the sale
      const stock = await trx
        .select({ stockQuantity: products.stockQuantity })
        .from(products)
        .where(eq(products.id, item.productId))
        .limit(1);

      if (!stock.length || stock[0].stockQuantity < item.quantity) {
        throw new Error(`Not enough stock for product ID ${item.productId}`);
      }

      // Update the product stock
      await trx
        .update(products)
        .set({
          stockQuantity: sql`${products.stockQuantity} - ${item.quantity}`,
        })
        .where(eq(products.id, item.productId));
    }

    // Validate and Insert the receipt into the receipts table
    await trx.insert(receipts).values({
      saleId: saleId,
      receiptUrl: parsedReceiptData.receiptUrl, // Using validated URL
      createdBy: requester.id,
    });

    // Return the sale ID(s)
    return { sales: [{ saleId }] };
  });
};

/**
 * ✅ Get all sales (Anyone can view sales)
 */
export const getAllSales = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10
) => {
  await authorizeAction(requester, "getAll", "sales");

  const offset = (page - 1) * limit;
  const isSuperadmin = requester.role === "superadmin";
  const isCashier = requester.role === "cashier";

  // Different filter logic based on role
  let salesFilter;

  if (isSuperadmin) {
    // Superadmin sees all sales
    salesFilter = undefined;
  } else if (requester.role === "admin") {
    // Admin can only see their own sales and sales from users they created
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

    if (teamMemberIds.length > 0) {
      // Admin can see their own sales and sales from users they created
      salesFilter = or(
        eq(sales.createdBy, requester.id),
        inArray(sales.createdBy, teamMemberIds)
      );
    } else {
      // If no team members found, just show their own sales
      salesFilter = eq(sales.createdBy, requester.id);
    }
  } else if (isCashier) {
    // Cashier can only see their own sales and sales from their admin and siblings

    // Find the cashier's creator (admin)
    const creatorResult = await postgresDb
      .select({ creatorId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    const creatorId = creatorResult[0]?.creatorId || undefined;

    if (creatorId) {
      // Get all users created by the same admin (siblings)
      const siblingsResult = await postgresDb
        .select({ siblingId: users.id })
        .from(users)
        .where(eq(users.createdBy, creatorId));

      const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

      // Build the filter to include own sales, creator's sales, and siblings' sales
      salesFilter = or(
        eq(sales.createdBy, requester.id),
        eq(sales.createdBy, creatorId),
        inArray(sales.createdBy, siblingIds)
      );
    } else {
      // If no creator found, just show their own sales
      salesFilter = eq(sales.createdBy, requester.id);
    }
  } else {
    // Default case - just show own sales
    salesFilter = eq(sales.createdBy, requester.id);
  }

  const [salesData, totalSales] = await Promise.all([
    postgresDb
      .select({
        id: sales.id,
        totalAmount: sales.totalAmount,
        paymentMethod: sales.paymentMethod,
        transactionDate: sales.transactionDate,
        createdBy: sales.createdBy,
        createdByName: users.name, // Include the creator's name
        storeId: sales.storeId,
        storeName: stores.name,
      })
      .from(sales)
      .leftJoin(users, eq(sales.createdBy, users.id))
      .leftJoin(stores, eq(sales.storeId, stores.id))
      .where(salesFilter)
      .orderBy(desc(sales.transactionDate))
      .limit(limit)
      .offset(offset),
    postgresDb.select({ count: count() }).from(sales).where(salesFilter),
  ]);

  // Get sale items for each sale
  const salesWithItems = await Promise.all(
    salesData.map(async (sale: { id: number }) => {
      const items = await getSaleDetails(sale.id);
      return {
        ...sale,
        items // Include the items directly in each sale object
      };
    })
  );

  return {
    total: totalSales[0]?.count ?? 0,
    page,
    perPage: limit,
    sales: salesWithItems,
  };
};

// salesDetailsService.ts

export const getSaleDetails = async (saleId: number) => {
  try {

    console.log(`Fetching sale items for saleId ${saleId}`);

    // Fetch sale items for a given saleId along with product details using a JOIN query
    const saleItemsWithDetails = await postgresDb
      .select({
        id: salesItems.id,
        saleId: salesItems.saleId,
        productId: salesItems.productId,
        productName: products.name,
        quantity: salesItems.quantity,
        price: salesItems.price,
      })
      .from(salesItems)
      .leftJoin(products, eq(salesItems.productId, products.id)) // Using leftJoin to ensure we get items even if product is deleted
      .where(eq(salesItems.saleId, saleId)); // Filter by the saleId

    // If no items found, create a fallback item based on the sale total
    if (!saleItemsWithDetails || saleItemsWithDetails.length === 0) {
      console.log(`No sale items found for saleId ${saleId}, checking sale total`);

      // Get the sale to create a fallback item
      const dbSaleData = await postgresDb
        .select({
          id: sales.id,
          totalAmount: sales.totalAmount,
        })
        .from(sales)
        .where(eq(sales.id, saleId))
        .limit(1);

      let saleData = null;
      if (dbSaleData && dbSaleData.length > 0) {
        saleData = dbSaleData[0];
      }

      if (saleData) {
        const fallbackItems = [{
          id: 0,
          saleId: saleId,
          productId: 0,
          productName: "Sale Total (No Items Found)",
          quantity: 1,
          price: (saleData as any)?.totalAmount || '0',
        }];

        return fallbackItems;
      }
    }

    // Map the results to ensure consistent property names
    const formattedItems = saleItemsWithDetails.map((item: { id: number; saleId: number; productId: number; productName: string | null; quantity: number; price: string }) => ({
      id: item.id,
      saleId: item.saleId,
      productId: item.productId,
      productName: item.productName || "Unknown Product",
      quantity: item.quantity,
      price: item.price,
    }));

    // Ensure we always return an array, even if empty
    return formattedItems;
  } catch (error) {
    console.error(`Error retrieving sale items for saleId ${saleId}:`, error);
    return []; // Return empty array on error
  }
};

/**
 * ✅ Get sale by ID (Anyone can view a sale)
 */
export const getSaleById = async (requester: JwtPayload, saleId: number) => {
  await authorizeAction(requester, "getById", "sales", saleId);

  const isSuperadmin = requester.role === "superadmin";
  const isCashier = requester.role === "cashier";

  // Different filter logic based on role
  let saleFilter;

  if (isSuperadmin) {
    // Superadmin can see any sale
    saleFilter = eq(sales.id, saleId);
  } else if (requester.role === "admin") {
    // Admin can only see their own sales and sales from users they created
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

    if (teamMemberIds.length > 0) {
      // Admin can see their own sales and sales from users they created
      saleFilter = and(
        eq(sales.id, saleId),
        or(
          eq(sales.createdBy, requester.id),
          inArray(sales.createdBy, teamMemberIds)
        )
      );
    } else {
      // If no team members found, just show their own sales
      saleFilter = and(
        eq(sales.id, saleId),
        eq(sales.createdBy, requester.id)
      );
    }
  } else if (isCashier) {
    // Cashier can only see their own sales and sales from their admin and siblings

    // Find the cashier's creator (admin)
    const creatorResult = await postgresDb
      .select({ creatorId: users.createdBy })
      .from(users)
      .where(eq(users.id, requester.id))
      .limit(1);

    const creatorId = creatorResult[0]?.creatorId || undefined;

    if (creatorId) {
      // Get all users created by the same admin (siblings)
      const siblingsResult = await postgresDb
        .select({ siblingId: users.id })
        .from(users)
        .where(eq(users.createdBy, creatorId));

      const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

      // Build the filter to include own sales, creator's sales, and siblings' sales
      saleFilter = and(
        eq(sales.id, saleId),
        or(
          eq(sales.createdBy, requester.id),
          eq(sales.createdBy, creatorId),
          inArray(sales.createdBy, siblingIds)
        )
      );
    } else {
      // If no creator found, just show their own sales
      saleFilter = and(
        eq(sales.id, saleId),
        eq(sales.createdBy, requester.id)
      );
    }
  } else {
    // Default case - just show own sales
    saleFilter = and(
      eq(sales.id, saleId),
      eq(sales.createdBy, requester.id)
    );
  }

  // Join with users and stores tables to get the creator's name and store info
  const saleData = await postgresDb
    .select({
      id: sales.id,
      totalAmount: sales.totalAmount,
      paymentMethod: sales.paymentMethod,
      transactionDate: sales.transactionDate,
      createdBy: sales.createdBy,
      createdByName: users.name, // Include the creator's name
      storeId: sales.storeId,
      storeName: stores.name,
      storeAddress: stores.address,
      storeCity: stores.city,
      storeState: stores.state,
      storeCountry: stores.country,
      storePhone: stores.phone,
      storeEmail: stores.email,
      storeLogo: stores.logo,
    })
    .from(sales)
    .leftJoin(users, eq(sales.createdBy, users.id))
    .leftJoin(stores, eq(sales.storeId, stores.id))
    .where(saleFilter)
    .limit(1);

  if (saleData.length === 0) throw new Error("Sale not found or unauthorized.");

  // Get the sale items for this sale (already cached in getSaleDetails)
  const saleItems = await getSaleDetails(saleId);

  // Return the sale with its items
  const saleWithItems = {
    ...saleData[0],
    items: saleItems // Include the items directly in the sale object
  };

  return saleWithItems;
};

/**
 * ✅ Update sale (Only Admins & Superadmins)
 */
export const updateSale = async (
  requester: JwtPayload,
  saleId: number,
  updateData: Partial<SaleData>
) => {
  await authorizeAction(requester, "update", "sales", saleId);

  const formattedUpdateData = {
    ...updateData,
    totalAmount: updateData.totalAmount
      ? updateData.totalAmount.toString()
      : undefined, // ✅ Convert number to string
  };

  const updatedSale = await postgresDb
    .update(sales)
    .set(formattedUpdateData)
    .where(eq(sales.id, saleId))
    .returning();

  if (!updatedSale || updatedSale.length === 0) {
    throw new Error("Update failed: Sale not found.");
  }

  // No cache invalidation needed

  return { updatedSale: updatedSale[0] };
};

/**
 * ❌ Delete sale (Only Admins & Superadmins)
 */
export const deleteSale = async (requester: JwtPayload, ids: number | number[]) => {
  const saleIds = Array.isArray(ids) ? ids : [ids];

  // Check authorization for each sale
  for (const saleId of saleIds) {
    await authorizeAction(requester, "delete", "sales", saleId);
  }

  if (requester.role === "cashier") {
    throw new Error("Unauthorized: Cashiers cannot delete sales.");
  }

  const isSuperadmin = requester.role === "superadmin";

  // Build the delete filter based on the user's role
  let deleteFilter;

  if (isSuperadmin) {
    // Superadmin can delete any sale
    deleteFilter = inArray(sales.id, saleIds);
  } else if (requester.role === "admin") {
    // Admin can only delete their own sales and sales from users they created
    const teamMembersResult = await postgresDb
      .select({ memberId: users.id })
      .from(users)
      .where(eq(users.createdBy, requester.id));

    const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

    if (teamMemberIds.length > 0) {
      // Admin can delete their own sales and sales from users they created
      deleteFilter = and(
        inArray(sales.id, saleIds),
        or(
          eq(sales.createdBy, requester.id),
          inArray(sales.createdBy, teamMemberIds)
        )
      );
    } else {
      // If no team members found, just delete their own sales
      deleteFilter = and(
        inArray(sales.id, saleIds),
        eq(sales.createdBy, requester.id)
      );
    }
  } else {
    // Default case - just delete own sales (should not happen due to earlier check)
    deleteFilter = and(
      inArray(sales.id, saleIds),
      eq(sales.createdBy, requester.id)
    );
  }

  const deletedSales = await postgresDb
    .delete(sales)
    .where(deleteFilter)
    .returning({ deletedId: sales.id });

  if (!deletedSales || deletedSales.length === 0) {
    throw new Error("Delete failed: Sale(s) not found or unauthorized.");
  }

  // No cache invalidation needed

  return {
    deletedIds: deletedSales.map((sale: { deletedId: number }) => sale.deletedId)
  };
};
