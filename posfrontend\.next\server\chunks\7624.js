"use strict";exports.id=7624,exports.ids=[7624],exports.modules={26222:(e,t,o)=>{o.d(t,{A:()=>z});var n=o(58009),r=o(56073),a=o.n(r),i=o(80775),l=o(22301),s=o(2866),c=o(27343),d=o(1439),u=o(47285),m=o(92864),b=o(10941),g=o(13662);let p=new d.<PERSON>("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new d.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new d.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),O=new d.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),h=new d.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),y=new d.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),$=e=>{let{componentCls:t,iconCls:o,antCls:n,badgeShadowSize:r,textFontSize:a,textFontSizeSM:i,statusSize:l,dotSize:s,textFontWeight:c,indicatorHeight:b,indicatorHeightSM:g,marginXS:$,calc:j}=e,C=`${n}-scroll-number`,x=(0,m.A)(e,(e,o)=>{let{darkColor:n}=o;return{[`&${t} ${t}-color-${e}`]:{background:n,[`&:not(${t}-count)`]:{color:n},"a:hover &":{background:n}}}});return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:b,height:b,color:e.badgeTextColor,fontWeight:c,fontSize:a,lineHeight:(0,d.zA)(b),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:j(b).div(2).equal(),boxShadow:`0 0 0 ${(0,d.zA)(r)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:g,height:g,fontSize:i,lineHeight:(0,d.zA)(g),borderRadius:j(g).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${(0,d.zA)(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:s,minWidth:s,height:s,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${(0,d.zA)(r)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${C}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${o}-spin`]:{animationName:y,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:l,height:l,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:p,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:$,color:e.colorText,fontSize:e.fontSize}}}),x),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:f,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:v,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:O,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:h,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${C}-custom-component, ${t}-count`]:{transform:"none"},[`${C}-custom-component, ${C}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[C]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${C}-only`]:{position:"relative",display:"inline-block",height:b,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${C}-only-unit`]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${C}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${C}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},j=e=>{let{fontHeight:t,lineWidth:o,marginXS:n,colorBorderBg:r}=e,a=e.colorTextLightSolid,i=e.colorError,l=e.colorErrorHover;return(0,b.oX)(e,{badgeFontHeight:t,badgeShadowSize:o,badgeTextColor:a,badgeColor:i,badgeColorHover:l,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},C=e=>{let{fontSize:t,lineHeight:o,fontSizeSM:n,lineWidth:r}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*o)-2*r,indicatorHeightSM:t,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}},x=(0,g.OF)("Badge",e=>$(j(e)),C),w=e=>{let{antCls:t,badgeFontHeight:o,marginXS:n,badgeRibbonOffset:r,calc:a}=e,i=`${t}-ribbon`,l=`${t}-ribbon-wrapper`,s=(0,m.A)(e,(e,t)=>{let{darkColor:o}=t;return{[`&${i}-color-${e}`]:{background:o,color:o}}});return{[l]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(e)),{position:"absolute",top:n,padding:`0 ${(0,d.zA)(e.paddingXS)}`,color:e.colorPrimary,lineHeight:(0,d.zA)(o),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${i}-text`]:{color:e.badgeTextColor},[`${i}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${(0,d.zA)(a(r).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),s),{[`&${i}-placement-end`]:{insetInlineEnd:a(r).mul(-1).equal(),borderEndEndRadius:0,[`${i}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${i}-placement-start`]:{insetInlineStart:a(r).mul(-1).equal(),borderEndStartRadius:0,[`${i}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},E=(0,g.OF)(["Badge","Ribbon"],e=>w(j(e)),C),S=e=>{let t;let{prefixCls:o,value:r,current:i,offset:l=0}=e;return l&&(t={position:"absolute",top:`${l}00%`,left:0}),n.createElement("span",{style:t,className:a()(`${o}-only-unit`,{current:i})},r)},k=e=>{let t,o;let{prefixCls:r,count:a,value:i}=e,l=Number(i),s=Math.abs(a),[c,d]=n.useState(l),[u,m]=n.useState(s),b=()=>{d(l),m(s)};if(n.useEffect(()=>{let e=setTimeout(b,1e3);return()=>clearTimeout(e)},[l]),c===l||Number.isNaN(l)||Number.isNaN(c))t=[n.createElement(S,Object.assign({},e,{key:l,current:!0}))],o={transition:"none"};else{t=[];let r=l+10,a=[];for(let e=l;e<=r;e+=1)a.push(e);let i=u<s?1:-1,d=a.findIndex(e=>e%10===c);t=(i<0?a.slice(0,d+1):a.slice(d)).map((t,o)=>n.createElement(S,Object.assign({},e,{key:t,value:t%10,offset:i<0?o-d:o,current:o===d}))),o={transform:`translateY(${-function(e,t,o){let n=e,r=0;for(;(n+10)%10!==t;)n+=o,r+=o;return r}(c,l,i)}00%)`}}return n.createElement("span",{className:`${r}-only`,style:o,onTransitionEnd:b},t)};var A=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let N=n.forwardRef((e,t)=>{let{prefixCls:o,count:r,className:i,motionClassName:l,style:d,title:u,show:m,component:b="sup",children:g}=e,p=A(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=n.useContext(c.QO),v=f("scroll-number",o),O=Object.assign(Object.assign({},p),{"data-show":m,style:d,className:a()(v,i,l),title:u}),h=r;if(r&&Number(r)%1==0){let e=String(r).split("");h=n.createElement("bdi",null,e.map((t,o)=>n.createElement(k,{prefixCls:v,count:Number(r),value:t,key:e.length-o})))}return((null==d?void 0:d.borderColor)&&(O.style=Object.assign(Object.assign({},d),{boxShadow:`0 0 0 1px ${d.borderColor} inset`})),g)?(0,s.Ob)(g,e=>({className:a()(`${v}-custom-component`,null==e?void 0:e.className,l)})):n.createElement(b,Object.assign({},O,{ref:t}),h)});var M=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let P=n.forwardRef((e,t)=>{var o,r,d,u,m;let{prefixCls:b,scrollNumberPrefixCls:g,children:p,status:f,text:v,color:O,count:h=null,overflowCount:y=99,dot:$=!1,size:j="default",title:C,offset:w,style:E,className:S,rootClassName:k,classNames:A,styles:P,showZero:z=!1}=e,I=M(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:R,direction:T,badge:F}=n.useContext(c.QO),B=R("badge",b),[D,V,W]=x(B),H=h>y?`${y}+`:h,Z="0"===H||0===H,q=(null!=f||null!=O)&&(null===h||Z&&!z),Q=$&&!Z,L=Q?"":H,Y=(0,n.useMemo)(()=>(null==L||""===L||Z&&!z)&&!Q,[L,Z,z,Q]),_=(0,n.useRef)(h);Y||(_.current=h);let X=_.current,J=(0,n.useRef)(L);Y||(J.current=L);let K=J.current,G=(0,n.useRef)(Q);Y||(G.current=Q);let U=(0,n.useMemo)(()=>{if(!w)return Object.assign(Object.assign({},null==F?void 0:F.style),E);let e={marginTop:w[1]};return"rtl"===T?e.left=parseInt(w[0],10):e.right=-parseInt(w[0],10),Object.assign(Object.assign(Object.assign({},e),null==F?void 0:F.style),E)},[T,w,E,null==F?void 0:F.style]),ee=null!=C?C:"string"==typeof X||"number"==typeof X?X:void 0,et=Y||!v?null:n.createElement("span",{className:`${B}-status-text`},v),eo=X&&"object"==typeof X?(0,s.Ob)(X,e=>({style:Object.assign(Object.assign({},U),e.style)})):void 0,en=(0,l.nP)(O,!1),er=a()(null==A?void 0:A.indicator,null===(o=null==F?void 0:F.classNames)||void 0===o?void 0:o.indicator,{[`${B}-status-dot`]:q,[`${B}-status-${f}`]:!!f,[`${B}-color-${O}`]:en}),ea={};O&&!en&&(ea.color=O,ea.background=O);let ei=a()(B,{[`${B}-status`]:q,[`${B}-not-a-wrapper`]:!p,[`${B}-rtl`]:"rtl"===T},S,k,null==F?void 0:F.className,null===(r=null==F?void 0:F.classNames)||void 0===r?void 0:r.root,null==A?void 0:A.root,V,W);if(!p&&q){let e=U.color;return D(n.createElement("span",Object.assign({},I,{className:ei,style:Object.assign(Object.assign(Object.assign({},null==P?void 0:P.root),null===(d=null==F?void 0:F.styles)||void 0===d?void 0:d.root),U)}),n.createElement("span",{className:er,style:Object.assign(Object.assign(Object.assign({},null==P?void 0:P.indicator),null===(u=null==F?void 0:F.styles)||void 0===u?void 0:u.indicator),ea)}),v&&n.createElement("span",{style:{color:e},className:`${B}-status-text`},v)))}return D(n.createElement("span",Object.assign({ref:t},I,{className:ei,style:Object.assign(Object.assign({},null===(m=null==F?void 0:F.styles)||void 0===m?void 0:m.root),null==P?void 0:P.root)}),p,n.createElement(i.Ay,{visible:!Y,motionName:`${B}-zoom`,motionAppear:!1,motionDeadline:1e3},e=>{var t,o;let{className:r}=e,i=R("scroll-number",g),l=G.current,s=a()(null==A?void 0:A.indicator,null===(t=null==F?void 0:F.classNames)||void 0===t?void 0:t.indicator,{[`${B}-dot`]:l,[`${B}-count`]:!l,[`${B}-count-sm`]:"small"===j,[`${B}-multiple-words`]:!l&&K&&K.toString().length>1,[`${B}-status-${f}`]:!!f,[`${B}-color-${O}`]:en}),c=Object.assign(Object.assign(Object.assign({},null==P?void 0:P.indicator),null===(o=null==F?void 0:F.styles)||void 0===o?void 0:o.indicator),U);return O&&!en&&((c=c||{}).background=O),n.createElement(N,{prefixCls:i,show:!Y,motionClassName:r,className:s,count:K,title:ee,style:c,key:"scrollNumber"},eo)}),et))});P.Ribbon=e=>{let{className:t,prefixCls:o,style:r,color:i,children:s,text:d,placement:u="end",rootClassName:m}=e,{getPrefixCls:b,direction:g}=n.useContext(c.QO),p=b("ribbon",o),f=`${p}-wrapper`,[v,O,h]=E(p,f),y=(0,l.nP)(i,!1),$=a()(p,`${p}-placement-${u}`,{[`${p}-rtl`]:"rtl"===g,[`${p}-color-${i}`]:y},t),j={},C={};return i&&!y&&(j.background=i,C.color=i),v(n.createElement("div",{className:a()(f,m,O,h)},s,n.createElement("div",{className:a()($,O),style:Object.assign(Object.assign({},j),r)},n.createElement("span",{className:`${p}-text`},d),n.createElement("div",{className:`${p}-corner`,style:C}))))};let z=P},54979:(e,t,o)=>{let n,r,a,i;o.d(t,{Ay:()=>Q,cr:()=>H});var l=o(58009),s=o(1439),c=o(93713),d=o(45860),u=o(2316),m=o(22505),b=o(95895),g=o(21703),p=o(65657);let f=e=>{let{locale:t={},children:o,_ANT_MARK__:n}=e;l.useEffect(()=>(0,g.L)(null==t?void 0:t.Modal),[t]);let r=l.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return l.createElement(p.A.Provider,{value:r},o)};var v=o(13439),O=o(56950),h=o(5206),y=o(96451),$=o(27343),j=o(7974),C=o(43891),x=o(7822),w=o(46557);let E=`-ant-${Date.now()}-${Math.random()}`;var S=o(87375),k=o(24964),A=o(56114);let{useId:N}=Object.assign({},l),M=void 0===N?()=>"":N;var P=o(80775),z=o(39772);function I(e){let{children:t}=e,[,o]=(0,z.Ay)(),{motion:n}=o,r=l.useRef(!1);return(r.current=r.current||!1===n,r.current)?l.createElement(P.Kq,{motion:n},t):t}let R=()=>null;var T=o(47285);let F=(e,t)=>{let[o,n]=(0,z.Ay)();return(0,s.IV)({theme:o,token:n,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce,layer:{name:"antd"}},()=>[(0,T.jz)(e)])};var B=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let D=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];function V(){return n||$.yH}function W(){return r||$.pM}let H=()=>({getPrefixCls:(e,t)=>t||(e?`${V()}-${e}`:V()),getIconPrefixCls:W,getRootPrefixCls:()=>n||V(),getTheme:()=>a,holderRender:i}),Z=e=>{let{children:t,csp:o,autoInsertSpaceInButton:n,alert:r,anchor:a,form:i,locale:g,componentSize:p,direction:j,space:C,splitter:x,virtual:w,dropdownMatchSelectWidth:E,popupMatchSelectWidth:N,popupOverflow:P,legacyLocale:z,parentContext:T,iconPrefixCls:V,theme:W,componentDisabled:H,segmented:Z,statistic:q,spin:Q,calendar:L,carousel:Y,cascader:_,collapse:X,typography:J,checkbox:K,descriptions:G,divider:U,drawer:ee,skeleton:et,steps:eo,image:en,layout:er,list:ea,mentions:ei,modal:el,progress:es,result:ec,slider:ed,breadcrumb:eu,menu:em,pagination:eb,input:eg,textArea:ep,empty:ef,badge:ev,radio:eO,rate:eh,switch:ey,transfer:e$,avatar:ej,message:eC,tag:ex,table:ew,card:eE,tabs:eS,timeline:ek,timePicker:eA,upload:eN,notification:eM,tree:eP,colorPicker:ez,datePicker:eI,rangePicker:eR,flex:eT,wave:eF,dropdown:eB,warning:eD,tour:eV,tooltip:eW,popover:eH,popconfirm:eZ,floatButtonGroup:eq,variant:eQ,inputNumber:eL,treeSelect:eY}=e,e_=l.useCallback((t,o)=>{let{prefixCls:n}=e;if(o)return o;let r=n||T.getPrefixCls("");return t?`${r}-${t}`:r},[T.getPrefixCls,e.prefixCls]),eX=V||T.iconPrefixCls||$.pM,eJ=o||T.csp;F(eX,eJ);let eK=function(e,t,o){var n;(0,m.rJ)("ConfigProvider");let r=e||{},a=!1!==r.inherit&&t?t:Object.assign(Object.assign({},h.sb),{hashed:null!==(n=null==t?void 0:t.hashed)&&void 0!==n?n:h.sb.hashed,cssVar:null==t?void 0:t.cssVar}),i=M();return(0,d.A)(()=>{var n,l;if(!e)return t;let s=Object.assign({},a.components);Object.keys(e.components||{}).forEach(t=>{s[t]=Object.assign(Object.assign({},s[t]),e.components[t])});let c=`css-var-${i.replace(/:/g,"")}`,d=(null!==(n=r.cssVar)&&void 0!==n?n:a.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:null==o?void 0:o.prefixCls},"object"==typeof a.cssVar?a.cssVar:{}),"object"==typeof r.cssVar?r.cssVar:{}),{key:"object"==typeof r.cssVar&&(null===(l=r.cssVar)||void 0===l?void 0:l.key)||c});return Object.assign(Object.assign(Object.assign({},a),r),{token:Object.assign(Object.assign({},a.token),r.token),components:s,cssVar:d})},[r,a],(e,t)=>e.some((e,o)=>{let n=t[o];return!(0,A.A)(e,n,!0)}))}(W,T.theme,{prefixCls:e_("")}),eG={csp:eJ,autoInsertSpaceInButton:n,alert:r,anchor:a,locale:g||z,direction:j,space:C,splitter:x,virtual:w,popupMatchSelectWidth:null!=N?N:E,popupOverflow:P,getPrefixCls:e_,iconPrefixCls:eX,theme:eK,segmented:Z,statistic:q,spin:Q,calendar:L,carousel:Y,cascader:_,collapse:X,typography:J,checkbox:K,descriptions:G,divider:U,drawer:ee,skeleton:et,steps:eo,image:en,input:eg,textArea:ep,layout:er,list:ea,mentions:ei,modal:el,progress:es,result:ec,slider:ed,breadcrumb:eu,menu:em,pagination:eb,empty:ef,badge:ev,radio:eO,rate:eh,switch:ey,transfer:e$,avatar:ej,message:eC,tag:ex,table:ew,card:eE,tabs:eS,timeline:ek,timePicker:eA,upload:eN,notification:eM,tree:eP,colorPicker:ez,datePicker:eI,rangePicker:eR,flex:eT,wave:eF,dropdown:eB,warning:eD,tour:eV,tooltip:eW,popover:eH,popconfirm:eZ,floatButtonGroup:eq,variant:eQ,inputNumber:eL,treeSelect:eY},eU=Object.assign({},T);Object.keys(eG).forEach(e=>{void 0!==eG[e]&&(eU[e]=eG[e])}),D.forEach(t=>{let o=e[t];o&&(eU[t]=o)}),void 0!==n&&(eU.button=Object.assign({autoInsertSpace:n},eU.button));let e0=(0,d.A)(()=>eU,eU,(e,t)=>{let o=Object.keys(e),n=Object.keys(t);return o.length!==n.length||o.some(o=>e[o]!==t[o])}),{layer:e1}=l.useContext(s.J),e5=l.useMemo(()=>({prefixCls:eX,csp:eJ,layer:e1?"antd":void 0}),[eX,eJ,e1]),e2=l.createElement(l.Fragment,null,l.createElement(R,{dropdownMatchSelectWidth:E}),t),e3=l.useMemo(()=>{var e,t,o,n;return(0,u.h)((null===(e=v.A.Form)||void 0===e?void 0:e.defaultValidateMessages)||{},(null===(o=null===(t=e0.locale)||void 0===t?void 0:t.Form)||void 0===o?void 0:o.defaultValidateMessages)||{},(null===(n=e0.form)||void 0===n?void 0:n.validateMessages)||{},(null==i?void 0:i.validateMessages)||{})},[e0,null==i?void 0:i.validateMessages]);Object.keys(e3).length>0&&(e2=l.createElement(b.A.Provider,{value:e3},e2)),g&&(e2=l.createElement(f,{locale:g,_ANT_MARK__:"internalMark"},e2)),(eX||eJ)&&(e2=l.createElement(c.A.Provider,{value:e5},e2)),p&&(e2=l.createElement(k.c,{size:p},e2)),e2=l.createElement(I,null,e2);let e7=l.useMemo(()=>{let e=eK||{},{algorithm:t,token:o,components:n,cssVar:r}=e,a=B(e,["algorithm","token","components","cssVar"]),i=t&&(!Array.isArray(t)||t.length>0)?(0,s.an)(t):O.A,l={};Object.entries(n||{}).forEach(e=>{let[t,o]=e,n=Object.assign({},o);"algorithm"in n&&(!0===n.algorithm?n.theme=i:(Array.isArray(n.algorithm)||"function"==typeof n.algorithm)&&(n.theme=(0,s.an)(n.algorithm)),delete n.algorithm),l[t]=n});let c=Object.assign(Object.assign({},y.A),o);return Object.assign(Object.assign({},a),{theme:i,token:c,components:l,override:Object.assign({override:c},l),cssVar:r})},[eK]);return W&&(e2=l.createElement(h.vG.Provider,{value:e7},e2)),e0.warning&&(e2=l.createElement(m._n.Provider,{value:e0.warning},e2)),void 0!==H&&(e2=l.createElement(S.X,{disabled:H},e2)),l.createElement($.QO.Provider,{value:e0},e2)},q=e=>{let t=l.useContext($.QO),o=l.useContext(p.A);return l.createElement(Z,Object.assign({parentContext:t,legacyLocale:o},e))};q.ConfigContext=$.QO,q.SizeContext=k.A,q.config=e=>{let{prefixCls:t,iconPrefixCls:o,theme:l,holderRender:s}=e;void 0!==t&&(n=t),void 0!==o&&(r=o),"holderRender"in e&&(i=s),l&&(Object.keys(l).some(e=>e.endsWith("Color"))?function(e,t){let o=function(e,t){let o={},n=(e,t)=>{let o=e.clone();return(o=(null==t?void 0:t(o))||o).toRgbString()},r=(e,t)=>{let r=new C.Y(e),a=(0,j.cM)(r.toRgbString());o[`${t}-color`]=n(r),o[`${t}-color-disabled`]=a[1],o[`${t}-color-hover`]=a[4],o[`${t}-color-active`]=a[6],o[`${t}-color-outline`]=r.clone().setA(.2).toRgbString(),o[`${t}-color-deprecated-bg`]=a[0],o[`${t}-color-deprecated-border`]=a[2]};if(t.primaryColor){r(t.primaryColor,"primary");let e=new C.Y(t.primaryColor),a=(0,j.cM)(e.toRgbString());a.forEach((e,t)=>{o[`primary-${t+1}`]=e}),o["primary-color-deprecated-l-35"]=n(e,e=>e.lighten(35)),o["primary-color-deprecated-l-20"]=n(e,e=>e.lighten(20)),o["primary-color-deprecated-t-20"]=n(e,e=>e.tint(20)),o["primary-color-deprecated-t-50"]=n(e,e=>e.tint(50)),o["primary-color-deprecated-f-12"]=n(e,e=>e.setA(.12*e.a));let i=new C.Y(a[0]);o["primary-color-active-deprecated-f-30"]=n(i,e=>e.setA(.3*e.a)),o["primary-color-active-deprecated-d-02"]=n(i,e=>e.darken(2))}t.successColor&&r(t.successColor,"success"),t.warningColor&&r(t.warningColor,"warning"),t.errorColor&&r(t.errorColor,"error"),t.infoColor&&r(t.infoColor,"info");let a=Object.keys(o).map(t=>`--${e}-${t}: ${o[t]};`);return`
  :root {
    ${a.join("\n")}
  }
  `.trim()}(e,t);(0,x.A)()&&(0,w.BD)(o,`${E}-dynamic-theme`)}(V(),l):a=l)},q.useConfig=function(){return{componentDisabled:(0,l.useContext)(S.A),componentSize:(0,l.useContext)(k.A)}},Object.defineProperty(q,"SizeContext",{get:()=>k.A});let Q=q},21703:(e,t,o)=>{o.d(t,{L:()=>l,l:()=>s});var n=o(13439);let r=Object.assign({},n.A.Modal),a=[],i=()=>a.reduce((e,t)=>Object.assign(Object.assign({},e),t),n.A.Modal);function l(e){if(e){let t=Object.assign({},e);return a.push(t),r=i(),()=>{a=a.filter(e=>e!==t),r=i()}}r=Object.assign({},n.A.Modal)}function s(){return r}}};