import dotenv from 'dotenv';

dotenv.config();

// Paystack configuration
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY || '';
const PAYSTACK_PUBLIC_KEY = process.env.PAYSTACK_PUBLIC_KEY || '';
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

// Disable mock verification to test real Paystack integration
const MOCK_VERIFICATION = false; // Temporarily disabled for testing

interface PaystackInitializeResponse {
  status: boolean;
  message: string;
  data: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
}

interface PaystackVerifyResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    domain: string;
    status: string;
    reference: string;
    amount: number;
    message: string | null;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    currency: string;
    ip_address: string;
    metadata: any;
    log: any;
    fees: number;
    fees_split: any;
    authorization: {
      authorization_code: string;
      bin: string;
      last4: string;
      exp_month: string;
      exp_year: string;
      channel: string;
      card_type: string;
      bank: string;
      country_code: string;
      brand: string;
      reusable: boolean;
      signature: string;
      account_name: string | null;
    };
    customer: {
      id: number;
      first_name: string | null;
      last_name: string | null;
      email: string;
      customer_code: string;
      phone: string | null;
      metadata: any;
      risk_action: string;
      international_format_phone: string | null;
    };
    plan: any;
    split: any;
    order_id: any;
    paidAt: string;
    createdAt: string;
    requested_amount: number;
    pos_transaction_data: any;
    source: any;
    fees_breakdown: any;
  };
}

/**
 * Initialize a Paystack payment transaction
 * @param email Customer email
 * @param amount Amount in smallest currency unit (pesewas for GHS, kobo for NGN)
 * @param reference Unique transaction reference
 * @param callbackUrl Callback URL after payment

 * @returns Payment initialization result
 */
export const initializePaystackPayment = async (
  email: string,
  amount: number,
  reference: string,
  callbackUrl?: string
): Promise<{ success: boolean; data?: any; message?: string }> => {
  console.log(`Initializing Paystack payment for ${email}, amount: ${amount} kobo, reference: ${reference}`);

  // Use mock initialization in development
  if (MOCK_VERIFICATION) {
    console.log('Using mock payment initialization (development mode)');
    return {
      success: true,
      data: {
        authorization_url: `https://checkout.paystack.com/mock-${reference}`,
        access_code: `mock_access_code_${reference}`,
        reference: reference
      }
    };
  }

  try {
    // Check if secret key is available
    if (!PAYSTACK_SECRET_KEY) {
      throw new Error('Paystack secret key not configured');
    }

    const payload: any = {
      email,
      amount: Math.round(amount), // Ensure amount is an integer (pesewas for GHS)
      reference,
      // Remove currency parameter to let Paystack use account default (GHS)
      callback_url: callbackUrl,
      metadata: {
        custom_fields: [
          {
            display_name: "Payment For",
            variable_name: "payment_for",
            value: "POS System Subscription"
          }
        ]
      }
    };

    console.log('Paystack payload:', JSON.stringify(payload, null, 2));

    // Make API request to Paystack
    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    // Check response status
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Paystack API error (${response.status}):`, errorText);
      throw new Error(`Paystack API returned status ${response.status}: ${errorText}`);
    }

    const result: PaystackInitializeResponse = await response.json();
    console.log('Paystack initialization response:', result);

    if (result.status) {
      return {
        success: true,
        data: result.data
      };
    } else {
      console.error('Paystack initialization failed:', result.message);
      return {
        success: false,
        message: result.message || 'Payment initialization failed'
      };
    }
  } catch (error: any) {
    console.error('Paystack initialization error:', error);
    return {
      success: false,
      message: error.message || 'Payment initialization failed'
    };
  }
};

/**
 * Verify a Paystack payment transaction
 * @param reference The transaction reference
 * @returns Verification result
 */
export const verifyPaystackPayment = async (
  reference: string
): Promise<{ success: boolean; data?: any; message?: string }> => {
  console.log(`Verifying Paystack payment with reference: ${reference}`);

  // Use mock verification in development
  if (MOCK_VERIFICATION) {
    console.log('Using mock payment verification (development mode)');
    return {
      success: true,
      data: {
        status: 'success',
        reference: reference,
        amount: 8900, // Mock amount in kobo
        currency: 'GHS',
        channel: 'card',
        authorization: {
          authorization_code: 'AUTH_mock123',
          channel: 'card'
        }
      }
    };
  }

  try {
    // Check if secret key is available
    if (!PAYSTACK_SECRET_KEY) {
      throw new Error('Paystack secret key not configured');
    }

    // Make API request to Paystack
    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // Check response status
    if (!response.ok) {
      throw new Error(`Paystack API returned status ${response.status}`);
    }

    const result: PaystackVerifyResponse = await response.json();

    if (result.status) {
      return {
        success: true,
        data: result.data
      };
    } else {
      return {
        success: false,
        message: result.message || 'Payment verification failed'
      };
    }
  } catch (error: any) {
    console.error('Paystack verification error:', error);
    return {
      success: false,
      message: error.message || 'Payment verification failed'
    };
  }
};

/**
 * Get Paystack public key for frontend
 * @returns Public key
 */
export const getPaystackPublicKey = (): string => {
  return PAYSTACK_PUBLIC_KEY;
};

/**
 * Convert amount from Ghana Cedis to Pesewas (smallest unit for GHS)
 * @param amountInCedis Amount in Ghana Cedis
 * @returns Amount in Pesewas
 */
export const convertCedisToKobo = (amountInCedis: number): number => {
  return Math.round(amountInCedis * 100);
};

/**
 * Convert amount from Pesewas to Ghana Cedis
 * @param amountInPesewas Amount in Pesewas
 * @returns Amount in Ghana Cedis
 */
export const convertKoboToCedis = (amountInPesewas: number): number => {
  return amountInPesewas / 100;
};
 