import { postgresDb } from "../db/db";
import { users, userStores, payments } from "../db/schema";
import { eq, or, count, desc, and, like, inArray, ne } from "drizzle-orm";
import { userSchema, loginSchema } from "../validation/schema";
import {
  generateToken,
  hashPassword,
  comparePassword,
  omitPassword,
} from "../utils/authUtils";
import { JwtPayload, UserRecord, UserRole } from "../types/type";
import { authorizeAction } from "../utils/authorizeAction";
import dayjs from "dayjs";
import { updatePaymentStatus } from "../utils/updatePaymentStatus";
import { getUserDefaultStore } from "./userStoreService";
import { initializeDefaultExpenseCategories } from "./expenseCategoryService";

// REMOVED: All caching to ensure fresh data
// import cache from "../config/lruCache";
// const userCache = new Map<string, UserRecord>();

export const registerUser = async (
  requester: JwtPayload,
  userData: {
    name: string;
    email: string;
    password: string;
    role?: UserRole;
    phone: string;
    paymentStatus?: "pending" | "paid" | "overdue" | "inactive";
  }
) => {
  console.log("Received Payment Status:", userData.paymentStatus);

  // 🔹 Default payment status based on requester role and user role
  let paymentStatus = userData.paymentStatus ?? "pending";

  // If an admin is creating a user (which will be a cashier), set status to "paid"
  if (requester.role === "admin" && !userData.paymentStatus) {
    paymentStatus = "paid";
  }

  // If a superadmin is creating an admin user, they get 3-month free trial with "paid" status
  if (requester.role === "superadmin" && (userData.role === "admin" || !userData.role)) {
    paymentStatus = "paid"; // Set to paid so they get the 3-month trial
  }

  if (!["pending", "paid", "overdue", "inactive"].includes(paymentStatus)) {
    throw new Error("Invalid payment status.");
  }

  // 🔹 Validate input using Zod schema
  userSchema.parse({ ...userData, paymentStatus });

  // 🔹 Check authorization
  await authorizeAction(requester, "create", "user");

  // 🔹 Check if email or phone already exists
  const existingUser = await postgresDb
    .select()
    .from(users)
    .where(or(eq(users.email, userData.email), eq(users.phone, userData.phone)))
    .limit(1);

  console.log("Existing User Found:", existingUser);

  if (existingUser.length > 0) {
    if (existingUser[0].email === userData.email) {
      throw new Error("Email is already in use.");
    }
    if (existingUser[0].phone === userData.phone) {
      throw new Error("Phone number is already in use.");
    }
  }

  // 🔹 Hash password before inserting
  const hashedPassword = await hashPassword(userData.password);
  const createdAt = new Date();

  // Set next payment due based on role and payment status
  let nextPaymentDue = null;

  if (paymentStatus === "paid") {
    // If admin is creating a cashier, use admin's payment schedule
    if (requester.role === "admin") {
      // Get admin's subscription period from their latest payment
      const adminLatestPayment = await postgresDb
        .select({
          subscriptionPeriod: payments.subscriptionPeriod,
          nextPaymentDue: users.nextPaymentDue
        })
        .from(payments)
        .leftJoin(users, eq(payments.userId, users.id))
        .where(
          and(
            eq(payments.userId, requester.id),
            eq(payments.status, "successful")
          )
        )
        .orderBy(desc(payments.paidAt))
        .limit(1);

      if (adminLatestPayment.length > 0) {
        // Use admin's subscription period
        const subscriptionPeriod = adminLatestPayment[0].subscriptionPeriod || 1;
        nextPaymentDue = dayjs(createdAt).add(subscriptionPeriod, "month").toDate();
        console.log(`📅 Cashier inherits admin's ${subscriptionPeriod}-month subscription period`);
      } else {
        // Fallback to 1 month if no payment history found
        nextPaymentDue = dayjs(createdAt).add(1, "month").toDate();
        console.log(`⚠️ No admin payment history found, defaulting to 1-month subscription`);
      }
    } else if (requester.role === "superadmin" && (userData.role === "admin" || !userData.role)) {
      // New admin users get 3-month free trial
      nextPaymentDue = dayjs(createdAt).add(3, "month").toDate();
      console.log(`🎉 New admin user gets 3-month free trial. Payment due: ${nextPaymentDue}`);
    } else {
      // Default case
      nextPaymentDue = dayjs(createdAt).add(1, "month").toDate();
    }
  }

  try {
    // Start a transaction to ensure atomicity
    return await postgresDb.transaction(async (trx) => {
      // 🔹 Insert new user into the database
      const [newUser] = await trx
        .insert(users)
        .values({
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          role: userData.role || "cashier",
          passwordHash: hashedPassword,
          createdBy: requester.id,
          paymentStatus,
          lastPaymentDate: createdAt,
          nextPaymentDue,
          createdAt,
        })
        .returning();

      // If an admin is creating a cashier, auto-associate with admin's default store
      if (requester.role === "admin" && newUser.role === "cashier") {
        try {
          // Get admin's default store
          const adminDefaultStore = await getUserDefaultStore(requester, requester.id);

          if (adminDefaultStore) {
            // Associate the new cashier with the admin's default store
            await trx
              .insert(userStores)
              .values({
                userId: newUser.id,
                storeId: adminDefaultStore.id,
                isDefault: true, // Make it the default store for the cashier
                createdBy: requester.id,
              });

            console.log(`Cashier (ID: ${newUser.id}) auto-associated with admin's store (ID: ${adminDefaultStore.id})`);
          }
        } catch (storeError) {
          console.error("Error associating cashier with admin's store:", storeError);
          // Continue even if store association fails - don't block user creation
        }
      }

      // Initialize default expense categories for new admin users
      if (newUser.role === "admin") {
        try {
          await initializeDefaultExpenseCategories(newUser.id);
          console.log(`Default expense categories initialized for admin (ID: ${newUser.id})`);
        } catch (expenseError) {
          console.error("Error initializing default expense categories:", expenseError);
          // Continue even if expense category initialization fails - don't block user creation
        }
      }

      // 🔹 Remove password before returning user data
      const safeUser = omitPassword(newUser);
      console.log("Registered User (No Password):", safeUser);

      // 🔹 Generate authentication token
      const accessToken = generateToken(safeUser.id, safeUser.role as UserRole);
      return { user: safeUser, accessToken };
    });
  } catch (error: any) {
    console.error("Database Error:", error);

    if (
      error.message.includes("duplicate key value violates unique constraint")
    ) {
      if (error.message.includes("users_email_unique")) {
        throw new Error("Email is already in use.");
      }
      if (error.message.includes("users_phone_unique")) {
        throw new Error("Phone number is already in use.");
      }
    }

    throw error;
  }
};

// ✅ **Login User** - NO CACHING for fresh data
export const loginUser = async (email: string, password: string) => {
  loginSchema.parse({ email, password });

  console.log(`🔍 Login attempt for user: ${email} - Fetching fresh data from database`);

  // ALWAYS fetch fresh data from database - NO CACHING
  const userData = await postgresDb
    .select({
      id: users.id,
      email: users.email,
      passwordHash: users.passwordHash,
      role: users.role,
      paymentStatus: users.paymentStatus,
      name: users.name,
      phone: users.phone,
      createdAt: users.createdAt,
      lastPaymentDate: users.lastPaymentDate,
      nextPaymentDue: users.nextPaymentDue,
      createdBy: users.createdBy
    })
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  if (userData.length === 0) throw new Error("User not found.");

  const userRecord = userData[0] as unknown as UserRecord;

  console.log(`✅ Fresh user data fetched for ${email}:`, {
    id: userRecord.id,
    paymentStatus: userRecord.paymentStatus,
    lastPaymentDate: userRecord.lastPaymentDate,
    nextPaymentDue: userRecord.nextPaymentDue
  });

  // Verify password
  const isPasswordValid = await comparePassword(
    password,
    userRecord.passwordHash
  );

  if (!isPasswordValid) {
    // Log failed login attempt
    console.warn(`Failed login attempt for user: ${email}`);
    throw new Error("Invalid credentials.");
  }

  // Create safe user object without password
  const safeUser = omitPassword(userRecord);

  // Generate token
  const accessToken = generateToken(safeUser.id, safeUser.role as UserRole);

  // REMOVED: No caching to ensure fresh data on every request
  // cache.set(userByIdCacheKey, userRecord, 1800000);

  console.log(`✅ Login successful for user ${email} with fresh payment status: ${userRecord.paymentStatus}`);

  return { user: safeUser, accessToken };
};

// ✅ **Get User by ID - NO CACHING for fresh data**
export const getUserById = async (requester: JwtPayload, id: number) => {
  try {
    console.log(`🔍 Getting user by ID: ${id} - Fetching fresh data from database`);

    // Check authorization first
    if (requester.role === "cashier" && id !== requester.id) {
      throw new Error("Unauthorized: Cashiers can only access their own details.");
    }

    // REMOVED: No caching to ensure fresh data
    // const cacheKey = `user:id:${id}:requester:${requester.id}:${requester.role}`;
    // const cachedUser = cache.get(cacheKey);

    // If not in cache, build the appropriate query
    let query;

    if (requester.role === "cashier") {
      // Cashier can only fetch their own details
      query = postgresDb.select().from(users).where(eq(users.id, requester.id));
    } else if (requester.role === "admin") {
      // Admin can fetch their own details or users they created
      if (id === requester.id) {
        // Admin fetching their own details
        query = postgresDb.select().from(users).where(eq(users.id, requester.id));
      } else {
        // Admin fetching details of a user they created
        query = postgresDb
          .select()
          .from(users)
          .where(and(eq(users.id, id), eq(users.createdBy, requester.id)));
      }
    } else if (requester.role === "superadmin") {
      // Superadmin has full access
      query = postgresDb.select().from(users).where(eq(users.id, id));
    } else {
      throw new Error("Unauthorized: Invalid role.");
    }

    // Execute the query
    const userData = await query;

    if (userData.length === 0) {
      throw new Error("User not found or access denied");
    }

    // Create safe user object without password
    const safeUser = omitPassword(userData[0]);

    // REMOVED: No caching to ensure fresh data
    // cache.set(cacheKey, safeUser, 600000);

    console.log(`✅ Fresh user data fetched for ID ${id}:`, {
      id: safeUser.id,
      paymentStatus: safeUser.paymentStatus,
      lastPaymentDate: safeUser.lastPaymentDate,
      nextPaymentDue: safeUser.nextPaymentDue
    });

    return safeUser;
  } catch (error) {
    console.error("Error in getUserById:", error);
    throw error;
  }
};


// ✅ **Delete Users by ID (Single or Multiple)**
export const deleteUserById = async (requester: JwtPayload, ids: number | number[]) => {
  const userIds = Array.isArray(ids) ? ids : [ids];

  // Check authorization for each user
  for (const id of userIds) {
    // ✅ Authorize the action (ensures Admins can only delete users they created)
    const query = authorizeAction(requester, "delete", "user", id);
    const userToDelete = await query;

    if (userToDelete.length === 0) {
      throw new Error(`User with ID ${id} not found or access denied`);
    }
  }

  // 🚨 **Perform the DELETE operation**
  const deletedUsers = await postgresDb
    .delete(users)
    .where(inArray(users.id, userIds))
    .returning({ deletedId: users.id });

  if (!deletedUsers || deletedUsers.length === 0) {
    throw new Error("Delete failed: Users not found.");
  }

  return {
    deletedIds: deletedUsers.map(user => user.deletedId),
  };
};

// ✅ **Get All Users**
export const getAllUsers = async (
  requester: JwtPayload,
  page: number = 1,
  limit: number = 10,
  searchTerm?: string
) => {
  const offset = (page - 1) * limit;

  // We'll use direct role-based filtering instead of authorizeAction
  // This is because authorizeAction returns a query, but we need to handle it differently
  // for getAllUsers

  let query;
  let countQuery;

  // Apply role-based filtering
  if (requester.role === "superadmin") {
    // Superadmin can see all users
    query = postgresDb.select().from(users);
    countQuery = postgresDb.select({ count: count() }).from(users);
  } else if (requester.role === "admin") {
    // Admin can only see users they created
    query = postgresDb
      .select()
      .from(users)
      .where(eq(users.createdBy, requester.id));
    countQuery = postgresDb
      .select({ count: count() })
      .from(users)
      .where(eq(users.createdBy, requester.id));
  } else {
    // Cashier can only see themselves
    query = postgresDb
      .select()
      .from(users)
      .where(eq(users.id, requester.id));
    countQuery = postgresDb
      .select({ count: count() })
      .from(users)
      .where(eq(users.id, requester.id));
  }

  // Apply search filter if provided
  if (searchTerm && searchTerm.trim() !== '') {
    // Use case-insensitive search with wildcards on both sides
    const searchPattern = `%${searchTerm.trim()}%`;

    // Log the exact search pattern for debugging
    console.log(`Search pattern: "${searchPattern}"`);

    const searchCondition = or(
      like(users.name, searchPattern),
      like(users.email, searchPattern),
      like(users.phone, searchPattern)
    );

    // For role-based queries, we need to modify the existing conditions
    if (requester.role === "superadmin") {
      // Superadmin - just add search condition
      query = postgresDb
        .select()
        .from(users)
        .where(searchCondition);

      countQuery = postgresDb
        .select({ count: count() })
        .from(users)
        .where(searchCondition);
    } else if (requester.role === "admin") {
      // Admin - combine with createdBy condition
      query = postgresDb
        .select()
        .from(users)
        .where(and(eq(users.createdBy, requester.id), searchCondition));

      countQuery = postgresDb
        .select({ count: count() })
        .from(users)
        .where(and(eq(users.createdBy, requester.id), searchCondition));
    } else {
      // Cashier - combine with id condition
      query = postgresDb
        .select()
        .from(users)
        .where(and(eq(users.id, requester.id), searchCondition));

      countQuery = postgresDb
        .select({ count: count() })
        .from(users)
        .where(and(eq(users.id, requester.id), searchCondition));
    }

    console.log(`Searching users with term: "${searchTerm}"`);
  }

  // Apply pagination and ordering
  query = query.orderBy(desc(users.createdAt)).limit(limit).offset(offset);

  try {
    // Execute both queries
    const [userData, totalUsers] = await Promise.all([query, countQuery]);

    // Log the results for debugging
    console.log(`getAllUsers - Found ${userData.length} users for ${requester.role} (ID: ${requester.id})`);

    return {
      total: totalUsers[0]?.count || 0,
      page,
      perPage: limit,
      users: userData.map(omitPassword),
    };
  } catch (error) {
    console.error("Error in getAllUsers:", error);
    throw error;
  }
};

// ✅ **Update User**
export const updateUserById = async (
  requester: JwtPayload,
  userId: number,
  updateData: Partial<{
    name: string;
    email: string;
    phone: string;
    role: string;
    paymentStatus: "pending" | "paid" | "overdue" | "inactive";
    currentPassword?: string;
    newPassword?: string;
    passwordChange?: boolean;
  }>
) => {
  // Handle password change if requested
  if (updateData.passwordChange && updateData.currentPassword && updateData.newPassword) {
    // Get the user's current password hash
    const [userToUpdate] = await postgresDb
      .select({ passwordHash: users.passwordHash })
      .from(users)
      .where(eq(users.id, userId));

    if (!userToUpdate) {
      throw new Error("User not found.");
    }

    // Verify current password
    const isPasswordValid = await comparePassword(
      updateData.currentPassword,
      userToUpdate.passwordHash
    );

    if (!isPasswordValid) {
      throw new Error("Current password is incorrect.");
    }

    // Hash the new password
    const newPasswordHash = await hashPassword(updateData.newPassword);

    // Update the password
    const [updatedUser] = await postgresDb
      .update(users)
      .set({ passwordHash: newPasswordHash })
      .where(eq(users.id, userId))
      .returning();

    if (!updatedUser) {
      throw new Error("Failed to update password.");
    }

    return { success: true, message: "Password updated successfully." };
  }

  // Regular user update (not password change)
  // 🔒 **Cashiers cannot update users**
  if (requester.role === "cashier" && userId !== requester.id) {
    throw new Error("Unauthorized: Cashiers can only update their own profile.");
  }

  // 🔒 **Admins can only update users they created**
  if (requester.role === "admin" && userId !== requester.id) {
    const [userToUpdate] = await postgresDb
      .select({ createdBy: users.createdBy })
      .from(users)
      .where(eq(users.id, userId));

    if (!userToUpdate || userToUpdate.createdBy !== requester.id) {
      throw new Error(
        "Unauthorized: Admins can only update users they created."
      );
    }

    // 🚫 **Admins cannot update paymentStatus**
    if (updateData.paymentStatus) {
      throw new Error("Unauthorized: Admins cannot update payment status.");
    }
  }

  // If a superadmin is updating payment status, propagate to all users created by this user
  if (requester.role === "superadmin" && updateData.paymentStatus) {
    // Get the user's role to check if they're an admin
    const [userToUpdate] = await postgresDb
      .select({ role: users.role })
      .from(users)
      .where(eq(users.id, userId));

    // Only propagate payment status changes for admin users
    if (userToUpdate && userToUpdate.role === "admin") {
      await updatePaymentStatus(userId, updateData.paymentStatus);
      console.log(`Payment status updated to ${updateData.paymentStatus} for admin ID ${userId} and all their cashiers`);
    }
  }

  // Remove password-related and undefined values from updateData
  const { currentPassword, newPassword, passwordChange, ...restData } = updateData;
  const filteredUpdateData = Object.fromEntries(
    Object.entries(restData).filter(([_, value]) => value !== undefined)
  );

  if (Object.keys(filteredUpdateData).length === 0) {
    throw new Error("No valid fields provided for update.");
  }

  // Check for duplicate email or phone before updating (only if they're actually changing)
  if (filteredUpdateData.email || filteredUpdateData.phone) {
    console.log(`🔍 Checking for duplicates - User ID: ${userId}`);
    console.log('Update data:', filteredUpdateData);

    // First get the current user data to compare
    const [currentUser] = await postgresDb
      .select({ email: users.email, phone: users.phone })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      throw new Error("User not found.");
    }

    console.log('Current user data:', currentUser);

    const conditions = [];

    // Only check for email duplicates if the email is actually changing
    if (filteredUpdateData.email && filteredUpdateData.email !== currentUser.email) {
      console.log(`📧 Email is changing: ${currentUser.email} → ${filteredUpdateData.email}`);
      conditions.push(eq(users.email, filteredUpdateData.email));
    } else if (filteredUpdateData.email) {
      console.log(`📧 Email not changing: ${filteredUpdateData.email} (same as current)`);
    }

    // Only check for phone duplicates if the phone is actually changing
    if (filteredUpdateData.phone && filteredUpdateData.phone !== currentUser.phone) {
      console.log(`📱 Phone is changing: ${currentUser.phone} → ${filteredUpdateData.phone}`);
      conditions.push(eq(users.phone, filteredUpdateData.phone));
    } else if (filteredUpdateData.phone) {
      console.log(`📱 Phone not changing: ${filteredUpdateData.phone} (same as current)`);
    }

    // Only run the duplicate check if there are conditions to check
    if (conditions.length > 0) {
      console.log(`🔍 Running duplicate check with ${conditions.length} condition(s)`);

      const existingUser = await postgresDb
        .select()
        .from(users)
        .where(
          and(
            or(...conditions),
            ne(users.id, userId) // Exclude the current user
          )
        )
        .limit(1);

      console.log('Existing user found:', existingUser);

      if (existingUser.length > 0) {
        if (filteredUpdateData.email &&
            filteredUpdateData.email !== currentUser.email &&
            existingUser[0].email === filteredUpdateData.email) {
          console.log('❌ Email conflict detected');
          throw new Error("Email is already in use by another user.");
        }
        if (filteredUpdateData.phone &&
            filteredUpdateData.phone !== currentUser.phone &&
            existingUser[0].phone === filteredUpdateData.phone) {
          console.log('❌ Phone conflict detected');
          throw new Error("Phone number is already in use by another user.");
        }
      }
    } else {
      console.log('✅ No duplicate check needed - no values are changing');
    }
  }

  // Update user data
  const [updatedUser] = await postgresDb
    .update(users)
    .set(filteredUpdateData)
    .where(eq(users.id, userId))
    .returning();

  if (!updatedUser) throw new Error("User not found or update failed.");

  return { updatedUser };
};


export const logoutUser = async (email: string) => {
  // REMOVED: No cache to clear
  // userCache.delete(email);
  console.log(`✅ User ${email} logged out - no cache to clear`);
};
