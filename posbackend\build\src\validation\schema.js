"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenseSchema = exports.expenseCategorySchema = exports.purchaseSchema = exports.supplierSchema = exports.receiptSchema = exports.stockAdjustmentSchema = exports.saleSchema = exports.productSchema = exports.categorySchema = exports.loginSchema = exports.userSchema = void 0;
const zod_1 = require("zod");
exports.userSchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, "Name is required.")
        .max(100, "Name must be at most 100 characters."),
    email: zod_1.z
        .string()
        .email("Invalid email format.")
        .max(100, "Email must be at most 100 characters."),
    phone: zod_1.z
        .string()
        .min(5, "Phone number must be at least 5 digits.")
        .max(20, "Phone number must be at most 20 digits."),
    password: zod_1.z.string().min(6, "Password must be at least 6 characters."),
    role: zod_1.z.enum(["superadmin", "admin", "cashier"], {
        message: "Invalid role. Must be 'superadmin', 'admin', or 'cashier'.",
    }),
    paymentStatus: zod_1.z
        .enum(["pending", "paid", "overdue", "inactive"], {
        message: "Invalid payment status. Must be 'pending', 'paid', 'overdue', or 'inactive'.",
    })
        .default("pending"),
    createdAt: zod_1.z.string().datetime().optional(), // ✅ Use string datetime if receiving timestamps as strings
});
exports.loginSchema = zod_1.z.object({
    email: zod_1.z
        .string()
        .email("Invalid email format.")
        .max(100, "Email must be at most 100 characters."),
    password: zod_1.z.string().min(6, "Password must be at least 6 characters."),
});
// Categories Schema
exports.categorySchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, "Category name is required.")
        .max(100, "Category name must be at most 100 characters."),
    description: zod_1.z.string().optional(),
});
// Products Schema
exports.productSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, "Product name is required.").max(255),
    categoryId: zod_1.z.number().int().positive("Category ID is required."),
    sku: zod_1.z.string().max(50).optional(),
    barcode: zod_1.z.string().max(50).optional(),
    imageUrl: zod_1.z.string().url("Invalid image URL").nullable().optional(),
    price: zod_1.z.number().min(0, "Price must be a positive number."),
    cost: zod_1.z.number().min(0, "Cost must be a positive number."),
    stockQuantity: zod_1.z.number().int().min(0).default(0),
    minStockLevel: zod_1.z.number().int().min(0).default(0),
    expiryDate: zod_1.z.preprocess((arg) => {
        if (typeof arg === "string" ||
            typeof arg === "number" ||
            arg instanceof Date) {
            const date = new Date(arg);
            return isNaN(date.getTime()) ? undefined : date;
        }
        return undefined;
    }, zod_1.z.date().optional()),
    createdAt: zod_1.z.date().optional(),
});
// Sales Schema
exports.saleSchema = zod_1.z.object({
    totalAmount: zod_1.z.number().min(0, "Total amount must be a positive number."),
    paymentMethod: zod_1.z.enum(["cash", "card", "mobile_money"]),
    items: zod_1.z
        .array(zod_1.z.object({
        productId: zod_1.z.number().int().positive("Product ID must be a positive integer."),
        quantity: zod_1.z.number().int().min(1, "Quantity must be at least 1."),
        price: zod_1.z.number().min(0, "Price must be a positive number."),
    }))
        .min(1, "At least one product must be included."),
});
// Stock Adjustments Schema
exports.stockAdjustmentSchema = zod_1.z.object({
    productId: zod_1.z
        .number()
        .int()
        .positive("Product ID must be a positive integer."),
    quantityChange: zod_1.z
        .number()
        .int()
        .refine((val) => val !== 0, {
        message: "Quantity change cannot be zero.",
    }),
    reason: zod_1.z.string().min(1, "Reason is required."),
    createdAt: zod_1.z.date().optional(),
});
// Receipts Schema
exports.receiptSchema = zod_1.z.object({
    saleId: zod_1.z.number().int().positive("Sale ID must be a positive integer."),
    receiptUrl: zod_1.z.string().url("Invalid receipt URL format."),
});
exports.supplierSchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, "Supplier name is required.")
        .max(255, "Supplier name must be at most 255 characters."),
    contactPerson: zod_1.z
        .string()
        .max(100, "Contact person must be at most 100 characters.")
        .optional(),
    phone: zod_1.z
        .string()
        .min(5, "Phone number must be at least 5 digits.")
        .max(20, "Phone number must be at most 20 digits."),
    email: zod_1.z
        .string()
        .max(100, "Email must be at most 100 characters.")
        .email("Invalid email format.")
        .optional()
        .or(zod_1.z.literal("")), // Allow empty string
    address: zod_1.z.string().optional(),
});
exports.purchaseSchema = zod_1.z.object({
    id: zod_1.z.number().int().positive().optional(),
    supplierId: zod_1.z.number().int().positive().nullable().optional(),
    productId: zod_1.z.number().int().positive(),
    quantity: zod_1.z.number().int().positive(),
    costPrice: zod_1.z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid cost price format"),
    totalCost: zod_1.z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid total cost format"),
    purchaseDate: zod_1.z.date().optional(),
    purchasedBy: zod_1.z.number().int().positive().nullable().optional(),
    createdBy: zod_1.z.number().int().positive().nullable().optional(),
});
// Expense Categories Schema
exports.expenseCategorySchema = zod_1.z.object({
    name: zod_1.z
        .string()
        .min(1, "Category name is required.")
        .max(100, "Category name must be at most 100 characters."),
    description: zod_1.z.string().optional(),
    color: zod_1.z
        .string()
        .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color code.")
        .default("#6B7280"),
    isDefault: zod_1.z.boolean().default(false),
});
// Expenses Schema
exports.expenseSchema = zod_1.z.object({
    title: zod_1.z
        .string()
        .min(1, "Expense title is required.")
        .max(255, "Title must be at most 255 characters."),
    description: zod_1.z.string().optional(),
    amount: zod_1.z.number().min(0.01, "Amount must be greater than 0."),
    categoryId: zod_1.z.number().int().positive().optional(),
    expenseDate: zod_1.z.preprocess((arg) => {
        if (typeof arg === "string" || typeof arg === "number" || arg instanceof Date) {
            const date = new Date(arg);
            return isNaN(date.getTime()) ? new Date() : date;
        }
        return new Date();
    }, zod_1.z.date()),
    paymentMethod: zod_1.z.enum(["cash", "card", "mobile_money", "bank_transfer", "cheque"]),
    receiptUrl: zod_1.z.string().url().optional().or(zod_1.z.literal("")),
    vendor: zod_1.z.string().max(255).optional(),
    isRecurring: zod_1.z.boolean().default(false),
    recurringFrequency: zod_1.z.enum(["daily", "weekly", "monthly", "quarterly", "yearly"]).optional(),
    tags: zod_1.z.string().optional(), // JSON string of tags
    storeId: zod_1.z.number().int().positive().optional(),
});
