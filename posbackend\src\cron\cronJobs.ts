import cron from "node-cron";
import { markOverduePayments, checkSubscriptionRenewals, fixIncorrectSubscriptionPeriods } from "../utils/updatePaymentStatus";

export const startCronJobs = () => {
  // Run every day at midnight (00:00 UTC) - Check for overdue payments
  cron.schedule("0 0 * * *", async () => {
    console.log("🕛 Running daily subscription management tasks...");

    try {
      // First fix any incorrect subscription periods
      console.log("🔧 Fixing incorrect subscription periods...");
      await fixIncorrectSubscriptionPeriods();

      // Then check for subscription renewals (users who need to renew)
      console.log("🔄 Checking subscription renewals...");
      await checkSubscriptionRenewals();

      // Finally mark overdue payments
      console.log("⏰ Checking for overdue payments...");
      await markOverduePayments();

      console.log("✅ Daily subscription management completed successfully");
    } catch (error) {
      console.error("❌ Error in daily subscription management:", error);
    }
  });

  // Run every hour during business hours (8 AM to 8 PM UTC) for more frequent checks
  cron.schedule("0 8-20 * * *", async () => {
    console.log("🕐 Running hourly subscription status check...");

    try {
      await checkSubscriptionRenewals();
      console.log("✅ Hourly subscription check completed");
    } catch (error) {
      console.error("❌ Error in hourly subscription check:", error);
    }
  });

  console.log("✅ Cron jobs initialized - Daily and hourly subscription management active.");
};
