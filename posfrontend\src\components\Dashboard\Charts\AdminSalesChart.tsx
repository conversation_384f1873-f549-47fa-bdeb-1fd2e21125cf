"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface AdminSalesChartProps {
  sales: any[];
}

const AdminSalesChart: React.FC<AdminSalesChartProps> = ({ sales }) => {
  const chartData = useMemo(() => {
    console.log('📊 Sales Chart - Sales found:', sales.length);

    // Create last 30 days array
    const last30Days: Array<{
      dayKey: string;
      dayLabel: string;
      salesCount: number;
      revenue: number;
    }> = [];
    for (let i = 29; i >= 0; i--) {
      const day = dayjs().subtract(i, 'day');
      last30Days.push({
        dayKey: day.format('YYYY-MM-DD'),
        dayLabel: day.format('MMM DD'),
        salesCount: 0,
        revenue: 0
      });
    }

    // Group sales by day
    sales.forEach(sale => {
      if (sale.transactionDate) {
        const saleDay = dayjs(sale.transactionDate).format('YYYY-MM-DD');
        const dayData = last30Days.find(d => d.dayKey === saleDay);
        if (dayData) {
          dayData.salesCount++;
          dayData.revenue += parseFloat(sale.totalAmount || '0');
        }
      }
    });

    const salesData = last30Days.map(day => ({
      x: day.dayLabel,
      y: day.salesCount
    }));

    const revenueData = last30Days.map(day => ({
      x: day.dayLabel,
      y: day.revenue
    }));

    console.log('📈 Sales chart data:', { salesData: salesData.slice(-7), revenueData: revenueData.slice(-7) });
    return { salesData, revenueData };
  }, [sales]);

  const options: ApexOptions = {
    chart: {
      type: 'line',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#5750F1', '#0ABEF9'],
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '14px',
      fontFamily: 'inherit',
      markers: {
        size: 8,
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: [
        {
          formatter: function(value) {
            return `${value} sales`;
          }
        },
        {
          formatter: function(value) {
            return `₵${value.toFixed(2)}`;
          }
        }
      ]
    },
    xaxis: {
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '12px',
        },
        rotate: -45,
      }
    },
    yaxis: [
      {
        title: {
          text: 'Number of Sales',
          style: {
            fontSize: '12px',
          }
        },
        labels: {
          formatter: function(value) {
            return Math.round(value).toString();
          },
          style: {
            fontSize: '12px',
          }
        }
      },
      {
        opposite: true,
        title: {
          text: 'Revenue (₵)',
          style: {
            fontSize: '12px',
          }
        },
        labels: {
          formatter: function(value) {
            return `₵${value.toFixed(0)}`;
          },
          style: {
            fontSize: '12px',
          }
        }
      }
    ]
  };

  const series = [
    {
      name: 'Sales Count',
      type: 'line',
      data: chartData.salesData
    },
    {
      name: 'Revenue',
      type: 'line',
      yAxisIndex: 1,
      data: chartData.revenueData
    }
  ];

  if (chartData.salesData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No sales data available</p>
          <p className="text-sm">Chart will appear when sales are made</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="line"
        height={250}
      />
    </div>
  );
};

export default AdminSalesChart;
