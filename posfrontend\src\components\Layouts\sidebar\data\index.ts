import * as Icons from "../icons";
import { UserRole } from "@/types/user";
import { NavItem, NavSection } from "../types";

// Common menu items for all roles
const COMMON_MENU_ITEMS: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Icons.HomeIcon,
    items: [],
  },
  {
    title: "Profile",
    url: "/dashboard/profile",
    icon: Icons.User,
    items: [],
  },
];

// POS menu item
const POS_MENU_ITEM: NavItem = {
  title: "POS",
  url: "/dashboard/pos",
  icon: Icons.Alphabet,
  items: [],
};

const INVENTORY_MENU_ITEM: NavItem = {
  title: "Inventory Menu",
  icon: Icons.CategoryIcon,
  items: [
    {
      title: "Categories",
      url: "/dashboard/categories",
    },
    {
      title: "Products",
      url: "/dashboard/products",
    },
    {
      title: "Suppliers",
      url: "/dashboard/suppliers",
    },
    {
      title: "Purchases",
  url: "/dashboard/purchases",
    },
  ],
};

// Products menu item
// const PRODUCTS_MENU_ITEM: NavItem = {
//   title: "Products",
//   url: "/dashboard/products",
//   icon: Icons.ShoppingIcon,
//   items: [],
// };

// Categories menu item
// const CATEGORIES_MENU_ITEM: NavItem = {
//   title: "Categories",
//   url: "/dashboard/categories",
//   icon: Icons.CategoryIcon,
//   items: [],
// };

// Suppliers menu item
// const SUPPLIERS_MENU_ITEM: NavItem = {
//   title: "Suppliers",
//   url: "/dashboard/suppliers",
//   icon: Icons.User,
//   items: [],
// };

// Stores menu item
const STORES_MENU_ITEM: NavItem = {
  title: "Stores",
  url: "/dashboard/stores",
  icon: Icons.ShopIcon,
  items: [],
};

// Purchases menu item
// const PURCHASES_MENU_ITEM: NavItem = {
//   title: "Purchases",
//   url: "/dashboard/purchases",
//   icon: Icons.ShoppingBagIcon,
//   items: [],
// };

// Sales menu item
const SALES_MENU_ITEM: NavItem = {
  title: "Sales",
  url: "/dashboard/sales",
  icon: Icons.ShoppingCartIcon,
  items: [],
};

// Receipts menu item
const RECEIPTS_MENU_ITEM: NavItem = {
  title: "Receipts",
  url: "/dashboard/receipts",
  icon: Icons.PrinterIcon,
  items: [],
};

// Reports menu item
const EXP_MENU_ITEM: NavItem = {
  title: "Expenses Menu",
  icon: Icons.ExpenseIcon,
  items: [
    {
      title: "Expense Categories",
      url: "/dashboard/expense-categories",
    },
    {
      title: "Expenses",
      url: "/dashboard/expenses",
    },
  ],
};

// Users menu item
const USERS_MENU_ITEM: NavItem = {
  title: "Users",
  url: "/dashboard/users",
  icon: Icons.User,
  items: [],
};

// Reports menu item
const REPORTS_MENU_ITEM: NavItem = {
  title: "Reports",
  icon: Icons.ReportsIcon,
  items: [
    {
      title: "Sales Reports",
      url: "/dashboard/reports/sales",
    },
    {
      title: "Inventory Reports",
      url: "/dashboard/reports/inventory",
    },
  ],
};

// Settings menu item
const SETTINGS_MENU_ITEM: NavItem = {
  title: "Settings",
  url: "/dashboard/settings",
  icon: Icons.Calendar,
  items: [],
};

// Payment menu item
// const PAYMENT_MENU_ITEM: NavItem = {
//   title: "Payment",
//   url: "/payment",
//   icon: Icons.PaymentIcon,
//   items: [],
// };

// Superadmin-only menu items
const SUPERADMIN_MENU_ITEMS = [
  {
    title: "System",
    icon: Icons.Calendar,
    items: [
      {
        title: "Configuration",
        url: "/dashboard/system/configuration",
      },
      {
        title: "Logs",
        url: "/dashboard/system/logs",
      },
    ],
  },
];

// Get menu items based on user role
export const getMenuItemsByRole = (role: UserRole): NavSection[] => {
  // Start with common menu items
  let menuItems: NavItem[] = [...COMMON_MENU_ITEMS];

  // Add role-specific menu items in a logical order
  if (role === "superadmin") {
    menuItems = [...menuItems, USERS_MENU_ITEM, STORES_MENU_ITEM];
  } else if (role === "admin") {
    menuItems = [
      ...menuItems,
      INVENTORY_MENU_ITEM,
      SALES_MENU_ITEM,
      RECEIPTS_MENU_ITEM,
      // EXPENSE_CATEGORIES_MENU_ITEM,
      // EXPENSES_MENU_ITEM,
      EXP_MENU_ITEM,
      REPORTS_MENU_ITEM,
      USERS_MENU_ITEM,
      // PAYMENT_MENU_ITEM,
    ];
  } else if (role === "cashier") {
    menuItems = [
      ...menuItems,
      // INVENTORY_MENU_ITEM,
      SALES_MENU_ITEM,
      RECEIPTS_MENU_ITEM,
    ];
  }

  return [
    {
      label: "MAIN MENU",
      items: menuItems,
    },
  ];
};

// Default export for backward compatibility
export const NAV_DATA = [
  {
    label: "MAIN MENU",
    items: [
      ...COMMON_MENU_ITEMS,
      INVENTORY_MENU_ITEM,
      SALES_MENU_ITEM,
      RECEIPTS_MENU_ITEM,

      // EXPENSE_CATEGORIES_MENU_ITEM,
      // EXPENSES_MENU_ITEM,
      EXP_MENU_ITEM,
      USERS_MENU_ITEM,
      // PAYMENT_MENU_ITEM,
    ],
  },
];
