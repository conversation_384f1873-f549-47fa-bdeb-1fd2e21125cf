"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleExpenseCategoryRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const expenseCategoryService_1 = require("../services/expenseCategoryService");
const modeValidator_1 = require("../utils/modeValidator");
const handleExpenseCategoryRequest = async (req, res) => {
    const { mode, categoryId, page, limit, search, ...data } = req.body;
    const requester = req.user;
    const validModes = ["createnew", "retrieve", "update", "delete"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            case "createnew": {
                if (!data.name) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Category name is required.");
                }
                const newCategory = await (0, expenseCategoryService_1.createExpenseCategory)(requester, {
                    name: data.name,
                    description: data.description,
                    color: data.color || "#6B7280",
                    isDefault: false, // User-created categories are never default
                });
                return (0, responseHelper_1.sendResponse)(res, 201, true, "Expense category created successfully.", newCategory);
            }
            case "retrieve": {
                if (categoryId) {
                    const category = await (0, expenseCategoryService_1.getExpenseCategoryById)(requester, Number(categoryId));
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Expense category retrieved successfully.", category);
                }
                else {
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 50;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    const categoriesData = await (0, expenseCategoryService_1.getAllExpenseCategories)(requester, pageNum, limitNum, search || '');
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Expense categories retrieved successfully.", categoriesData);
                }
            }
            case "update": {
                if (!categoryId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Category ID is required for updating.");
                }
                const updateData = {};
                if (data.name)
                    updateData.name = data.name;
                if (data.description !== undefined)
                    updateData.description = data.description;
                if (data.color)
                    updateData.color = data.color;
                const updatedCategory = await (0, expenseCategoryService_1.updateExpenseCategoryById)(requester, Number(categoryId), updateData);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Expense category updated successfully.", updatedCategory);
            }
            case "delete": {
                const { categoryIds } = req.body;
                if (!categoryId && !categoryIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Category ID(s) are required for deletion.");
                }
                const idsToDelete = categoryIds
                    ? (Array.isArray(categoryIds) ? categoryIds : [categoryIds])
                    : [Number(categoryId)];
                const result = await (0, expenseCategoryService_1.deleteExpenseCategoryById)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} expense categories deleted successfully.`
                    : "Expense category deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid mode.");
        }
    }
    catch (error) {
        console.error("Error in expense category controller:", error);
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Error processing expense category request");
    }
};
exports.handleExpenseCategoryRequest = handleExpenseCategoryRequest;
