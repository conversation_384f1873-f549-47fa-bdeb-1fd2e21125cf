"use client";

import { useDeleteSaleMutation } from "@/reduxRTK/services/salesApi";
import { showMessage } from "@/utils/showMessage";

export const useSaleDelete = (onSuccess?: () => void) => {
  // Delete sale mutation
  const [deleteSale, { isLoading: isDeleting }] = useDeleteSaleMutation();

  // Handle delete sale
  const handleDeleteSale = async (saleId: number) => {
    try {
      const response = await deleteSale(saleId).unwrap();
      if (response.success) {
        showMessage("success", "Sale deleted successfully");
        if (onSuccess) onSuccess();
        return true;
      } else {
        showMessage("error", response.message || "Failed to delete sale");
        return false;
      }
    } catch (error: any) {
      showMessage("error", error.data?.message || "An error occurred while deleting the sale");
      return false;
    }
  };

  return {
    deleteSale: handleDeleteSale,
    isDeleting,
  };
};
