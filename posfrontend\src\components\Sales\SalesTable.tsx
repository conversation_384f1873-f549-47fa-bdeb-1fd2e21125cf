"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EyeOutlined,
  DeleteOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  CalendarOutlined,
  DeleteFilled,
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Sale } from "@/reduxRTK/services/salesApi";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import dayjs from "dayjs";
import "./sales-panels.css";

interface SalesTableProps {
  sales: Sale[];
  loading: boolean;
  onViewSale: (sale: Sale) => void;
  onDelete: (saleId: number) => void;
  onBulkDelete?: (saleIds: number[]) => void;
  isMobile?: boolean;
}

const SalesTable: React.FC<SalesTableProps> = ({
  sales,
  loading,
  onViewSale,
  onDelete,
  onBulkDelete,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const { user } = useSelector((state: RootState) => state.auth);

  // State for selected sales
  const [selectedSales, setSelectedSales] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Check if user can delete sales
  const canDeleteSale = user?.role === "admin" || user?.role === "superadmin";

  // The backend already filters sales based on user role and relationships
  const filteredSales = sales;

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all sales that the user can delete
      const selectableSaleIds = canDeleteSale
        ? filteredSales.map((sale) => sale.id)
        : [];
      setSelectedSales(selectableSaleIds);
    } else {
      // Deselect all sales
      setSelectedSales([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (saleId: number, checked: boolean) => {
    if (checked) {
      setSelectedSales((prev) => [...prev, saleId]);
    } else {
      setSelectedSales((prev) => prev.filter((id) => id !== saleId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedSales.length > 0 && onBulkDelete) {
      onBulkDelete(selectedSales);
      setSelectedSales([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: "No sales selected",
        description: "Please select at least one sale to delete.",
      });
    }
  };

  // Format currency for display
  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("en-GH", {
      style: "currency",
      currency: "GHS",
    }).format(parseFloat(amount));
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY HH:mm");
  };

  // Handle delete sale
  const handleDeleteSale = (saleId: number) => {
    onDelete(saleId);
  };

  // Check if we have any sales after filtering
  const hasSales = filteredSales.length > 0;

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when sales are selected */}
      {selectedSales.length > 0 && canDeleteSale && (
        <div className="flex items-center justify-between border-b bg-gray-100 p-2">
          <span className="text-sm font-medium text-gray-700">
            {selectedSales.length}{" "}
            {selectedSales.length === 1 ? "sale" : "sales"} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {!hasSales && sales.length > 0 && (
        <div className="flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800">
          <p>You don&quot;t have access to view these sales.</p>
          <p className="mt-2 text-sm text-gray-500">
            You can only view sales created by you or your team.
          </p>
        </div>
      )}

      {hasSales && (
        isMobile ? (
          // Mobile: Use CSS Grid
          <ResponsiveTableGrid
            columns={canDeleteSale ? "50px 80px 120px 120px 120px 150px" : "80px 120px 120px 120px 150px"}
            minWidth={canDeleteSale ? "700px" : "650px"}
          >
            {/* Mobile Headers */}
            {canDeleteSale && (
              <TableHeader className="text-center">
                <Checkbox
                  checked={selectAll}
                  onChange={handleSelectAllChange}
                  disabled={filteredSales.length === 0}
                />
              </TableHeader>
            )}
            <TableHeader>
              <span className="flex items-center">
                <ShoppingCartOutlined className="mr-1" />
                ID
              </span>
            </TableHeader>
            <TableHeader>
              <span className="flex items-center">
                <DollarOutlined className="mr-1" />
                Amount
              </span>
            </TableHeader>
            <TableHeader>
              Payment
            </TableHeader>
            <TableHeader>
              <span className="flex items-center">
                <CalendarOutlined className="mr-1" />
                Date
              </span>
            </TableHeader>
            <TableHeader className="text-right">
              Actions
            </TableHeader>

            {/* Mobile Rows */}
            {filteredSales.map((sale) => (
              <TableRow
                key={sale.id}
                selected={selectedSales.includes(sale.id)}
              >
                {canDeleteSale && (
                  <TableCell className="text-center">
                    <Checkbox
                      checked={selectedSales.includes(sale.id)}
                      onChange={(e) =>
                        handleCheckboxChange(sale.id, e.target.checked)
                      }
                    />
                  </TableCell>
                )}
                <TableCell>
                  <span className="font-medium">#{sale.id}</span>
                </TableCell>
                <TableCell>
                  <span className="font-medium text-green-600">
                    {formatCurrency(sale.totalAmount)}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                      sale.paymentMethod === "cash"
                        ? "bg-green-800 text-green-100"
                        : sale.paymentMethod === "card"
                          ? "bg-blue-800 text-blue-100"
                          : "bg-purple-800 text-purple-100"
                    }`}
                  >
                    {sale.paymentMethod.replace("_", " ").toUpperCase()}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="text-sm">
                    {dayjs(sale.transactionDate).format("MMM D, YYYY")}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-1">
                    <Tooltip title="View Details">
                      <Button
                        icon={<EyeOutlined />}
                        onClick={() => onViewSale(sale)}
                        type="text"
                        className="view-button text-green-500 hover:text-green-400"
                        size="small"
                      />
                    </Tooltip>
                    {(user?.role === "admin" || user?.role === "superadmin") && (
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteSale(sale.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </ResponsiveTableGrid>
        ) : (
          // Desktop: Use traditional HTML table
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Checkbox Column */}
                  {canDeleteSale && (
                    <th scope="col" className="w-10 px-3 py-3 text-center">
                      <Checkbox
                        checked={selectAll}
                        onChange={handleSelectAllChange}
                        disabled={filteredSales.length === 0}
                      />
                    </th>
                  )}

                  {/* ID Column - Always visible */}
                  <th
                    scope="col"
                    className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700"
                  >
                    <span className="flex items-center">
                      <ShoppingCartOutlined className="mr-1" />
                      ID
                    </span>
                  </th>

                  {/* Total Amount Column */}
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700"
                  >
                    <span className="flex items-center">
                      <DollarOutlined className="mr-1" />
                      Total Amount
                    </span>
                  </th>

                  {/* Payment Method Column */}
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700"
                  >
                    <span className="flex items-center">
                      <DollarOutlined className="mr-1" />
                      Payment Method
                    </span>
                  </th>

                  {/* Date Column */}
                  <th
                    scope="col"
                    className="px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700"
                  >
                    <span className="flex items-center">
                      <CalendarOutlined className="mr-1" />
                      Date
                    </span>
                  </th>

                  {/* Actions Column - Always visible */}
                  <th
                    scope="col"
                    className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {filteredSales.map((sale) => (
                  <tr
                    key={sale.id}
                    className={selectedSales.includes(sale.id) ? "bg-blue-50" : ""}
                  >
                    {/* Checkbox Column */}
                    {canDeleteSale && (
                      <td className="whitespace-nowrap px-3 py-4 text-center">
                        <Checkbox
                          checked={selectedSales.includes(sale.id)}
                          onChange={(e) =>
                            handleCheckboxChange(sale.id, e.target.checked)
                          }
                        />
                      </td>
                    )}

                    {/* ID Column - Always visible */}
                    <td className="sticky left-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-gray-800">
                      #{sale.id}
                    </td>

                    {/* Total Amount Column */}
                    <td className="whitespace-nowrap px-3 py-4 text-gray-800">
                      {formatCurrency(sale.totalAmount)}
                    </td>

                    {/* Payment Method Column */}
                    <td className="whitespace-nowrap px-3 py-4">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          sale.paymentMethod === "cash"
                            ? "bg-green-800 text-green-100"
                            : sale.paymentMethod === "card"
                              ? "bg-blue-800 text-blue-100"
                              : "bg-purple-800 text-purple-100"
                        }`}
                      >
                        {sale.paymentMethod.replace("_", " ").toUpperCase()}
                      </span>
                    </td>

                    {/* Date Column */}
                    <td className="whitespace-nowrap px-3 py-4 text-gray-800">
                      {formatDate(sale.transactionDate)}
                    </td>

                    {/* Actions Column - Always visible */}
                    <td className="sticky right-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-1">
                        <Tooltip title="View Details">
                          <Button
                            icon={<EyeOutlined />}
                            onClick={() => onViewSale(sale)}
                            type="text"
                            className="view-button text-green-500"
                            size="middle"
                          />
                        </Tooltip>
                        {(user?.role === "admin" ||
                          user?.role === "superadmin") && (
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => handleDeleteSale(sale.id)}
                              type="text"
                              className="delete-button text-red-500"
                              size="middle"
                            />
                          </Tooltip>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      )}
    </div>
  );
};

export default SalesTable;
