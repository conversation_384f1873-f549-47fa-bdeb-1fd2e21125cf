(()=>{var e={};e.id=785,e.ids=[785],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},17428:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=a(70260),r=a(28203),o=a(25155),i=a.n(o),n=a(67292),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let d=["",{children:["dashboard",{children:["sales",{children:["offline-pos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,76722)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\offline-pos\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\offline-pos\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/sales/offline-pos/page",pathname:"/dashboard/sales/offline-pos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76525:(e,t,a)=>{Promise.resolve().then(a.bind(a,76722))},493:(e,t,a)=>{Promise.resolve().then(a.bind(a,73907))},73907:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(45512),r=a(58009),o=a(45103),i=a(60636),n=a(37246),l=a(14396),d=a(2274),c=a(10499),u=a(49792),p=a(66785),h=a(46211);let m=()=>{let{user:e}=(0,i.A)(),[t,a]=(0,r.useState)(!1),[s,o]=(0,r.useState)([]),[m,x]=(0,r.useState)(null),[f,y]=(0,r.useState)({isOffline:!1,pendingSales:0,failedSales:0,cachedProducts:0}),{data:g,isLoading:b}=(0,d.r3)({page:1,limit:1e3},{skip:t,refetchOnMountOrArgChange:!1,refetchOnReconnect:!1}),[v]=(0,c.E$)(),{data:w}=(0,h.Rv)(e?.id||0,{skip:!e?.id}),j=(0,r.useCallback)(async()=>{try{let e=await n.V.getCachedProducts();o(e)}catch(e){console.error("Failed to load cached products:",e)}},[]),k=(0,r.useCallback)(async()=>{try{let e=await n.V.getStorageStats(),t=await l.zy.getSyncStatus();y({isOffline:!navigator.onLine,pendingSales:e.pendingSales,failedSales:e.failedSales,cachedProducts:e.cachedProducts,lastSync:t.lastSyncAttempt}),console.log("✅ Offline status updated:",{isOffline:!navigator.onLine,cachedProducts:e.cachedProducts})}catch(e){console.error("Failed to update offline status:",e)}},[]);(0,r.useEffect)(()=>{(async()=>{try{await n.V.init(),await k(),await j(),console.log("✅ Offline POS initialized")}catch(e){console.error("Failed to initialize offline POS:",e)}})();let e=setInterval(()=>{navigator.onLine&&(k(),j())},3e5);return()=>clearInterval(e)},[j,k]),(0,r.useEffect)(()=>{let e=()=>{let e=!navigator.onLine;a(e),k(),console.log(`Network status changed: ${e?"offline":"online"}`)};return e(),window.addEventListener("online",e),window.addEventListener("offline",e),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",e)}},[k]),(0,r.useEffect)(()=>{(async()=>{if(g?.data?.products&&!t)try{let e=g.data.products.map(e=>({id:e.id,name:e.name,price:e.price,stockQuantity:e.stockQuantity,sku:e.sku,barcode:e.barcode,imageUrl:e.imageUrl||void 0,categoryId:e.categoryId,lastUpdated:new Date}));await n.V.cacheProducts(e),await j()}catch(e){console.error("Failed to cache products:",e)}})()},[g,t,j]),(0,r.useEffect)(()=>{(async()=>{if(w?.data&&!t)try{await n.V.cacheStore(w.data),x(w.data)}catch(e){console.error("Failed to cache store details:",e)}})()},[w,t]);let N=(0,r.useCallback)(()=>t||!g?.data?.products?s:g.data.products.map(e=>({id:e.id,name:e.name,price:e.price,stockQuantity:e.stockQuantity,sku:e.sku,barcode:e.barcode,imageUrl:e.imageUrl||void 0,categoryId:e.categoryId,lastUpdated:new Date})),[t,g,s]),P=(0,r.useCallback)(async e=>{if(t)return await n.V.searchProducts(e);let a=N(),s=e.toLowerCase();return a.filter(e=>e.name.toLowerCase().includes(s)||e.sku&&e.sku.toLowerCase().includes(s)||e.barcode&&e.barcode.toLowerCase().includes(s))},[t,N]),S=(0,r.useCallback)(async e=>t?await n.V.getProductByBarcode(e):N().find(t=>t.barcode===e||t.sku===e),[t,N]),C=(0,r.useCallback)(async(a,s,r)=>{if(!e)throw Error("User not authenticated");let o=a.reduce((e,t)=>e+t.price*t.quantity,0),i="";try{let e=(0,p.jY)({id:Date.now(),totalAmount:o,paymentMethod:s,transactionDate:new Date().toISOString(),items:a.map(e=>({productName:e.productName,quantity:e.quantity,price:e.price}))},{name:"POS System",address:"",city:"",country:"Ghana"});i=await (0,p.fS)(e)}catch(e){console.warn("Failed to generate receipt:",e)}if(t)try{let t=await n.V.saveOfflineSale({totalAmount:o,paymentMethod:s,items:a,receiptUrl:i,storeId:r,createdBy:e.id});return await k(),(0,u.r)("success","Sale saved offline - will sync when connection returns"),{success:!0,saleId:t,isOffline:!0}}catch(e){throw console.error("Failed to save offline sale:",e),Error("Failed to save sale offline")}else try{let e={totalAmount:o,paymentMethod:s,items:a.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),receiptUrl:i,storeId:r},t=await v(e).unwrap();if(t.success)return(0,u.r)("success","Sale created successfully"),{success:!0,saleId:t.data?.sales?.[0]?.saleId?.toString()||"unknown"};throw Error(t.message||"Sale creation failed")}catch(t){console.error("Online sale failed:",t);try{let t=await n.V.saveOfflineSale({totalAmount:o,paymentMethod:s,items:a,receiptUrl:i,storeId:r,createdBy:e.id});return await k(),(0,u.r)("warning","Online sale failed - saved offline instead"),{success:!0,saleId:t,isOffline:!0}}catch(e){throw console.error("Offline fallback failed:",e),Error("Both online and offline sale creation failed")}}},[e,t,v,k]),A=(0,r.useCallback)(async()=>await n.V.getOfflineSales(),[]),D=(0,r.useCallback)(async()=>{await l.zy.forceSyncNow(),await k()},[k]),E=(0,r.useCallback)(()=>t||!w?.data?m:w.data,[t,w,m]);return{isOffline:t,offlineStatus:f,isLoading:b&&0===s.length,products:N(),searchProducts:P,findProductByBarcode:S,createSale:C,getOfflineSales:A,forceSyncNow:D,updateOfflineStatus:k,getStore:E}};var x={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var y=(e,t)=>{let a=(0,r.forwardRef)(({color:a="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:i,children:n,...l},d)=>(0,r.createElement)("svg",{ref:d,...x,width:s,height:s,stroke:a,strokeWidth:i?24*Number(o)/Number(s):o,className:`lucide lucide-${f(e)}`,...l},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...(Array.isArray(n)?n:[n])||[]]));return a.displayName=`${e}`,a};let g=y("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),b=y("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),v=y("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),w=y("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),j=()=>{let{isOffline:e,offlineStatus:t,isLoading:a,products:i,searchProducts:n,findProductByBarcode:l,createSale:d,forceSyncNow:c,getStore:p}=m(),[h,x]=(0,r.useState)([]),[f,y]=(0,r.useState)(""),[j,k]=(0,r.useState)(""),[N,P]=(0,r.useState)(!1),[S,C]=(0,r.useState)("cash"),A=(0,r.useMemo)(()=>f?i.filter(e=>e.name.toLowerCase().includes(f.toLowerCase())||e.sku&&e.sku.toLowerCase().includes(f.toLowerCase())||e.barcode&&e.barcode.toLowerCase().includes(f.toLowerCase())).slice(0,50):i.slice(0,50),[i,f]),D=e=>{if(e.stockQuantity<=0){(0,u.r)("warning","Product out of stock");return}x(t=>t.find(t=>t.productId===e.id)?t.map(t=>t.productId===e.id?{...t,quantity:t.quantity+1}:t):[...t,{productId:e.id,productName:e.name,quantity:1,price:Number(e.price)}])},E=(e,t)=>{t<=0?x(t=>t.filter(t=>t.productId!==e)):x(a=>a.map(a=>a.productId===e?{...a,quantity:t}:a))},O=e=>{x(t=>t.filter(t=>t.productId!==e))},L=async e=>{if(e.preventDefault(),j.trim())try{let e=await l(j.trim());e?(D(e),k(""),(0,u.r)("success",`Added ${e.name} to cart`)):(0,u.r)("error","Product not found")}catch(e){(0,u.r)("error","Failed to find product")}},_=(0,r.useMemo)(()=>h.reduce((e,t)=>e+t.price*t.quantity,0),[h]),$=_+0,I=async()=>{if(0===h.length){(0,u.r)("error","Cart is empty");return}P(!0);try{let e=await d(h,S);e.success&&(x([]),C("cash"),e.isOffline?(0,u.r)("success","Sale saved offline - will sync when online"):(0,u.r)("success","Sale completed successfully"))}catch(e){console.error("Sale failed:",e),(0,u.r)("error",e instanceof Error?e.message:"Sale failed")}finally{P(!1)}},T=p?p():null;return a?(0,s.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading POS system..."})]})}):(0,s.jsxs)("div",{className:"h-screen flex bg-gray-50",children:[e&&T&&(0,s.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 rounded shadow text-blue-900",children:[(0,s.jsx)("div",{className:"font-bold text-lg",children:T.name||"Store Name"}),T.address&&(0,s.jsx)("div",{className:"text-sm",children:T.address}),T.phone&&(0,s.jsxs)("div",{className:"text-sm",children:["Phone: ",T.phone]})]}),(0,s.jsxs)("div",{className:"w-1/3 bg-white border-r border-gray-200 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"POS System"}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:e?(0,s.jsxs)("div",{className:"flex items-center gap-1 text-orange-600",children:[(0,s.jsx)(g,{size:16}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Offline"})]}):(0,s.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(b,{size:16}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Online"})]})})]}),(t.pendingSales>0||t.failedSales>0)&&(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(v,{size:16,className:"text-yellow-600"}),(0,s.jsxs)("span",{className:"text-sm text-yellow-800",children:[t.pendingSales," pending, ",t.failedSales," failed"]})]}),(0,s.jsxs)("button",{onClick:c,className:"text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700",disabled:e,children:[(0,s.jsx)(w,{size:12,className:"inline mr-1"}),"Sync"]})]})}),(0,s.jsx)("form",{onSubmit:L,className:"mb-2",children:(0,s.jsx)("input",{type:"text",value:j,onChange:e=>k(e.target.value),placeholder:"Scan or enter barcode...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-700 mb-3",children:"Cart Items"}),0===h.length?(0,s.jsxs)("div",{className:"text-center text-gray-400 py-8",children:[(0,s.jsx)("p",{children:"Cart is empty"}),(0,s.jsx)("p",{className:"text-sm",children:"Add products to start a sale"})]}):(0,s.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 text-sm",children:e.productName}),(0,s.jsx)("button",{onClick:()=>O(e.productId),className:"text-red-500 hover:text-red-700 text-xs",children:"✕"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>E(e.productId,e.quantity-1),className:"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300",children:"-"}),(0,s.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,s.jsx)("button",{onClick:()=>E(e.productId,e.quantity+1),className:"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300",children:"+"})]}),(0,s.jsxs)("span",{className:"font-semibold text-blue-600",children:["₵",(e.price*e.quantity).toFixed(2)]})]})]},e.productId))})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Subtotal:"}),(0,s.jsxs)("span",{children:["₵",_.toFixed(2)]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Tax:"}),(0,s.jsxs)("span",{children:["₵","0.00"]})]}),(0,s.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t pt-2",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{className:"text-blue-600",children:["₵",$.toFixed(2)]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,s.jsxs)("select",{value:S,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"cash",children:"Cash"}),(0,s.jsx)("option",{value:"card",children:"Card"}),(0,s.jsx)("option",{value:"mobile_money",children:"Mobile Money"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("button",{onClick:I,disabled:0===h.length||N,className:"w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:N?"Processing...":`PAY ₵${$.toFixed(2)}`}),(0,s.jsx)("button",{onClick:()=>x([]),disabled:0===h.length,className:"w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 disabled:bg-gray-300",children:"Clear Cart"})]})]})]}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-200 bg-white",children:[(0,s.jsx)("input",{type:"text",value:f,onChange:e=>y(e.target.value),placeholder:"Search products...",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e?`${t.cachedProducts} cached products`:`${i.length} products available`})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,s.jsx)("div",{className:"grid grid-cols-4 xl:grid-cols-6 gap-3",children:A.map(e=>(0,s.jsxs)("button",{onClick:()=>D(e),disabled:e.stockQuantity<=0,className:`aspect-square rounded-lg p-3 flex flex-col items-center justify-center text-center transition-all ${e.stockQuantity<=0?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white border-2 border-gray-200 hover:border-blue-400 hover:shadow-md active:scale-95"}`,children:[e.imageUrl?(0,s.jsx)(o.default,{src:e.imageUrl,alt:e.name,width:48,height:48,className:"object-cover rounded mb-2"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded mb-2 flex items-center justify-center text-2xl",children:"\uD83D\uDECD️"}),(0,s.jsx)("span",{className:"font-medium text-xs text-gray-800 truncate w-full mb-1",children:e.name}),(0,s.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:["₵",Number(e.price).toFixed(2)]}),e.stockQuantity<=5&&e.stockQuantity>0&&(0,s.jsx)("span",{className:"text-xs text-orange-600",children:"Low stock"})]},e.id))})})]})]})},k=()=>(0,s.jsx)("div",{className:"h-screen",children:(0,s.jsx)(j,{})})},60636:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(92273),r=a(25510),o=a(97245),i=a(42211);let n=()=>{let e=(0,s.wA)(),{user:t,accessToken:a}=(0,s.d4)(e=>e.auth),n=(0,r._)(),{refetch:l}=(0,o.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!a,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let d=!!t&&!!a,c=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let s=await l();console.log("useAuth - Refetch result:",s);let r=s.data;if(r?.success&&r?.data){console.log("useAuth - API response data:",r.data);let s=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let o=r.data.phone||t.phone||"",n=r.data.createdAt||t.createdAt||"",l=r.data.lastPaymentDate||t.lastPaymentDate||void 0,d=r.data.nextPaymentDue||t.nextPaymentDue||null,c=r.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:r.data.phone,userPhone:t.phone,finalPhone:o,apiCreatedAt:r.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:n,apiLastPaymentDate:r.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:l,apiNextPaymentDue:r.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:d,apiCreatedBy:r.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:c});let u={...r.data,phone:o,createdAt:n,lastPaymentDate:l,nextPaymentDue:d,createdBy:c,paymentStatus:s};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",a?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,i.gV)({user:u,accessToken:a||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",r?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:a,isAuthenticated:d,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&n.needsPayment,paymentStatus:n,refreshUser:c}}},66785:(e,t,a)=>{"use strict";a.d(t,{fS:()=>o,iG:()=>n,jY:()=>i});let s="dutmiedgk",r="pos_receipts",o=async e=>{try{let t=document.createElement("div");t.innerHTML=e,t.style.width="350px",t.style.padding="20px",t.style.backgroundColor="white",t.style.color="#555555",t.style.fontFamily="Arial, sans-serif",t.style.position="absolute",t.style.left="-9999px",t.style.borderLeft="1px solid #000000",t.style.borderRight="1px solid #000000",t.style.borderTop="1px solid #000000",t.style.borderBottom="1px solid #000000",t.style.boxSizing="border-box",t.style.fontSize="14px",t.style.lineHeight="1.5",document.body.appendChild(t);let o=(await a.e(2835).then(a.bind(a,2835))).default,i=await o(t,{scale:3,backgroundColor:"white",logging:!1,width:350,height:t.offsetHeight,windowWidth:350,useCORS:!0});document.body.removeChild(t);let n=await new Promise(e=>{i.toBlob(t=>{e(t)},"image/png",.95)}),l=new FormData;l.append("file",n),l.append("upload_preset",r);let d=await fetch(`https://api.cloudinary.com/v1_1/${s}/image/upload`,{method:"POST",body:l}),c=await d.json();if(d.ok)return c.secure_url;throw console.error("Cloudinary upload failed:",c),Error(c.error?.message||"Failed to upload receipt image")}catch(e){throw console.error("Error generating receipt image:",e),e}},i=(e,t)=>{let a=new Date(e.transactionDate),s=a.toLocaleDateString(),r=a.toLocaleTimeString(),o=e.paymentMethod.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase());return`
  <div style="font-family: monospace; width: 280px; margin: 0 auto; padding: 10px; background-color: white; color: black; font-size: 12px; box-sizing: border-box;">

  <!-- Header and Title -->
  <div style="text-align: center; margin-bottom: 10px;">
    <div style="font-size: 18px; font-weight: bold;">${t.name||"POS System"}</div>
    <div style="font-size: 16px; font-weight: bold;">#INV-${e.id}-${new Date().getFullYear()}${(new Date().getMonth()+1).toString().padStart(2,"0")}${new Date().getDate().toString().padStart(2,"0")}</div>
    <div style="font-size: 12px; margin-top: 4px; line-height: 1.4;">
      ${t.address?`<div>${t.address}</div>`:""}
      ${t.city?`<div>${t.city}</div>`:""}
      ${t.country?`<div>${t.country}</div>`:""}
    </div>
  </div>

  <!-- Items Table -->
  <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin-bottom: 6px;">
    <thead>
      <tr>
        <th style="text-align: left; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Item</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Qty</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Unit</th>
        <th style="text-align: right; border-bottom: 1px solid #ccc; padding-bottom: 2px;">Total</th>
      </tr>
    </thead>
    <tbody>
      ${e.items.map((e,t)=>{let a="string"==typeof e.price?parseFloat(e.price):e.price,s=a*e.quantity;return`
          <tr>
            <td style="word-break: break-word; max-width: 90px; padding-right: 4px;">${t+1}. ${e.productName}</td>
            <td style="text-align: right;">${e.quantity}</td>
            <td style="text-align: right;">${a.toFixed(2)}</td>
            <td style="text-align: right;">${s.toFixed(2)}</td>
          </tr>
        `}).join("")}
    </tbody>
  </table>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Totals -->
  <div style="display: flex; justify-content: space-between; font-weight: bold;">
    <div>TOTAL</div>
    <div>GHS ${"string"==typeof e.totalAmount?parseFloat(e.totalAmount).toFixed(2):e.totalAmount.toFixed(2)}</div>
  </div>
  <div style="display: flex; justify-content: space-between; font-size: 11px; margin-top: 2px;">
    <div>TAX</div>
    <div>0.00</div>
  </div>

  <!-- Dotted Divider -->
  <div style="border-top: 1px dashed black; margin: 10px 0;"></div>

  <!-- Payment Info -->
  <div style="font-size: 11px; margin-bottom: 6px;">
    <div>Payment: ${o.toUpperCase()}</div>
    ${o.toLowerCase().includes("card")?"<div>**** **** **** ****</div>":""}
  </div>

  <!-- Date/Time -->
  <div style="display: flex; justify-content: space-between; font-size: 11px;">
    <div><strong>Time:</strong> ${r}</div>
    <div><strong>Date:</strong> ${s}</div>
  </div>

  <!-- Barcode -->
  <div style="text-align: center; margin: 12px 0;">
    <div style="font-size: 40px; letter-spacing: -1px; color: #555;">|||||||||||</div>
    <div style="font-size: 11px; margin-top: 4px;">${e.id}</div>
  </div>

  <!-- Footer -->
  <div style="text-align: center; font-size: 12px; margin-top: 8px;">
    THANK YOU!
  </div>
</div>

`},n=async e=>{try{let t=new FormData;t.append("file",e),t.append("upload_preset",r),t.append("folder","products");let a=await fetch(`https://api.cloudinary.com/v1_1/${s}/image/upload`,{method:"POST",body:t}),o=await a.json();if(a.ok)return o.secure_url;throw console.error("Cloudinary upload failed:",o),Error(o.error?.message||"Failed to upload product image")}catch(e){throw console.error("Error uploading product image:",e),e}}},76722:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\sales\\\\offline-pos\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\offline-pos\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,5482,106,4286],()=>a(17428));module.exports=s})();