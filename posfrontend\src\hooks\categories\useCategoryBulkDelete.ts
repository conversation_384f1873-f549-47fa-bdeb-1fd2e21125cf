import { useBulkDeleteCategoriesMutation } from "@/reduxRTK/services/categoryApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useCategoryBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting categories
  const [bulkDeleteCategories, { isLoading }] = useBulkDeleteCategoriesMutation();

  const deleteCategories = async (categoryIds: number[]) => {
    try {
      console.log("Bulk deleting categories with IDs:", categoryIds);
      
      const result = await bulkDeleteCategories(categoryIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete categories");
      }

      showMessage("success", `${categoryIds.length} categories deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete categories error:", error);
      showMessage("error", error.message || "Failed to delete categories");
      throw error;
    }
  };

  return {
    bulkDeleteCategories: deleteCategories,
    isDeleting: isLoading
  };
};
