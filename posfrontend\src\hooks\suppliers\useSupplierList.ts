"use client";

import { useState, useEffect } from "react";
import { useGetAllSuppliersQuery, Supplier } from "@/reduxRTK/services/supplierApi";
import { useDebounce } from "@/hooks/useDebounce";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

export const useSupplierList = (initialPage = 1, initialLimit = 10) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [searchTerm, setSearchTerm] = useState('');

  // Get current user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;
  const userId = user?.id;

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // Fetch suppliers with pagination and search
  const {
    data,
    error,
    isLoading,
    refetch
  } = useGetAllSuppliersQuery({
    page,
    limit,
    search: debouncedSearchTerm
  });

  // Extract suppliers and pagination info from the response
  // The backend already filters suppliers based on user role and permissions
  const suppliers: Supplier[] = data?.data?.suppliers || [];
  const total: number = data?.data?.total || 0;

  console.log("Suppliers from API:", suppliers);
  console.log("Total suppliers:", total);
  console.log("Current user ID:", userId);
  console.log("User role:", userRole);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to page 1 when changing limit
  };

  return {
    suppliers,
    total,
    page,
    limit,
    isLoading,
    error,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
    handleLimitChange
  };
};
