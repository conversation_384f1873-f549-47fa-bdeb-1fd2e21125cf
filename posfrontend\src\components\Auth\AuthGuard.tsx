"use client";

import { useEffect, useState, ReactNode } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter, usePathname } from "next/navigation";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface AuthGuardProps {
  children: ReactNode;
}

/**
 * AuthGuard component to protect routes that require authentication
 * Redirects to login page if user is not authenticated
 */
const AuthGuard = ({ children }: AuthGuardProps) => {
  const { user, accessToken } = useSelector((state: RootState) => state.auth);
  const router = useRouter();
  const pathname = usePathname();

  const [isClient, setIsClient] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Set isClient to true when component mounts on client
  useEffect(() => {
    setIsClient(true);
    console.log("AuthGuard mounted on client", {
      isAuthenticated: !!user && !!accessToken,
      userRole: user?.role,
      pathname
    });
  }, [accessToken, pathname, user]);

  useEffect(() => {
    // If no user or token, redirect to login
    if (isClient && (!user || !accessToken) && !isRedirecting) {
      console.log("AuthGuard - Redirecting to login", { pathname });
      setIsRedirecting(true);

      // Store the attempted URL for redirection after login
      sessionStorage.setItem("redirectUrl", pathname);

      // Add a small delay before redirecting to avoid race conditions
      setTimeout(() => {
        router.push("/");
      }, 100);
    }
  }, [user, accessToken, router, pathname, isClient, isRedirecting]);

  // Show loading spinner while checking authentication or before client-side hydration
  if (!isClient || !user || !accessToken) {
    return <LoadingSpinner fullScreen />;
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default AuthGuard;
