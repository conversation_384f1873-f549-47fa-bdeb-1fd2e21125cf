"use client";

import React, { useState } from "react";
import { Form, Input, Button } from "antd";
import { LockOutlined } from "@ant-design/icons";
import { useChangePasswordMutation } from "@/reduxRTK/services/authApi";
import { showMessage } from "@/utils/showMessage";

interface ChangePasswordFormProps {
  userId: string | number;
}

const ChangePasswordForm: React.FC<ChangePasswordFormProps> = ({ userId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [changePassword] = useChangePasswordMutation();

  const onFinish = async (values: any) => {
    if (values.newPassword !== values.confirmPassword) {
      showMessage("error", "New passwords do not match");
      return;
    }

    setLoading(true);
    try {
      // Log the password change attempt (without showing the actual passwords)
      console.log("ChangePasswordForm - Attempting to change password for user:", userId);
      console.log("ChangePasswordForm - Password lengths:", {
        currentPasswordLength: values.currentPassword?.length || 0,
        newPasswordLength: values.newPassword?.length || 0,
        confirmPasswordLength: values.confirmPassword?.length || 0
      });

      // Validate password inputs
      if (!values.currentPassword || !values.newPassword) {
        showMessage("error", "All password fields are required");
        setLoading(false);
        return;
      }

      // Call the API to change the password
      const response = await changePassword({
        userId,
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      });

      // Check if the response is successful
      if ('data' in response) {
        const result = response.data as any;
        console.log("ChangePasswordForm - Response:", result);

        if (result.success) {
          // Show success message from backend or fallback using toast
          showMessage("success", result.message || "Password changed successfully");

          // Reset form fields on success
          form.resetFields();
        } else {
          // Handle unsuccessful response with success: false
          console.error("ChangePasswordForm - Error:", result.message);
          showMessage("error", result.message || "Failed to change password");
        }
      } else if ('error' in response) {
        // Handle error response
        const errorData = response.error as any;
        console.error("ChangePasswordForm - Error response:", errorData);

        // Extract detailed error message if available
        if (errorData?.data?.message) {
          showMessage("error", errorData.data.message);
        } else if (errorData?.message) {
          showMessage("error", errorData.message);
        } else {
          showMessage("error", "Failed to change password. Please try again.");
        }
      }
    } catch (error: any) {
      console.error("Error changing password:", error);

      // Log detailed error information
      console.error("ChangePasswordForm - Detailed error:", {
        message: error.message,
        data: error.data,
        status: error.status,
        stack: error.stack
      });

      // Extract error message from the error response if available
      const errorMessage = error.data?.message ||
                          error.message ||
                          "Failed to change password. Please check your current password and try again.";

      showMessage("error", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="text-gray-800"
    >
      <Form.Item
        name="currentPassword"
        label={<span className="text-gray-700">Current Password</span>}
        rules={[
          { required: true, message: "Please enter your current password" },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="text-gray-500" />}
          className="bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"
          placeholder="Enter your current password"
        />
      </Form.Item>

      <Form.Item
        name="newPassword"
        label={<span className="text-gray-700">New Password</span>}
        rules={[
          { required: true, message: "Please enter your new password" },
          { min: 8, message: "Password must be at least 8 characters" },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
            message: "Password must contain uppercase, lowercase, number and special character",
          },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="text-gray-500" />}
          className="bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"
          placeholder="Enter your new password"
        />
      </Form.Item>

      <Form.Item
        name="confirmPassword"
        label={<span className="text-gray-700">Confirm New Password</span>}
        rules={[
          { required: true, message: "Please confirm your new password" },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue("newPassword") === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error("The two passwords do not match"));
            },
          }),
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="text-gray-500" />}
          className="bg-white border-gray-300 text-gray-800 hover:border-blue-400 focus:border-blue-400"
          placeholder="Confirm your new password"
        />
      </Form.Item>

      <Form.Item className="mt-6">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          className="bg-blue-600 hover:bg-blue-700 border-none text-white"
        >
          Change Password
        </Button>
      </Form.Item>

      <div className="mt-4 text-gray-600 text-sm">
        <p>Password requirements:</p>
        <ul className="list-disc pl-5 mt-1">
          <li>Minimum 8 characters</li>
          <li>At least one uppercase letter</li>
          <li>At least one lowercase letter</li>
          <li>At least one number</li>
          <li>At least one special character (@$!%*?&)</li>
        </ul>
      </div>
    </Form>
  );
};

export default ChangePasswordForm;
