import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createStore,
  getAllStores,
  getStoreById,
  updateStore,
  deleteStore,
} from "../services/storeService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

/**
 * Handle Store Requests
 * This controller handles all store-related operations
 */
export const handleStoreRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, storeId, page, limit, ...storeData } = req.body;
  const requester = req.user as DecodedToken;

  // Validate mode
  const validModes = ["createnew", "retrieve", "update", "delete"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      /**
       * Create a new store
       * Required fields: name
       */
      case "createnew": {
        // Validate required fields
        if (!storeData.name) {
          return sendResponse(
            res,
            400,
            false,
            "Store name is required."
          );
        }

        // Create store
        const store = await createStore(requester, storeData);
        return sendResponse(
          res,
          201,
          true,
          "Store created successfully.",
          store
        );
      }

      /**
       * Retrieve stores (single or all)
       * If storeId is provided, retrieve a single store
       * Otherwise, retrieve all stores with pagination
       */
      case "retrieve": {
        if (storeId) {
          // Retrieve a single store
          const store = await getStoreById(requester, Number(storeId));
          return sendResponse(
            res,
            200,
            true,
            "Store retrieved successfully.",
            store
          );
        } else {
          // Retrieve all stores with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const stores = await getAllStores(requester, pageNum, limitNum);
          return sendResponse(
            res,
            200,
            true,
            "Stores retrieved successfully.",
            stores
          );
        }
      }

      /**
       * Update a store
       * Required fields: storeId
       */
      case "update": {
        if (!storeId) {
          return sendResponse(
            res,
            400,
            false,
            "Store ID is required for update."
          );
        }

        // Update store
        const updatedStore = await updateStore(
          requester,
          Number(storeId),
          storeData
        );
        return sendResponse(
          res,
          200,
          true,
          "Store updated successfully.",
          updatedStore
        );
      }

      /**
       * Delete stores (Single or Multiple)
       * Required fields: storeId or storeIds
       */
      case "delete": {
        // Check if we have storeIds (array) or storeId (single)
        const { storeIds } = req.body;

        if (!storeId && !storeIds) {
          return sendResponse(
            res,
            400,
            false,
            "Store ID(s) are required for deletion."
          );
        }

        // Use storeIds if provided, otherwise use single storeId
        const idsToDelete = storeIds
          ? (Array.isArray(storeIds) ? storeIds : [storeIds])
          : [Number(storeId)];

        const result = await deleteStore(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} stores deleted successfully.`
          : "Store deleted successfully.";

        return sendResponse(
          res,
          200,
          true,
          message,
          result
        );
      }

      default:
        return sendResponse(res, 400, false, "Invalid mode.");
    }
  } catch (error: any) {
    console.error("Store request error:", error);
    return sendResponse(
      res,
      error.statusCode || 500,
      false,
      error.message || "Internal Server Error"
    );
  }
};
