"use client";

import React, { useState } from "react";
import Image from "next/image";
import { <PERSON>ton, Tooltip, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  DollarOutlined,
  ShoppingOutlined,
  BarcodeOutlined,
  CalendarOutlined,
  PlusCircleOutlined,
  DeleteFilled,
  PictureOutlined
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Product } from "@/reduxRTK/services/productApi";
import { useGetAllCategoriesQuery } from "@/reduxRTK/services/categoryApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface ProductTableProps {
  products: Product[];
  loading: boolean;
  onView: (productId: number) => void;
  onEdit: (product: Product) => void;
  onDelete: (productId: number) => void;
  onBulkDelete?: (productIds: number[]) => void;
  onAdjustStock: (product: Product) => void;
  isMobile?: boolean;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  onAdjustStock,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected products
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Get categories for category names
  const { data: categoriesData } = useGetAllCategoriesQuery({ page: 1, limit: 100 });
  const categories = (categoriesData as any)?.data?.categories || [];

  const getCategoryName = (categoryId: number) => {
    const category = categories.find((cat: any) => cat.id === categoryId);
    return category ? category.name : 'Uncategorized';
  };

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all products that the user can delete
      const selectableProductIds = products
        .filter(product => canEditDelete(product))
        .map(product => product.id);
      setSelectedProducts(selectableProductIds);
    } else {
      // Deselect all products
      setSelectedProducts([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedProducts.length > 0 && onBulkDelete) {
      onBulkDelete(selectedProducts);
      setSelectedProducts([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No products selected',
        description: 'Please select at least one product to delete.',
      });
    }
  };

  // Check if user can edit/delete
  const canEditDelete = (product: Product) => {
    if (userRole === "superadmin") return true;
    if (userRole === "admin" && user?.id === product.createdBy) return true;
    return false;
  };

  // Check if user can adjust stock
  const canAdjustStock = userRole === "admin";

  const renderProductImage = (product: Product) => {
    if (product.imageUrl) {
      return (
        <Image
          src={product.imageUrl}
          alt={product.name}
          width={48}
          height={48}
          className="object-cover rounded-md"
        />
      );
    }
    return (
      <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
        <PictureOutlined className="text-2xl text-gray-400" />
      </div>
    );
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when products are selected */}
      {selectedProducts.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedProducts.length} {selectedProducts.length === 1 ? 'product' : 'products'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 120px 150px"
          minWidth="700px"
        >
          {/* Mobile Headers */}
          <TableHeader className="text-center">
            <Checkbox
              checked={selectAll}
              onChange={handleSelectAllChange}
              disabled={products.filter(product => canEditDelete(product)).length === 0}
            />
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShoppingOutlined className="mr-1" />
              Name
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <DollarOutlined className="mr-1" />
              Price
            </span>
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {products.map((product) => (
            <TableRow
              key={product.id}
              selected={selectedProducts.includes(product.id)}
            >
              <TableCell className="text-center">
                {canEditDelete(product) && (
                  <Checkbox
                    checked={selectedProducts.includes(product.id)}
                    onChange={(e) => handleCheckboxChange(product.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-3">
                  {renderProductImage(product)}
                  <div>
                    <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                      {product.name}
                    </div>
                    <Tag color="blue" className="mt-1">
                      {getCategoryName(product.categoryId)}
                    </Tag>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center text-green-600">
                  <DollarOutlined className="mr-1" />
                  {parseFloat(product.price).toFixed(2)}
                </div>
                <div className="flex items-center text-sm text-gray-500 mt-1">
                  <ShoppingOutlined className="mr-1" />
                  {product.stockQuantity}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(product.id)}
                      type="text"
                      className="view-button text-blue-600"
                      size="small"
                    />
                  </Tooltip>
                  {canEditDelete(product) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(product)}
                          type="text"
                          className="edit-button"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(product.id)}
                          type="text"
                          className="delete-button"
                          danger
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                  {canAdjustStock && (
                    <Tooltip title="Adjust Stock">
                      <Button
                        icon={<PlusCircleOutlined />}
                        onClick={() => onAdjustStock(product)}
                        type="text"
                        className="adjust-stock-button"
                        size="small"
                      />
                    </Tooltip>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={products.filter(product => canEditDelete(product)).length === 0}
                  />
                </th>

                {/* Image Column */}
                <th scope="col" className="w-12 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Image
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Name
                </th>

                {/* Category Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Category
                </th>

                {/* SKU Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  SKU
                </th>

                {/* Barcode Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Barcode
                </th>

                {/* Price Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Price
                </th>

                {/* Stock Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Stock
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className={selectedProducts.includes(product.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canEditDelete(product) && (
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) => handleCheckboxChange(product.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Image Column */}
                  <td className="px-3 py-4 whitespace-nowrap">
                    {renderProductImage(product)}
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {product.name}
                    </div>
                  </td>

                  {/* Category Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <Tag color="blue">{getCategoryName(product.categoryId)}</Tag>
                  </td>

                  {/* SKU Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {product.sku || '-'}
                  </td>

                  {/* Barcode Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {product.barcode || '-'}
                  </td>

                  {/* Price Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="flex items-center text-green-600">
                      <DollarOutlined className="mr-1" />
                      {parseFloat(product.price).toFixed(2)}
                    </div>
                  </td>

                  {/* Stock Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="flex items-center">
                      <ShoppingOutlined className="mr-1" />
                      {product.stockQuantity}
                    </div>
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(product.id)}
                          type="text"
                          className="view-button text-blue-600"
                          size="middle"
                        />
                      </Tooltip>
                      {canEditDelete(product) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(product)}
                              type="text"
                              className="edit-button"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(product.id)}
                              type="text"
                              className="delete-button"
                              danger
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                      {canAdjustStock && (
                        <Tooltip title="Adjust Stock">
                          <Button
                            icon={<PlusCircleOutlined />}
                            onClick={() => onAdjustStock(product)}
                            type="text"
                            className="adjust-stock-button"
                            size="middle"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ProductTable;
