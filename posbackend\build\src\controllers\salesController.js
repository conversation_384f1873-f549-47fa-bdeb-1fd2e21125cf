"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSalesRequest = void 0;
const responseHelper_1 = require("../utils/responseHelper");
const salesService_1 = require("../services/salesService");
const modeValidator_1 = require("../utils/modeValidator");
const handleSalesRequest = async (req, res) => {
    const { mode, saleId, page, limit, saleData } = req.body; // saleData instead of bulkSalesData
    const requester = req.user;
    const validModes = ["createnew", "retrieve", "update", "delete"];
    if (!(0, modeValidator_1.validateMode)(res, mode, validModes))
        return;
    try {
        switch (mode) {
            case "createnew": {
                // Validate saleData for "createnew" mode
                if (!saleData ||
                    !saleData.items ||
                    !Array.isArray(saleData.items) ||
                    saleData.items.length === 0) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Sale data with valid items is required.");
                }
                // Handle sale creation
                const result = await (0, salesService_1.createSale)(requester, { saleData });
                return (0, responseHelper_1.sendResponse)(res, 201, true, "Sale created successfully.", result);
            }
            case "retrieve": {
                if (saleId) {
                    try {
                        // Fetch sale with items included
                        const sale = await (0, salesService_1.getSaleById)(requester, Number(saleId));
                        console.log("Retrieved sale with items:", sale);
                        // Return the sale with its items
                        return (0, responseHelper_1.sendResponse)(res, 200, true, "Sale retrieved successfully.", {
                            sale,
                            saleItems: sale.items || [], // Include items for backward compatibility
                        });
                    }
                    catch (error) {
                        console.error("Error retrieving sale:", error);
                        return (0, responseHelper_1.sendResponse)(res, 500, false, "Error retrieving sale details");
                    }
                }
                else {
                    const pageNum = Number(page) || 1;
                    const limitNum = Number(limit) || 10;
                    if (pageNum < 1 || limitNum < 1) {
                        return (0, responseHelper_1.sendResponse)(res, 400, false, "Invalid pagination values.");
                    }
                    // Fetch sales data with items already included
                    const salesData = await (0, salesService_1.getAllSales)(requester, pageNum, limitNum);
                    console.log("Retrieved sales with items:", salesData.sales.length);
                    return (0, responseHelper_1.sendResponse)(res, 200, true, "Sales retrieved successfully.", salesData);
                }
            }
            case "update": {
                if (!saleId) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Sale ID is required for updating.");
                }
                const updatedSale = await (0, salesService_1.updateSale)(requester, Number(saleId), req.body);
                return (0, responseHelper_1.sendResponse)(res, 200, true, "Sale updated successfully.", updatedSale);
            }
            case "delete": {
                // Check if we have saleIds (array) or saleId (single)
                const { saleIds } = req.body;
                if (!saleId && !saleIds) {
                    return (0, responseHelper_1.sendResponse)(res, 400, false, "Sale ID(s) are required for deletion.");
                }
                // Use saleIds if provided, otherwise use single saleId
                const idsToDelete = saleIds
                    ? (Array.isArray(saleIds) ? saleIds : [saleIds])
                    : [Number(saleId)];
                const result = await (0, salesService_1.deleteSale)(requester, idsToDelete);
                const message = idsToDelete.length > 1
                    ? `${idsToDelete.length} sales deleted successfully.`
                    : "Sale deleted successfully.";
                return (0, responseHelper_1.sendResponse)(res, 200, true, message, result);
            }
            default:
                return (0, responseHelper_1.sendResponse)(res, 400, false, "Unexpected error occurred.");
        }
    }
    catch (error) {
        return (0, responseHelper_1.sendResponse)(res, 500, false, error.message || "Internal Server Error");
    }
};
exports.handleSalesRequest = handleSalesRequest;
