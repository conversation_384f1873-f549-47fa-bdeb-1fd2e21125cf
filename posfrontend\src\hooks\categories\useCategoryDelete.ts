"use client";

import { useDeleteCategoryMutation } from "@/reduxRTK/services/categoryApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useCategoryDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a category
  const [deleteCategory, { isLoading }] = useDeleteCategoryMutation();

  const deleteCategoryById = async (categoryId: number) => {
    try {
      const result = await deleteCategory(categoryId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete category");
      }

      showMessage("success", "Category deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete category error:", error);
      showMessage("error", error.message || "Failed to delete category");
      throw error;
    }
  };

  return {
    deleteCategory: deleteCategoryById,
    isDeleting: isLoading
  };
};
