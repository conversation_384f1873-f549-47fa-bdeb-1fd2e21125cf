"use client";

import React from 'react';
import dynamic from 'next/dynamic';
import { ApexOptions } from 'apexcharts';
import { AdminStats } from '@/reduxRTK/services/dashboardApi';

// Dynamically import Chart to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

interface ProfitLossChartProps {
  stats: AdminStats;
  formatLargeNumber?: (num: number) => string;
}

const ProfitLossChart: React.FC<ProfitLossChartProps> = ({ stats, formatLargeNumber }) => {
  const chartData = [
    {
      name: 'Revenue',
      data: [stats.revenue.value]
    },
    {
      name: 'COGS',
      data: [stats.cogs.value]
    },
    {
      name: 'Expenses',
      data: [stats.expenses.value]
    },
    {
      name: 'Profit',
      data: [stats.profit.value]
    }
  ];

  const formatValue = (val: number) => {
    if (formatLargeNumber) return formatLargeNumber(val);
    return `₵${val.toLocaleString()}`;
  };

  const options: ApexOptions = {
    chart: {
      type: 'bar',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#10B981', '#EF4444', '#F59E0B', '#3B82F6'],
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '60%',
        borderRadius: 4,
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function(val: number) {
        return formatValue(val);
      },
      style: {
        fontSize: '12px',
        fontWeight: 'bold',
      }
    },
    stroke: {
      show: true,
      width: 2,
      colors: ['transparent']
    },
    xaxis: {
      categories: ['Financial Overview'],
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        formatter: function(val: number) {
          return formatValue(val);
        }
      }
    },
    fill: {
      opacity: 1
    },
    tooltip: {
      y: {
        formatter: function(val: number) {
          return formatValue(val);
        }
      }
    },
    legend: {
      position: 'top',
      horizontalAlign: 'center',
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
  };

  return (
    <div className="h-80">
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="text-center p-2 bg-green-50 rounded">
            <div className="text-green-600 font-semibold">Profit Margin</div>
            <div className="text-lg font-bold text-green-700">
              {stats.profitMargin.value.toFixed(1)}%
            </div>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded">
            <div className="text-blue-600 font-semibold">Net Profit</div>
            <div className="text-lg font-bold text-blue-700">
              {formatValue(stats.profit.value)}
            </div>
          </div>
        </div>
      </div>
      <Chart
        options={options}
        series={chartData}
        type="bar"
        height={250}
      />
    </div>
  );
};

export default ProfitLossChart;
