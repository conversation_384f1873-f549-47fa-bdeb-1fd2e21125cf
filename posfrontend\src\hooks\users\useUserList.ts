"use client";

import { useState, useEffect } from "react";
import { useGetAllUsersQuery } from "@/reduxRTK/services/authApi";
import { ApiResponse, PaginatedUsers } from "@/types/user";
import { useDebounce } from "@/hooks/useDebounce";

export const useUserList = () => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // RTK Query hook for fetching users
  const {
    data,
    isLoading,
    isFetching,
    refetch
  } = useGetAllUsersQuery({
    page,
    limit: pageSize,
    search: debouncedSearchTerm
  }, {
    // Refetch when the search term changes
    refetchOnMountOrArgChange: true
  }) as {
    data?: ApiResponse<PaginatedUsers>;
    isLoading: boolean;
    isFetching: boolean;
    refetch: () => void
  };

  // Log search state for debugging
  console.log("User search state:", {
    searchTerm,
    debouncedSearchTerm,
    resultsCount: data?.data?.users?.length || 0,
    totalResults: data?.data?.total || 0
  });

  return {
    users: data?.data?.users || [],
    total: data?.data?.total || 0,
    isLoading,
    isFetching,
    refetch,
    searchTerm,
    setSearchTerm,
    pagination: {
      page,
      pageSize,
      setPage,
      setPageSize
    }
  };
};
