"use client";

import { useDeletePurchaseMutation } from "@/reduxRTK/services/purchaseApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const usePurchaseDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a purchase
  const [deletePurchase, { isLoading }] = useDeletePurchaseMutation();

  const deletePurchaseById = async (purchaseId: number) => {
    try {
      const result = await deletePurchase(purchaseId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete purchase");
      }

      showMessage("success", "Purchase deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete purchase error:", error);
      showMessage("error", error.message || "Failed to delete purchase");
      throw error;
    }
  };

  return {
    deletePurchase: deletePurchaseById,
    isDeleting: isLoading
  };
};
