import { eq } from "drizzle-orm";
import { postgresDb } from "../db/db";
import { users } from "../db/schema";

export const checkPaymentStatus = async (requesterId: number): Promise<boolean> => {
    // 🚨 **Fetch requester's role & payment status**
    const requesterResult = await postgresDb
      .select({ role: users.role, paymentStatus: users.paymentStatus })
      .from(users)
      .where(eq(users.id, requesterId))
      .limit(1);

    const requester = requesterResult[0];

    if (!requester) {
      throw new Error("Unauthorized: User not found.");
    }

    // 🚨 **Block unpaid admins & cashiers**
    if ((requester.role === "admin" || requester.role === "cashier") && requester.paymentStatus !== "successful") {
      throw new Error("Unauthorized: Payment required.");
    }

    return true; // ✅ Payment is valid, proceed
  };
