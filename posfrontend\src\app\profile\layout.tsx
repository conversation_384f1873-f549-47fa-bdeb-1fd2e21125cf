"use client";

import type { PropsWithChildren } from "react";
import React, { useState, useEffect } from "react";
import { Sidebar } from "@/components/Layouts/sidebar";
import { MobileSidebar } from "@/components/Layouts/sidebar/MobileSidebar";
import { Header } from "@/components/Layouts/header";
import ProtectedRoute from "@/components/Auth/ProtectedRoute";
import { useSidebarContext } from "@/components/Layouts/sidebar/sidebar-context";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function ProfileLayout({ children }: PropsWithChildren) {
  const [isClient, setIsClient] = useState(false);
  const { setIsOpen, isMobile } = useSidebarContext();

  // Set isClient to true when component mounts on client
  useEffect(() => {
    setIsClient(true);
    // Don't automatically close the sidebar - let the sidebar context handle it
    console.log("ProfileLayout mounted on client", { isMobile });
  }, [isMobile]);

  // Force close sidebar when component unmounts to prevent persistence
  useEffect(() => {
    return () => {
      setIsOpen(false);
      console.log("ProfileLayout unmounted - closing sidebar");
    };
  }, [setIsOpen]);

  // Show loading spinner until client-side hydration is complete
  if (!isClient) {
    return <LoadingSpinner tip="Loading profile..." />;
  }

  return (
    <ProtectedRoute checkPayment={false}>
      <div className="flex h-screen overflow-hidden">
        {/* Mobile sidebar - only visible when explicitly toggled */}
        <MobileSidebar />

        {/* Fixed sidebar - hidden on mobile, visible on desktop */}
        <div className="fixed left-0 top-0 h-full z-20 hidden lg:block">
          <Sidebar />
        </div>

        {/* Main content area with fixed header and scrollable content */}
        <div className="w-full lg:ml-[290px] bg-white flex flex-col h-screen">
          {/* Fixed header */}
          <div className="sticky top-0 z-30 w-full bg-white border-b border-gray-200">
            <Header />
          </div>

          {/* Scrollable content */}
          <div className="flex-1 overflow-y-auto">
            <main className="isolate mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10">
              {children}
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
