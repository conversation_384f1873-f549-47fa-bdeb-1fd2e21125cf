ALTER TABLE "receipts" DROP CONSTRAINT "receipts_sale_id_sales_id_fk";
--> statement-breakpoint
ALTER TABLE "sales" DROP CONSTRAINT "sales_cashier_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "sales_items" DROP CONSTRAINT "sales_items_product_id_products_id_fk";
--> statement-breakpoint
ALTER TABLE "stock_adjustments" DROP CONSTRAINT "stock_adjustments_product_id_products_id_fk";
--> statement-breakpoint
ALTER TABLE "stock_adjustments" DROP CONSTRAINT "stock_adjustments_adjusted_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "products" ALTER COLUMN "category_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "receipts" ALTER COLUMN "sale_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "sales_items" ALTER COLUMN "sale_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "sales_items" ALTER COLUMN "product_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "stock_adjustments" ALTER COLUMN "product_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "receipts" ADD CONSTRAINT "receipts_sale_id_sales_id_fk" FOREIGN KEY ("sale_id") REFERENCES "public"."sales"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales" ADD CONSTRAINT "sales_cashier_id_users_id_fk" FOREIGN KEY ("cashier_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_items" ADD CONSTRAINT "sales_items_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "stock_adjustments" ADD CONSTRAINT "stock_adjustments_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "stock_adjustments" ADD CONSTRAINT "stock_adjustments_adjusted_by_users_id_fk" FOREIGN KEY ("adjusted_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_categories_name" ON "categories" USING btree ("name");--> statement-breakpoint
CREATE INDEX "idx_products_category" ON "products" USING btree ("category_id");--> statement-breakpoint
CREATE INDEX "idx_receipts_sale" ON "receipts" USING btree ("sale_id");--> statement-breakpoint
CREATE INDEX "idx_sales_transaction_date" ON "sales" USING btree ("transaction_date");--> statement-breakpoint
CREATE INDEX "idx_sales_cashier" ON "sales" USING btree ("cashier_id");--> statement-breakpoint
CREATE INDEX "idx_sales_items_sale" ON "sales_items" USING btree ("sale_id");--> statement-breakpoint
CREATE INDEX "idx_sales_items_product" ON "sales_items" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "idx_stock_adjustments_product" ON "stock_adjustments" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "idx_users_email" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "idx_users_phone" ON "users" USING btree ("phone");