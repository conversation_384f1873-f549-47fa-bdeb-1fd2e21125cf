"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5984],{1227:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},99315:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},81910:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-792 72h752v120H136V232zm752 560H136V440h752v352zm-237-64h165c4.4 0 8-3.6 8-8v-72c0-4.4-3.6-8-8-8H651c-4.4 0-8 3.6-8 8v72c0 4.4 3.6 8 8 8z"}}]},name:"credit-card",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},90954:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},7162:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},15424:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},72278:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},51814:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},39279:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0014.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h676c17.7 0 32-14.3 32-32V535a175 175 0 0015.6-28.9c9.5-22.3 14.4-46 14.4-70.4V304c0-17-13.3-30.9-30-31.9zM214 184h596v88H214v-88zm362 656.1H448V736h128v104.1zm234 0H640V704c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v136.1H214V597.9c2.9 1.4 5.9 2.8 9 4 22.3 9.4 46 14.1 70.4 14.1s48-4.7 70.4-14.1c13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 3-1.3 6-2.6 9-4v242.2zm30-404.4c0 59.8-49 108.3-109.3 108.3-40.8 0-76.4-22.1-95.2-54.9-2.9-5-8.1-8.1-13.9-8.1h-.6c-5.7 0-11 3.1-13.9 8.1A109.24 109.24 0 01512 544c-40.7 0-76.2-22-95-54.7-3-5.1-8.4-8.3-14.3-8.3s-11.4 3.2-14.3 8.3a109.63 109.63 0 01-95.1 54.7C233 544 184 495.5 184 435.7v-91.2c0-.3.2-.5.5-.5h655c.3 0 .5.2.5.5v91.2z"}}]},name:"shop",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},98623:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},55750:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},45556:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(85407),n=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var c=a(84021);let l=n.forwardRef(function(e,t){return n.createElement(c.A,(0,r.A)({},e,{ref:t,icon:o}))})},78444:(e,t,a)=>{a.d(t,{A:()=>C});var r=a(12115),n=a(4617),o=a.n(n),c=a(30377),l=a(15231),i=a(45049),s=a(31049),d=a(7926),u=a(27651),v=a(7703);let p=r.createContext({});var f=a(67548),m=a(70695),g=a(1086),h=a(56204);let b=e=>{let{antCls:t,componentCls:a,iconCls:r,avatarBg:n,avatarColor:o,containerSize:c,containerSizeLG:l,containerSizeSM:i,textFontSize:s,textFontSizeLG:d,textFontSizeSM:u,borderRadius:v,borderRadiusLG:p,borderRadiusSM:g,lineWidth:h,lineType:b}=e,y=(e,t,n)=>({width:e,height:e,borderRadius:"50%",["&".concat(a,"-square")]:{borderRadius:n},["&".concat(a,"-icon")]:{fontSize:t,["> ".concat(r)]:{margin:0}}});return{[a]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,m.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:o,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:n,border:"".concat((0,f.zA)(h)," ").concat(b," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),y(c,s,v)),{"&-lg":Object.assign({},y(l,d,p)),"&-sm":Object.assign({},y(i,u,g)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=e=>{let{componentCls:t,groupBorderColor:a,groupOverlapping:r,groupSpace:n}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:a},"> *:not(:first-child)":{marginInlineStart:r}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:n}}}},O=(0,g.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:a}=e,r=(0,h.oX)(e,{avatarBg:a,avatarColor:t});return[b(r),y(r)]},e=>{let{controlHeight:t,controlHeightLG:a,controlHeightSM:r,fontSize:n,fontSizeLG:o,fontSizeXL:c,fontSizeHeading3:l,marginXS:i,marginXXS:s,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:a,containerSizeSM:r,textFontSize:Math.round((o+c)/2),textFontSizeLG:l,textFontSizeSM:n,groupSpace:s,groupOverlapping:-i,groupBorderColor:d}});var x=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(a[r[n]]=e[r[n]]);return a};let z=r.forwardRef((e,t)=>{let a;let{prefixCls:n,shape:f,size:m,src:g,srcSet:h,icon:b,className:y,rootClassName:z,style:w,alt:A,draggable:E,children:j,crossOrigin:C,gap:S=4,onError:k}=e,M=x(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[V,N]=r.useState(1),[B,H]=r.useState(!1),[R,P]=r.useState(!0),L=r.useRef(null),W=r.useRef(null),F=(0,l.K4)(t,L),{getPrefixCls:I,avatar:_}=r.useContext(s.QO),D=r.useContext(p),T=()=>{if(!W.current||!L.current)return;let e=W.current.offsetWidth,t=L.current.offsetWidth;0!==e&&0!==t&&2*S<t&&N(t-2*S<e?(t-2*S)/e:1)};r.useEffect(()=>{H(!0)},[]),r.useEffect(()=>{P(!0),N(1)},[g]),r.useEffect(T,[S]);let K=(0,u.A)(e=>{var t,a;return null!==(a=null!==(t=null!=m?m:null==D?void 0:D.size)&&void 0!==t?t:e)&&void 0!==a?a:"default"}),G=Object.keys("object"==typeof K&&K||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),Q=(0,v.A)(G),X=r.useMemo(()=>{if("object"!=typeof K)return{};let e=K[i.ye.find(e=>Q[e])];return e?{width:e,height:e,fontSize:e&&(b||j)?e/2:18}:{}},[Q,K]),q=I("avatar",n),U=(0,d.A)(q),[Y,J,Z]=O(q,U),$=o()({["".concat(q,"-lg")]:"large"===K,["".concat(q,"-sm")]:"small"===K}),ee=r.isValidElement(g),et=f||(null==D?void 0:D.shape)||"circle",ea=o()(q,$,null==_?void 0:_.className,"".concat(q,"-").concat(et),{["".concat(q,"-image")]:ee||g&&R,["".concat(q,"-icon")]:!!b},Z,U,y,z,J),er="number"==typeof K?{width:K,height:K,fontSize:b?K/2:18}:{};if("string"==typeof g&&R)a=r.createElement("img",{src:g,draggable:E,srcSet:h,onError:()=>{!1!==(null==k?void 0:k())&&P(!1)},alt:A,crossOrigin:C});else if(ee)a=g;else if(b)a=b;else if(B||1!==V){let e="scale(".concat(V,")");a=r.createElement(c.A,{onResize:T},r.createElement("span",{className:"".concat(q,"-string"),ref:W,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},j))}else a=r.createElement("span",{className:"".concat(q,"-string"),style:{opacity:0},ref:W},j);return Y(r.createElement("span",Object.assign({},M,{style:Object.assign(Object.assign(Object.assign(Object.assign({},er),X),null==_?void 0:_.style),w),className:ea,ref:F}),a))});var w=a(63588),A=a(58292),E=a(70739);let j=e=>{let{size:t,shape:a}=r.useContext(p),n=r.useMemo(()=>({size:e.size||t,shape:e.shape||a}),[e.size,e.shape,t,a]);return r.createElement(p.Provider,{value:n},e.children)};z.Group=e=>{var t,a,n,c;let{getPrefixCls:l,direction:i}=r.useContext(s.QO),{prefixCls:u,className:v,rootClassName:p,style:f,maxCount:m,maxStyle:g,size:h,shape:b,maxPopoverPlacement:y,maxPopoverTrigger:x,children:C,max:S}=e,k=l("avatar",u),M="".concat(k,"-group"),V=(0,d.A)(k),[N,B,H]=O(k,V),R=o()(M,{["".concat(M,"-rtl")]:"rtl"===i},H,V,v,p,B),P=(0,w.A)(C).map((e,t)=>(0,A.Ob)(e,{key:"avatar-key-".concat(t)})),L=(null==S?void 0:S.count)||m,W=P.length;if(L&&L<W){let e=P.slice(0,L),l=P.slice(L,W),i=(null==S?void 0:S.style)||g,s=(null===(t=null==S?void 0:S.popover)||void 0===t?void 0:t.trigger)||x||"hover",d=(null===(a=null==S?void 0:S.popover)||void 0===a?void 0:a.placement)||y||"top",u=Object.assign(Object.assign({content:l},null==S?void 0:S.popover),{classNames:{root:o()("".concat(M,"-popover"),null===(c=null===(n=null==S?void 0:S.popover)||void 0===n?void 0:n.classNames)||void 0===c?void 0:c.root)},placement:d,trigger:s});return e.push(r.createElement(E.A,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},u),r.createElement(z,{style:i},"+".concat(W-L)))),N(r.createElement(j,{shape:b,size:h},r.createElement("div",{className:R,style:f},e)))}return N(r.createElement(j,{shape:b,size:h},r.createElement("div",{className:R,style:f},P)))};let C=z},70739:(e,t,a)=>{a.d(t,{A:()=>k});var r=a(12115),n=a(4617),o=a.n(n),c=a(35015),l=a(23672);let i=e=>e?"function"==typeof e?e():e:null;var s=a(19635),d=a(58292),u=a(6457),v=a(67804),p=a(31049),f=a(70695),m=a(9023),g=a(29449),h=a(50887),b=a(57554),y=a(1086),O=a(56204);let x=e=>{let{componentCls:t,popoverColor:a,titleMinWidth:r,fontWeightStrong:n,innerPadding:o,boxShadowSecondary:c,colorTextHeading:l,borderRadiusLG:i,zIndexPopup:s,titleMarginBottom:d,colorBgElevated:u,popoverBg:v,titleBorderBottom:p,innerContentPadding:m,titlePadding:h}=e;return[{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:s,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":u,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},["".concat(t,"-content")]:{position:"relative"},["".concat(t,"-inner")]:{backgroundColor:v,backgroundClip:"padding-box",borderRadius:i,boxShadow:c,padding:o},["".concat(t,"-title")]:{minWidth:r,marginBottom:d,color:l,fontWeight:n,borderBottom:p,padding:h},["".concat(t,"-inner-content")]:{color:a,padding:m}})},(0,g.Ay)(e,"var(--antd-arrow-background-color)"),{["".concat(t,"-pure")]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",["".concat(t,"-content")]:{display:"inline-block"}}}]},z=e=>{let{componentCls:t}=e;return{[t]:b.s.map(a=>{let r=e["".concat(a,"6")];return{["&".concat(t,"-").concat(a)]:{"--antd-arrow-background-color":r,["".concat(t,"-inner")]:{backgroundColor:r},["".concat(t,"-arrow")]:{background:"transparent"}}}})}},w=(0,y.OF)("Popover",e=>{let{colorBgElevated:t,colorText:a}=e,r=(0,O.oX)(e,{popoverBg:t,popoverColor:a});return[x(r),z(r),(0,m.aB)(r,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:a,fontHeight:r,padding:n,wireframe:o,zIndexPopupBase:c,borderRadiusLG:l,marginXS:i,lineType:s,colorSplit:d,paddingSM:u}=e,v=a-r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:c+30},(0,h.n)(e)),(0,g.Ke)({contentRadius:l,limitVerticalRadius:!0})),{innerPadding:o?0:12,titleMarginBottom:o?0:i,titlePadding:o?"".concat(v/2,"px ").concat(n,"px ").concat(v/2-t,"px"):0,titleBorderBottom:o?"".concat(t,"px ").concat(s," ").concat(d):"none",innerContentPadding:o?"".concat(u,"px ").concat(n,"px"):0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var A=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(a[r[n]]=e[r[n]]);return a};let E=e=>{let{title:t,content:a,prefixCls:n}=e;return t||a?r.createElement(r.Fragment,null,t&&r.createElement("div",{className:"".concat(n,"-title")},t),a&&r.createElement("div",{className:"".concat(n,"-inner-content")},a)):null},j=e=>{let{hashId:t,prefixCls:a,className:n,style:c,placement:l="top",title:s,content:d,children:u}=e,p=i(s),f=i(d),m=o()(t,a,"".concat(a,"-pure"),"".concat(a,"-placement-").concat(l),n);return r.createElement("div",{className:m,style:c},r.createElement("div",{className:"".concat(a,"-arrow")}),r.createElement(v.z,Object.assign({},e,{className:t,prefixCls:a}),u||r.createElement(E,{prefixCls:a,title:p,content:f})))};var C=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(a[r[n]]=e[r[n]]);return a};let S=r.forwardRef((e,t)=>{var a,n;let{prefixCls:v,title:f,content:m,overlayClassName:g,placement:h="top",trigger:b="hover",children:y,mouseEnterDelay:O=.1,mouseLeaveDelay:x=.1,onOpenChange:z,overlayStyle:A={},styles:j,classNames:S}=e,k=C(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:M,className:V,style:N,classNames:B,styles:H}=(0,p.TP)("popover"),R=M("popover",v),[P,L,W]=w(R),F=M(),I=o()(g,L,W,V,B.root,null==S?void 0:S.root),_=o()(B.body,null==S?void 0:S.body),[D,T]=(0,c.A)(!1,{value:null!==(a=e.open)&&void 0!==a?a:e.visible,defaultValue:null!==(n=e.defaultOpen)&&void 0!==n?n:e.defaultVisible}),K=(e,t)=>{T(e,!0),null==z||z(e,t)},G=e=>{e.keyCode===l.A.ESC&&K(!1,e)},Q=i(f),X=i(m);return P(r.createElement(u.A,Object.assign({placement:h,trigger:b,mouseEnterDelay:O,mouseLeaveDelay:x},k,{prefixCls:R,classNames:{root:I,body:_},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},H.root),N),A),null==j?void 0:j.root),body:Object.assign(Object.assign({},H.body),null==j?void 0:j.body)},ref:t,open:D,onOpenChange:e=>{K(e)},overlay:Q||X?r.createElement(E,{prefixCls:R,title:Q,content:X}):null,transitionName:(0,s.b)(F,"zoom-big",k.transitionName),"data-popover-inject":!0}),(0,d.Ob)(y,{onKeyDown:e=>{var t,a;r.isValidElement(y)&&(null===(a=null==y?void 0:(t=y.props).onKeyDown)||void 0===a||a.call(t,e)),G(e)}})))});S._InternalPanelDoNotUseOrYouWillBeFired=e=>{let{prefixCls:t,className:a}=e,n=A(e,["prefixCls","className"]),{getPrefixCls:c}=r.useContext(p.QO),l=c("popover",t),[i,s,d]=w(l);return i(r.createElement(j,Object.assign({},n,{prefixCls:l,hashId:s,className:o()(a,d)})))};let k=S}}]);