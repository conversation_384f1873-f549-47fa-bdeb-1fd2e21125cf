(()=>{var e={};e.id=484,e.ids=[484],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},7182:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=s(70260),l=s(28203),r=s(25155),n=s.n(r),i=s(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["dashboard",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68203)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/dashboard/(home)/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91417:(e,t,s)=>{Promise.resolve().then(s.bind(s,68203))},4569:(e,t,s)=>{Promise.resolve().then(s.bind(s,20946))},86977:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),l=s(58009);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var n=s(78480);let i=l.forwardRef(function(e,t){return l.createElement(n.A,(0,a.A)({},e,{ref:t,icon:r}))})},59022:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(11855),l=s(58009);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var n=s(78480);let i=l.forwardRef(function(e,t){return l.createElement(n.A,(0,a.A)({},e,{ref:t,icon:r}))})},9170:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=s(59286).A},1236:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=s(14207).A},1387:(e,t,s)=>{"use strict";s.d(t,{A:()=>N});var a=s(58009),l=s(85303),r=s(2866),n=s(56073),i=s.n(n),o=s(90365),c=s(27343),d=s(31716);let x=e=>{let t;let{value:s,formatter:l,precision:r,decimalSeparator:n,groupSeparator:i="",prefixCls:o}=e;if("function"==typeof l)t=l(s);else{let e=String(s),l=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(l&&"-"!==e){let e=l[1],s=l[2]||"0",c=l[4]||"";s=s.replace(/\B(?=(\d{3})+(?!\d))/g,i),"number"==typeof r&&(c=c.padEnd(r,"0").slice(0,r>0?r:0)),c&&(c=`${n}${c}`),t=[a.createElement("span",{key:"int",className:`${o}-content-value-int`},e,s),c&&a.createElement("span",{key:"decimal",className:`${o}-content-value-decimal`},c)]}else t=e}return a.createElement("span",{className:`${o}-content-value`},t)};var u=s(47285),h=s(13662),m=s(10941);let f=e=>{let{componentCls:t,marginXXS:s,padding:a,colorTextDescription:l,titleFontSize:r,colorTextHeading:n,contentFontSize:i,fontFamily:o}=e;return{[t]:Object.assign(Object.assign({},(0,u.dF)(e)),{[`${t}-title`]:{marginBottom:s,color:l,fontSize:r},[`${t}-skeleton`]:{paddingTop:a},[`${t}-content`]:{color:n,fontSize:i,fontFamily:o,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:s},[`${t}-content-suffix`]:{marginInlineStart:s}}})}},p=(0,h.OF)("Statistic",e=>[f((0,m.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:s}=e;return{titleFontSize:s,contentFontSize:t}});var y=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)0>t.indexOf(a[l])&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(s[a[l]]=e[a[l]]);return s};let g=e=>{let{prefixCls:t,className:s,rootClassName:l,style:r,valueStyle:n,value:u=0,title:h,valueRender:m,prefix:f,suffix:g,loading:v=!1,formatter:j,precision:b,decimalSeparator:w=".",groupSeparator:N=",",onMouseEnter:A,onMouseLeave:S}=e,P=y(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:M,direction:D,className:R,style:C}=(0,c.TP)("statistic"),z=M("statistic",t),[k,E,$]=p(z),F=a.createElement(x,{decimalSeparator:w,groupSeparator:N,prefixCls:z,formatter:j,precision:b,value:u}),O=i()(z,{[`${z}-rtl`]:"rtl"===D},R,s,l,E,$),T=(0,o.A)(P,{aria:!0,data:!0});return k(a.createElement("div",Object.assign({},T,{className:O,style:Object.assign(Object.assign({},C),r),onMouseEnter:A,onMouseLeave:S}),h&&a.createElement("div",{className:`${z}-title`},h),a.createElement(d.A,{paragraph:!1,loading:v,className:`${z}-skeleton`},a.createElement("div",{style:n,className:`${z}-content`},f&&a.createElement("span",{className:`${z}-content-prefix`},f),m?m(F):F,g&&a.createElement("span",{className:`${z}-content-suffix`},g)))))},v=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var j=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)0>t.indexOf(a[l])&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(s[a[l]]=e[a[l]]);return s};let b=1e3/30,w=a.memo(e=>{let{value:t,format:s="HH:mm:ss",onChange:n,onFinish:i}=e,o=j(e,["value","format","onChange","onFinish"]),c=(0,l.A)(),d=a.useRef(null),x=()=>{null==i||i(),d.current&&(clearInterval(d.current),d.current=null)},u=()=>{let e=new Date(t).getTime();e>=Date.now()&&(d.current=setInterval(()=>{c(),null==n||n(e-Date.now()),e<Date.now()&&x()},b))};return a.useEffect(()=>(u(),()=>{d.current&&(clearInterval(d.current),d.current=null)}),[t]),a.createElement(g,Object.assign({},o,{value:t,valueRender:e=>(0,r.Ob)(e,{title:void 0}),formatter:(e,t)=>(function(e,t){let{format:s=""}=t;return function(e,t){let s=e,a=/\[[^\]]*]/g,l=(t.match(a)||[]).map(e=>e.slice(1,-1)),r=t.replace(a,"[]"),n=v.reduce((e,t)=>{let[a,l]=t;if(e.includes(a)){let t=Math.floor(s/l);return s-=t*l,e.replace(RegExp(`${a}+`,"g"),e=>{let s=e.length;return t.toString().padStart(s,"0")})}return e},r),i=0;return n.replace(a,()=>{let e=l[i];return i+=1,e})}(Math.max(new Date(e).getTime()-Date.now(),0),s)})(e,Object.assign(Object.assign({},t),{format:s}))}))});g.Countdown=w;let N=g},19716:function(e){var t;t=function(){return function(e,t,s){t.prototype.isBetween=function(e,t,a,l){var r=s(e),n=s(t),i="("===(l=l||"()")[0],o=")"===l[1];return(i?this.isAfter(r,a):!this.isBefore(r,a))&&(o?this.isBefore(n,a):!this.isAfter(n,a))||(i?this.isBefore(r,a):!this.isAfter(r,a))&&(o?this.isAfter(n,a):!this.isBefore(n,a))}}},e.exports=t()},86256:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let a=s(25488)._(s(40568));function l(e,t){var s;let l={};"function"==typeof e&&(l.loader=e);let r={...l,...t};return(0,a.default)({...r,modules:null==(s=r.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3400:(e,t)=>{"use strict";function s(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return s}})},65771:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let a=s(54639);function l(e){let{reason:t,children:s}=e;throw new a.BailoutToCSRError(t)}},40568:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let a=s(45512),l=s(58009),r=s(65771),n=s(86054);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},c=function(e){let t={...o,...e},s=(0,l.lazy)(()=>t.loader().then(i)),c=t.loading;function d(e){let i=c?(0,a.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,d=o?l.Suspense:l.Fragment,x=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.PreloadChunks,{moduleIds:t.modules}),(0,a.jsx)(s,{...e})]}):(0,a.jsx)(r.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(d,{...o?{fallback:i}:{},children:x})}return d.displayName="LoadableComponent",d}},86054:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return i}});let a=s(45512),l=s(55740),r=s(29294),n=s(3400);function i(e){let{moduleIds:t}=e,s=r.workAsyncStorage.getStore();if(void 0===s)return null;let i=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files;i.push(...t)}}return 0===i.length?null:(0,a.jsx)(a.Fragment,{children:i.map(e=>{let t=s.assetPrefix+"/_next/"+(0,n.encodeURIPath)(e);return e.endsWith(".css")?(0,a.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,l.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},20946:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eh});var a=s(45512),l=s(58009),r=s(60636),n=s(42391),i=s(97245),o=s(21419),c=s(1236),d=s(9170),x=s(6987),u=s(1387),h=s(88752);let m=({className:e=""})=>{let[t,s]=(0,l.useState)([]),{data:r,isLoading:n,error:c,refetch:d}=(0,i.VL)({page:1,limit:100,search:""});(0,l.useEffect)(()=>{if(r?.success&&r.data){let e=(r.data.users||[]).filter(e=>"admin"===e.role).map(e=>{let t="Monthly",s=89,a=1;if(e.lastPaymentDate&&e.nextPaymentDue){let l=new Date(e.lastPaymentDate),r=Math.round((new Date(e.nextPaymentDue).getTime()-l.getTime())/864e5);console.log(`📊 User ${e.email} - Days between payments: ${r}`),r>=350?(t="Annual",s=360,a=12):r>=80?(t="Quarterly",s=108,a=3):(t="Monthly",s=40,a=1)}return{id:e.id,name:e.name||"Admin User",email:e.email,plan:t,amount:s,status:"paid"===e.paymentStatus?"Active":"pending"===e.paymentStatus?"Pending":"overdue"===e.paymentStatus?"Overdue":"Inactive",nextPaymentDue:e.nextPaymentDue?new Date(e.nextPaymentDue).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",subscriptionPeriod:a}});s(e),console.log("✅ Formatted admin subscriptions from Redux:",e)}},[r]);let x=()=>{d()},u=e=>{switch(e.toLowerCase()){case"active":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},m=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase();return n?(0,a.jsxs)("div",{className:`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${e}`,children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"})}),(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(h.A,{style:{fontSize:24},spin:!0})})})]}):c?(0,a.jsxs)("div",{className:`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${e}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"}),(0,a.jsx)("button",{onClick:x,className:"text-sm text-blue-600 cursor-pointer hover:underline",children:"Retry"})]}),(0,a.jsx)("div",{className:"text-center text-red-500 py-8",children:(0,a.jsxs)("p",{children:["Error loading subscriptions: ",c.toString()]})})]}):(0,a.jsxs)("div",{className:`bg-white rounded-lg p-5 shadow-md border border-gray-200 ${e}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Admin Subscriptions"}),(0,a.jsx)("button",{onClick:x,className:"text-sm text-blue-600 cursor-pointer hover:underline",children:"Refresh"})]}),0===t.length?(0,a.jsx)("div",{className:"text-center text-gray-500 py-8",children:(0,a.jsx)("p",{children:"No admin subscriptions found"})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Admin"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Next Payment"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-medium text-sm",children:m(e.name)})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.plan}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₵",e.amount,".00"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${u(e.status)}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.nextPaymentDue})]},e.id))})]})})]})};var f=s(86256),p=s.n(f),y=s(16589),g=s.n(y);let v=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\RevenueChart.tsx -> react-apexcharts"]},ssr:!1}),j=({users:e})=>{let t=(0,l.useMemo)(()=>{let t=e.filter(e=>"admin"===e.role&&e.lastPaymentDate);console.log("\uD83D\uDCCA Revenue Chart - Admin users found:",t.length);let s=[];for(let e=11;e>=0;e--){let t=g()().subtract(e,"month");s.push({monthKey:t.format("YYYY-MM"),monthLabel:t.format("MMM YYYY"),revenue:0,adminCount:0})}t.forEach(e=>{if(e.lastPaymentDate&&e.nextPaymentDue){let t=g()(e.lastPaymentDate),a=g()(e.nextPaymentDue).diff(t,"day"),l=89;a>=350?l=1014:a>=80&&(l=259),console.log(`💰 User ${e.email}: ${a} days = ₵${l}`);let r=t.format("YYYY-MM"),n=s.find(e=>e.monthKey===r);n&&(n.revenue+=l)}});let a=s.map(e=>({x:e.monthLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Revenue chart data:",a),a},[e]),s=[{name:"Revenue",data:t}];return 0===t.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No revenue data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Revenue will appear when admins make payments"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(v,{options:{chart:{type:"area",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.7,opacityTo:.1,stops:[0,90,100]}},stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},tooltip:{y:{formatter:function(e){return`₵${e.toFixed(2)}`}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return`₵${e}`},style:{fontSize:"12px"}}}},series:s,type:"area",height:250})})},b=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\SubscriptionDistributionChart.tsx -> react-apexcharts"]},ssr:!1}),w=({users:e})=>{let t=(0,l.useMemo)(()=>{let t=e.filter(e=>"admin"===e.role&&"paid"===e.paymentStatus&&e.lastPaymentDate&&e.nextPaymentDue),s=0,a=0,l=0;return t.forEach(e=>{let t=g()(e.lastPaymentDate),r=g()(e.nextPaymentDue).diff(t,"day");r>=350?l++:r>=80?a++:s++}),{labels:["Monthly","Quarterly","Annual"],series:[s,a,l],total:s+a+l}},[e]),s={chart:{type:"donut",height:300},colors:["#5750F1","#0ABEF9","#10B981"],labels:t.labels,legend:{position:"bottom",horizontalAlign:"center",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},plotOptions:{pie:{donut:{size:"60%",labels:{show:!0,total:{show:!0,label:"Total Admins",fontSize:"16px",fontWeight:600,color:"#374151",formatter:function(){return t.total.toString()}},value:{show:!0,fontSize:"24px",fontWeight:700,color:"#111827"}}}}},dataLabels:{enabled:!0,formatter:function(e,t){let s=t.w.config.series[t.seriesIndex];return 0===s?"":`${s}`},style:{fontSize:"14px",fontWeight:700,colors:["#ffffff"]},dropShadow:{enabled:!0,top:1,left:1,blur:1,color:"#000",opacity:.45}},tooltip:{y:{formatter:function(e,{seriesIndex:s}){t.labels[s];let a=(e/t.total*100).toFixed(1);return`${e} admins (${a}%)`}}},responsive:[{breakpoint:480,options:{chart:{height:250},legend:{position:"bottom"}}}]};return 0===t.total?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No subscription data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when admins have active subscriptions"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(b,{options:s,series:t.series,type:"donut",height:250})})},N=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\UserGrowthChart.tsx -> react-apexcharts"]},ssr:!1}),A=({users:e})=>{let t=(0,l.useMemo)(()=>{let t=new Map;e.forEach(e=>{if(e.createdAt){let s=g()(e.createdAt).format("YYYY-MM"),a=t.get(s)||{admins:0,cashiers:0,total:0};"admin"===e.role?a.admins++:"cashier"===e.role&&a.cashiers++,a.total++,t.set(s,a)}});let s=Array.from(t.entries()).sort(([e],[t])=>e.localeCompare(t)).slice(-12),a=0,l=0,r=0,n=[],i=[],o=[];for(let[e,t]of s){a+=t.admins,l+=t.cashiers,r+=t.total;let s=g()(e).format("MMM YYYY");n.push({x:s,y:a}),i.push({x:s,y:l}),o.push({x:s,y:r})}return{adminSeries:n,cashierSeries:i,totalSeries:o}},[e]),s=[{name:"Total Users",data:t.totalSeries},{name:"Admins",data:t.adminSeries},{name:"Cashiers",data:t.cashierSeries}];return 0===t.totalSeries.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No user growth data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear as users register over time"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(N,{options:{chart:{type:"line",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9","#10B981"],stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:{formatter:function(e){return`${e} users`}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}}},series:s,type:"line",height:250})})},S=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\PaymentStatusChart.tsx -> react-apexcharts"]},ssr:!1}),P=({users:e})=>{let t=(0,l.useMemo)(()=>{let t=e.filter(e=>"admin"===e.role),s=0,a=0,l=0,r=0;return t.forEach(e=>{switch(e.paymentStatus){case"paid":s++;break;case"overdue":a++;break;case"pending":l++;break;default:r++}}),{labels:["Active (Paid)","Overdue","Pending","Inactive"],series:[s,a,l,r],total:s+a+l+r}},[e]),s={chart:{type:"bar",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981","#EF4444","#F59E0B","#6B7280"],plotOptions:{bar:{horizontal:!1,columnWidth:"60%",borderRadius:4}},dataLabels:{enabled:!0,style:{fontSize:"12px",fontWeight:600,colors:["#fff"]}},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},legend:{show:!1},tooltip:{y:{formatter:function(e,{dataPointIndex:s}){t.labels[s];let a=(e/t.total*100).toFixed(1);return`${e} admins (${a}%)`}}},xaxis:{categories:t.labels,axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"},rotate:-45}},yaxis:{labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}}},r=[{name:"Admin Count",data:t.series}];return 0===t.total?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No payment status data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when admin users are registered"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(S,{options:s,series:r,type:"bar",height:250})})},M=({className:e=""})=>{let{data:t,isLoading:s,error:l}=(0,n.P)(),{data:r,isLoading:f}=(0,i.VL)({page:1,limit:1e3,search:""}),p=t?.data,y=r?.data?.users||[];return(console.log("\uD83D\uDD0D SuperAdmin Dashboard Data:",{dashboardData:t,stats:p,usersCount:y.length,dashboardLoading:s,usersLoading:f,dashboardError:l}),s||f)?(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(h.A,{style:{fontSize:48},spin:!0})})}):l?(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})}):(0,a.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SuperAdmin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive overview of your POS system performance"})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(u.A,{title:"Total Revenue",value:Number(p?.revenue?.value)||0,prefix:"₵",precision:0,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Monthly recurring revenue"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(u.A,{title:"Active Stores",value:Number(p?.stores?.value)||0,precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Stores using the system"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(u.A,{title:"Admin Users",value:Number(p?.admins?.value)||0,precision:0,valueStyle:{color:"#722ed1",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"Paying admin accounts"})]})}),(0,a.jsx)(d.A,{xs:24,sm:12,lg:6,children:(0,a.jsxs)(x.A,{className:"text-center shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)(u.A,{title:"Total Users",value:Number(p?.users?.value)||0,precision:0,valueStyle:{color:"#fa8c16",fontSize:"24px",fontWeight:"bold"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:"All system users"})]})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Revenue Trends",className:"shadow-md h-96",children:(0,a.jsx)(j,{users:y})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Subscription Distribution",className:"shadow-md h-96",children:(0,a.jsx)(w,{users:y})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"User Growth",className:"shadow-md h-96",children:(0,a.jsx)(A,{users:y})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Payment Status Overview",className:"shadow-md h-96",children:(0,a.jsx)(P,{users:y})})})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(m,{className:"shadow-lg"})})]})};var D=s(10499),R=s(2274),C=s(70001),z=s(11855);let k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var E=s(78480),$=l.forwardRef(function(e,t){return l.createElement(E.A,(0,z.A)({},e,{ref:t,icon:k}))});let F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"};var O=l.forwardRef(function(e,t){return l.createElement(E.A,(0,z.A)({},e,{ref:t,icon:F}))});let T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var L=l.forwardRef(function(e,t){return l.createElement(E.A,(0,z.A)({},e,{ref:t,icon:T}))}),Y=s(45905),_=s(44599),B=s(92273),I=s(79334),W=s(25510);let G=()=>{let e=(0,B.d4)(e=>e.auth.user),t=(0,I.useRouter)(),{isActive:s,daysRemaining:l,needsPayment:r,status:n}=(0,W._)();if(console.log("\uD83D\uDCB3 PaymentStatusWidget - Status:",{userRole:e?.role,userPaymentStatus:e?.paymentStatus,isActive:s,daysRemaining:l,needsPayment:r,status:n,nextPaymentDue:e?.nextPaymentDue,lastPaymentDate:e?.lastPaymentDate}),e?.paymentStatus==="pending"&&console.log("\uD83D\uDEA8 PaymentStatusWidget - PENDING USER detected but showing as active! This is the UI bug!"),!e||"superadmin"===e.role)return null;let i=()=>{t.push("/payment")};return(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Subscription Status"}),(0,a.jsx)("button",{onClick:i,className:"text-blue-600 hover:text-blue-700 text-sm",children:"Manage"})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(_.A,{type:"circle",percent:(()=>{if(!e.nextPaymentDue||!e.lastPaymentDate)return 0;let t=g()(e.lastPaymentDate),s=g()(e.nextPaymentDue),a=g()(),l=s.diff(t,"day");if(l<=0)return 0;let r=a.diff(t,"day");return r<0?0:Math.min(100,Math.round(r/l*100))})(),status:"pending"===e.paymentStatus?"warning":"overdue"!==e.paymentStatus&&"inactive"!==e.paymentStatus&&("paid"!==e.paymentStatus||s)?null!==l&&l<=7?"warning":"success":"exception",strokeColor:"pending"===e.paymentStatus?"#faad14":"overdue"!==e.paymentStatus&&"inactive"!==e.paymentStatus&&("paid"!==e.paymentStatus||s)?null!==l&&l<=7?"#faad14":"#52c41a":"#f5222d",className:"mx-auto",trailColor:"#f0f0f0"}),(0,a.jsx)("div",{className:"text-center mt-4",children:"paid"===e.paymentStatus&&s?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-green-500 font-medium",children:"Your subscription is active"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵40/month)"}),null!==l&&(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:l>0?`Renews in ${l} day${l>1?"s":""}`:"Renewal due today"})]}):"pending"===e.paymentStatus?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-yellow-500 font-medium",children:"Your payment is pending verification"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Please wait while we verify your payment"})]}):"overdue"===e.paymentStatus?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-500 font-medium",children:"Your payment is overdue"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Please make a payment to continue using the system"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-red-500 font-medium",children:"Your subscription is inactive"}),(0,a.jsx)("p",{className:"text-gray-800 text-sm mt-1",children:"Monthly Plan (GH₵64/month)"}),(0,a.jsx)("span",{className:"block text-sm mt-1 text-gray-500",children:"Make a payment to continue using the system"})]})}),r&&(0,a.jsx)("button",{onClick:i,className:"w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors",children:"Manage Subscription"})]})]})},U=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\AdminSalesChart.tsx -> react-apexcharts"]},ssr:!1}),H=({sales:e})=>{let t=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Sales Chart - Sales found:",e.length);let t=[];for(let e=29;e>=0;e--){let s=g()().subtract(e,"day");t.push({dayKey:s.format("YYYY-MM-DD"),dayLabel:s.format("MMM DD"),salesCount:0,revenue:0})}e.forEach(e=>{if(e.transactionDate){let s=g()(e.transactionDate).format("YYYY-MM-DD"),a=t.find(e=>e.dayKey===s);a&&(a.salesCount++,a.revenue+=parseFloat(e.totalAmount||"0"))}});let s=t.map(e=>({x:e.dayLabel,y:e.salesCount})),a=t.map(e=>({x:e.dayLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Sales chart data:",{salesData:s.slice(-7),revenueData:a.slice(-7)}),{salesData:s,revenueData:a}},[e]),s=[{name:"Sales Count",type:"line",data:t.salesData},{name:"Revenue",type:"line",yAxisIndex:1,data:t.revenueData}];return 0===t.salesData.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No sales data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(U,{options:{chart:{type:"line",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9"],stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:[{formatter:function(e){return`${e} sales`}},{formatter:function(e){return`₵${e.toFixed(2)}`}}]},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"},rotate:-45}},yaxis:[{title:{text:"Number of Sales",style:{fontSize:"12px"}},labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}},{opposite:!0,title:{text:"Revenue (₵)",style:{fontSize:"12px"}},labels:{formatter:function(e){return`₵${e.toFixed(0)}`},style:{fontSize:"12px"}}}]},series:s,type:"line",height:250})})},V=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\AdminProductsChart.tsx -> react-apexcharts"]},ssr:!1}),q=({products:e,sales:t})=>{let s=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Products Chart - Products found:",e.length,"Sales found:",t.length),console.log("\uD83D\uDCCA Sample sale structure:",t[0]),console.log("\uD83D\uDCCA Sample product structure:",e[0]);let s=new Map;e.forEach(e=>{let t=String(e.id||e.productId||e._id);s.set(t,{name:e.name||e.productName||"Unknown Product",count:0,revenue:0,productId:t})}),console.log("\uD83D\uDCCA Initialized product sales map:",Array.from(s.entries()).slice(0,3)),t.forEach((e,t)=>{console.log(`📊 Processing sale ${t}:`,{saleId:e.id,saleItems:e.saleItems,items:e.items,products:e.products});let a=e.saleItems||e.items||e.products||[];Array.isArray(a)&&a.forEach((e,t)=>{console.log(`📊 Processing item ${t}:`,e);let a=String(e.productId||e.product_id||e.id||e.product?.id||e.product?.productId||""),l=Number(e.quantity||e.qty||e.amount||1),r=parseFloat(e.totalPrice||e.total_price||e.price||e.total||e.amount||"0");if(console.log(`📊 Extracted data:`,{productId:a,quantity:l,totalPrice:r}),a&&s.has(a)){let e=s.get(a);e.count+=l,e.revenue+=r,console.log(`📊 Updated product ${a}:`,e)}else console.log(`📊 Product ${a} not found in products map`)})});let a=Array.from(s.values()).filter(e=>e.count>0).sort((e,t)=>t.count-e.count);console.log("\uD83D\uDCCA Products with sales:",a);let l=a.slice(0,15),r=l.map(e=>e.name.length>20?e.name.substring(0,17)+"...":e.name),n=l.map(e=>e.count),i=l.map(e=>e.revenue);return console.log("\uD83D\uDCC8 Final chart data:",{totalProducts:e.length,productsWithSales:a.length,topProductsShown:l.length,categories:r,salesData:n,revenueData:i}),{categories:r,salesData:n,revenueData:i,totalProductsWithSales:a.length,totalProducts:e.length}},[e,t]),r=s.categories.length>8,n={chart:{type:"bar",height:r?Math.max(300,25*s.categories.length):300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#5750F1","#0ABEF9"],plotOptions:{bar:{horizontal:r,columnWidth:r?"70%":"60%",borderRadius:4}},dataLabels:{enabled:!1},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},legend:{position:"top",horizontalAlign:"left",fontSize:"14px",fontFamily:"inherit",markers:{size:8}},tooltip:{shared:!0,intersect:!1,y:[{formatter:function(e){return`${e} units sold`}},{formatter:function(e){return`₵${e.toFixed(2)} revenue`}}]},xaxis:{categories:s.categories,axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"11px"},rotate:r?0:-45,maxHeight:r?void 0:60,trim:!0}},yaxis:[{title:{text:"Units Sold",style:{fontSize:"12px"}},labels:{formatter:function(e){return Math.round(e).toString()},style:{fontSize:"12px"}}},{opposite:!0,title:{text:"Revenue (₵)",style:{fontSize:"12px"}},labels:{formatter:function(e){return`₵${e.toFixed(0)}`},style:{fontSize:"12px"}}}]},i=[{name:"Units Sold",data:s.salesData},{name:"Revenue",data:s.revenueData}];if(0===s.categories.length)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No product sales data available"}),(0,a.jsxs)("p",{className:"text-sm",children:["You have ",s.totalProducts," products, but none have been sold yet"]}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when products are sold"})]})});let o=r?Math.max(250,25*s.categories.length):230;return(0,a.jsxs)("div",{className:r?"auto":"h-64",style:{minHeight:r?o+50:void 0},children:[(0,a.jsxs)("div",{className:"mb-2 text-xs text-gray-500 text-center",children:["Showing top ",s.categories.length," of ",s.totalProductsWithSales," products with sales (",s.totalProducts," total products)"]}),(0,a.jsx)(V,{options:n,series:i,type:"bar",height:o})]})},J=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\AdminRevenueChart.tsx -> react-apexcharts"]},ssr:!1}),K=({sales:e})=>{let t=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Revenue Chart - Sales found:",e.length);let t=[];for(let e=11;e>=0;e--){let s=g()().subtract(e,"month");t.push({monthKey:s.format("YYYY-MM"),monthLabel:s.format("MMM YYYY"),revenue:0,salesCount:0})}e.forEach(e=>{if(e.transactionDate){let s=g()(e.transactionDate).format("YYYY-MM"),a=t.find(e=>e.monthKey===s);a&&(a.revenue+=parseFloat(e.totalAmount||"0"),a.salesCount++)}});let s=t.map(e=>({x:e.monthLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Revenue chart data:",s),s},[e]),s=[{name:"Revenue",data:t}];return 0===t.length||t.every(e=>0===e.y)?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No revenue data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(J,{options:{chart:{type:"area",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.7,opacityTo:.1,stops:[0,90,100]}},stroke:{curve:"smooth",width:3},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}},dataLabels:{enabled:!1},tooltip:{y:{formatter:function(e){return`₵${e.toFixed(2)}`}}},xaxis:{axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{fontSize:"12px"}}},yaxis:{labels:{formatter:function(e){return`₵${e.toFixed(0)}`},style:{fontSize:"12px"}}}},series:s,type:"area",height:250})})},Q=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\ProfitLossChart.tsx -> react-apexcharts"]},ssr:!1}),X=({stats:e,formatLargeNumber:t})=>{let s=[{name:"Revenue",data:[e.revenue.value]},{name:"COGS",data:[e.cogs.value]},{name:"Expenses",data:[e.expenses.value]},{name:"Profit",data:[e.profit.value]}],l=e=>t?t(e):`₵${e.toLocaleString()}`;return(0,a.jsxs)("div",{className:"h-80",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,a.jsx)("div",{className:"text-green-600 font-semibold",children:"Profit Margin"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-green-700",children:[e.profitMargin.value.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,a.jsx)("div",{className:"text-blue-600 font-semibold",children:"Net Profit"}),(0,a.jsx)("div",{className:"text-lg font-bold text-blue-700",children:l(e.profit.value)})]})]})}),(0,a.jsx)(Q,{options:{chart:{type:"bar",height:300,toolbar:{show:!1},fontFamily:"inherit"},colors:["#10B981","#EF4444","#F59E0B","#3B82F6"],plotOptions:{bar:{horizontal:!1,columnWidth:"60%",borderRadius:4}},dataLabels:{enabled:!0,formatter:function(e){return l(e)},style:{fontSize:"12px",fontWeight:"bold"}},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:["Financial Overview"],axisBorder:{show:!1},axisTicks:{show:!1}},yaxis:{labels:{formatter:function(e){return l(e)}}},fill:{opacity:1},tooltip:{y:{formatter:function(e){return l(e)}}},legend:{position:"top",horizontalAlign:"center"},grid:{strokeDashArray:5,yaxis:{lines:{show:!0}}}},series:s,type:"bar",height:250})]})},Z=p()(async()=>{},{loadableGenerated:{modules:["components\\Dashboard\\Charts\\DailySalesChart.tsx -> react-apexcharts"]},ssr:!1}),ee=({sales:e})=>{let t=(0,l.useMemo)(()=>{console.log("\uD83D\uDCCA Daily Sales Chart - Sales found:",e.length);let t=[];for(let e=6;e>=0;e--){let s=g()().subtract(e,"day");t.push({dayKey:s.format("YYYY-MM-DD"),dayLabel:s.format("ddd DD"),salesCount:0,revenue:0})}e.forEach(e=>{if(e.transactionDate){let s=g()(e.transactionDate).format("YYYY-MM-DD"),a=t.find(e=>e.dayKey===s);a&&(a.salesCount++,a.revenue+=parseFloat(e.totalAmount||"0"))}});let s=t.map(e=>({x:e.dayLabel,y:e.salesCount})),a=t.map(e=>({x:e.dayLabel,y:e.revenue}));return console.log("\uD83D\uDCC8 Daily Sales chart data:",{salesData:s,revenueData:a}),{salesData:s,revenueData:a}},[e]),s=[{name:"Sales Count",type:"column",data:t.salesData},{name:"Revenue (₵)",type:"line",data:t.revenueData}],r={chart:{height:350,type:"line",stacked:!1,toolbar:{show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1},stroke:{width:[0,4],curve:"smooth"},plotOptions:{bar:{columnWidth:"50%"}},fill:{opacity:[.85,1],gradient:{inverseColors:!1,shade:"light",type:"vertical",opacityFrom:.85,opacityTo:.55,stops:[0,100,100,100]}},labels:t.salesData.map(e=>e.x),markers:{size:0},xaxis:{type:"category",labels:{style:{fontSize:"12px",fontWeight:500}}},yaxis:[{title:{text:"Sales Count",style:{color:"#1f77b4",fontSize:"14px",fontWeight:600}},labels:{style:{colors:"#1f77b4"},formatter:e=>Math.round(e).toString()}},{opposite:!0,title:{text:"Revenue (₵)",style:{color:"#ff7f0e",fontSize:"14px",fontWeight:600}},labels:{style:{colors:"#ff7f0e"},formatter:e=>`₵${e.toFixed(0)}`}}],tooltip:{shared:!0,intersect:!1,y:[{formatter:e=>`${e} sales`},{formatter:e=>`₵${e.toFixed(2)}`}]},legend:{horizontalAlign:"left",offsetX:40},colors:["#1f77b4","#ff7f0e"],grid:{borderColor:"#f1f1f1",strokeDashArray:3}};return 0===t.salesData.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{children:"No daily sales data available"}),(0,a.jsx)("p",{className:"text-sm",children:"Chart will appear when sales are made"})]})}):(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(Z,{options:r,series:s,type:"line",height:250})})},et=({className:e=""})=>{let{data:t,isLoading:s,error:l}=(0,n.P)(),{data:r,isLoading:i}=(0,D.JZ)({page:1,limit:1e3,search:""}),{data:m,isLoading:f}=(0,R.r3)({page:1,limit:1e3,search:""}),p=t?.data,y=r?.data?.sales||[],g=m?.data?.products||[];if(s||i||f)return(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(h.A,{style:{fontSize:48},spin:!0})})});if(l)return(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})});if(!p)return(0,a.jsx)("div",{className:"text-center text-gray-500 p-8",children:(0,a.jsx)("p",{children:"No dashboard data available."})});y.length,y.reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0),g.length;let v=e=>{let t=(100*e).toFixed(1),s=e>=0;return{value:`${s?"+":""}${t}%`,color:s?"#3f8600":"#cf1322",icon:s?(0,a.jsx)($,{}):(0,a.jsx)(O,{})}};function j(e,t=!1){let s=t?"₵":"";return e>=1e9?`${s}${(e/1e9).toFixed(1)}B`:e>=1e6?`${s}${(e/1e6).toFixed(1)}M`:e>=1e3?`${s}${(e/1e3).toFixed(1)}k`:t?`₵${e.toLocaleString()}`:e.toLocaleString()}function b({value:e,isMoney:t=!1,isPercent:s=!1}){let l=j(e,t),r="";return(l.includes("k")&&(r="k = thousand"),l.includes("M")&&(r="M = million"),l.includes("B")&&(r="B = billion"),s)?(0,a.jsxs)(a.Fragment,{children:[l," %"]}):r?(0,a.jsx)(C.A,{title:r,children:l}):(0,a.jsx)(a.Fragment,{children:l})}return(0,a.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your store operations and track performance"})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Revenue (Team) - This Month",value:p.revenue.value,valueRender:()=>(0,a.jsx)(b,{value:p.revenue.value,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:[(0,a.jsx)(L,{className:"mr-1"}),"Admin + Cashiers"]}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.revenue.growthRate).color},children:[v(p.revenue.growthRate).icon," ",v(p.revenue.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Total Sales - This Month",value:p.sales.value,valueRender:()=>(0,a.jsx)(b,{value:p.sales.value}),precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Completed transactions"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.sales.growthRate).color},children:[v(p.sales.growthRate).icon," ",v(p.sales.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Net Profit - This Month",value:p.profit.value,valueRender:()=>(0,a.jsx)(b,{value:p.profit.value,isMoney:!0}),precision:2,valueStyle:{color:p.profit.value>=0?"#3f8600":"#cf1322",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"After expenses & COGS"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.profit.growthRate).color},children:[v(p.profit.growthRate).icon," ",v(p.profit.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Products (All Time)",value:p.products.value,valueRender:()=>(0,a.jsx)(b,{value:p.products.value}),precision:0,valueStyle:{color:"#fa8c16",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"In inventory"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.products.growthRate).color},children:[v(p.products.growthRate).icon," ",v(p.products.growthRate).value]})]})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Cashiers - This Month",value:p.cashiers.value,valueRender:()=>(0,a.jsx)(b,{value:p.cashiers.value}),precision:0,valueStyle:{color:"#722ed1",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Team members"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.cashiers.growthRate).color},children:[v(p.cashiers.growthRate).icon," ",v(p.cashiers.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Expenses - This Month",value:p.expenses.value,valueRender:()=>(0,a.jsx)(b,{value:p.expenses.value,isMoney:!0}),precision:2,valueStyle:{color:"#cf1322",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Total expenses"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.expenses.growthRate).color},children:[v(p.expenses.growthRate).icon," ",v(p.expenses.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"COGS - This Month",value:p.cogs.value,valueRender:()=>(0,a.jsx)(b,{value:p.cogs.value,isMoney:!0}),precision:2,valueStyle:{color:"#fa541c",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Cost of goods sold"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.cogs.growthRate).color},children:[v(p.cogs.growthRate).icon," ",v(p.cogs.growthRate).value]})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-full",children:[(0,a.jsx)(u.A,{title:"Profit Margin - This Month",value:p.profitMargin.value,valueRender:()=>(0,a.jsx)(b,{value:p.profitMargin.value,isPercent:!0}),precision:1,valueStyle:{color:p.profitMargin.value>=0?"#3f8600":"#cf1322",fontSize:"20px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Profit percentage"}),(0,a.jsxs)("div",{className:"text-xs mt-1 text-center",style:{color:v(p.profitMargin.growthRate).color},children:[v(p.profitMargin.growthRate).icon," ",v(p.profitMargin.growthRate).value]})]})})})]}),(0,a.jsx)(c.A,{gutter:[16,16],className:"mb-6",children:(0,a.jsx)(d.A,{xs:24,children:(0,a.jsxs)(x.A,{title:"Daily Sales (Last 7 Days)",className:"shadow-md h-96",children:[(0,a.jsx)(ee,{sales:y}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(Y.A,{className:"mr-1"}),"Sales count and revenue for the past week"]})]})})}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Sales Trends (30 Days)",className:"shadow-md h-96",children:(0,a.jsx)(H,{sales:y})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsxs)(x.A,{title:"Revenue Over Time (Team)",className:"shadow-md h-96",children:[(0,a.jsx)(K,{sales:y}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(L,{className:"mr-1"}),"Includes admin and cashier sales"]})]})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Profit & Loss Analysis (This Month)",className:"shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col",bodyStyle:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,a.jsx)(X,{stats:p,formatLargeNumber:j})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Product Performance",className:"shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col",bodyStyle:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,a.jsx)(q,{products:g,sales:y})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Payment Status",className:"shadow-md h-96",children:(0,a.jsx)(G,{})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"Team Performance (This Month)",className:"shadow-md h-96",children:(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 h-full",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j(p.sales.value)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Sales"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:v(p.sales.growthRate).color},children:[v(p.sales.growthRate).icon," ",v(p.sales.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j(p.revenue.value)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Team Revenue"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:v(p.revenue.growthRate).color},children:[v(p.revenue.growthRate).icon," ",v(p.revenue.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:p.cashiers.value}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Cashiers"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:v(p.cashiers.growthRate).color},children:[v(p.cashiers.growthRate).icon," ",v(p.cashiers.growthRate).value]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:p.products.value}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Products"}),(0,a.jsxs)("div",{className:"text-xs mt-1",style:{color:v(p.products.growthRate).color},children:[v(p.products.growthRate).icon," ",v(p.products.growthRate).value]})]})]})})})})]})]})};var es=s(3117),ea=s(37287);let el={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V160a16 16 0 0116-16zm-52 68H212v200h200zm493.33 87.69a16 16 0 010 22.62L724.31 503.33a16 16 0 01-22.62 0L520.67 322.31a16 16 0 010-22.62l181.02-181.02a16 16 0 0122.62 0zm-84.85 11.3L713 203.53 605.52 311 713 418.48zM464 544a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H212v200h200zm452-68a16 16 0 0116 16v304a16 16 0 01-16 16H560a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H612v200h200z"}}]},name:"product",theme:"outlined"};var er=l.forwardRef(function(e,t){return l.createElement(E.A,(0,z.A)({},e,{ref:t,icon:el}))}),en=s(24648),ei=s(94262),eo=s(19716),ec=s.n(eo);g().extend(ec());let ed=({className:e=""})=>{let t=(0,B.d4)(e=>e.auth.user),s=(0,I.useRouter)(),[r,i]=(0,l.useState)(!1),{data:m,isLoading:f,error:p}=(0,n.P)(),{data:y,isLoading:v}=(0,D.JZ)({page:1,limit:1e3,search:""}),{data:j,isLoading:b}=(0,R.r3)({page:1,limit:1e3,search:""});m?.data;let w=y?.data?.sales||[];j?.data?.products;let N=w.filter(e=>e.createdBy===t?.id);if(f||v||b)return(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(h.A,{style:{fontSize:48},spin:!0})})});if(p)return(0,a.jsx)("div",{className:"text-center text-red-500 p-8",children:(0,a.jsx)("p",{children:"Error loading dashboard data. Please try again."})});let A=g()().startOf("month"),S=g()().endOf("month"),P=N.filter(e=>g()(e.transactionDate).isBetween(A,S,null,"[]")),M=P.reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0),C=P.length,z=C>0?M/C:0,k=g()(),E=N.filter(e=>g()(e.transactionDate).isSame(k,"day")).reduce((e,t)=>e+parseFloat(t.totalAmount||"0"),0);function $({value:e,isMoney:t=!1}){let s=function(e,t=!1){let s=t?"₵":"";return e>=1e9?`${s}${(e/1e9).toFixed(1)}B`:e>=1e6?`${s}${(e/1e6).toFixed(1)}M`:e>=1e3?`${s}${(e/1e3).toFixed(1)}k`:t?`₵${e.toLocaleString()}`:e.toLocaleString()}(e,t),l="";return s.includes("k")&&(l="k = thousand"),s.includes("M")&&(l="M = million"),s.includes("B")&&(l="B = billion"),l?(0,a.jsx)("span",{title:l,children:s}):(0,a.jsx)(a.Fragment,{children:s})}return(0,a.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",t?.name,"!"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Ready to serve customers and process sales"})]})})}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",justify:"center",align:"middle",children:[(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(u.A,{title:"Today's Revenue",value:E,valueRender:()=>(0,a.jsx)($,{value:E,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Earnings for today"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(u.A,{title:"Revenue (This Month)",value:M,valueRender:()=>(0,a.jsx)($,{value:M,isMoney:!0}),precision:2,valueStyle:{color:"#3f8600",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Earnings for this month"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(u.A,{title:"Sales (This Month)",value:C,valueRender:()=>(0,a.jsx)($,{value:C}),precision:0,valueStyle:{color:"#1890ff",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Transactions this month"})]})})}),(0,a.jsx)(d.A,{xs:24,sm:12,md:8,lg:6,style:{display:"flex"},children:(0,a.jsx)(x.A,{className:"h-full flex flex-col justify-center w-full min-h-[170px]",bodyStyle:{padding:16,height:"100%"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,a.jsx)(u.A,{title:"Average Order (This Month)",value:z,valueRender:()=>(0,a.jsx)($,{value:z,isMoney:!0}),precision:2,valueStyle:{color:"#722ed1",fontSize:"24px",fontWeight:"bold",width:"100%",textAlign:"center"}}),(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-2 text-center",children:"Per transaction (monthly)"})]})})})]}),(0,a.jsxs)(c.A,{gutter:[16,16],className:"mb-6",children:[(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"My Sales Performance",className:"shadow-md h-96",children:(0,a.jsx)(H,{sales:N})})}),(0,a.jsx)(d.A,{xs:24,lg:12,children:(0,a.jsx)(x.A,{title:"My Revenue Trends",className:"shadow-md h-96",children:(0,a.jsx)(K,{sales:N})})})]}),(0,a.jsx)(c.A,{gutter:[16,16],className:"mb-6",children:(0,a.jsx)(d.A,{xs:24,children:(0,a.jsx)(x.A,{title:"Quick Actions",className:"shadow-md",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(es.Ay,{type:"primary",size:"large",className:"h-20 flex flex-col items-center justify-center bg-green-600 hover:bg-green-700 border-green-600",onClick:()=>i(!0),children:[(0,a.jsx)(ea.A,{className:"text-xl mb-2"}),"New Sale"]}),(0,a.jsxs)(es.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-blue-50 border-blue-300",onClick:()=>s.push("/dashboard/sales"),children:[(0,a.jsx)(Y.A,{className:"text-xl mb-2"}),"View Sales"]}),(0,a.jsxs)(es.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-purple-50 border-purple-300",onClick:()=>s.push("/dashboard/products"),children:[(0,a.jsx)(er,{className:"text-xl mb-2"}),"Products"]}),(0,a.jsxs)(es.Ay,{size:"large",className:"h-20 flex flex-col items-center justify-center hover:bg-gray-50 border-gray-300",onClick:()=>s.push("/dashboard/profile"),children:[(0,a.jsx)(en.A,{className:"text-xl mb-2"}),"Profile"]})]})})})}),(0,a.jsx)(ei.A,{isOpen:r,onClose:()=>i(!1),onSuccess:()=>{i(!1)}})]})},ex=({extractTimeFrame:e})=>{let{user:t,isSuperAdmin:s,isAdmin:l,isCashier:n}=(0,r.A)();return t?s()?(0,a.jsx)(M,{}):l()?(0,a.jsx)(et,{}):n()?(0,a.jsx)(ed,{}):(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-5 shadow-md",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-gray-800",children:["Welcome, ",t.name,"!"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Your role (",t.role,') doesn"t have a specific dashboard view.']}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Please contact your administrator for assistance."})]}):null},eu=({selected_time_frame:e})=>{var t;let{user:s}=(0,B.d4)(e=>e.auth);if((0,l.useEffect)(()=>{console.log("DashboardContent - Mounted with params:",{selected_time_frame:e,user:s?.name,role:s?.role})},[e,s]),!s)return(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{children:"Please log in to view the dashboard."})]})});let r=(t=e||void 0,e=>t?.split(",").find(t=>t.includes(e)));return(0,a.jsx)("div",{className:"dashboard-content w-full overflow-hidden",children:(0,a.jsx)(ex,{extractTimeFrame:r})})};function eh(){let e=(0,I.useSearchParams)(),t=e?.get("selected_time_frame")||null,[s,r]=(0,l.useState)(!0);return s?(0,a.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,a.jsx)(o.A,{indicator:(0,a.jsx)(h.A,{style:{fontSize:24},spin:!0})})}):(0,a.jsx)(eu,{selected_time_frame:t})}},98776:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(45512),l=s(58009),r=s(3117),n=s(97071);let i=({isOpen:e,onClose:t,title:s,children:i,width:o="400px",footer:c,fullWidth:d=!1})=>{let[x,u]=(0,l.useState)(!1),[h,m]=(0,l.useState)(!1),[f,p]=(0,l.useState)(1024);if((0,l.useEffect)(()=>{let e=()=>{p(window.innerWidth)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,l.useEffect)(()=>{if(console.log("SlidingPanel - isOpen changed:",e,"title:",s),e)m(!0),console.log("SlidingPanel - Setting isRendered to true"),setTimeout(()=>{u(!0),console.log("SlidingPanel - Setting isVisible to true")},50);else{u(!1),console.log("SlidingPanel - Setting isVisible to false");let e=setTimeout(()=>{m(!1),console.log("SlidingPanel - Setting isRendered to false")},300);return()=>clearTimeout(e)}},[e,s]),!h)return null;let y="Point of Sale"===s||d||"100vw"===o;return(0,a.jsxs)("div",{className:`fixed inset-0 z-[1000] overflow-hidden ${y?"sales-panel-container":""}`,children:[(0,a.jsx)("div",{className:`absolute inset-0 bg-black transition-opacity duration-300 ${x?"opacity-50":"opacity-0"}`,onClick:t}),(0,a.jsxs)("div",{className:`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${x?"translate-x-0":"translate-x-full"}`,style:{width:"Point of Sale"===s||d||"100vw"===o||f<640?"100vw":f<1024?"500px":"string"==typeof o&&o.includes("px")&&parseInt(o)>600?"600px":o},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-800 truncate",children:s}),(0,a.jsx)(r.Ay,{type:"text",icon:(0,a.jsx)(n.A,{style:{color:"#333"}}),onClick:t,"aria-label":"Close panel",style:{color:"#333",borderColor:"transparent",background:"transparent"}})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 pt-6 bg-white",children:i}),c&&(0,a.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 bg-gray-50",children:c})]})]})}},60636:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(92273),l=s(25510),r=s(97245),n=s(42211);let i=()=>{let e=(0,a.wA)(),{user:t,accessToken:s}=(0,a.d4)(e=>e.auth),i=(0,l._)(),{refetch:o}=(0,r.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!s,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!s,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let a=await o();console.log("useAuth - Refetch result:",a);let l=a.data;if(l?.success&&l?.data){console.log("useAuth - API response data:",l.data);let a=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let r=l.data.phone||t.phone||"",i=l.data.createdAt||t.createdAt||"",o=l.data.lastPaymentDate||t.lastPaymentDate||void 0,c=l.data.nextPaymentDue||t.nextPaymentDue||null,d=l.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:l.data.phone,userPhone:t.phone,finalPhone:r,apiCreatedAt:l.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:i,apiLastPaymentDate:l.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:o,apiNextPaymentDue:l.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:l.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let x={...l.data,phone:r,createdAt:i,lastPaymentDate:o,nextPaymentDue:c,createdBy:d,paymentStatus:a};console.log("useAuth - Updating Redux store with:",x),console.log("useAuth - Using current access token:",s?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,n.gV)({user:x,accessToken:s||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",l?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:s,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&i.needsPayment,paymentStatus:i,refreshUser:d}}},68203:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7764,1257,7325,984,5482,106,4286,4262],()=>s(7182));module.exports=a})();