"use strict";exports.id=7325,exports.ids=[7325],exports.modules={77953:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(11855),r=n(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};var l=n(78480);let a=r.forwardRef(function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))})},14092:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(58009),r=n.n(o),i=n(27343),l=n(78959);let a=e=>{let{componentName:t}=e,{getPrefixCls:n}=(0,o.useContext)(i.QO),a=n("empty");switch(t){case"Table":case"List":return r().createElement(l.A,{image:l.A.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return r().createElement(l.A,{image:l.A.PRESENTED_IMAGE_SIMPLE,className:`${a}-small`});case"Table.filter":return null;default:return r().createElement(l.A,null)}}},78959:(e,t,n)=>{n.d(t,{A:()=>b});var o=n(58009),r=n(56073),i=n.n(r),l=n(76155),a=n(43891),c=n(39772),u=n(13662),s=n(10941);let d=e=>{let{componentCls:t,margin:n,marginXS:o,marginXL:r,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:o,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:r,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},f=(0,u.OF)("Empty",e=>{let{componentCls:t,controlHeightLG:n,calc:o}=e;return[d((0,s.oX)(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()}))]});var p=n(27343),m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let v=o.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,l.A)("Empty"),n=new a.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return o.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{fill:"none",fillRule:"evenodd"},o.createElement("g",{transform:"translate(24 31.67)"},o.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),o.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),o.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),o.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),o.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),o.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),o.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},o.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),o.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},null),g=o.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,l.A)("Empty"),{colorFill:n,colorFillTertiary:r,colorFillQuaternary:i,colorBgContainer:u}=e,{borderColor:s,shadowColor:d,contentColor:f}=(0,o.useMemo)(()=>({borderColor:new a.Y(n).onBackground(u).toHexString(),shadowColor:new a.Y(r).onBackground(u).toHexString(),contentColor:new a.Y(i).onBackground(u).toHexString()}),[n,r,i,u]);return o.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},o.createElement("ellipse",{fill:d,cx:"32",cy:"33",rx:"32",ry:"7"}),o.createElement("g",{fillRule:"nonzero",stroke:s},o.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),o.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:f}))))},null),h=e=>{let{className:t,rootClassName:n,prefixCls:r,image:a=v,description:c,children:u,imageStyle:s,style:d,classNames:h,styles:b}=e,A=m(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:y,direction:w,className:E,style:S,classNames:C,styles:x}=(0,p.TP)("empty"),$=y("empty",r),[I,O,M]=f($),[R]=(0,l.A)("Empty"),z=void 0!==c?c:null==R?void 0:R.description,D=null;return D="string"==typeof a?o.createElement("img",{alt:"string"==typeof z?z:"empty",src:a}):a,I(o.createElement("div",Object.assign({className:i()(O,M,$,E,{[`${$}-normal`]:a===g,[`${$}-rtl`]:"rtl"===w},t,n,C.root,null==h?void 0:h.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},x.root),S),null==b?void 0:b.root),d)},A),o.createElement("div",{className:i()(`${$}-image`,C.image,null==h?void 0:h.image),style:Object.assign(Object.assign(Object.assign({},s),x.image),null==b?void 0:b.image)},D),z&&o.createElement("div",{className:i()(`${$}-description`,C.description,null==h?void 0:h.description),style:Object.assign(Object.assign({},x.description),null==b?void 0:b.description)},z),u&&o.createElement("div",{className:i()(`${$}-footer`,C.footer,null==h?void 0:h.footer),style:Object.assign(Object.assign({},x.footer),null==b?void 0:b.footer)},u)))};h.PRESENTED_IMAGE_DEFAULT=v,h.PRESENTED_IMAGE_SIMPLE=g;let b=h},7325:(e,t,n)=>{n.d(t,{A:()=>te});var o=n(58009),r=n.n(o),i=n(56073),l=n.n(i),a=n(11855),c=n(43984),u=n(65074),s=n(12992),d=n(7770),f=n(49543),p=n(97549),m=n(61849),v=n(67010),g=n(55977),h=n(45022),b=n(80799);let A=function(e){var t=e.className,n=e.customizeIcon,r=e.customizeIconProps,i=e.children,a=e.onMouseDown,c=e.onClick,u="function"==typeof n?n(r):n;return o.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null==a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},void 0!==u?u:o.createElement("span",{className:l()(t.split(/\s+/).map(function(e){return"".concat(e,"-icon")}))},i))};var y=function(e,t,n,o,i){var l=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,u=r().useMemo(function(){return"object"===(0,p.A)(o)?o.clearIcon:i||void 0},[o,i]);return{allowClear:r().useMemo(function(){return!l&&!!o&&(!!n.length||!!a)&&!("combobox"===c&&""===a)},[o,l,n.length,a,c]),clearIcon:r().createElement(A,{className:"".concat(e,"-clear"),onMouseDown:t,customizeIcon:u},"\xd7")}},w=o.createContext(null);function E(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,t=o.useRef(null),n=o.useRef(null);return o.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]),[function(){return t.current},function(o){(o||null===t.current)&&(t.current=o),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}]}var S=n(73924),C=n(90365),x=n(54732),$=o.forwardRef(function(e,t){var n,r=e.prefixCls,i=e.id,a=e.inputElement,c=e.disabled,u=e.tabIndex,d=e.autoFocus,f=e.autoComplete,p=e.editable,m=e.activeDescendantId,g=e.value,h=e.maxLength,A=e.onKeyDown,y=e.onMouseDown,w=e.onChange,E=e.onPaste,S=e.onCompositionStart,C=e.onCompositionEnd,x=e.onBlur,$=e.open,I=e.attrs,O=a||o.createElement("input",null),M=O,R=M.ref,z=M.props,D=z.onKeyDown,H=z.onChange,N=z.onMouseDown,T=z.onCompositionStart,B=z.onCompositionEnd,P=z.onBlur,L=z.style;return(0,v.$e)(!("maxLength"in O.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),O=o.cloneElement(O,(0,s.A)((0,s.A)((0,s.A)({type:"search"},z),{},{id:i,ref:(0,b.K4)(t,R),disabled:c,tabIndex:u,autoComplete:f||"off",autoFocus:d,className:l()("".concat(r,"-selection-search-input"),null===(n=O)||void 0===n||null===(n=n.props)||void 0===n?void 0:n.className),role:"combobox","aria-expanded":$||!1,"aria-haspopup":"listbox","aria-owns":"".concat(i,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(i,"_list"),"aria-activedescendant":$?m:void 0},I),{},{value:p?g:"",maxLength:h,readOnly:!p,unselectable:p?null:"on",style:(0,s.A)((0,s.A)({},L),{},{opacity:p?null:0}),onKeyDown:function(e){A(e),D&&D(e)},onMouseDown:function(e){y(e),N&&N(e)},onChange:function(e){w(e),H&&H(e)},onCompositionStart:function(e){S(e),T&&T(e)},onCompositionEnd:function(e){C(e),B&&B(e)},onPaste:E,onBlur:function(e){x(e),P&&P(e)}}))});function I(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var O="undefined"!=typeof window&&window.document&&window.document.documentElement;function M(e){return["string","number"].includes((0,p.A)(e))}function R(e){var t=void 0;return e&&(M(e.title)?t=e.title.toString():M(e.label)&&(t=e.label.toString())),t}function z(e){var t;return null!==(t=e.key)&&void 0!==t?t:e.value}var D=function(e){e.preventDefault(),e.stopPropagation()};let H=function(e){var t,n,r=e.id,i=e.prefixCls,a=e.values,c=e.open,s=e.searchValue,f=e.autoClearSearchValue,p=e.inputRef,m=e.placeholder,v=e.disabled,g=e.mode,h=e.showSearch,b=e.autoFocus,y=e.autoComplete,w=e.activeDescendantId,E=e.tabIndex,S=e.removeIcon,I=e.maxTagCount,M=e.maxTagTextLength,H=e.maxTagPlaceholder,N=void 0===H?function(e){return"+ ".concat(e.length," ...")}:H,T=e.tagRender,B=e.onToggleOpen,P=e.onRemove,L=e.onInputChange,k=e.onInputPaste,j=e.onInputKeyDown,W=e.onInputMouseDown,F=e.onInputCompositionStart,V=e.onInputCompositionEnd,_=e.onInputBlur,K=o.useRef(null),Y=(0,o.useState)(0),X=(0,d.A)(Y,2),q=X[0],G=X[1],U=(0,o.useState)(!1),Q=(0,d.A)(U,2),J=Q[0],Z=Q[1],ee="".concat(i,"-selection"),et=c||"multiple"===g&&!1===f||"tags"===g?s:"",en="tags"===g||"multiple"===g&&!1===f||h&&(c||J);t=function(){G(K.current.scrollWidth)},n=[et],O?o.useLayoutEffect(t,n):o.useEffect(t,n);var eo=function(e,t,n,r,i){return o.createElement("span",{title:R(e),className:l()("".concat(ee,"-item"),(0,u.A)({},"".concat(ee,"-item-disabled"),n))},o.createElement("span",{className:"".concat(ee,"-item-content")},t),r&&o.createElement(A,{className:"".concat(ee,"-item-remove"),onMouseDown:D,onClick:i,customizeIcon:S},"\xd7"))},er=function(e,t,n,r,i,l){return o.createElement("span",{onMouseDown:function(e){D(e),B(!c)}},T({label:t,value:e,disabled:n,closable:r,onClose:i,isMaxTag:!!l}))},ei=o.createElement("div",{className:"".concat(ee,"-search"),style:{width:q},onFocus:function(){Z(!0)},onBlur:function(){Z(!1)}},o.createElement($,{ref:p,open:c,prefixCls:i,id:r,inputElement:null,disabled:v,autoFocus:b,autoComplete:y,editable:en,activeDescendantId:w,value:et,onKeyDown:j,onMouseDown:W,onChange:L,onPaste:k,onCompositionStart:F,onCompositionEnd:V,onBlur:_,tabIndex:E,attrs:(0,C.A)(e,!0)}),o.createElement("span",{ref:K,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},et,"\xa0")),el=o.createElement(x.A,{prefixCls:"".concat(ee,"-overflow"),data:a,renderItem:function(e){var t=e.disabled,n=e.label,o=e.value,r=!v&&!t,i=n;if("number"==typeof M&&("string"==typeof n||"number"==typeof n)){var l=String(i);l.length>M&&(i="".concat(l.slice(0,M),"..."))}var a=function(t){t&&t.stopPropagation(),P(e)};return"function"==typeof T?er(o,i,t,r,a):eo(e,i,t,r,a)},renderRest:function(e){if(!a.length)return null;var t="function"==typeof N?N(e):N;return"function"==typeof T?er(void 0,t,!1,!1,void 0,!0):eo({title:t},t,!1)},suffix:ei,itemKey:z,maxCount:I});return o.createElement("span",{className:"".concat(ee,"-wrap")},el,!a.length&&!et&&o.createElement("span",{className:"".concat(ee,"-placeholder")},m))},N=function(e){var t=e.inputElement,n=e.prefixCls,r=e.id,i=e.inputRef,l=e.disabled,a=e.autoFocus,c=e.autoComplete,u=e.activeDescendantId,s=e.mode,f=e.open,p=e.values,m=e.placeholder,v=e.tabIndex,g=e.showSearch,h=e.searchValue,b=e.activeValue,A=e.maxLength,y=e.onInputKeyDown,w=e.onInputMouseDown,E=e.onInputChange,S=e.onInputPaste,x=e.onInputCompositionStart,I=e.onInputCompositionEnd,O=e.onInputBlur,M=e.title,z=o.useState(!1),D=(0,d.A)(z,2),H=D[0],N=D[1],T="combobox"===s,B=T||g,P=p[0],L=h||"";T&&b&&!H&&(L=b),o.useEffect(function(){T&&N(!1)},[T,b]);var k=("combobox"===s||!!f||!!g)&&!!L,j=void 0===M?R(P):M,W=o.useMemo(function(){return P?null:o.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:k?{visibility:"hidden"}:void 0},m)},[P,k,m,n]);return o.createElement("span",{className:"".concat(n,"-selection-wrap")},o.createElement("span",{className:"".concat(n,"-selection-search")},o.createElement($,{ref:i,prefixCls:n,id:r,open:f,inputElement:t,disabled:l,autoFocus:a,autoComplete:c,editable:B,activeDescendantId:u,value:L,onKeyDown:y,onMouseDown:w,onChange:function(e){N(!0),E(e)},onPaste:S,onCompositionStart:x,onCompositionEnd:I,onBlur:O,tabIndex:v,attrs:(0,C.A)(e,!0),maxLength:T?A:void 0})),!T&&P?o.createElement("span",{className:"".concat(n,"-selection-item"),title:j,style:k?{visibility:"hidden"}:void 0},P.label):null,W)};var T=o.forwardRef(function(e,t){var n=(0,o.useRef)(null),r=(0,o.useRef)(!1),i=e.prefixCls,l=e.open,c=e.mode,u=e.showSearch,s=e.tokenWithEnter,f=e.disabled,p=e.prefix,m=e.autoClearSearchValue,v=e.onSearch,g=e.onSearchSubmit,h=e.onToggleOpen,b=e.onInputKeyDown,A=e.onInputBlur,y=e.domRef;o.useImperativeHandle(t,function(){return{focus:function(e){n.current.focus(e)},blur:function(){n.current.blur()}}});var w=E(0),C=(0,d.A)(w,2),x=C[0],$=C[1],I=(0,o.useRef)(null),O=function(e){!1!==v(e,!0,r.current)&&h(!0)},M={inputRef:n,onInputKeyDown:function(e){var t=e.which,o=n.current instanceof HTMLTextAreaElement;!o&&l&&(t===S.A.UP||t===S.A.DOWN)&&e.preventDefault(),b&&b(e),t!==S.A.ENTER||"tags"!==c||r.current||l||null==g||g(e.target.value),o&&!l&&~[S.A.UP,S.A.DOWN,S.A.LEFT,S.A.RIGHT].indexOf(t)||!t||[S.A.ESC,S.A.SHIFT,S.A.BACKSPACE,S.A.TAB,S.A.WIN_KEY,S.A.ALT,S.A.META,S.A.WIN_KEY_RIGHT,S.A.CTRL,S.A.SEMICOLON,S.A.EQUALS,S.A.CAPS_LOCK,S.A.CONTEXT_MENU,S.A.F1,S.A.F2,S.A.F3,S.A.F4,S.A.F5,S.A.F6,S.A.F7,S.A.F8,S.A.F9,S.A.F10,S.A.F11,S.A.F12].includes(t)||h(!0)},onInputMouseDown:function(){$(!0)},onInputChange:function(e){var t=e.target.value;if(s&&I.current&&/[\r\n]/.test(I.current)){var n=I.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");t=t.replace(n,I.current)}I.current=null,O(t)},onInputPaste:function(e){var t=e.clipboardData,n=null==t?void 0:t.getData("text");I.current=n||""},onInputCompositionStart:function(){r.current=!0},onInputCompositionEnd:function(e){r.current=!1,"combobox"!==c&&O(e.target.value)},onInputBlur:A},R="multiple"===c||"tags"===c?o.createElement(H,(0,a.A)({},e,M)):o.createElement(N,(0,a.A)({},e,M));return o.createElement("div",{ref:y,className:"".concat(i,"-selector"),onClick:function(e){e.target!==n.current&&(void 0!==document.body.style.msTouchAction?setTimeout(function(){n.current.focus()}):n.current.focus())},onMouseDown:function(e){var t=x();e.target===n.current||t||"combobox"===c&&f||e.preventDefault(),("combobox"===c||u&&t)&&l||(l&&!1!==m&&v("",!0,!1),h())}},p&&o.createElement("div",{className:"".concat(i,"-prefix")},p),R)}),B=n(65412),P=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],L=function(e){var t=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"}}},k=o.forwardRef(function(e,t){var n=e.prefixCls,r=(e.disabled,e.visible),i=e.children,c=e.popupElement,d=e.animation,p=e.transitionName,m=e.dropdownStyle,v=e.dropdownClassName,g=e.direction,h=e.placement,b=e.builtinPlacements,A=e.dropdownMatchSelectWidth,y=e.dropdownRender,w=e.dropdownAlign,E=e.getPopupContainer,S=e.empty,C=e.getTriggerDOMNode,x=e.onPopupVisibleChange,$=e.onPopupMouseEnter,I=(0,f.A)(e,P),O="".concat(n,"-dropdown"),M=c;y&&(M=y(c));var R=o.useMemo(function(){return b||L(A)},[b,A]),z=d?"".concat(O,"-").concat(d):p,D="number"==typeof A,H=o.useMemo(function(){return D?null:!1===A?"minWidth":"width"},[A,D]),N=m;D&&(N=(0,s.A)((0,s.A)({},N),{},{width:A}));var T=o.useRef(null);return o.useImperativeHandle(t,function(){return{getPopupElement:function(){var e;return null===(e=T.current)||void 0===e?void 0:e.popupElement}}}),o.createElement(B.A,(0,a.A)({},I,{showAction:x?["click"]:[],hideAction:x?["click"]:[],popupPlacement:h||("rtl"===(void 0===g?"ltr":g)?"bottomRight":"bottomLeft"),builtinPlacements:R,prefixCls:O,popupTransitionName:z,popup:o.createElement("div",{onMouseEnter:$},M),ref:T,stretch:H,popupAlign:w,popupVisible:r,getPopupContainer:E,popupClassName:l()(v,(0,u.A)({},"".concat(O,"-empty"),S)),popupStyle:N,getTriggerDOMNode:C,onPopupVisibleChange:x}),i)}),j=n(70904);function W(e,t){var n,o=e.key;return("value"in e&&(n=e.value),null!=o)?o:void 0!==n?n:"rc-index-key-".concat(t)}function F(e){return void 0!==e&&!Number.isNaN(e)}function V(e,t){var n=e||{},o=n.label,r=n.value,i=n.options,l=n.groupLabel,a=o||(t?"children":"label");return{label:a,value:r||"value",options:i||"options",groupLabel:l||a}}function _(e){var t=(0,s.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,v.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var K=function(e,t,n){if(!t||!t.length)return null;var o=!1,r=function e(t,n){var r=(0,j.A)(n),i=r[0],l=r.slice(1);if(!i)return[t];var a=t.split(i);return o=o||a.length>1,a.reduce(function(t,n){return[].concat((0,c.A)(t),(0,c.A)(e(n,l)))},[]).filter(Boolean)}(e,t);return o?void 0!==n?r.slice(0,n):r:null},Y=o.createContext(null);function X(e){var t=e.visible,n=e.values;return t?o.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,50).map(function(e){var t=e.label,n=e.value;return["number","string"].includes((0,p.A)(t))?t:n}).join(", ")),n.length>50?", ...":null):null}var q=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],G=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],U=function(e){return"tags"===e||"multiple"===e},Q=o.forwardRef(function(e,t){var n,r,i,p,v,S,C,x=e.id,$=e.prefixCls,I=e.className,O=e.showSearch,M=e.tagRender,R=e.direction,z=e.omitDomProps,D=e.displayValues,H=e.onDisplayValuesChange,N=e.emptyOptions,B=e.notFoundContent,P=void 0===B?"Not Found":B,L=e.onClear,j=e.mode,W=e.disabled,V=e.loading,_=e.getInputElement,Q=e.getRawInputElement,J=e.open,Z=e.defaultOpen,ee=e.onDropdownVisibleChange,et=e.activeValue,en=e.onActiveValueChange,eo=e.activeDescendantId,er=e.searchValue,ei=e.autoClearSearchValue,el=e.onSearch,ea=e.onSearchSplit,ec=e.tokenSeparators,eu=e.allowClear,es=e.prefix,ed=e.suffixIcon,ef=e.clearIcon,ep=e.OptionList,em=e.animation,ev=e.transitionName,eg=e.dropdownStyle,eh=e.dropdownClassName,eb=e.dropdownMatchSelectWidth,eA=e.dropdownRender,ey=e.dropdownAlign,ew=e.placement,eE=e.builtinPlacements,eS=e.getPopupContainer,eC=e.showAction,ex=void 0===eC?[]:eC,e$=e.onFocus,eI=e.onBlur,eO=e.onKeyUp,eM=e.onKeyDown,eR=e.onMouseDown,ez=(0,f.A)(e,q),eD=U(j),eH=(void 0!==O?O:eD)||"combobox"===j,eN=(0,s.A)({},ez);G.forEach(function(e){delete eN[e]}),null==z||z.forEach(function(e){delete eN[e]});var eT=o.useState(!1),eB=(0,d.A)(eT,2),eP=eB[0],eL=eB[1];o.useEffect(function(){eL((0,h.A)())},[]);var ek=o.useRef(null),ej=o.useRef(null),eW=o.useRef(null),eF=o.useRef(null),eV=o.useRef(null),e_=o.useRef(!1),eK=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=o.useState(!1),n=(0,d.A)(t,2),r=n[0],i=n[1],l=o.useRef(null),a=function(){window.clearTimeout(l.current)};return o.useEffect(function(){return a},[]),[r,function(t,n){a(),l.current=window.setTimeout(function(){i(t),n&&n()},e)},a]}(),eY=(0,d.A)(eK,3),eX=eY[0],eq=eY[1],eG=eY[2];o.useImperativeHandle(t,function(){var e,t;return{focus:null===(e=eF.current)||void 0===e?void 0:e.focus,blur:null===(t=eF.current)||void 0===t?void 0:t.blur,scrollTo:function(e){var t;return null===(t=eV.current)||void 0===t?void 0:t.scrollTo(e)},nativeElement:ek.current||ej.current}});var eU=o.useMemo(function(){if("combobox"!==j)return er;var e,t=null===(e=D[0])||void 0===e?void 0:e.value;return"string"==typeof t||"number"==typeof t?String(t):""},[er,j,D]),eQ="combobox"===j&&"function"==typeof _&&_()||null,eJ="function"==typeof Q&&Q(),eZ=(0,b.xK)(ej,null==eJ||null===(p=eJ.props)||void 0===p?void 0:p.ref),e0=o.useState(!1),e1=(0,d.A)(e0,2),e2=e1[0],e5=e1[1];(0,g.A)(function(){e5(!0)},[]);var e4=(0,m.A)(!1,{defaultValue:Z,value:J}),e3=(0,d.A)(e4,2),e7=e3[0],e9=e3[1],e6=!!e2&&e7,e8=!P&&N;(W||e8&&e6&&"combobox"===j)&&(e6=!1);var te=!e8&&e6,tt=o.useCallback(function(e){var t=void 0!==e?e:!e6;W||(e9(t),e6!==t&&(null==ee||ee(t)))},[W,e6,e9,ee]),tn=o.useMemo(function(){return(ec||[]).some(function(e){return["\n","\r\n"].includes(e)})},[ec]),to=o.useContext(Y)||{},tr=to.maxCount,ti=to.rawValues,tl=function(e,t,n){if(!(eD&&F(tr))||!((null==ti?void 0:ti.size)>=tr)){var o=!0,r=e;null==en||en(null);var i=K(e,ec,F(tr)?tr-ti.size:void 0),l=n?null:i;return"combobox"!==j&&l&&(r="",null==ea||ea(l),tt(!1),o=!1),el&&eU!==r&&el(r,{source:t?"typing":"effect"}),o}};o.useEffect(function(){e6||eD||"combobox"===j||tl("",!1,!1)},[e6]),o.useEffect(function(){e7&&W&&e9(!1),W&&!e_.current&&eq(!1)},[W]);var ta=E(),tc=(0,d.A)(ta,2),tu=tc[0],ts=tc[1],td=o.useRef(!1),tf=o.useRef(!1),tp=[];o.useEffect(function(){return function(){tp.forEach(function(e){return clearTimeout(e)}),tp.splice(0,tp.length)}},[]);var tm=o.useState({}),tv=(0,d.A)(tm,2)[1];eJ&&(v=function(e){tt(e)}),n=function(){var e;return[ek.current,null===(e=eW.current)||void 0===e?void 0:e.getPopupElement()]},r=!!eJ,(i=o.useRef(null)).current={open:te,triggerOpen:tt,customizedTrigger:r},o.useEffect(function(){function e(e){if(null===(t=i.current)||void 0===t||!t.customizedTrigger){var t,o=e.target;o.shadowRoot&&e.composed&&(o=e.composedPath()[0]||o),i.current.open&&n().filter(function(e){return e}).every(function(e){return!e.contains(o)&&e!==o})&&i.current.triggerOpen(!1)}}return window.addEventListener("mousedown",e),function(){return window.removeEventListener("mousedown",e)}},[]);var tg=o.useMemo(function(){return(0,s.A)((0,s.A)({},e),{},{notFoundContent:P,open:e6,triggerOpen:te,id:x,showSearch:eH,multiple:eD,toggleOpen:tt})},[e,P,te,e6,x,eH,eD,tt]),th=!!ed||V;th&&(S=o.createElement(A,{className:l()("".concat($,"-arrow"),(0,u.A)({},"".concat($,"-arrow-loading"),V)),customizeIcon:ed,customizeIconProps:{loading:V,searchValue:eU,open:e6,focused:eX,showSearch:eH}}));var tb=y($,function(){var e;null==L||L(),null===(e=eF.current)||void 0===e||e.focus(),H([],{type:"clear",values:D}),tl("",!1,!1)},D,eu,ef,W,eU,j),tA=tb.allowClear,ty=tb.clearIcon,tw=o.createElement(ep,{ref:eV}),tE=l()($,I,(0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)({},"".concat($,"-focused"),eX),"".concat($,"-multiple"),eD),"".concat($,"-single"),!eD),"".concat($,"-allow-clear"),eu),"".concat($,"-show-arrow"),th),"".concat($,"-disabled"),W),"".concat($,"-loading"),V),"".concat($,"-open"),e6),"".concat($,"-customize-input"),eQ),"".concat($,"-show-search"),eH)),tS=o.createElement(k,{ref:eW,disabled:W,prefixCls:$,visible:te,popupElement:tw,animation:em,transitionName:ev,dropdownStyle:eg,dropdownClassName:eh,direction:R,dropdownMatchSelectWidth:eb,dropdownRender:eA,dropdownAlign:ey,placement:ew,builtinPlacements:eE,getPopupContainer:eS,empty:N,getTriggerDOMNode:function(e){return ej.current||e},onPopupVisibleChange:v,onPopupMouseEnter:function(){tv({})}},eJ?o.cloneElement(eJ,{ref:eZ}):o.createElement(T,(0,a.A)({},e,{domRef:ej,prefixCls:$,inputElement:eQ,ref:eF,id:x,prefix:es,showSearch:eH,autoClearSearchValue:ei,mode:j,activeDescendantId:eo,tagRender:M,values:D,open:e6,onToggleOpen:tt,activeValue:et,searchValue:eU,onSearch:tl,onSearchSubmit:function(e){e&&e.trim()&&el(e,{source:"submit"})},onRemove:function(e){H(D.filter(function(t){return t!==e}),{type:"remove",values:[e]})},tokenWithEnter:tn,onInputBlur:function(){td.current=!1}})));return C=eJ?tS:o.createElement("div",(0,a.A)({className:tE},eN,{ref:ek,onMouseDown:function(e){var t,n=e.target,o=null===(t=eW.current)||void 0===t?void 0:t.getPopupElement();if(o&&o.contains(n)){var r=setTimeout(function(){var e,t=tp.indexOf(r);-1!==t&&tp.splice(t,1),eG(),eP||o.contains(document.activeElement)||null===(e=eF.current)||void 0===e||e.focus()});tp.push(r)}for(var i=arguments.length,l=Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];null==eR||eR.apply(void 0,[e].concat(l))},onKeyDown:function(e){var t,n=tu(),o=e.key,r="Enter"===o;if(r&&("combobox"!==j&&e.preventDefault(),e6||tt(!0)),ts(!!eU),"Backspace"===o&&!n&&eD&&!eU&&D.length){for(var i=(0,c.A)(D),l=null,a=i.length-1;a>=0;a-=1){var u=i[a];if(!u.disabled){i.splice(a,1),l=u;break}}l&&H(i,{type:"remove",values:[l]})}for(var s=arguments.length,d=Array(s>1?s-1:0),f=1;f<s;f++)d[f-1]=arguments[f];!e6||r&&td.current||(r&&(td.current=!0),null===(t=eV.current)||void 0===t||t.onKeyDown.apply(t,[e].concat(d))),null==eM||eM.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var t,n=arguments.length,o=Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];e6&&(null===(t=eV.current)||void 0===t||t.onKeyUp.apply(t,[e].concat(o))),"Enter"===e.key&&(td.current=!1),null==eO||eO.apply(void 0,[e].concat(o))},onFocus:function(){eq(!0),!W&&(e$&&!tf.current&&e$.apply(void 0,arguments),ex.includes("focus")&&tt(!0)),tf.current=!0},onBlur:function(){e_.current=!0,eq(!1,function(){tf.current=!1,e_.current=!1,tt(!1)}),!W&&(eU&&("tags"===j?el(eU,{source:"submit"}):"multiple"===j&&el("",{source:"blur"})),eI&&eI.apply(void 0,arguments))}}),o.createElement(X,{visible:eX&&!e6,values:D}),tS,S,tA&&ty),o.createElement(w.Provider,{value:tg},C)}),J=function(){return null};J.isSelectOptGroup=!0;var Z=function(){return null};Z.isSelectOption=!0;var ee=n(45860),et=n(55681),en=n(94456),eo=["disabled","title","children","style","className"];function er(e){return"string"==typeof e||"number"==typeof e}var ei=o.forwardRef(function(e,t){var n=o.useContext(w),r=n.prefixCls,i=n.id,s=n.open,p=n.multiple,m=n.mode,v=n.searchValue,g=n.toggleOpen,h=n.notFoundContent,b=n.onPopupScroll,y=o.useContext(Y),E=y.maxCount,x=y.flattenOptions,$=y.onActiveValue,I=y.defaultActiveFirstOption,O=y.onSelect,M=y.menuItemSelectedIcon,R=y.rawValues,z=y.fieldNames,D=y.virtual,H=y.direction,N=y.listHeight,T=y.listItemHeight,B=y.optionRender,P="".concat(r,"-item"),L=(0,ee.A)(function(){return x},[s,x],function(e,t){return t[0]&&e[1]!==t[1]}),k=o.useRef(null),j=o.useMemo(function(){return p&&F(E)&&(null==R?void 0:R.size)>=E},[p,E,null==R?void 0:R.size]),W=function(e){e.preventDefault()},V=function(e){var t;null===(t=k.current)||void 0===t||t.scrollTo("number"==typeof e?{index:e}:e)},_=o.useCallback(function(e){return"combobox"!==m&&R.has(e)},[m,(0,c.A)(R).toString(),R.size]),K=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=L.length,o=0;o<n;o+=1){var r=(e+o*t+n)%n,i=L[r]||{},l=i.group,a=i.data;if(!l&&!(null!=a&&a.disabled)&&(_(a.value)||!j))return r}return -1},X=o.useState(function(){return K(0)}),q=(0,d.A)(X,2),G=q[0],U=q[1],Q=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];U(e);var n={source:t?"keyboard":"mouse"},o=L[e];if(!o){$(null,-1,n);return}$(o.value,e,n)};(0,o.useEffect)(function(){Q(!1!==I?K(0):-1)},[L.length,v]);var J=o.useCallback(function(e){return"combobox"===m?String(e).toLowerCase()===v.toLowerCase():R.has(e)},[m,v,(0,c.A)(R).toString(),R.size]);(0,o.useEffect)(function(){var e,t=setTimeout(function(){if(!p&&s&&1===R.size){var e=Array.from(R)[0],t=L.findIndex(function(t){return t.data.value===e});-1!==t&&(Q(t),V(t))}});return s&&(null===(e=k.current)||void 0===e||e.scrollTo(void 0)),function(){return clearTimeout(t)}},[s,v]);var Z=function(e){void 0!==e&&O(e,{selected:!R.has(e)}),p||g(!1)};if(o.useImperativeHandle(t,function(){return{onKeyDown:function(e){var t=e.which,n=e.ctrlKey;switch(t){case S.A.N:case S.A.P:case S.A.UP:case S.A.DOWN:var o=0;if(t===S.A.UP?o=-1:t===S.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&n&&(t===S.A.N?o=1:t===S.A.P&&(o=-1)),0!==o){var r=K(G+o,o);V(r),Q(r,!0)}break;case S.A.TAB:case S.A.ENTER:var i,l=L[G];!l||null!=l&&null!==(i=l.data)&&void 0!==i&&i.disabled||j?Z(void 0):Z(l.value),s&&e.preventDefault();break;case S.A.ESC:g(!1),s&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){V(e)}}}),0===L.length)return o.createElement("div",{role:"listbox",id:"".concat(i,"_list"),className:"".concat(P,"-empty"),onMouseDown:W},h);var ei=Object.keys(z).map(function(e){return z[e]}),el=function(e){return e.label};function ea(e,t){return{role:e.group?"presentation":"option",id:"".concat(i,"_list_").concat(t)}}var ec=function(e){var t=L[e];if(!t)return null;var n=t.data||{},r=n.value,i=t.group,l=(0,C.A)(n,!0),c=el(t);return t?o.createElement("div",(0,a.A)({"aria-label":"string"!=typeof c||i?null:c},l,{key:e},ea(t,e),{"aria-selected":J(r)}),r):null},eu={role:"listbox",id:"".concat(i,"_list")};return o.createElement(o.Fragment,null,D&&o.createElement("div",(0,a.A)({},eu,{style:{height:0,width:0,overflow:"hidden"}}),ec(G-1),ec(G),ec(G+1)),o.createElement(en.A,{itemKey:"key",ref:k,data:L,height:N,itemHeight:T,fullHeight:!1,onMouseDown:W,onScroll:b,virtual:D,direction:H,innerProps:D?null:eu},function(e,t){var n=e.group,r=e.groupOption,i=e.data,c=e.label,s=e.value,d=i.key;if(n){var p,m=null!==(p=i.title)&&void 0!==p?p:er(c)?c.toString():void 0;return o.createElement("div",{className:l()(P,"".concat(P,"-group"),i.className),title:m},void 0!==c?c:d)}var v=i.disabled,g=i.title,h=(i.children,i.style),b=i.className,y=(0,f.A)(i,eo),w=(0,et.A)(y,ei),E=_(s),S=v||!E&&j,x="".concat(P,"-option"),$=l()(P,x,b,(0,u.A)((0,u.A)((0,u.A)((0,u.A)({},"".concat(x,"-grouped"),r),"".concat(x,"-active"),G===t&&!S),"".concat(x,"-disabled"),S),"".concat(x,"-selected"),E)),I=el(e),O=!M||"function"==typeof M||E,R="number"==typeof I?I:I||s,z=er(R)?R.toString():void 0;return void 0!==g&&(z=g),o.createElement("div",(0,a.A)({},(0,C.A)(w),D?{}:ea(e,t),{"aria-selected":J(s),className:$,title:z,onMouseMove:function(){G===t||S||Q(t)},onClick:function(){S||Z(s)},style:h}),o.createElement("div",{className:"".concat(x,"-content")},"function"==typeof B?B(e,{index:t}):R),o.isValidElement(M)||E,O&&o.createElement(A,{className:"".concat(P,"-option-state"),customizeIcon:M,customizeIconProps:{value:s,disabled:S,isSelected:E}},E?"✓":null))}))});let el=function(e,t){var n=o.useRef({values:new Map,options:new Map});return[o.useMemo(function(){var o=n.current,r=o.values,i=o.options,l=e.map(function(e){if(void 0===e.label){var t;return(0,s.A)((0,s.A)({},e),{},{label:null===(t=r.get(e.value))||void 0===t?void 0:t.label})}return e}),a=new Map,c=new Map;return l.forEach(function(e){a.set(e.value,e),c.set(e.value,t.get(e.value)||i.get(e.value))}),n.current.values=a,n.current.options=c,l},[e,t]),o.useCallback(function(e){return t.get(e)||n.current.options.get(e)},[t])]};function ea(e,t){return I(e).join("").toUpperCase().includes(t)}var ec=n(7822),eu=0,es=(0,ec.A)(),ed=n(86866),ef=["children","value"],ep=["children"];function em(e){var t=o.useRef();return t.current=e,o.useCallback(function(){return t.current.apply(t,arguments)},[])}var ev=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],eg=["inputValue"],eh=o.forwardRef(function(e,t){var n,r,i,l,v,g=e.id,h=e.mode,b=e.prefixCls,A=e.backfill,y=e.fieldNames,w=e.inputValue,E=e.searchValue,S=e.onSearch,C=e.autoClearSearchValue,x=void 0===C||C,$=e.onSelect,O=e.onDeselect,M=e.dropdownMatchSelectWidth,R=void 0===M||M,z=e.filterOption,D=e.filterSort,H=e.optionFilterProp,N=e.optionLabelProp,T=e.options,B=e.optionRender,P=e.children,L=e.defaultActiveFirstOption,k=e.menuItemSelectedIcon,j=e.virtual,F=e.direction,K=e.listHeight,X=void 0===K?200:K,q=e.listItemHeight,G=void 0===q?20:q,J=e.labelRender,Z=e.value,ee=e.defaultValue,et=e.labelInValue,en=e.onChange,eo=e.maxCount,er=(0,f.A)(e,ev),ec=(n=o.useState(),i=(r=(0,d.A)(n,2))[0],l=r[1],o.useEffect(function(){var e;l("rc_select_".concat((es?(e=eu,eu+=1):e="TEST_OR_SSR",e)))},[]),g||i),eh=U(h),eb=!!(!T&&P),eA=o.useMemo(function(){return(void 0!==z||"combobox"!==h)&&z},[z,h]),ey=o.useMemo(function(){return V(y,eb)},[JSON.stringify(y),eb]),ew=(0,m.A)("",{value:void 0!==E?E:w,postState:function(e){return e||""}}),eE=(0,d.A)(ew,2),eS=eE[0],eC=eE[1],ex=o.useMemo(function(){var e=T;T||(e=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,ed.A)(t).map(function(t,r){if(!o.isValidElement(t)||!t.type)return null;var i,l,a,c,u,d=t.type.isSelectOptGroup,p=t.key,m=t.props,v=m.children,g=(0,f.A)(m,ep);return n||!d?(i=t.key,a=(l=t.props).children,c=l.value,u=(0,f.A)(l,ef),(0,s.A)({key:i,value:void 0!==c?c:i,children:a},u)):(0,s.A)((0,s.A)({key:"__RC_SELECT_GRP__".concat(null===p?r:p,"__"),label:p},g),{},{options:e(v)})}).filter(function(e){return e})}(P));var t=new Map,n=new Map,r=function(e,t,n){n&&"string"==typeof n&&e.set(t[n],t)};return function e(o){for(var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l=0;l<o.length;l+=1){var a=o[l];!a[ey.options]||i?(t.set(a[ey.value],a),r(n,a,ey.label),r(n,a,H),r(n,a,N)):e(a[ey.options],!0)}}(e),{options:e,valueOptions:t,labelOptions:n}},[T,P,ey,H,N]),e$=ex.valueOptions,eI=ex.labelOptions,eO=ex.options,eM=o.useCallback(function(e){return I(e).map(function(e){e&&"object"===(0,p.A)(e)?(o=e.key,n=e.label,t=null!==(l=e.value)&&void 0!==l?l:o):t=e;var t,n,o,r,i,l,a,c=e$.get(t);return c&&(void 0===n&&(n=null==c?void 0:c[N||ey.label]),void 0===o&&(o=null!==(a=null==c?void 0:c.key)&&void 0!==a?a:t),r=null==c?void 0:c.disabled,i=null==c?void 0:c.title),{label:n,value:t,key:o,disabled:r,title:i}})},[ey,N,e$]),eR=(0,m.A)(ee,{value:Z}),ez=(0,d.A)(eR,2),eD=ez[0],eH=ez[1],eN=el(o.useMemo(function(){var e,t,n=eM(eh&&null===eD?[]:eD);return"combobox"!==h||(t=null===(e=n[0])||void 0===e?void 0:e.value)||0===t?n:[]},[eD,eM,h,eh]),e$),eT=(0,d.A)(eN,2),eB=eT[0],eP=eT[1],eL=o.useMemo(function(){if(!h&&1===eB.length){var e=eB[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return eB.map(function(e){var t;return(0,s.A)((0,s.A)({},e),{},{label:null!==(t="function"==typeof J?J(e):e.label)&&void 0!==t?t:e.value})})},[h,eB,J]),ek=o.useMemo(function(){return new Set(eB.map(function(e){return e.value}))},[eB]);o.useEffect(function(){if("combobox"===h){var e,t=null===(e=eB[0])||void 0===e?void 0:e.value;eC(null!=t?String(t):"")}},[eB]);var ej=em(function(e,t){var n=null!=t?t:e;return(0,u.A)((0,u.A)({},ey.value,e),ey.label,n)}),eW=(v=o.useMemo(function(){if("tags"!==h)return eO;var e=(0,c.A)(eO);return(0,c.A)(eB).sort(function(e,t){return e.value<t.value?-1:1}).forEach(function(t){var n=t.value;e$.has(n)||e.push(ej(n,t.label))}),e},[ej,eO,e$,eB,h]),o.useMemo(function(){if(!eS||!1===eA)return v;var e=ey.options,t=ey.label,n=ey.value,o=[],r="function"==typeof eA,i=eS.toUpperCase(),l=r?eA:function(o,r){return H?ea(r[H],i):r[e]?ea(r["children"!==t?t:"label"],i):ea(r[n],i)},a=r?function(e){return _(e)}:function(e){return e};return v.forEach(function(t){if(t[e]){if(l(eS,a(t)))o.push(t);else{var n=t[e].filter(function(e){return l(eS,a(e))});n.length&&o.push((0,s.A)((0,s.A)({},t),{},(0,u.A)({},e,n)))}return}l(eS,a(t))&&o.push(t)}),o},[v,eA,H,eS,ey])),eF=o.useMemo(function(){return"tags"!==h||!eS||eW.some(function(e){return e[H||"value"]===eS})||eW.some(function(e){return e[ey.value]===eS})?eW:[ej(eS)].concat((0,c.A)(eW))},[ej,H,h,eW,eS,ey]),eV=o.useMemo(function(){return D?function e(t){return(0,c.A)(t).sort(function(e,t){return D(e,t,{searchValue:eS})}).map(function(t){return Array.isArray(t.options)?(0,s.A)((0,s.A)({},t),{},{options:t.options.length>0?e(t.options):t.options}):t})}(eF):eF},[eF,D,eS]),e_=o.useMemo(function(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],i=V(n,!1),l=i.label,a=i.value,c=i.options,u=i.groupLabel;return function e(t,n){Array.isArray(t)&&t.forEach(function(t){if(!n&&c in t){var i=t[u];void 0===i&&o&&(i=t.label),r.push({key:W(t,r.length),group:!0,data:t,label:i}),e(t[c],!0)}else{var s=t[a];r.push({key:W(t,r.length),groupOption:n,data:t,label:t[l],value:s})}})}(e,!1),r}(eV,{fieldNames:ey,childrenAsData:eb})},[eV,ey,eb]),eK=function(e){var t=eM(e);if(eH(t),en&&(t.length!==eB.length||t.some(function(e,t){var n;return(null===(n=eB[t])||void 0===n?void 0:n.value)!==(null==e?void 0:e.value)}))){var n=et?t:t.map(function(e){return e.value}),o=t.map(function(e){return _(eP(e.value))});en(eh?n:n[0],eh?o:o[0])}},eY=o.useState(null),eX=(0,d.A)(eY,2),eq=eX[0],eG=eX[1],eU=o.useState(0),eQ=(0,d.A)(eU,2),eJ=eQ[0],eZ=eQ[1],e0=void 0!==L?L:"combobox"!==h,e1=o.useCallback(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.source;eZ(t),A&&"combobox"===h&&null!==e&&"keyboard"===(void 0===o?"keyboard":o)&&eG(String(e))},[A,h]),e2=function(e,t,n){var o=function(){var t,n=eP(e);return[et?{label:null==n?void 0:n[ey.label],value:e,key:null!==(t=null==n?void 0:n.key)&&void 0!==t?t:e}:e,_(n)]};if(t&&$){var r=o(),i=(0,d.A)(r,2);$(i[0],i[1])}else if(!t&&O&&"clear"!==n){var l=o(),a=(0,d.A)(l,2);O(a[0],a[1])}},e5=em(function(e,t){var n=!eh||t.selected;eK(n?eh?[].concat((0,c.A)(eB),[e]):[e]:eB.filter(function(t){return t.value!==e})),e2(e,n),"combobox"===h?eG(""):(!U||x)&&(eC(""),eG(""))}),e4=o.useMemo(function(){var e=!1!==j&&!1!==R;return(0,s.A)((0,s.A)({},ex),{},{flattenOptions:e_,onActiveValue:e1,defaultActiveFirstOption:e0,onSelect:e5,menuItemSelectedIcon:k,rawValues:ek,fieldNames:ey,virtual:e,direction:F,listHeight:X,listItemHeight:G,childrenAsData:eb,maxCount:eo,optionRender:B})},[eo,ex,e_,e1,e0,e5,k,ek,ey,j,R,F,X,G,eb,B]);return o.createElement(Y.Provider,{value:e4},o.createElement(Q,(0,a.A)({},er,{id:ec,prefixCls:void 0===b?"rc-select":b,ref:t,omitDomProps:eg,mode:h,displayValues:eL,onDisplayValuesChange:function(e,t){eK(e);var n=t.type,o=t.values;("remove"===n||"clear"===n)&&o.forEach(function(e){e2(e.value,!1,n)})},direction:F,searchValue:eS,onSearch:function(e,t){if(eC(e),eG(null),"submit"===t.source){var n=(e||"").trim();n&&(eK(Array.from(new Set([].concat((0,c.A)(ek),[n])))),e2(n,!0),eC(""));return}"blur"!==t.source&&("combobox"===h&&eK(e),null==S||S(e))},autoClearSearchValue:x,onSearchSplit:function(e){var t=e;"tags"!==h&&(t=e.map(function(e){var t=eI.get(e);return null==t?void 0:t.value}).filter(function(e){return void 0!==e}));var n=Array.from(new Set([].concat((0,c.A)(ek),(0,c.A)(t))));eK(n),n.forEach(function(e){e2(e,!0)})},dropdownMatchSelectWidth:R,OptionList:ei,emptyOptions:!e_.length,activeValue:eq,activeDescendantId:"".concat(ec,"_list_").concat(eJ)})))});eh.Option=Z,eh.OptGroup=J;var eb=n(78371),eA=n(46219),ey=n(80349),ew=n(92534),eE=n(27343),eS=n(14092),eC=n(87375),ex=n(90334),e$=n(43089),eI=n(53421),eO=n(55168),eM=n(66799),eR=n(39772);let ez=e=>{let t={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},t),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},t),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},t),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},t),{points:["br","tr"],offset:[0,-4]})}};var eD=n(47285),eH=n(22974),eN=n(13662),eT=n(10941),eB=n(36485),eP=n(1195);let eL=e=>{let{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:t,padding:r,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},ek=e=>{let{antCls:t,componentCls:n}=e,o=`${n}-item`,r=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,a=`${n}-dropdown-placement-`,c=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},(0,eD.dF)(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${r}${a}bottomLeft,
          ${i}${a}bottomLeft
        `]:{animationName:eB.ox},[`
          ${r}${a}topLeft,
          ${i}${a}topLeft,
          ${r}${a}topRight,
          ${i}${a}topRight
        `]:{animationName:eB.nP},[`${l}${a}bottomLeft`]:{animationName:eB.vR},[`
          ${l}${a}topLeft,
          ${l}${a}topRight
        `]:{animationName:eB.YU},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},eL(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},eD.L9),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},eL(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},(0,eB._j)(e,"slide-up"),(0,eB._j)(e,"slide-down"),(0,eP.Mh)(e,"move-up"),(0,eP.Mh)(e,"move-down")]};var ej=n(96556),eW=n(1439);function eF(e,t){let{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:r}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${n}-${t}`:"";return{[`${n}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},(0,eD.dF)(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:(0,eW.zA)(i)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:(0,eW.zA)(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[`&:after,${n}-selection-item:empty:after,${n}-selection-placeholder:empty:after`]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-search,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${(0,eW.zA)(o)}`,[`${n}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:(0,eW.zA)(i)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${(0,eW.zA)(o)}`,"&:after":{display:"none"}}}}}}}let eV=(e,t)=>{let{componentCls:n,antCls:o,controlOutlineWidth:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${(0,eW.zA)(r)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},e_=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},eV(e,t))}),eK=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},eV(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),e_(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),e_(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),eY=(e,t)=>{let{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},eX=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},eY(e,t))}),eq=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},eY(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),eX(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),eX(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),eG=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),eU=(e,t)=>{let{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${(0,eW.zA)(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},eQ=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},eU(e,t))}),eJ=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},eU(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),eQ(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),eQ(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,eW.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),eZ=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},eK(e)),eq(e)),eG(e)),eJ(e))}),e0=e=>{let{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},e1=e=>{let{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},e2=e=>{let{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:r}=e;return{[n]:Object.assign(Object.assign({},(0,eD.dF)(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},e0(e)),e1(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},eD.L9),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},eD.L9),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},(0,eD.Nk)()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[r]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},[`&:hover ${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},e5=e=>{let{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},e2(e),function(e){let{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[eF(e),eF((0,eT.oX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${(0,eW.zA)(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},eF((0,eT.oX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}(e),(0,ej.Ay)(e),ek(e),{[`${t}-rtl`]:{direction:"rtl"}},(0,eH.G)(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},e4=(0,eN.OF)("Select",(e,t)=>{let{rootPrefixCls:n}=t,o=(0,eT.oX)(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[e5(o),eZ(o)]},e=>{let{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:r,controlHeightSM:i,controlHeightLG:l,paddingXXS:a,controlPaddingHorizontal:c,zIndexPopupBase:u,colorText:s,fontWeightStrong:d,controlItemBgActive:f,controlItemBgHover:p,colorBgContainer:m,colorFillSecondary:v,colorBgContainerDisabled:g,colorTextDisabled:h,colorPrimaryHover:b,colorPrimary:A,controlOutline:y}=e,w=2*a,E=2*o,S=Math.min(r-w,r-E),C=Math.min(i-w,i-E),x=Math.min(l-w,l-E);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(a/2),zIndexPopup:u+50,optionSelectedColor:s,optionSelectedFontWeight:d,optionSelectedBg:f,optionActiveBg:p,optionPadding:`${(r-t*n)/2}px ${c}px`,optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:m,clearBg:m,singleItemHeightLG:l,multipleItemBg:v,multipleItemBorderColor:"transparent",multipleItemHeight:S,multipleItemHeightSM:C,multipleItemHeightLG:x,multipleSelectorBgDisabled:g,multipleItemColorDisabled:h,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:A,activeOutlineColor:y,selectAffixPadding:a}},{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var e3=n(85077),e7=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let e9="SECRET_COMBOBOX_MODE_DO_NOT_USE",e6=o.forwardRef((e,t)=>{var n;let r;let{prefixCls:i,bordered:a,className:c,rootClassName:u,getPopupContainer:s,popupClassName:d,dropdownClassName:f,listHeight:p=256,placement:m,listItemHeight:v,size:g,disabled:h,notFoundContent:b,status:A,builtinPlacements:y,dropdownMatchSelectWidth:w,popupMatchSelectWidth:E,direction:S,style:C,allowClear:x,variant:$,dropdownStyle:I,transitionName:O,tagRender:M,maxCount:R,prefix:z}=e,D=e7(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix"]),{getPopupContainer:H,getPrefixCls:N,renderEmpty:T,direction:B,virtual:P,popupMatchSelectWidth:L,popupOverflow:k}=o.useContext(eE.QO),j=(0,eE.TP)("select"),[,W]=(0,eR.Ay)(),F=null!=v?v:null==W?void 0:W.controlHeight,V=N("select",i),_=N(),K=null!=S?S:B,{compactSize:Y,compactItemClassnames:X}=(0,eM.RQ)(V,K),[q,G]=(0,eO.A)("select",$,a),U=(0,ex.A)(V),[Q,J,Z]=e4(V,U),ee=o.useMemo(()=>{let{mode:t}=e;return"combobox"===t?void 0:t===e9?"combobox":t},[e.mode]),en="multiple"===ee||"tags"===ee,eo=function(e,t){return void 0!==t?t:null!==e}(e.suffixIcon,e.showArrow),er=null!==(n=null!=E?E:w)&&void 0!==n?n:L,{status:ei,hasFeedback:el,isFormItemInput:ea,feedbackIcon:ec}=o.useContext(eI.$W),eu=(0,ew.v)(ei,A);r=void 0!==b?b:"combobox"===ee?null:(null==T?void 0:T("Select"))||o.createElement(eS.A,{componentName:"Select"});let{suffixIcon:es,itemIcon:ed,removeIcon:ef,clearIcon:ep}=(0,e3.A)(Object.assign(Object.assign({},D),{multiple:en,hasFeedback:el,feedbackIcon:ec,showSuffixIcon:eo,prefixCls:V,componentName:"Select"})),em=(0,et.A)(D,["suffixIcon","itemIcon"]),ev=l()(d||f,{[`${V}-dropdown-${K}`]:"rtl"===K},u,Z,U,J),eg=(0,e$.A)(e=>{var t;return null!==(t=null!=g?g:Y)&&void 0!==t?t:e}),ey=o.useContext(eC.A),eD=l()({[`${V}-lg`]:"large"===eg,[`${V}-sm`]:"small"===eg,[`${V}-rtl`]:"rtl"===K,[`${V}-${q}`]:G,[`${V}-in-form-item`]:ea},(0,ew.L)(V,eu,el),X,j.className,c,u,Z,U,J),eH=o.useMemo(()=>void 0!==m?m:"rtl"===K?"bottomRight":"bottomLeft",[m,K]),[eN]=(0,eb.YK)("SelectLike",null==I?void 0:I.zIndex);return Q(o.createElement(eh,Object.assign({ref:t,virtual:P,showSearch:j.showSearch},em,{style:Object.assign(Object.assign({},j.style),C),dropdownMatchSelectWidth:er,transitionName:(0,eA.b)(_,"slide-up",O),builtinPlacements:y||ez(k),listHeight:p,listItemHeight:F,mode:ee,prefixCls:V,placement:eH,direction:K,prefix:z,suffixIcon:es,menuItemSelectedIcon:ed,removeIcon:ef,allowClear:!0===x?{clearIcon:ep}:x,notFoundContent:r,className:eD,getPopupContainer:s||H,dropdownClassName:ev,disabled:null!=h?h:ey,dropdownStyle:Object.assign(Object.assign({},I),{zIndex:eN}),maxCount:en?R:void 0,tagRender:en?M:void 0})))}),e8=(0,ey.A)(e6,"dropdownAlign");e6.SECRET_COMBOBOX_MODE_DO_NOT_USE=e9,e6.Option=Z,e6.OptGroup=J,e6._InternalPanelDoNotUseOrYouWillBeFired=e8;let te=e6},96556:(e,t,n)=>{n.d(t,{Ay:()=>d,Q3:()=>c,_8:()=>l});var o=n(1439),r=n(47285),i=n(10941);let l=e=>{let{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:r,INTERNAL_FIXED_ITEM_MARGIN:i}=e,l=e.max(e.calc(n).sub(r).equal(),0),a=e.max(e.calc(l).sub(i).equal(),0);return{basePadding:l,containerPadding:a,itemHeight:(0,o.zA)(t),itemLineHeight:(0,o.zA)(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},a=e=>{let{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()},c=e=>{let{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:i,paddingXS:l,multipleItemColorDisabled:a,multipleItemBorderColorDisabled:c,colorIcon:u,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:d}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:d,borderRadius:o,cursor:"default",transition:`font-size ${i}, line-height ${i}, height ${i}`,marginInlineEnd:e.calc(d).mul(2).equal(),paddingInlineStart:l,paddingInlineEnd:e.calc(l).div(2).equal(),[`${t}-disabled&`]:{color:a,borderColor:c,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(l).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},(0,r.Nk)()),{display:"inline-flex",alignItems:"center",color:u,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},u=(e,t)=>{let{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:r}=e,i=`${n}-selection-overflow`,u=e.multipleSelectItemHeight,s=a(e),d=t?`${n}-${t}`:"",f=l(e);return{[`${n}-multiple${d}`]:Object.assign(Object.assign({},c(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:f.basePadding,paddingBlock:f.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${(0,o.zA)(r)} 0`,lineHeight:(0,o.zA)(u),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:f.itemHeight,lineHeight:(0,o.zA)(f.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:(0,o.zA)(u),marginBlock:r}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal()},[`${i}-item + ${i}-item,
        ${n}-prefix + ${n}-selection-wrap
      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${i}-item-suffix`]:{minHeight:f.itemHeight,marginBlock:r},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s).equal(),[`
          &-input,
          &-mirror
        `]:{height:u,fontFamily:e.fontFamily,lineHeight:(0,o.zA)(u),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function s(e,t){let{componentCls:n}=e,o=t?`${n}-${t}`:"",r={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[u(e,t),r]}let d=e=>{let{componentCls:t}=e,n=(0,i.oX)(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=(0,i.oX)(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[s(e),s(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},s(o,"lg")]}},85077:(e,t,n)=>{n.d(t,{A:()=>s});var o=n(58009),r=n(31127),i=n(43119),l=n(97071),a=n(77953),c=n(88752),u=n(58733);function s(e){let{suffixIcon:t,clearIcon:n,menuItemSelectedIcon:s,removeIcon:d,loading:f,multiple:p,hasFeedback:m,prefixCls:v,showSuffixIcon:g,feedbackIcon:h,showArrow:b,componentName:A}=e,y=null!=n?n:o.createElement(i.A,null),w=e=>null!==t||m||b?o.createElement(o.Fragment,null,!1!==g&&e,m&&h):null,E=null;if(void 0!==t)E=w(t);else if(f)E=w(o.createElement(c.A,{spin:!0}));else{let e=`${v}-suffix`;E=t=>{let{open:n,showSearch:r}=t;return n&&r?w(o.createElement(u.A,{className:e})):w(o.createElement(a.A,{className:e}))}}let S=null;return S=void 0!==s?s:p?o.createElement(r.A,null):null,{clearIcon:y,suffixIcon:E,itemIcon:S,removeIcon:void 0!==d?d:o.createElement(l.A,null)}}},1195:(e,t,n)=>{n.d(t,{Mh:()=>f});var o=n(1439),r=n(98472);let i=new o.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new o.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),a=new o.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new o.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u=new o.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new o.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d={"move-up":{inKeyframes:new o.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:l},"move-left":{inKeyframes:a,outKeyframes:c},"move-right":{inKeyframes:u,outKeyframes:s}},f=(e,t)=>{let{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:i,outKeyframes:l}=d[t];return[(0,r.b)(o,i,l,e.motionDurationMid),{[`
        ${o}-enter,
        ${o}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},94456:(e,t,n)=>{n.d(t,{A:()=>N});var o=n(11855),r=n(97549),i=n(12992),l=n(65074),a=n(7770),c=n(49543),u=n(56073),s=n.n(u),d=n(21776),f=n(29966),p=n(55977),m=n(58009),v=n(55740),g=m.forwardRef(function(e,t){var n=e.height,r=e.offsetY,a=e.offsetX,c=e.children,u=e.prefixCls,f=e.onInnerResize,p=e.innerProps,v=e.rtl,g=e.extra,h={},b={display:"flex",flexDirection:"column"};return void 0!==r&&(h={height:n,position:"relative",overflow:"hidden"},b=(0,i.A)((0,i.A)({},b),{},(0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)({transform:"translateY(".concat(r,"px)")},v?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),m.createElement("div",{style:h},m.createElement(d.A,{onResize:function(e){e.offsetHeight&&f&&f()}},m.createElement("div",(0,o.A)({style:b,className:s()((0,l.A)({},"".concat(u,"-holder-inner"),u)),ref:t},p),c,g)))});function h(e){var t=e.children,n=e.setRef,o=m.useCallback(function(e){n(e)},[]);return m.cloneElement(t,{ref:o})}g.displayName="Filler";var b=n(64267),A=("undefined"==typeof navigator?"undefined":(0,r.A)(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);let y=function(e,t,n,o){var r=(0,m.useRef)(!1),i=(0,m.useRef)(null),l=(0,m.useRef)({top:e,bottom:t,left:n,right:o});return l.current.top=e,l.current.bottom=t,l.current.left=n,l.current.right=o,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e?t<0&&l.current.left||t>0&&l.current.right:t<0&&l.current.top||t>0&&l.current.bottom;return n&&o?(clearTimeout(i.current),r.current=!1):(!o||r.current)&&(clearTimeout(i.current),r.current=!0,i.current=setTimeout(function(){r.current=!1},50)),!r.current&&o}};var w=n(70476),E=n(85430),S=function(){function e(){(0,w.A)(this,e),(0,l.A)(this,"maps",void 0),(0,l.A)(this,"id",0),(0,l.A)(this,"diffKeys",new Set),this.maps=Object.create(null)}return(0,E.A)(e,[{key:"set",value:function(e,t){this.maps[e]=t,this.id+=1,this.diffKeys.add(e)}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffKeys.clear()}},{key:"getRecord",value:function(){return this.diffKeys}}]),e}();function C(e){var t=parseFloat(e);return isNaN(t)?0:t}var x=14/15;function $(e){return Math.floor(Math.pow(e,.5))}function I(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var O=m.forwardRef(function(e,t){var n=e.prefixCls,o=e.rtl,r=e.scrollOffset,c=e.scrollRange,u=e.onStartMove,d=e.onStopMove,f=e.onScroll,p=e.horizontal,v=e.spinSize,g=e.containerSize,h=e.style,A=e.thumbStyle,y=e.showScrollBar,w=m.useState(!1),E=(0,a.A)(w,2),S=E[0],C=E[1],x=m.useState(null),$=(0,a.A)(x,2),O=$[0],M=$[1],R=m.useState(null),z=(0,a.A)(R,2),D=z[0],H=z[1],N=!o,T=m.useRef(),B=m.useRef(),P=m.useState(y),L=(0,a.A)(P,2),k=L[0],j=L[1],W=m.useRef(),F=function(){!0!==y&&!1!==y&&(clearTimeout(W.current),j(!0),W.current=setTimeout(function(){j(!1)},3e3))},V=c-g||0,_=g-v||0,K=m.useMemo(function(){return 0===r||0===V?0:r/V*_},[r,V,_]),Y=m.useRef({top:K,dragging:S,pageY:O,startTop:D});Y.current={top:K,dragging:S,pageY:O,startTop:D};var X=function(e){C(!0),M(I(e,p)),H(Y.current.top),u(),e.stopPropagation(),e.preventDefault()};m.useEffect(function(){var e=function(e){e.preventDefault()},t=T.current,n=B.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",X,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",X)}},[]);var q=m.useRef();q.current=V;var G=m.useRef();G.current=_,m.useEffect(function(){if(S){var e,t=function(t){var n=Y.current,o=n.dragging,r=n.pageY,i=n.startTop;b.A.cancel(e);var l=T.current.getBoundingClientRect(),a=g/(p?l.width:l.height);if(o){var c=(I(t,p)-r)*a,u=i;!N&&p?u-=c:u+=c;var s=q.current,d=G.current,m=Math.ceil((d?u/d:0)*s);m=Math.min(m=Math.max(m,0),s),e=(0,b.A)(function(){f(m,p)})}},n=function(){C(!1),d()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),b.A.cancel(e)}}},[S]),m.useEffect(function(){return F(),function(){clearTimeout(W.current)}},[r]),m.useImperativeHandle(t,function(){return{delayHidden:F}});var U="".concat(n,"-scrollbar"),Q={position:"absolute",visibility:k?null:"hidden"},J={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return p?(Q.height=8,Q.left=0,Q.right=0,Q.bottom=0,J.height="100%",J.width=v,N?J.left=K:J.right=K):(Q.width=8,Q.top=0,Q.bottom=0,N?Q.right=0:Q.left=0,J.width="100%",J.height=v,J.top=K),m.createElement("div",{ref:T,className:s()(U,(0,l.A)((0,l.A)((0,l.A)({},"".concat(U,"-horizontal"),p),"".concat(U,"-vertical"),!p),"".concat(U,"-visible"),k)),style:(0,i.A)((0,i.A)({},Q),h),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:F},m.createElement("div",{ref:B,className:s()("".concat(U,"-thumb"),(0,l.A)({},"".concat(U,"-thumb-moving"),S)),style:(0,i.A)((0,i.A)({},J),A),onMouseDown:X}))});function M(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),Math.floor(n=Math.max(n,20))}var R=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],z=[],D={overflowY:"auto",overflowAnchor:"none"},H=m.forwardRef(function(e,t){var n,u,w,E,H,N,T,B,P,L,k,j,W,F,V,_,K,Y,X,q,G,U,Q,J,Z,ee,et,en,eo,er,ei,el,ea,ec,eu,es,ed,ef=e.prefixCls,ep=void 0===ef?"rc-virtual-list":ef,em=e.className,ev=e.height,eg=e.itemHeight,eh=e.fullHeight,eb=e.style,eA=e.data,ey=e.children,ew=e.itemKey,eE=e.virtual,eS=e.direction,eC=e.scrollWidth,ex=e.component,e$=e.onScroll,eI=e.onVirtualScroll,eO=e.onVisibleChange,eM=e.innerProps,eR=e.extraRender,ez=e.styles,eD=e.showScrollBar,eH=void 0===eD?"optional":eD,eN=(0,c.A)(e,R),eT=m.useCallback(function(e){return"function"==typeof ew?ew(e):null==e?void 0:e[ew]},[ew]),eB=function(e,t,n){var o=m.useState(0),r=(0,a.A)(o,2),i=r[0],l=r[1],c=(0,m.useRef)(new Map),u=(0,m.useRef)(new S),s=(0,m.useRef)(0);function d(){s.current+=1}function f(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];d();var t=function(){var e=!1;c.current.forEach(function(t,n){if(t&&t.offsetParent){var o=t.offsetHeight,r=getComputedStyle(t),i=r.marginTop,l=r.marginBottom,a=o+C(i)+C(l);u.current.get(n)!==a&&(u.current.set(n,a),e=!0)}}),e&&l(function(e){return e+1})};if(e)t();else{s.current+=1;var n=s.current;Promise.resolve().then(function(){n===s.current&&t()})}}return(0,m.useEffect)(function(){return d},[]),[function(o,r){var i=e(o),l=c.current.get(i);r?(c.current.set(i,r),f()):c.current.delete(i),!l!=!r&&(r?null==t||t(o):null==n||n(o))},f,u.current,i]}(eT,null,null),eP=(0,a.A)(eB,4),eL=eP[0],ek=eP[1],ej=eP[2],eW=eP[3],eF=!!(!1!==eE&&ev&&eg),eV=m.useMemo(function(){return Object.values(ej.maps).reduce(function(e,t){return e+t},0)},[ej.id,ej.maps]),e_=eF&&eA&&(Math.max(eg*eA.length,eV)>ev||!!eC),eK="rtl"===eS,eY=s()(ep,(0,l.A)({},"".concat(ep,"-rtl"),eK),em),eX=eA||z,eq=(0,m.useRef)(),eG=(0,m.useRef)(),eU=(0,m.useRef)(),eQ=(0,m.useState)(0),eJ=(0,a.A)(eQ,2),eZ=eJ[0],e0=eJ[1],e1=(0,m.useState)(0),e2=(0,a.A)(e1,2),e5=e2[0],e4=e2[1],e3=(0,m.useState)(!1),e7=(0,a.A)(e3,2),e9=e7[0],e6=e7[1],e8=function(){e6(!0)},te=function(){e6(!1)};function tt(e){e0(function(t){var n,o=(n="function"==typeof e?e(t):e,Number.isNaN(ty.current)||(n=Math.min(n,ty.current)),n=Math.max(n,0));return eq.current.scrollTop=o,o})}var tn=(0,m.useRef)({start:0,end:eX.length}),to=(0,m.useRef)(),tr=(n=m.useState(eX),w=(u=(0,a.A)(n,2))[0],E=u[1],H=m.useState(null),T=(N=(0,a.A)(H,2))[0],B=N[1],m.useEffect(function(){var e=function(e,t,n){var o,r,i=e.length,l=t.length;if(0===i&&0===l)return null;i<l?(o=e,r=t):(o=t,r=e);var a={__EMPTY_ITEM__:!0};function c(e){return void 0!==e?n(e):a}for(var u=null,s=1!==Math.abs(i-l),d=0;d<r.length;d+=1){var f=c(o[d]);if(f!==c(r[d])){u=d,s=s||f!==c(r[d+1]);break}}return null===u?null:{index:u,multiple:s}}(w||[],eX||[],eT);(null==e?void 0:e.index)!==void 0&&B(eX[e.index]),E(eX)},[eX]),[T]),ti=(0,a.A)(tr,1)[0];to.current=ti;var tl=m.useMemo(function(){if(!eF)return{scrollHeight:void 0,start:0,end:eX.length-1,offset:void 0};if(!e_)return{scrollHeight:(null===(e=eG.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:eX.length-1,offset:void 0};for(var e,t,n,o,r=0,i=eX.length,l=0;l<i;l+=1){var a=eT(eX[l]),c=ej.get(a),u=r+(void 0===c?eg:c);u>=eZ&&void 0===t&&(t=l,n=r),u>eZ+ev&&void 0===o&&(o=l),r=u}return void 0===t&&(t=0,n=0,o=Math.ceil(ev/eg)),void 0===o&&(o=eX.length-1),{scrollHeight:r,start:t,end:o=Math.min(o+1,eX.length-1),offset:n}},[e_,eF,eZ,eX,eW,ev]),ta=tl.scrollHeight,tc=tl.start,tu=tl.end,ts=tl.offset;tn.current.start=tc,tn.current.end=tu,m.useLayoutEffect(function(){var e=ej.getRecord();if(1===e.size){var t=Array.from(e)[0],n=eX[tc];if(n&&eT(n)===t){var o=ej.get(t)-eg;tt(function(e){return e+o})}}ej.resetRecord()},[ta]);var td=m.useState({width:0,height:ev}),tf=(0,a.A)(td,2),tp=tf[0],tm=tf[1],tv=(0,m.useRef)(),tg=(0,m.useRef)(),th=m.useMemo(function(){return M(tp.width,eC)},[tp.width,eC]),tb=m.useMemo(function(){return M(tp.height,ta)},[tp.height,ta]),tA=ta-ev,ty=(0,m.useRef)(tA);ty.current=tA;var tw=eZ<=0,tE=eZ>=tA,tS=e5<=0,tC=e5>=eC,tx=y(tw,tE,tS,tC),t$=function(){return{x:eK?-e5:e5,y:eZ}},tI=(0,m.useRef)(t$()),tO=(0,f._q)(function(e){if(eI){var t=(0,i.A)((0,i.A)({},t$()),e);(tI.current.x!==t.x||tI.current.y!==t.y)&&(eI(t),tI.current=t)}});function tM(e,t){t?((0,v.flushSync)(function(){e4(e)}),tO()):tt(e)}var tR=function(e){var t=e,n=eC?eC-tp.width:0;return Math.min(t=Math.max(t,0),n)},tz=(0,f._q)(function(e,t){t?((0,v.flushSync)(function(){e4(function(t){return tR(t+(eK?-e:e))})}),tO()):tt(function(t){return t+e})}),tD=(P=!!eC,L=(0,m.useRef)(0),k=(0,m.useRef)(null),j=(0,m.useRef)(null),W=(0,m.useRef)(!1),F=y(tw,tE,tS,tC),V=(0,m.useRef)(null),_=(0,m.useRef)(null),[function(e){if(eF){b.A.cancel(_.current),_.current=(0,b.A)(function(){V.current=null},2);var t,n=e.deltaX,o=e.deltaY,r=e.shiftKey,i=n,l=o;("sx"===V.current||!V.current&&r&&o&&!n)&&(i=o,l=0,V.current="sx");var a=Math.abs(i),c=Math.abs(l);(null===V.current&&(V.current=P&&a>c?"x":"y"),"y"===V.current)?(t=l,b.A.cancel(k.current),F(!1,t)||e._virtualHandled||(e._virtualHandled=!0,L.current+=t,j.current=t,A||e.preventDefault(),k.current=(0,b.A)(function(){var e=W.current?10:1;tz(L.current*e,!1),L.current=0}))):(tz(i,!0),A||e.preventDefault())}},function(e){eF&&(W.current=e.detail===j.current)}]),tH=(0,a.A)(tD,2),tN=tH[0],tT=tH[1];K=function(e,t,n,o){return!tx(e,t,n)&&(!o||!o._virtualHandled)&&(o&&(o._virtualHandled=!0),tN({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0)},X=(0,m.useRef)(!1),q=(0,m.useRef)(0),G=(0,m.useRef)(0),U=(0,m.useRef)(null),Q=(0,m.useRef)(null),J=function(e){if(X.current){var t=Math.ceil(e.touches[0].pageX),n=Math.ceil(e.touches[0].pageY),o=q.current-t,r=G.current-n,i=Math.abs(o)>Math.abs(r);i?q.current=t:G.current=n;var l=K(i,i?o:r,!1,e);l&&e.preventDefault(),clearInterval(Q.current),l&&(Q.current=setInterval(function(){i?o*=x:r*=x;var e=Math.floor(i?o:r);(!K(i,e,!0)||.1>=Math.abs(e))&&clearInterval(Q.current)},16))}},Z=function(){X.current=!1,Y()},ee=function(e){Y(),1!==e.touches.length||X.current||(X.current=!0,q.current=Math.ceil(e.touches[0].pageX),G.current=Math.ceil(e.touches[0].pageY),U.current=e.target,U.current.addEventListener("touchmove",J,{passive:!1}),U.current.addEventListener("touchend",Z,{passive:!0}))},Y=function(){U.current&&(U.current.removeEventListener("touchmove",J),U.current.removeEventListener("touchend",Z))},(0,p.A)(function(){return eF&&eq.current.addEventListener("touchstart",ee,{passive:!0}),function(){var e;null===(e=eq.current)||void 0===e||e.removeEventListener("touchstart",ee),Y(),clearInterval(Q.current)}},[eF]),et=function(e){tt(function(t){return t+e})},m.useEffect(function(){var e=eq.current;if(e_&&e){var t,n,o=!1,r=function(){b.A.cancel(t)},i=function e(){r(),t=(0,b.A)(function(){et(n),e()})},l=function(e){!e.target.draggable&&0===e.button&&(e._virtualHandled||(e._virtualHandled=!0,o=!0))},a=function(){o=!1,r()},c=function(t){if(o){var l=I(t,!1),a=e.getBoundingClientRect(),c=a.top,u=a.bottom;l<=c?(n=-$(c-l),i()):l>=u?(n=$(l-u),i()):r()}};return e.addEventListener("mousedown",l),e.ownerDocument.addEventListener("mouseup",a),e.ownerDocument.addEventListener("mousemove",c),function(){e.removeEventListener("mousedown",l),e.ownerDocument.removeEventListener("mouseup",a),e.ownerDocument.removeEventListener("mousemove",c),r()}}},[e_]),(0,p.A)(function(){function e(e){var t=tw&&e.detail<0,n=tE&&e.detail>0;!eF||t||n||e.preventDefault()}var t=eq.current;return t.addEventListener("wheel",tN,{passive:!1}),t.addEventListener("DOMMouseScroll",tT,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",tN),t.removeEventListener("DOMMouseScroll",tT),t.removeEventListener("MozMousePixelScroll",e)}},[eF,tw,tE]),(0,p.A)(function(){if(eC){var e=tR(e5);e4(e),tO({x:e})}},[tp.width,eC]);var tB=function(){var e,t;null===(e=tv.current)||void 0===e||e.delayHidden(),null===(t=tg.current)||void 0===t||t.delayHidden()},tP=(en=function(){return ek(!0)},eo=m.useRef(),er=m.useState(null),el=(ei=(0,a.A)(er,2))[0],ea=ei[1],(0,p.A)(function(){if(el&&el.times<10){if(!eq.current){ea(function(e){return(0,i.A)({},e)});return}en();var e=el.targetAlign,t=el.originAlign,n=el.index,o=el.offset,r=eq.current.clientHeight,l=!1,a=e,c=null;if(r){for(var u=e||t,s=0,d=0,f=0,p=Math.min(eX.length-1,n),m=0;m<=p;m+=1){var v=eT(eX[m]);d=s;var g=ej.get(v);s=f=d+(void 0===g?eg:g)}for(var h="top"===u?o:r-o,b=p;b>=0;b-=1){var A=eT(eX[b]),y=ej.get(A);if(void 0===y){l=!0;break}if((h-=y)<=0)break}switch(u){case"top":c=d-o;break;case"bottom":c=f-r+o;break;default:var w=eq.current.scrollTop;d<w?a="top":f>w+r&&(a="bottom")}null!==c&&tt(c),c!==el.lastTop&&(l=!0)}l&&ea((0,i.A)((0,i.A)({},el),{},{times:el.times+1,targetAlign:a,lastTop:c}))}},[el,eq.current]),function(e){if(null==e){tB();return}if(b.A.cancel(eo.current),"number"==typeof e)tt(e);else if(e&&"object"===(0,r.A)(e)){var t,n=e.align;t="index"in e?e.index:eX.findIndex(function(t){return eT(t)===e.key});var o=e.offset;ea({times:0,index:t,offset:void 0===o?0:o,originAlign:n})}});m.useImperativeHandle(t,function(){return{nativeElement:eU.current,getScrollInfo:t$,scrollTo:function(e){e&&"object"===(0,r.A)(e)&&("left"in e||"top"in e)?(void 0!==e.left&&e4(tR(e.left)),tP(e.top)):tP(e)}}}),(0,p.A)(function(){eO&&eO(eX.slice(tc,tu+1),eX)},[tc,tu,eX]);var tL=(ec=m.useMemo(function(){return[new Map,[]]},[eX,ej.id,eg]),es=(eu=(0,a.A)(ec,2))[0],ed=eu[1],function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=es.get(e),o=es.get(t);if(void 0===n||void 0===o)for(var r=eX.length,i=ed.length;i<r;i+=1){var l,a=eT(eX[i]);es.set(a,i);var c=null!==(l=ej.get(a))&&void 0!==l?l:eg;if(ed[i]=(ed[i-1]||0)+c,a===e&&(n=i),a===t&&(o=i),void 0!==n&&void 0!==o)break}return{top:ed[n-1]||0,bottom:ed[o]}}),tk=null==eR?void 0:eR({start:tc,end:tu,virtual:e_,offsetX:e5,offsetY:ts,rtl:eK,getSize:tL}),tj=eX.slice(tc,tu+1).map(function(e,t){var n=ey(e,tc+t,{style:{width:eC},offsetX:e5}),o=eT(e);return m.createElement(h,{key:o,setRef:function(t){return eL(e,t)}},n)}),tW=null;ev&&(tW=(0,i.A)((0,l.A)({},void 0===eh||eh?"height":"maxHeight",ev),D),eF&&(tW.overflowY="hidden",eC&&(tW.overflowX="hidden"),e9&&(tW.pointerEvents="none")));var tF={};return eK&&(tF.dir="rtl"),m.createElement("div",(0,o.A)({ref:eU,style:(0,i.A)((0,i.A)({},eb),{},{position:"relative"}),className:eY},tF,eN),m.createElement(d.A,{onResize:function(e){tm({width:e.offsetWidth,height:e.offsetHeight})}},m.createElement(void 0===ex?"div":ex,{className:"".concat(ep,"-holder"),style:tW,ref:eq,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==eZ&&tt(t),null==e$||e$(e),tO()},onMouseEnter:tB},m.createElement(g,{prefixCls:ep,height:ta,offsetX:e5,offsetY:ts,scrollWidth:eC,onInnerResize:ek,ref:eG,innerProps:eM,rtl:eK,extra:tk},tj))),e_&&ta>ev&&m.createElement(O,{ref:tv,prefixCls:ep,scrollOffset:eZ,scrollRange:ta,rtl:eK,onScroll:tM,onStartMove:e8,onStopMove:te,spinSize:tb,containerSize:tp.height,style:null==ez?void 0:ez.verticalScrollBar,thumbStyle:null==ez?void 0:ez.verticalScrollBarThumb,showScrollBar:eH}),e_&&eC>tp.width&&m.createElement(O,{ref:tg,prefixCls:ep,scrollOffset:e5,scrollRange:eC,rtl:eK,onScroll:tM,onStartMove:e8,onStopMove:te,spinSize:th,containerSize:tp.width,horizontal:!0,style:null==ez?void 0:ez.horizontalScrollBar,thumbStyle:null==ez?void 0:ez.horizontalScrollBarThumb,showScrollBar:eH}))});H.displayName="List";let N=H}};