"use client";

import { useDeleteStoreMutation } from "@/reduxRTK/services/storeApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/api";

export const useStoreDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a store
  const [deleteStore, { isLoading }] = useDeleteStoreMutation();

  const deleteStoreById = async (storeId: number) => {
    try {
      const result = await deleteStore(storeId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete store");
      }

      showMessage("success", "Store deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete store error:", error);
      showMessage("error", error.message || "Failed to delete store");
      throw error;
    }
  };

  return {
    deleteStore: deleteStoreById,
    isDeleting: isLoading
  };
};
