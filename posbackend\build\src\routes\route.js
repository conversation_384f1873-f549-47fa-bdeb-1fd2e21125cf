"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const controllers_1 = require("../controllers");
const dashboardController_1 = require("../controllers/dashboardController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const checkPaymentStatus_1 = require("../middleware/checkPaymentStatus");
const router = express_1.default.Router();
// ✅ Public Routes (No Auth Required)
router.post("/login", controllers_1.loginUserController);
router.post("/logout", controllers_1.logoutController);
// ✅ Protected Routes (Require authentication)
router.post("/users", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleUserRequest);
router.post("/categories", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleCategoryRequest);
router.post("/products", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleProductRequest);
router.post("/suppliers", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleSupplierRequest);
router.post("/purchases", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handlePurchaseRequest);
router.post("/stockadjustment", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleStockAdjustmentRequest);
router.post("/sales", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleSalesRequest);
router.post("/receipt", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleReceiptRequest);
router.post("/dashboard", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, dashboardController_1.handleDashboardRequest);
// Payment route - doesn't need payment status check
router.post("/payment", authMiddleware_1.authMiddleware, controllers_1.handlePaymentRequest);
// Store routes
router.post("/stores", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleStoreRequest);
// User-Store routes
router.post("/user-stores", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleUserStoreRequest);
// Expense routes
router.post("/expenses", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleExpenseRequest);
// Expense Category routes
router.post("/expense-categories", authMiddleware_1.authMiddleware, checkPaymentStatus_1.checkPaymentStatus, controllers_1.handleExpenseCategoryRequest);
exports.default = router;
