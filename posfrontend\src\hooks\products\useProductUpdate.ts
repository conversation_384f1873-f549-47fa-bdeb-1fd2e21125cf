"use client";

import { useUpdateProductMutation, UpdateProductDto } from "@/reduxRTK/services/productApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useProductUpdate = (onSuccess?: () => void) => {
  // RTK Query hook for updating a product
  const [updateProduct, { isLoading }] = useUpdateProductMutation();

  const updateExistingProduct = async (productId: number, productData: UpdateProductDto) => {
    try {
      console.log("Updating product:", productId, productData);

      const result = await updateProduct({
        productId,
        data: productData
      }).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to update product");
      }

      showMessage("success", "Product updated successfully");

      // Call onSuccess with a small delay to ensure the cache is invalidated
      if (onSuccess) {
        console.log("Calling onSuccess callback for product update");
        onSuccess();

        // Call it again after a delay to ensure the table refreshes
        setTimeout(() => {
          console.log("Calling delayed onSuccess to ensure table refresh after product update");
          onSuccess();
        }, 300);
      }

      return result.data;
    } catch (error: any) {
      console.error("Update product error:", error);
      showMessage("error", error.message || "Failed to update product");
      throw error;
    }
  };

  return {
    updateProduct: updateExistingProduct,
    isUpdating: isLoading
  };
};
