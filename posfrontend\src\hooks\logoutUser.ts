import { useDispatch } from 'react-redux';
import { useLogoutUserMutation } from '@/reduxRTK/services/authApi';  
import { clearUser } from '@/reduxRTK/services/authSlice';  
import { useState } from 'react';
import { ApiResponse, UserResponse } from '@/types/user';

export const useLogoutUser = () => {
  const dispatch = useDispatch();
  const [state, setState] = useState({
    loading: false,
    error: '',
  });

  const [logoutUser] = useLogoutUserMutation<ApiResponse<UserResponse>>();;  

  const logout = async (email: string) => {
    setState({ loading: true, error: '' });

    try {
      const response = await logoutUser({ email }).unwrap()as unknown as ApiResponse<UserResponse>;;
      if (response.success) {
        dispatch(clearUser());
        window.location.href = '/';
        setState({ loading: false, error: '' });
      } else {
        setState({ loading: false, error: response.message });
      }
    } catch (error: any) {
      const errorMessage = error?.data?.message || error.message || 'Logout failed';
      setState({ loading: false, error: errorMessage });
    }
  };

  return {
    logout,
    ...state,
  };
};
