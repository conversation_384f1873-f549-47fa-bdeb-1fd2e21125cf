"use client";

import { useGetCategoryByIdQuery, Category } from "@/reduxRTK/services/categoryApi";
import { useState, useEffect } from "react";

export const useCategoryDetail = (categoryId: number | null) => {
  // Skip the query if no categoryId is provided
  const {
    data,
    error,
    isLoading,
    refetch
  } = useGetCategoryByIdQuery(categoryId || 0, {
    skip: !categoryId,
  });

  // Extract category from the response
  const category: Category | null = (data as any)?.data || null;

  return {
    category,
    isLoading,
    error,
    refetch
  };
};
