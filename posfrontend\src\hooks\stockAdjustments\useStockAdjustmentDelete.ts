"use client";

import { useDeleteStockAdjustmentMutation } from "@/reduxRTK/services/stockAdjustmentApi";
import { ApiResponse } from "@/types/user";
import { showMessage } from "@/utils/showMessage";

export const useStockAdjustmentDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting a stock adjustment
  const [deleteStockAdjustment, { isLoading }] = useDeleteStockAdjustmentMutation();

  const deleteStockAdjustmentById = async (adjustmentId: number) => {
    try {
      const result = await deleteStockAdjustment(adjustmentId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete stock adjustment");
      }

      showMessage("success", "Stock adjustment deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete stock adjustment error:", error);
      showMessage("error", error.message || "Failed to delete stock adjustment");
      throw error;
    }
  };

  return {
    deleteStockAdjustment: deleteStockAdjustmentById,
    isDeleting: isLoading
  };
};
