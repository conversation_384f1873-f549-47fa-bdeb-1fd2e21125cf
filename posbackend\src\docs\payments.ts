/**
 * @swagger
 * components:
 *   schemas:
 *     InitializePaymentRequest:
 *       type: object
 *       required:
 *         - amount
 *         - email
 *         - subscriptionPeriod
 *       properties:
 *         amount:
 *           type: number
 *           format: decimal
 *           example: 64.00
 *           description: Payment amount in GHS
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's email address
 *         subscriptionPeriod:
 *           type: integer
 *           enum: [1, 3, 12]
 *           example: 1
 *           description: Subscription period in months
 *         metadata:
 *           type: object
 *           properties:
 *             userId:
 *               type: integer
 *               example: 1
 *             plan:
 *               type: string
 *               example: monthly
 *           description: Additional metadata for the payment
 *     
 *     PaymentResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Payment initialized successfully
 *         data:
 *           type: object
 *           properties:
 *             authorizationUrl:
 *               type: string
 *               example: https://checkout.paystack.com/abc123
 *               description: Paystack checkout URL
 *             accessCode:
 *               type: string
 *               example: abc123def456
 *               description: Paystack access code
 *             reference:
 *               type: string
 *               example: ref_abc123def456
 *               description: Payment reference
 *     
 *     VerifyPaymentRequest:
 *       type: object
 *       required:
 *         - reference
 *       properties:
 *         reference:
 *           type: string
 *           example: ref_abc123def456
 *           description: Payment reference to verify
 *     
 *     Payment:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         userId:
 *           type: integer
 *           example: 1
 *         amount:
 *           type: string
 *           example: "64.00"
 *         provider:
 *           type: string
 *           example: paystack
 *         transactionId:
 *           type: string
 *           example: **********
 *         status:
 *           type: string
 *           enum: [pending, successful, failed, abandoned]
 *           example: successful
 *         paidAt:
 *           type: string
 *           format: date-time
 *           example: 2024-01-15T10:30:00Z
 *         paystackReference:
 *           type: string
 *           example: ref_abc123def456
 *         authorizationCode:
 *           type: string
 *           example: AUTH_abc123def
 *         channel:
 *           type: string
 *           example: card
 *         currency:
 *           type: string
 *           example: GHS
 *         subscriptionPeriod:
 *           type: integer
 *           example: 1
 */

/**
 * @swagger
 * /payment:
 *   post:
 *     summary: Manage payments
 *     description: Initialize, verify, or retrieve payment information
 *     tags: [Payment Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - allOf:
 *                   - type: object
 *                     properties:
 *                       mode:
 *                         type: string
 *                         enum: [initialize]
 *                         example: initialize
 *                   - $ref: '#/components/schemas/InitializePaymentRequest'
 *               - allOf:
 *                   - type: object
 *                     properties:
 *                       mode:
 *                         type: string
 *                         enum: [verify]
 *                         example: verify
 *                   - $ref: '#/components/schemas/VerifyPaymentRequest'
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [read]
 *                     example: read
 *                   page:
 *                     type: integer
 *                     example: 1
 *                     description: Page number for pagination
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                     description: Number of items per page
 *                   status:
 *                     type: string
 *                     enum: [pending, successful, failed, abandoned]
 *                     example: successful
 *                     description: Filter by payment status
 *                   userId:
 *                     type: integer
 *                     example: 1
 *                     description: Filter by user ID
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     example: 2024-01-01
 *                     description: Filter payments from this date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     example: 2024-01-31
 *                     description: Filter payments until this date
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - $ref: '#/components/schemas/PaymentResponse'
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: Payments retrieved successfully
 *                     data:
 *                       type: object
 *                       properties:
 *                         payments:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Payment'
 *                         total:
 *                           type: integer
 *                           example: 25
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 10
 *                         totalPages:
 *                           type: integer
 *                           example: 3
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       402:
 *         description: Payment required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Payment not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
