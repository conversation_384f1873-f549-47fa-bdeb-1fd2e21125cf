import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import { getAllReceipts, getReceiptBySaleId, deleteReceiptById } from "../services/receiptService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

/**
 * ✅ **Handle Receipt Requests**
 */
export const handleReceiptRequest = async (req: Request, res: Response): Promise<void> => {
  const { mode, page, limit, saleId, id } = req.body;
  const requester = req.user as DecodedToken; // ✅ Extract requester from middleware

  // ✅ Validate mode
  const validModes = ["retrieve", "retrieveBySaleId", "delete"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      /**
       * ✅ **Retrieve All Receipts (Paginated)**
       */
      case "retrieve": {
        const pageNum = Number(page) || 1;
        const limitNum = Number(limit) || 10;

        if (pageNum < 1 || limitNum < 1) {
          return sendResponse(res, 400, false, "Invalid pagination values.");
        }

        const receipts = await getAllReceipts(requester, pageNum, limitNum);
        return sendResponse(res, 200, true, "Receipts retrieved successfully.", receipts);
      }

      /**
       * ✅ **Retrieve a Receipt by Sale ID**
       */
      case "retrieveBySaleId": {
        if (!saleId) {
          return sendResponse(res, 400, false, "saleId is required.");
        }

        const receipt = await getReceiptBySaleId(requester, Number(saleId));
        return sendResponse(res, 200, true, "Receipt retrieved successfully.", receipt);
      }

      /**
       * ✅ **Delete a Receipt (Single or Multiple)**
       */
      case "delete": {
        // Check if we have receiptIds (array) or id (single)
        const { receiptIds } = req.body;

        if (!id && !receiptIds) {
          return sendResponse(res, 400, false, "Receipt ID(s) are required for deletion.");
        }

        // Use receiptIds if provided, otherwise use single id
        const idsToDelete = receiptIds
          ? (Array.isArray(receiptIds) ? receiptIds : [receiptIds])
          : [Number(id)];

        const result = await deleteReceiptById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} receipts deleted successfully.`
          : "Receipt deleted successfully.";

        return sendResponse(res, 200, true, message, result);
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(res, 500, false, error.message || "Internal Server Error");
  }
};
