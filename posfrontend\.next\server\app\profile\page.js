(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},89160:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>p});var s=t(70260),o=t(28203),n=t(25155),i=t.n(n),a=t(67292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let p=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11891)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,47004)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},24765:(e,r,t)=>{Promise.resolve().then(t.bind(t,47004))},52381:(e,r,t)=>{Promise.resolve().then(t.bind(t,37328))},82490:(e,r,t)=>{Promise.resolve().then(t.bind(t,11891))},22658:(e,r,t)=>{Promise.resolve().then(t.bind(t,65215))},37328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(45512),o=t(58009),n=t(15144),i=t(59262),a=t(94682),l=t(45546),p=t(60746),d=t(24715);function c({children:e}){let[r,t]=(0,o.useState)(!1),{setIsOpen:c,isMobile:u}=(0,p.V)();return r?(0,s.jsx)(l.default,{checkPayment:!1,children:(0,s.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,s.jsx)(i.R,{}),(0,s.jsx)("div",{className:"fixed left-0 top-0 h-full z-20 hidden lg:block",children:(0,s.jsx)(n.B,{})}),(0,s.jsxs)("div",{className:"w-full lg:ml-[290px] bg-white flex flex-col h-screen",children:[(0,s.jsx)("div",{className:"sticky top-0 z-30 w-full bg-white border-b border-gray-200",children:(0,s.jsx)(a.Y,{})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,s.jsx)("main",{className:"isolate mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10",children:e})})]})]})}):(0,s.jsx)(d.A,{tip:"Loading profile..."})}},65215:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(45512);t(58009);var o=t(79334),n=t(24715);function i(){return(0,o.useRouter)(),(0,s.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,s.jsx)(n.A,{tip:"Redirecting to profile..."})})}},47004:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\profile\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\layout.tsx","default")},11891:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\profile\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,3391,4877,3999,9198,1184,1716,3712,7624,2648,5482,106],()=>t(89160));module.exports=s})();