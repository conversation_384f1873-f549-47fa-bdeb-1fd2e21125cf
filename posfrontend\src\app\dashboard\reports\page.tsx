"use client";

import React from "react";
import { <PERSON>, Row, Col, Button } from "antd";
import {
  BarChartOutlined,
  DatabaseOutlined,
  ArrowRightOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useRouter } from "next/navigation";

const ReportsPage = () => {
  const { user: currentUser } = useAuth();
  const isMobile = useIsMobile();
  const router = useRouter();

  const reportCategories = [
    {
      title: "Sales Reports",
      description: "Analyze sales performance, revenue trends, and customer insights",
      icon: <BarChartOutlined className="text-4xl text-blue-600" />,
      path: "/dashboard/reports/sales",
      features: [
        "Sales summary and trends",
        "Product performance analysis",
        "Customer purchase patterns",
        "Revenue breakdowns"
      ]
    },
    {
      title: "Inventory Reports",
      description: "Monitor stock levels, track inventory movement, and manage reorders",
      icon: <DatabaseOutlined className="text-4xl text-green-600" />,
      path: "/dashboard/reports/inventory",
      features: [
        "Current stock levels",
        "Low stock alerts",
        "Inventory valuation",
        "Stock movement tracking"
      ]
    }
  ];

  const handleNavigateToReport = (path: string) => {
    router.push(path);
  };

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={
          <div className="flex items-center gap-2">
            <FileTextOutlined className="text-blue-600" />
            <span className="text-gray-800">Reports & Analytics</span>
          </div>
        }
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
      >
        <div className="w-full space-y-6">
          {/* Welcome Section */}
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-800 mb-2">
                Business Intelligence Dashboard
              </h2>
              <p className="text-gray-600">
                Generate comprehensive reports to gain insights into your business performance
              </p>
            </div>
          </Card>

          {/* Report Categories */}
          <Row gutter={[24, 24]}>
            {reportCategories.map((category, index) => (
              <Col xs={24} lg={12} key={index}>
                <Card
                  className="h-full hover:shadow-lg transition-shadow duration-300 border-gray-200"
                  styles={{
                    body: { padding: "24px" }
                  }}
                >
                  <div className="text-center mb-4">
                    {category.icon}
                  </div>

                  <div className="text-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {category.description}
                    </p>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Features:</h4>
                    <ul className="space-y-2">
                      {category.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Button
                    type="primary"
                    block
                    size="large"
                    onClick={() => handleNavigateToReport(category.path)}
                    className="bg-blue-600 hover:bg-blue-700"
                    icon={<ArrowRightOutlined />}
                  >
                    View {category.title}
                  </Button>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Quick Stats Overview */}
          <Card title="Quick Overview" className="bg-gray-50">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <div className="text-center p-4 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">2</div>
                  <div className="text-sm text-gray-600">Report Types</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div className="text-center p-4 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-green-600">∞</div>
                  <div className="text-sm text-gray-600">Data Points</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div className="text-center p-4 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">24/7</div>
                  <div className="text-sm text-gray-600">Availability</div>
                </div>
              </Col>
              <Col xs={12} sm={6}>
                <div className="text-center p-4 bg-white rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">PDF</div>
                  <div className="text-sm text-gray-600">Export Format</div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Coming Soon Features */}
          <Card className="bg-yellow-50 border-yellow-200">
            <div className="text-center text-yellow-800">
              <h3 className="font-semibold mb-2">🚀 More Reports Coming Soon</h3>
              <p className="text-sm mb-3">
                We&apos;re working on additional reporting features to help you make better business decisions.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                <div>• Financial Reports</div>
                <div>• Customer Analytics</div>
                <div>• Expense Reports</div>
                <div>• Performance Dashboards</div>
              </div>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default ReportsPage;
