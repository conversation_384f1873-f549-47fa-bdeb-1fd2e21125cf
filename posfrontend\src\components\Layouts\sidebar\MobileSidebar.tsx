"use client";

import { Logo } from "@/components/logo";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { NAV_DATA, getMenuItemsByRole } from "./data";
import { ArrowLeftIcon, ChevronUp } from "./icons";
import { MenuItem } from "./menu-item";
import { useSidebarContext } from "./sidebar-context";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";

// Define types for navigation items
interface SubItem {
  title: string;
  url: string;
}

interface NavItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  items: SubItem[];
  url?: string;
}

interface NavSection {
  label: string;
  items: NavItem[];
}

export function MobileSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { setIsOpen, isOpen, toggleSidebar, isMobile } = useSidebarContext();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Get user role from Redux store
  const { user } = useSelector((state: RootState) => state.auth);

  // Get menu items based on user role
  const menuData = user ? getMenuItemsByRole(user.role) : NAV_DATA;

  // Track previous pathname to detect navigation
  const [prevPathname, setPrevPathname] = useState(pathname);

  // Only close sidebar when navigating to a new page, not on initial render
  useEffect(() => {
    if (
      prevPathname !== pathname &&
      prevPathname !== "" &&
      isMobile &&
      isOpen
    ) {
      console.log("MobileSidebar - Path changed, closing sidebar on mobile", {
        from: prevPathname,
        to: pathname,
      });

      // Use timeout to prevent immediate closing
      setTimeout(() => {
        setIsOpen(false);
      }, 100);
    }

    // Update previous pathname
    setPrevPathname(pathname);
  }, [pathname, isMobile, isOpen, setIsOpen, prevPathname]);

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) => (prev.includes(title) ? [] : [title]));
  };

  useEffect(() => {
    menuData.some((section: NavSection) => {
      return section.items.some((item: NavItem) => {
        return item.items.some((subItem: SubItem) => {
          if (subItem.url === pathname) {
            if (!expandedItems.includes(item.title)) {
              toggleExpanded(item.title);
            }
            return true;
          }
        });
      });
    });
  }, [pathname, menuData, expandedItems]);

  // Only render when in mobile mode AND explicitly toggled open
  if (!isMobile || !isOpen) {
    return null;
  }

  // Add debug log to track when sidebar is opened
  console.log("Mobile sidebar is open", { isMobile, isOpen });

  return (
    <>
      {/* Mobile Overlay */}
      <div
        className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
        onClick={() => {
          console.log("Overlay clicked, closing sidebar with delay");
          // Use timeout to prevent race conditions
          setTimeout(() => {
            setIsOpen(false);
          }, 50);
        }}
        aria-hidden="true"
      />

      {/* Mobile Sidebar */}
      <aside
        className="fixed bottom-0 left-0 top-0 z-50 h-screen w-[290px] overflow-hidden border-r border-gray-200 bg-white transition-transform duration-300 ease-in-out"
        aria-label="Mobile navigation"
      >
        <div className="flex h-full flex-col py-10 pl-[25px] pr-[7px]">
          <div className="relative pr-4.5">
            <Link
              href={"/dashboard"}
              onClick={(e) => {

                const isOnUsersPage =
                  window.location.pathname.includes("/dashboard/users");

                if (isOnUsersPage) {
                  toggleSidebar();
                  return; // Allow default navigation
                }

                // Otherwise use client-side navigation
                e.preventDefault();
                toggleSidebar();

                // Use Next.js router for client-side navigation
                router.push("/dashboard");
              }}
              className="px-0 py-2.5 min-[850px]:py-0"
            >
              <Logo />
            </Link>

            <button
              onClick={(e) => {
                // Prevent event bubbling
                e.stopPropagation();
                console.log(
                  "Mobile back button clicked, closing sidebar with delay",
                );
                // Use direct setIsOpen instead of toggleSidebar to ensure it closes
                setTimeout(() => {
                  setIsOpen(false);
                }, 50);
              }}
              className="mobile-back-button absolute right-0 top-1/2 -translate-y-1/2 text-right"
              aria-label="Close sidebar"
            >
              <span className="sr-only">Close Menu</span>
              <ArrowLeftIcon className="ml-auto size-7" />
            </button>
          </div>

          {/* Navigation */}
          <div className="custom-scrollbar mt-6 flex-1 overflow-y-auto pr-3">
            {menuData.map((section: NavSection) => (
              <div key={section.label} className="mb-6">
                <h2 className="mb-5 text-sm font-medium text-gray-600">
                  {section.label}
                </h2>

                <nav role="navigation" aria-label={section.label}>
                  <ul className="space-y-2">
                    {section.items.map((item: NavItem) => (
                      <li key={item.title}>
                        {item.items.length ? (
                          <div>
                            <MenuItem
                              isActive={item.items.some(
                                ({ url }) => url === pathname,
                              )}
                              onClick={() => toggleExpanded(item.title)}
                            >
                              <item.icon
                                className="size-6 shrink-0"
                                aria-hidden="true"
                              />

                              <span>{item.title}</span>

                              <ChevronUp
                                className={cn(
                                  "ml-auto rotate-180 transition-transform duration-200",
                                  expandedItems.includes(item.title) &&
                                    "rotate-0",
                                )}
                                aria-hidden="true"
                              />
                            </MenuItem>

                            {expandedItems.includes(item.title) && (
                              <ul
                                className="ml-9 mr-0 space-y-1.5 pb-[15px] pr-0 pt-2"
                                role="menu"
                              >
                                {item.items.map((subItem: SubItem) => (
                                  <li key={subItem.title} role="none">
                                    <MenuItem
                                      as="link"
                                      href={subItem.url}
                                      isActive={pathname === subItem.url}
                                    >
                                      <span>{subItem.title}</span>
                                    </MenuItem>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ) : (
                          (() => {
                            const href =
                              "url" in item && item.url
                                ? item.url
                                : "/" +
                                  item.title.toLowerCase().split(" ").join("-");

                            return (
                              <MenuItem
                                className="flex items-center gap-3 py-3"
                                as="link"
                                href={href}
                                isActive={pathname === href}
                              >
                                <item.icon
                                  className="size-6 shrink-0"
                                  aria-hidden="true"
                                />

                                <span>{item.title}</span>
                              </MenuItem>
                            );
                          })()
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            ))}
          </div>
        </div>
      </aside>
    </>
  );
}
