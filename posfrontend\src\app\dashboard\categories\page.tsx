"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Spin, notification } from "antd";
import { TagOutlined, LoadingOutlined } from "@ant-design/icons";
import { useAuth } from "@/hooks/useAuth";
import { useCategoryList } from "@/hooks/categories/useCategoryList";
import { useCategoryDelete } from "@/hooks/categories/useCategoryDelete";
import { useCategoryBulkDelete } from "@/hooks/categories/useCategoryBulkDelete";
import CategoryTable from "@/components/Categories/CategoryTable";
import CategoryPagination from "@/components/Categories/CategoryPagination";
import CategoryFormPanel from "@/components/Categories/CategoryFormPanel";
import CategoryDetailPanel from "@/components/Categories/CategoryDetailPanel";
import CategorySearch from "@/components/Categories/CategorySearch";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { Category } from "@/reduxRTK/services/categoryApi";
import { showMessage } from "@/utils/showMessage";
import "./categories.css";

// Custom hook for responsive design
const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    // Handler to call on window resize
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);

      // Call handler right away so state gets updated with initial window size
      handleResize();

      // Remove event listener on cleanup
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []); // Empty array ensures that effect is only run on mount and unmount

  return windowSize;
};

const CategoriesPage = () => {
  const { user: currentUser } = useAuth();
  const { width } = useWindowSize();
  const isMobile = width < 640;

  // UI state
  const [isAddPanelOpen, setIsAddPanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Category list state and handlers
  const {
    categories,
    total,
    page,
    limit,
    isLoading,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
  } = useCategoryList();

  // Delete category hook
  const { deleteCategory, isDeleting } = useCategoryDelete(() => {
    refetch();
    setIsDeleteDialogOpen(false);
  });

  // Bulk delete category hook
  const { bulkDeleteCategories, isDeleting: isBulkDeleting } = useCategoryBulkDelete(() => {
    setIsBulkDeleteDialogOpen(false);
    refetch();
  });

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [categoriesToDelete, setCategoriesToDelete] = useState<number[]>([]);

  // Handle view category
  const handleViewCategory = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    setIsDetailPanelOpen(true);
  };

  // Handle edit category
  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsEditPanelOpen(true);
  };

  // Handle delete category
  const handleDeleteCategory = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete category
  const confirmDeleteCategory = async () => {
    if (selectedCategoryId) {
      try {
        await deleteCategory(selectedCategoryId);
      } catch (error) {
        // Error is already handled in the hook
        console.error("Error deleting category:", error);
      }
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setSelectedCategoryId(null);
  };

  // Handle bulk delete
  const handleBulkDelete = (categoryIds: number[]) => {
    console.log("handleBulkDelete called with categoryIds:", categoryIds);
    setCategoriesToDelete(categoryIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with categories:", categoriesToDelete);

    if (categoriesToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple categories
        await bulkDeleteCategories(categoriesToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setCategoriesToDelete([]);
  };

  // Handle edit from detail panel
  const handleEditFromDetail = (categoryId: number) => {
    setIsDetailPanelOpen(false);
    const category = categories.find(c => c.id === categoryId) || null;
    if (category) {
      handleEditCategory(category);
    }
  };

  // Check if user can add categories (only admin can manage categories)
  const canAddCategory = currentUser?.role === "admin";

  // Handle add category
  const handleAddCategory = () => {
    console.log("Add category button clicked");
    setSelectedCategory(null);
    // Use a timeout to ensure state updates properly
    setTimeout(() => {
      setIsAddPanelOpen(true);
    }, 0);
  };

  // Monitor isAddPanelOpen changes
  useEffect(() => {
    console.log("isAddPanelOpen changed:", isAddPanelOpen);
  }, [isAddPanelOpen]);

  // Show welcome message on component mount
  // useEffect(() => {
  //   showMessage("success", "Welcome to Category Management");
  // }, []);

  if (isLoading) {
    return (
      <div className="p-2 sm:p-4 w-full">
        <Card
          title={<span className="text-gray-800">Category Management</span>}
          styles={{
            body: { padding: '16px', minHeight: '200px', backgroundColor: '#ffffff' },
            header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
          }}
        >
          <div className="flex justify-center items-center h-60">
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
          </div>
        </Card>
      </div>
    );
  }

  // We'll handle empty state within the main layout to keep the search visible

  return (
    <div className="p-2 sm:p-4 w-full">
      <Card
        title={<span className="text-gray-800">Category Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: { padding: '12px', overflow: 'hidden', backgroundColor: '#ffffff' },
          header: { padding: isMobile ? '12px 16px' : '16px 24px', backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' }
        }}
        extra={
          canAddCategory && (
            <Button
              type="primary"
              icon={<TagOutlined />}
              onClick={handleAddCategory}
              size={isMobile ? "small" : "middle"}
            >
              {isMobile ? "" : "Add Category"}
            </Button>
          )
        }
      >
        <div className="w-full bg-white rounded-md shadow-sm overflow-hidden border border-gray-200">
          {/* Search Component - Always visible */}
          <CategorySearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex justify-center items-center h-60 bg-gray-50">
              <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
            </div>
          ) : (
            <>
              {/* Category Table Component */}
              {categories.length > 0 ? (
                <CategoryTable
                  categories={categories}
                  loading={false}
                  onView={handleViewCategory}
                  onEdit={handleEditCategory}
                  onDelete={handleDeleteCategory}
                  onBulkDelete={handleBulkDelete}
                  isMobile={isMobile}
                />
              ) : (
                <div className="flex flex-col justify-center items-center h-60 bg-gray-50 text-gray-800">
                  {searchTerm ? (
                    <>
                      <p>No categories found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearchTerm('')}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No categories found. {canAddCategory && "Click 'Add Category' to create one."}</p>
                  )}
                </div>
              )}

              {/* Pagination Component - Only show if we have results */}
              {categories.length > 0 && (
                <CategoryPagination
                  current={page}
                  pageSize={limit}
                  total={total}
                  onChange={handlePageChange}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
        </div>
      </Card>

      {/* Add Category Panel */}
      <CategoryFormPanel
        isOpen={isAddPanelOpen}
        onClose={() => setIsAddPanelOpen(false)}
        onSuccess={() => {
          setIsAddPanelOpen(false);
          refetch();
        }}
        currentUser={currentUser}
      />

      {/* Edit Category Panel */}
      <CategoryFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);
          refetch();
        }}
        category={selectedCategory}
        currentUser={currentUser}
      />

      {/* View Category Details Panel */}
      <CategoryDetailPanel
        isOpen={isDetailPanelOpen}
        onClose={() => {
          setIsDetailPanelOpen(false);
          setSelectedCategoryId(null);
        }}
        categoryId={selectedCategoryId}
        onEdit={handleEditFromDetail}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDelete}
        onConfirm={confirmDeleteCategory}
        title="Delete Category"
        message="Are you sure you want to delete this category? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        isLoading={isDeleting}
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Categories"
        message={`Are you sure you want to delete ${categoriesToDelete.length} categories? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default CategoriesPage;
