# NEXAPO Landing Page - Deployment Guide for Shared Hosting

## Environment Variables Setup

Your NEXAPO landing page requires these environment variables to function properly:

### Required Environment Variables:
- `GMAIL_USER` - Your Gmail address (e.g., <EMAIL>)
- `GMAIL_APP_PASSWORD` - Gmail app password (16-character password from <PERSON>)
- `ADMIN_EMAIL` - Email where contact form submissions are sent

## Deployment Options

### Option 1: Hosting with Environment Variable Support (Recommended)

**Popular providers that support environment variables:**
- Vercel (Free tier available)
- Netlify (Free tier available)
- Railway
- Render
- DigitalOcean App Platform

**Steps:**
1. Build your application: `npm run build`
2. Upload the built files to your hosting provider
3. Set environment variables in your hosting provider's dashboard:
   - `GMAIL_USER=<EMAIL>`
   - `GMAIL_APP_PASSWORD=your-16-character-app-password`
   - `ADMIN_EMAIL=<EMAIL>`

### Option 2: Traditional Shared Hosting (cPanel, etc.)

**If your hosting provider doesn't support environment variables:**

1. **Build the application locally:**
   ```bash
   npm run build
   ```

2. **Create environment file on server:**
   After uploading, create a `.env.production` file in your app's root directory on the server:
   ```
   GMAIL_USER=<EMAIL>
   GMAIL_APP_PASSWORD=your-16-character-app-password
   ADMIN_EMAIL=<EMAIL>
   ```

3. **Upload files:**
   - Upload the `.next` folder
   - Upload `package.json`
   - Upload `next.config.js`
   - Upload your `.env.production` file
   - Upload any other necessary files

4. **Install dependencies on server:**
   ```bash
   npm install --production
   ```

5. **Start the application:**
   ```bash
   npm start
   ```

## Security Considerations

### For Production:
1. **Never commit sensitive environment variables to Git**
2. **Use strong, unique Gmail app passwords**
3. **Regularly rotate your Gmail app password**
4. **Monitor your email sending limits**

### Gmail App Password Setup:
1. Go to https://myaccount.google.com/security
2. Enable 2-Step Verification
3. Go to "App passwords" section
4. Generate a new app password for "Mail"
5. Use the 16-character password as `GMAIL_APP_PASSWORD`

## Troubleshooting

### Contact Form Not Working:
1. Check if environment variables are set correctly
2. Verify Gmail app password is valid
3. Check server logs for error messages
4. Ensure Gmail account has 2FA enabled

### Build Issues:
1. Run `npm install` to ensure all dependencies are installed
2. Check for TypeScript errors: `npm run lint`
3. Verify Node.js version compatibility (Node 18+ recommended)

## Alternative Email Services

If you prefer not to use Gmail, you can modify the email configuration to use:
- SendGrid (Free tier: 100 emails/day)
- Mailgun (Free tier: 5,000 emails/month)
- Amazon SES
- Any SMTP service

## Support

For deployment issues, contact: <EMAIL>
