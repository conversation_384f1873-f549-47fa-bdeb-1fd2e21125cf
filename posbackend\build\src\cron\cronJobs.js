"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startCronJobs = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const updatePaymentStatus_1 = require("../utils/updatePaymentStatus");
const startCronJobs = () => {
    // Run every day at midnight (00:00 UTC) - Check for overdue payments
    node_cron_1.default.schedule("0 0 * * *", async () => {
        console.log("🕛 Running daily subscription management tasks...");
        try {
            // First fix any incorrect subscription periods
            console.log("🔧 Fixing incorrect subscription periods...");
            await (0, updatePaymentStatus_1.fixIncorrectSubscriptionPeriods)();
            // Then check for subscription renewals (users who need to renew)
            console.log("🔄 Checking subscription renewals...");
            await (0, updatePaymentStatus_1.checkSubscriptionRenewals)();
            // Finally mark overdue payments
            console.log("⏰ Checking for overdue payments...");
            await (0, updatePaymentStatus_1.markOverduePayments)();
            console.log("✅ Daily subscription management completed successfully");
        }
        catch (error) {
            console.error("❌ Error in daily subscription management:", error);
        }
    });
    // Run every hour during business hours (8 AM to 8 PM UTC) for more frequent checks
    node_cron_1.default.schedule("0 8-20 * * *", async () => {
        console.log("🕐 Running hourly subscription status check...");
        try {
            await (0, updatePaymentStatus_1.checkSubscriptionRenewals)();
            console.log("✅ Hourly subscription check completed");
        }
        catch (error) {
            console.error("❌ Error in hourly subscription check:", error);
        }
    });
    console.log("✅ Cron jobs initialized - Daily and hourly subscription management active.");
};
exports.startCronJobs = startCronJobs;
