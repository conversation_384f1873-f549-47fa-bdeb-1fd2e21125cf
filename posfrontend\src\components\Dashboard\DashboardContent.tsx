"use client";

import React, { useState, useEffect } from "react";
import { ClientPaymentsOverview } from "@/components/Charts/payments-overview/client";
import { ClientWeeksProfit } from "@/components/Charts/weeks-profit/client";
import { createTimeFrameExtractor } from "@/utils/timeframe-extractor";
import { ClientOverviewCards } from "@/app/dashboard/(home)/_components/overview-cards/client";
import { OverviewCardsSkeleton } from "@/app/dashboard/(home)/_components/overview-cards/skeleton";
import RoleBasedDashboard from "@/components/Dashboard/RoleBasedDashboard";

import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface DashboardContentProps {
  selected_time_frame?: string | null;
}

const DashboardContent: React.FC<DashboardContentProps> = ({
  selected_time_frame
}) => {
  const { user } = useSelector((state: RootState) => state.auth);

  // Debug log (only in development)
  useEffect(() => {
    console.log("DashboardContent - Mounted with params:", {
      selected_time_frame,
      user: user?.name,
      role: user?.role
    });
  }, [selected_time_frame, user]);

  // If no user is found after client-side hydration, show message
  if (!user) {
    return (
      <div className="flex h-full w-full items-center justify-center p-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p>Please log in to view the dashboard.</p>
        </div>
      </div>
    );
  }

  // Safe to use the time frame extractor now that we're on the client
  const extractTimeFrame = createTimeFrameExtractor(selected_time_frame || undefined);



  return (
    <div className="dashboard-content w-full overflow-hidden">
      {/* Role-based dashboard content */}
      <RoleBasedDashboard
        extractTimeFrame={extractTimeFrame}
      />
    </div>
  );
};

export default DashboardContent;

