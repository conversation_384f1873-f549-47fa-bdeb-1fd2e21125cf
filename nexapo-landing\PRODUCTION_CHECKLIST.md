# NEXAPO Landing Page - Production Deployment Checklist

## Pre-Deployment Checklist

### 1. Environment Variables
- [ ] Gmail account has 2-Factor Authentication enabled
- [ ] Gmail App Password generated and tested
- [ ] Environment variables documented
- [ ] `.env.local` file excluded from version control
- [ ] Production environment variables prepared

### 2. Code Quality
- [ ] Run `npm run lint` - no errors
- [ ] Run `npm run build` - builds successfully
- [ ] Test contact form locally
- [ ] All images optimized and compressed
- [ ] Remove any console.log statements

### 3. Security
- [ ] No sensitive data in client-side code
- [ ] Environment variables properly secured
- [ ] HTTPS enabled on hosting platform
- [ ] Email rate limiting considered

## Deployment Steps

### For Modern Hosting (Vercel, Netlify, etc.)
1. [ ] Connect repository to hosting platform
2. [ ] Set environment variables in hosting dashboard
3. [ ] Configure build command: `npm run build`
4. [ ] Configure start command: `npm start`
5. [ ] Deploy and test

### For Traditional Shared Hosting
1. [ ] Run `npm run build` locally
2. [ ] Upload built files to server
3. [ ] Create `.env.production` file on server
4. [ ] Install dependencies: `npm install --production`
5. [ ] Start application: `npm start`

## Post-Deployment Testing

### Functionality Tests
- [ ] Landing page loads correctly
- [ ] All navigation links work
- [ ] Contact form submits successfully
- [ ] Email notifications received
- [ ] Auto-reply emails sent
- [ ] Mobile responsiveness verified
- [ ] Page load speed acceptable

### Email Testing
- [ ] Test contact form with valid data
- [ ] Verify admin receives notification email
- [ ] Verify customer receives auto-reply
- [ ] Test with different email providers
- [ ] Check spam folders

## Monitoring & Maintenance

### Regular Checks
- [ ] Monitor email delivery rates
- [ ] Check server logs for errors
- [ ] Verify SSL certificate validity
- [ ] Monitor website uptime
- [ ] Update dependencies regularly

### Gmail Limits
- Gmail allows 500 emails per day for free accounts
- Monitor usage to avoid hitting limits
- Consider upgrading to Google Workspace if needed

## Troubleshooting Common Issues

### Contact Form Not Working
1. Check environment variables are set correctly
2. Verify Gmail app password is valid
3. Check server logs for detailed error messages
4. Test Gmail SMTP connection manually

### Build Failures
1. Clear node_modules and reinstall: `rm -rf node_modules && npm install`
2. Check Node.js version compatibility
3. Verify all dependencies are compatible
4. Check for TypeScript errors

### Email Delivery Issues
1. Check Gmail account status
2. Verify 2FA is enabled
3. Regenerate app password if needed
4. Check email content for spam triggers

## Performance Optimization

### Before Going Live
- [ ] Enable image optimization
- [ ] Minimize CSS and JavaScript
- [ ] Enable gzip compression
- [ ] Set up CDN if available
- [ ] Configure caching headers

## Backup & Recovery

### Important Files to Backup
- [ ] Source code repository
- [ ] Environment variables documentation
- [ ] Gmail app password (securely stored)
- [ ] Hosting configuration settings

## Support Contacts

- **Technical Issues**: <EMAIL>
- **Hosting Support**: Contact your hosting provider
- **Gmail Issues**: Google Support
