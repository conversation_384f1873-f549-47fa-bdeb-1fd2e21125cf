"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertKoboToCedis = exports.convertCedisToKobo = exports.getPaystackPublicKey = exports.verifyPaystackPayment = exports.initializePaystackPayment = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Paystack configuration
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY || '';
const PAYSTACK_PUBLIC_KEY = process.env.PAYSTACK_PUBLIC_KEY || '';
const PAYSTACK_BASE_URL = 'https://api.paystack.co';
// Disable mock verification to test real Paystack integration
const MOCK_VERIFICATION = false; // Temporarily disabled for testing
/**
 * Initialize a Paystack payment transaction
 * @param email Customer email
 * @param amount Amount in smallest currency unit (pesewas for GHS, kobo for NGN)
 * @param reference Unique transaction reference
 * @param callbackUrl Callback URL after payment

 * @returns Payment initialization result
 */
const initializePaystackPayment = async (email, amount, reference, callbackUrl) => {
    console.log(`Initializing Paystack payment for ${email}, amount: ${amount} kobo, reference: ${reference}`);
    // Use mock initialization in development
    if (MOCK_VERIFICATION) {
        console.log('Using mock payment initialization (development mode)');
        return {
            success: true,
            data: {
                authorization_url: `https://checkout.paystack.com/mock-${reference}`,
                access_code: `mock_access_code_${reference}`,
                reference: reference
            }
        };
    }
    try {
        // Check if secret key is available
        if (!PAYSTACK_SECRET_KEY) {
            throw new Error('Paystack secret key not configured');
        }
        const payload = {
            email,
            amount: Math.round(amount), // Ensure amount is an integer (pesewas for GHS)
            reference,
            // Remove currency parameter to let Paystack use account default (GHS)
            callback_url: callbackUrl,
            metadata: {
                custom_fields: [
                    {
                        display_name: "Payment For",
                        variable_name: "payment_for",
                        value: "POS System Subscription"
                    }
                ]
            }
        };
        console.log('Paystack payload:', JSON.stringify(payload, null, 2));
        // Make API request to Paystack
        const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        // Check response status
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Paystack API error (${response.status}):`, errorText);
            throw new Error(`Paystack API returned status ${response.status}: ${errorText}`);
        }
        const result = await response.json();
        console.log('Paystack initialization response:', result);
        if (result.status) {
            return {
                success: true,
                data: result.data
            };
        }
        else {
            console.error('Paystack initialization failed:', result.message);
            return {
                success: false,
                message: result.message || 'Payment initialization failed'
            };
        }
    }
    catch (error) {
        console.error('Paystack initialization error:', error);
        return {
            success: false,
            message: error.message || 'Payment initialization failed'
        };
    }
};
exports.initializePaystackPayment = initializePaystackPayment;
/**
 * Verify a Paystack payment transaction
 * @param reference The transaction reference
 * @returns Verification result
 */
const verifyPaystackPayment = async (reference) => {
    console.log(`Verifying Paystack payment with reference: ${reference}`);
    // Use mock verification in development
    if (MOCK_VERIFICATION) {
        console.log('Using mock payment verification (development mode)');
        return {
            success: true,
            data: {
                status: 'success',
                reference: reference,
                amount: 8900, // Mock amount in kobo
                currency: 'GHS',
                channel: 'card',
                authorization: {
                    authorization_code: 'AUTH_mock123',
                    channel: 'card'
                }
            }
        };
    }
    try {
        // Check if secret key is available
        if (!PAYSTACK_SECRET_KEY) {
            throw new Error('Paystack secret key not configured');
        }
        // Make API request to Paystack
        const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        // Check response status
        if (!response.ok) {
            throw new Error(`Paystack API returned status ${response.status}`);
        }
        const result = await response.json();
        if (result.status) {
            return {
                success: true,
                data: result.data
            };
        }
        else {
            return {
                success: false,
                message: result.message || 'Payment verification failed'
            };
        }
    }
    catch (error) {
        console.error('Paystack verification error:', error);
        return {
            success: false,
            message: error.message || 'Payment verification failed'
        };
    }
};
exports.verifyPaystackPayment = verifyPaystackPayment;
/**
 * Get Paystack public key for frontend
 * @returns Public key
 */
const getPaystackPublicKey = () => {
    return PAYSTACK_PUBLIC_KEY;
};
exports.getPaystackPublicKey = getPaystackPublicKey;
/**
 * Convert amount from Ghana Cedis to Pesewas (smallest unit for GHS)
 * @param amountInCedis Amount in Ghana Cedis
 * @returns Amount in Pesewas
 */
const convertCedisToKobo = (amountInCedis) => {
    return Math.round(amountInCedis * 100);
};
exports.convertCedisToKobo = convertCedisToKobo;
/**
 * Convert amount from Pesewas to Ghana Cedis
 * @param amountInPesewas Amount in Pesewas
 * @returns Amount in Ghana Cedis
 */
const convertKoboToCedis = (amountInPesewas) => {
    return amountInPesewas / 100;
};
exports.convertKoboToCedis = convertKoboToCedis;
