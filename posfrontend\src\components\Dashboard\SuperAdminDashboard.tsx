"use client";

import React from 'react';
import { useGetDashboardStatsQuery } from '@/reduxRTK/services/dashboardApi';
import { useGetAllUsersQuery } from '@/reduxRTK/services/authApi';
import { Spin, Card, Row, Col, Statistic } from 'antd';
import { LoadingOutlined, DollarOutlined, UserOutlined, ShopOutlined, TeamOutlined } from '@ant-design/icons';
import AdminSubscriptionsTable from './AdminSubscriptionsTable';
import RevenueChart from './Charts/RevenueChart';
import SubscriptionDistributionChart from './Charts/SubscriptionDistributionChart';
import UserGrowthChart from './Charts/UserGrowthChart';
import PaymentStatusChart from './Charts/PaymentStatusChart';

interface SuperAdminDashboardProps {
  className?: string;
}

const SuperAdminDashboard: React.FC<SuperAdminDashboardProps> = ({ className = "" }) => {
  // Fetch dashboard stats
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useGetDashboardStatsQuery();

  // Fetch users data for charts
  const { data: usersData, isLoading: usersLoading } = useGetAllUsersQuery({
    page: 1,
    limit: 1000, // Get all users for analytics
    search: ''
  });

  const stats = dashboardData?.data as import('@/reduxRTK/services/dashboardApi').SuperAdminStats | undefined;
  const users = (usersData as any)?.data?.users || [];

  // Debug logging
  console.log('🔍 SuperAdmin Dashboard Data:', {
    dashboardData,
    stats,
    usersCount: users.length,
    dashboardLoading,
    usersLoading,
    dashboardError
  });

  if (dashboardLoading || usersLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
      </div>
    );
  }

  if (dashboardError) {
    return (
      <div className="text-center text-red-500 p-8">
        <p>Error loading dashboard data. Please try again.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">SuperAdmin Dashboard</h1>
        <p className="text-gray-600">Comprehensive overview of your POS system performance</p>
      </div>

      {/* Key Metrics Cards */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center shadow-md hover:shadow-lg transition-shadow">
            <Statistic
              title="Total Revenue"
              value={Number(stats?.revenue?.value) || 0}
              prefix="₵"
              precision={0}
              valueStyle={{ color: '#3f8600', fontSize: '24px', fontWeight: 'bold' }}
            />
            <div className="text-sm text-gray-500 mt-2">Monthly recurring revenue</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center shadow-md hover:shadow-lg transition-shadow">
            <Statistic
              title="Active Stores"
              value={Number(stats?.stores?.value) || 0}
              precision={0}
              valueStyle={{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold' }}
            />
            <div className="text-sm text-gray-500 mt-2">Stores using the system</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center shadow-md hover:shadow-lg transition-shadow">
            <Statistic
              title="Admin Users"
              value={Number(stats?.admins?.value) || 0}
              precision={0}
              valueStyle={{ color: '#722ed1', fontSize: '24px', fontWeight: 'bold' }}
            />
            <div className="text-sm text-gray-500 mt-2">Paying admin accounts</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center shadow-md hover:shadow-lg transition-shadow">
            <Statistic
              title="Total Users"
              value={Number(stats?.users?.value) || 0}
              precision={0}
              valueStyle={{ color: '#fa8c16', fontSize: '24px', fontWeight: 'bold' }}
            />
            <div className="text-sm text-gray-500 mt-2">All system users</div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 1 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="Revenue Trends" className="shadow-md h-96">
            <RevenueChart users={users} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Subscription Distribution" className="shadow-md h-96">
            <SubscriptionDistributionChart users={users} />
          </Card>
        </Col>
      </Row>

      {/* Charts Row 2 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="User Growth" className="shadow-md h-96">
            <UserGrowthChart users={users} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Payment Status Overview" className="shadow-md h-96">
            <PaymentStatusChart users={users} />
          </Card>
        </Col>
      </Row>

      {/* Admin Subscriptions Table */}
      <div className="mt-8">
        <AdminSubscriptionsTable className="shadow-lg" />
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
