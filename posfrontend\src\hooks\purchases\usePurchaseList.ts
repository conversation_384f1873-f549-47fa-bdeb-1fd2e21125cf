"use client";

import { useState, useEffect } from "react";
import { useGetAllPurchasesQuery, Purchase } from "@/reduxRTK/services/purchaseApi";
import { useDebounce } from "@/hooks/useDebounce";

export const usePurchaseList = (initialPage = 1, initialLimit = 10) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [searchTerm, setSearchTerm] = useState('');

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // Fetch purchases with pagination and search
  const {
    data,
    error,
    isLoading,
    refetch
  } = useGetAllPurchasesQuery({
    page,
    limit,
    search: debouncedSearchTerm
  });

  // Extract purchases and pagination info from the response
  // The backend already filters purchases based on user role and permissions
  const purchases: Purchase[] = data?.data?.purchases || [];
  const total: number = data?.data?.total || 0;
  
  console.log("Purchases from API:", purchases);
  console.log("Total purchases:", total);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to page 1 when changing limit
  };

  return {
    purchases,
    total,
    page,
    limit,
    isLoading,
    error,
    refetch,
    searchTerm,
    setSearchTerm,
    handlePageChange,
    handleLimitChange
  };
};
