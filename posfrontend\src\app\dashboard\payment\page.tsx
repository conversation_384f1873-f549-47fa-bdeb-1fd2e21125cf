"use client";
import React, { useEffect } from "react";
import { Tabs, Card } from "antd";
import PaymentForm from "@/components/Payment/PaymentForm";
import PaymentStatus from "@/components/Payment/PaymentStatus";
import PaymentHistory from "@/components/Payment/PaymentHistory";
import { useRouter } from "next/navigation";

const { TabPane } = Tabs;

export default function DashboardPaymentPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the standalone payment page
    router.push("/payment");
  }, [router]);

  return null;
}
