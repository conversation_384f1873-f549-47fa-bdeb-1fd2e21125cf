"use client";

import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface RevenueChartProps {
  users: any[];
}

const RevenueChart: React.FC<RevenueChartProps> = ({ users }) => {
  const chartData = useMemo(() => {
    // Filter admin users with payment data
    const adminUsers = users.filter(user =>
      user.role === 'admin' &&
      user.lastPaymentDate
    );

    console.log('📊 Revenue Chart - Admin users found:', adminUsers.length);

    // Create last 12 months array
    const last12Months: Array<{
      monthKey: string;
      monthLabel: string;
      revenue: number;
      adminCount: number;
    }> = [];
    for (let i = 11; i >= 0; i--) {
      const month = dayjs().subtract(i, 'month');
      last12Months.push({
        monthKey: month.format('YYYY-MM'),
        monthLabel: month.format('MMM YYYY'),
        revenue: 0,
        adminCount: 0
      });
    }

    // Calculate revenue for each admin user and add to appropriate month
    adminUsers.forEach(user => {
      if (user.lastPaymentDate && user.nextPaymentDue) {
        const lastPayment = dayjs(user.lastPaymentDate);
        const nextDue = dayjs(user.nextPaymentDue);
        const daysDiff = nextDue.diff(lastPayment, 'day');

        // Determine subscription amount based on period
        let amount = 89; // Monthly default
        if (daysDiff >= 350) {
          amount = 1014; // Annual
        } else if (daysDiff >= 80) {
          amount = 259; // Quarterly
        }

        console.log(`💰 User ${user.email}: ${daysDiff} days = ₵${amount}`);

        const paymentMonth = lastPayment.format('YYYY-MM');
        const monthData = last12Months.find(m => m.monthKey === paymentMonth);
        if (monthData) {
          monthData.revenue += amount;
        }
      }
    });

    const chartData = last12Months.map(month => ({
      x: month.monthLabel,
      y: month.revenue
    }));

    console.log('📈 Revenue chart data:', chartData);
    return chartData;
  }, [users]);

  const options: ApexOptions = {
    chart: {
      type: 'area',
      height: 300,
      toolbar: {
        show: false,
      },
      fontFamily: 'inherit',
    },
    colors: ['#5750F1'],
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.1,
        stops: [0, 90, 100]
      }
    },
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    grid: {
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return `₵${value.toFixed(2)}`;
        }
      }
    },
    xaxis: {
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          fontSize: '12px',
        }
      }
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          return `₵${value}`;
        },
        style: {
          fontSize: '12px',
        }
      }
    }
  };

  const series = [{
    name: 'Revenue',
    data: chartData
  }];

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <p>No revenue data available</p>
          <p className="text-sm">Revenue will appear when admins make payments</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-64">
      <Chart
        options={options}
        series={series}
        type="area"
        height={250}
      />
    </div>
  );
};

export default RevenueChart;
