"use strict";exports.id=1716,exports.ids=[1716],exports.modules={31716:(e,t,a)=>{a.d(t,{A:()=>R});var i=a(58009),l=a(56073),n=a.n(l),s=a(27343),r=a(55681);let c=e=>{let{prefixCls:t,className:a,style:l,size:s,shape:r}=e,c=n()({[`${t}-lg`]:"large"===s,[`${t}-sm`]:"small"===s}),o=n()({[`${t}-circle`]:"circle"===r,[`${t}-square`]:"square"===r,[`${t}-round`]:"round"===r}),g=i.useMemo(()=>"number"==typeof s?{width:s,height:s,lineHeight:`${s}px`}:{},[s]);return i.createElement("span",{className:n()(t,c,o,a),style:Object.assign(Object.assign({},g),l)})};var o=a(1439),g=a(13662),d=a(10941);let u=new o.Mo("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),m=e=>({height:e,lineHeight:(0,o.zA)(e)}),b=e=>Object.assign({width:e},m(e)),$=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:u,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),h=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},m(e)),p=e=>{let{skeletonAvatarCls:t,gradientFromColor:a,controlHeight:i,controlHeightLG:l,controlHeightSM:n}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:a},b(i)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},b(l)),[`${t}${t}-sm`]:Object.assign({},b(n))}},O=e=>{let{controlHeight:t,borderRadiusSM:a,skeletonInputCls:i,controlHeightLG:l,controlHeightSM:n,gradientFromColor:s,calc:r}=e;return{[i]:Object.assign({display:"inline-block",verticalAlign:"top",background:s,borderRadius:a},h(t,r)),[`${i}-lg`]:Object.assign({},h(l,r)),[`${i}-sm`]:Object.assign({},h(n,r))}},j=e=>Object.assign({width:e},m(e)),k=e=>{let{skeletonImageCls:t,imageSizeBase:a,gradientFromColor:i,borderRadiusSM:l,calc:n}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:i,borderRadius:l},j(n(a).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},j(a)),{maxWidth:n(a).mul(4).equal(),maxHeight:n(a).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},v=(e,t,a)=>{let{skeletonButtonCls:i}=e;return{[`${a}${i}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${a}${i}-round`]:{borderRadius:t}}},f=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},m(e)),C=e=>{let{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:i,controlHeightLG:l,controlHeightSM:n,gradientFromColor:s,calc:r}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:s,borderRadius:t,width:r(i).mul(2).equal(),minWidth:r(i).mul(2).equal()},f(i,r))},v(e,i,a)),{[`${a}-lg`]:Object.assign({},f(l,r))}),v(e,l,`${a}-lg`)),{[`${a}-sm`]:Object.assign({},f(n,r))}),v(e,n,`${a}-sm`))},w=e=>{let{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:i,skeletonParagraphCls:l,skeletonButtonCls:n,skeletonInputCls:s,skeletonImageCls:r,controlHeight:c,controlHeightLG:o,controlHeightSM:g,gradientFromColor:d,padding:u,marginSM:m,borderRadius:h,titleHeight:j,blockRadius:v,paragraphLiHeight:f,controlHeightXS:w,paragraphMarginTop:x}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:u,verticalAlign:"top",[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:d},b(c)),[`${a}-circle`]:{borderRadius:"50%"},[`${a}-lg`]:Object.assign({},b(o)),[`${a}-sm`]:Object.assign({},b(g))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[i]:{width:"100%",height:j,background:d,borderRadius:v,[`+ ${l}`]:{marginBlockStart:g}},[l]:{padding:0,"> li":{width:"100%",height:f,listStyle:"none",background:d,borderRadius:v,"+ li":{marginBlockStart:w}}},[`${l}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${i}, ${l} > li`]:{borderRadius:h}}},[`${t}-with-avatar ${t}-content`]:{[i]:{marginBlockStart:m,[`+ ${l}`]:{marginBlockStart:x}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},C(e)),p(e)),O(e)),k(e)),[`${t}${t}-block`]:{width:"100%",[n]:{width:"100%"},[s]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${i},
        ${l} > li,
        ${a},
        ${n},
        ${s},
        ${r}
      `]:Object.assign({},$(e))}}},x=(0,g.OF)("Skeleton",e=>{let{componentCls:t,calc:a}=e;return[w((0,d.oX)(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:a(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"}))]},e=>{let{colorFillContent:t,colorFill:a}=e;return{color:t,colorGradientEnd:a,gradientFromColor:t,gradientToColor:a,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),E=(e,t)=>{let{width:a,rows:i=2}=t;return Array.isArray(a)?a[e]:i-1===e?a:void 0},y=e=>{let{prefixCls:t,className:a,style:l,rows:s=0}=e,r=Array.from({length:s}).map((t,a)=>i.createElement("li",{key:a,style:{width:E(a,e)}}));return i.createElement("ul",{className:n()(t,a),style:l},r)},q=e=>{let{prefixCls:t,className:a,width:l,style:s}=e;return i.createElement("h3",{className:n()(t,a),style:Object.assign({width:l},s)})};function N(e){return e&&"object"==typeof e?e:{}}let A=e=>{let{prefixCls:t,loading:a,className:l,rootClassName:r,style:o,children:g,avatar:d=!1,title:u=!0,paragraph:m=!0,active:b,round:$}=e,{getPrefixCls:h,direction:p,className:O,style:j}=(0,s.TP)("skeleton"),k=h("skeleton",t),[v,f,C]=x(k);if(a||!("loading"in e)){let e,t;let a=!!d,s=!!u,g=!!m;if(a){let t=Object.assign(Object.assign({prefixCls:`${k}-avatar`},s&&!g?{size:"large",shape:"square"}:{size:"large",shape:"circle"}),N(d));e=i.createElement("div",{className:`${k}-header`},i.createElement(c,Object.assign({},t)))}if(s||g){let e,l;if(s){let t=Object.assign(Object.assign({prefixCls:`${k}-title`},function(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}(a,g)),N(u));e=i.createElement(q,Object.assign({},t))}if(g){let e=Object.assign(Object.assign({prefixCls:`${k}-paragraph`},function(e,t){let a={};return e&&t||(a.width="61%"),!e&&t?a.rows=3:a.rows=2,a}(a,s)),N(m));l=i.createElement(y,Object.assign({},e))}t=i.createElement("div",{className:`${k}-content`},e,l)}let h=n()(k,{[`${k}-with-avatar`]:a,[`${k}-active`]:b,[`${k}-rtl`]:"rtl"===p,[`${k}-round`]:$},O,l,r,f,C);return v(i.createElement("div",{className:h,style:Object.assign(Object.assign({},j),o)},e,t))}return null!=g?g:null};A.Button=e=>{let{prefixCls:t,className:a,rootClassName:l,active:o,block:g=!1,size:d="default"}=e,{getPrefixCls:u}=i.useContext(s.QO),m=u("skeleton",t),[b,$,h]=x(m),p=(0,r.A)(e,["prefixCls"]),O=n()(m,`${m}-element`,{[`${m}-active`]:o,[`${m}-block`]:g},a,l,$,h);return b(i.createElement("div",{className:O},i.createElement(c,Object.assign({prefixCls:`${m}-button`,size:d},p))))},A.Avatar=e=>{let{prefixCls:t,className:a,rootClassName:l,active:o,shape:g="circle",size:d="default"}=e,{getPrefixCls:u}=i.useContext(s.QO),m=u("skeleton",t),[b,$,h]=x(m),p=(0,r.A)(e,["prefixCls","className"]),O=n()(m,`${m}-element`,{[`${m}-active`]:o},a,l,$,h);return b(i.createElement("div",{className:O},i.createElement(c,Object.assign({prefixCls:`${m}-avatar`,shape:g,size:d},p))))},A.Input=e=>{let{prefixCls:t,className:a,rootClassName:l,active:o,block:g,size:d="default"}=e,{getPrefixCls:u}=i.useContext(s.QO),m=u("skeleton",t),[b,$,h]=x(m),p=(0,r.A)(e,["prefixCls"]),O=n()(m,`${m}-element`,{[`${m}-active`]:o,[`${m}-block`]:g},a,l,$,h);return b(i.createElement("div",{className:O},i.createElement(c,Object.assign({prefixCls:`${m}-input`,size:d},p))))},A.Image=e=>{let{prefixCls:t,className:a,rootClassName:l,style:r,active:c}=e,{getPrefixCls:o}=i.useContext(s.QO),g=o("skeleton",t),[d,u,m]=x(g),b=n()(g,`${g}-element`,{[`${g}-active`]:c},a,l,u,m);return d(i.createElement("div",{className:b},i.createElement("div",{className:n()(`${g}-image`,a),style:r},i.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${g}-image-svg`},i.createElement("title",null,"Image placeholder"),i.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:`${g}-image-path`})))))},A.Node=e=>{let{prefixCls:t,className:a,rootClassName:l,style:r,active:c,children:o}=e,{getPrefixCls:g}=i.useContext(s.QO),d=g("skeleton",t),[u,m,b]=x(d),$=n()(d,`${d}-element`,{[`${d}-active`]:c},m,a,l,b);return u(i.createElement("div",{className:$},i.createElement("div",{className:n()(`${d}-image`,a),style:r},o)))};let R=A}};