(()=>{var e={};e.id=7810,e.ids=[7810],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},5948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(70260),a=r(28203),n=r(25155),l=r.n(n),o=r(67292),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let c=["",{children:["dashboard",{children:["reports",{children:["inventory",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22605)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\inventory\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\inventory\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/reports/inventory/page",pathname:"/dashboard/reports/inventory",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69902:(e,t,r)=>{Promise.resolve().then(r.bind(r,22605))},32950:(e,t,r)=>{Promise.resolve().then(r.bind(r,39601))},431:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"};var l=r(78480);let o=a.forwardRef(function(e,t){return a.createElement(l.A,(0,s.A)({},e,{ref:t,icon:n}))})},59022:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var l=r(78480);let o=a.forwardRef(function(e,t){return a.createElement(l.A,(0,s.A)({},e,{ref:t,icon:n}))})},58733:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"};var l=r(78480);let o=a.forwardRef(function(e,t){return a.createElement(l.A,(0,s.A)({},e,{ref:t,icon:n}))})},45211:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var l=r(78480);let o=a.forwardRef(function(e,t){return a.createElement(l.A,(0,s.A)({},e,{ref:t,icon:n}))})},85303:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(58009);function a(){let[,e]=s.useReducer(e=>e+1,0);return e}},83893:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,ko:()=>c,ye:()=>l});var s=r(58009),a=r.n(s),n=r(39772);let l=["xxl","xl","lg","md","sm","xs"],o=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}),i=e=>{let t=[].concat(l).reverse();return t.forEach((r,s)=>{let a=r.toUpperCase(),n=`screen${a}Min`,l=`screen${a}`;if(!(e[n]<=e[l]))throw Error(`${n}<=${l} fails : !(${e[n]}<=${e[l]})`);if(s<t.length-1){let r=`screen${a}Max`;if(!(e[l]<=e[r]))throw Error(`${l}<=${r} fails : !(${e[l]}<=${e[r]})`);let n=t[s+1].toUpperCase(),o=`screen${n}Min`;if(!(e[r]<=e[o]))throw Error(`${r}<=${o} fails : !(${e[r]}<=${e[o]})`)}}),e},c=(e,t)=>{if(t){for(let r of l)if(e[r]&&(null==t?void 0:t[r])!==void 0)return t[r]}},d=()=>{let[,e]=(0,n.Ay)(),t=o(i(e));return a().useMemo(()=>{let e=new Map,r=-1,s={};return{responsiveMap:t,matchHandlers:{},dispatch:t=>(s=t,e.forEach(e=>e(s)),e.size>=1),subscribe(t){return e.size||this.register(),r+=1,e.set(r,t),t(s),r},unsubscribe(t){e.delete(t),e.size||this.unregister()},register(){Object.keys(t).forEach(e=>{let r=t[e],a=t=>{let{matches:r}=t;this.dispatch(Object.assign(Object.assign({},s),{[e]:r}))},n=window.matchMedia(r);n.addListener(a),this.matchHandlers[r]={mql:n,listener:a},a(n)})},unregister(){Object.keys(t).forEach(e=>{let r=t[e],s=this.matchHandlers[r];null==s||s.mql.removeListener(null==s?void 0:s.listener)}),e.clear()}}},[e])}},9170:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=r(59286).A},84133:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(58009).createContext)({})},59286:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(58009),a=r(56073),n=r.n(a),l=r(27343),o=r(84133),i=r(49342),c=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],m=s.forwardRef((e,t)=>{let{getPrefixCls:r,direction:a}=s.useContext(l.QO),{gutter:m,wrap:x}=s.useContext(o.A),{prefixCls:p,span:f,order:h,offset:y,push:g,pull:v,className:b,children:j,flex:A,style:w}=e,N=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),S=r("col",p),[$,k,P]=(0,i.xV)(S),O={},C={};u.forEach(t=>{let r={},s=e[t];"number"==typeof s?r.span=s:"object"==typeof s&&(r=s||{}),delete N[t],C=Object.assign(Object.assign({},C),{[`${S}-${t}-${r.span}`]:void 0!==r.span,[`${S}-${t}-order-${r.order}`]:r.order||0===r.order,[`${S}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${S}-${t}-push-${r.push}`]:r.push||0===r.push,[`${S}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${S}-rtl`]:"rtl"===a}),r.flex&&(C[`${S}-${t}-flex`]=!0,O[`--${S}-${t}-flex`]=d(r.flex))});let E=n()(S,{[`${S}-${f}`]:void 0!==f,[`${S}-order-${h}`]:h,[`${S}-offset-${y}`]:y,[`${S}-push-${g}`]:g,[`${S}-pull-${v}`]:v},b,C,k,P),M={};if(m&&m[0]>0){let e=m[0]/2;M.paddingLeft=e,M.paddingRight=e}return A&&(M.flex=d(A),!1!==x||M.minWidth||(M.minWidth=0)),$(s.createElement("div",Object.assign({},N,{style:Object.assign(Object.assign(Object.assign({},M),w),O),className:E,ref:t}),j))})},52271:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(58009),a=r(55977),n=r(85303),l=r(83893);let o=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,s.useRef)(t),o=(0,n.A)(),i=(0,l.Ay)();return(0,a.A)(()=>{let t=i.subscribe(t=>{r.current=t,e&&o()});return()=>i.unsubscribe(t)},[]),r.current}},14207:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(58009),a=r(56073),n=r.n(a),l=r(83893),o=r(27343),i=r(52271),c=r(84133),d=r(49342),u=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};function m(e,t){let[r,a]=s.useState("string"==typeof e?e:""),n=()=>{if("string"==typeof e&&a(e),"object"==typeof e)for(let r=0;r<l.ye.length;r++){let s=l.ye[r];if(!t||!t[s])continue;let n=e[s];if(void 0!==n){a(n);return}}};return s.useEffect(()=>{n()},[JSON.stringify(e),t]),r}let x=s.forwardRef((e,t)=>{let{prefixCls:r,justify:a,align:x,className:p,style:f,children:h,gutter:y=0,wrap:g}=e,v=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:b,direction:j}=s.useContext(o.QO),A=(0,i.A)(!0,null),w=m(x,A),N=m(a,A),S=b("row",r),[$,k,P]=(0,d.L3)(S),O=function(e,t){let r=[void 0,void 0],s=Array.isArray(e)?e:[e,void 0],a=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return s.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let s=0;s<l.ye.length;s++){let n=l.ye[s];if(a[n]&&void 0!==e[n]){r[t]=e[n];break}}else r[t]=e}),r}(y,A),C=n()(S,{[`${S}-no-wrap`]:!1===g,[`${S}-${N}`]:N,[`${S}-${w}`]:w,[`${S}-rtl`]:"rtl"===j},p,k,P),E={},M=null!=O[0]&&O[0]>0?-(O[0]/2):void 0;M&&(E.marginLeft=M,E.marginRight=M);let[R,z]=O;E.rowGap=z;let D=s.useMemo(()=>({gutter:[R,z],wrap:g}),[R,z,g]);return $(s.createElement(c.A.Provider,{value:D},s.createElement("div",Object.assign({},v,{className:C,style:Object.assign(Object.assign({},E),f),ref:t}),h)))})},1236:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=r(14207).A},1387:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var s=r(58009),a=r(85303),n=r(2866),l=r(56073),o=r.n(l),i=r(90365),c=r(27343),d=r(31716);let u=e=>{let t;let{value:r,formatter:a,precision:n,decimalSeparator:l,groupSeparator:o="",prefixCls:i}=e;if("function"==typeof a)t=a(r);else{let e=String(r),a=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(a&&"-"!==e){let e=a[1],r=a[2]||"0",c=a[4]||"";r=r.replace(/\B(?=(\d{3})+(?!\d))/g,o),"number"==typeof n&&(c=c.padEnd(n,"0").slice(0,n>0?n:0)),c&&(c=`${l}${c}`),t=[s.createElement("span",{key:"int",className:`${i}-content-value-int`},e,r),c&&s.createElement("span",{key:"decimal",className:`${i}-content-value-decimal`},c)]}else t=e}return s.createElement("span",{className:`${i}-content-value`},t)};var m=r(47285),x=r(13662),p=r(10941);let f=e=>{let{componentCls:t,marginXXS:r,padding:s,colorTextDescription:a,titleFontSize:n,colorTextHeading:l,contentFontSize:o,fontFamily:i}=e;return{[t]:Object.assign(Object.assign({},(0,m.dF)(e)),{[`${t}-title`]:{marginBottom:r,color:a,fontSize:n},[`${t}-skeleton`]:{paddingTop:s},[`${t}-content`]:{color:l,fontSize:o,fontFamily:i,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:r},[`${t}-content-suffix`]:{marginInlineStart:r}}})}},h=(0,x.OF)("Statistic",e=>[f((0,p.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:r}=e;return{titleFontSize:r,contentFontSize:t}});var y=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let g=e=>{let{prefixCls:t,className:r,rootClassName:a,style:n,valueStyle:l,value:m=0,title:x,valueRender:p,prefix:f,suffix:g,loading:v=!1,formatter:b,precision:j,decimalSeparator:A=".",groupSeparator:w=",",onMouseEnter:N,onMouseLeave:S}=e,$=y(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:k,direction:P,className:O,style:C}=(0,c.TP)("statistic"),E=k("statistic",t),[M,R,z]=h(E),D=s.createElement(u,{decimalSeparator:A,groupSeparator:w,prefixCls:E,formatter:b,precision:j,value:m}),L=o()(E,{[`${E}-rtl`]:"rtl"===P},O,r,a,R,z),I=(0,i.A)($,{aria:!0,data:!0});return M(s.createElement("div",Object.assign({},I,{className:L,style:Object.assign(Object.assign({},C),n),onMouseEnter:N,onMouseLeave:S}),x&&s.createElement("div",{className:`${E}-title`},x),s.createElement(d.A,{paragraph:!1,loading:v,className:`${E}-skeleton`},s.createElement("div",{style:l,className:`${E}-content`},f&&s.createElement("span",{className:`${E}-content-prefix`},f),p?p(D):D,g&&s.createElement("span",{className:`${E}-content-suffix`},g)))))},v=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var b=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let j=1e3/30,A=s.memo(e=>{let{value:t,format:r="HH:mm:ss",onChange:l,onFinish:o}=e,i=b(e,["value","format","onChange","onFinish"]),c=(0,a.A)(),d=s.useRef(null),u=()=>{null==o||o(),d.current&&(clearInterval(d.current),d.current=null)},m=()=>{let e=new Date(t).getTime();e>=Date.now()&&(d.current=setInterval(()=>{c(),null==l||l(e-Date.now()),e<Date.now()&&u()},j))};return s.useEffect(()=>(m(),()=>{d.current&&(clearInterval(d.current),d.current=null)}),[t]),s.createElement(g,Object.assign({},i,{value:t,valueRender:e=>(0,n.Ob)(e,{title:void 0}),formatter:(e,t)=>(function(e,t){let{format:r=""}=t;return function(e,t){let r=e,s=/\[[^\]]*]/g,a=(t.match(s)||[]).map(e=>e.slice(1,-1)),n=t.replace(s,"[]"),l=v.reduce((e,t)=>{let[s,a]=t;if(e.includes(s)){let t=Math.floor(r/a);return r-=t*a,e.replace(RegExp(`${s}+`,"g"),e=>{let r=e.length;return t.toString().padStart(r,"0")})}return e},n),o=0;return l.replace(s,()=>{let e=a[o];return o+=1,e})}(Math.max(new Date(e).getTime()-Date.now(),0),r)})(e,Object.assign(Object.assign({},t),{format:r}))}))});g.Countdown=A;let w=g},39601:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var s=r(45512),a=r(58009),n=r(7325),l=r(53950),o=r(6987),i=r(3117),c=r(1236),d=r(9170),u=r(1387),m=r(21419),x=r(431),p=r(91054),f=r(27069),h=r(45211),y=r(88752),g=r(60380),v=r(60636),b=r(765),j=r(2274),A=r(43087),w=r(16589),N=r.n(w),S=r(41561),$=r(5607),k=r(85187);let{Option:P}=n.A,O=()=>{let{user:e}=(0,v.A)(),t=(0,b.a)(),[r,w]=(0,a.useState)("stock-levels"),[O,C]=(0,a.useState)("all"),[E,M]=(0,a.useState)(!0),{data:R,isLoading:z,refetch:D}=(0,j.r3)({page:1,limit:1e3,search:""}),{data:L,isLoading:I}=(0,A.lg)({page:1,limit:100,search:""}),_=z||I,{inventoryStats:T,inventoryData:F}=(0,a.useMemo)(()=>{let e=R?.data?.products||[],t=(L?.data?.categories||[]).reduce((e,t)=>(e[t.id]=t.name,e),{}),r="all"===O?e:e.filter(e=>{let r=t[e.categoryId];return r?.toLowerCase()===O.toLowerCase()}),s=r.length,a=r.filter(e=>e.stockQuantity>0&&e.minStockLevel&&e.stockQuantity<=e.minStockLevel).length;return{inventoryStats:{totalProducts:s,lowStockItems:a,outOfStockItems:r.filter(e=>0===e.stockQuantity).length,totalValue:r.reduce((e,t)=>e+t.stockQuantity*parseFloat(t.price),0)},inventoryData:r.map(e=>{let r=e.minStockLevel||5,s="In Stock";return 0===e.stockQuantity?s="Out of Stock":e.stockQuantity<=r&&(s="Low Stock"),{key:e.id.toString(),id:e.id,name:e.name,category:t[e.categoryId]||"Unknown",currentStock:e.stockQuantity,minStock:r,status:s,value:e.stockQuantity*parseFloat(e.price),price:parseFloat(e.price),sku:e.sku||"N/A"}})}},[R,L,O]),[Y,U]=(0,a.useState)(1),[V,B]=(0,a.useState)(10),G=F;"low-stock"===r?G=F.filter(e=>"Low Stock"===e.status):"out-of-stock"===r?G=F.filter(e=>"Out of Stock"===e.status):"stock-levels"===r?G=F:"movement"===r&&(G=F);let H=G.slice((Y-1)*V,Y*V);return(0,s.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,s.jsx)(o.A,{title:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"text-blue-600"}),(0,s.jsx)("span",{className:"text-gray-800",children:"Inventory Reports"})]}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,s.jsxs)("div",{className:t?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,s.jsx)(i.Ay,{type:"primary",icon:(0,s.jsx)(p.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){l.Ay.error("Please allow popups to print inventory report");return}let t=`
      <html>
        <head>
          <title>Inventory Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print { .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Inventory Report</h2>
            <p class="date">Generated on: ${N()().format("MMMM D, YYYY h:mm A")}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>SKU</th>
                <th>Product Name</th>
                <th>Category</th>
                ${"movement"===r||"valuation"===r?"<th>Month</th>":""}
                <th>Current Stock</th>
                <th>Min Stock</th>
                <th>Unit Price</th>
                <th>Status</th>
                <th>Total Value</th>
              </tr>
            </thead>
            <tbody>
              ${G.map(e=>`
                <tr>
                  <td>${e.sku}</td>
                  <td>${e.name}</td>
                  <td>${e.category}</td>
                  ${"movement"===r||"valuation"===r?`<td>${N()().format("MMMM YYYY")}</td>`:""}
                  <td>${e.currentStock}</td>
                  <td>${e.minStock}</td>
                  <td>₵${e.price.toFixed(2)}</td>
                  <td>${e.status}</td>
                  <td>₵${e.value.toFixed(2)}</td>
                </tr>
              `).join("")}
              <tr>
                <td colspan="${"movement"===r||"valuation"===r?"8":"7"}" style="font-weight:bold; text-align:right;">Total Value</td>
                <td style="font-weight:bold;">₵${G.reduce((e,t)=>e+t.value,0).toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;e.document.write(t),e.document.close()},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Print"}),(0,s.jsx)(i.Ay,{type:"primary",icon:(0,s.jsx)(f.A,{}),onClick:()=>{let e=new S.uE;e.setFontSize(16),e.text("Inventory Report",14,15),e.setFontSize(10),e.text(`Generated on: ${N()().format("MMMM D, YYYY h:mm A")}`,14,22);let t=G.map(e=>[e.sku,e.name,e.category,..."movement"===r||"valuation"===r?[N()().format("MMMM YYYY")]:[],e.currentStock,e.minStock,`₵${e.price.toFixed(2)}`,e.status,`₵${e.value.toFixed(2)}`]);t.push([{content:"Total Value",colSpan:9,styles:{fontStyle:"bold",halign:"right"}},{content:`₵${G.reduce((e,t)=>e+t.value,0).toFixed(2)}`,styles:{fontStyle:"bold"}}]),(0,$.Ay)(e,{head:[["SKU","Product Name","Category",..."movement"===r||"valuation"===r?["Month"]:[],"Current Stock","Min Stock","Unit Price","Status","Total Value"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("inventory-report.pdf")},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Export"})]}),children:(0,s.jsxs)("div",{className:"w-full space-y-6",children:[(0,s.jsx)(o.A,{title:"Report Filters",size:"small",className:"bg-gray-50",children:(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(d.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Report Type"}),(0,s.jsxs)(n.A,{value:r,onChange:w,className:"w-full",children:[(0,s.jsx)(P,{value:"stock-levels",children:"Stock Levels"}),(0,s.jsx)(P,{value:"low-stock",children:"Low Stock Alert"}),(0,s.jsx)(P,{value:"out-of-stock",children:"Out of Stock"}),(0,s.jsx)(P,{value:"valuation",children:"Inventory Valuation"}),(0,s.jsx)(P,{value:"movement",children:"Stock Movement"})]})]})}),(0,s.jsx)(d.A,{xs:24,sm:12,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Category"}),(0,s.jsxs)(n.A,{value:O,onChange:C,className:"w-full",children:[(0,s.jsx)(P,{value:"all",children:"All Categories"}),L?.data?.categories?.map(e=>s.jsx(P,{value:e.name.toLowerCase(),children:e.name},e.id))]})]})}),(0,s.jsx)(d.A,{xs:24,sm:24,md:8,children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xa0"}),(0,s.jsx)(i.Ay,{type:"primary",onClick:()=>{M(!0),D(),l.Ay.success("Inventory report generated successfully!")},loading:_,className:"w-full bg-blue-600 hover:bg-blue-700",icon:(0,s.jsx)(x.A,{}),children:"Generate Report"})]})})]})}),(0,s.jsxs)(c.A,{gutter:[16,16],children:[(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(u.A,{title:"Total Products",value:T.totalProducts,valueStyle:{color:"#1890ff",fontSize:"18px"},className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(u.A,{title:"Low Stock Items",value:T.lowStockItems,valueStyle:{color:"#fa8c16",fontSize:"18px"},suffix:(0,s.jsx)(h.A,{}),className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(u.A,{title:"Out of Stock",value:T.outOfStockItems,valueStyle:{color:"#f5222d",fontSize:"18px"},suffix:(0,s.jsx)(h.A,{}),className:"w-full"})})}),(0,s.jsx)(d.A,{xs:12,sm:12,md:6,children:(0,s.jsx)(o.A,{className:"text-center h-24 flex items-center justify-center",children:(0,s.jsx)(u.A,{title:"Total Value",value:T.totalValue,precision:2,prefix:"₵",valueStyle:{color:"#3f8600",fontSize:"18px"},className:"w-full"})})})]}),(0,s.jsx)(o.A,{title:"Inventory Details",children:_?(0,s.jsx)("div",{className:"flex h-60 items-center justify-center",children:(0,s.jsx)(m.A,{indicator:(0,s.jsx)(y.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):E?"movement"===r?(0,s.jsxs)("div",{className:"text-center text-blue-700 py-10",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Stock Movement Tracking Coming Soon"}),(0,s.jsx)("p",{className:"text-sm",children:"Stock movement logs, adjustments, and history will be available in future updates."})]}):H.length>0?(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full min-w-[800px]",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"SKU"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Product Name"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Category"}),"movement"===r||"valuation"===r?(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Month"}):null,(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Current Stock"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Min Stock"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Unit Price"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Status"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Total Value"})]})}),(0,s.jsx)("tbody",{children:H.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.sku}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.name}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.category}),"movement"===r||"valuation"===r?(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:N()().format("MMMM YYYY")}):null,(0,s.jsx)("td",{className:`px-4 py-3 text-sm font-semibold ${0===e.currentStock?"text-red-600":e.currentStock<=e.minStock?"text-orange-600":"text-green-600"}`,children:e.currentStock}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.minStock}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:["₵",e.price.toFixed(2)]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm",children:(0,s.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"Out of Stock"===e.status?"bg-red-100 text-red-800":"Low Stock"===e.status?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"}`,children:["Out of Stock"===e.status&&(0,s.jsx)(h.A,{className:"mr-1"}),"Low Stock"===e.status&&(0,s.jsx)(h.A,{className:"mr-1"}),"In Stock"===e.status&&(0,s.jsx)(g.A,{className:"mr-1"}),e.status]})}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.value.toFixed(2)]})]},e.key))})]})}),(0,s.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t text-sm text-gray-600",children:["Showing ",H.length," products"]})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("p",{children:"out-of-stock"===r?"No out of stock products found.":"No products found for the selected criteria."}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"all"!==O?"Try selecting 'All Categories' or check if products exist in this category.":"No products have been added to the system yet."})]}):(0,s.jsxs)("div",{className:"text-center text-gray-500 py-20",children:[(0,s.jsx)(x.A,{className:"text-4xl mb-4"}),(0,s.jsx)("p",{children:'Click "Generate Report" to view inventory details'}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Product data will be displayed here"})]})}),(0,s.jsx)(k.A,{current:Y,pageSize:V,total:G.length,onChange:e=>U(e),isMobile:t}),(0,s.jsx)(o.A,{className:"bg-blue-50 border-blue-200",children:(0,s.jsxs)("div",{className:"text-center text-blue-700",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCE6 Advanced Inventory Analytics Coming Soon"}),(0,s.jsx)("p",{className:"text-sm",children:"Stock movement tracking, reorder suggestions, and ABC analysis will be available in future updates."})]})})]})})})}},85187:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(45512);r(58009);var a=r(59022),n=r(60165);let l=({current:e,pageSize:t,total:r,onChange:l,isMobile:o=!1})=>{let i=Math.ceil(r/t);return 0===r?null:(0,s.jsxs)("div",{className:"bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,s.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:(e-1)*t+1})," to"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:Math.min(e*t,r)})," of"," ",(0,s.jsx)("span",{className:"font-medium text-gray-900",children:r})," results"]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,s.jsxs)("button",{onClick:()=>l(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Previous"}),(0,s.jsx)(a.A,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:Math.min(5,i)},(t,r)=>{let a=r+1;return(0,s.jsx)("button",{onClick:()=>l(a),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:a},a)}),(0,s.jsxs)("button",{onClick:()=>l(e+1),disabled:e>=i,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${e>=i?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)("span",{className:"sr-only",children:"Next"}),(0,s.jsx)(n.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full sm:hidden",children:[(0,s.jsx)("button",{onClick:()=>l(Math.max(1,e-1)),disabled:1===e,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===e?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Previous"}),(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",e," of ",i]}),(0,s.jsx)("button",{onClick:()=>l(e+1),disabled:e>=i,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${e>=i?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white hover:bg-gray-50"}`,children:"Next"})]})]})}},60636:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(92273),a=r(25510),n=r(97245),l=r(42211);let o=()=>{let e=(0,s.wA)(),{user:t,accessToken:r}=(0,s.d4)(e=>e.auth),o=(0,a._)(),{refetch:i}=(0,n.$f)(t?.id||0,{skip:!t?.id});console.log("useAuth - Auth State:",{isAuthenticated:!!t&&!!r,role:t?.role,phone:t?.phone,phoneType:t?.phone?typeof t.phone:"undefined/null",createdAt:t?.createdAt,createdAtType:t?.createdAt?typeof t.createdAt:"undefined/null"}),console.log("useAuth - Complete user object:",JSON.stringify(t,null,2));let c=!!t&&!!r,d=async()=>{if(!t?.id){console.error("Cannot refresh user data: No user ID available");return}try{console.log("useAuth - Refreshing user data for ID:",t.id);let s=await i();console.log("useAuth - Refetch result:",s);let a=s.data;if(a?.success&&a?.data){console.log("useAuth - API response data:",a.data);let s=t.paymentStatus;t.lastPaymentDate,t.nextPaymentDue;let n=a.data.phone||t.phone||"",o=a.data.createdAt||t.createdAt||"",i=a.data.lastPaymentDate||t.lastPaymentDate||void 0,c=a.data.nextPaymentDue||t.nextPaymentDue||null,d=a.data.createdBy||t.createdBy||void 0;console.log("useAuth - User field values:",{apiPhone:a.data.phone,userPhone:t.phone,finalPhone:n,apiCreatedAt:a.data.createdAt,userCreatedAt:t.createdAt,finalCreatedAt:o,apiLastPaymentDate:a.data.lastPaymentDate,userLastPaymentDate:t.lastPaymentDate,finalLastPaymentDate:i,apiNextPaymentDue:a.data.nextPaymentDue,userNextPaymentDue:t.nextPaymentDue,finalNextPaymentDue:c,apiCreatedBy:a.data.createdBy,userCreatedBy:t.createdBy,finalCreatedBy:d});let u={...a.data,phone:n,createdAt:o,lastPaymentDate:i,nextPaymentDue:c,createdBy:d,paymentStatus:s};console.log("useAuth - Updating Redux store with:",u),console.log("useAuth - Using current access token:",r?"Token exists (not showing for security)":"No token found"),window.__PROFILE_UPDATE_IN_PROGRESS=!0,window.__LAST_PROFILE_UPDATE_PATH=window.location.pathname,e((0,l.gV)({user:u,accessToken:r||""})),setTimeout(()=>{window.__PROFILE_UPDATE_IN_PROGRESS=!1,console.log("useAuth - Profile update flag cleared")},500),console.log("User data refreshed successfully (payment status preserved)")}else console.error("Failed to refresh user data:",a?.message||"Unknown error")}catch(e){console.error("Error refreshing user data:",e)}};return{user:t,accessToken:r,isAuthenticated:c,hasRole:e=>!!t&&(Array.isArray(e)?e.includes(t.role):t.role===e),isSuperAdmin:()=>t?.role==="superadmin",isAdmin:()=>t?.role==="admin",isCashier:()=>t?.role==="cashier",needsPayment:()=>!!t&&"superadmin"!==t.role&&o.needsPayment,paymentStatus:o,refreshUser:d}}},22605:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\reports\\\\inventory\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\inventory\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7325,3950,9486,5482,106,4286],()=>r(5948));module.exports=s})();