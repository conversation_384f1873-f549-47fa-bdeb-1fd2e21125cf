# Enhanced School Management System Database Schema
## Comprehensive Database Design with Payslip, Transportation & Library Management

---

## 1. CORE SYSTEM TABLES

### 1.1 User Management Tables

#### users
```sql
- id (Primary Key)
- username (Unique)
- email (Unique)
- password_hash
- first_name
- last_name
- middle_name
- phone
- alternative_phone
- role (super_admin, admin, teacher, staff, hr_manager, librarian, transport_coordinator, accountant)
- employee_id (Unique for staff)
- department
- designation
- date_of_joining
- date_of_birth
- gender
- address
- city
- state
- country
- postal_code
- emergency_contact_name
- emergency_contact_phone
- profile_picture
- status (active, inactive, suspended, terminated)
- last_login
- password_reset_token
- password_reset_expires
- email_verified
- phone_verified
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### user_permissions
```sql
- id (Primary Key)
- user_id (Foreign Key to users)
- module_name
- permission_type (read, write, delete, admin)
- granted_by (Foreign Key to users)
- granted_at
- expires_at
- is_active
```

#### user_sessions
```sql
- id (Primary Key)
- user_id (Foreign Key to users)
- session_token
- ip_address
- user_agent
- device_type
- location
- login_time
- logout_time
- is_active
```

### 1.2 School Structure Tables

#### schools
```sql
- id (Primary Key)
- name
- code (Unique)
- address
- city
- state
- country
- postal_code
- phone
- email
- website
- logo
- registration_number
- tax_id
- established_date
- principal_id (Foreign Key to users)
- admin_id (Foreign Key to users)
- academic_session_start_month
- academic_session_end_month
- timezone
- currency
- language
- status (active, inactive, suspended)
- subscription_plan
- subscription_expiry
- created_at
- updated_at
```

#### campuses
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- campus_name
- campus_code
- address
- city
- state
- country
- phone
- email
- campus_head_id (Foreign Key to users)
- total_area
- built_up_area
- facilities (JSON)
- is_main_campus
- status (active, inactive)
- created_at
- updated_at
```

#### academic_years
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- year_name (e.g., "2023/2024")
- start_date
- end_date
- is_current
- total_working_days
- holidays (JSON array)
- terms (JSON array with term details)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### grades
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- grade_name (Creche, Nursery 1, Nursery 2, Primary 1-6, JSS 1-3)
- grade_code
- grade_level (1-12)
- description
- minimum_age
- maximum_age
- capacity
- subjects (JSON array)
- is_active
- created_at
- updated_at
```

#### classes
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- campus_id (Foreign Key to campuses)
- grade_id (Foreign Key to grades)
- academic_year_id (Foreign Key to academic_years)
- class_name (e.g., "Primary 1A")
- section
- capacity
- current_strength
- class_teacher_id (Foreign Key to users)
- assistant_teacher_id (Foreign Key to users)
- room_number
- building
- floor
- facilities (JSON)
- timetable (JSON)
- is_active
- created_at
- updated_at
```

#### subjects
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- subject_name
- subject_code
- description
- grade_levels (JSON array)
- is_mandatory
- credit_hours
- pass_marks
- full_marks
- subject_type (core, elective, extra_curricular)
- department
- syllabus_file
- is_active
- created_by (Foreign Key to users)
- created_at
- updated_at
```

### 1.3 Student Management Tables

#### students
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- campus_id (Foreign Key to campuses)
- student_id (Unique per school)
- admission_number (Unique)
- roll_number
- first_name
- last_name
- middle_name
- date_of_birth
- gender
- blood_group
- nationality
- religion
- caste
- category
- mother_tongue
- address
- city
- state
- country
- postal_code
- phone
- email
- photo
- birth_certificate
- previous_school
- transfer_certificate
- admission_date
- academic_year_admitted
- current_class_id (Foreign Key to classes)
- status (active, graduated, transferred, withdrawn, suspended)
- medical_conditions (JSON)
- allergies (JSON)
- special_needs (JSON)
- transportation_required
- library_card_number
- emergency_contact_name
- emergency_contact_phone
- emergency_contact_relationship
- pickup_persons (JSON array)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### student_guardians
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- guardian_type (father, mother, guardian, relative)
- title (Mr, Mrs, Dr, etc.)
- first_name
- last_name
- phone
- alternative_phone
- email
- occupation
- designation
- organization
- workplace_address
- annual_income
- education_qualification
- address
- city
- state
- country
- postal_code
- is_primary_contact
- is_emergency_contact
- is_fee_payer
- relationship
- photo
- id_proof_type
- id_proof_number
- created_at
- updated_at
```

#### student_enrollments
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- class_id (Foreign Key to classes)
- academic_year_id (Foreign Key to academic_years)
- enrollment_date
- promotion_date
- status (active, promoted, repeated, transferred, withdrawn)
- roll_number
- section
- subjects_enrolled (JSON array)
- fee_structure_id (Foreign Key to fee_structures)
- scholarship_applied
- scholarship_amount
- remarks
- created_by (Foreign Key to users)
- created_at
- updated_at
```

---

## 2. PAYROLL MANAGEMENT TABLES

### 2.1 Employee Salary Management

#### employee_salary_structures
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- employee_id (Foreign Key to users)
- salary_grade
- basic_salary
- house_rent_allowance
- transport_allowance
- medical_allowance
- dearness_allowance
- special_allowance
- other_allowances (JSON)
- total_gross_salary
- provident_fund_percentage
- professional_tax
- income_tax_percentage
- insurance_premium
- other_deductions (JSON)
- effective_from
- effective_to
- is_active
- approved_by (Foreign Key to users)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### payroll_periods
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- period_name (e.g., "January 2024")
- period_type (monthly, quarterly, annual)
- start_date
- end_date
- working_days
- status (draft, processing, approved, paid, closed)
- total_employees
- total_gross_amount
- total_deductions
- total_net_amount
- processed_by (Foreign Key to users)
- processed_at
- approved_by (Foreign Key to users)
- approved_at
- payment_date
- bank_transfer_reference
- created_at
- updated_at
```

#### payroll_items
```sql
- id (Primary Key)
- payroll_period_id (Foreign Key to payroll_periods)
- employee_id (Foreign Key to users)
- salary_structure_id (Foreign Key to employee_salary_structures)
- basic_salary
- house_rent_allowance
- transport_allowance
- medical_allowance
- other_allowances
- overtime_amount
- bonus_amount
- arrears_amount
- gross_salary
- provident_fund_deduction
- professional_tax
- income_tax_deduction
- insurance_deduction
- loan_deduction
- advance_deduction
- other_deductions
- total_deductions
- net_salary
- days_worked
- days_absent
- days_on_leave
- overtime_hours
- late_days
- early_departure_days
- status (draft, approved, paid)
- remarks
- created_at
- updated_at
```

#### payslips
```sql
- id (Primary Key)
- payroll_item_id (Foreign Key to payroll_items)
- employee_id (Foreign Key to users)
- payslip_number (Unique)
- pay_period_start
- pay_period_end
- generated_date
- pdf_file_path
- email_sent
- email_sent_date
- sms_sent
- sms_sent_date
- download_count
- last_downloaded_at
- last_downloaded_by
- is_active
- created_by (Foreign Key to users)
- created_at
```

#### employee_deductions
```sql
- id (Primary Key)
- employee_id (Foreign Key to users)
- deduction_type (tax, insurance, loan, advance, fine, other)
- deduction_name
- amount
- percentage
- is_recurring
- frequency (monthly, quarterly, annual, one_time)
- start_date
- end_date
- total_installments
- remaining_installments
- description
- reference_number
- is_active
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### employee_bonuses
```sql
- id (Primary Key)
- employee_id (Foreign Key to users)
- bonus_type (performance, festival, annual, project, attendance, other)
- bonus_name
- amount
- percentage_of_salary
- description
- criteria_met (JSON)
- awarded_date
- pay_period_id (Foreign Key to payroll_periods)
- awarded_by (Foreign Key to users)
- approved_by (Foreign Key to users)
- status (pending, approved, paid, rejected)
- remarks
- created_at
- updated_at
```

#### employee_attendance
```sql
- id (Primary Key)
- employee_id (Foreign Key to users)
- date
- check_in_time
- check_out_time
- break_start_time
- break_end_time
- total_hours
- regular_hours
- overtime_hours
- status (present, absent, half_day, late, early_leave, holiday, weekend)
- late_minutes
- early_departure_minutes
- location_check_in
- location_check_out
- device_used
- ip_address
- remarks
- approved_by (Foreign Key to users)
- created_at
- updated_at
```

#### employee_leaves
```sql
- id (Primary Key)
- employee_id (Foreign Key to users)
- leave_type (casual, sick, annual, maternity, paternity, emergency, unpaid)
- start_date
- end_date
- total_days
- reason
- application_date
- status (pending, approved, rejected, cancelled)
- approved_by (Foreign Key to users)
- approved_date
- rejection_reason
- supporting_documents (JSON)
- is_half_day
- contact_during_leave
- handover_to (Foreign Key to users)
- created_at
- updated_at
```

---

## 3. TRANSPORTATION MANAGEMENT TABLES

### 3.1 Route and Vehicle Management

#### transport_routes
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- route_name
- route_code (Unique per school)
- start_location
- end_location
- total_distance_km
- estimated_time_minutes
- pickup_points (JSON array with coordinates and timings)
- drop_points (JSON array with coordinates and timings)
- route_map_coordinates (JSON)
- monthly_fee
- fuel_cost_per_km
- driver_allowance
- conductor_allowance
- maintenance_cost_per_km
- total_operating_cost
- max_students
- current_students
- is_active
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### vehicles
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- vehicle_number (Unique)
- vehicle_type (bus, van, car, mini_bus)
- make
- model
- year_manufactured
- engine_number
- chassis_number
- seating_capacity
- fuel_type (petrol, diesel, cng, electric)
- fuel_tank_capacity
- mileage_per_liter
- purchase_date
- purchase_price
- insurance_company
- insurance_policy_number
- insurance_expiry_date
- registration_expiry_date
- fitness_certificate_number
- fitness_certificate_expiry
- pollution_certificate_expiry
- gps_device_id
- gps_device_imei
- vehicle_color
- vehicle_photos (JSON array)
- current_mileage
- last_service_date
- next_service_due_date
- status (active, maintenance, repair, retired, accident)
- assigned_route_id (Foreign Key to transport_routes)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### drivers
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- employee_id (Foreign Key to users, nullable)
- driver_name
- phone
- alternative_phone
- email
- address
- city
- state
- country
- date_of_birth
- license_number (Unique)
- license_type
- license_issue_date
- license_expiry_date
- experience_years
- previous_employer
- emergency_contact_name
- emergency_contact_phone
- background_check_status
- background_check_date
- medical_certificate_number
- medical_certificate_expiry
- training_certificates (JSON array)
- salary_type (monthly, daily, per_trip)
- salary_amount
- photo
- documents (JSON array)
- status (active, inactive, suspended, terminated)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### route_assignments
```sql
- id (Primary Key)
- route_id (Foreign Key to transport_routes)
- vehicle_id (Foreign Key to vehicles)
- driver_id (Foreign Key to drivers)
- conductor_id (Foreign Key to users, nullable)
- academic_year_id (Foreign Key to academic_years)
- shift_type (morning, afternoon, both)
- start_date
- end_date
- monday_active
- tuesday_active
- wednesday_active
- thursday_active
- friday_active
- saturday_active
- sunday_active
- morning_start_time
- morning_end_time
- afternoon_start_time
- afternoon_end_time
- is_active
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### student_transport
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- route_id (Foreign Key to transport_routes)
- pickup_point_name
- pickup_point_coordinates
- drop_point_name
- drop_point_coordinates
- pickup_time
- drop_time
- transport_fee_monthly
- academic_year_id (Foreign Key to academic_years)
- start_date
- end_date
- status (active, inactive, suspended, graduated)
- parent_phone_pickup
- parent_phone_drop
- special_instructions
- emergency_contact
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### transport_attendance
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- route_assignment_id (Foreign Key to route_assignments)
- date
- shift_type (morning, afternoon)
- pickup_status (picked, missed, absent, holiday)
- pickup_time
- pickup_location_coordinates
- drop_status (dropped, missed, absent, holiday)
- drop_time
- drop_location_coordinates
- driver_remarks
- parent_notified
- notification_time
- recorded_by (Foreign Key to users)
- device_used
- created_at
```

#### vehicle_maintenance
```sql
- id (Primary Key)
- vehicle_id (Foreign Key to vehicles)
- maintenance_type (routine_service, repair, inspection, cleaning, fuel)
- description
- cost
- maintenance_date
- next_maintenance_date
- mileage_at_maintenance
- vendor_name
- vendor_contact
- vendor_address
- invoice_number
- warranty_period
- parts_replaced (JSON array)
- status (scheduled, in_progress, completed, cancelled)
- priority (low, medium, high, urgent)
- estimated_completion_date
- actual_completion_date
- quality_rating
- created_by (Foreign Key to users)
- approved_by (Foreign Key to users)
- created_at
- updated_at
```

#### transport_expenses
```sql
- id (Primary Key)
- vehicle_id (Foreign Key to vehicles)
- route_id (Foreign Key to transport_routes, nullable)
- expense_type (fuel, maintenance, insurance, tax, permit, salary, other)
- amount
- expense_date
- description
- vendor_name
- invoice_number
- payment_method (cash, cheque, bank_transfer, card)
- payment_reference
- mileage_at_expense
- fuel_quantity (for fuel expenses)
- fuel_rate_per_liter
- approved_by (Foreign Key to users)
- created_by (Foreign Key to users)
- created_at
```

---

## 4. LIBRARY MANAGEMENT TABLES

### 4.1 Book and Resource Management

#### library_books
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- isbn (Unique)
- isbn13
- title
- subtitle
- author
- co_authors (JSON array)
- publisher
- publication_year
- edition
- language
- pages
- price
- currency
- category_id (Foreign Key to library_categories)
- subcategory_id (Foreign Key to library_categories)
- subject_tags (JSON array)
- grade_levels (JSON array)
- description
- summary
- table_of_contents
- location_shelf
- location_row
- location_section
- barcode (Unique)
- qr_code (Unique)
- book_cover_image
- total_copies
- available_copies
- issued_copies
- reserved_copies
- damaged_copies
- lost_copies
- status (available, out_of_stock, discontinued)
- condition (new, good, fair, poor, damaged)
- acquisition_date
- acquisition_type (purchase, donation, exchange)
- acquisition_cost
- vendor_name
- added_by (Foreign Key to users)
- last_updated_by (Foreign Key to users)
- created_at
- updated_at
```

#### library_categories
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- category_name
- category_code
- description
- parent_category_id (Foreign Key to library_categories, nullable)
- dewey_decimal_number
- color_code
- icon
- is_fiction
- target_age_group
- is_active
- sort_order
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### library_members
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- member_type (student, teacher, staff, parent, guest)
- member_id (Foreign Key to students/users)
- library_card_number (Unique)
- barcode
- qr_code
- membership_date
- expiry_date
- renewal_date
- max_books_allowed
- max_days_allowed
- current_books_count
- total_books_issued
- total_fines_paid
- current_fine_amount
- membership_fee_paid
- status (active, suspended, expired, blocked)
- suspension_reason
- suspension_date
- notes
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### book_transactions
```sql
- id (Primary Key)
- transaction_number (Unique)
- book_id (Foreign Key to library_books)
- member_id (Foreign Key to library_members)
- transaction_type (issue, return, renew, reserve, cancel_reservation)
- issue_date
- due_date
- return_date
- renewal_count
- max_renewals_allowed
- fine_amount
- fine_paid_amount
- fine_waived_amount
- condition_on_issue (new, good, fair, poor)
- condition_on_return (new, good, fair, poor, damaged, lost)
- damage_description
- replacement_cost
- issued_by (Foreign Key to users)
- returned_to (Foreign Key to users)
- approved_by (Foreign Key to users)
- remarks
- is_overdue
- overdue_days
- reminder_sent_count
- last_reminder_date
- created_at
- updated_at
```

#### library_reservations
```sql
- id (Primary Key)
- book_id (Foreign Key to library_books)
- member_id (Foreign Key to library_members)
- reservation_date
- expected_availability_date
- actual_availability_date
- notification_sent
- notification_date
- pickup_deadline
- priority_number
- status (active, fulfilled, cancelled, expired)
- cancellation_reason
- reserved_by (Foreign Key to users)
- fulfilled_by (Foreign Key to users)
- created_at
- updated_at
```

#### library_fines
```sql
- id (Primary Key)
- member_id (Foreign Key to library_members)
- transaction_id (Foreign Key to book_transactions)
- fine_type (overdue, damage, lost_book, late_return, other)
- amount
- description
- fine_date
- due_date
- payment_date
- payment_amount
- payment_method (cash, card, online, cheque)
- payment_reference
- waived_amount
- waived_reason
- status (pending, partial_paid, paid, waived, cancelled)
- waived_by (Foreign Key to users)
- collected_by (Foreign Key to users)
- receipt_number
- created_at
- updated_at
```

#### digital_resources
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- resource_type (ebook, audiobook, video, journal, database, software)
- title
- author
- publisher
- isbn
- url
- file_path
- file_size_mb
- format (pdf, epub, mp3, mp4, html, other)
- access_type (free, subscription, purchased, licensed)
- license_type (single_user, multi_user, unlimited)
- license_start_date
- license_expiry_date
- max_concurrent_users
- current_active_users
- total_downloads
- category_id (Foreign Key to library_categories)
- subject_tags (JSON array)
- grade_levels (JSON array)
- description
- thumbnail_image
- preview_available
- download_allowed
- print_allowed
- copy_allowed
- is_active
- added_by (Foreign Key to users)
- created_at
- updated_at
```

#### library_settings
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- max_issue_days_student
- max_issue_days_teacher
- max_issue_days_staff
- max_renewal_times
- fine_per_day
- max_fine_amount
- grace_period_days
- max_books_per_student
- max_books_per_teacher
- max_books_per_staff
- library_hours_weekday_start
- library_hours_weekday_end
- library_hours_weekend_start
- library_hours_weekend_end
- holiday_schedule (JSON)
- auto_renewal_enabled
- email_reminders_enabled
- sms_reminders_enabled
- reminder_days_before_due
- overdue_reminder_frequency
- membership_validity_months
- membership_fee
- damage_fine_percentage
- lost_book_replacement_cost_multiplier
- updated_by (Foreign Key to users)
- updated_at
```

#### library_reports
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- report_type (circulation, inventory, fines, popular_books, member_activity)
- report_period_start
- report_period_end
- total_books_issued
- total_books_returned
- total_overdue_books
- total_fines_collected
- total_new_members
- most_popular_books (JSON)
- most_active_members (JSON)
- category_wise_circulation (JSON)
- generated_by (Foreign Key to users)
- generated_at
- file_path
```

---

## 5. ADDITIONAL MANAGEMENT TABLES

### 5.1 Academic and Assessment Tables

#### student_attendance
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- class_id (Foreign Key to classes)
- date
- status (present, absent, late, excused, sick, holiday)
- time_in
- time_out
- late_minutes
- remarks
- recorded_by (Foreign Key to users)
- parent_notified
- notification_time
- medical_certificate
- created_at
- updated_at
```

#### assessments
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- class_id (Foreign Key to classes)
- subject_id (Foreign Key to subjects)
- academic_year_id (Foreign Key to academic_years)
- assessment_name
- assessment_type (exam, test, assignment, project, quiz, practical)
- total_marks
- pass_marks
- weight_percentage
- date_conducted
- duration_minutes
- instructions
- syllabus_covered (JSON)
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### student_grades
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- subject_id (Foreign Key to subjects)
- assessment_id (Foreign Key to assessments)
- marks_obtained
- total_marks
- percentage
- grade_letter
- grade_points
- remarks
- is_absent
- teacher_id (Foreign Key to users)
- recorded_at
- updated_at
```

### 5.2 Fee Management Tables

#### fee_structures
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- academic_year_id (Foreign Key to academic_years)
- grade_id (Foreign Key to grades)
- fee_type (tuition, admission, transport, meal, uniform, books, exam, activity, other)
- fee_name
- amount
- due_date
- late_fee_amount
- late_fee_after_days
- is_mandatory
- is_refundable
- installments_allowed
- installment_count
- description
- created_by (Foreign Key to users)
- created_at
- updated_at
```

#### student_fees
```sql
- id (Primary Key)
- student_id (Foreign Key to students)
- fee_structure_id (Foreign Key to fee_structures)
- academic_year_id (Foreign Key to academic_years)
- amount_due
- amount_paid
- balance
- discount_amount
- discount_reason
- scholarship_amount
- due_date
- status (pending, partial, paid, overdue, waived)
- waived_by (Foreign Key to users)
- waived_reason
- created_at
- updated_at
```

#### fee_payments
```sql
- id (Primary Key)
- student_fee_id (Foreign Key to student_fees)
- payment_date
- amount_paid
- payment_method (cash, bank_transfer, cheque, card, online, upi)
- reference_number
- transaction_id
- bank_name
- cheque_number
- cheque_date
- received_by (Foreign Key to users)
- receipt_number
- remarks
- status (pending, cleared, bounced, cancelled)
- created_at
- updated_at
```

---

## 6. SYSTEM CONFIGURATION TABLES

#### system_settings
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- module_name
- setting_key
- setting_value
- setting_type (string, number, boolean, json, date, time)
- description
- is_editable
- updated_by (Foreign Key to users)
- updated_at
```

#### audit_logs
```sql
- id (Primary Key)
- user_id (Foreign Key to users)
- action
- module_name
- table_name
- record_id
- old_values (JSON)
- new_values (JSON)
- ip_address
- user_agent
- device_type
- location
- created_at
```

#### notifications
```sql
- id (Primary Key)
- school_id (Foreign Key to schools)
- recipient_type (user, student, parent, all, role_based)
- recipient_id
- title
- message
- notification_type (sms, email, push, in_app)
- priority (low, medium, high, urgent)
- status (pending, sent, delivered, failed, read)
- sent_at
- delivered_at
- read_at
- retry_count
- error_message
- template_id
- variables (JSON)
- created_by (Foreign Key to users)
- created_at
```

---

*This comprehensive database schema supports all enhanced features including payroll management, transportation coordination, library management, and robust administrative capabilities for modern school management systems.*