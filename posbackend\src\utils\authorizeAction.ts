import { and, eq, or, inArray } from "drizzle-orm";
import { JwtPayload } from "../types/type";
import { postgresDb } from "../db/db";
import {
  products,
  categories,
  users,
  suppliers,
  purchases,
  stockAdjustments,
  sales,
  receipts,
  stores,
  expenses,
  expenseCategories
} from "../db/schema";

export type AllowedActions =
  | "create"
  | "update"
  | "delete"
  | "getAll"
  | "getById"
  | "read";
export type Entity =
  | "product"
  | "category"
  | "user"
  | "supplier"
  | "purchases"
  | "stockAdjustments"
  | "sales"
  | "receipts"
  | "stores"
  | "expenses"
  | "expense_categories"

interface EntitySchema {
  table: any;
  createdByColumn: any;
}

const entityConfig: Record<Entity, EntitySchema> = {
  product: { table: products, createdByColumn: products.createdBy },
  category: { table: categories, createdByColumn: categories.createdBy },
  user: { table: users, createdByColumn: users.createdBy },
  supplier: { table: suppliers, createdByColumn: suppliers.createdBy },
  purchases: { table: purchases, createdByColumn: purchases.createdBy },
  stockAdjustments: {
    table: stockAdjustments,
    createdByColumn: stockAdjustments.createdBy,
  },
  sales: { table: sales, createdByColumn: sales.createdBy },
  receipts: { table: receipts, createdByColumn: receipts.createdBy },
  stores: { table: stores, createdByColumn: stores.createdBy },
  expenses: { table: expenses, createdByColumn: expenses.createdBy },
  expense_categories: { table: expenseCategories, createdByColumn: expenseCategories.createdBy },
};

/**
 * Role-based authorization function.
 * Returns a query for fetch operations (`getAll`, `getById`),
 * or a filtering query for update/delete actions.
 *
 * This is an async function that must be awaited.
 * It can perform database lookups to determine proper authorization.
 */
export const authorizeAction = async (
  requester: JwtPayload,
  action: AllowedActions,
  entity: Entity,
  entityId?: number
) => {
  const { table, createdByColumn } = entityConfig[entity];

  // ✅ Cashier: Can see products created by their admin & create sales
  if (requester.role === "cashier") {
    if (entity === "product") {
      if (action === "getAll") {
        // Find the cashier's admin ID
        return postgresDb
          .select()
          .from(table)
          .where(
            eq(
              createdByColumn,
              postgresDb
                .select({ adminId: users.createdBy })
                .from(users)
                .where(eq(users.id, requester.id))
                .limit(1)
            )
          );
      }

      if (action === "getById") {
        if (!entityId) throw new Error(`Unauthorized: Missing ${entity} ID.`);

        // For getById, we need to check if the product was created by the cashier's admin
        return postgresDb
          .select()
          .from(table)
          .where(
            and(
              eq(table.id, entityId),
              eq(
                createdByColumn,
                postgresDb
                  .select({ adminId: users.createdBy })
                  .from(users)
                  .where(eq(users.id, requester.id))
                  .limit(1)
              )
            )
          );
      }
    }

    // Handle sales and receipts for cashiers
    if (entity === "sales" || entity === "receipts") {
      if (action === "create") {
        return postgresDb.select().from(table); // ✅ Cashier can create sales and receipts
      }

      if (action === "getAll" || action === "getById") {
        // Find the cashier's creator (admin)
        const creatorResult = await postgresDb
          .select({ creatorId: users.createdBy })
          .from(users)
          .where(eq(users.id, requester.id))
          .limit(1);

        const creatorId = creatorResult[0]?.creatorId;

        if (creatorId) {
          // Get all users created by the same admin (siblings)
          const siblingsResult = await postgresDb
            .select({ siblingId: users.id })
            .from(users)
            .where(eq(users.createdBy, creatorId));

          const siblingIds = siblingsResult.map((s: { siblingId: number }) => s.siblingId);

          // Cashier can view sales/receipts made by them, their admin, and their siblings
          return postgresDb
            .select()
            .from(table)
            .where(
              or(
                eq(createdByColumn, requester.id),
                eq(createdByColumn, creatorId),
                inArray(createdByColumn, siblingIds)
              )
            );
        } else {
          // If no creator found, just show their own sales/receipts
          return postgresDb
            .select()
            .from(table)
            .where(eq(createdByColumn, requester.id));
        }
      }
    }

    // ❌ Explicitly prevent cashiers from performing stock adjustments
    if (entity === "stockAdjustments") {
      throw new Error("Unauthorized: Cashiers cannot perform stock adjustments.");
    }

    // ❌ Explicitly prevent cashiers from managing expenses
    if (entity === "expenses" || entity === "expense_categories") {
      throw new Error("Unauthorized: Cashiers cannot manage expenses.");
    }

    if (action === "getById") {
      if (!entityId) throw new Error(`Unauthorized: Missing ${entity} ID.`);

      if (entity === "user") {
        return postgresDb
          .select()
          .from(table)
          .where(eq(table.id, requester.id)); // ✅ Cashier can only see themselves
      }

      if (entity === "category") {
        return postgresDb.select().from(table).where(eq(table.id, entityId)); // ✅ Cashier can view categories
      }
    }

    throw new Error(`Unauthorized: Cashiers cannot ${action} ${entity}s.`);
  }

  // ✅ Superadmin: Full access
  if (requester.role === "superadmin") {
    return postgresDb.select().from(table);
  }

  // ✅ Admin Access Rules
  if (requester.role === "admin") {
    if (action === "create") {
      return postgresDb.select().from(table);
    }

    if (action === "getAll") {
      // Special case for users - admins can see users they created
      if (entity === "user") {
        return postgresDb
          .select()
          .from(table)
          .where(or(eq(table.id, requester.id), eq(createdByColumn, requester.id)));
      }

      // Special case for sales, receipts, expenses, and expense_categories - admins can ONLY see these from their team
      if (entity === "sales" || entity === "receipts" || entity === "expenses" || entity === "expense_categories") {
        // Get all users created by this admin
        const teamMembersResult = await postgresDb
          .select({ memberId: users.id })
          .from(users)
          .where(eq(users.createdBy, requester.id));

        const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

        // Admin can see their own sales/receipts/expenses and those from users they created
        if (teamMemberIds.length > 0) {
          return postgresDb
            .select()
            .from(table)
            .where(
              or(
                eq(createdByColumn, requester.id),
                inArray(createdByColumn, teamMemberIds)
              )
            );
        } else {
          // If no team members, just show their own
          return postgresDb
            .select()
            .from(table)
            .where(eq(createdByColumn, requester.id));
        }
      }

      // For other entities, admins can only see what they created
      return postgresDb
        .select()
        .from(table)
        .where(eq(createdByColumn, requester.id));
    }

    if (!entityId) {
      throw new Error(`Unauthorized: Missing ${entity} ID.`);
    }

    // 🔹 Special Case for Users: Allow Admins to Fetch Their Own Data
    if (entity === "user") {
      return postgresDb
        .select()
        .from(table)
        .where(
          or(eq(table.id, requester.id), eq(createdByColumn, requester.id))
        ); // ✅ Admin can get their own user details
    }

    // Special case for sales, receipts, expenses, and expense_categories - admins can ONLY see these from their team
    if (entity === "sales" || entity === "receipts" || entity === "expenses" || entity === "expense_categories") {
      // Get all users created by this admin
      const teamMembersResult = await postgresDb
        .select({ memberId: users.id })
        .from(users)
        .where(eq(users.createdBy, requester.id));

      const teamMemberIds = teamMembersResult.map((m: { memberId: number }) => m.memberId);

      if (teamMemberIds.length > 0) {
        // Admin can see their own sales/receipts/expenses and those from users they created
        return postgresDb
          .select()
          .from(table)
          .where(
            and(
              eq(table.id, entityId),
              or(
                eq(createdByColumn, requester.id),
                inArray(createdByColumn, teamMemberIds)
              )
            )
          );
      } else {
        // If no team members, just show their own
        return postgresDb
          .select()
          .from(table)
          .where(
            and(
              eq(table.id, entityId),
              eq(createdByColumn, requester.id)
            )
          );
      }
    }

    return postgresDb
      .select()
      .from(table)
      .where(and(eq(table.id, entityId), eq(createdByColumn, requester.id)));
  }

  throw new Error("Unauthorized: Invalid role.");
};
