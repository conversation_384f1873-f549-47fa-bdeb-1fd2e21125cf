// services/categoryApi.ts
import { createApi, EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define Category types
export interface Category {
  id: number;
  name: string;
  description?: string;
  createdBy: number;
  createdAt: string;
  updatedAt?: string;
}

export interface PaginatedCategories {
  total: number;
  page: number;
  perPage: number;
  categories: Category[];
}

export interface CreateCategoryDto {
  name: string;
  description?: string;
}

export interface UpdateCategoryDto {
  name?: string;
  description?: string;
}

type CategoryEndpoints = {
  getAllCategories: any;
  getCategoryById: any;
  createCategory: any;
  updateCategory: any;
  deleteCategory: any;
  bulkDeleteCategories: any;
};

export const categoryApi = createApi({
  reducerPath: 'categoryApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Category'] as const,
  endpoints: (builder): CategoryEndpoints => ({
    // Get all categories (paginated)
    getAllCategories: builder.query<ApiResponse<PaginatedCategories>, { page?: number; limit?: number; search?: string }>({
      query: ({ page = 1, limit = 10, search = '' }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Log the search parameters
        console.log("getAllCategories query params:", { page, limit, search });

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search: search.trim(), // Include search parameter
          },
          token,
        };
      },
      // Force refetch when search parameters change
      keepUnusedDataFor: 0,
      providesTags: ['Category'],
    }),

    // Get category by ID
    getCategoryById: builder.query<ApiResponse<Category>, number>({
      query: (categoryId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'retrieve',
            categoryId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'Category', id }],
    }),

    // Create new category
    createCategory: builder.mutation<ApiResponse<Category>, CreateCategoryDto>({
      query: (categoryData): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Debug logging
        console.log('Creating category with token:', token ? 'Token exists (not showing for security)' : 'No token found');
        console.log('Category data:', categoryData);
        console.log('Auth state:', {
          hasUser: !!authState?.user,
          hasToken: !!authState?.accessToken,
          userRole: authState?.user?.role
        });

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'createnew',
            ...categoryData,
          },
          token,
        };
      },
      invalidatesTags: ['Category'],
    }),

    // Update category
    updateCategory: builder.mutation<ApiResponse<Category>, { categoryId: number; data: UpdateCategoryDto }>({
      query: ({ categoryId, data }): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'update',
            categoryId,
            ...data,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { categoryId }) => [
        { type: 'Category', id: categoryId },
        'Category',
      ],
    }),

    // Delete category (single)
    deleteCategory: builder.mutation<ApiResponse<{ success: boolean }>, number>({
      query: (categoryId): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'delete',
            categoryId,
          },
          token,
        };
      },
      invalidatesTags: ['Category'],
    }),

    // Bulk delete categories
    bulkDeleteCategories: builder.mutation<ApiResponse<{ deletedIds: number[] }>, number[]>({
      query: (categoryIds): { urlpath: string; payloaddata: any; token?: string } => {
        // Get token from store - ensure it's a string, not undefined
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        // Check if token is missing and throw a more helpful error
        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/categories',
          payloaddata: {
            mode: 'delete',
            categoryIds,
          },
          token,
        };
      },
      invalidatesTags: ['Category'],
    }),
  }),
});

export const {
  useGetAllCategoriesQuery,
  useGetCategoryByIdQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
  useBulkDeleteCategoriesMutation,
} = categoryApi;

