"use client";

import React from 'react';
import { useGetDashboardStatsQuery } from '@/reduxRTK/services/dashboardApi';
import { useGetAllSalesQuery } from '@/reduxRTK/services/salesApi';
import { useGetAllProductsQuery } from '@/reduxRTK/services/productApi';
import { Spin, Card, Row, Col, Statistic, Tooltip } from 'antd';
import { LoadingOutlined, DollarOutlined, ShoppingCartOutlined, ProductOutlined, RiseOutlined, TeamOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import PaymentStatusWidget from './PaymentStatusWidget';
import AdminSalesChart from './Charts/AdminSalesChart';
import AdminProductsChart from './Charts/AdminProductsChart';
import AdminRevenueChart from './Charts/AdminRevenueChart';
import ProfitLossChart from './Charts/ProfitLossChart';
import { AdminStats } from '@/reduxRTK/services/dashboardApi';

interface AdminDashboardProps {
  className?: string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ className = "" }) => {
  // Fetch dashboard stats
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useGetDashboardStatsQuery();

  // Fetch sales data for charts
  const { data: salesData, isLoading: salesLoading } = useGetAllSalesQuery({
    page: 1,
    limit: 1000,
    search: ''
  });

  // Fetch products data
  const { data: productsData, isLoading: productsLoading } = useGetAllProductsQuery({
    page: 1,
    limit: 1000,
    search: ''
  });

  const stats = dashboardData?.data as AdminStats;
  const sales = salesData?.data?.sales || [];
  const products = productsData?.data?.products || [];

  if (dashboardLoading || salesLoading || productsLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
      </div>
    );
  }

  if (dashboardError) {
    return (
      <div className="text-center text-red-500 p-8">
        <p>Error loading dashboard data. Please try again.</p>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center text-gray-500 p-8">
        <p>No dashboard data available.</p>
      </div>
    );
  }

  // Calculate additional metrics from frontend data for charts
  const totalSales = sales.length;
  const frontendRevenue = sales.reduce((sum, sale) => sum + parseFloat(sale.totalAmount || '0'), 0);
  const averageOrderValue = totalSales > 0 ? frontendRevenue / totalSales : 0;
  const totalProducts = products.length;

  // Helper function to format growth rate
  const formatGrowthRate = (rate: number) => {
    const percentage = (rate * 100).toFixed(1);
    const isPositive = rate >= 0;
    return {
      value: `${isPositive ? '+' : ''}${percentage}%`,
      color: isPositive ? '#3f8600' : '#cf1322',
      icon: isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />
    };
  };

  // Helper to format large numbers (e.g., 49250000 -> 49.3M)
  function formatLargeNumber(num: number, isMoney = false): string {
    let prefix = isMoney ? '₵' : '';
    if (num >= 1_000_000_000) return `${prefix}${(num / 1_000_000_000).toFixed(1)}B`;
    if (num >= 1_000_000) return `${prefix}${(num / 1_000_000).toFixed(1)}M`;
    if (num >= 1_000) return `${prefix}${(num / 1_000).toFixed(1)}k`;
    return isMoney ? `₵${num.toLocaleString()}` : num.toLocaleString();
  }

  // Helper to render a value with a tooltip for k/M/B
  function ValueWithTooltip({ value, isMoney = false, isPercent = false }: { value: number, isMoney?: boolean, isPercent?: boolean }) {
    const formatted = formatLargeNumber(value, isMoney);
    let explanation = '';
    if (formatted.includes('k')) explanation = 'k = thousand';
    if (formatted.includes('M')) explanation = 'M = million';
    if (formatted.includes('B')) explanation = 'B = billion';
    if (isPercent) return <>{formatted} %</>;
    return explanation ? (
      <Tooltip title={explanation}>{formatted}</Tooltip>
    ) : (
      <>{formatted}</>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">Manage your store operations and track performance</p>
      </div>

      {/* Key Metrics Cards */}
      <Row gutter={[16, 16]} className="mb-6" justify="center" align="middle">
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Revenue (Team) - This Month"
                value={stats.revenue.value}
                valueRender={() => <ValueWithTooltip value={stats.revenue.value} isMoney />}
                precision={2}
                valueStyle={{ color: '#3f8600', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">
                <TeamOutlined className="mr-1" />
                Admin + Cashiers
              </div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.revenue.growthRate).color }}>
                {formatGrowthRate(stats.revenue.growthRate).icon} {formatGrowthRate(stats.revenue.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Total Sales - This Month"
                value={stats.sales.value}
                valueRender={() => <ValueWithTooltip value={stats.sales.value} />}
                precision={0}
                valueStyle={{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Completed transactions</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.sales.growthRate).color }}>
                {formatGrowthRate(stats.sales.growthRate).icon} {formatGrowthRate(stats.sales.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Net Profit - This Month"
                value={stats.profit.value}
                valueRender={() => <ValueWithTooltip value={stats.profit.value} isMoney />}
                precision={2}
                valueStyle={{ color: stats.profit.value >= 0 ? '#3f8600' : '#cf1322', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">After expenses & COGS</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.profit.growthRate).color }}>
                {formatGrowthRate(stats.profit.growthRate).icon} {formatGrowthRate(stats.profit.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Products (All Time)"
                value={stats.products.value}
                valueRender={() => <ValueWithTooltip value={stats.products.value} />}
                precision={0}
                valueStyle={{ color: '#fa8c16', fontSize: '24px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">In inventory</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.products.growthRate).color }}>
                {formatGrowthRate(stats.products.growthRate).icon} {formatGrowthRate(stats.products.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Additional Metrics Row */}
      <Row gutter={[16, 16]} className="mb-6" justify="center" align="middle">
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Cashiers - This Month"
                value={stats.cashiers.value}
                valueRender={() => <ValueWithTooltip value={stats.cashiers.value} />}
                precision={0}
                valueStyle={{ color: '#722ed1', fontSize: '20px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Team members</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.cashiers.growthRate).color }}>
                {formatGrowthRate(stats.cashiers.growthRate).icon} {formatGrowthRate(stats.cashiers.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Expenses - This Month"
                value={stats.expenses.value}
                valueRender={() => <ValueWithTooltip value={stats.expenses.value} isMoney />}
                precision={2}
                valueStyle={{ color: '#cf1322', fontSize: '20px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Total expenses</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.expenses.growthRate).color }}>
                {formatGrowthRate(stats.expenses.growthRate).icon} {formatGrowthRate(stats.expenses.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="COGS - This Month"
                value={stats.cogs.value}
                valueRender={() => <ValueWithTooltip value={stats.cogs.value} isMoney />}
                precision={2}
                valueStyle={{ color: '#fa541c', fontSize: '20px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Cost of goods sold</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.cogs.growthRate).color }}>
                {formatGrowthRate(stats.cogs.growthRate).icon} {formatGrowthRate(stats.cogs.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} style={{ display: 'flex' }}>
          <Card className="h-full w-full flex flex-col items-center justify-center min-h-[180px] max-h-[180px]" bodyStyle={{ padding: 16, height: '100%' }}>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <Statistic
                title="Profit Margin - This Month"
                value={stats.profitMargin.value}
                valueRender={() => <ValueWithTooltip value={stats.profitMargin.value} isPercent />}
                precision={1}
                valueStyle={{ color: stats.profitMargin.value >= 0 ? '#3f8600' : '#cf1322', fontSize: '20px', fontWeight: 'bold', width: '100%', textAlign: 'center' }}
              />
              <div className="text-sm text-gray-500 mt-2 text-center">Profit percentage</div>
              <div className="text-xs mt-1 text-center" style={{ color: formatGrowthRate(stats.profitMargin.growthRate).color }}>
                {formatGrowthRate(stats.profitMargin.growthRate).icon} {formatGrowthRate(stats.profitMargin.growthRate).value}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 1 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="Sales Trends" className="shadow-md h-96">
            <AdminSalesChart sales={sales} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Revenue Over Time (Team)" className="shadow-md h-96">
            <AdminRevenueChart sales={sales} />
            <div className="text-xs text-gray-500 mt-2 text-center">
              <TeamOutlined className="mr-1" />
              Includes admin and cashier sales
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row 2 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="Profit & Loss Analysis (This Month)" className="shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col" bodyStyle={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <ProfitLossChart stats={stats} formatLargeNumber={formatLargeNumber} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Product Performance" className="shadow-md h-[400px] min-h-[400px] max-h-[400px] flex flex-col" bodyStyle={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <AdminProductsChart products={products} sales={sales} />
          </Card>
        </Col>
      </Row>

      {/* Charts Row 3 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="Payment Status" className="shadow-md h-96">
            <PaymentStatusWidget />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Team Performance (This Month)" className="shadow-md h-96">
            <div className="p-4">
              <div className="grid grid-cols-2 gap-4 h-full">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{formatLargeNumber(stats.sales.value)}</div>
                  <div className="text-sm text-gray-600">Total Sales</div>
                  <div className="text-xs mt-1" style={{ color: formatGrowthRate(stats.sales.growthRate).color }}>
                    {formatGrowthRate(stats.sales.growthRate).icon} {formatGrowthRate(stats.sales.growthRate).value}
                  </div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{formatLargeNumber(stats.revenue.value)}</div>
                  <div className="text-sm text-gray-600">Team Revenue</div>
                  <div className="text-xs mt-1" style={{ color: formatGrowthRate(stats.revenue.growthRate).color }}>
                    {formatGrowthRate(stats.revenue.growthRate).icon} {formatGrowthRate(stats.revenue.growthRate).value}
                  </div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{stats.cashiers.value}</div>
                  <div className="text-sm text-gray-600">Cashiers</div>
                  <div className="text-xs mt-1" style={{ color: formatGrowthRate(stats.cashiers.growthRate).color }}>
                    {formatGrowthRate(stats.cashiers.growthRate).icon} {formatGrowthRate(stats.cashiers.growthRate).value}
                  </div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{stats.products.value}</div>
                  <div className="text-sm text-gray-600">Products</div>
                  <div className="text-xs mt-1" style={{ color: formatGrowthRate(stats.products.growthRate).color }}>
                    {formatGrowthRate(stats.products.growthRate).icon} {formatGrowthRate(stats.products.growthRate).value}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AdminDashboard;
