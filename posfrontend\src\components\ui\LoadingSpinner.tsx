"use client";

import React from "react";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

interface LoadingSpinnerProps {
  size?: "small" | "default" | "large";
  fullScreen?: boolean;
  tip?: string;
}

/**
 * LoadingSpinner component that uses Ant Design's Spin component
 * Simple, centered spinner with no text
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "large",
  fullScreen = false,
  tip,
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  // For fullScreen loading
  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80">
        <Spin
          size={size}
          indicator={antIcon}
          tip={tip}
        />
      </div>
    );
  }

  // For regular loading
  return (
    <div className="flex flex-col h-full w-full items-center justify-center py-8">
      <Spin
        size={size}
        indicator={antIcon}
        tip={tip}
      />
    </div>
  );
};

export default LoadingSpinner;
