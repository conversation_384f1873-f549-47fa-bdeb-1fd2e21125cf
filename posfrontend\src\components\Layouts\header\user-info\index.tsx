"use client";

import { ChevronUpIcon } from "@/assets/icons";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { LogOutIcon, SettingsIcon, UserIcon } from "./icons";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux"; // Import useSelector to access Redux state
import { useLogoutUser } from "@/hooks/logoutUser"; // Import the useLogoutUser hook
import { RootState } from "@/reduxRTK/store/store"; // Assuming RootState is the type of your Redux store

// Fallback avatar icon
const DefaultAvatar = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    className="h-6 w-6 text-gray-500"
  >
    <circle cx="12" cy="8" r="4" />
    <path d="M4 20c0-4 4-4 8-4s8 4 8 4" />
  </svg>
);

export function UserInfo() {
  const [isOpen, setIsOpen] = useState(false);
  const user = useSelector((state: RootState) => state.auth.user);

  const router = useRouter();
  const { logout } = useLogoutUser();

  const handleLogout = async () => {
    if (user && user.email) {
      await logout(user.email);
      router.replace("/");
    }
  };

  return (
    <Dropdown isOpen={isOpen} setIsOpen={setIsOpen}>
      <DropdownTrigger className="rounded align-middle outline-none ring-primary ring-offset-2 focus-visible:ring-1">
        <span className="sr-only">My Account</span>
        <figure className="flex items-center gap-3">
          <DefaultAvatar />
          <figcaption className="flex items-center gap-1 font-medium text-dark max-[1024px]:sr-only">
            <span>{user?.name.split(" ")[0] || "John Smith"}</span>
            <ChevronUpIcon
              aria-hidden
              className={cn(
                "rotate-180 transition-transform",
                isOpen && "rotate-0",
              )}
              strokeWidth={1.5}
            />
          </figcaption>
        </figure>
      </DropdownTrigger>
      <DropdownContent
        className="border border-stroke bg-white shadow-md min-[230px]:min-w-[17.5rem]"
        align="end"
      >
        <h2 className="sr-only">User information</h2>
        <figure className="flex items-center gap-2.5 px-5 py-3.5">
          <DefaultAvatar />
          <figcaption className="space-y-1 text-base font-medium">
            <div className="mb-2 leading-none text-dark">
              {user?.name || "user"}
            </div>
            <div className="leading-none text-gray-6">
              {user?.email || "<EMAIL>"}
            </div>
          </figcaption>
        </figure>
        <hr className="border-[#E8E8E8]" />
        <div className="p-2 text-base text-[#4B5563] [&>*]:cursor-pointer">
          <Link
            href={"/dashboard/profile"}
            onClick={(e) => {
              e.preventDefault();
              setIsOpen(false);
              router.push("/dashboard/profile");
            }}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark"
          >
            <UserIcon />
            <span className="mr-auto text-base font-medium">View profile</span>
          </Link>
          {/* <Link
            href={"/pages/settings"}
            onClick={() => {
              setIsOpen(false);
              // Allow default navigation for settings page
            }}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark"
          >
            <SettingsIcon />
            <span className="mr-auto text-base font-medium">
              Account Settings
            </span>
          </Link> */}
        </div>
        <hr className="border-[#E8E8E8]" />
        <div className="p-2 text-base text-[#4B5563]">
          <button
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark"
            onClick={handleLogout}
          >
            <LogOutIcon />
            <span className="text-base font-medium">Log out</span>
          </button>
        </div>
      </DropdownContent>
    </Dropdown>
  );
}
