"use client";

import React, { useState, useEffect } from "react";
import { Sidebar } from "@/components/Layouts/sidebar";
import { MobileSidebar } from "@/components/Layouts/sidebar/MobileSidebar";
import { Header } from "@/components/Layouts/header";
import PaymentNotification from "@/components/Payment/PaymentNotification";
import ProtectedRoute from "@/components/Auth/ProtectedRoute";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useSidebarContext } from "@/components/Layouts/sidebar/sidebar-context";
import OfflineIndicator from "@/components/Common/OfflineIndicator";
import PWAInstallPrompt from "@/components/Common/PWAInstallPrompt";
import PWAUpdateNotification from "@/components/Common/PWAUpdateNotification";
import { useDispatch, useSelector } from "react-redux";
import { userApi } from "@/reduxRTK/services/authApi";
import { RootState } from "@/reduxRTK/store/store";
import { useRouter, usePathname } from "next/navigation";
import { setUser } from "@/reduxRTK/services/authSlice";
import { usePaymentWebSocket } from '@/hooks/usePaymentWebSocket';

interface ProtectedDashboardContentProps {
  children: React.ReactNode;
}

const ProtectedDashboardContent = ({ children }: ProtectedDashboardContentProps) => {
  const [isClient, setIsClient] = useState(false);
  const { setIsOpen, isMobile } = useSidebarContext();

  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const pathname = usePathname();

  usePaymentWebSocket();

  // Live user sync: always refetch user on dashboard load
  React.useEffect(() => {
    if (!user) return;
    // @ts-ignore - RTK Query initiate returns a thunk, so we cast to any for .then
    (dispatch as any)(userApi.endpoints.getCurrentUser.initiate(undefined, { forceRefetch: true }))
      .then((result: any) => {
        if (result?.data?.success && result.data.data) {
          // Only update Redux state if API call is successful
          const freshUser = result.data.data;
          if (accessToken) {
            console.log("[Sync] Updating Redux state with fresh user and token:", freshUser, accessToken);
            dispatch(setUser({ user: freshUser, accessToken }));
          } else {
            console.warn("[Sync] No access token found in Redux state; not updating user.");
          }
        } else {
          // Handle errors
          let errorMsg = "";
          if (typeof result?.error === "string") {
            errorMsg = result.error.toLowerCase();
          } else {
            errorMsg =
              result?.error?.message?.toLowerCase?.() ||
              result?.error?.data?.message?.toLowerCase?.() ||
              result?.error?.data?.error?.toLowerCase?.() ||
              "";
          }
          console.log("[Sync] Error message:", errorMsg);
          if (errorMsg.includes("payment required")) {
            if (user && user.paymentStatus === "paid" && accessToken) {
              console.log("[Sync] Setting user paymentStatus to 'pending' with token:", accessToken);
              dispatch(setUser({ user: { ...user, paymentStatus: "pending" }, accessToken }));
            } else {
              console.warn("[Sync] No access token found in Redux state; not updating user to pending.");
            }
            console.log("[Sync] Redirecting to /payment");
            router.replace("/payment");
          }
        }
      });
  }, [dispatch, user, router, accessToken]);

  // Redirect if payment status changes from 'paid' to anything else
  React.useEffect(() => {
    if (!user) return;
    if (user.role === "superadmin") return;
    if (user.paymentStatus !== "paid" && pathname !== "/payment") {
      console.log("[Watcher] Payment status changed, redirecting to /payment");
      router.replace("/payment");
    }
  }, [user, router, pathname]);

  // Set isClient to true when component mounts on client
  useEffect(() => {
    setIsClient(true);
    // Don't automatically close the sidebar - let the sidebar context handle it
    console.log("ProtectedDashboardContent mounted on client", { isMobile });
  }, [isMobile]);

  // Force close sidebar when component unmounts to prevent persistence
  useEffect(() => {
    return () => {
      setIsOpen(false);
      console.log("ProtectedDashboardContent unmounted - closing sidebar");
    };
  }, [setIsOpen]);

  // Show loading spinner until client-side hydration is complete
  if (!isClient) {
    return <LoadingSpinner tip="Loading dashboard..." />;
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden">
        {/* Mobile sidebar - only visible when explicitly toggled */}
        <MobileSidebar />

        {/* Fixed sidebar - hidden on mobile, visible on desktop */}
        <div className="fixed left-0 top-0 h-full z-20 hidden lg:block">
          <Sidebar />
        </div>

        {/* Main content area with fixed header and scrollable content */}
        <div className="w-full lg:ml-[290px] bg-white flex flex-col h-screen min-w-0">
          {/* Fixed header */}
          <div className="sticky top-0 z-30 w-full bg-white border-b border-gray-200">
            <Header />
          </div>

          {/* Scrollable content */}
          <div className="flex-1 overflow-y-auto min-w-0">
            <main className="isolate mx-auto w-full max-w-screen-2xl p-4 md:p-6 2xl:p-10">
              <PaymentNotification />
              {children}
            </main>
          </div>
        </div>
      </div>

      {/* PWA Components */}
      <PWAInstallPrompt />
      <PWAUpdateNotification />
    </ProtectedRoute>
  );
};

export default ProtectedDashboardContent;
