'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Card, Modal, Typography, Space, Divider } from 'antd';
import { 
  DownloadOutlined, 
  MobileOutlined, 
  DesktopOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  WifiOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      // Check for standalone mode (iOS)
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      // Check for PWA mode (iOS Safari)
      const isPWA = (window.navigator as any).standalone === true;

      setIsInstalled(isStandalone || isPWA);
    };

    checkIfInstalled();

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show install prompt after a delay (only if not installed)
      if (!isInstalled) {
        setTimeout(() => {
          setShowInstallPrompt(true);
        }, 10000); // Show after 10 seconds
      }
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('PWA installation accepted');
      } else {
        console.log('PWA installation dismissed');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
      setShowModal(false);
    } catch (error) {
      console.error('Error during PWA installation:', error);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // Don't show again for this session
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  };

  const handleShowModal = () => {
    setShowModal(true);
  };

  // Don't show if already installed or dismissed this session
  if (isInstalled || sessionStorage.getItem('pwa-install-dismissed')) {
    return null;
  }

  return (
    <>
      {/* Floating Install Button */}
      {showInstallPrompt && deferredPrompt && (
        <div className="fixed bottom-4 right-4 z-50">
          <Card 
            className="shadow-lg border-blue-500 border-2"
            style={{ maxWidth: 300 }}
            actions={[
              <Button 
                key="install"
                type="primary" 
                icon={<DownloadOutlined />}
                onClick={handleShowModal}
                className="w-full"
              >
                Install App
              </Button>
            ]}
          >
            <div className="text-center">
              <MobileOutlined className="text-2xl text-blue-500 mb-2" />
              <Title level={5} className="mb-1">Install NEXAPO POS</Title>
              <Text type="secondary" className="text-sm">
                Get faster access and work offline
              </Text>
            </div>
            <Button 
              type="text" 
              icon={<CloseOutlined />}
              onClick={handleDismiss}
              className="absolute top-2 right-2 p-1"
              size="small"
            />
          </Card>
        </div>
      )}

      {/* Detailed Install Modal */}
      <Modal
        title={
          <div className="flex items-center">
            <DownloadOutlined className="mr-2 text-blue-500" />
            Install NEXAPO POS App
          </div>
        }
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowModal(false)}>
            Maybe Later
          </Button>,
          <Button 
            key="install" 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleInstallClick}
            disabled={!deferredPrompt}
          >
            Install Now
          </Button>
        ]}
        width={500}
      >
        <div className="py-4">
          <div className="text-center mb-6">
            <div className="bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <ShoppingCartOutlined className="text-3xl text-blue-500" />
            </div>
            <Title level={4} className="mb-2">
              Get the full NEXAPO POS experience
            </Title>
            <Text type="secondary">
              Install our app for faster access and enhanced offline capabilities
            </Text>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircleOutlined className="text-green-500 mt-1" />
              <div>
                <Text strong>Lightning Fast</Text>
                <br />
                <Text type="secondary" className="text-sm">
                  Instant loading and smooth performance
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <WifiOutlined className="text-green-500 mt-1" />
              <div>
                <Text strong>Works Offline</Text>
                <br />
                <Text type="secondary" className="text-sm">
                  Continue making sales even without internet
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <MobileOutlined className="text-green-500 mt-1" />
              <div>
                <Text strong>Native App Experience</Text>
                <br />
                <Text type="secondary" className="text-sm">
                  Full-screen mode with app-like navigation
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <DesktopOutlined className="text-green-500 mt-1" />
              <div>
                <Text strong>Desktop & Mobile</Text>
                <br />
                <Text type="secondary" className="text-sm">
                  Works perfectly on all your devices
                </Text>
              </div>
            </div>
          </div>

          <Divider />

          <div className="bg-blue-50 p-4 rounded-lg">
            <Text strong className="text-blue-700">
              💡 Pro Tip:
            </Text>
            <br />
            <Text className="text-blue-600 text-sm">
              After installation, you can access NEXAPO POS directly from your home screen 
              or desktop, just like any other app!
            </Text>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default PWAInstallPrompt;
