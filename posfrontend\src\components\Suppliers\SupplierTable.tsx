"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
  HomeOutlined,
  CalendarOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Supplier } from "@/reduxRTK/services/supplierApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface SupplierTableProps {
  suppliers: Supplier[];
  loading: boolean;
  onView: (supplierId: number) => void;
  onEdit: (supplier: Supplier) => void;
  onDelete: (supplierId: number) => void;
  onBulkDelete?: (supplierIds: number[]) => void;
  isMobile?: boolean;
}

const SupplierTable: React.FC<SupplierTableProps> = ({
  suppliers,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected suppliers
  const [selectedSuppliers, setSelectedSuppliers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all suppliers that the user can delete
      const selectableSupplierIds = suppliers
        .filter(supplier => canEditDelete(supplier))
        .map(supplier => supplier.id);
      setSelectedSuppliers(selectableSupplierIds);
    } else {
      // Deselect all suppliers
      setSelectedSuppliers([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (supplierId: number, checked: boolean) => {
    if (checked) {
      setSelectedSuppliers(prev => [...prev, supplierId]);
    } else {
      setSelectedSuppliers(prev => prev.filter(id => id !== supplierId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedSuppliers.length > 0 && onBulkDelete) {
      onBulkDelete(selectedSuppliers);
      setSelectedSuppliers([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No suppliers selected',
        description: 'Please select at least one supplier to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Check if user can edit/delete (admin can edit/delete all suppliers they can see)
  const canEditDelete = (supplier: Supplier) => {
    // Admin can edit/delete all suppliers they can see
    // The backend already filters suppliers based on permissions
    return userRole === "admin";
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when suppliers are selected */}
      {selectedSuppliers.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedSuppliers.length} {selectedSuppliers.length === 1 ? 'supplier' : 'suppliers'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 150px 120px 120px 150px"
          minWidth="800px"
        >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={suppliers.filter(supplier => canEditDelete(supplier)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          <span className="flex items-center">
            <UserOutlined className="mr-1" />
            Name
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <MailOutlined className="mr-1" />
            Email
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <PhoneOutlined className="mr-1" />
            Phone
          </span>
        </TableHeader>
        {!isMobile && (
          <TableHeader>
            <span className="flex items-center">
              <UserOutlined className="mr-1" />
              Contact Person
            </span>
          </TableHeader>
        )}
        <TableHeader>
          <span className="flex items-center">
            <CalendarOutlined className="mr-1" />
            Created At
          </span>
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
          {/* Mobile Rows */}
          {suppliers.map((supplier) => (
            <TableRow
              key={supplier.id}
              selected={selectedSuppliers.includes(supplier.id)}
            >
              <TableCell className="text-center">
                {canEditDelete(supplier) && (
                  <Checkbox
                    checked={selectedSuppliers.includes(supplier.id)}
                    onChange={(e) => handleCheckboxChange(supplier.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {supplier.name}
                </div>
              </TableCell>
              <TableCell>
                <span className="text-blue-600">
                  {supplier.email || 'N/A'}
                </span>
              </TableCell>
              <TableCell>
                <span className="font-mono text-sm">
                  {supplier.phone || 'N/A'}
                </span>
              </TableCell>
              <TableCell>
                {formatDate(supplier.createdAt)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(supplier.id)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  {canEditDelete(supplier) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(supplier)}
                          type="text"
                          className="edit-button text-blue-500 hover:text-blue-400"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(supplier.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={suppliers.filter(supplier => canEditDelete(supplier)).length === 0}
                  />
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <UserOutlined className="mr-1" />
                    Name
                  </span>
                </th>

                {/* Email Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <MailOutlined className="mr-1" />
                    Email
                  </span>
                </th>

                {/* Phone Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <PhoneOutlined className="mr-1" />
                    Phone
                  </span>
                </th>

                {/* Contact Person Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <UserOutlined className="mr-1" />
                    Contact Person
                  </span>
                </th>

                {/* Created At Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <CalendarOutlined className="mr-1" />
                    Created At
                  </span>
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {suppliers.map((supplier) => (
                <tr key={supplier.id} className={selectedSuppliers.includes(supplier.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canEditDelete(supplier) && (
                      <Checkbox
                        checked={selectedSuppliers.includes(supplier.id)}
                        onChange={(e) => handleCheckboxChange(supplier.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {supplier.name}
                    </div>
                  </td>

                  {/* Email Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[200px] overflow-hidden text-ellipsis">
                      {supplier.email || 'N/A'}
                    </div>
                  </td>

                  {/* Phone Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[140px] overflow-hidden text-ellipsis">
                      {supplier.phone || 'N/A'}
                    </div>
                  </td>

                  {/* Contact Person Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[140px] overflow-hidden text-ellipsis">
                      {supplier.contactPerson || 'N/A'}
                    </div>
                  </td>

                  {/* Created At Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[130px] overflow-hidden text-ellipsis">
                      {formatDate(supplier.createdAt)}
                    </div>
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(supplier.id)}
                          type="text"
                          className="view-button text-green-500"
                          size="middle"
                        />
                      </Tooltip>
                      {canEditDelete(supplier) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(supplier)}
                              type="text"
                              className="edit-button text-blue-500"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(supplier.id)}
                              type="text"
                              className="delete-button text-red-500"
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default SupplierTable;
