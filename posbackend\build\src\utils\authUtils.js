"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.omitPassword = exports.comparePassword = exports.hashPassword = exports.generateToken = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const constant_1 = require("../constant/constant");
// ✅ **Generate JWT Token**
const generateToken = (id, role) => {
    const payload = { id, role };
    return jsonwebtoken_1.default.sign(payload, constant_1.JWT_SECRET, { expiresIn: "7d" });
};
exports.generateToken = generateToken;
// ✅ **Hash Password**
const hashPassword = async (password) => {
    return await bcryptjs_1.default.hash(password, constant_1.SALT_ROUNDS);
};
exports.hashPassword = hashPassword;
// ✅ **Compare Password**
const comparePassword = async (password, hash) => {
    return await bcryptjs_1.default.compare(password, hash);
};
exports.comparePassword = comparePassword;
// ✅ Helper function to exclude password
const omitPassword = (user) => {
    const { passwordHash, id, ...safeUser } = user;
    return { ...safeUser, id: Number(id) };
};
exports.omitPassword = omitPassword;
