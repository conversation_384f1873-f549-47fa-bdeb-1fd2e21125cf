"use strict";
/**
 * @swagger
 * components:
 *   schemas:
 *     SaleItem:
 *       type: object
 *       required:
 *         - productId
 *         - quantity
 *         - price
 *       properties:
 *         productId:
 *           type: integer
 *           example: 1
 *           description: Product ID
 *         quantity:
 *           type: integer
 *           example: 2
 *           description: Quantity sold
 *         price:
 *           type: number
 *           format: decimal
 *           example: 5.50
 *           description: Unit price at time of sale
 *
 *     CreateSaleRequest:
 *       type: object
 *       required:
 *         - items
 *         - paymentMethod
 *         - totalAmount
 *       properties:
 *         items:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SaleItem'
 *           description: Array of items being sold
 *         paymentMethod:
 *           type: string
 *           enum: [cash, card, paystack]
 *           example: cash
 *           description: Payment method used
 *         totalAmount:
 *           type: number
 *           format: decimal
 *           example: 11.00
 *           description: Total sale amount in GHS
 *         storeId:
 *           type: integer
 *           example: 1
 *           description: Store ID where sale was made (optional)
 *         customerName:
 *           type: string
 *           example: John Customer
 *           description: Customer name (optional)
 *         customerPhone:
 *           type: string
 *           example: "+233123456789"
 *           description: Customer phone (optional)
 *
 *     Sale:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         totalAmount:
 *           type: string
 *           example: "11.00"
 *         paymentMethod:
 *           type: string
 *           enum: [cash, card, paystack]
 *           example: cash
 *         transactionDate:
 *           type: string
 *           format: date-time
 *           example: "2024-01-15T14:30:00Z"
 *         createdBy:
 *           type: integer
 *           example: 1
 *         storeId:
 *           type: integer
 *           example: 1
 *         items:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 example: 1
 *               productId:
 *                 type: integer
 *                 example: 1
 *               quantity:
 *                 type: integer
 *                 example: 2
 *               price:
 *                 type: string
 *                 example: "5.50"
 *               product:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 1
 *                   name:
 *                     type: string
 *                     example: "Coca Cola 500ml"
 *                   sku:
 *                     type: string
 *                     example: "CC500ML001"
 *
 *     SalesListResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Sales retrieved successfully
 *         data:
 *           type: object
 *           properties:
 *             sales:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Sale'
 *             total:
 *               type: integer
 *               example: 50
 *               description: Total number of sales
 *             page:
 *               type: integer
 *               example: 1
 *               description: Current page number
 *             limit:
 *               type: integer
 *               example: 10
 *               description: Items per page
 *             totalPages:
 *               type: integer
 *               example: 5
 *               description: Total number of pages
 *             totalRevenue:
 *               type: string
 *               example: "1250.50"
 *               description: Total revenue from all sales
 */
/**
 * @swagger
 * /receipt:
 *   post:
 *     summary: Manage receipts
 *     description: Generate receipts for sales
 *     tags: [Sales Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mode:
 *                 type: string
 *                 enum: [createnew]
 *                 example: createnew
 *               saleId:
 *                 type: integer
 *                 example: 1
 *                 description: Sale ID to generate receipt for
 *     responses:
 *       200:
 *         description: Receipt generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Receipt generated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     receiptUrl:
 *                       type: string
 *                       example: "/receipts/receipt-123.pdf"
 *                       description: "URL to download the receipt"
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Sale not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
/**
 * @swagger
 * /sales:
 *   post:
 *     summary: Manage sales
 *     description: Create, read, update, or delete sales based on mode parameter
 *     tags: [Sales Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - allOf:
 *                   - type: object
 *                     properties:
 *                       mode:
 *                         type: string
 *                         enum: [createnew]
 *                         example: createnew
 *                   - $ref: '#/components/schemas/CreateSaleRequest'
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [retrieve]
 *                     example: retrieve
 *                   page:
 *                     type: integer
 *                     example: 1
 *                     description: Page number for pagination
 *                   limit:
 *                     type: integer
 *                     example: 10
 *                     description: Number of items per page
 *                   search:
 *                     type: string
 *                     example: "coca"
 *                     description: "Search term for filtering sales"
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     example: "2024-01-01"
 *                     description: "Filter sales from this date"
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     example: "2024-01-31"
 *                     description: "Filter sales until this date"
 *                   paymentMethod:
 *                     type: string
 *                     enum: [cash, card, paystack]
 *                     example: cash
 *                     description: "Filter by payment method"
 *                   storeId:
 *                     type: integer
 *                     example: 1
 *                     description: "Filter by store ID"
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [delete]
 *                     example: delete
 *                   id:
 *                     type: integer
 *                     example: 1
 *                     description: "Sale ID to delete"
 *               - type: object
 *                 properties:
 *                   mode:
 *                     type: string
 *                     enum: [bulk-delete]
 *                     example: bulk-delete
 *                   ids:
 *                     type: array
 *                     items:
 *                       type: integer
 *                     example: [1, 2, 3]
 *                     description: "Array of sale IDs to delete"
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - $ref: '#/components/schemas/SalesListResponse'
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Forbidden - insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Sale not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Insufficient stock
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
