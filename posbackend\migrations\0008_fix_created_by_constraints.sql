-- Migration: Fix created_by constraints to allow NULL when users are deleted
-- Date: 2024-06-01
-- Description: Remove NOT NULL constraints from created_by columns that have ON DELETE SET NULL

-- Fix suppliers table
ALTER TABLE suppliers 
ALTER COLUMN created_by DROP NOT NULL;

-- Fix user_suppliers table  
ALTER TABLE user_suppliers 
ALTER COLUMN created_by DROP NOT NULL;

-- Fix user_stores table and change constraint to SET NULL
ALTER TABLE user_stores 
DROP CONSTRAINT IF EXISTS user_stores_created_by_users_id_fk;

ALTER TABLE user_stores 
ALTER COLUMN created_by DROP NOT NULL;

ALTER TABLE user_stores 
ADD CONSTRAINT user_stores_created_by_users_id_fk 
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
