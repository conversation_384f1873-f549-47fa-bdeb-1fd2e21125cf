import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface POSDatabase extends DBSchema {
  user: {
    key: string;
    value: any;
  };
  store: {
    key: string;
    value: any;
  };
  products: {
    key: string;
    value: any;
  };
  sales: {
    key: string;
    value: any;
  };
}

class IndexedDBService {
  private db: IDBPDatabase<POSDatabase> | null = null;
  private readonly DB_NAME = 'pos-offline-db';
  private readonly DB_VERSION = 1;

  async initDB(): Promise<void> {
    try {
      this.db = await openDB<POSDatabase>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          // Create object stores
          if (!db.objectStoreNames.contains('user')) {
            db.createObjectStore('user');
          }
          if (!db.objectStoreNames.contains('store')) {
            db.createObjectStore('store');
          }
          if (!db.objectStoreNames.contains('products')) {
            db.createObjectStore('products');
          }
          if (!db.objectStoreNames.contains('sales')) {
            db.createObjectStore('sales');
          }
        },
      });
      console.log('✅ IndexedDB initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing IndexedDB:', error);
      throw error;
    }
  }

  async cacheUserData(userData: any): Promise<void> {
    if (!this.db) await this.initDB();
    try {
      await this.db!.put('user', userData, 'current-user');
      console.log('✅ User data cached successfully');
    } catch (error) {
      console.error('❌ Error caching user data:', error);
      throw error;
    }
  }

  async getCachedUserData(): Promise<any> {
    if (!this.db) await this.initDB();
    try {
      const userData = await this.db!.get('user', 'current-user');
      return userData || null;
    } catch (error) {
      console.error('❌ Error retrieving cached user data:', error);
      return null;
    }
  }

  async cacheStoreData(storeData: any): Promise<void> {
    if (!this.db) await this.initDB();
    try {
      await this.db!.put('store', storeData, 'current-store');
      console.log('✅ Store data cached successfully');
    } catch (error) {
      console.error('❌ Error caching store data:', error);
      throw error;
    }
  }

  async getCachedStoreData(): Promise<any> {
    if (!this.db) await this.initDB();
    try {
      const storeData = await this.db!.get('store', 'current-store');
      return storeData || null;
    } catch (error) {
      console.error('❌ Error retrieving cached store data:', error);
      return null;
    }
  }

  async cacheProducts(products: any[]): Promise<void> {
    if (!this.db) await this.initDB();
    try {
      await this.db!.put('products', products, 'all-products');
      console.log('✅ Products cached successfully');
    } catch (error) {
      console.error('❌ Error caching products:', error);
      throw error;
    }
  }

  async getCachedProducts(): Promise<any[]> {
    if (!this.db) await this.initDB();
    try {
      const products = await this.db!.get('products', 'all-products');
      return products || [];
    } catch (error) {
      console.error('❌ Error retrieving cached products:', error);
      return [];
    }
  }

  async cacheSales(sales: any[]): Promise<void> {
    if (!this.db) await this.initDB();
    try {
      await this.db!.put('sales', sales, 'all-sales');
      console.log('✅ Sales cached successfully');
    } catch (error) {
      console.error('❌ Error caching sales:', error);
      throw error;
    }
  }

  async getCachedSales(): Promise<any[]> {
    if (!this.db) await this.initDB();
    try {
      const sales = await this.db!.get('sales', 'all-sales');
      return sales || [];
    } catch (error) {
      console.error('❌ Error retrieving cached sales:', error);
      return [];
    }
  }

  async clearCache(): Promise<void> {
    if (!this.db) await this.initDB();
    try {
      await this.db!.clear('user');
      await this.db!.clear('store');
      await this.db!.clear('products');
      await this.db!.clear('sales');
      console.log('✅ Cache cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
      throw error;
    }
  }
}

export const indexedDBService = new IndexedDBService(); 