"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Spin } from "antd";
import {
  PlusOutlined,
  UserOutlined,
  ShopOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import StoresTable from "@/components/Stores/StoresTable";
import StoresSearch from "@/components/Stores/StoresSearch";
import StoreFormPanel from "@/components/Stores/StoreFormPanel";
import StoreDetailsPanel from "@/components/Stores/StoreDetailsPanel";
import UserStorePanel from "@/components/Stores/UserStorePanel";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { Store } from "@/types/store";
import { useGetAllStoresQuery } from "@/reduxRTK/services/storeApi";
import { useStoreBulkDelete } from "@/hooks/stores/useStoreBulkDelete";
import { useIsMobile } from "@/hooks/use-mobile";

const StoresPage: React.FC = () => {
  // Check if on mobile
  const isMobile = useIsMobile();

  // State for search and panels
  const [search, setSearch] = useState("");
  const [isCreatePanelOpen, setIsCreatePanelOpen] = useState(false);
  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [isViewPanelOpen, setIsViewPanelOpen] = useState(false);
  const [isUserStorePanelOpen, setIsUserStorePanelOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);

  // State for bulk delete
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [storesToDelete, setStoresToDelete] = useState<number[]>([]);

  // Refetch stores when needed
  const { refetch, isLoading, data } = useGetAllStoresQuery({
    page: 1,
    limit: isMobile ? 5 : 10,
    search,
  });

  // Bulk delete store handler
  const { bulkDeleteStores, isDeleting: isBulkDeleting } = useStoreBulkDelete(
    () => {
      setIsBulkDeleteDialogOpen(false);
      refetch();
    },
  );

  // Refetch when mobile status changes
  useEffect(() => {
    refetch();
  }, [isMobile, refetch]);

  // Handle store actions
  const handleEdit = (store: Store) => {
    setSelectedStore(store);
    setIsEditPanelOpen(true);
  };

  const handleView = (store: Store) => {
    setSelectedStore(store);
    setIsViewPanelOpen(true);
  };

  const handleManageUsers = (store: Store) => {
    setSelectedStore(store);
    setIsUserStorePanelOpen(true);
  };

  const handleSuccess = () => {
    refetch();
  };

  // Handle bulk delete
  const handleBulkDelete = (storeIds: number[]) => {
    console.log("handleBulkDelete called with storeIds:", storeIds);
    setStoresToDelete(storeIds);
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    console.log("confirmBulkDelete called with stores:", storesToDelete);

    if (storesToDelete.length > 0) {
      try {
        // Use the bulk delete hook to delete multiple stores
        await bulkDeleteStores(storesToDelete);

        // The hook will handle success notification and dialog closing
      } catch (error) {
        console.error("Error in confirmBulkDelete:", error);
        // The hook will handle error notifications
      }
    }
  };

  // Cancel bulk delete
  const cancelBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    setStoresToDelete([]);
  };

  return (
    <div className="w-full p-2 sm:p-4">
      <Card
        title={<span className="text-gray-800">Store Management</span>}
        className="w-full overflow-hidden"
        styles={{
          body: {
            padding: "12px",
            overflow: "hidden",
            backgroundColor: "#ffffff",
          },
          header: {
            padding: isMobile ? "12px 16px" : "16px 24px",
            backgroundColor: "#f5f5f5",
            borderColor: "#e8e8e8",
          },
        }}
        extra={
          <div className="flex space-x-2">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreatePanelOpen(true)}
              size={isMobile ? "small" : "middle"}
              className="bg-blue-600"
            >
              {isMobile ? "" : "Add Store"}
            </Button>
            {selectedStore && (
              <Button
                icon={<UserOutlined />}
                onClick={() => handleManageUsers(selectedStore)}
                className="bg-green-600 text-white"
                size={isMobile ? "small" : "middle"}
              >
                {isMobile ? "" : "Manage Users"}
              </Button>
            )}
          </div>
        }
      >
        <div className="w-full overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm">
          {/* Search Component - Always visible */}
          <StoresSearch
            value={search}
            onChange={setSearch}
            isMobile={isMobile}
          />

          {isLoading ? (
            <div className="flex h-60 items-center justify-center bg-gray-50">
              <Spin
                indicator={
                  <LoadingOutlined
                    style={{ fontSize: 24, color: "#1890ff" }}
                    spin
                  />
                }
              />
            </div>
          ) : (
            <>
              {/* Store Table Component */}
              {data?.data?.stores && data.data.stores.length > 0 ? (
                <div className="overflow-x-auto">
                  <StoresTable
                    search={search}
                    onEdit={handleEdit}
                    onView={handleView}
                    onSuccess={handleSuccess}
                    onSelect={setSelectedStore}
                    onBulkDelete={handleBulkDelete}
                    isMobile={isMobile}
                  />
                </div>
              ) : (
                <div className="flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800">
                  {search ? (
                    <>
                      <p>No stores found matching your search criteria.</p>
                      <Button
                        type="primary"
                        onClick={() => setSearch("")}
                        className="mt-4 bg-blue-600"
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <p>No stores found. Click &quot;Add Store&quot; to create one.</p>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </Card>

      {/* Create Store Panel */}
      <StoreFormPanel
        isOpen={isCreatePanelOpen}
        onClose={() => setIsCreatePanelOpen(false)}
        onSuccess={() => {
          setIsCreatePanelOpen(false);
          refetch();
        }}
        mode="create"
      />

      {/* Edit Store Panel */}
      <StoreFormPanel
        isOpen={isEditPanelOpen}
        onClose={() => setIsEditPanelOpen(false)}
        onSuccess={() => {
          setIsEditPanelOpen(false);
          refetch();
        }}
        editStore={selectedStore}
        mode="edit"
      />

      {/* View Store Panel */}
      <StoreDetailsPanel
        isOpen={isViewPanelOpen}
        onClose={() => setIsViewPanelOpen(false)}
        onEdit={handleEdit}
        storeId={selectedStore?.id}
      />

      {/* User Store Panel */}
      <UserStorePanel
        isOpen={isUserStorePanelOpen}
        onClose={() => setIsUserStorePanelOpen(false)}
        store={selectedStore}
        onSuccess={handleSuccess}
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isBulkDeleteDialogOpen}
        onClose={cancelBulkDelete}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Stores"
        message={`Are you sure you want to delete ${storesToDelete.length} stores? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        isLoading={isBulkDeleting}
        type="danger"
      />
    </div>
  );
};

export default StoresPage;
