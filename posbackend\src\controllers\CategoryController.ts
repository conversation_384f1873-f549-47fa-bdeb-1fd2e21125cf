import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategoryById,
  deleteCategoryById,
} from "../services/categoryService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleCategoryRequest = async (req: Request, res: Response): Promise<void> => {
  const { mode, categoryId, page, limit, ...data } = req.body;
  const requester = req.user as DecodedToken; // ✅ Extract `requester` from middleware

  // ✅ Validate mode
  const validModes = ["createnew", "update", "delete", "retrieve"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      // ✅ Create New Category
      case "createnew": {
        if (!data.name) {
          return sendResponse(res, 400, false, "Category name is required.");
        }

        const newCategory = await createCategory(requester, {
          name: data.name,
          description: data.description,
        });

        return sendResponse(res, 201, true, "Category created successfully.", newCategory);
      }

      // ✅ Retrieve Categories (Single or All)
      case "retrieve": {
        if (categoryId) {
          // Fetch a single category by ID
          const category = await getCategoryById(requester, Number(categoryId));
          return sendResponse(res, 200, true, "Category retrieved successfully.", category);
        } else {
          // Fetch all categories with pagination
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 10;
          const { search } = req.body;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          console.log("getAllCategories request:", {
            requester: { id: requester.id, role: requester.role },
            page: pageNum,
            limit: limitNum,
            search: search || 'none'
          });

          // Log the raw request body for debugging
          console.log("Raw category request body:", req.body);

          const categories = await getAllCategories(requester, pageNum, limitNum, search);
          return sendResponse(res, 200, true, "Categories retrieved successfully.", categories);
        }
      }

      // ✅ Update Category
      case "update": {
        if (!categoryId) {
          return sendResponse(res, 400, false, "Category ID is required for updating.");
        }

        const updatedCategory = await updateCategoryById(requester, Number(categoryId), data);
        return sendResponse(res, 200, true, "Category updated successfully.", updatedCategory);
      }

      // ✅ Delete Category (Single or Multiple)
      case "delete": {
        // Check if we have categoryIds (array) or categoryId (single)
        const { categoryIds } = req.body;

        if (!categoryId && !categoryIds) {
          return sendResponse(res, 400, false, "Category ID(s) are required for deletion.");
        }

        // Use categoryIds if provided, otherwise use single categoryId
        const idsToDelete = categoryIds
          ? (Array.isArray(categoryIds) ? categoryIds : [categoryIds])
          : [Number(categoryId)];

        const result = await deleteCategoryById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} categories deleted successfully.`
          : "Category deleted successfully.";

        return sendResponse(res, 200, true, message, result);
      }

      default:
        return sendResponse(res, 400, false, "Unexpected error occurred.");
    }
  } catch (error: any) {
    return sendResponse(res, 500, false, error.message || "Internal Server Error");
  }
};
