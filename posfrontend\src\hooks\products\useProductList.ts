"use client";

import { useState, useEffect } from "react";
import { useGetAllProductsQuery, Product } from "@/reduxRTK/services/productApi";
import { useDebounce } from "@/hooks/useDebounce";

export const useProductList = (initialPage = 1, initialLimit = 10) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [searchTerm, setSearchTerm] = useState('');
  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // Fetch products with pagination and search
  const {
    data,
    error,
    isLoading,
    refetch
  } = useGetAllProductsQuery({
    page,
    limit,
    search: debouncedSearchTerm
  }, {
    // Force refetch on mount and when arguments change
    refetchOnMountOrArgChange: true
  });

  // Extract products and pagination info from the response
  const products: Product[] = data?.data?.products || [];
  const total: number = data?.data?.total || 0;

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to page 1 when changing limit
  };

  // Function to force a refresh of the data
  const forceRefresh = () => {
    console.log("Forcing product list refresh");
    // Call refetch directly
    refetch();
  };

  return {
    products,
    total,
    page,
    limit,
    isLoading,
    error,
    refetch,
    forceRefresh, // Add the force refresh function
    searchTerm,
    setSearchTerm,
    handlePageChange,
    handleLimitChange
  };
};
