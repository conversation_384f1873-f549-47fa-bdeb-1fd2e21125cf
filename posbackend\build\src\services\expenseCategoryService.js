"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDefaultExpenseCategories = exports.deleteExpenseCategoryById = exports.updateExpenseCategoryById = exports.getExpenseCategoryById = exports.getAllExpenseCategories = exports.createExpenseCategory = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db/db");
const schema_1 = require("../db/schema");
const authorizeAction_1 = require("../utils/authorizeAction");
const schema_2 = require("../validation/schema");
/**
 * ✅ Create a new expense category
 */
const createExpenseCategory = async (requester, categoryData) => {
    // Validate category data
    schema_2.expenseCategorySchema.parse(categoryData);
    await (0, authorizeAction_1.authorizeAction)(requester, "create", "expense_categories");
    const createdBy = requester.id;
    // Check if category name already exists for this user
    const existingCategory = await db_1.postgresDb
        .select()
        .from(schema_1.expenseCategories)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.expenseCategories.name, categoryData.name), (0, drizzle_orm_1.eq)(schema_1.expenseCategories.createdBy, createdBy)))
        .limit(1);
    if (existingCategory.length > 0) {
        throw new Error("Category with this name already exists.");
    }
    // Insert new category
    const [newCategory] = await db_1.postgresDb
        .insert(schema_1.expenseCategories)
        .values({
        ...categoryData,
        createdBy,
    })
        .returning();
    return newCategory;
};
exports.createExpenseCategory = createExpenseCategory;
/**
 * ✅ Get all expense categories
 */
const getAllExpenseCategories = async (requester, page = 1, limit = 50, search = '') => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getAll", "expense_categories");
    // Build team member IDs for admin users
    let teamMemberIds = [];
    if (requester.role === "admin") {
        const teamMembersResult = await db_1.postgresDb
            .select({ memberId: schema_1.users.id })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.createdBy, requester.id));
        teamMemberIds = teamMembersResult.map((m) => m.memberId);
    }
    // Include the requester's ID in the team IDs, plus default categories
    const teamIds = requester.role === "admin"
        ? [requester.id, ...teamMemberIds]
        : [requester.id];
    // Get categories created by user/team OR default categories
    let whereConditions = (0, drizzle_orm_1.or)((0, drizzle_orm_1.inArray)(schema_1.expenseCategories.createdBy, teamIds), (0, drizzle_orm_1.eq)(schema_1.expenseCategories.isDefault, true));
    // Add search condition if provided
    if (search && search.trim()) {
        const searchTerm = `%${search.trim()}%`;
        whereConditions = (0, drizzle_orm_1.and)(whereConditions, (0, drizzle_orm_1.or)((0, drizzle_orm_1.sql) `${schema_1.expenseCategories.name} ILIKE ${searchTerm}`, (0, drizzle_orm_1.sql) `${schema_1.expenseCategories.description} ILIKE ${searchTerm}`));
    }
    // Get total count
    const totalCountResult = await db_1.postgresDb
        .select({ count: (0, drizzle_orm_1.count)() })
        .from(schema_1.expenseCategories)
        .where(whereConditions);
    const totalCount = totalCountResult[0]?.count || 0;
    // Get categories
    const categoriesResult = await db_1.postgresDb
        .select()
        .from(schema_1.expenseCategories)
        .where(whereConditions)
        .orderBy((0, drizzle_orm_1.desc)(schema_1.expenseCategories.isDefault), schema_1.expenseCategories.name)
        .limit(limit)
        .offset((page - 1) * limit);
    return {
        categories: categoriesResult,
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
    };
};
exports.getAllExpenseCategories = getAllExpenseCategories;
/**
 * ✅ Get expense category by ID
 */
const getExpenseCategoryById = async (requester, categoryId) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "getById", "expense_categories", categoryId);
    const categoryResult = await db_1.postgresDb
        .select()
        .from(schema_1.expenseCategories)
        .where((0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, categoryId))
        .limit(1);
    if (categoryResult.length === 0) {
        throw new Error("Expense category not found.");
    }
    return categoryResult[0];
};
exports.getExpenseCategoryById = getExpenseCategoryById;
/**
 * ✅ Update expense category by ID
 */
const updateExpenseCategoryById = async (requester, categoryId, updateData) => {
    await (0, authorizeAction_1.authorizeAction)(requester, "update", "expense_categories", categoryId);
    // Check if it's a default category (cannot be updated)
    const category = await db_1.postgresDb
        .select()
        .from(schema_1.expenseCategories)
        .where((0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, categoryId))
        .limit(1);
    if (category.length === 0) {
        throw new Error("Category not found.");
    }
    if (category[0].isDefault) {
        throw new Error("Default categories cannot be modified.");
    }
    // Check if new name conflicts with existing categories
    if (updateData.name) {
        const existingCategory = await db_1.postgresDb
            .select()
            .from(schema_1.expenseCategories)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.expenseCategories.name, updateData.name), (0, drizzle_orm_1.eq)(schema_1.expenseCategories.createdBy, requester.id), (0, drizzle_orm_1.sql) `${schema_1.expenseCategories.id} != ${categoryId}`))
            .limit(1);
        if (existingCategory.length > 0) {
            throw new Error("Category with this name already exists.");
        }
    }
    const [updatedCategory] = await db_1.postgresDb
        .update(schema_1.expenseCategories)
        .set(updateData)
        .where((0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, categoryId))
        .returning();
    if (!updatedCategory) {
        throw new Error("Category not found or update failed.");
    }
    return updatedCategory;
};
exports.updateExpenseCategoryById = updateExpenseCategoryById;
/**
 * ✅ Delete expense category(s) by ID(s)
 */
const deleteExpenseCategoryById = async (requester, categoryIds) => {
    // Validate each category before deletion
    for (const categoryId of categoryIds) {
        await (0, authorizeAction_1.authorizeAction)(requester, "delete", "expense_categories", categoryId);
        // Check if it's a default category (cannot be deleted)
        const category = await db_1.postgresDb
            .select()
            .from(schema_1.expenseCategories)
            .where((0, drizzle_orm_1.eq)(schema_1.expenseCategories.id, categoryId))
            .limit(1);
        if (category.length > 0 && category[0].isDefault) {
            throw new Error(`Default category "${category[0].name}" cannot be deleted.`);
        }
    }
    const deletedCategories = await db_1.postgresDb
        .delete(schema_1.expenseCategories)
        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.inArray)(schema_1.expenseCategories.id, categoryIds), (0, drizzle_orm_1.eq)(schema_1.expenseCategories.isDefault, false) // Extra safety check
    ))
        .returning({ id: schema_1.expenseCategories.id, name: schema_1.expenseCategories.name });
    if (deletedCategories.length === 0) {
        throw new Error("No categories found for deletion or all are default categories.");
    }
    return {
        deletedCount: deletedCategories.length,
        deletedCategories,
    };
};
exports.deleteExpenseCategoryById = deleteExpenseCategoryById;
/**
 * ✅ Initialize default expense categories for a new user
 */
const initializeDefaultExpenseCategories = async (userId) => {
    const defaultCategories = [
        { name: "Office Supplies", description: "Stationery, equipment, and office materials", color: "#3B82F6", isDefault: true },
        { name: "Utilities", description: "Electricity, water, internet, phone bills", color: "#EF4444", isDefault: true },
        { name: "Rent", description: "Office or store rent payments", color: "#8B5CF6", isDefault: true },
        { name: "Marketing", description: "Advertising, promotions, and marketing expenses", color: "#10B981", isDefault: true },
        { name: "Transportation", description: "Fuel, vehicle maintenance, delivery costs", color: "#F59E0B", isDefault: true },
        { name: "Professional Services", description: "Legal, accounting, consulting fees", color: "#6366F1", isDefault: true },
        { name: "Insurance", description: "Business insurance premiums", color: "#EC4899", isDefault: true },
        { name: "Maintenance", description: "Equipment and facility maintenance", color: "#14B8A6", isDefault: true },
        { name: "Training", description: "Staff training and development", color: "#F97316", isDefault: true },
        { name: "Miscellaneous", description: "Other business expenses", color: "#6B7280", isDefault: true },
    ];
    try {
        await db_1.postgresDb
            .insert(schema_1.expenseCategories)
            .values(defaultCategories.map(cat => ({
            ...cat,
            createdBy: userId,
        })));
    }
    catch (error) {
        // If categories already exist, ignore the error
        console.log("Default expense categories may already exist for user:", userId);
    }
};
exports.initializeDefaultExpenseCategories = initializeDefaultExpenseCategories;
