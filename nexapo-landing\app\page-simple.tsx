'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMenuOpen(false)
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-lg py-2' : 'bg-transparent py-4'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🚀</span>
              <span className={`text-2xl font-bold ${
                isScrolled ? 'text-gray-900' : 'text-white'
              }`}>
                NEXAPO
              </span>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => scrollToSection('features')}
                className={`font-medium transition-colors ${
                  isScrolled ? 'text-gray-700 hover:text-blue-600' : 'text-white hover:text-yellow-400'
                }`}
              >
                Features
              </button>
              <button 
                onClick={() => scrollToSection('pricing')}
                className={`font-medium transition-colors ${
                  isScrolled ? 'text-gray-700 hover:text-blue-600' : 'text-white hover:text-yellow-400'
                }`}
              >
                Pricing
              </button>
              <button 
                onClick={() => scrollToSection('testimonials')}
                className={`font-medium transition-colors ${
                  isScrolled ? 'text-gray-700 hover:text-blue-600' : 'text-white hover:text-yellow-400'
                }`}
              >
                Reviews
              </button>
              <button 
                onClick={() => scrollToSection('contact')}
                className={`font-medium transition-colors ${
                  isScrolled ? 'text-gray-700 hover:text-blue-600' : 'text-white hover:text-yellow-400'
                }`}
              >
                Contact
              </button>
              <button 
                onClick={() => scrollToSection('demo')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300"
              >
                Free Demo
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`md:hidden text-2xl ${isScrolled ? 'text-gray-900' : 'text-white'}`}
            >
              {isMenuOpen ? '✕' : '☰'}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden mt-4 bg-white rounded-lg shadow-lg p-4">
              <div className="flex flex-col space-y-4">
                <button 
                  onClick={() => scrollToSection('features')}
                  className="text-gray-700 hover:text-blue-600 font-medium text-left"
                >
                  Features
                </button>
                <button 
                  onClick={() => scrollToSection('pricing')}
                  className="text-gray-700 hover:text-blue-600 font-medium text-left"
                >
                  Pricing
                </button>
                <button 
                  onClick={() => scrollToSection('testimonials')}
                  className="text-gray-700 hover:text-blue-600 font-medium text-left"
                >
                  Reviews
                </button>
                <button 
                  onClick={() => scrollToSection('contact')}
                  className="text-gray-700 hover:text-blue-600 font-medium text-left"
                >
                  Contact
                </button>
                <button 
                  onClick={() => scrollToSection('demo')}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg w-full"
                >
                  Free Demo
                </button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-white">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                Transform Your
                <span className="block text-yellow-400">Business Today!</span>
              </h1>
              
              <p className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">
                NEXAPO is Ghana's #1 POS system. Process sales 10x faster, track inventory in real-time, 
                and generate professional reports instantly.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button 
                  onClick={() => scrollToSection('demo')}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold text-lg px-8 py-4 rounded-lg transition-all duration-300 flex items-center justify-center gap-2"
                >
                  🚀 Start 30-Day FREE Trial
                </button>
                <button 
                  onClick={() => scrollToSection('features')}
                  className="bg-white hover:bg-gray-50 text-blue-600 font-semibold text-lg px-8 py-4 rounded-lg border-2 border-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  → See How It Works
                </button>
              </div>

              <div className="flex items-center gap-8 text-blue-100">
                <div className="flex items-center gap-2">
                  <span className="text-yellow-400">✓</span>
                  <span>No Setup Fees</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-yellow-400">✓</span>
                  <span>No Credit Card Required</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-yellow-400">✓</span>
                  <span>Cancel Anytime</span>
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image */}
            <div className="relative">
              <div className="relative bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <Image
                  src="https://via.placeholder.com/600x400/3b82f6/ffffff?text=NEXAPO+Dashboard+Screenshot"
                  alt="NEXAPO POS System Dashboard"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg"
                  priority
                />
                <div className="absolute -top-4 -right-4 bg-yellow-500 text-white px-4 py-2 rounded-full font-bold text-sm">
                  Live Demo
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-8 -left-8 bg-white rounded-lg shadow-lg p-4">
                <div className="flex items-center gap-2">
                  <span className="text-green-500 text-xl">📈</span>
                  <div>
                    <div className="text-sm font-semibold text-gray-900">Sales Today</div>
                    <div className="text-lg font-bold text-green-500">₵2,450</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-8 -right-8 bg-white rounded-lg shadow-lg p-4">
                <div className="flex items-center gap-2">
                  <span className="text-blue-500 text-xl">🛒</span>
                  <div>
                    <div className="text-sm font-semibold text-gray-900">Orders</div>
                    <div className="text-lg font-bold text-blue-500">127</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white">
          <div className="flex flex-col items-center gap-2">
            <span className="text-sm">Scroll to explore</span>
            <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white rounded-full mt-2"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-600 mb-8">Trusted by 1000+ businesses across Ghana</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
              <div className="flex flex-col items-center">
                <div className="text-3xl font-bold text-blue-600">1000+</div>
                <div className="text-gray-600">Happy Customers</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="text-3xl font-bold text-blue-600">99.9%</div>
                <div className="text-gray-600">Uptime</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="text-3xl font-bold text-blue-600">24/7</div>
                <div className="text-gray-600">Support</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="text-3xl font-bold text-blue-600">4.9★</div>
                <div className="text-gray-600">Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Simple Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-6">
              <span className="text-2xl">🚀</span>
              <span className="text-2xl font-bold">NEXAPO</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
              Ghana's leading POS system helping businesses grow faster with smart technology 
              and exceptional support.
            </p>
            <div className="flex justify-center space-x-6 mb-8">
              <span className="text-gray-400">📞 +233 XX XXX XXXX</span>
              <span className="text-gray-400">📧 <EMAIL></span>
              <span className="text-gray-400">🌐 www.nexapo.com</span>
            </div>
            <div className="border-t border-gray-800 pt-8">
              <div className="text-gray-400 text-sm">
                © 2024 NEXAPO. All rights reserved.
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
