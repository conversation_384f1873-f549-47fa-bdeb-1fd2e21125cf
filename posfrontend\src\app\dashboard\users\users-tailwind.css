/* Custom styles for the users table */

/* Make individual cells scrollable */
.overflow-hidden .max-w-\[120px\],
.overflow-hidden .max-w-\[140px\],
.overflow-hidden .max-w-\[150px\],
.overflow-hidden .max-w-\[200px\],
.overflow-hidden .max-w-\[100px\],
.overflow-hidden .max-w-\[130px\] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
}

/* Show scrollbar on hover */
.overflow-hidden .max-w-\[120px\]:hover,
.overflow-hidden .max-w-\[140px\]:hover,
.overflow-hidden .max-w-\[150px\]:hover,
.overflow-hidden .max-w-\[200px\]:hover,
.overflow-hidden .max-w-\[100px\]:hover,
.overflow-hidden .max-w-\[130px\]:hover {
  overflow: auto !important;
  text-overflow: clip;
  z-index: 10;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  cursor: text;
}

/* Style for scrollbar */
.overflow-hidden div::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

.overflow-hidden div::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
}

.overflow-hidden div::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.sticky.left-0 {
  position: sticky;
  left: 0;
  z-index: 10;
}

.sticky.right-0 {
  position: sticky;
  right: 0;
  z-index: 10;
}

/* Fix for hover state */
tr.hover\:bg-\[#1a2234\]:hover td {
  background-color: #f5f5f5;
}

/* Fix for sticky columns on hover */
tr.hover\:bg-\[#1a2234\]:hover td.sticky {
  background-color: #f5f5f5 !important;
}

/* Mobile specific styles */
@media (max-width: 640px) {
  /* Ensure the table doesn't affect the layout */
  .overflow-hidden {
    -webkit-overflow-scrolling: touch;
  }

  /* Smaller padding for mobile */
  .px-3.py-4 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Ensure sticky columns work on mobile */
  .sticky.left-0,
  .sticky.right-0 {
    background-color: #ffffff;
  }

  /* Fix for hover state on mobile */
  tr.hover\:bg-\[#1a2234\]:hover td.sticky {
    background-color: #f5f5f5 !important;
  }
}
