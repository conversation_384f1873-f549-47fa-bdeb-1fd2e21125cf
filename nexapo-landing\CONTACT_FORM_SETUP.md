# 📧 NEXAPO Contact Form Setup Guide

## ✅ **What's Been Implemented**

Your NEXAPO contact form is now fully functional with:

### **Frontend Features:**
- ✅ React Hook Form validation
- ✅ Real-time error messages
- ✅ Loading states during submission
- ✅ Success/error feedback
- ✅ Professional form styling
- ✅ Ghana phone number validation
- ✅ Email format validation

### **Backend Features:**
- ✅ Next.js API route (`/api/contact`)
- ✅ Server-side validation
- ✅ Professional email templates
- ✅ Auto-reply to customers
- ✅ Admin notifications
- ✅ Error handling

---

## 🔧 **Required Setup (5 minutes)**

### **Step 1: Gmail App Password Setup**

1. **Go to your Gmail account settings:**
   - Visit: https://myaccount.google.com/security

2. **Enable 2-Step Verification:**
   - If not already enabled, turn on 2-Step Verification

3. **Generate App Password:**
   - Go to "App passwords" section
   - Select "Mail" as the app
   - Generate a 16-character password
   - Copy this password

### **Step 2: Update Environment Variables**

Edit the `.env.local` file in your project root:

```env
# Replace with your actual Gmail credentials
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-16-character-app-password

# Where contact form submissions will be sent
ADMIN_EMAIL=<EMAIL>
```

**Example:**
```env
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=abcd efgh ijkl mnop
ADMIN_EMAIL=<EMAIL>
```

### **Step 3: Test the Form**

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Navigate to the contact page:**
   ```
   http://localhost:3000/contact
   ```

3. **Fill out and submit the form**

4. **Check your email for:**
   - Admin notification (to ADMIN_EMAIL)
   - Customer auto-reply (to form submitter)

---

## 📧 **Email Templates**

### **Admin Notification Email:**
- Professional HTML template
- All form data clearly displayed
- Quick action links (reply, call)
- Branded NEXAPO styling

### **Customer Auto-Reply Email:**
- Welcome message
- Submission confirmation
- Contact information
- Special offers for early adopters
- Professional NEXAPO branding

---

## 🛡️ **Security Features**

### **Validation:**
- ✅ Required field validation
- ✅ Email format validation
- ✅ Ghana phone number format
- ✅ Message length validation
- ✅ Server-side validation
- ✅ Input sanitization

### **Error Handling:**
- ✅ Network error handling
- ✅ Server error handling
- ✅ User-friendly error messages
- ✅ Form state management

---

## 🎯 **Form Features**

### **Required Fields:**
- First Name (min 2 characters)
- Last Name (min 2 characters)
- Email (valid format)
- Subject (dropdown selection)
- Message (min 10 characters)

### **Optional Fields:**
- Phone Number (Ghana format validation)
- Business Name
- Newsletter subscription

### **Subject Options:**
- Request Personalized Demo
- Business Consultation
- Pricing Information
- Setup & Training
- Partnership Opportunity
- Technical Support
- General Inquiry

---

## 🚀 **Production Deployment**

### **Environment Variables for Production:**
```env
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-production-app-password
ADMIN_EMAIL=<EMAIL>
```

### **Recommended Production Setup:**
1. Use a dedicated Gmail account for NEXAPO
2. Set up email forwarding to your main business email
3. Monitor email delivery and response times
4. Consider adding email analytics

---

## 📊 **Monitoring & Analytics**

### **What to Monitor:**
- Form submission success rate
- Email delivery status
- Response times to inquiries
- Most common inquiry types

### **Recommended Tools:**
- Google Analytics for form tracking
- Email delivery monitoring
- Customer response tracking

---

## 🔧 **Troubleshooting**

### **Common Issues:**

**1. Emails not sending:**
- Check Gmail App Password is correct
- Verify 2-Step Verification is enabled
- Check environment variables are set

**2. Form validation errors:**
- Check React Hook Form setup
- Verify validation rules
- Check error message display

**3. Network errors:**
- Check API route is working
- Verify server is running
- Check browser console for errors

### **Testing Commands:**
```bash
# Test form submission
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{"firstName":"Test","lastName":"User","email":"<EMAIL>","subject":"demo","message":"Test message"}'
```

---

## ✨ **Success! Your Contact Form is Ready**

Your NEXAPO contact form is now:
- ✅ Fully functional
- ✅ Professionally styled
- ✅ Properly validated
- ✅ Email-enabled
- ✅ Production-ready

**Next Steps:**
1. Set up your Gmail App Password
2. Update environment variables
3. Test the form
4. Deploy to production
5. Start receiving customer inquiries!

---

**Need Help?** The form includes comprehensive error handling and user feedback to guide users through any issues.
