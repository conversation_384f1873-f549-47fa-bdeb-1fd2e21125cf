"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenseCategories = exports.expenses = exports.userStores = exports.stores = exports.payments = exports.userCategories = exports.userSuppliers = exports.suppliers = exports.purchases = exports.stockAdjustments = exports.salesItems = exports.sales = exports.receipts = exports.products = exports.categories = exports.users = void 0;
const schema_1 = require("./schema");
Object.defineProperty(exports, "users", { enumerable: true, get: function () { return schema_1.users; } });
Object.defineProperty(exports, "categories", { enumerable: true, get: function () { return schema_1.categories; } });
Object.defineProperty(exports, "products", { enumerable: true, get: function () { return schema_1.products; } });
Object.defineProperty(exports, "receipts", { enumerable: true, get: function () { return schema_1.receipts; } });
Object.defineProperty(exports, "sales", { enumerable: true, get: function () { return schema_1.sales; } });
Object.defineProperty(exports, "salesItems", { enumerable: true, get: function () { return schema_1.salesItems; } });
Object.defineProperty(exports, "stockAdjustments", { enumerable: true, get: function () { return schema_1.stockAdjustments; } });
Object.defineProperty(exports, "purchases", { enumerable: true, get: function () { return schema_1.purchases; } });
Object.defineProperty(exports, "suppliers", { enumerable: true, get: function () { return schema_1.suppliers; } });
Object.defineProperty(exports, "userSuppliers", { enumerable: true, get: function () { return schema_1.userSuppliers; } });
Object.defineProperty(exports, "userCategories", { enumerable: true, get: function () { return schema_1.userCategories; } });
Object.defineProperty(exports, "payments", { enumerable: true, get: function () { return schema_1.payments; } });
Object.defineProperty(exports, "stores", { enumerable: true, get: function () { return schema_1.stores; } });
Object.defineProperty(exports, "userStores", { enumerable: true, get: function () { return schema_1.userStores; } });
Object.defineProperty(exports, "expenses", { enumerable: true, get: function () { return schema_1.expenses; } });
Object.defineProperty(exports, "expenseCategories", { enumerable: true, get: function () { return schema_1.expenseCategories; } });
