"use client";
import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { Badge, Card, Button, Spin, Alert, message } from "antd";
import { ClockCircleOutlined, CheckCircleOutlined, WarningOutlined, StopOutlined, ReloadOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import { useCheckPaymentStatus } from "@/hooks/useCheckPaymentStatus";

import { refreshUserDataAfterPayment } from "@/utils/refreshUserData";

interface PaymentStatusProps {
  showPayButton?: boolean;
}

const PaymentStatus: React.FC<PaymentStatusProps> = ({ showPayButton = true }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const dispatch = useDispatch();
  const router = useRouter();
  const { needsPayment, status, daysRemaining } = useCheckPaymentStatus();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Calculate subscription plan based on date difference (NEW PRICING)
  const getSubscriptionPlan = () => {
    if (!user?.lastPaymentDate || !user?.nextPaymentDue) {
      return "Monthly (₵40/month)";
    }

    const lastPayment = dayjs(user.lastPaymentDate);
    const nextDue = dayjs(user.nextPaymentDue);
    const daysDiff = nextDue.diff(lastPayment, 'day');
    const monthsDiff = nextDue.diff(lastPayment, 'month', true);


    // More precise calculation using both days and months (NEW PRICING)
    if (daysDiff >= 350 || monthsDiff >= 11) {
      console.log('✅ Detected: Annual subscription');
      return "Annual (₵360/year)";
    }
    if (daysDiff >= 80 || monthsDiff >= 2.5) {
      console.log('✅ Detected: Quarterly subscription');
      return "Quarterly (₵108/3 months)";
    }

    console.log('✅ Detected: Monthly subscription');
    return "Monthly (₵40/month)";
  };

  if (!user) {
    return (
      <Alert
        message="Authentication Required"
        description="Please log in to view your payment status."
        type="warning"
        showIcon
      />
    );
  }

  const getStatusIcon = () => {
    switch (user.paymentStatus) {
      case "paid":
        return <CheckCircleOutlined className="text-green-500 text-xl" />;
      case "pending":
        return <ClockCircleOutlined className="text-yellow-500 text-xl" />;
      case "overdue":
        return <WarningOutlined className="text-red-500 text-xl" />;
      case "inactive":
        return <StopOutlined className="text-gray-500 text-xl" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (user.paymentStatus) {
      case "paid":
        return "success";
      case "pending":
        return "warning";
      case "overdue":
        return "error";
      case "inactive":
        return "default";
      default:
        return "default";
    }
  };

  const getStatusText = () => {
    switch (user.paymentStatus) {
      case "paid":
        return "Your subscription is active";
      case "pending":
        return "Your payment is pending verification";
      case "overdue":
        return "Your payment is overdue";
      case "inactive":
        return "Your subscription is inactive";
      default:
        return "Unknown status";
    }
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    return dayjs(dateString).format("MMM D, YYYY");
  };

  const handleMakePayment = () => {
    router.push("/payment");
  };

  const handleRefreshStatus = async () => {
    if (!user || !accessToken) {
      message.error("Unable to refresh: User not authenticated");
      return;
    }

    setIsRefreshing(true);
    console.log('🔄 Manual payment status refresh triggered');

    try {
      const refreshResult = await refreshUserDataAfterPayment({
        dispatch,
        accessToken,
        currentUser: user,
        maxRetries: 3,
        retryDelay: 1000
      });

      if (refreshResult.success) {
        message.success("Payment status refreshed successfully! Reloading page...");
        console.log('✅ Manual refresh successful');

        // Force page reload to ensure fresh data
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        message.warning("Refresh completed but status may not have changed");
        console.log('⚠️ Manual refresh completed with warnings');
      }
    } catch (error: any) {
      console.error('❌ Manual refresh failed:', error);
      message.error("Failed to refresh payment status. Please try again.");
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Card
      title={
        <div className="flex items-center justify-between pl-2">
          <div className="flex items-center">
            {getStatusIcon()}
            <span className="ml-2 text-gray-800">Subscription Status</span>
          </div>
          <Button
            type="text"
            icon={<ReloadOutlined spin={isRefreshing} />}
            onClick={handleRefreshStatus}
            loading={isRefreshing}
            size="small"
            className="text-gray-600 hover:text-blue-600"
            title="Refresh payment status"
          />
        </div>
      }
      className="w-full shadow-md bg-white border border-gray-200"
      styles={{
        header: { backgroundColor: '#f5f5f5', borderColor: '#e8e8e8' },
        body: { backgroundColor: '#ffffff' }
      }}
    >
      <div className="flex flex-col space-y-4 px-2">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Status:</span>
          <Badge status={getStatusColor() as any} text={user.paymentStatus.toUpperCase()} />
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Plan:</span>
          <span className="text-gray-800">{getSubscriptionPlan()}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Last Payment:</span>
          <span className="text-gray-800">{formatDate(user.lastPaymentDate)}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Next Payment Due:</span>
          <div className="text-right">
            <div className="text-gray-800">{formatDate(user.nextPaymentDue)}</div>
            {user.nextPaymentDue && user.paymentStatus === 'paid' && (
              <div className="text-xs text-green-600">
                {(() => {
                  const daysLeft = dayjs(user.nextPaymentDue).diff(dayjs(), 'day');
                  if (daysLeft > 0) return `${daysLeft} days remaining`;
                  if (daysLeft === 0) return 'Due today';
                  return `${Math.abs(daysLeft)} days overdue`;
                })()}
              </div>
            )}
          </div>
        </div>

        <Alert
          message={getStatusText()}
          type={getStatusColor() as any}
          showIcon
          className="mt-2"
        />

        {showPayButton && needsPayment && (
          <Button
            type="primary"
            onClick={handleMakePayment}
            className="w-full mt-4 bg-blue-600 hover:bg-blue-700 h-10 font-medium"
          >
            Manage Subscription
          </Button>
        )}
      </div>
    </Card>
  );
};

export default PaymentStatus;
