import { useBulkDeleteSuppliersMutation } from "@/reduxRTK/services/supplierApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useSupplierBulkDelete = (onSuccess?: () => void) => {
  // RTK Query hook for bulk deleting suppliers
  const [bulkDeleteSuppliers, { isLoading }] = useBulkDeleteSuppliersMutation();

  const deleteSuppliers = async (supplierIds: number[]) => {
    try {
      console.log("Bulk deleting suppliers with IDs:", supplierIds);
      
      const result = await bulkDeleteSuppliers(supplierIds).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete suppliers");
      }

      showMessage("success", `${supplierIds.length} suppliers deleted successfully`);
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Bulk delete suppliers error:", error);
      showMessage("error", error.message || "Failed to delete suppliers");
      throw error;
    }
  };

  return {
    bulkDeleteSuppliers: deleteSuppliers,
    isDeleting: isLoading
  };
};
