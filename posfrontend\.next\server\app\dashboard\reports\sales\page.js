(()=>{var e={};e.id=1518,e.ids=[1518],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},56372:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>i});var a=s(70260),r=s(28203),l=s(25155),n=s.n(l),o=s(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let i=["",{children:["dashboard",{children:["reports",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,24577)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18606)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/reports/sales/page",pathname:"/dashboard/reports/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},30102:(e,t,s)=>{Promise.resolve().then(s.bind(s,24577))},93310:(e,t,s)=>{Promise.resolve().then(s.bind(s,48085))},81045:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(11855),r=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var n=s(78480);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},58733:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(11855),r=s(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"};var n=s(78480);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},1387:(e,t,s)=>{"use strict";s.d(t,{A:()=>w});var a=s(58009),r=s(85303),l=s(2866),n=s(56073),o=s.n(n),d=s(90365),i=s(27343),c=s(31716);let m=e=>{let t;let{value:s,formatter:r,precision:l,decimalSeparator:n,groupSeparator:o="",prefixCls:d}=e;if("function"==typeof r)t=r(s);else{let e=String(s),r=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(r&&"-"!==e){let e=r[1],s=r[2]||"0",i=r[4]||"";s=s.replace(/\B(?=(\d{3})+(?!\d))/g,o),"number"==typeof l&&(i=i.padEnd(l,"0").slice(0,l>0?l:0)),i&&(i=`${n}${i}`),t=[a.createElement("span",{key:"int",className:`${d}-content-value-int`},e,s),i&&a.createElement("span",{key:"decimal",className:`${d}-content-value-decimal`},i)]}else t=e}return a.createElement("span",{className:`${d}-content-value`},t)};var x=s(47285),p=s(13662),h=s(10941);let u=e=>{let{componentCls:t,marginXXS:s,padding:a,colorTextDescription:r,titleFontSize:l,colorTextHeading:n,contentFontSize:o,fontFamily:d}=e;return{[t]:Object.assign(Object.assign({},(0,x.dF)(e)),{[`${t}-title`]:{marginBottom:s,color:r,fontSize:l},[`${t}-skeleton`]:{paddingTop:a},[`${t}-content`]:{color:n,fontSize:o,fontFamily:d,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:s},[`${t}-content-suffix`]:{marginInlineStart:s}}})}},f=(0,p.OF)("Statistic",e=>[u((0,h.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:s}=e;return{titleFontSize:s,contentFontSize:t}});var y=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(s[a[r]]=e[a[r]]);return s};let b=e=>{let{prefixCls:t,className:s,rootClassName:r,style:l,valueStyle:n,value:x=0,title:p,valueRender:h,prefix:u,suffix:b,loading:g=!1,formatter:j,precision:v,decimalSeparator:N=".",groupSeparator:w=",",onMouseEnter:A,onMouseLeave:S}=e,M=y(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:P,direction:O,className:C,style:E}=(0,i.TP)("statistic"),D=P("statistic",t),[Y,$,R]=f(D),k=a.createElement(m,{decimalSeparator:N,groupSeparator:w,prefixCls:D,formatter:j,precision:v,value:x}),F=o()(D,{[`${D}-rtl`]:"rtl"===O},C,s,r,$,R),T=(0,d.A)(M,{aria:!0,data:!0});return Y(a.createElement("div",Object.assign({},T,{className:F,style:Object.assign(Object.assign({},E),l),onMouseEnter:A,onMouseLeave:S}),p&&a.createElement("div",{className:`${D}-title`},p),a.createElement(c.A,{paragraph:!1,loading:g,className:`${D}-skeleton`},a.createElement("div",{style:n,className:`${D}-content`},u&&a.createElement("span",{className:`${D}-content-prefix`},u),h?h(k):k,b&&a.createElement("span",{className:`${D}-content-suffix`},b)))))},g=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var j=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(s[a[r]]=e[a[r]]);return s};let v=1e3/30,N=a.memo(e=>{let{value:t,format:s="HH:mm:ss",onChange:n,onFinish:o}=e,d=j(e,["value","format","onChange","onFinish"]),i=(0,r.A)(),c=a.useRef(null),m=()=>{null==o||o(),c.current&&(clearInterval(c.current),c.current=null)},x=()=>{let e=new Date(t).getTime();e>=Date.now()&&(c.current=setInterval(()=>{i(),null==n||n(e-Date.now()),e<Date.now()&&m()},v))};return a.useEffect(()=>(x(),()=>{c.current&&(clearInterval(c.current),c.current=null)}),[t]),a.createElement(b,Object.assign({},d,{value:t,valueRender:e=>(0,l.Ob)(e,{title:void 0}),formatter:(e,t)=>(function(e,t){let{format:s=""}=t;return function(e,t){let s=e,a=/\[[^\]]*]/g,r=(t.match(a)||[]).map(e=>e.slice(1,-1)),l=t.replace(a,"[]"),n=g.reduce((e,t)=>{let[a,r]=t;if(e.includes(a)){let t=Math.floor(s/r);return s-=t*r,e.replace(RegExp(`${a}+`,"g"),e=>{let s=e.length;return t.toString().padStart(s,"0")})}return e},l),o=0;return n.replace(a,()=>{let e=r[o];return o+=1,e})}(Math.max(new Date(e).getTime()-Date.now(),0),s)})(e,Object.assign(Object.assign({},t),{format:s}))}))});b.Countdown=N;let w=b},48085:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(45512),r=s(58009),l=s(55735),n=s(7325),o=s(53950),d=s(6987),i=s(3117),c=s(1236),m=s(9170),x=s(1387),p=s(21419),h=s(66157),u=s(91054),f=s(27069),y=s(81045),b=s(88752),g=s(60636),j=s(765),v=s(42391),N=s(10499),w=s(2274),A=s(16589),S=s.n(A),M=s(41561),P=s(5607);let{RangePicker:O}=l.A,{Option:C}=n.A,E=()=>{let{user:e}=(0,g.A)(),t=(0,j.a)(),[s,l]=(0,r.useState)([S()().subtract(30,"days"),S()()]),[A,E]=(0,r.useState)("summary"),[D,Y]=(0,r.useState)(!0),{data:$,isLoading:R}=(0,v.P)(),{data:k,isLoading:F,refetch:T}=(0,N.JZ)({page:1,limit:1e3,search:""}),{data:z,isLoading:q}=(0,w.r3)({page:1,limit:1e3,search:""}),_=R||F||q,B=(0,r.useMemo)(()=>{let e=k?.data?.sales||[],t=z?.data?.products||[];$?.data;let a=s?e.filter(e=>{let t=S()(e.transactionDate);return t.isAfter(s[0].startOf("day"))&&t.isBefore(s[1].endOf("day"))}):e,r=a.reduce((e,t)=>e+parseFloat(t.totalAmount),0),l=a.length,n={};a.forEach(e=>{e.items&&e.items.length>0&&e.items.forEach(e=>{let t=e.productId.toString();n[t]||(n[t]={quantity:0,revenue:0,name:e.productName||`Product ${e.productId}`}),n[t].quantity+=e.quantity,n[t].revenue+=parseFloat(e.price)*e.quantity})});let o=Object.entries(n).sort(([,e],[,t])=>t.quantity-e.quantity)[0],d="No sales data";o?d=o[1].name:t.length>0&&(d=`${t[0].name} (No sales yet)`);let i={};a.forEach(e=>{let t=e.createdBy.toString(),s=e.createdByName||`User ${e.createdBy}`;i[t]||(i[t]={transactions:0,revenue:0,name:s}),i[t].transactions+=1,i[t].revenue+=parseFloat(e.totalAmount)});let c={};return a.forEach(e=>{let t=e.paymentMethod;c[t]||(c[t]={count:0,revenue:0}),c[t].count+=1,c[t].revenue+=parseFloat(e.totalAmount)}),{totalSales:r,totalTransactions:l,averageOrderValue:l>0?r/l:0,topSellingProduct:d,productPerformance:Object.entries(n).map(([e,t])=>({productId:parseInt(e),productName:t.name,quantity:t.quantity,revenue:t.revenue})).sort((e,t)=>t.revenue-e.revenue).slice(0,10),customerAnalysis:Object.entries(i).map(([e,t])=>({cashierId:parseInt(e),cashierName:t.name,transactions:t.transactions,revenue:t.revenue,averageOrderValue:t.transactions>0?t.revenue/t.transactions:0})).sort((e,t)=>t.revenue-e.revenue),paymentMethods:Object.entries(c).map(([e,t])=>({method:e,count:t.count,revenue:t.revenue,percentage:l>0?t.count/l*100:0})).sort((e,t)=>t.revenue-e.revenue)}},[k,z,s,$,D,A]),I=(k?.data?.sales||[]).filter(e=>{if(!s)return!0;let t=S()(e.transactionDate);return t.isAfter(s[0].startOf("day"))&&t.isBefore(s[1].endOf("day"))}),H=I.reduce((e,t)=>e+parseFloat(t.totalAmount),0);return(0,a.jsx)("div",{className:"w-full p-2 sm:p-4",children:(0,a.jsx)(d.A,{title:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"text-blue-600"}),(0,a.jsx)("span",{className:"text-gray-800",children:"Sales Reports"})]}),className:"w-full overflow-hidden",styles:{body:{padding:"12px",overflow:"hidden",backgroundColor:"#ffffff"},header:{padding:t?"12px 16px":"16px 24px",backgroundColor:"#f5f5f5",borderColor:"#e8e8e8"}},extra:(0,a.jsxs)("div",{className:t?"flex flex-col gap-2":"flex flex-row gap-2 items-center",children:[(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(u.A,{}),onClick:()=>{let e=window.open("","_blank");if(!e){o.Ay.error("Please allow popups to print sales report");return}let t=`
      <html>
        <head>
          <title>Sales Report</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .header { text-align: center; margin-bottom: 20px; }
            .date { color: #666; font-size: 0.9em; }
            @media print { .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Sales Report</h2>
            <p class="date">Generated on: ${S()().format("MMMM D, YYYY h:mm A")}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Month</th>
                <th>Amount</th>
                <th>Payment Method</th>
                <th>Cashier</th>
              </tr>
            </thead>
            <tbody>
              ${I.map(e=>`
                <tr>
                  <td>${S()(e.transactionDate).format("YYYY-MM-DD HH:mm")}</td>
                  <td>${S()(e.transactionDate).format("MMMM YYYY")}</td>
                  <td>₵${parseFloat(e.totalAmount).toFixed(2)}</td>
                  <td>${e.paymentMethod.replace("_"," ").toUpperCase()}</td>
                  <td>${e.createdByName||`User ${e.createdBy}`}</td>
                </tr>
              `).join("")}
              <tr>
                <td colspan="2" style="font-weight:bold; text-align:right;">Total</td>
                <td style="font-weight:bold;">₵${H.toFixed(2)}</td>
                <td colspan="2"></td>
              </tr>
            </tbody>
          </table>
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Report</button>
          </div>
        </body>
      </html>
    `;e.document.write(t),e.document.close()},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Print"}),(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(f.A,{}),onClick:()=>{let e=new M.uE;e.setFontSize(16),e.text("Sales Report",14,15),e.setFontSize(10),e.text(`Generated on: ${S()().format("MMMM D, YYYY h:mm A")}`,14,22);let t=I.map(e=>[S()(e.transactionDate).format("YYYY-MM-DD HH:mm"),S()(e.transactionDate).format("MMMM YYYY"),`₵${parseFloat(e.totalAmount).toFixed(2)}`,e.paymentMethod.replace("_"," ").toUpperCase(),e.createdByName||`User ${e.createdBy}`]);t.push(["Total","",`₵${H.toFixed(2)}`,"",""]),(0,P.Ay)(e,{head:[["Date","Month","Amount","Payment Method","Cashier"]],body:t,startY:30,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),e.save("sales-report.pdf")},size:t?"small":"middle",className:"bg-green-600 hover:bg-green-700",children:t?"":"Export"})]}),children:(0,a.jsxs)("div",{className:"w-full space-y-6",children:[(0,a.jsx)(d.A,{title:"Report Filters",size:"small",className:"bg-gray-50",children:(0,a.jsxs)(c.A,{gutter:[16,16],children:[(0,a.jsx)(m.A,{xs:24,sm:12,md:8,children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Date Range"}),(0,a.jsx)(O,{value:s,onChange:e=>{e&&e[0]&&e[1]?l([e[0],e[1]]):l(null)},className:"w-full",format:"YYYY-MM-DD",suffixIcon:(0,a.jsx)(y.A,{}),getPopupContainer:e=>e.parentElement||document.body,style:{zIndex:1e3}})]})}),(0,a.jsx)(m.A,{xs:24,sm:12,md:8,children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Report Type"}),(0,a.jsxs)(n.A,{value:A,onChange:E,className:"w-full",children:[(0,a.jsx)(C,{value:"summary",children:"Sales Summary"}),(0,a.jsx)(C,{value:"detailed",children:"Detailed Sales"}),(0,a.jsx)(C,{value:"products",children:"Product Performance"}),(0,a.jsx)(C,{value:"customers",children:"Customer Analysis"})]})]})}),(0,a.jsx)(m.A,{xs:24,sm:24,md:8,children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xa0"}),(0,a.jsx)(i.Ay,{type:"primary",onClick:()=>{Y(!0),T(),o.Ay.success("Report generated successfully!")},loading:_,className:"w-full bg-blue-600 hover:bg-blue-700",icon:(0,a.jsx)(h.A,{}),children:"Generate Report"})]})})]})}),(0,a.jsxs)(c.A,{gutter:[16,16],children:[(0,a.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,a.jsx)(d.A,{className:"text-center h-24 flex items-center justify-center",children:(0,a.jsx)(x.A,{title:"Total Sales",value:B.totalSales,precision:2,prefix:"₵",valueStyle:{color:"#3f8600",fontSize:"18px"},className:"w-full"})})}),(0,a.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,a.jsx)(d.A,{className:"text-center h-24 flex items-center justify-center",children:(0,a.jsx)(x.A,{title:"Transactions",value:B.totalTransactions,valueStyle:{color:"#1890ff",fontSize:"18px"},className:"w-full"})})}),(0,a.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,a.jsx)(d.A,{className:"text-center h-24 flex items-center justify-center",children:(0,a.jsx)(x.A,{title:"Avg. Order Value",value:B.averageOrderValue,precision:2,prefix:"₵",valueStyle:{color:"#722ed1",fontSize:"18px"},className:"w-full"})})}),(0,a.jsx)(m.A,{xs:12,sm:12,md:6,children:(0,a.jsx)(d.A,{className:"text-center h-24 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Top Product"}),(0,a.jsx)("div",{className:"text-sm font-semibold text-gray-800 break-words max-w-full",style:{wordBreak:"break-word",whiteSpace:"normal"},title:B.topSellingProduct,children:B.topSellingProduct})]})})})]}),(0,a.jsx)(d.A,{title:"Report Data",className:"min-h-96",children:_?(0,a.jsx)("div",{className:"flex h-60 items-center justify-center",children:(0,a.jsx)(p.A,{indicator:(0,a.jsx)(b.A,{style:{fontSize:24,color:"#1890ff"},spin:!0})})}):D?(0,a.jsxs)("div",{className:"space-y-6",children:["summary"===A&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Sales Summary"}),(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Metric"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Value"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Description"})]})}),(0,a.jsxs)("tbody",{children:[(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Total Revenue"}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",B.totalSales.toFixed(2)]}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Total sales revenue for selected period"})]}),(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Total Transactions"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:B.totalTransactions}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Number of completed sales"})]}),(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Average Order Value"}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",B.averageOrderValue.toFixed(2)]}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Average revenue per transaction"})]}),(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Top Selling Product"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:B.topSellingProduct}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Best performing product by quantity"})]})]})]})})})]}),"products"===A&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Product Performance"}),B.productPerformance&&B.productPerformance.length>0?(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full min-w-[500px]",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Rank"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Product Name"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Quantity Sold"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"})]})}),(0,a.jsx)("tbody",{children:B.productPerformance.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:t+1}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.productName}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.quantity}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]})]},t))})]})})}):(0,a.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,a.jsx)("p",{children:"No product performance data available."}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"This could be because there are no sales with product details in the selected period."})]})]}),"customers"===A&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Cashier Performance"}),B.customerAnalysis&&B.customerAnalysis.length>0?(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Rank"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Cashier Name"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Transactions"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Avg. Order Value"})]})}),(0,a.jsx)("tbody",{children:B.customerAnalysis.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:t+1}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.cashierName}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.transactions}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.averageOrderValue.toFixed(2)]})]},t))})]})})}):(0,a.jsx)("div",{className:"text-center text-gray-500 py-10",children:(0,a.jsx)("p",{children:"No cashier performance data available."})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Payment Methods Analysis"}),B.paymentMethods&&B.paymentMethods.length>0?(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full min-w-[500px]",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Payment Method"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Transactions"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Revenue"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Percentage"})]})}),(0,a.jsx)("tbody",{children:B.paymentMethods.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.method.replace("_"," ").toUpperCase()}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.count}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",e.revenue.toFixed(2)]}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-green-600",children:[e.percentage.toFixed(1),"%"]})]},t))})]})})}):(0,a.jsx)("div",{className:"text-center text-gray-500 py-10",children:(0,a.jsx)("p",{children:"No payment method data available."})})]})]}),"detailed"===A&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Detailed Sales"}),k?.data?.sales&&k.data.sales.length>0?(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full min-w-[600px]",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Date"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Month"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Amount"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Payment Method"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700 border-b",children:"Cashier"})]})}),(0,a.jsx)("tbody",{children:k.data.sales.filter(e=>{if(!s)return!0;let t=S()(e.transactionDate);return t.isAfter(s[0].startOf("day"))&&t.isBefore(s[1].endOf("day"))}).map((e,t)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:S()(e.transactionDate).format("YYYY-MM-DD HH:mm")}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:S()(e.transactionDate).format("MMMM YYYY")}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm font-semibold text-blue-600",children:["₵",parseFloat(e.totalAmount).toFixed(2)]}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.paymentMethod.replace("_"," ").toUpperCase()}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.createdByName||`User ${e.createdBy}`})]},e.id))})]})}),(0,a.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t text-sm text-gray-600",children:["Showing ",k.data.sales.filter(e=>{if(!s)return!0;let t=S()(e.transactionDate);return t.isAfter(s[0].startOf("day"))&&t.isBefore(s[1].endOf("day"))}).length," sales transactions"]})]}):(0,a.jsxs)("div",{className:"text-center text-gray-500 py-10",children:[(0,a.jsx)("p",{children:"No sales data available for the selected period."}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"Try adjusting the date range or check if there are any sales in the system."})]})]})]}):(0,a.jsxs)("div",{className:"text-center text-gray-500 py-20",children:[(0,a.jsx)(h.A,{className:"text-4xl mb-4"}),(0,a.jsx)("p",{children:'Select filters and click "Generate Report" to view sales data'}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"Charts and detailed analytics will appear here"})]})}),(0,a.jsx)(d.A,{className:"bg-blue-50 border-blue-200",children:(0,a.jsxs)("div",{className:"text-center text-blue-700",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCCA Advanced Analytics Coming Soon"}),(0,a.jsx)("p",{className:"text-sm",children:"Interactive charts, trend analysis, and detailed breakdowns will be available in future updates."})]})})]})})})}},24577:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\dashboard\\\\reports\\\\sales\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,3391,4877,3999,9198,1184,1716,9085,3712,7624,2648,7175,3309,7325,3950,9486,5735,5482,106,4286,8736],()=>s(56372));module.exports=a})();