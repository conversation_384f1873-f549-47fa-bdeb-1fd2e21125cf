import { useDeleteReceiptMutation } from "@/reduxRTK/services/receiptApi";
import { showMessage } from "@/utils/showMessage";
import { ApiResponse } from "@/types/user";

export const useReceiptDelete = (onSuccess?: () => void) => {
  // RTK Query hook for deleting receipts
  const [deleteReceipt, { isLoading }] = useDeleteReceiptMutation();

  const deleteReceiptById = async (receiptId: number) => {
    try {
      console.log("Deleting receipt with ID:", receiptId);
      
      const result = await deleteReceipt(receiptId).unwrap() as ApiResponse<any>;

      if (!result.success) {
        throw new Error(result.message || "Failed to delete receipt");
      }

      showMessage("success", "Receipt deleted successfully");
      
      if (onSuccess) {
        onSuccess();
      }
      
      return result.data;
    } catch (error: any) {
      console.error("Delete receipt error:", error);
      showMessage("error", error.message || "Failed to delete receipt");
      throw error;
    }
  };

  return {
    deleteReceipt: deleteReceiptById,
    isDeleting: isLoading
  };
};
