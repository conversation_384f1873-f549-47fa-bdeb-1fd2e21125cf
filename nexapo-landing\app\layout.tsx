import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'NEXAPO - Complete Business Management System | POS, Inventory & Expenses',
  description: 'NEXAPO is Ghana\'s most comprehensive business management platform. POS system, inventory management, expenses tracking, and powerful analytics - all in one solution.',
  keywords: 'business management system Ghana, POS system, inventory management, expenses tracking, business software, retail software, analytics, Ghana business solutions',
  authors: [{ name: 'Aetortech Team' }],
  creator: 'AETORTECH',
  publisher: 'AETORTECH',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://nexapo.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'NEXAPO - Ghana\'s #1 POS System',
    description: 'Transform your business with Ghana\'s smartest POS system. 30-day free trial available!',
    url: 'https://nexapo.com',
    siteName: 'NEXAPO',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'NEXAPO POS System',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NEXAPO - Ghana\'s #1 POS System',
    description: 'Transform your business with Ghana\'s smartest POS system. 30-day free trial available!',
    images: ['/logo.png'],
    creator: '@aetortech',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  )
}
