import { createApi } from '@reduxjs/toolkit/query/react';
import { customBaseQuery } from '../customBaseQuery';
import { ApiResponse } from '@/types/user';
import { store } from '@/reduxRTK/store/store';

// Define expense types
export interface Expense {
  id: number;
  title: string;
  description?: string;
  amount: string;
  categoryId?: number;
  expenseDate: string;
  paymentMethod: string;
  receiptUrl?: string;
  vendor?: string;
  isRecurring: boolean;
  recurringFrequency?: string;
  tags?: string;
  storeId?: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: number;
    name: string;
    color: string;
  };
}

export interface PaginatedExpenses {
  expenses: Expense[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateExpenseDto {
  title: string;
  description?: string;
  amount: number;
  categoryId?: number;
  expenseDate?: string;
  paymentMethod: string;
  receiptUrl?: string;
  vendor?: string;
  isRecurring?: boolean;
  recurringFrequency?: string;
  tags?: string;
  storeId?: number;
}

export interface UpdateExpenseDto {
  title?: string;
  description?: string;
  amount?: number;
  categoryId?: number;
  expenseDate?: string;
  paymentMethod?: string;
  receiptUrl?: string;
  vendor?: string;
  isRecurring?: boolean;
  recurringFrequency?: string;
  tags?: string;
  storeId?: number;
}

export interface ExpenseStats {
  totalExpenses: number;
  totalAmount: number;
  monthlyExpenses: number;
  monthlyAmount: number;
  topCategories: Array<{
    categoryName: string;
    amount: number;
    count: number;
  }>;
  recentExpenses: Expense[];
}

export const expenseApi = createApi({
  reducerPath: 'expenseApi' as const,
  baseQuery: customBaseQuery,
  tagTypes: ['Expense'] as const,
  endpoints: (builder) => ({
    // Get all expenses (paginated)
    getAllExpenses: builder.query<ApiResponse<PaginatedExpenses>, { page?: number; limit?: number; search?: string; categoryId?: number; startDate?: string; endDate?: string }>({
      query: ({ page = 1, limit = 10, search = '', categoryId, startDate, endDate }): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'retrieve',
            page,
            limit,
            search,
            categoryId,
            startDate,
            endDate,
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Expense'],
    }),

    // Get expense by ID
    getExpenseById: builder.query<ApiResponse<Expense>, number>({
      query: (expenseId): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'retrieve',
            expenseId,
          },
          token,
        };
      },
      providesTags: (_result, _error, id) => [{ type: 'Expense', id }],
    }),

    // Create new expense
    createExpense: builder.mutation<ApiResponse<Expense>, CreateExpenseDto>({
      query: (expenseData): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'createnew',
            ...expenseData,
          },
          token,
        };
      },
      invalidatesTags: ['Expense'],
    }),

    // Update expense
    updateExpense: builder.mutation<ApiResponse<Expense>, { expenseId: number; expenseData: UpdateExpenseDto }>({
      query: ({ expenseId, expenseData }): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'update',
            expenseId,
            ...expenseData,
          },
          token,
        };
      },
      invalidatesTags: (_result, _error, { expenseId }) => [{ type: 'Expense', id: expenseId }, 'Expense'],
    }),

    // Delete expense
    deleteExpense: builder.mutation<ApiResponse<void>, number>({
      query: (expenseId): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'delete',
            expenseId,
          },
          token,
        };
      },
      invalidatesTags: ['Expense'],
    }),

    // Get expense statistics
    getExpenseStats: builder.query<ApiResponse<ExpenseStats>, { startDate?: string; endDate?: string }>({
      query: ({ startDate, endDate } = {}): { urlpath: string; payloaddata: any; token?: string } => {
        const authState = store.getState().auth;
        const token = authState?.accessToken || '';

        if (!token) {
          console.error('Authentication token is missing. User may need to log in again.');
          throw new Error('Authentication token is missing. Please log in again.');
        }

        return {
          urlpath: '/expenses',
          payloaddata: {
            mode: 'stats',
            startDate,
            endDate,
          },
          token,
        };
      },
      keepUnusedDataFor: 0,
      providesTags: ['Expense'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetAllExpensesQuery,
  useGetExpenseByIdQuery,
  useCreateExpenseMutation,
  useUpdateExpenseMutation,
  useDeleteExpenseMutation,
  useGetExpenseStatsQuery,
} = expenseApi;
