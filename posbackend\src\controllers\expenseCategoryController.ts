import { Request, Response } from "express";
import { sendResponse } from "../utils/responseHelper";
import {
  createExpenseCategory,
  getAllExpenseCategories,
  getExpenseCategoryById,
  updateExpenseCategoryById,
  deleteExpenseCategoryById,
} from "../services/expenseCategoryService";
import { DecodedToken } from "../types/type";
import { validateMode } from "../utils/modeValidator";

export const handleExpenseCategoryRequest = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { mode, categoryId, page, limit, search, ...data } = req.body;
  const requester = req.user as DecodedToken;

  const validModes = ["createnew", "retrieve", "update", "delete"];
  if (!validateMode(res, mode, validModes)) return;

  try {
    switch (mode) {
      case "createnew": {
        if (!data.name) {
          return sendResponse(res, 400, false, "Category name is required.");
        }

        const newCategory = await createExpenseCategory(requester, {
          name: data.name,
          description: data.description,
          color: data.color || "#6B7280",
          isDefault: false, // User-created categories are never default
        });

        return sendResponse(res, 201, true, "Expense category created successfully.", newCategory);
      }

      case "retrieve": {
        if (categoryId) {
          const category = await getExpenseCategoryById(requester, Number(categoryId));
          return sendResponse(res, 200, true, "Expense category retrieved successfully.", category);
        } else {
          const pageNum = Number(page) || 1;
          const limitNum = Number(limit) || 50;

          if (pageNum < 1 || limitNum < 1) {
            return sendResponse(res, 400, false, "Invalid pagination values.");
          }

          const categoriesData = await getAllExpenseCategories(requester, pageNum, limitNum, search || '');
          return sendResponse(res, 200, true, "Expense categories retrieved successfully.", categoriesData);
        }
      }

      case "update": {
        if (!categoryId) {
          return sendResponse(res, 400, false, "Category ID is required for updating.");
        }

        const updateData: any = {};
        if (data.name) updateData.name = data.name;
        if (data.description !== undefined) updateData.description = data.description;
        if (data.color) updateData.color = data.color;

        const updatedCategory = await updateExpenseCategoryById(requester, Number(categoryId), updateData);
        return sendResponse(res, 200, true, "Expense category updated successfully.", updatedCategory);
      }

      case "delete": {
        const { categoryIds } = req.body;

        if (!categoryId && !categoryIds) {
          return sendResponse(res, 400, false, "Category ID(s) are required for deletion.");
        }

        const idsToDelete = categoryIds
          ? (Array.isArray(categoryIds) ? categoryIds : [categoryIds])
          : [Number(categoryId)];

        const result = await deleteExpenseCategoryById(requester, idsToDelete);

        const message = idsToDelete.length > 1
          ? `${idsToDelete.length} expense categories deleted successfully.`
          : "Expense category deleted successfully.";

        return sendResponse(res, 200, true, message, result);
      }

      default:
        return sendResponse(res, 400, false, "Invalid mode.");
    }
  } catch (error: any) {
    console.error("Error in expense category controller:", error);
    return sendResponse(res, 500, false, error.message || "Error processing expense category request");
  }
};
